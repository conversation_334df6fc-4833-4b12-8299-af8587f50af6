<?php
/**
 * TechSupport Pro - Contact Form Handler
 * 
 * This script processes contact form submissions, validates data,
 * and sends email notifications to site administrators.
 */

// Set error reporting for development (remove in production)
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

// Define configuration
$config = [
    'admin_email' => '<EMAIL>',
    'from_email' => '<EMAIL>',
    'subject_prefix' => '[TechSupport Pro Contact] ',
    'success_redirect' => 'index.html#contact?status=success',
    'error_redirect' => 'index.html#contact?status=error'
];

// Process only POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Initialize response array
    $response = [
        'success' => false,
        'errors' => [],
        'message' => ''
    ];
    
    // Get form data and sanitize - Updated for PHP 8+ compatibility
    $name = filter_input(INPUT_POST, 'name');
    $name = $name ? htmlspecialchars($name, ENT_QUOTES, 'UTF-8') : '';
    
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    
    $phone = filter_input(INPUT_POST, 'phone');
    $phone = $phone ? htmlspecialchars($phone, ENT_QUOTES, 'UTF-8') : '';
    
    $company = filter_input(INPUT_POST, 'company');
    $company = $company ? htmlspecialchars($company, ENT_QUOTES, 'UTF-8') : '';
    
    $subject = filter_input(INPUT_POST, 'subject');
    $subject = $subject ? htmlspecialchars($subject, ENT_QUOTES, 'UTF-8') : '';
    
    $priority = filter_input(INPUT_POST, 'priority');
    $priority = $priority ? htmlspecialchars($priority, ENT_QUOTES, 'UTF-8') : '';
    
    $message = filter_input(INPUT_POST, 'message');
    $message = $message ? htmlspecialchars($message, ENT_QUOTES, 'UTF-8') : '';
    
    $terms = isset($_POST['terms']) ? true : false;
    
    // Basic validation
    if (empty($name)) {
        $response['errors'][] = 'Name is required';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['errors'][] = 'Valid email is required';
    }
    
    if (empty($subject)) {
        $response['errors'][] = 'Subject is required';
    }
    
    if (empty($message)) {
        $response['errors'][] = 'Message is required';
    }
    
    if (!$terms) {
        $response['errors'][] = 'You must agree to the privacy policy';
    }
    
    // If no validation errors, process the form
    if (empty($response['errors'])) {
        // Map subject value to readable text
        $subjectMap = [
            'network' => 'Netwerk Beheer',
            'hardware' => 'Hardware Support',
            'software' => 'Software Oplossingen',
            'cloud' => 'Cloud Services',
            'security' => 'Cybersecurity',
            'quote' => 'Offerte aanvragen',
            'other' => 'Anders'
        ];
        
        $subjectText = isset($subjectMap[$subject]) ? $subjectMap[$subject] : 'Contact Form Submission';
        
        // Prepare email content
        $emailSubject = $config['subject_prefix'] . $subjectText;
        
        $emailBody = "
        <html>
        <head>
            <title>Nieuwe contact aanvraag</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h2 { color: #0056b3; }
                .info { margin-bottom: 20px; }
                .label { font-weight: bold; }
                .priority-high { color: #d32f2f; font-weight: bold; }
                .priority-medium { color: #ff9800; }
                .footer { margin-top: 30px; font-size: 12px; color: #777; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h2>Nieuwe contact aanvraag via website</h2>
                
                <div class='info'>
                    <p><span class='label'>Naam:</span> $name</p>
                    <p><span class='label'>E-mail:</span> $email</p>
                    " . (!empty($phone) ? "<p><span class='label'>Telefoon:</span> $phone</p>" : "") . "
                    " . (!empty($company) ? "<p><span class='label'>Bedrijf:</span> $company</p>" : "") . "
                    <p><span class='label'>Onderwerp:</span> {$subjectMap[$subject]}</p>
                    <p><span class='label'>Urgentie:</span> 
                        <span class='" . ($priority == 'hoog' ? 'priority-high' : 
                                       ($priority == 'medium' ? 'priority-medium' : '')) . "'>
                            $priority
                        </span>
                    </p>
                </div>
                
                <div class='info'>
                    <p class='label'>Bericht:</p>
                    <p>" . nl2br($message) . "</p>
                </div>
                
                <div class='footer'>
                    <p>Dit bericht is automatisch verzonden vanaf het contactformulier op techsupportpro.nl</p>
                    <p>IP: {$_SERVER['REMOTE_ADDR']} | Datum: " . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        // Set email headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: TechSupport Pro <' . $config['from_email'] . '>',
            'Reply-To: ' . $name . ' <' . $email . '>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Attempt to send email
        $mailSent = mail(
            $config['admin_email'], 
            $emailSubject, 
            $emailBody, 
            implode("\r\n", $headers)
        );
        
        if ($mailSent) {
            // Success!
            $response['success'] = true;
            $response['message'] = 'Thank you for your message! We will contact you soon.';
            
            // In a real application, you might log the submission to database here
            
            // Redirect to success page
            header('Location: ' . $config['success_redirect']);
            exit;
        } else {
            // Email sending failed
            $response['errors'][] = 'Failed to send email. Please try again later.';
        }
    }
    
    // If we've reached here with errors, redirect to error page
    if (!empty($response['errors'])) {
        header('Location: ' . $config['error_redirect']);
        exit;
    }
} else {
    // Not a POST request, redirect to homepage
    header('Location: index.html');
    exit;
}