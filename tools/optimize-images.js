#!/usr/bin/env node

/**
 * Image Optimization Tool
 * Optimizes images for web delivery
 */

import { promises as fs } from 'fs';
import path from 'path';
import imagemin from 'imagemin';
import imageminWebp from 'imagemin-webp';
import imageminAvif from 'imagemin-avif';

const INPUT_DIR = 'src/assets/images';
const OUTPUT_DIR = 'src/assets/images/optimized';

/**
 * Optimize images
 */
async function optimizeImages() {
  try {
    console.log('🖼️  Starting image optimization...');
    
    // Ensure output directory exists
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    
    // Generate WebP versions
    console.log('📸 Generating WebP images...');
    await imagemin([`${INPUT_DIR}/**/*.{jpg,jpeg,png}`], {
      destination: OUTPUT_DIR,
      plugins: [
        imageminWebp({
          quality: 80,
          method: 6
        })
      ]
    });
    
    // Generate AVIF versions (modern format)
    console.log('🎨 Generating AVIF images...');
    await imagemin([`${INPUT_DIR}/**/*.{jpg,jpeg,png}`], {
      destination: OUTPUT_DIR,
      plugins: [
        imageminAvif({
          quality: 70,
          speed: 2
        })
      ]
    });
    
    console.log('✅ Image optimization complete!');
    console.log(`📁 Optimized images saved to: ${OUTPUT_DIR}`);
    
  } catch (error) {
    console.error('❌ Error optimizing images:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  optimizeImages();
}

export { optimizeImages };
