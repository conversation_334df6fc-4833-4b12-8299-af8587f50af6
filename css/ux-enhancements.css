/* 
 * UX Enhancement Styles 
 * This file contains CSS styles for improved user experience elements
 */

/* ====== BREADCRUMBS ====== */
.breadcrumbs {
    padding: 0.75rem 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--secondary-color);
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.breadcrumbs ol {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
}

.breadcrumbs li {
    display: inline-flex;
    align-items: center;
}

.breadcrumbs li + li::before {
    display: inline-block;
    padding: 0 0.5rem;
    color: var(--border-color);
    content: "/";
}

.breadcrumbs a {
    text-decoration: none;
    color: var(--link-color);
    transition: color 0.2s ease;
}

.breadcrumbs a:hover {
    text-decoration: underline;
    color: var(--primary-color);
}

.breadcrumbs [aria-current="page"] {
    color: var(--text-color);
    font-weight: 500;
}

/* ====== BACK TO TOP BUTTON ====== */
.back-to-top {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--light-text);
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 99;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top.show {
    opacity: 0.9;
    transform: translateY(0);
}

.back-to-top:hover, 
.back-to-top:focus {
    background-color: #004494;
    opacity: 1;
}

.back-to-top i {
    font-size: 1.25rem;
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .back-to-top {
        transition: opacity 0.1s linear;
        transform: translateY(0);
    }
}

/* ====== DROPDOWN MENU ENHANCEMENTS ====== */
.dropdown-indicator {
    font-size: 0.75em;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.dropdown-active .dropdown-indicator {
    transform: rotate(180deg);
}

.nav-item .nav-link {
    display: flex;
    align-items: center;
}

/* Active nav states */
.nav-link.active {
    position: relative;
    font-weight: 500;
}

.nav-link.active::after {
    content: "";
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(1);
    transition: transform 0.3s ease;
}

.nav-link:not(.active)::after {
    content: "";
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-link:hover::after,
.nav-link:focus::after {
    transform: scaleX(0.8);
}

.nav-link.active-parent {
    color: var(--accent-color);
}

/* ====== NOTIFICATION SYSTEM ====== */
#notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9999;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.notification {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.16);
    transform: translateX(120%);
    transition: transform 0.3s ease;
    opacity: 0.95;
    margin-bottom: 0.5rem;
}

.notification.visible {
    transform: translateX(0);
}

.notification-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
}

.notification-content {
    flex: 1;
    padding-right: 1rem;
}

.notification-close {
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    font-size: 0.875rem;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: #444;
}

/* Colors by notification type */
.notification-success {
    border-left: 4px solid #28a745;
}
.notification-success .notification-icon {
    color: #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}
.notification-error .notification-icon {
    color: #dc3545;
}

.notification-warning {
    border-left: 4px solid #ffc107;
}
.notification-warning .notification-icon {
    color: #ffc107;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}
.notification-info .notification-icon {
    color: #17a2b8;
}

/* ====== PAGE LOAD PROGRESS ====== */
.page-load-progress {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(to right, var(--accent-color), var(--primary-color));
    width: 0%;
    z-index: 9999;
    transition: width 0.5s ease, opacity 0.3s ease;
}

/* ====== FORM PROGRESS INDICATORS ====== */
.form-progress {
    display: flex;
    margin-bottom: 2rem;
    justify-content: space-between;
    position: relative;
}

.form-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    height: 2px;
    background-color: var(--border-color);
    width: 100%;
    transform: translateY(-50%);
    z-index: 0;
}

.form-progress-step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid var(--border-color);
    color: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-progress-step::after {
    content: attr(data-step);
}

.form-progress-step.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.form-progress-step.completed {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.form-progress-step.completed::after {
    content: '✓';
}

.form-progress-step[data-label]::before {
    content: attr(data-label);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    white-space: nowrap;
    color: var(--text-color);
}

/* ====== ENHANCED FORM VALIDATION ====== */
.form-input,
.form-select,
.form-textarea {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.15);
}

.form-input.valid,
.form-select.valid,
.form-textarea.valid {
    border-color: #28a745;
    padding-right: 2.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.4em + 0.25rem) center;
    background-size: calc(0.8em + 0.5rem) calc(0.8em + 0.5rem);
}

.form-input.invalid,
.form-select.invalid,
.form-textarea.invalid {
    border-color: #dc3545;
    padding-right: 2.25rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.4em + 0.25rem) center;
    background-size: calc(0.8em + 0.5rem) calc(0.8em + 0.5rem);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    height: 0;
    overflow: hidden;
    transition: height 0.2s ease;
}

/* ====== RIPPLE EFFECT ====== */
.btn, 
.nav-link, 
.service-card {
    position: relative;
    overflow: hidden;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ====== SCROLL ANIMATIONS ====== */
.will-animate {
    opacity: 0;
}

.will-animate.animated {
    opacity: 1;
}

.animate-fade-in.animated {
    animation: fadeIn 0.5s ease forwards;
}

.animate-slide-up.animated {
    animation: slideUp 0.5s ease forwards;
}

.animate-slide-in-right.animated {
    animation: slideInRight 0.5s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideInRight {
    from { 
        opacity: 0; 
        transform: translateX(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

/* ====== READING PROGRESS BAR ====== */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--accent-color);
    z-index: 9999;
    transition: width 0.1s ease;
}

/* ====== PAGE TRANSITIONS ====== */
.page-exit {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.page-enter {
    animation: pageEnter 0.5s ease;
}

@keyframes pageEnter {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* ====== CONTEXTUAL CTA ====== */
.cta-highlight {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(0, 86, 179, 0.4);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 86, 179, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 86, 179, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 86, 179, 0);
    }
}

.cta-message {
    background-color: var(--secondary-color);
    border-left: 4px solid var(--accent-color);
    padding: 0.75rem;
    margin-top: 1rem;
    font-weight: 500;
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .animate-fade-in.animated,
    .animate-slide-up.animated,
    .animate-slide-in-right.animated {
        animation: none;
        opacity: 1;
        transform: none;
    }
    
    .cta-highlight {
        animation: none;
        box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.4);
    }
    
    .page-enter {
        animation: none;
    }
}
