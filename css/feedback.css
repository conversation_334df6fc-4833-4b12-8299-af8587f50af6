/* 
 * User Feedback Styles
 * This file contains CSS for feedback widgets, reaction buttons, and surveys
 */

/* ====== FEEDBACK WIDGET ====== */
.feedback-widget {
    position: fixed;
    bottom: -400px;
    right: 30px;
    width: 320px;
    padding: 1.5rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 99;
    transition: transform 0.4s ease;
    transform: translateY(0);
    max-height: 90vh;
    overflow-y: auto;
}

.feedback-widget.visible {
    transform: translateY(-420px);
}

.feedback-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 1rem;
    color: var(--border-color);
    cursor: pointer;
    padding: 0.5rem;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feedback-close:hover,
.feedback-close:focus {
    color: var(--text-color);
}

#feedback-title {
    margin-top: 0;
    font-size: 1.25rem;
}

.feedback-options {
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0;
}

.feedback-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 0.25rem;
}

.feedback-option:hover,
.feedback-option:focus {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.05);
}

.feedback-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.1);
}

.feedback-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.feedback-option[data-value="positive"] i {
    color: #28a745;
}

.feedback-option[data-value="neutral"] i {
    color: #ffc107;
}

.feedback-option[data-value="negative"] i {
    color: #dc3545;
}

.feedback-form {
    margin-top: 1rem;
}

.feedback-comment {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

.feedback-submit {
    margin-top: 1rem;
    width: 100%;
}

/* ====== REACTION BUTTONS ====== */
.reaction-buttons {
    margin: 3rem 0 2rem;
    padding: 1.5rem;
    background-color: var(--secondary-color);
    border-radius: 8px;
}

.reaction-buttons h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.reaction-options {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.reaction-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reaction-btn:hover,
.reaction-btn:focus {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.05);
}

.reaction-btn.active {
    border-color: var(--primary-color);
    background-color: rgba(0, 86, 179, 0.1);
}

.reaction-btn i {
    margin-right: 0.5rem;
}

.reaction-btn[data-reaction="helpful"] i {
    color: #28a745;
}

.reaction-btn[data-reaction="not-helpful"] i {
    color: #dc3545;
}

.reaction-feedback {
    margin-top: 1rem;
}

.reaction-comment {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-height: 60px;
    resize: vertical;
    font-family: inherit;
    margin-bottom: 0.75rem;
}

.reaction-thanks {
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
    background-color: #e1f5e1;
    border-radius: 4px;
    color: #28a745;
    text-align: center;
}

/* ====== RESPONSIVE ADJUSTMENTS ====== */
@media (max-width: 768px) {
    .feedback-widget {
        bottom: -420px;
        right: 10px;
        left: 10px;
        width: calc(100% - 20px);
    }
    
    .feedback-widget.visible {
        transform: translateY(-430px);
    }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .feedback-widget {
        transition: none;
    }
    
    .feedback-widget.visible {
        bottom: 20px;
        transform: none;
    }
}
