/* TechSupport Pro - Main Stylesheet */

/* --- Base Styles --- */
:root {
  --primary-color: #0056b3;
  --secondary-color: #f8f9fa;
  --accent-color: #e69c00; /* Darkened from #ffc107 for better contrast */
  --text-color: #1a1a1a; /* Darkened for better contrast */
  --light-text: #ffffff; /* Pure white for maximum contrast on dark backgrounds */
  --border-color: #6c757d; /* Darkened for better contrast */
  --link-color: #004494; /* New color for links with better contrast */
  --error-color: #dc3545; /* Added for form validation */
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-main);
  color: var(--text-color);
  line-height: var(--line-height-body);
  font-size: 1rem;
  letter-spacing: 0.01em;
}

/* Improve link contrast */
a {
    color: var(--link-color);
    text-decoration: underline; /* Makes links more identifiable */
}

a:hover, a:focus {
    color: #003366;
    text-decoration: underline;
}

/* --- Typography --- */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  margin-bottom: 1.2rem;
  line-height: var(--line-height-headings);
}

p {
  margin-bottom: 1.2rem;
  max-width: 70ch; /* Improves readability by limiting line length */
}

/* --- Layout: Container --- */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* --- Navigation --- */
.navbar {
  background-color: var(--primary-color);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
}

.nav-menu {
  display: flex;
  list-style: none;
}

.nav-item {
  margin-left: 1.5rem;
}

.nav-link {
  color: var(--light-text);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--accent-color);
}

.hamburger {
  display: none;
  cursor: pointer;
  background: none;
  border: none;
  color: var(--light-text);
  font-size: 1.5rem;
  padding: 0.75rem;
  margin: -0.75rem;  /* Negative margin to maintain layout while increasing touch target */
  min-width: 44px;  /* Minimum size for touch targets */
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.hamburger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--light-text);
}

/* Make the icon more visible */
.hamburger i {
  font-size: 24px;
}

/* --- Hero Section --- */
.hero {
  background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), 
              url('../images/hero-bg.jpg') no-repeat center center;
  background-size: cover;
  color: var(--light-text);
  padding: 5rem 0;
  text-align: center;
  position: relative; /* Added for positioning free help blocks */
}

/* Free help/support text blocks */
.free-help-block {
  position: absolute;
  background-color: rgba(0, 86, 179, 0.7); /* Less bright, slightly transparent blue matching the theme */
  color: white; /* White text */
  font-weight: bold;
  padding: 10px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: rotate(-5deg);
  font-size: 1.1rem;
  z-index: 5;
  text-align: center;
  animation: pulse 3s infinite; /* Slower, more subtle animation */
  border: 1px solid rgba(255, 255, 255, 0.6); /* Subtle border */
  letter-spacing: 0.3px;
}

.free-help-left {
  top: 30%;
  left: 5%;
  transform: rotate(-5deg); /* Slightly different rotation than right side */
}

.free-help-right {
  top: 30%;
  right: 5%;
  transform: rotate(5deg); /* Opposite rotation from left side */
  animation: pulse-right 3s infinite; /* Separate animation for right side, slower */
}

@keyframes pulse-right {
  0% {
    transform: rotate(5deg) scale(1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
  }
  50% {
    transform: rotate(5deg) scale(1.05);
    box-shadow: 0 3px 7px rgba(0, 0, 0, 0.25);
    opacity: 1;
  }
  100% {
    transform: rotate(5deg) scale(1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
  }
}

@keyframes pulse {
  0% {
    transform: rotate(-5deg) scale(1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
  }
  50% {
    transform: rotate(-5deg) scale(1.05);
    box-shadow: 0 3px 7px rgba(0, 0, 0, 0.25);
    opacity: 1;
  }
  100% {
    transform: rotate(-5deg) scale(1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
  }
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.hero p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 2rem;
}

.btn {
  display: inline-block;
  padding: 0.875rem 1.75rem;  /* Increased padding for better touch targets */
  min-width: 180px;  /* Standardized minimum width */
  background-color: var(--accent-color);
  color: var(--text-color);
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 1rem;
  line-height: 1.5;
  cursor: pointer;
  border: none;
}

.btn:hover {
  background-color: #e6ac00;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.5);
  transform: translateY(-2px);
}

/* Service Button Styling */
.btn-service {
  background-color: var(--primary-color);
  color: white;
  margin-top: 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-service:hover {
  background-color: #004494;
  color: white;
}

.btn-service:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.3);
  color: white;
}

.btn-service::after {
  content: "→";
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.btn-service:hover::after {
  transform: translateX(4px);
}

/* --- Sections --- */
.section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.info-section {
  max-width: 800px;
  margin: 0 auto 2rem;
  text-align: center;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.service-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.service-icon {
  background-color: var(--primary-color);
  color: white;
  padding: 1.5rem;
  text-align: center;
  font-size: 2rem;
}

.service-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.service-content h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.service-content ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.service-content li {
  margin-bottom: 0.5rem;
}

.more-link {
  margin-top: auto;
  text-align: right;
}

.more-link a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.more-link a:hover {
  color: var(--accent-color);
}

/* --- Cards --- */
.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.card {
  background: var(--secondary-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
}

/* --- Footer --- */
footer {
  background-color: var(--primary-color);
  color: var(--light-text);
  padding: 3rem 0 1.5rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem;
  margin-bottom: 2rem;
}

.footer-logo {
  flex: 1 1 250px;
  margin-bottom: 1rem;
}

.footer-logo img {
  margin-bottom: 1rem;
  filter: brightness(0) invert(1); /* Makes dark logo white for better visibility */
  max-width: 180px;
}

.footer-logo p {
  font-size: 0.95rem;
  line-height: 1.5;
  opacity: 0.9;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  flex: 2 1 600px;
  gap: 2.5rem;
}

/* Main navigation columns in footer */
.footer-nav-column {
  flex: 1 1 180px;
}

.footer-heading {
  color: white;
  font-size: 1.1rem;
  margin-bottom: 1.2rem;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.footer-heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -6px;
  height: 2px;
  width: 30px;
  background-color: var(--accent-color);
}

.footer-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-nav-list li {
  margin-bottom: 0.7rem;
}

.footer-nav-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: color 0.2s ease, transform 0.2s ease;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
}

.footer-nav-link:hover, 
.footer-nav-link:focus {
  color: white;
  text-decoration: underline;
  transform: translateX(3px);
}

/* Contact info section */
.footer-contact {
  flex: 1 1 300px;
}

.footer-contact-info {
  margin-top: 0.5rem;
}

.footer-contact-item {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.footer-contact-icon {
  color: var(--accent-color);
  font-size: 1.1rem;
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.footer-contact-text {
  line-height: 1.5;
}

.footer-contact-text a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-contact-text a:hover,
.footer-contact-text a:focus {
  color: white;
  text-decoration: underline;
}

/* Social media icons */
.footer-social {
  margin-top: 1.5rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-icon:hover,
.social-icon:focus {
  background: var(--accent-color);
  transform: translateY(-3px);
  color: white;
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
}

.footer-bottom p {
  margin: 0;
  opacity: 0.8;
}

.footer-bottom-links {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.footer-bottom-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.2s ease;
  font-size: 0.85rem;
}

.footer-bottom-links a:hover,
.footer-bottom-links a:focus {
  color: white;
  text-decoration: underline;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: var(--accent-color);
  transform: translateY(-3px);
}

.social-links i {
  font-size: 1rem;
}

/* --- Contact Form Styles --- */
.contact-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
}

.contact-info {
  background: var(--secondary-color);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.contact-info h3 {
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.info-item .icon {
  margin-right: 1rem;
  color: var(--primary-color);
  min-width: 24px;
  fill: var(--primary-color);
}

.info-item strong {
  display: block;
  margin-bottom: 0.25rem;
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-link:hover {
  text-decoration: underline;
}

.social-links {
  margin-top: 2rem;
}

.social-links h4 {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
 
}

.contact-form h3 {
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-size: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
  outline: none;
}

/* Required field indicators */
.required-field::after {
  content: "*";
  color: var(--error-color);
  margin-left: 0.25rem;
}

/* Error states */
.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.radio-group {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.radio-option {
  display: flex;
  align-items: center;
}

.radio-option input[type="radio"] {
  margin-right: 0.5rem;
  width: 18px;
  height: 18px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.btn-secondary {
  background-color: #e9ecef;
  color: #212529;
}

.btn-secondary:hover {
  background-color: #dee2e6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(233, 236, 239, 0.5);
  transform: translateY(-2px);
}

.required-note {
  margin-top: 1.5rem;
  font-size: 0.85rem;
  color: #666;
}

/* --- Services Section Styles --- */
.services-section {
  padding-bottom: 6rem;
}

.section-subtitle {
  text-align: center;
  max-width: 800px;
  margin: -1.5rem auto 3rem;
  color: #666;
}

/* Service Tabs */
.services-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 3rem;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  margin: 0 0.5rem;
  cursor: pointer;
  font-family: var(--font-heading);
  font-weight: 500;
  color: #666;
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom: 3px solid var(--primary-color);
}

.tab-icon {
  margin-right: 0.5rem;
  fill: currentColor;
}

/* Service Content */
.service-content {
  display: none;
}

.service-content.active {
  display: block;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.service-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.service-text h3 {
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.service-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.7;
}

.service-text h4 {
  margin: 1rem 0;
  font-size: 1.3rem;
}

.service-list {
  list-style: none;
  margin-bottom: 2rem;
}

.service-list li {
  margin-bottom: 1.5rem;
  position: relative;
}

.service-list li strong {
  display: block;
  margin-bottom: 0.3rem;
  color: #333;
  font-size: 1.1rem;
}

.service-list li:before {
  content: "✓";
  color: var(--primary-color);
  font-weight: bold;
  display: inline-block;
  width: 1.2rem;
  margin-left: -1.2rem;
}

.service-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.service-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.service-cta {
  margin-top: 2rem;
}

/* Service Packages */
.service-packages {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid var(--border-color);
}

.packages-title {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 3rem;
}

.packages-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.package {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.package:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.package.featured {
  border: 2px solid var(--accent-color);
  transform: scale(1.05);
}

.package.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.package-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--accent-color);
  color: var(--text-color);
  font-weight: bold;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-bottom-left-radius: 8px;
}

.package-header {
  background-color: var(--primary-color);
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.package-header h4 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.package-price {
  font-size: 1.8rem;
  font-weight: bold;
}

.package-price span {
  font-size: 0.9rem;
  font-weight: normal;
  opacity: 0.8;
}

.package-body {
  padding: 2rem;
}

.package-features {
  list-style: none;
  margin-bottom: 1.5rem;
  min-height: 250px;
}

.package-features li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.package-features li::before {
  content: "✓";
  color: var(--primary-color);
  position: absolute;
  left: 0;
}

.package-description {
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
  text-align: center;
}

.package-footer {
  padding: 1rem 2rem 2rem;
  text-align: center;
}

.package.featured .btn {
  background-color: var(--accent-color);
}

/* Service FAQ */
.service-faq {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid var(--border-color);
}

.service-faq h3 {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 3rem;
}

.accordion {
  max-width: 900px;
  margin: 0 auto;
}

.accordion-item {
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.accordion-header {
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  padding: 1.2rem;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.accordion-header:hover {
  background-color: #f9f9f9;
}

.accordion-icon {
  font-size: 1.2rem;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.accordion-header[aria-expanded="true"] .accordion-icon {
  transform: rotate(45deg);
}

.accordion-content {
  padding: 0 1.2rem;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-header[aria-expanded="true"] + .accordion-content {
  max-height: 500px;
  padding: 0 1.2rem 1.2rem;
}

/* --- Blog Highlights Styles --- */
.blog-highlights {
  background-color: #f9f9f9;
}

.blog-highlights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.blog-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.08);
}

.blog-card-image {
  position: relative;
  display: block;
}

.blog-card-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.blog-card-category {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  text-transform: uppercase;
}

.blog-card-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.blog-card-title {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.blog-card-title a {
  color: var(--text-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-card-title a:hover {
  color: var(--primary-color);
}

.blog-card-date {
  color: #666;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.blog-card-excerpt {
  color: #555;
  line-height: 1.6;
}

.blog-highlights-footer {
  text-align: center;
}

@media screen and (max-width: 992px) {
  .blog-highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .blog-highlights-grid {
    grid-template-columns: 1fr;
  }
}

/* --- Forum Styles --- */
.forum-intro {
    background-color: var(--light-bg);
    padding: 3rem 0;
}

.participation-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.participation-list li {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.participation-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.questions-grid {
    display: grid;
    gap: 2rem;
    margin: 3rem 0;
}

.question-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.question-card:hover {
    transform: translateY(-5px);
}

.question-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.meta {
    font-size: 0.9rem;
    color: var(--secondary-text);
    margin-bottom: 1rem;
}

.discussion-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.discussion-link:hover {
    text-decoration: underline;
}

.question-form {
    max-width: 600px;
    margin: 2rem auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.faq-grid {
    display: grid;
    gap: 2rem;
    margin: 2rem 0;
}

.faq-item {
    background: var(--light-bg);
    padding: 1.5rem;
    border-radius: 8px;
}

.faq-item h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.forum-list {
    margin: 2rem 0;
}

.forum-thread {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.forum-thread:hover {
    transform: translateY(-3px);
}

.forum-thread h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.forum-thread .meta {
    font-size: 0.9rem;
    color: var(--secondary-text);
    margin-bottom: 1rem;
}

.onboarding-section {
    background: var(--light-bg);
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.onboarding-section ul {
    list-style: none;
    padding: 0;
}

.onboarding-section li {
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
}

.onboarding-section li::before {
    content: "→";
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.contact-form {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.faq-section {
    background: var(--light-bg);
    padding: 3rem 0;
}

.faq-list {
    max-width: 800px;
    margin: 2rem auto;
}

details {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

summary {
    font-weight: 500;
    color: var(--primary-color);
    cursor: pointer;
    padding: 0.5rem;
}

summary::-webkit-details-marker {
    color: var(--primary-color);
}

details p {
    padding: 1rem;
    margin: 0;
}

.section-title {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* --- Responsive Design --- */
/* Tablets */
@media screen and (max-width: 992px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-content {
    flex-direction: column;
  }
  
  .footer-logo {
    margin-bottom: 1.5rem;
  }
  
  .footer-links {
    flex-wrap: wrap;
    gap: 1.5rem;
  }
  
  .footer-links-column {
    flex: 1 1 40%;
  }
  
  .contact-container {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    margin-bottom: 2rem;
  }
  
  .service-details {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .service-image {
    order: -1;
  }
  
  .packages-container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .package.featured {
    transform: scale(1);
  }
  
  .package.featured:hover {
    transform: translateY(-5px);
  }
  
  /* Responsive adjustments for free help blocks */
  .free-help-block {
    padding: 10px 16px;
    font-size: 1rem;
  }
  
  .free-help-left {
    top: 15%;
    left: 2%;
    transform: rotate(-5deg);
  }
  
  .free-help-right {
    top: 15%;
    right: 2%;
    transform: rotate(5deg);
  }
}

/* Large Mobile */
@media screen and (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .hamburger {
    display: block;
  }
  
  .nav-menu {
    position: fixed;
    top: 70px;
    right: -100%;
    flex-direction: column;
    width: 250px;
    height: 100vh;
    background-color: var(--primary-color);
    padding: 2rem 0;
    transition: right 0.3s ease;
  }
  
  .nav-menu.active {
    right: 0;
  }
  
  .nav-item {
    margin: 1rem 0;
    text-align: center;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
  
  .services-tabs {
    flex-direction: column;
    align-items: center;
  }
  
  .tab-button {
    width: 100%;
    margin: 0.25rem 0;
    justify-content: center;
  }
  
  /* Adjust free help blocks for smaller screens */
  .free-help-block {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
  
  .free-help-left {
    top: 10%;
    left: 1%;
    transform: rotate(-5deg);
    animation: pulse 3s infinite;
  }
  
  .free-help-right {
    top: 10%;
    right: 1%;
    transform: rotate(5deg);
    animation: pulse-right 3s infinite;
  }
}

/* Small Mobile */
@media screen and (max-width: 576px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-links-column {
    flex: 1 1 100%;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .footer-bottom-links {
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .section {
    padding: 2.5rem 0;
  }
  
  /* Stack free help blocks vertically on very small screens */
  .free-help-block {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
  
  .free-help-left {
    top: 5%;
    left: 50%;
    transform: translateX(-50%) rotate(-5deg);
    animation: pulse 3s infinite;
  }
  
  .free-help-right {
    top: auto;
    bottom: 5%;
    right: 50%;
    transform: translateX(50%) rotate(5deg);
    animation: pulse-right 3s infinite;
  }
}

/* --- Enhanced Accessibility Features --- */

/* Skip to content link - improved visibility */
.skip-to-content {
  position: absolute;
  top: -9999px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: var(--light-text);
  padding: 1rem;
  z-index: 9999;
  text-decoration: none;
  font-weight: bold;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  width: max-content;
  transition: top 0.3s ease;
}

.skip-to-content:focus {
  top: 0;
}

/* ARIA landmarks styling */
[role="banner"],
[role="navigation"],
[role="main"],
[role="contentinfo"],
[role="complementary"],
[role="search"],
main, header, footer, nav, aside {
  outline: none;
}

/* Focus indicators for all interactive elements */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus,
[role="button"]:focus,
[tabindex]:focus {
  outline: 3px solid var(--accent-color);
  outline-offset: 3px;
  transition: outline-offset 0.2s ease;
}

/* Focus management for modals/dialogs */
.modal:focus,
[role="dialog"]:focus {
  outline: none;
}

/* Ensure high contrast for text */
.high-contrast-text {
  color: var(--text-color);
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
}

/* Reduced motion option */
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.001ms !important;
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}

/* Accessible form error states */
.form-error {
  border-color: var(--error-color);
  border-width: 2px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23dc3545'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 20px;
  padding-right: 40px;
}

.error-message {
  display: flex;
  align-items: center;
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.error-message::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='%23dc3545'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
  background-size: contain;
  margin-right: 6px;
}

/* Live region for announcements */
.sr-announcer {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Improved focus indication for keyboard users */
.focus-visible-only:focus:not(:focus-visible) {
  outline: none;
}

/* Accessible tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-text {
  visibility: hidden;
  position: absolute;
  z-index: 100;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;
  max-width: 250px;
  background-color: var(--text-color);
  color: var(--light-text);
  text-align: center;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.875rem;
  pointer-events: none;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--text-color) transparent transparent transparent;
}

.tooltip:hover .tooltip-text,
.tooltip:focus .tooltip-text,
.tooltip:focus-within .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Accessible tables */
.accessible-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.accessible-table caption {
  font-weight: bold;
  padding: 0.5rem;
  text-align: left;
  color: var(--text-color);
  background-color: var(--secondary-color);
  border-radius: 4px 4px 0 0;
}

.accessible-table th {
  background-color: var(--primary-color);
  color: var(--light-text);
  text-align: left;
  padding: 0.75rem;
}

.accessible-table td {
  border: 1px solid var(--border-color);
  padding: 0.75rem;
}

.accessible-table tr:nth-child(even) {
  background-color: var(--secondary-color);
}

.accessible-table tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Form field grouping for screen readers */
.form-group[role="group"] {
  margin-bottom: 1.5rem;
}

.form-group[role="group"] > .form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Accessible accordion */
.a11y-accordion {
  margin: 1rem 0;
}

.a11y-accordion-header {
  margin: 0;
}

.a11y-accordion-trigger {
  display: block;
  width: 100%;
  text-align: left;
  padding: 1rem;
  background-color: var(--secondary-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.a11y-accordion-trigger:hover,
.a11y-accordion-trigger:focus {
  background-color: #e9ecef;
}

.a11y-accordion-trigger[aria-expanded="true"] {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
}

.a11y-accordion-trigger::after {
  content: "+";
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  transition: transform 0.2s ease;
}

.a11y-accordion-trigger[aria-expanded="true"]::after {
  content: "−";
}

.a11y-accordion-panel {
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 4px 4px;
  padding: 1rem;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.a11y-accordion-panel[aria-hidden="false"] {
  max-height: 1000px;
  padding: 1rem;
}

/* Skip links for complex UI */
.skip-link-list {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  background-color: var(--primary-color);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.skip-link-list:focus-within {
  transform: translateY(0);
}

.skip-link {
  color: var(--light-text);
  margin: 0 1rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
}

.skip-link:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .btn,
  .nav-link,
  .card,
  .service-card {
    border: 1px solid ButtonText;
  }
  
  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus {
    outline: 2px solid Highlight;
  }
}

/* Print Styles */
@media print {
  .navbar, .hero, footer {
    display: none;
  }
  
  body {
    font-size: 12pt;
    color: #000;
    background: #fff;
  }
  
  .container {
    width: 100%;
    max-width: 100%;
  }
}

/* --- Language Selector Styles --- */
.language-selector {
  position: relative;
  margin-left: 1.5rem;
}

.language-selector-toggle {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.language-selector-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.language-selector-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.3);
}

.current-language {
  font-weight: 500;
  margin-right: 0.5rem;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 180px;
  margin-top: 0.5rem;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.language-selector-toggle[aria-expanded="true"] + .language-dropdown {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.language-dropdown li {
  list-style: none;
}

.language-option {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.language-option:hover {
  background-color: #f5f5f5;
}

.language-option.active {
  background-color: #f0f5ff;
  color: var(--primary-color);
  font-weight: 500;
}

.language-option img {
  margin-right: 0.75rem;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Skip to main content link - for keyboard users */
.skip-to-main-content {
    position: absolute;
    left: -9999px;
    z-index: 999;
    padding: 1em;
    background-color: var(--primary-color);
    color: var(--light-text);
    text-decoration: none;
}

.skip-to-main-content:focus {
    left: 50%;
    transform: translateX(-50%);
}

/* Improved button focus states */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--light-text), 0 0 0 4px var(--primary-color);
}

/* Make sure interactive elements are clearly visible when focused */
.nav-link:focus,
.btn:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
    text-decoration: underline;
}

/* Ensure dropdown menus are keyboard accessible */
.dropdown-menu {
    display: none;
}

.dropdown-toggle:focus + .dropdown-menu,
.dropdown-menu:focus-within {
    display: block;
}

/* Improved touch targets for mobile */
@media screen and (max-width: 768px) {
    /* Form inputs and buttons */
    .form-input,
    .form-textarea,
    .form-select,
    .btn {
        min-height: 48px;  /* Minimum touch target size */
        padding: 12px 16px;
        font-size: 16px;  /* Prevent iOS zoom on focus */
    }

    /* Increase spacing between form elements */
    .form-group {
        margin-bottom: 24px;
    }

    /* Larger radio and checkbox inputs */
    input[type="radio"],
    input[type="checkbox"] {
        min-width: 24px;
        min-height: 24px;
        margin-right: 12px;
    }

    /* Improve select dropdowns */
    .form-select {
        padding-right: 36px; /* Space for dropdown arrow */
        background-position: right 12px center;
    }

    /* Navigation improvements */
    .nav-item {
        margin-left: 0;
        margin-bottom: 8px;
    }

    .nav-link {
        display: block;
        padding: 12px 16px;
        width: 100%;
    }

    /* Language selector improvements */
    .language-selector-toggle {
        padding: 12px 16px;
        min-width: 44px;
        min-height: 44px;
    }

    .language-option {
        padding: 12px 16px;
        min-height: 44px;
    }

    /* Card and button improvements */
    .card {
        margin-bottom: 24px;
    }

    .btn {
        width: 100%;
        margin-bottom: 16px;
        justify-content: center;
        align-items: center;
        display: flex;
    }

    /* Improved spacing for better touch targets */
    .service-card,
    .blog-card {
        padding: 20px;
        margin-bottom: 24px;
    }

    /* Footer improvements */
    .footer-links-column ul li {
        margin-bottom: 16px;
    }

    .footer-bottom-links a {
        display: inline-block;
        padding: 8px 12px;
        margin: 4px;
    }

    /* Social media links */
    .social-links a {
        width: 44px;
        height: 44px;
        margin: 0 8px;
    }
}

/* Tablet improvements */
@media screen and (max-width: 992px) {
    .container {
        padding: 0 20px;
    }

    /* Improved grid layouts */
    .services-grid,
    .blog-highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
    }

    /* Better spacing for touch targets */
    .nav-item {
        margin-left: 20px;
    }

    .btn {
        min-width: 160px;
        padding: 12px 24px;
    }
}

/* Landscape phone improvements */
@media screen and (max-width: 576px) {
    html {
        font-size: 15px;
    }

    .hero {
        padding: 48px 0;
    }

    .hero h1 {
        font-size: 2rem;
        margin-bottom: 16px;
    }

    /* Stack buttons vertically */
    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        margin-bottom: 12px;
    }

    /* Improve table responsiveness */
    table {
        display: block;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Better spacing for small screens */
    .section {
        padding: 40px 0;
    }

    .card-grid,
    .services-grid,
    .blog-highlights-grid {
        grid-template-columns: 1fr;
    }
}

/* --- Performance & UX Improvements --- */

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background-color: var(--primary-color);
    color: var(--light-text);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 98;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--accent-color);
    transform: translateY(-3px);
}

/* Page Loading Indicator */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    z-index: 9999;
}

.page-loader.loading {
    transform: scaleX(0.3);
    transition: transform 3s cubic-bezier(0.1, 0.05, 0.1, 0.95);
}

.page-loader.complete {
    transform: scaleX(1);
    transition: transform 0.3s ease;
}

/* Skip Navigation Link */
.skip-nav {
    position: absolute;
    top: -40px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: max-content;
    background: var(--primary-color);
    color: var(--light-text);
    padding: 8px 16px;
    border-radius: 0 0 4px 4px;
    transition: top 0.3s ease;
    z-index: 1000;
}

.skip-nav:focus {
    top: 0;
}

/* Enhanced Focus Styles */
:focus-visible {
    outline: 3px solid var(--accent-color);
    outline-offset: 3px;
    border-radius: 2px;
}

/* Breadcrumbs Navigation */
.breadcrumbs {
    padding: 1rem 0;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.breadcrumbs ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.breadcrumbs li:not(:last-child):after {
    content: "/";
    margin-left: 0.5rem;
    color: var(--border-color);
}

.breadcrumbs a {
    color: var(--text-color);
    text-decoration: none;
}

.breadcrumbs a:hover {
    color: var(--primary-color);
}

.breadcrumbs .current {
    color: var(--primary-color);
    font-weight: 500;
}

/* Enhanced Form Validation States */
.form-group {
    position: relative;
}

.form-feedback {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.form-feedback.error {
    color: var(--error-color);
}

.form-feedback.success {
    color: #198754;
}

/* Loading States for Interactive Elements */
.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

.btn-loading:after {
    content: "";
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-right-color: currentColor;
    border-radius: 50%;
    position: absolute;
    right: 1rem;
    animation: button-loading 0.8s linear infinite;
}

@keyframes button-loading {
    to {
        transform: rotate(360deg);
    }
}

/* Enhanced Error Pages */
.error-page {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
}

.error-page h1 {
    font-size: 6rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.error-page p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Print Styles Enhancement */
@media print {
    .no-print {
        display: none !important;
    }

    a[href]:after {
        content: " (" attr(href) ")";
    }

    body {
        font-size: 12pt;
    }

    .container {
        max-width: 100%;
        padding: 0;
    }
}