/* TechSupport Pro - Blog Specific Styles */

/* Blog Hero Section */
.blog-hero {
  background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), 
              url('../images/blog/blog-hero-bg.jpg') no-repeat center center;
  background-size: cover;
  color: var(--light-text);
  padding: 4rem 0;
  text-align: center;
}

.blog-hero h1 {
  font-size: 2.8rem;
  margin-bottom: 1rem;
}

.blog-hero p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

/* Blog Filters */
.blog-filters {
  background-color: var(--secondary-color);
  padding: 1.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.search-box {
  position: relative;
  max-width: 350px;
  width: 100%;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 50px;
  font-family: var(--font-main);
  font-size: 0.95rem;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0,86,179,0.1);
}

.search-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
}

.search-button svg {
  fill: currentColor;
}

.category-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-label {
  margin-right: 0.5rem;
  font-weight: 500;
  color: #666;
}

.filter-btn {
  background: none;
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Featured Post */
.featured-post {
  padding: 3rem 0;
}

.featured-post-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease;
}

.featured-post-card:hover {
  transform: translateY(-5px);
}

.featured-post-image {
  position: relative;
  height: 100%;
}

.featured-post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.featured-post-badge {
  position: absolute;
  top: 20px;
  right: 0;
  background-color: var(--accent-color);
  color: var(--text-color);
  font-weight: bold;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.featured-post-content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.post-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.post-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  margin-right: 1rem;
  text-transform: uppercase;
}

.post-date {
  color: #666;
  font-size: 0.875rem;
}

.post-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.post-excerpt {
  color: #555;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.featured-post-content .btn {
  align-self: flex-start;
  margin-top: auto;
}

/* Blog Post Grid */
.blog-posts {
  padding: 3rem 0 5rem;
  background-color: #f9f9f9;
}

.post-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.post-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.post-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.08);
}

.post-image {
  position: relative;
  display: block;
}

.post-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.post-image .post-category {
  position: absolute;
  bottom: 10px;
  left: 10px;
  margin-right: 0;
}

.post-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.post-content .post-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.post-content .post-title a {
  color: var(--text-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.post-content .post-title a:hover {
  color: var(--primary-color);
}

.post-content .post-date {
  margin-bottom: 1rem;
}

/* Load More Button */
.load-more-container {
  text-align: center;
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Newsletter Section */
.newsletter-section {
  padding: 4rem 0;
  background-color: #f0f5ff;
}

.newsletter-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.newsletter-content h2 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.newsletter-content p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.newsletter-form {
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-form .form-group {
  display: flex;
}

.newsletter-form input[type="email"] {
  flex-grow: 1;
  padding: 0.9rem 1.2rem;
  border: 1px solid var(--border-color);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  font-family: var(--font-main);
  font-size: 1rem;
}

.newsletter-form .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.form-checkbox {
  display: flex;
  align-items: flex-start;
  margin-top: 1rem;
  text-align: left;
}

.form-checkbox input {
  margin-right: 0.5rem;
  margin-top: 0.3rem;
}

.form-checkbox label {
  font-size: 0.9rem;
  line-height: 1.5;
}

.newsletter-message {
  margin-top: 1rem;
  font-weight: 500;
}

.newsletter-message.success {
  color: #2e7d32;
}

.newsletter-message.error {
  color: #d32f2f;
}

/* Blog Post Page Styles */
.blog-post-header {
  padding: 3rem 0;
  text-align: center;
}

.blog-post-header h1 {
  font-size: 2.5rem;
  max-width: 900px;
  margin: 0 auto 1rem;
}

.blog-post-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.blog-post-meta .author {
  display: flex;
  align-items: center;
}

.author-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 0.75rem;
}

.author-name {
  font-weight: 500;
}

.blog-post-meta .post-date {
  display: flex;
  align-items: center;
}

.blog-post-meta .post-date svg {
  margin-right: 0.5rem;
}

.blog-post-meta .post-category {
  margin-right: 0;
}

.blog-post-hero {
  max-width: 1000px;
  margin: 0 auto 3rem;
}

.blog-post-hero img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.blog-post-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem 4rem;
}

.blog-post-content p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  font-size: 1.1rem;
}

.blog-post-content h2 {
  margin: 2.5rem 0 1rem;
  font-size: 1.8rem;
  color: var(--primary-color);
}

.blog-post-content h3 {
  margin: 2rem 0 1rem;
  font-size: 1.5rem;
}

.blog-post-content ul, 
.blog-post-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.blog-post-content li {
  margin-bottom: 0.5rem;
}

.blog-post-content blockquote {
  padding: 1.5rem;
  margin: 2rem 0;
  background-color: #f9f9f9;
  border-left: 4px solid var(--primary-color);
  font-style: italic;
}

.blog-post-content img {
  max-width: 100%;
  border-radius: 8px;
  margin: 2rem 0;
}

.blog-post-content .tip-box {
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 4px;
}

.blog-post-content .warning-box {
  background-color: #fff8e1;
  border-left: 4px solid #ffc107;
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 4px;
}

/* Related Posts */
.related-posts {
  background-color: #f9f9f9;
  padding: 4rem 0;
}

.related-posts h2 {
  text-align: center;
  margin-bottom: 3rem;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

/* Share Post */
.share-post {
  margin-top: 3rem;
  text-align: center;
}

.share-post h3 {
  margin-bottom: 1rem;
}

.share-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f0f0f0;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.share-button.facebook:hover {
  background-color: #1877f2;
}

.share-button.twitter:hover {
  background-color: #1da1f2;
}

.share-button.linkedin:hover {
  background-color: #0a66c2;
}

.share-button.email:hover {
  background-color: #ea4335;
}

.share-button svg {
  fill: #555;
  transition: fill 0.3s ease;
}

.share-button:hover svg {
  fill: white;
}

/* Blog Post Form */
.blog-post-form {
    background-color: var(--light-bg);
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.blog-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.blog-form .form-group {
    margin-bottom: 1.5rem;
}

.blog-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-text);
}

.blog-form input[type="text"],
.blog-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.blog-form input[type="text"]:focus,
.blog-form textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,86,179,0.1);
}

.blog-form button[type="submit"] {
    background: var(--primary-color);
    color: white;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.blog-form button[type="submit"]:hover {
    background: var(--primary-dark);
}

/* ICTTilburg Section */
.icttilburg-section {
    background: #f1f8ff;
    border-left: 6px solid var(--primary-color);
    border-radius: 12px;
    padding: 2rem;
    margin: 3rem auto;
    max-width: 800px;
    text-align: center;
}

.icttilburg-content h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.icttilburg-content .subtitle {
    font-size: 1.1rem;
    color: var(--secondary-text);
    margin: 0;
}

/* Responsive Blog Styles */
@media screen and (max-width: 1200px) {
  .post-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 992px) {
  .featured-post-card {
    grid-template-columns: 1fr;
  }
  
  .featured-post-image {
    min-height: 300px;
  }
  
  .related-posts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .category-filters {
    justify-content: center;
  }
  
  .post-grid {
    grid-template-columns: 1fr;
  }
  
  .blog-hero h1 {
    font-size: 2rem;
  }
  
  .blog-post-header h1 {
    font-size: 2rem;
  }
  
  .related-posts-grid {
    grid-template-columns: 1fr;
  }
  
  .newsletter-form .form-group {
    flex-direction: column;
  }
  
  .newsletter-form input[type="email"] {
    border-radius: 4px;
    margin-bottom: 0.75rem;
  }
  
  .newsletter-form .btn {
    border-radius: 4px;
    width: 100%;
  }
}