/* 
 * Loading States and Animations
 * This file contains CSS for loading indicators, skeleton screens and animations
 */

/* ====== PAGE LOADING OVERLAY ====== */
.loading-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.loaded {
    opacity: 0;
    visibility: hidden;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner-circle {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 86, 179, 0.1);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

.loading-text {
    margin-top: 0.75rem;
    font-size: 1rem;
    color: var(--text-color);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ====== BUTTON LOADING STATES ====== */
.btn {
    position: relative;
}

.btn.loading {
    color: transparent !important;
}

.btn.loading .btn-spinner {
    opacity: 1;
}

.btn-spinner {
    position: absolute;
    left: calc(50% - 0.5rem);
    top: calc(50% - 0.5rem);
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-top-color: white;
    border-radius: 50%;
    opacity: 0;
    animation: spin 0.6s linear infinite;
}

.btn.btn-outline .btn-spinner,
.btn.btn-light .btn-spinner {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--primary-color);
}

/* ====== FORM LOADING OVERLAY ====== */
.form-loading-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.form-loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.form-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--primary-color);
}

.form-spinner .spinner-circle {
    width: 30px;
    height: 30px;
}

.form-spinner span {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

/* ====== SKELETON LOADING ====== */
.skeleton-loading {
    position: relative;
    overflow: hidden;
}

.skeleton-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

.skeleton-line {
    height: 16px;
    margin-bottom: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

@keyframes shimmer {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(100%);
    }
}

.skeleton-loading.loaded {
    animation: fadeIn 0.3s ease;
}

.load-error-message {
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.load-error-message p {
    margin-bottom: 1rem;
    color: #dc3545;
}

/* ====== LAZY LOAD ELEMENTS ====== */
.lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.lazy-load.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Different entrance animations */
.lazy-load.fade-in {
    transform: translateY(0);
}

.lazy-load.slide-in-right {
    transform: translateX(40px);
}

.lazy-load.slide-in-right.in-view {
    transform: translateX(0);
}

.lazy-load.zoom-in {
    transform: scale(0.95);
}

.lazy-load.zoom-in.in-view {
    transform: scale(1);
}

/* ====== ANIMATION TOGGLE ====== */
.animation-toggle-wrapper {
    margin-top: 2rem;
    text-align: center;
}

.animation-toggle-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    font-size: 0.875rem;
    color: var(--text-color);
    cursor: pointer;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background-color: #ccc;
    border-radius: 20px;
    margin-left: 8px;
    transition: background-color 0.2s ease;
}

.toggle-switch::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.2s ease;
}

.animation-toggle-btn[aria-pressed="true"] .toggle-switch {
    background-color: var(--primary-color);
}

.animation-toggle-btn[aria-pressed="true"] .toggle-switch::after {
    transform: translateX(20px);
}

/* ====== REDUCED MOTION ====== */
body.reduce-motion *,
body.reduce-motion *::before,
body.reduce-motion *::after {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
    scroll-behavior: auto !important;
}

@media (prefers-reduced-motion: reduce) {
    .loading-overlay {
        transition: none;
    }
    
    .spinner-circle {
        animation: none;
        border-top-color: var(--primary-color);
        border-left-color: var(--primary-color);
    }
    
    .btn-spinner {
        animation: none;
    }
    
    .form-loading-overlay {
        transition: none;
    }
    
    .skeleton-loading::after {
        animation: none;
        display: none;
    }
    
    .lazy-load {
        transition: none;
        opacity: 1;
        transform: none;
    }
    
    .toggle-switch,
    .toggle-switch::after {
        transition: none;
    }
}
