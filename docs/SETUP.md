# TechSupport Pro - Setup Guide

## Prerequisites

- Node.js 18+ and npm 9+
- Modern web browser
- Git

## Quick Start

1. **<PERSON>lone and Install**
   ```bash
   git clone <repository-url>
   cd techsupport-pro
   npm install
   ```

2. **Development**
   ```bash
   npm run dev
   ```
   Opens development server at `http://localhost:3000`

3. **Build for Production**
   ```bash
   npm run build
   ```

4. **Preview Production Build**
   ```bash
   npm run preview
   ```

## Available Scripts

### Development
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally

### Code Quality
- `npm run lint` - Check JavaScript for errors
- `npm run lint:fix` - Fix JavaScript errors automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### Testing
- `npm test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run a11y-test` - Run accessibility tests
- `npm run lighthouse` - Run Lighthouse performance audit

### Optimization
- `npm run optimize-images` - Optimize images for web
- `npm run generate-sitemap` - Generate XML sitemap
- `npm run clean` - Clean build directory

## Project Structure

```
src/
├── assets/           # Static assets (images, fonts)
├── styles/           # SCSS stylesheets (7-1 pattern)
│   ├── abstracts/    # Variables, mixins, functions
│   ├── base/         # Reset, typography, base styles
│   ├── components/   # UI components
│   ├── layout/       # Layout-specific styles
│   ├── pages/        # Page-specific styles
│   ├── themes/       # Theme variations
│   ├── utilities/    # Utility classes
│   └── main.scss     # Main stylesheet
├── scripts/          # JavaScript modules
│   ├── components/   # UI components
│   ├── services/     # Business logic
│   ├── utils/        # Utility functions
│   └── main.js       # Entry point
├── templates/        # HTML templates
├── data/            # Static data files
└── index.html       # Main HTML file
```

## Development Workflow

1. **Feature Development**
   - Create feature branch
   - Make changes in `src/` directory
   - Test with `npm run dev`
   - Run quality checks: `npm run lint && npm run format:check`

2. **Testing**
   - Write unit tests in `tests/` directory
   - Run tests: `npm test`
   - Check accessibility: `npm run a11y-test`

3. **Performance**
   - Optimize images: `npm run optimize-images`
   - Check performance: `npm run lighthouse`
   - Monitor Core Web Vitals in browser

4. **Deployment**
   - Build: `npm run build`
   - Test build: `npm run preview`
   - Deploy `dist/` directory

## Configuration

### Vite (Build Tool)
- Configuration: `vite.config.js`
- Handles SCSS compilation, JS bundling, optimization

### ESLint (Code Quality)
- Configuration: `.eslintrc.js`
- Rules for code quality and consistency

### Prettier (Code Formatting)
- Configuration: `.prettierrc`
- Automatic code formatting

### PostCSS (CSS Processing)
- Configuration: `postcss.config.js`
- Autoprefixer and CSS optimization

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance Targets

- Lighthouse Performance: 90+
- Lighthouse Accessibility: 95+
- Lighthouse Best Practices: 90+
- Lighthouse SEO: 95+

## Troubleshooting

### Common Issues

1. **Build Errors**
   - Clear cache: `rm -rf node_modules/.vite`
   - Reinstall: `rm -rf node_modules && npm install`

2. **SCSS Compilation Issues**
   - Check import paths in `src/styles/main.scss`
   - Ensure all partial files start with underscore

3. **JavaScript Module Errors**
   - Check import/export syntax
   - Verify file paths and extensions

### Getting Help

- Check browser console for errors
- Run `npm run lint` for code issues
- Check network tab for loading issues
- Use `npm run lighthouse` for performance insights
