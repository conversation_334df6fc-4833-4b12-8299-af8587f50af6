<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechSupport Pro - Service & Ondersteuning</title>
    <meta name="description" content="Professionele IT-ondersteuning voor bedrijven en particulieren in Nederland. 24/7 technische support en IT-services.">
    <meta name="theme-color" content="#667eea">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }

        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 5%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #667eea;
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: #333;
            margin: 3px 0;
            transition: 0.3s;
        }

        .hero {
            padding: 120px 5% 80px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease;
        }

        .hero p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 1s ease 0.2s both;
        }

        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease 0.4s both;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 5%;
        }

        .section {
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .faq-section {
            background: #f8f9fa;
        }

        .faq-item {
            background: white;
            margin-bottom: 1rem;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .faq-question {
            padding: 1.5rem;
            background: white;
            border: none;
            width: 100%;
            text-align: left;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
            position: relative;
        }

        .faq-question:hover {
            background: #f8f9fa;
        }

        .faq-question::after {
            content: '+';
            position: absolute;
            right: 1.5rem;
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .faq-question.active::after {
            transform: rotate(45deg);
        }

        .faq-answer {
            padding: 0 1.5rem;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 1.5rem;
            max-height: 200px;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .contact-info {
            text-align: center;
            padding: 2rem;
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            backdrop-filter: blur(10px);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: white;
                flex-direction: column;
                padding: 2rem;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .hamburger {
                display: flex;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <a href="#" class="logo">TechSupport Pro</a>
            <ul class="nav-links" id="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#services">Diensten</a></li>
                <li><a href="#faq">Veelgestelde Vragen</a></li>
                <li><a href="#contact">Contact</a></li>
                <li><a href="#" id="lang-switch" aria-label="Switch to English">EN</a></li>
            </ul>
            <div class="hamburger" onclick="toggleMenu()" aria-expanded="false" aria-controls="nav-menu" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <section id="home" class="hero gradient-bg">
        <div class="floating-elements">
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
        </div>
        <div class="container">
            <h1>Professionele IT-Ondersteuning</h1>
            <p>Wij bieden 24/7 technische support en IT-services voor bedrijven en particulieren. Uw technologische uitdagingen zijn onze specialiteit.</p>
            <a href="#contact" class="cta-button">Neem Contact Op</a>
        </div>
    </section>

    <section id="services" class="section">
        <div class="container">
            <h2 class="section-title">Onze Diensten</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🔧</div>
                    <h3>Technische Support</h3>
                    <p>24/7 technische ondersteuning voor al uw IT-problemen. Van software-installatie tot hardware-reparaties.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🛡️</div>
                    <h3>Cybersecurity</h3>
                    <p>Bescherm uw systemen tegen cyber-aanvallen met onze geavanceerde beveiligingsoplossingen.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">☁️</div>
                    <h3>Cloud Services</h3>
                    <p>Migratie naar de cloud, backup-oplossingen en cloud-infrastructuur beheer voor uw bedrijf.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">📱</div>
                    <h3>Mobiele Support</h3>
                    <p>Ondersteuning voor smartphones, tablets en mobiele applicaties voor iOS en Android.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🔄</div>
                    <h3>Data Recovery</h3>
                    <p>Professionele gegevensherstel services voor verloren of beschadigde bestanden en databases.</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">⚡</div>
                    <h3>Netwerk Optimalisatie</h3>
                    <p>Verbeter de prestaties van uw netwerk met onze optimalisatie en monitoring services.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="faq" class="section faq-section">
        <div class="container">
            <h2 class="section-title">Veelgestelde Vragen</h2>
            <div class="faq-container">
                <div class="faq-item">
                    <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false" aria-controls="faq-answer-1">
                        Hoe snel krijg ik ondersteuning?
                    </button>
                    <div class="faq-answer" id="faq-answer-1">
                        <p>Wij bieden 24/7 ondersteuning met een gemiddelde responstijd van 15 minuten voor kritieke problemen en binnen 2 uur voor standaard vragen.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false" aria-controls="faq-answer-2">
                        Wat zijn de kosten voor uw diensten?
                    </button>
                    <div class="faq-answer" id="faq-answer-2">
                        <p>Onze tarieven variëren afhankelijk van de dienst. Wij bieden zowel eenmalige ondersteuning als maandelijkse service-abonnementen. Neem contact op voor een persoonlijke offerte.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false" aria-controls="faq-answer-3">
                        Werken jullie ook met particulieren?
                    </button>
                    <div class="faq-answer" id="faq-answer-3">
                        <p>Ja, wij verlenen onze diensten aan zowel bedrijven als particulieren. Van kleine computerproblemen tot complexe IT-infrastructuur.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false" aria-controls="faq-answer-4">
                        Bieden jullie ook ondersteuning op locatie?
                    </button>
                    <div class="faq-answer" id="faq-answer-4">
                        <p>Ja, naast remote ondersteuning bieden wij ook on-site service binnen de Randstad. Voor andere locaties in Nederland maken wij graag een afspraak.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false" aria-controls="faq-answer-5">
                        Welke garantie bieden jullie op jullie werk?
                    </button>
                    <div class="faq-answer" id="faq-answer-5">
                        <p>Wij bieden een 30-dagen garantie op al onze diensten. Als het probleem zich opnieuw voordoet binnen deze periode, lossen wij het kosteloos op.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="section contact-section">
        <div class="container">
            <h2 class="section-title" style="color: white;">Neem Contact Op</h2>
            <div class="contact-grid">
                <div class="contact-info">
                    <div class="contact-icon">📞</div>
                    <h3>Telefonisch</h3>
                    <p>+31 20 123 4567</p>
                    <p>24/7 beschikbaar</p>
                </div>
                <div class="contact-info">
                    <div class="contact-icon">✉️</div>
                    <h3>E-mail</h3>
                    <p><a href="mailto:<EMAIL>" style="color: white; text-decoration: underline;"><EMAIL></a></p>
                    <p><a href="mailto:<EMAIL>" style="color: white; text-decoration: underline;"><EMAIL></a></p>
                </div>
                <div class="contact-info">
                    <div class="contact-icon">💬</div>
                    <h3>Live Chat</h3>
                    <p>Beschikbaar op onze website</p>
                    <p>Ma-Vr: 08:00-18:00</p>
                </div>
                <div class="contact-info">
                    <div class="contact-icon">📍</div>
                    <h3>Locatie</h3>
                    <p>Amsterdam, Nederland</p>
                    <p>Landelijke service</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; 2025 TechSupport Pro. Alle rechten voorbehouden. | <a href="#privacy-policy" style="color: white; text-decoration: underline;">Privacy Policy</a> | <a href="#cookie-policy" style="color: white; text-decoration: underline;">Cookie Policy</a> | <a href="#terms" style="color: white; text-decoration: underline;">Algemene Voorwaarden</a></p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const navLinks = document.querySelector('.nav-links');
            const hamburger = document.querySelector('.hamburger');
            navLinks.classList.toggle('active');
            const isExpanded = navLinks.classList.contains('active');
            hamburger.setAttribute('aria-expanded', isExpanded);
        }

        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const isActive = element.classList.contains('active');
            
            // Close all FAQ items
            document.querySelectorAll('.faq-question').forEach(q => {
                q.classList.remove('active');
                q.nextElementSibling.classList.remove('active');
                q.setAttribute('aria-expanded', 'false');
            });
            
            // Toggle current item
            if (!isActive) {
                element.classList.add('active');
                answer.classList.add('active');
                element.setAttribute('aria-expanded', 'true');
            }
        }

        // Language switcher
        document.getElementById('lang-switch').addEventListener('click', function(e) {
            e.preventDefault();
            // Redirect to English version (to be implemented)
            alert('English version coming soon!');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                if(this.id === 'lang-switch') return; // Skip for language switcher
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 30px rgba(0, 0, 0, 0.15)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe service cards
        document.querySelectorAll('.service-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.transitionDelay = (index * 0.1) + 's';
            observer.observe(card);
        });
    </script>
</body>
</html>