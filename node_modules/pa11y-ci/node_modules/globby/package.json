{"name": "globby", "version": "6.1.0", "description": "Extends `glob` with support for multiple patterns and exposes a Promise API", "license": "MIT", "repository": "sindresorhus/globby", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"bench": "npm update glob-stream && matcha bench.js", "test": "xo && ava"}, "files": ["index.js"], "keywords": ["all", "array", "directories", "dirs", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise"], "dependencies": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "glob-stream": "gulpjs/glob-stream#master", "globby": "sindresorhus/globby#master", "matcha": "^0.7.0", "rimraf": "^2.2.8", "xo": "^0.16.0"}}