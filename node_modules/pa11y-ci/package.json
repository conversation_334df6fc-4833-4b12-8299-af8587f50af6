{"name": "pa11y-ci", "version": "3.1.0", "description": "Pa11y CI is a CI-centric accessibility test runner, built using Pa11y", "keywords": [], "author": "Team Pa11y", "contributors": ["<PERSON> (https://rowanmanning.com/)"], "repository": {"type": "git", "url": "https://github.com/pa11y/pa11y-ci.git"}, "homepage": "https://github.com/pa11y/pa11y-ci", "bugs": "https://github.com/pa11y/pa11y-ci/issues", "license": "LGPL-3.0-only", "engines": {"node": ">= 12"}, "dependencies": {"async": "~2.6.4", "cheerio": "~1.0.0-rc.10", "commander": "~6.2.1", "globby": "~6.1.0", "kleur": "~4.1.4", "lodash": "~4.17.21", "node-fetch": "~2.6.1", "pa11y": "^6.2.3", "protocolify": "~3.0.0", "puppeteer": "~9.1.1", "wordwrap": "~1.0.0"}, "devDependencies": {"eslint": "^7.27.0", "mocha": "^8.4.0", "mockery": "^2.1.0", "nyc": "^15.1.0", "pa11y-lint-config": "^3.0.0", "proclaim": "^3.6.0", "sinon": "^11.1.0"}, "main": "./lib/pa11y-ci.js", "bin": {"pa11y-ci": "./bin/pa11y-ci.js"}, "scripts": {"lint": "eslint .", "verify-coverage": "nyc check-coverage --lines 90 --functions 90 --branches 90", "test-unit": "mocha --file test/unit/setup.test.js 'test/unit/**/*.test.js' --recursive", "test-coverage": "nyc --reporter=text --reporter=html mocha --file test/unit/setup.test.js 'test/unit/**/*.test.js' --recursive", "test-integration": "mocha --file test/integration/setup.test.js 'test/integration/**/*.test.js' test/integration/teardown.test.js --recursive --timeout 20000 --slow 5000", "test": "npm run test-coverage && npm run verify-coverage && npm run test-integration"}, "files": ["bin", "lib"]}