/**
 * @param {import('./dom.js').DOM} dom
 */
export function createGauge(dom: import('./dom.js').DOM): Element;
/**
 * @param {import('./dom.js').DOM} dom
 * @param {Element} componentEl
 * @param {LH.ReportResult.Category} category
 */
export function updateGauge(dom: import('./dom.js').DOM, componentEl: Element, category: LH.ReportResult.Category): void;
//# sourceMappingURL=explodey-gauge.d.ts.map