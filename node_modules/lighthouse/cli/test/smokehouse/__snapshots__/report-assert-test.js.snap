// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`getAssertionReport works (multiple failing) 1`] = `
"X difference at cumulative-layout-shift audit.details.items.length
              expected: []
                 found: [{\\"cumulativeLayoutShiftMainFrame\\":0.13570762803819444,\\"newEngineResult\\":{\\"cumulativeLayoutShift\\":0.13570762803819444,\\"cumulativeLayoutShiftMainFrame\\":0.13570762803819444},\\"newEngineResultDiffered\\":false}]


  X difference at cumulative-layout-shift audit.details.blah
              expected: 123
                 found: undefined

          found result:
      {
        \\"id\\": \\"cumulative-layout-shift\\",
        \\"title\\": \\"Cumulative Layout Shift\\",
        \\"description\\": \\"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).\\",
        \\"score\\": 0.8,
        \\"scoreDisplayMode\\": \\"numeric\\",
        \\"numericValue\\": 0.13570762803819444,
        \\"numericUnit\\": \\"unitless\\",
        \\"displayValue\\": \\"0.136\\",
        \\"scoringOptions\\": {
          \\"p10\\": 0.1,
          \\"median\\": 0.25
        },
        \\"details\\": {
          \\"type\\": \\"debugdata\\",
          \\"items\\": [
            {
              \\"cumulativeLayoutShiftMainFrame\\": 0.13570762803819444,
              \\"newEngineResult\\": {
                \\"cumulativeLayoutShift\\": 0.13570762803819444,
                \\"cumulativeLayoutShiftMainFrame\\": 0.13570762803819444
              },
              \\"newEngineResultDiffered\\": false
            }
          ]
        }
      }"
`;

exports[`getAssertionReport works (trivial failing) 1`] = `
"X difference at cumulative-layout-shift audit.details.items.length
              expected: []
                 found: [{\\"cumulativeLayoutShiftMainFrame\\":0.13570762803819444,\\"newEngineResult\\":{\\"cumulativeLayoutShift\\":0.13570762803819444,\\"cumulativeLayoutShiftMainFrame\\":0.13570762803819444},\\"newEngineResultDiffered\\":false}]

          found result:
      {
        \\"id\\": \\"cumulative-layout-shift\\",
        \\"title\\": \\"Cumulative Layout Shift\\",
        \\"description\\": \\"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).\\",
        \\"score\\": 0.8,
        \\"scoreDisplayMode\\": \\"numeric\\",
        \\"numericValue\\": 0.13570762803819444,
        \\"numericUnit\\": \\"unitless\\",
        \\"displayValue\\": \\"0.136\\",
        \\"scoringOptions\\": {
          \\"p10\\": 0.1,
          \\"median\\": 0.25
        },
        \\"details\\": {
          \\"type\\": \\"debugdata\\",
          \\"items\\": [
            {
              \\"cumulativeLayoutShiftMainFrame\\": 0.13570762803819444,
              \\"newEngineResult\\": {
                \\"cumulativeLayoutShift\\": 0.13570762803819444,
                \\"cumulativeLayoutShiftMainFrame\\": 0.13570762803819444
              },
              \\"newEngineResultDiffered\\": false
            }
          ]
        }
      }"
`;

exports[`getAssertionReport works (trivial failing, actual undefined) 1`] = `
"Error: Config did not trigger run of expected audit cumulative-layout-shift-no-exist

  X difference at cumulative-layout-shift-no-exist audit
              expected: {\\"details\\":{\\"items\\":[]}}
                 found: undefined

          found result:
      undefined"
`;
