export { LanternFirstContentfulPaintComputed as LanternFirstContentfulPaint };
export type Node = import('../../lib/lantern/base-node.js').Node<LH.Artifacts.NetworkRequest>;
export type CPUNode = import('../../lib/lantern/cpu-node.js').CPUNode<LH.Artifacts.NetworkRequest>;
export type NetworkNode = import('../../lib/lantern/network-node.js').NetworkNode<LH.Artifacts.NetworkRequest>;
declare const LanternFirstContentfulPaintComputed: typeof LanternFirstContentfulPaint & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/lantern/base-node.js').Node<LH.Artifacts.NetworkRequest>} Node */
/** @typedef {import('../../lib/lantern/cpu-node.js').CPUNode<LH.Artifacts.NetworkRequest>} CPUNode */
/** @typedef {import('../../lib/lantern/network-node.js').NetworkNode<LH.Artifacts.NetworkRequest>} NetworkNode */
declare class LanternFirstContentfulPaint extends LanternMetric {
    /**
     * @typedef FirstPaintBasedGraphOpts
     * @property {number} cutoffTimestamp The timestamp used to filter out tasks that occured after
     *    our paint of interest. Typically this is First Contentful Paint or First Meaningful Paint.
     * @property {function(NetworkNode):boolean} treatNodeAsRenderBlocking The function that determines
     *    which resources should be considered *possibly* render-blocking.
     * @property {(function(CPUNode):boolean)=} additionalCpuNodesToTreatAsRenderBlocking The function that
     *    determines which CPU nodes should also be included in our blocking node IDs set,
     *    beyond what getRenderBlockingNodeData() already includes.
     */
    /**
     * This function computes the set of URLs that *appeared* to be render-blocking based on our filter,
     * *but definitely were not* render-blocking based on the timing of their EvaluateScript task.
     * It also computes the set of corresponding CPU node ids that were needed for the paint at the
     * given timestamp.
     *
     * @param {Node} graph
     * @param {FirstPaintBasedGraphOpts} opts
     * @return {{definitelyNotRenderBlockingScriptUrls: Set<string>, renderBlockingCpuNodeIds: Set<string>}}
     */
    static getRenderBlockingNodeData(graph: Node, { cutoffTimestamp, treatNodeAsRenderBlocking, additionalCpuNodesToTreatAsRenderBlocking }: {
        /**
         * The timestamp used to filter out tasks that occured after
         * our paint of interest. Typically this is First Contentful Paint or First Meaningful Paint.
         */
        cutoffTimestamp: number;
        /**
         * The function that determines
         * which resources should be considered *possibly* render-blocking.
         */
        treatNodeAsRenderBlocking: (arg0: NetworkNode) => boolean;
        /**
         * The function that
         * determines which CPU nodes should also be included in our blocking node IDs set,
         * beyond what getRenderBlockingNodeData() already includes.
         */
        additionalCpuNodesToTreatAsRenderBlocking?: ((arg0: CPUNode) => boolean) | undefined;
    }): {
        definitelyNotRenderBlockingScriptUrls: Set<string>;
        renderBlockingCpuNodeIds: Set<string>;
    };
    /**
     * This function computes the graph required for the first paint of interest.
     *
     * @param {Node} dependencyGraph
     * @param {FirstPaintBasedGraphOpts} opts
     * @return {Node}
     */
    static getFirstPaintBasedGraph(dependencyGraph: Node, { cutoffTimestamp, treatNodeAsRenderBlocking, additionalCpuNodesToTreatAsRenderBlocking }: {
        /**
         * The timestamp used to filter out tasks that occured after
         * our paint of interest. Typically this is First Contentful Paint or First Meaningful Paint.
         */
        cutoffTimestamp: number;
        /**
         * The function that determines
         * which resources should be considered *possibly* render-blocking.
         */
        treatNodeAsRenderBlocking: (arg0: NetworkNode) => boolean;
        /**
         * The function that
         * determines which CPU nodes should also be included in our blocking node IDs set,
         * beyond what getRenderBlockingNodeData() already includes.
         */
        additionalCpuNodesToTreatAsRenderBlocking?: ((arg0: CPUNode) => boolean) | undefined;
    }): Node;
    /**
     * @param {Node} dependencyGraph
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node, processedNavigation: LH.Artifacts.ProcessedNavigation): Node;
    /**
     * @param {Node} dependencyGraph
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node, processedNavigation: LH.Artifacts.ProcessedNavigation): Node;
}
import { LanternMetric } from './lantern-metric.js';
//# sourceMappingURL=lantern-first-contentful-paint.d.ts.map