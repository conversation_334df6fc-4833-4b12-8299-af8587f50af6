/**
 * @license
 * Copyright 2016 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {Audit} from './audit.js';
import * as i18n from '../lib/i18n/i18n.js';

const UIStrings = {
  /** Title of a Lighthouse audit that provides detail on the content size of a web site compared to the viewport, which is the size of the screen the site is displayed on. This descriptive title is shown to users when the site's content is sized appropriately. */
  title: 'Content is sized correctly for the viewport',
  /** Title of a Lighthouse audit that provides detail on the content size of a web site compared to the viewport, which is the size of the screen the site is displayed on. This descriptive title is shown to users when the site's content is not sized appropriately. */
  failureTitle: 'Content is not sized correctly for the viewport',
  /** Description of a Lighthouse audit that tells the user why they should care that a site's content size should match its viewport size, which is the size of the screen the site is displayed on. This is displayed after a user expands the section to see more. No character length limits. The last sentence starting with 'Learn' becomes link text to additional documentation. */
  description: 'If the width of your app\'s content doesn\'t match the width ' +
    'of the viewport, your app might not be optimized for mobile screens. ' +
    '[Learn how to size content for the viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/).',
  /**
   * @description Explanatory message stating that the viewport size and window size differ.
   * @example {100} innerWidth
   * @example {101} outerWidth
   * */
  explanation: 'The viewport size of {innerWidth}px does not match the window ' +
    'size of {outerWidth}px.',
};

const str_ = i18n.createIcuMessageFn(import.meta.url, UIStrings);

class ContentWidth extends Audit {
  /**
   * @return {LH.Audit.Meta}
   */
  static get meta() {
    return {
      id: 'content-width',
      title: str_(UIStrings.title),
      failureTitle: str_(UIStrings.failureTitle),
      description: str_(UIStrings.description),
      requiredArtifacts: ['ViewportDimensions'],
    };
  }

  /**
   * @param {LH.Artifacts} artifacts
   * @param {LH.Audit.Context} context
   * @return {LH.Audit.Product}
   */
  static audit(artifacts, context) {
    const viewportWidth = artifacts.ViewportDimensions.innerWidth;
    const windowWidth = artifacts.ViewportDimensions.outerWidth;
    const widthsMatch = viewportWidth === windowWidth;

    if (context.settings.formFactor === 'desktop') {
      return {
        score: 1,
        notApplicable: true,
      };
    }

    let explanation;
    if (!widthsMatch) {
      explanation = str_(UIStrings.explanation,
        {innerWidth: artifacts.ViewportDimensions.innerWidth,
          outerWidth: artifacts.ViewportDimensions.outerWidth});
    }

    return {
      score: Number(widthsMatch),
      explanation,
    };
  }
}

export default ContentWidth;
export {UIStrings};
