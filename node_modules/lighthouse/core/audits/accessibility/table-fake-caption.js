/**
 * @license
 * Copyright 2023 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * @fileoverview Ensure that tables use `<caption>` instead of colspan for a caption.
 * See base class in axe-audit.js for audit() implementation.
 */

import AxeAudit from './axe-audit.js';
import * as i18n from '../../lib/i18n/i18n.js';

const UIStrings = {
  /** Title of an accesibility audit that evaluates if all tables use caption instead of colspan to indicate a caption. This title is descriptive of the successful state and is shown to users when no user action is required. */
  title: 'Tables use `<caption>` instead of cells with the `[colspan]` attribute to indicate a ' +
      'caption.',
  /** Title of an accesibility audit that evaluates if all tables use caption instead of colspan to indicate a caption. This title is descriptive of the failing state and is shown to users when there is a failure that needs to be addressed. */
  failureTitle: 'Tables do not use `<caption>` instead of cells with the `[colspan]` attribute ' +
      'to indicate a caption.',
  /** Description of a Lighthouse audit that tells the user *why* they should try to pass. This is displayed after a user expands the section to see more. No character length limits. The last sentence starting with 'Learn' becomes link text to additional documentation. */
  description: 'Screen readers have features to make navigating tables easier. Ensuring ' +
      'that tables use the actual caption element instead of cells with the `[colspan]` ' +
      'attribute may improve the experience for screen reader users. ' +
      '[Learn more about captions](https://dequeuniversity.com/rules/axe/4.9/table-fake-caption).',
};

const str_ = i18n.createIcuMessageFn(import.meta.url, UIStrings);

class TableFakeCaption extends AxeAudit {
  /**
   * @return {LH.Audit.Meta}
   */
  static get meta() {
    return {
      id: 'table-fake-caption',
      title: str_(UIStrings.title),
      failureTitle: str_(UIStrings.failureTitle),
      description: str_(UIStrings.description),
      requiredArtifacts: ['Accessibility'],
    };
  }
}

export default TableFakeCaption;
export {UIStrings};
