export default InstallableManifest;
/**
 * @fileoverview
 * Audits if the page's web app manifest and service worker qualify for triggering a beforeinstallprompt event.
 * https://github.com/GoogleChrome/lighthouse/issues/23#issuecomment-270453303
 *
 * Requirements based on Chrome Devtools' installability requirements.
 * Origin of logging:
 * https://source.chromium.org/chromium/chromium/src/+/main:chrome/browser/installable/installable_logging.cc
 * DevTools InstallabilityError implementation:
 * https://source.chromium.org/search?q=getInstallabilityErrorMessages&ss=chromium%2Fchromium%2Fsrc:third_party%2Fdevtools-frontend%2Fsrc%2Ffront_end%2Fresources%2F
 */
declare class InstallableManifest extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {{i18nErrors: Array<LH.IcuMessage | string>; warnings: Array<LH.IcuMessage>}}
     */
    static getInstallabilityErrors(artifacts: LH.Artifacts): {
        i18nErrors: Array<LH.IcuMessage | string>;
        warnings: Array<LH.IcuMessage>;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     *
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export const UIStrings: {
    /** Title of a Lighthouse audit that provides detail on if a website is installable as an application. This descriptive title is shown to users when a webapp is installable. */
    title: string;
    /** Title of a Lighthouse audit that provides detail on if a website is installable as an application. This descriptive title is shown to users when a webapp is not installable. */
    failureTitle: string;
    /** Description of a Lighthouse audit that tells the user why installability is important for webapps. This is displayed after a user expands the section to see more. No character length limits. The last sentence starting with 'Learn' becomes link text to additional documentation. */
    description: string;
    /** Label for a column in a data table; entries in the column will be a string explaining why a failure occurred. */
    columnValue: string;
    /**
     * @description [ICU Syntax] Label for an audit identifying the number of installability errors found in the page.
     */
    displayValue: string;
    /**
     * @description Error message describing a DevTools error id that was found and has not been identified by this audit.
     * @example {platform-not-supported-on-android} errorId
     */
    noErrorId: string;
    /** Error message explaining that the page is not loaded in the frame.  */
    'not-in-main-frame': string;
    /** Error message explaining that the page is served from a secure origin. */
    'not-from-secure-origin': string;
    /** Error message explaining that the page has no manifest URL. */
    'no-manifest': string;
    /** Error message explaining that the provided manifest URL is invalid. */
    'start-url-not-valid': string;
    /** Error message explaining that the provided manifest does not contain a name or short_name field. */
    'manifest-missing-name-or-short-name': string;
    /** Error message explaining that the manifest display property must be one of 'standalone', 'fullscreen', or 'minimal-ui'. */
    'manifest-display-not-supported': string;
    /** Error message explaining that the manifest could not be fetched, might be empty, or could not be parsed. */
    'manifest-empty': string;
    /** Error message explaining that the manifest could not be fetched, might be empty, or could not be parsed. */
    'manifest-parsing-or-network-error': string;
    /**
     * @description Error message explaining that the manifest does not contain a suitable icon.
     * @example {192} value0
     */
    'manifest-missing-suitable-icon': string;
    /**
     * @description Error message explaining that the manifest does not supply an icon of the correct format.
     * @example {192} value0
     */
    'no-acceptable-icon': string;
    /** Error message explaining that the icon could not be downloaded. */
    'cannot-download-icon': string;
    /** Error message explaining that the downloaded icon was empty or corrupt. */
    'no-icon-available': string;
    /** Error message explaining that the specified application platform is not supported on Android. */
    'platform-not-supported-on-android': string;
    /** Error message explaining that a Play store ID was not provided. */
    'no-id-specified': string;
    /** Error message explaining that the Play Store app URL and Play Store ID do not match. */
    'ids-do-not-match': string;
    /** Error message explaining that the app is already installed. */
    'already-installed': string;
    /** Error message explaining that a URL in the manifest contains a username, password, or port. */
    'url-not-supported-for-webapk': string;
    /** Error message explaining that the page is loaded in an incognito window. */
    'in-incognito': string;
    /** Error message explaining that the page does not work offline. */
    'not-offline-capable': string;
    /** Error message explaining that service worker could not be checked without a start_url. */
    'no-url-for-service-worker': string;
    /** Error message explaining that the manifest specifies prefer_related_applications: true. */
    'prefer-related-applications': string;
    /** Error message explaining that prefer_related_applications is only supported on Chrome Beta and Stable channels on Android. */
    'prefer-related-applications-only-beta-stable': string;
    /** Error message explaining that the manifest contains 'display_override' field, and the
        first supported display mode must be one of 'standalone', 'fullscreen', or 'minimal-ui'. */
    'manifest-display-override-not-supported': string;
    /** Error message explaining that the web manifest's URL changed while the manifest was being downloaded by the browser. */
    'manifest-location-changed': string;
    /** Warning message explaining that the page does not work offline. */
    'warn-not-offline-capable': string;
    /** Error message explaining that Lighthouse failed while checking if the page is installable, and directing the user to try again in a new Chrome. */
    'protocol-timeout': string;
    /** Message logged when the web app has been uninstalled o desktop, signalling that the install banner state is being reset. */
    'pipeline-restarted': string;
};
import { Audit } from './audit.js';
//# sourceMappingURL=installable-manifest.d.ts.map