export default BFCache;
declare class BFCache extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const actionableFailureType: string;
    const notActionableFailureType: string;
    const supportPendingFailureType: string;
    const failureReasonColumn: string;
    const failureTypeColumn: string;
    const warningHeadless: string;
    const displayValue: string;
}
import { Audit } from './audit.js';
//# sourceMappingURL=bf-cache.d.ts.map