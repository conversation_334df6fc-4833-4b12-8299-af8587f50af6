export namespace pageFunctions {
    export { wrapRuntimeEvalErrorInBrowser };
    export { getElementsInDocument };
    export { getOuterHTMLSnippet };
    export { computeBenchmarkIndex };
    export { getNodeDetails };
    export { getNodePath };
    export { getNodeSelector };
    export { getNodeLabel };
    export { isPositionFixed };
    export { wrapRequestIdleCallback };
    export { getBoundingClientRect };
    export { truncate };
    export { esbuildFunctionWrapperString };
    export { getRuntimeFunctionName };
}
/**
 * `typed-query-selector`'s CSS selector parser.
 */
export type ParseSelector<T extends string> = import('typed-query-selector/parser').ParseSelector<T>;
/**
 * @fileoverview
 * Helper functions that are passed by `toString()` by Driver to be evaluated in target page.
 *
 * Every function in this module only runs in the browser, so it is ignored from
 * the c8 code coverage tool. See c8.sh
 *
 * Important: this module should only be imported like this:
 *     const pageFunctions = require('...');
 * Never like this:
 *     const {justWhatINeed} = require('...');
 * Otherwise, minification will mangle the variable names and break usage.
 */
/**
 * `typed-query-selector`'s CSS selector parser.
 * @template {string} T
 * @typedef {import('typed-query-selector/parser').ParseSelector<T>} ParseSelector
 */
/**
 * The `exceptionDetails` provided by the debugger protocol does not contain the useful
 * information such as name, message, and stack trace of the error when it's wrapped in a
 * promise. Instead, map to a successful object that contains this information.
 * @param {string|Error} [err] The error to convert
 * @return {{__failedInBrowser: boolean, name: string, message: string, stack: string|undefined}}
 */
declare function wrapRuntimeEvalErrorInBrowser(err?: string | Error | undefined): {
    __failedInBrowser: boolean;
    name: string;
    message: string;
    stack: string | undefined;
};
/**
 * @template {string} T
 * @param {T=} selector Optional simple CSS selector to filter nodes on.
 *     Combinators are not supported.
 * @return {Array<ParseSelector<T>>}
 */
declare function getElementsInDocument<T extends string>(selector?: T | undefined): import("typed-query-selector/parser").ParseSelector<T, Element>[];
/**
 * Gets the opening tag text of the given node.
 * @param {Element|ShadowRoot} element
 * @param {Array<string>=} ignoreAttrs An optional array of attribute tags to not include in the HTML snippet.
 * @return {string}
 */
declare function getOuterHTMLSnippet(element: Element | ShadowRoot, ignoreAttrs?: Array<string> | undefined, snippetCharacterLimit?: number): string;
declare namespace getOuterHTMLSnippet {
    function toString(): string;
}
/**
 * Computes a memory/CPU performance benchmark index to determine rough device class.
 * @see https://github.com/GoogleChrome/lighthouse/issues/9085
 * @see https://docs.google.com/spreadsheets/d/1E0gZwKsxegudkjJl8Fki_sOwHKpqgXwt8aBAfuUaB8A/edit?usp=sharing
 *
 * Historically (until LH 6.3), this benchmark created a string of length 100,000 in a loop, and returned
 * the number of times per second the string can be created.
 *
 * Changes to v8 in 8.6.106 changed this number and also made Chrome more variable w.r.t GC interupts.
 * This benchmark now is a hybrid of a similar GC-heavy approach to the original benchmark and an array
 * copy benchmark.
 *
 * As of Chrome m86...
 *
 *  - 1000+ is a desktop-class device, Core i3 PC, iPhone X, etc
 *  - 800+ is a high-end Android phone, Galaxy S8, low-end Chromebook, etc
 *  - 125+ is a mid-tier Android phone, Moto G4, etc
 *  - <125 is a budget Android phone, Alcatel Ideal, Galaxy J2, etc
 * @return {number}
 */
declare function computeBenchmarkIndex(): number;
/**
 * @param {Element|ShadowRoot} element
 * @return {LH.Artifacts.NodeDetails}
 */
declare function getNodeDetails(element: Element | ShadowRoot): LH.Artifacts.NodeDetails;
declare namespace getNodeDetails {
    function toString(): string;
}
/**
 * Adapted from DevTools' SDK.DOMNode.prototype.path
 *   https://github.com/ChromeDevTools/devtools-frontend/blob/4fff931bb/front_end/sdk/DOMModel.js#L625-L647
 * Backend: https://source.chromium.org/search?q=f:node.cc%20symbol:PrintNodePathTo&sq=&ss=chromium%2Fchromium%2Fsrc
 *
 * TODO: DevTools nodePath handling doesn't support iframes, but probably could. https://crbug.com/1127635
 * @param {Node} node
 * @return {string}
 */
declare function getNodePath(node: Node): string;
/**
 * @param {Element} element
 * @return {string}
 *
 * Note: CSS Selectors having no standard mechanism to describe shadow DOM piercing. So we can't.
 *
 * If the node resides within shadow DOM, the selector *only* starts from the shadow root.
 * For example, consider this img within a <section> within a shadow root..
 *  - DOM: <html> <body> <div> #shadow-root <section> <img/>
 *  - nodePath: 0,HTML,1,BODY,1,DIV,a,#document-fragment,0,SECTION,0,IMG
 *  - nodeSelector: section > img
 */
declare function getNodeSelector(element: Element): string;
/**
 * Generate a human-readable label for the given element, based on end-user facing
 * strings like the innerText or alt attribute.
 * Returns label string or null if no useful label is found.
 * @param {Element} element
 * @return {string | null}
 */
declare function getNodeLabel(element: Element): string | null;
declare namespace getNodeLabel {
    function toString(): string;
}
/**
 * This function checks if an element or an ancestor of an element is `position:fixed`.
 * In addition we ensure that the element is capable of behaving as a `position:fixed`
 * element, checking that it lives within a scrollable ancestor.
 * @param {HTMLElement} element
 * @return {boolean}
 */
declare function isPositionFixed(element: HTMLElement): boolean;
/**
 * RequestIdleCallback shim that calculates the remaining deadline time in order to avoid a potential lighthouse
 * penalty for tests run with simulated throttling. Reduces the deadline time to (50 - safetyAllowance) / cpuSlowdownMultiplier to
 * ensure a long task is very unlikely if using the API correctly.
 * @param {number} cpuSlowdownMultiplier
 */
declare function wrapRequestIdleCallback(cpuSlowdownMultiplier: number): void;
/**
 * @param {Element} element
 * @return {LH.Artifacts.Rect}
 */
declare function getBoundingClientRect(element: Element): LH.Artifacts.Rect;
/**
 *
 * @param {string} string
 * @param {number} characterLimit
 * @return {string}
 */
declare function truncate(string: string, characterLimit: number): string;
declare namespace truncate {
    function toString(): string;
}
declare const esbuildFunctionWrapperString: string;
/**
 * @param {Function} fn
 * @return {string}
 */
declare function getRuntimeFunctionName(fn: Function): string;
export {};
//# sourceMappingURL=page-functions.d.ts.map