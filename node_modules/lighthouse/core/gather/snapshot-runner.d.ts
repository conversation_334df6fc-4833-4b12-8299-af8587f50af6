/**
 * @param {LH.Puppeteer.Page} page
 * @param {{config?: LH.Config, flags?: LH.Flags}} [options]
 * @return {Promise<LH.Gatherer.GatherResult>}
 */
export function snapshotGather(page: LH.Puppeteer.Page, options?: {
    config?: import("../../types/config.js").default | undefined;
    flags?: import("../index.js").Flags | undefined;
} | undefined): Promise<LH.Gatherer.GatherResult>;
//# sourceMappingURL=snapshot-runner.d.ts.map