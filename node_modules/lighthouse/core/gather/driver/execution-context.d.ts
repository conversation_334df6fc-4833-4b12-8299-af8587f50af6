export class ExecutionContext {
    /**
     * Prefix every script evaluation with a shadowing of common globals that tend to be ponyfilled
     * incorrectly by many sites. This allows functions to still refer to `Promise` instead of
     * Lighthouse-specific backups like `__nativePromise` (injected by `cacheNativesOnNewDocument` above).
     */
    static _cachedNativesPreamble: string;
    /**
     * Serializes an array of arguments for use in an `eval` string across the protocol.
     * @param {unknown[]} args
     * @return {string}
     */
    static serializeArguments(args: unknown[]): string;
    /**
     * Serializes an array of functions or strings.
     *
     * Also makes sure that an esbuild-bundled version of Lighthouse will
     * continue to create working code to be executed within the browser.
     * @param {Array<Function|string>=} deps
     * @return {string}
     */
    static serializeDeps(deps?: Array<Function | string> | undefined): string;
    /** @param {LH.Gatherer.ProtocolSession} session */
    constructor(session: LH.Gatherer.ProtocolSession);
    _session: LH.Gatherer.ProtocolSession;
    /** @type {number|undefined} */
    _executionContextId: number | undefined;
    /**
     * Marks how many execution context ids have been created, for purposes of having a unique
     * value (that doesn't expose the actual execution context id) to
     * use for __lighthouseExecutionContextUniqueIdentifier.
     * @type {number}
     */
    _executionContextIdentifiersCreated: number;
    /**
     * Returns the isolated context ID currently in use.
     */
    getContextId(): number | undefined;
    /**
     * Clears the remembered context ID. Use this method when we have knowledge that the runtime context
     * we were using has been destroyed by the browser and is no longer available.
     */
    clearContextId(): void;
    /**
     * Returns the cached isolated execution context ID or creates a new execution context for the main
     * frame. The cached execution context is cleared on every gotoURL invocation, so a new one will
     * always be created on the first call on a new page.
     * @return {Promise<number>}
     */
    _getOrCreateIsolatedContextId(): Promise<number>;
    /**
     * Evaluate an expression in the given execution context; an undefined contextId implies the main
     * page without isolation.
     * @param {string} expression
     * @param {number|undefined} contextId
     * @param {number} timeout
     * @return {Promise<*>}
     */
    _evaluateInContext(expression: string, contextId: number | undefined, timeout: number): Promise<any>;
    /**
     * Note: Prefer `evaluate` instead.
     * Evaluate an expression in the context of the current page. If useIsolation is true, the expression
     * will be evaluated in a content script that has access to the page's DOM but whose JavaScript state
     * is completely separate.
     * Returns a promise that resolves on the expression's value.
     * @param {string} expression
     * @param {{useIsolation?: boolean}=} options
     * @return {Promise<*>}
     */
    evaluateAsync(expression: string, options?: {
        useIsolation?: boolean;
    } | undefined): Promise<any>;
    /**
     * Evaluate a function in the context of the current page.
     * If `useIsolation` is true, the function will be evaluated in a content script that has
     * access to the page's DOM but whose JavaScript state is completely separate.
     * Returns a promise that resolves on a value of `mainFn`'s return type.
     * @template {unknown[]} T, R
     * @param {((...args: T) => R)} mainFn The main function to call.
     * @param {{args: T, useIsolation?: boolean, deps?: Array<Function|string>}} options `args` should
     *   match the args of `mainFn`, and can be any serializable value. `deps` are functions that must be
     *   defined for `mainFn` to work.
     * @return {Promise<Awaited<R>>}
     */
    evaluate<T extends unknown[], R>(mainFn: (...args: T) => R, options: {
        args: T;
        useIsolation?: boolean | undefined;
        deps?: (string | Function)[] | undefined;
    }): Promise<Awaited<R>>;
    /**
     * Evaluate a function on every new frame from now on.
     * @template {unknown[]} T
     * @param {((...args: T) => void)} mainFn The main function to call.
     * @param {{args: T, deps?: Array<Function|string>}} options `args` should
     *   match the args of `mainFn`, and can be any serializable value. `deps` are functions that must be
     *   defined for `mainFn` to work.
     * @return {Promise<void>}
     */
    evaluateOnNewDocument<T_1 extends unknown[]>(mainFn: (...args: T_1) => void, options: {
        args: T_1;
        deps?: (string | Function)[] | undefined;
    }): Promise<void>;
    /**
     * Cache native functions/objects inside window so we are sure polyfills do not overwrite the
     * native implementations when the page loads.
     * @return {Promise<void>}
     */
    cacheNativesOnNewDocument(): Promise<void>;
}
import * as LH from '../../../types/lh.js';
//# sourceMappingURL=execution-context.d.ts.map