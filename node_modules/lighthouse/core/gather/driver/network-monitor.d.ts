export type NetworkRecorderEventMap = import('../../lib/network-recorder.js').NetworkRecorderEventMap;
export type NetworkMonitorEvent_ = 'network-2-idle' | 'network-critical-idle' | 'networkidle' | 'networkbusy' | 'network-critical-busy' | 'network-2-busy';
export type NetworkMonitorEventMap = Record<NetworkMonitorEvent_, []> & NetworkRecorderEventMap;
export type NetworkMonitorEvent = keyof NetworkMonitorEventMap;
export type NetworkMonitorEmitter = LH.Protocol.StrictEventEmitterClass<NetworkMonitorEventMap>;
declare const NetworkMonitor_base: NetworkMonitorEmitter;
export class NetworkMonitor extends NetworkMonitor_base {
    /**
     * Finds all time periods where the number of inflight requests is less than or equal to the
     * number of allowed concurrent requests.
     * @param {Array<LH.Artifacts.NetworkRequest>} requests
     * @param {number} allowedConcurrentRequests
     * @param {number=} endTime
     * @return {Array<{start: number, end: number}>}
     */
    static findNetworkQuietPeriods(requests: Array<LH.Artifacts.NetworkRequest>, allowedConcurrentRequests: number, endTime?: number | undefined): Array<{
        start: number;
        end: number;
    }>;
    /** @param {LH.Gatherer.Driver['targetManager']} targetManager */
    constructor(targetManager: LH.Gatherer.Driver['targetManager']);
    /** @type {NetworkRecorder|undefined} */
    _networkRecorder: NetworkRecorder | undefined;
    /** @type {Array<LH.Crdp.Page.Frame>} */
    _frameNavigations: Array<LH.Crdp.Page.Frame>;
    /** @type {LH.Gatherer.Driver['targetManager']} */
    _targetManager: LH.Gatherer.Driver['targetManager'];
    /** @type {LH.Gatherer.ProtocolSession} */
    _session: LH.Gatherer.ProtocolSession;
    /** @param {LH.Crdp.Page.FrameNavigatedEvent} event */
    _onFrameNavigated: (event: LH.Crdp.Page.FrameNavigatedEvent) => number;
    /** @param {LH.Protocol.RawEventMessage} event */
    _onProtocolMessage: (event: LH.Protocol.RawEventMessage) => void;
    /**
     * @return {Promise<void>}
     */
    enable(): Promise<void>;
    /**
     * @return {Promise<void>}
     */
    disable(): Promise<void>;
    /** @return {Promise<{requestedUrl?: string, mainDocumentUrl?: string}>} */
    getNavigationUrls(): Promise<{
        requestedUrl?: string | undefined;
        mainDocumentUrl?: string | undefined;
    }>;
    /**
     * @return {Array<NetworkRequest>}
     */
    getInflightRequests(): Array<NetworkRequest>;
    /**
     * Returns whether the network is completely idle (i.e. there are 0 inflight network requests).
     */
    isIdle(): boolean;
    /**
     * Returns whether any important resources for the page are in progress.
     * Above-the-fold images and XHRs should be included.
     * Tracking pixels, low priority images, and cross frame requests should be excluded.
     * @return {boolean}
     */
    isCriticalIdle(): boolean;
    /**
     * Returns whether the network is semi-idle (i.e. there are 2 or fewer inflight network requests).
     */
    is2Idle(): boolean;
    /**
     * Returns whether the number of currently inflight requests is less than or
     * equal to the number of allowed concurrent requests.
     * @param {number} allowedRequests
     * @param {(request: NetworkRequest) => boolean} [requestFilter]
     * @return {boolean}
     */
    _isActiveIdlePeriod(allowedRequests: number, requestFilter?: ((request: NetworkRequest) => boolean) | undefined): boolean;
    /**
     * Emits the appropriate network status event.
     */
    _emitNetworkStatus(): void;
}
import * as LH from '../../../types/lh.js';
import { NetworkRecorder } from '../../lib/network-recorder.js';
import { NetworkRequest } from '../../lib/network-request.js';
export {};
//# sourceMappingURL=network-monitor.d.ts.map