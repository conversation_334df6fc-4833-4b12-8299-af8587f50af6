/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @param {LH.Gatherer.ProtocolSession} session
 * @return {Promise<LH.Crdp.ServiceWorker.WorkerVersionUpdatedEvent>}
 */
export function getServiceWorkerVersions(session: LH.Gatherer.ProtocolSession): Promise<LH.Crdp.ServiceWorker.WorkerVersionUpdatedEvent>;
/**
 * @param {LH.Gatherer.ProtocolSession} session
 * @return {Promise<LH.Crdp.ServiceWorker.WorkerRegistrationUpdatedEvent>}
 */
export function getServiceWorkerRegistrations(session: LH.Gatherer.ProtocolSession): Promise<LH.Crdp.ServiceWorker.WorkerRegistrationUpdatedEvent>;
//# sourceMappingURL=service-workers.d.ts.map