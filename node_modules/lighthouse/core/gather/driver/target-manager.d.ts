export type TargetWithSession = {
    target: LH.Crdp.Target.TargetInfo;
    cdpSession: LH.Puppeteer.CDPSession;
    session: LH.Gatherer.ProtocolSession;
    protocolListener: (event: unknown) => void;
};
export type ProtocolEventMap = {
    'protocolevent': [LH.Protocol.RawEventMessage];
};
export type ProtocolEventMessageEmitter = LH.Protocol.StrictEventEmitterClass<ProtocolEventMap>;
declare const TargetManager_base: ProtocolEventMessageEmitter;
/**
 * Tracks targets (the page itself, its iframes, their iframes, etc) as they
 * appear and allows listeners to the flattened protocol events from all targets.
 */
export class TargetManager extends TargetManager_base {
    /** @param {LH.Puppeteer.CDPSession} cdpSession */
    constructor(cdpSession: LH.Puppeteer.CDPSession);
    _enabled: boolean;
    _rootCdpSession: import("../../../types/puppeteer.js").default.CDPSession;
    _mainFrameId: string;
    /**
     * A map of target id to target/session information. Used to ensure unique
     * attached targets.
     * @type {Map<string, TargetWithSession>}
     */
    _targetIdToTargets: Map<string, TargetWithSession>;
    /** @type {Map<string, LH.Crdp.Runtime.ExecutionContextDescription>} */
    _executionContextIdToDescriptions: Map<string, LH.Crdp.Runtime.ExecutionContextDescription>;
    /**
     * @param {LH.Puppeteer.CDPSession} cdpSession
     */
    _onSessionAttached(cdpSession: LH.Puppeteer.CDPSession): Promise<void>;
    /**
     * @param {LH.Crdp.Page.FrameNavigatedEvent} frameNavigatedEvent
     */
    _onFrameNavigated(frameNavigatedEvent: LH.Crdp.Page.FrameNavigatedEvent): Promise<void>;
    /**
     * @param {LH.Crdp.Runtime.ExecutionContextCreatedEvent} event
     */
    _onExecutionContextCreated(event: LH.Crdp.Runtime.ExecutionContextCreatedEvent): void;
    /**
     * @param {LH.Crdp.Runtime.ExecutionContextDestroyedEvent} event
     */
    _onExecutionContextDestroyed(event: LH.Crdp.Runtime.ExecutionContextDestroyedEvent): void;
    _onExecutionContextsCleared(): void;
    /**
     * @param {string} sessionId
     * @return {LH.Gatherer.ProtocolSession}
     */
    _findSession(sessionId: string): LH.Gatherer.ProtocolSession;
    /**
     * @param {string} targetType
     * @return {targetType is LH.Protocol.TargetType}
     */
    _isAcceptedTargetType(targetType: string): targetType is import("../../../types/protocol.js").default.TargetType;
    /**
     * Returns the root session.
     * @return {LH.Gatherer.ProtocolSession}
     */
    rootSession(): LH.Gatherer.ProtocolSession;
    mainFrameExecutionContexts(): import("devtools-protocol").Protocol.Runtime.ExecutionContextDescription[];
    /**
     * Returns a listener for all protocol events from session, and augments the
     * event with the sessionId.
     * @param {LH.Protocol.TargetType} targetType
     * @param {string} sessionId
     */
    _getProtocolEventListener(targetType: LH.Protocol.TargetType, sessionId: string): <EventName extends keyof import("puppeteer-core").ProtocolMapping.Events>(method: EventName, params: import("../../../types/protocol.js").default.RawEventMessageRecord[EventName]["params"]) => void;
    /**
     * @return {Promise<void>}
     */
    enable(): Promise<void>;
    /**
     * @return {Promise<void>}
     */
    disable(): Promise<void>;
}
import { ProtocolSession } from '../session.js';
export {};
//# sourceMappingURL=target-manager.d.ts.map