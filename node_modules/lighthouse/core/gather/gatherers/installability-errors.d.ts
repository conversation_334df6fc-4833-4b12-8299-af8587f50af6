export default InstallabilityErrors;
declare class InstallabilityErrors extends BaseGatherer {
    /**
     * Creates an Artifacts.InstallabilityErrors, tranforming data from the protocol
     * for old versions of Chrome.
     * @param {LH.Gatherer.ProtocolSession} session
     * @return {Promise<LH.Artifacts['InstallabilityErrors']>}
     */
    static getInstallabilityErrors(session: LH.Gatherer.ProtocolSession): Promise<LH.Artifacts['InstallabilityErrors']>;
    /**
     * @param {LH.Gatherer.Context} context
     * @return {Promise<LH.Artifacts['InstallabilityErrors']>}
     */
    getArtifact(context: LH.Gatherer.Context): Promise<LH.Artifacts['InstallabilityErrors']>;
}
import BaseGatherer from '../base-gatherer.js';
//# sourceMappingURL=installability-errors.d.ts.map