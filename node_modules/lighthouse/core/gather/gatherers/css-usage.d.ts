export default CSSUsage;
declare class CSSUsage extends BaseGatherer {
    /** @type {LH.Gatherer.ProtocolSession|undefined} */
    _session: LH.Gatherer.ProtocolSession | undefined;
    /** @type {Map<string, Promise<LH.Artifacts.CSSStyleSheetInfo|Error>>} */
    _sheetPromises: Map<string, Promise<LH.Artifacts.CSSStyleSheetInfo | Error>>;
    /**
     * Initialize as undefined so we can assert results are fetched.
     * @type {LH.Crdp.CSS.RuleUsage[]|undefined}
     */
    _ruleUsage: LH.Crdp.CSS.RuleUsage[] | undefined;
    /**
     * @param {LH.Crdp.CSS.StyleSheetAddedEvent} event
     */
    _onStylesheetAdded(event: LH.Crdp.CSS.StyleSheetAddedEvent): void;
    /**
     * @param {LH.Gatherer.Context} context
     */
    startInstrumentation(context: LH.Gatherer.Context): Promise<void>;
    /**
     * @param {LH.Gatherer.Context} context
     */
    stopInstrumentation(context: LH.Gatherer.Context): Promise<void>;
    /**
     * @param {LH.Gatherer.Context} context
     * @return {Promise<LH.Artifacts['CSSUsage']>}
     */
    getArtifact(context: LH.Gatherer.Context): Promise<LH.Artifacts['CSSUsage']>;
}
import BaseGatherer from '../base-gatherer.js';
//# sourceMappingURL=css-usage.d.ts.map