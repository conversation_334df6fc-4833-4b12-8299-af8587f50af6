export default WebAppManifest;
declare class WebAppManifest extends BaseGatherer {
    /**
     * @param {LH.Gatherer.ProtocolSession} session
     * @return {Promise<{url: string, data: string}|null>}
     */
    static fetchAppManifest(session: LH.Gatherer.ProtocolSession): Promise<{
        url: string;
        data: string;
    } | null>;
    /**
     * Uses the debugger protocol to fetch the manifest from within the context of
     * the target page, reusing any credentials, emulation, etc, already established
     * there.
     *
     * Returns the parsed manifest or null if the page had no manifest. If the manifest
     * was unparseable as JSON, manifest.value will be undefined and manifest.warning
     * will have the reason. See manifest-parser.js for more information.
     *
     * @param {LH.Gatherer.ProtocolSession} session
     * @param {string} pageUrl
     * @return {Promise<LH.Artifacts.Manifest|null>}
     */
    static getWebAppManifest(session: LH.Gatherer.ProtocolSession, pageUrl: string): Promise<LH.Artifacts.Manifest | null>;
    /**
     * @param {LH.Gatherer.Context} context
     * @return {Promise<LH.Artifacts['WebAppManifest']>}
     */
    getArtifact(context: LH.Gatherer.Context): Promise<LH.Artifacts['WebAppManifest']>;
}
import BaseGatherer from '../base-gatherer.js';
//# sourceMappingURL=web-app-manifest.d.ts.map