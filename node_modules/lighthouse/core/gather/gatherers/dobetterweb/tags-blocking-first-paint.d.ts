export default TagsBlockingFirstPaint;
export type MediaChange = {
    href: string;
    media: string;
    msSinceHTMLEnd: number;
    matches: boolean;
};
export type LinkTag = {
    tagName: 'LINK';
    url: string;
    href: string;
    rel: string;
    media: string;
    disabled: boolean;
    mediaChanges: Array<MediaChange>;
};
export type ScriptTag = {
    tagName: 'SCRIPT';
    url: string;
    src: string;
};
declare class TagsBlockingFirstPaint extends BaseGatherer {
    /**
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Map<string, LH.Artifacts.NetworkRequest>}
     */
    static _filteredAndIndexedByUrl(networkRecords: Array<LH.Artifacts.NetworkRequest>): Map<string, LH.Artifacts.NetworkRequest>;
    /**
     * @param {LH.Gatherer.Driver} driver
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @return {Promise<Array<LH.Artifacts.TagBlockingFirstPaint>>}
     */
    static findBlockingTags(driver: LH.Gatherer.Driver, networkRecords: Array<LH.Artifacts.NetworkRequest>): Promise<Array<LH.Artifacts.TagBlockingFirstPaint>>;
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.Context} context
     */
    startSensitiveInstrumentation(context: LH.Gatherer.Context): Promise<void>;
    /**
     * @param {LH.Gatherer.Context<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['TagsBlockingFirstPaint']>}
     */
    getArtifact(context: LH.Gatherer.Context<'DevtoolsLog'>): Promise<LH.Artifacts['TagsBlockingFirstPaint']>;
}
import BaseGatherer from '../../base-gatherer.js';
//# sourceMappingURL=tags-blocking-first-paint.d.ts.map