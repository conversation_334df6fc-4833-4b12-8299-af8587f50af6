export default TapTargets;
declare class TapTargets extends BaseGatherer {
    /**
     * @param {LH.Gatherer.ProtocolSession} session
     * @param {string} className
     * @return {Promise<string>}
     */
    addStyleRule(session: LH.Gatherer.ProtocolSession, className: string): Promise<string>;
    /**
     * @param {LH.Gatherer.ProtocolSession} session
     * @param {string} styleSheetId
     */
    removeStyleRule(session: LH.Gatherer.ProtocolSession, styleSheetId: string): Promise<void>;
    /**
     * @param {LH.Gatherer.Context} passContext
     * @return {Promise<LH.Artifacts.TapTarget[]>} All visible tap targets with their positions and sizes
     */
    getArtifact(passContext: LH.Gatherer.Context): Promise<LH.Artifacts.TapTarget[]>;
}
import BaseGatherer from '../../base-gatherer.js';
//# sourceMappingURL=tap-targets.d.ts.map