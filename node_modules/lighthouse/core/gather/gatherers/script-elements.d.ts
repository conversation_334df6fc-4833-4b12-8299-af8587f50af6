export default ScriptElements;
/**
 * @fileoverview Gets JavaScript file contents.
 */
declare class ScriptElements extends BaseGatherer {
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.Context} context
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @return {Promise<LH.Artifacts['ScriptElements']>}
     */
    _getArtifact(context: LH.Gatherer.Context, networkRecords: LH.Artifacts.NetworkRequest[]): Promise<LH.Artifacts['ScriptElements']>;
    /**
     * @param {LH.Gatherer.Context<'DevtoolsLog'>} context
     */
    getArtifact(context: LH.Gatherer.Context<'DevtoolsLog'>): Promise<import("../../index.js").Artifacts.ScriptElement[]>;
}
import BaseGatherer from '../base-gatherer.js';
import { NetworkRequest } from '../../lib/network-request.js';
//# sourceMappingURL=script-elements.d.ts.map