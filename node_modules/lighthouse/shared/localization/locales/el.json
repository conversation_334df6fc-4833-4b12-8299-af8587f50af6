{"core/audits/accessibility/accesskeys.js | description": {"message": "Τα πλήκτρα πρόσβασης επιτρέπουν στους χρήστες να εστιάζουν γρήγορα σε ένα τμήμα της σελίδας. Για σωστή πλοήγηση, κάθε πλήκτρο πρόσβασης πρέπει να είναι μοναδικό. [Μάθετε περισσότερα σχετικά με τα κλειδιά πρόσβασης](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Οι τιμές `[accesskey]` δεν είναι μοναδικές"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` τιμές είναι μοναδικές"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Κάθε στοιχε<PERSON><PERSON> ARIA `role` υποστηρίζει ένα συγκεκριμένο υποσύνολο χαρακτηριστικών `aria-*`. Η λανθασμένη αντιστοίχισή τους καθιστά μη έγκυρα τα χαρακτηριστικά `aria-*`. [Μάθετε πώς μπορείτε να αντιστοιχίσετε τα χαρακτηριστικά ARIA με τους ρόλους τους](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Τα χαρακτηριστικά `[aria-*]` δεν αντιστοιχούν στους ρόλους τους"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Τα χαρακτηριστικά `[aria-*]` αντιστοιχούν στους ρόλους τους"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "Οι `role` ARIA επιτρέπουν στις τεχνολογίες υποβοήθησης να γνωρίζουν τον ρόλο του κάθε στοιχείου στην ιστοσελίδα. Εάν οι τιμές του `role` δεν έχουν γραφτεί σωστά, δεν υπάρχουν οι υπάρχουσες τιμές `role`ARIA ή οι αφηρημένοι ρόλοι, τότε ο σκοπός του στοιχείου δεν θα κοινοποιείται στους χρήστες τεχνολογιών υποβοήθησης. [Μάθετε περισσότερα σχετικά με τους ρόλους ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Οι τιμές που έχουν εκχωρηθεί στο στοιχείο `role=\"\"` είναι έγκυροι ρόλοι ARIA."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Οι τιμές που έχουν εκχωρηθεί στο στοιχείο `role=\"\"` είναι έγκυροι ρόλοι ARIA."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Όταν ένα στοιχείο δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε πώς μπορείτε να κάνετε πιο προσιτά τα στοιχεία εντολών](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Τα στοιχεία `button`, `link` και `menuitem` δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Τα στοιχεία `button`, `link` και `menuitem` έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Τα στοιχεία παραθύρου διαλόγου ARIA χωρίς προσβάσιμα ονόματα ενδέχεται να εμποδίζουν τους χρήστες του αναγνώστη οθόνης να διακρίνουν τον σκοπό αυτών των στοιχείων. [Μάθετε πώς μπορείτε να κάνετε τα στοιχεία διαλόγου ARIA πιο προσβάσιμα](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Τα στοιχεία με `role=\"dialog\"` ή `role=\"alertdialog\"` δεν έχουν προσβάσιμα ονόματα."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Τα στοιχεία με `role=\"dialog\"` ή `role=\"alertdialog\"` έχουν προσβάσιμα ονόματα."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Οι τεχνολογίες υποβοήθησης, ό<PERSON><PERSON><PERSON> οι αναγνώστες οθόνης, λειτουργούν με μη συνεπή τρόπο όταν το `aria-hidden=\"true\"` έχει οριστεί στο έγγραφο `<body>`. [Μάθετε πώς το `aria-hidden` επηρεάζει το σώμα του εγγράφου](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Το `[aria-hidden=\"true\"]` υπάρχει στο έγγραφο `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Το `[aria-hidden=\"true\"]` δεν υπάρχει στο έγγραφο `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Τα θυγατρικά στοιχεία με δυνατότητα εστίασης εντός ενός στοιχείου `[aria-hidden=\"true\"]` αποτρέπουν τη διάθεση αυτών των στοιχείων αλληλεπίδρασης σε χρήστες τεχνολογιών υποβοήθησης, όπως είναι οι αναγνώστες οθόνης. [Μάθετε πώς το `aria-hidden` επηρεάζει στοιχεία με δυνατότητα εστίασης](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Τα στοιχεία `[aria-hidden=\"true\"]` περιέχουν εξαρτημένα στοιχεία με δυνατότητα εστίασης"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Τα στοιχεία `[aria-hidden=\"true\"]` δεν περιέχουν εξαρτημένα στοιχεία με δυνατότητα εστίασης"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Όταν ένα πεδίο εισαγωγής δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε περισσότερα σχετικά με τις ετικέτες πεδίων εισαγωγής](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Τα πεδία εισαγωγής ARIA δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Τα πεδία εισαγωγής ARIA διαθέτουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Όταν ένα στοιχείο μετρητή δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε πώς μπορείτε να ονομάσετε στοιχεία `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Τα στοιχεία ARIA `meter` δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Τα στοιχεία ARIA `meter` έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Όταν ένα στοιχείο `progressbar` δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε πώς μπορείτε να προσθέσετε ετικέτα σε στοιχεία `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Τα στοιχεία ARIA `progressbar` δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Τα στοιχεία ARIA `progressbar` έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Ορισμένοι ρόλοι ARIA έχουν απαιτούμενα χαρακτηριστικά που περιγράφουν την κατάσταση του στοιχείου στους αναγνώστες οθόνης. [Μάθετε περισσότερα σχετικά με τους ρόλους και τα απαιτούμενα χαρακτηριστικά](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Τα στοιχεία `[role]` δεν έχουν όλα τα απαιτούμενα χαρακτηριστικά `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Τα στοιχεία `[role]` έχουν όλα τα απαιτούμενα χαρακτηριστικά `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Ορισμένοι γονικοί ρόλοι ARIA πρέπει να περιέχουν συγκεκριμένους θυγατρικούς ρόλους, για να μπορούν να εκτελέσουν τις προβλεπόμενες λειτουργίες προσβασιμότητας. [Μάθετε περισσότερα σχετικά με τους ρόλους και τα απαιτούμενα θυγατρικά στοιχεία](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Λείπουν από τα στοιχεία με ARIA `[role]`, τα οποία απαιτούν από τα θυγατρικά στοιχεία να περιέχουν ένα συγκεκριμένο `[role]`, ορισμένα ή όλα τα απαιτούμενα θυγατρικά στοιχεία."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Τα στοιχεία με ARIA `[role]`, τα οποία απαιτούν από τα θυγατρικά στοιχεία να περιέχουν ένα συγκεκριμένο `[role]`, έχουν όλα τα απαιτούμενα θυγατρικά στοιχεία."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Ορισμένοι θυγατρικοί ρόλοι ARIA πρέπει να περιέχονται σε συγκεκριμένους γονικούς ρόλους, για να μπορούν να εκτελέσουν σωστά τις προβλεπόμενες λειτουργίες προσβασιμότητας. [Μάθετε περισσότερα σχετικά με τους ρόλους ARIA και το απαιτούμενο γονικό στοιχείο](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Τα στοιχεία `[role]` δεν περιλαμβάνονται στο απαιτούμενο γονικό στοιχείο τους"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Τα στοιχεία `[role]` περιλαμβάνοντα<PERSON> στο απαιτούμενο γονικό στοιχείο τους"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Οι ρόλοι ARIA πρέπει να έχουν έγκυρες τιμές, για να μπορούν να εκτελέσουν τις προβλεπόμενες λειτουργίες προσβασιμότητας. [Μάθετε περισσότερα σχετικά με τους έγκυρους ρόλους ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Οι τιμές `[role]` δεν είναι έγκυρες"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Οι τιμές `[role]` είναι έγκυρες"}, "core/audits/accessibility/aria-text.js | description": {"message": "Η προσθήκη του `role=text` γύρω από έναν κόμβο κειμένου που διαχωρίζεται κατά σήμανση επιτρέπει στο VoiceOver να το μεταχειρίζεται ως φράση, αλλά τα θυγατρικά στοιχεία με δυνατότητα εστίασης δεν θα ανακοινώνονται. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Τα στοιχεία με το χαρακτηριστικ<PERSON> `role=text` έχουν θυγατρικά στοιχεία με δυνατότητα εστίασης."}, "core/audits/accessibility/aria-text.js | title": {"message": "Τα στοιχεία με το χαρακτηριστικ<PERSON> `role=text` δεν έχουν θυγατρικά στοιχεία με δυνατότητα εστίασης."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Όταν ένα πεδίο εναλλαγής δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε περισσότερα σχετικά με τα πεδία εναλλαγής](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Τα πεδία εναλλαγής ARIA δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Τα πεδία εναλλαγής ARIA διαθέτουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Όταν ένα στοιχείο επεξήγησης εργαλείου δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε πώς μπορείτε να ονομάσετε στοιχεία `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Τα στοιχεία ARIA `tooltip` δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Τα στοιχεία ARIA `tooltip` έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Όταν ένα στοιχείο `treeitem` δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το περιγράφουν με ένα γενικό όνομα, με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από χρήστες που βασίζονται σε αναγνώστες οθόνης. [Μάθετε περισσότερα σχετικά με την προσθήκη ετικετών σε στοιχεία `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Τα στοιχεία ARIA `treeitem` δεν έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Τα στοιχεία ARIA `treeitem` έχουν προσβάσιμα ονόματα"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Οι τεχνολογίες υποβοήθησης, ό<PERSON>ως οι αναγνώστες οθόνης, δεν μπορούν να ερμηνεύσουν τα χαρακτηριστικ<PERSON> ARIA με μη έγκυρες τιμές. [Μάθετε περισσότερα σχετικά με τις έγκυρες τιμές για τα χαρακτηριστικά ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Τα χαρακτηριστικά `[aria-*]` δεν έχουν έγκυρες τιμές"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Τα χαρακτηριστικά `[aria-*]` έχουν έγκυρες τιμές"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Οι τεχνολογίες υποβοήθησης, όπως οι αναγνώστες οθόνης, δεν μπορούν να ερμηνεύσουν τα χαρακτηριστικ<PERSON> ARIA με μη έγκυρα ονόματα. [Μάθετε περισσότερα σχετικά με τα έγκυρα χαρακτηριστικά ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Τα χαρακτηριστικά `[aria-*]` δεν είναι έγκυρα ή έχουν ορθογραφικά λάθη"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Τα χαρακτηριστικά `[aria-*]` είναι έγκυρα και δεν έχουν ορθογραφικά λάθη"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Στοιχεία που απέτυχαν"}, "core/audits/accessibility/button-name.js | description": {"message": "Όταν ένα κουμπί δεν έχει προσβάσιμο όνομα, οι αναγνώστες οθόνης το εκφωνούν ως \"κουμπί\", με αποτέλεσμα να μην μπορεί να χρησιμοποιηθεί από τους χρήστες που βασίζονται στους αναγνώστες οθόνης. [Μάθετε πώς μπορείτε να κάνετε τα κουμπιά πιο προσβάσιμα](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Τα κουμπιά δεν έχουν προσβάσιμο όνομα"}, "core/audits/accessibility/button-name.js | title": {"message": "Τα κουμπιά έχουν προσβάσιμο όνομα"}, "core/audits/accessibility/bypass.js | description": {"message": "Η προσθήκη τρόπων για την παράκαμψη του επαναλαμβανόμενου περιεχομένου επιτρέπει στους χρήστες του πληκτρολογίου να πλοηγούνται πιο αποτελεσματικά στη σελίδα. [Μάθετε περισσότερα σχετικά με την παράκαμψη αποκλεισμών](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Η σελίδα δεν περιέχει κεφαλίδα, σύνδεσμο παράβλεψης ή περιοχή ορόσημου"}, "core/audits/accessibility/bypass.js | title": {"message": "Η σελίδα περιέχει κεφαλίδα, σύνδεσμο παράβλεψης ή περιοχή ορόσημου"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Ένα κείμενο χαμηλής αντίθεσης είναι δύσκολο ή αδύνατο να αναγνωστεί. [Μάθετε πώς να μπορείτε να χρησιμοποιήσετε επαρκή χρωματική αντίθεση](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Τα χρώματα παρασκηνίου και προσκηνίου δεν έχουν επαρκή αναλογία αντίθεσης."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Τα χρώματα παρασκηνίου και προσκηνίου έχουν επαρκή αναλογία αντίθεσης"}, "core/audits/accessibility/definition-list.js | description": {"message": "Όταν οι λίστες ορισμών δεν επισημαίνονται σωστά, οι αναγνώστες οθόνης μπορεί να παράγουν συγκεχυμένα ή ανακριβή αποτελέσματα. [Μάθετε πώς μπορείτε να διαμορφώσετε σωστά τις λίστες ορισμών](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Τα στοιχεία `<dl>` δεν περιέχουν μόνο σωστά ταξινομημένες ομάδες `<dt>` και `<dd>` ή στοιχεία `<script>`, `<template>` ή `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Τα στοιχεία `<dl>` περιέχουν μόνο σωστά ταξινομημένες ομάδες `<dt>` και `<dd>` ή στοιχεία `<script>`, `<template>` ή `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Τα στοιχεία λίστας ορισμών (`<dt>` και `<dd>`) πρέπει να περιτυλίγονται σε ένα γονικό στοιχείο `<dl>`, ώστε οι αναγνώστες οθόνης να μπορούν να τα εκφωνήσουν σωστά. [Μάθετε πώς μπορείτε να διαμορφώσετε σωστά τις λίστες ορισμών](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Τα στοιχεία της λίστας ορισμών δεν περιτυλίγονται σε στοιχεία `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Τα στοιχεία της λίστας ορισμών περιτυλίγονται σε στοιχεία `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Για τους χρήστες ενός αναγνώστη οθόνης, ο τίτλος λειτουργεί ως επισκόπηση της σελίδας. Για τους χρήστες μιας μηχανής αναζήτησης, ο τίτλος τούς επιτρέπει να αποφασίσουν εάν μια σελίδα είναι σχετική με την αναζήτησή τους. [Μάθετε περισσότερα σχετικά με τους τίτλους εγγράφων](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Το έγγραφο δεν έχει ένα στοιχείο `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Το έγγρα<PERSON><PERSON> έχει ένα στοιχείο `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Όλα τα στοιχεία με δυνατότητα εστίασης πρέπει να διαθέτουν ένα μοναδικό `id`, για να διασφαλιστεί ότι είναι ορατά σε τεχνολογίες υποβοήθησης. [Μάθετε πώς μπορείτε να διορθώσετε διπλότυπα `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]` χαρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON> σε ενεργ<PERSON> στοιχεία με δυνατότητα εστίασης, δεν είναι μοναδικά"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]` χαρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON> σε ενερ<PERSON><PERSON> στοιχεία με δυνατότητα εστίασης, είν<PERSON><PERSON> μοναδικά"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Η τιμή ενός αναγνωριστικού ARIA πρέπει να είναι μοναδική, προκειμένου να μην παραβλέπονται άλλες παρουσίες κατά τη χρήση τεχνολογιών υποβοήθησης. [Μάθετε πώς μπορείτε να διορθώσετε διπλότυπα αναγνωριστικά ARIA](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Τα αναγνω<PERSON>ιστικ<PERSON> ARIA δεν είναι μοναδικά"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Τα αναγνωριστικ<PERSON> ARIA είναι μοναδικά"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Μια επικεφαλίδα χωρίς περιεχόμενο ή με μη προσβάσιμο κείμενο εμποδίζει την πρόσβαση των χρηστών του αναγνώστη οθόνης σε πληροφορίες σχετικά με τη δομή της σελίδας. [Μάθετε περισσότερα σχετικά με τις κεφαλίδες](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Τα στοιχεία κεφαλίδας δεν διαθέτουν περιεχόμενο."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Όλα τα στοιχεία κεφαλίδας περιλαμβάνουν περιεχόμενο."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Τα πεδία φόρμας με πολλές ετικέτες ενδέχεται να ανακοινώνονται με τρόπο που δημιουργεί σύγχυση στους χρήστες τεχνολογιών υποβοήθησης, όπως με τους αναγνώστες οθόνης που χρησιμοποιούν είτε την πρώτη, είτε την τελευταία, είτε όλες τις ετικέτες. [Μάθετε πώς μπορείτε να χρησιμοποιήσετε ετικέτες φόρμας](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Τα πεδία φόρμας έχουν πολλές ετικέτες"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Δεν υπάρχουν πεδία φόρμας με πολλές ετικέτες"}, "core/audits/accessibility/frame-title.js | description": {"message": "Οι χρήστες ενός αναγνώστη οθόνης βασίζονται στους τίτλους των πλαισίων για την περιγραφή του περιεχομένου των πλαισίων. [Μάθετε περισσότερα σχετικά με τους τίτλους πλαισίων](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Τα στοιχεία `<frame>` ή `<iframe>` δεν έχουν τίτλο"}, "core/audits/accessibility/frame-title.js | title": {"message": "Τα στοιχεία `<frame>` ή `<iframe>` έχουν έναν τίτλο"}, "core/audits/accessibility/heading-order.js | description": {"message": "Οι σωστά ταξινομημένες κεφαλίδες που δεν παραβλέπουν επίπεδα αποδίδουν τη σημασιολογική δομή της σελίδας, διευκολύνοντας την πλοήγηση και την κατανόηση κατά τη χρήση τεχνολογιών υποβοήθησης. [Μάθετε περισσότερα σχετικά με την σειρά επικεφαλίδων](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Τα στοιχεία κεφαλίδας δεν εμφανίζονται με διαδοχικά φθίνουσα σειρά"}, "core/audits/accessibility/heading-order.js | title": {"message": "Τα στοιχεία κεφαλίδας εμφανίζονται με διαδοχικά φθίνουσα σειρά"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Εάν μια σελίδα δεν προσδιορίζει κάποιο χαρακτηριστικό `lang`, ο αναγνώστης οθόνης υποθέτει ότι η σελίδα εμφανίζεται στην προεπιλεγμένη γλώσσα που επέλεξε ο χρήστης κατά τη ρύθμιση του αναγνώστη οθόνης. Εάν η σελίδα δεν εμφανίζεται στην προεπιλεγμένη γλώσσα, τότε ο αναγνώστης οθόνης μπορεί να μην εκφωνήσει σωστά το κείμενο της σελίδας. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Το στοιχείο `<html>` δεν έχει ένα χαρακτηριστικό `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Το στοιχείο `<html>` έχει ένα χαρακτηριστικό `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Ο ορισμός μιας έγκυρης [γλώσσας BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) βοηθά τους αναγνώστες οθόνης να εκφωνούν σωστά το κείμενο. [Μάθετε πώς μπορείτε να χρησιμοποιήσετε το χαρακτηριστικό `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Το στοιχείο `<html>` δεν έχει μια έγκυρη τιμή για το χαρακτηριστικό `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Το στοιχείο `<html>` έχει μια έγκυρη τιμή για το χαρακτηριστικό `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Εάν η ιστοσελίδα δεν καθορίζει μια σταθερή γλώσσα, τότε ο αναγνώστης οθόνης μπορεί να μην εκφωνήσει σωστά το κείμενο της σελίδας. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Το στοιχείο `<html>` δεν έχει ένα χαρακτηριστικό `[xml:lang]` με την ίδια βασική γλώσσα με το χαρακτηριστικό `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Το στοιχείο `<html>` έχει ένα χαρακτηριστικό `[xml:lang]` με την ίδια βασική γλώσσα με το χαρακτηριστικό `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Οι σύνδεσμοι με τον ίδιο προορισμό θα πρέπει να έχουν την ίδια περιγραφή, ώστε να βοηθούν τους χρήστες να κατανοήσουν τον σκοπό του συνδέσμου και να αποφασίσουν αν θα τον ακολουθήσουν. [Μάθετε περισσότερα σχετικά με τους πανομοιότυπους συνδέσμους](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Οι πανομοιότυποι σύνδεσμοι δεν έχουν τον ίδιο σκοπό."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Οι πανομοιότυποι σύνδεσμοι έχουν τον ίδιο σκοπό."}, "core/audits/accessibility/image-alt.js | description": {"message": "Τα πληροφοριακά στοιχεία θα πρέπει να στοχεύουν σε σύντομο και περιγραφικό εναλλακτικό κείμενο. Τα διακοσμητικά στοιχεία μπορούν να παραβλεφθούν με ένα κενό χαρακτηριστικό alt. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Τα στοιχεία εικόνας δεν έχουν χαρακτηριστικά `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Τα στοιχεία εικόνας έχουν χαρακτηριστικά `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Τα πληροφορι<PERSON><PERSON><PERSON> στοιχεία θα πρέπει να στοχεύουν σε σύντομο και περιγραφικό εναλλακτικό κείμενο. Εναλλακτικό κείμενο που είναι ακριβώς το ίδιο με το κείμενο που βρίσκεται δίπλα στον σύνδεσμο ή την εικόνα ενδέχεται να προκαλέσει σύγχυση στους χρήστες του αναγνώστη οθόνης, επειδή το κείμενο θα διαβαστεί δύο φορές. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Τα στοιχεία εικόνας έχουν χαρακτηριστικά `[alt]` που είναι περιττό κείμενο."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Τα στοιχεία εικόνας δεν έχουν χαρακτηριστικά `[alt]` που είναι περιττό κείμενο."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Η προσθήκη ευανάγνωστου και προσβάσιμου κειμένου στα κουμπιά εισόδου μπορεί να βοηθήσει τους χρήστες του αναγνώστη οθόνης να κατανοήσουν τον σκοπό των κουμπιών. [Μάθετε περισσότερα σχετικά με τα κουμπιά εισόδου](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Τα κουμπιά εισόδου δεν έχουν ευανάγνωστο κείμενο."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Τα κουμπιά εισόδου έχουν ευανάγνωστο κείμενο."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Όταν μια εικόνα χρησιμοποιείται ως κουμπί `<input>`, η παροχή εναλλακτικού κειμένου μπορεί να βοηθήσει τους χρήστες του αναγνώστη οθόνης να κατανοήσουν τον σκοπό του κουμπιού. [Μάθετε σχετικά με το εναλλακτικό κείμενο εικόνας εισόδου](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Τα στοιχεία `<input type=\"image\">` δεν έχουν κείμενο `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Τα στοιχεία `<input type=\"image\">` έχουν κείμενο `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Οι ορατές ετικέτες κειμένου που δεν αντιστοιχούν στο προσβάσιμο όνομα μπορεί να προκαλέσουν σύγχυση στους χρήστες του αναγνώστη οθόνης. [Μάθετε περισσότερα σχετικά με τα προσβάσιμα ονόματα](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Τα στοιχεία με ορατές ετικέτες κειμένου δεν έχουν αντίστοιχα προσβάσιμα ονόματα."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Τα στοιχεία με ορατές ετικέτες κειμένου έχουν προσβάσιμα ονόματα που ταιριάζουν."}, "core/audits/accessibility/label.js | description": {"message": "Οι ετικέτες διασφαλίζουν ότι οι τεχνολογίες υποβοήθησης, όπως οι αναγνώστες οθόνης, εκφων<PERSON>ύν σωστά τα στοιχεία ελέγχου φόρμας. [Μάθετε περισσότερα σχετικά με τις ετικέτες στοιχείων φόρμας](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Τα στοιχεία φρόμας δεν έχουν συσχετισμένες ετικέτες"}, "core/audits/accessibility/label.js | title": {"message": "Τα στοιχεία φόρμας έχουν συσχετισμένες ετικέτες"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Ένα κύριο ορόσημο βοηθά τους χρήστες του αναγνώστη οθόνης να πλοηγηθούν σε μια ιστοσελίδα. [Μάθετε περισσότερα σχετικά με τα ορόσημα](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Το έγγραφο δεν διαθέτει κύριο ορόσημο."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Το έγγρα<PERSON><PERSON> έχει ένα κύριο ορόσημο."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Ένα κείμενο χαμηλής αντίθεσης είναι δύσκολο ή αδύνατο να αναγνωστεί. Το κείμενο συνδέσμου που είναι ευανάγνωστο βελτιώνει την εμπειρία για τους χρήστες με χαμηλή όραση. [Μάθετε πώς μπορείτε να κάνετε τους συνδέσμους διακριτούς](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Οι σύνδεσμοι βασίζονται στο χρώμα για να είναι διακριτοί."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Οι σύνδεσμοι διακρίνονται χωρίς να βασίζονται στο χρώμα."}, "core/audits/accessibility/link-name.js | description": {"message": "Το κείμενο συνδέσμων (και το εναλλακτικ<PERSON> κείμενο για εικόνες όταν χρησιμοποιούνται ως σύνδεσμοι) που είναι διακριτ<PERSON>, μο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και έχει δυνατότητα εστίασης βελτιώνει την εμπειρία πλοήγησης για τους χρήστες των αναγνωστών οθόνης. [Μάθετε πώς μπορείτε να κάνετε προσβάσιμους τους συνδέσμους](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Οι σύνδεσμοι δεν έχουν διακριτό όνομα"}, "core/audits/accessibility/link-name.js | title": {"message": "Οι σύνδεσμοι έχουν διακριτό όνομα"}, "core/audits/accessibility/list.js | description": {"message": "Οι αναγνώστες οθόνης έχουν έναν συγκεκριμένο τρόπο εκφώνησης των λιστών. Η χρήση κατάλληλης δομής λίστας συμβάλλει στην αποτελεσματικότερη λειτουργία των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με την κατάλληλη δομή λίστας](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Οι λίστες δεν περιέχουν μόνο στοιχεία `<li>` και στοιχεία υποστήριξης σεναρίων (`<script>` και `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Οι λίστες περιέχουν μόνο στοιχεία `<li>` και στοιχεία υποστήριξης σεναρίων (`<script>` και `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Για τη σωστή εκφώνηση των στοιχείων λίστας (`<li>`) από τους αναγνώστες οθόνης, τα στοιχεία πρέπει να περιέχονται σε ένα γονικό `<ul>`, `<ol>` ή `<menu>`. [Μάθετε περισσότερα σχετικά με την κατάλληλη δομή λίστας](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Τα στοιχεία λίστας (`<li>`) δεν περιλαμβάνονται στα γονικά στοιχεία `<ul>`, `<ol>` ή `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Τα στοιχεία λίστας (`<li>`) περιλαμβάνονται στα γονικά στοιχεία `<ul>`, `<ol>` ή `<menu>`."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Οι χρήστες δεν περιμένουν ότι μια σελίδα θα ανανεωθεί αυτόματα και η εστίαση θα επιστρέψει στην κορυφή της σελίδας. Αυτό μπορεί να δημιουργήσει δυσαρέσκεια ή σύγχυση. [Μάθετε περισσότερα σχετικά με τη μεταετικέτα ανανέωσης](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Το έγγραφο χρησιμοποιεί μια ετικέτα `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Το έγγραφο δεν χρησιμοποιεί `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Η απενεργοποίηση της δυνατότητας εστίασης αποτελεί πρόβλημα για τους χρήστες με περιορισμένη όραση που βασίζονται στη μεγέθυνση οθόνης για να βλέπουν σωστά τα περιεχόμενα μιας ιστοσελίδας. [Μάθετε περισσότερα σχετικά με τη μεταετικέτα θύρας προβολής](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "To χαρακτηριστικό `[user-scalable=\"no\"]` χρησιμοποιείται στο στοιχείο `<meta name=\"viewport\">` ή το χαρακτηριστικό `[maximum-scale]` έχει τιμή μικρότερη από 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "To χαρακτηριστικό `[user-scalable=\"no\"]` δεν χρησιμοποιείται στο στοιχείο `<meta name=\"viewport\">` και το χαρακτηριστικό `[maximum-scale]` δεν έχει τιμή μικρότερη από 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Οι αναγνώστες οθόνης δεν μπορούν να μεταφράσουν περιεχόμενο που δεν είναι κείμενο. Η προσθήκη εναλλακτικού κειμένου στα στοιχεία `<object>` βοηθά τους αναγνώστες οθόνης να αποδίδουν το νόημα στους χρήστες. [Μάθετε περισσότερα σχετικά με το εναλλακτικό κείμενο για στοιχεία `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Τα στοιχεία `<object>` δεν έχουν εναλλακτικό κείμενο"}, "core/audits/accessibility/object-alt.js | title": {"message": "Τα στοιχεία `<object>` έχουν εναλλακτικό κείμενο"}, "core/audits/accessibility/select-name.js | description": {"message": "Τα στοιχεία φόρμας χωρίς αποτελεσματικές ετικέτες μπορούν να δημιουργήσουν ενοχλητικές εμπειρίες για τους χρήστες του αναγνώστη οθόνης. [Μάθετε περισσότερα σχετικά με το στοιχείο `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Κάποια στοιχεία δεν έχουν συσχετισμένα στοιχεία ετικέτας."}, "core/audits/accessibility/select-name.js | title": {"message": "Ορισμένα επιλεγμένα στοιχεία έχουν συσχετισμένα στοιχεία ετικέτας."}, "core/audits/accessibility/skip-link.js | description": {"message": "Η συμπερίληψη ενός συνδέσμου παράβλεψης μπορεί να βοηθήσει τους χρήστες να μεταβούν στο κύριο περιεχόμενο για να εξοικονομήσουν χρόνο. [Μάθετε περισσότερα σχετικά με τους συνδέσμους παράβλεψης](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Οι σύνδεσμοι παράβλεψης δεν είναι εστιασμένοι."}, "core/audits/accessibility/skip-link.js | title": {"message": "Οι σύνδεσμοι παράβλεψης είναι εστιασμένοι."}, "core/audits/accessibility/tabindex.js | description": {"message": "Μια τιμή μεγαλύτερη από 0 υποδηλώνει μια ρητή σειρά πλοήγησης. Εάν και ορθό από τεχνικής άποψης, αυτό συχνά επηρεάζει αρνητικά την εμπειρία των χρηστών που βασίζονται στις τεχνολογίες υποβοήθησης. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Ορισμένα στοιχεία έχουν μια τιμή `[tabindex]` μεγαλύτερη από 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Κανένα στοιχείο δεν έχει τιμή `[tabindex]` μεγαλύτερη από 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Το χαρακτηριστικό summary θα πρέπει να περιγράφει τη δομή του πίνακα, ενώ το στοιχείο `<caption>` πρέπει να έχει τον τίτλο στην οθόνη. Η ακριβής σήμανση πίνακα βοηθά τους χρήστες των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό summary και το στοιχείο caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Οι πίνακες έχουν το ίδιο περιεχόμενο στο χαρακτηριστικό summary και το στοιχείο `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Οι πίνακες έχουν διαφορετικό περιεχόμενο στο χαρακτηριστικό summary και το στοιχείο `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Οι αναγνώστες οθόνης έχουν λειτουργίες που διευκολύνουν την πλοήγηση στους πίνακες. Διασφαλίζοντας ότι οι πίνακες χρησιμοποιούν το πραγματικό στοιχείο υποτίτλων αντί για κελιά με το χαρακτηριστικό `[colspan]`, ενδέχεται να βελτιωθεί η εμπειρία για τους χρήστες των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με τους υπότιτλους](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Οι πίνακες δεν χρησιμοποιούν το στοιχείο `<caption>` αντί για κελιά με το χαρακτηριστικό `[colspan]` για να υποδείξουν έναν υπότιτλο."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Οι πίνακες χρησιμοποιούν το στοιχείο `<caption>` αντί για κελιά με το χαρακτηριστικό `[colspan]`, για να υποδεικνύουν έναν υπότιτλο."}, "core/audits/accessibility/target-size.js | description": {"message": "Οι στόχοι αφής με επαρκές μέγεθος και απόσταση βοηθούν τους χρήστες που μπορεί να δυσκολεύονται να στοχεύσουν μικρά στοιχεία ελέγχου να ενεργοποιήσουν τους στόχους. [Μάθετε περισσότερα σχετικά με τους στόχους αφής](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Οι στόχοι αφής δεν έχουν επαρκές μέγεθος ή απόσταση."}, "core/audits/accessibility/target-size.js | title": {"message": "Οι στόχοι αφής έχουν επαρκές μέγεθος και απόσταση."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Οι αναγνώστες οθόνης έχουν λειτουργίες που διευκολύνουν την πλοήγηση στους πίνακες. Διασφαλίζοντας ότι τα στοιχεία `<td>` σε έναν μεγάλο πίνακα (3 ή περισσότερα κελιά σε πλάτος και ύψος) έχουν μια συσχετισμένη κεφαλίδα πίνακα, ενδέχεται να βελτιωθεί η εμπειρία για τους χρήστες των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με τις κεφαλίδες πίνακα](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Τα στοιχεία `<td>` σε ένα μεγάλο `<table>` δεν έχουν κεφαλίδες πίνακα."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Τα στοιχεία `<td>` σε ένα μεγάλο `<table>` έχουν μία ή περισσότερες κεφαλίδες πίνακα."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Οι αναγνώστες οθόνης έχουν λειτουργίες που διευκολύνουν την πλοήγηση στους πίνακες. Διασφαλίζοντας ότι τα κελιά `<td>` που χρησιμοποιούν το χαρακτηριστικό `[headers]` παραπέμπουν μόνο σε άλλα κελιά στον ίδιο πίνακα, μπορείτε να βελτιώσετε την εμπειρία των χρηστών των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με το χαρακτηριστικό `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Τα κελιά σε ένα στοιχείο `<table>` που χρησιμοποιούν το χαρακτηριστικό `[headers]` παραπέμπουν σε ένα στοιχείο `id` που δεν βρίσκεται στον ίδιο πίνακα."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Τα κελιά σε ένα στοιχείο `<table>` που χρησιμοποιούν το χαρακτηριστικό `[headers]` παραπέμπουν σε κελιά εντός του ίδιου πίνακα."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Οι αναγνώστες οθόνης έχουν λειτουργίες που διευκολύνουν την πλοήγηση στους πίνακες. Διασφαλίζοντας ότι οι κεφαλίδες πίνακα παραπέμπουν πάντα σε ένα σύνολο κελιών του πίνακα, μπορείτε να βελτιώσετε την εμπειρία των χρηστών των αναγνωστών οθόνης. [Μάθετε περισσότερα σχετικά με τις κεφαλίδες πίνακα](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Τα στοιχεία `<th>` και τα στοιχεία με `[role=\"columnheader\"/\"rowheader\"]` δεν έχουν τα κελιά δεδομένων που περιγράφουν."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Τα στοιχεία `<th>` και τα στοιχεία με `[role=\"columnheader\"/\"rowheader\"]` έχουν τα κελιά δεδομένων που περιγράφουν."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ο ορισμός μιας έγκυρης [γλώσ<PERSON><PERSON><PERSON> BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) στα στοιχεία συμβάλλει στο να διασφαλιστεί ότι ο αναγνώστης οθόνης θα εκφωνήσει σωστά το κείμενο. [Μάθετε πώς μπορείτε να χρησιμοποιήσετε το χαρακτηριστικό `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Τα χαρακτηριστικά `[lang]` δεν έχουν έγκυρη τιμή"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Τα χαρακτηριστικά `[lang]` έχουν μια έγκυρη τιμή"}, "core/audits/accessibility/video-caption.js | description": {"message": "Όταν ένα βίντεο περιέχει υπότιτλους, διευκολύνεται η πρόσβαση των χρηστών που είναι κωφοί ή έχουν προβλήματα ακοής στις πληροφορίες που περιέχει. [Μάθετε περισσότερα σχετικά με τους υπότιτλους βίντεο](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Τα στοιχεία `<video>` δεν περιέχουν ένα στοιχείο `<track>` με `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Τα στοιχεία `<video>` περιέχουν ένα στοιχείο `<track>` με `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Τρέχουσα τιμή"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Προτεινόμενο διακριτικό"}, "core/audits/autocomplete.js | description": {"message": "Το χαρακτηριστικό `autocomplete` βοηθά τους χρήστες να υποβάλλουν φόρμες ταχύτερα. Για να μειώσετε τον φόρτο εργασίας των χρηστών, εξετάστε το ενδεχόμενο ενεργοποίησης ορίζοντας το χαρακτηριστικό `autocomplete` σε μια έγκυρη τιμή. [Μάθετε περισσότερα σχετικά με το `autocomplete` σε φόρμες](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` στοιχεία δεν διαθέτουν σωστά χαρακτηριστικά `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Απαιτεί μη αυτόματο έλεγχο"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Ελέγξτε τη σειρά των διακριτικών"}, "core/audits/autocomplete.js | title": {"message": "`<input>` στοιχεία χρησιμοποιούν σωστά το χαρακτηριστικό `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Διακριτικά `autocomplete`: Το {token} δεν είναι έγκυρο στο {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Έλεγχος σειράς διακριτικών: \"{tokens}\" στο {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Με δυνατότητα ενέργειας"}, "core/audits/bf-cache.js | description": {"message": "Πολλές πλοηγήσεις εκτελούνται με την επιστροφή στην προηγούμενη σελίδα ή με την μετάβαση ξανά μπροστά. Η κρυφή μνήμη πίσω-εμπρός (bfcache) μπορεί να επιταχύνει τις πλοηγήσεις επιστροφής. [Μάθετε περισσότερα σχετικά με το bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 λόγος αποτυχίας}other{# λόγοι αποτυχίας}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Αιτία αποτυχίας"}, "core/audits/bf-cache.js | failureTitle": {"message": "Η σελίδα απέτρεψε την επαναφ<PERSON>ρά της κρυφής μνήμης πίσω-εμπρός"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Τύ<PERSON>ος αποτυχίας"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> δυνατότητα ενέργειας"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Εκκρεμής υποστήριξη προγράμματος περιήγησης"}, "core/audits/bf-cache.js | title": {"message": "Η σελίδα δεν εμπόδισε την επαναφορά της κρυφής μνήμης πίσω-εμπρός"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Δεν είναι δυνατή η δοκιμή της κρυφής μνήμης πίσω-εμπρός στο παλιό Headless Chrome (`--chrome-flags=\"--headless=old\"`). Για να δείτε τα αποτελέσματα του ελέγχου, χρησιμοποιήστε το νέο Headless Chrome (`--chrome-flags=\"--headless=new\"`) ή το βασικό Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Οι επεκτάσεις του Chrome επηρέασαν αρνητικά την απόδοση φόρτωσης αυτής της σελίδας. Δοκιμάστε να ελέγξετε τη σελίδα σε κατάσταση ανώνυμης περιήγησης ή από ένα προφίλ του Chrome χωρίς επεκτάσεις."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Αξιολόγηση σεναρίου"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Ανάλυση σεναρίου"}, "core/audits/bootup-time.js | columnTotal": {"message": "Συνολικός χρόνος CPU"}, "core/audits/bootup-time.js | description": {"message": "Εξετάστε το ενδεχόμενο να μειώσετε τον χρόνο ανάλυσης, μεταγλώττισης και εκτέλεσης JS. Μπορεί να διαπιστώσετε ότι η προβολή μικρότερων φορτίων δεδομένων JS συμβάλλει προς αυτή την κατεύθυνση. [Μάθετε πώς μπορείτε να μειώσετε τον χρόνο εκτέλεσης JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Μείωση χρόνου εκτέλεσης JavaScript"}, "core/audits/bootup-time.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> εκτέλεσης <PERSON>"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Καταργήστε τις μεγάλες, διπλότυπες λειτουργικές μονάδες JavaScript από πακέτα, για να μειώσετε τα μη απαραίτητα byte που καταναλώνονται από τη δραστηριότητα δικτύου. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Κατάργηση διπλότυπων λειτουργικών μονάδων σε πακέτα JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Οι μεγάλες εικόνες GIF δεν είναι αποδοτικές για την προβολή περιεχομένου κινούμενων εικόνων. Εξετάστε το ενδεχόμενο, αντί να χρησιμοποιείτε εικόνες GIF, να χρησιμοποιείτε βίντεο MPEG4/WebM για τις κινούμενες εικόνες και εικόνες PNG/WebP για τις στατικές εικόνες, ώστε να εξοικονομείτε byte δικτύου. [Μάθετε περισσότερα σχετικά με τις αποδοτικές μορφές βίντεο](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Χρήση μορφών βίντεο για περιεχόμενο κινούμενων εικόνων"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Τα Polyfill και οι μετασχηματισμοί επιτρέπουν στα προγράμματα περιήγησης παλαιού τύπου να χρησιμοποιούν νέες λειτουργίες JavaScript. Ωστόσ<PERSON>, πολλές από αυτές δεν είναι απαραίτητες για σύγχρονα προγράμματα περιήγησης. Για JavaScript σε πακέτο, υιοθετήστε μια σύγχρονη στρατηγική ανάπτυξης σεναρίων χρησιμοποιώντας εντοπισμό λειτουργίας module/nomodule για μείωση του όγκου κώδικα που αποστέλλεται σε σύγχρονα προγράμματα περιήγησης, διατηρώντας την υποστήριξη για προγράμματα περιήγησης παλαιού τύπου. [Μάθετε πώς να χρησιμοποιείτε τη σύγχρονη JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Αποφύγετε την προβολή JavaScript παλαιού τύπου σε σύγχρονα προγράμματα περιήγησης"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Οι μορφές εικόνα<PERSON>, <PERSON><PERSON><PERSON><PERSON> WebP και A<PERSON>, παρέχουν συχνά καλύτερη συμπίεση σε σχέση με τις μορφές PNG ή JPEG, κάτι που σημαίνει ταχύτερες λήψεις και μικρότερη κατανάλωση δεδομένων. [Μάθετε περισσότερα σχετικά με τις σύγχρονες μορφές εικόνας](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Προβολή εικόνων σε μορφές επόμενης γενιάς"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Εξετάστε το ενδεχόμενο αργής φόρτωσης των εικόνων εκτός οθόνης και των κρυφών εικόνων μετά τη φόρτωση όλων των κρίσιμων πόρων. Με αυτόν τον τρόπο, μπορεί να μειωθεί ο χρόνος για αλληλεπίδραση. [Μάθετε πώς μπορείτε να αναβάλετε τη φόρτωση εικόνων εκτός οθόνης](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Καθυστέρηση φόρτωσης εικόνων εκτός οθόνης"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Υπάρχουν πόροι οι οποίοι αποκλείουν την πρώτη σχεδίαση (FP) της σελίδας σας. Εξετάστε το ενδεχόμενο προβολής σημαντικών ενσωματωμένων JS/CSS και καθυστέρησης όλων των μη σημαντικών JS/στιλ. [Μάθετε πώς μπορείτε να εξαλείψετε τους πόρους που αποκλείουν την απόδοση](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Εξάλειψη πόρων που αποκλείουν την απόδοση"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Τα μεγάλα φορτία δικτύου συνεπάγονται οικονομικό κόστος για τους χρήστες και σχετίζονται σε μεγάλο βαθμό με εκτενείς χρόνους φόρτωσης. [Μάθετε πώς μπορείτε να μειώσετε τα μεγέθη των ωφέλιμων φορτίων](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Το συνολικό μέγεθος ήταν {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Αποφύγετε τα πολύ μεγάλα φορτία δεδομένων δικτύου"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Αποφεύγει τα πολύ μεγάλα φορτία δεδομένων δικτύου"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "H ελαχιστοποίηση των αρχείων CSS μπορεί να μειώσει τα μεγέθη των ωφέλιμων φορτίων δικτύου. [Μάθετε πώς μπορείτε να ελαχιστοποιήσετε το CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Ελαχιστοποίηση CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Η ελαχιστοποίηση των αρχείων JavaScript μπορεί να μειώσει τα μεγέθη των ωφέλιμων φορτίων και τον χρόνο ανάλυσης σεναρίων. [Μάθετε πώς μπορείτε να ελαχιστοποιήσετε την JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Ελαχιστοποίηση JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Μειώστε τους κανόνες που δεν χρησιμοποιούνται από τα φύλλα στιλ και αναβάλετε τη φόρτωση στοιχείων CSS που δεν χρησιμοποιούνται για περιεχόμενο στο επάνω μέρος, προκειμένου να μειωθούν τα byte που καταναλώνονται από τη δραστηριότητα δικτύου. [Μάθετε πώς μπορείτε να μειώσετε το CSS που δεν χρησιμοποιείται](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Μείωση στοιχε<PERSON>ων CSS που δεν χρησιμοποιούνται"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Μειώστε τα στοιχεία JavaScript που δεν χρησιμοποιούνται και αναβάλετε τη φόρτωση σεναρίων μέχρι να ζητηθούν, προκειμένου να μειωθούν τα byte που καταναλώνονται από τη δραστηριότητα δικτύου. [Μάθετε πώς μπορείτε να μειώσετε την JavaScript που δεν χρησιμοποιείται](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Μείωση στοιχείων JavaScript που δεν χρησιμοποιούνται"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Η μεγάλη διάρκεια ζωής της κρυφής μνήμης μπορεί να επιταχύνει τις επαναλαμβανόμενες επισκέψεις στη σελίδα σας. [Μάθετε περισσότερα σχετικά με τις αποδοτικές πολιτικές για την κρυφή μνήμη](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 πόρος}other{Βρέθηκαν # πόροι}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Προβολή στατικών στοιχείων με επαρκή πολιτική κρυφής μνήμης"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Χρησιμοποι<PERSON><PERSON> αποδοτική πολιτική κρυφής μνήμης σε στατικά στοιχεία"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Οι βελτιστοποιημένες εικόνες φορτώνονται πιο γρήγορα και καταναλώνουν λιγότερα δεδομένα κινητής τηλεφωνίας. [Μάθετε πώς μπορείτε να κωδικοποιήσετε αποτελεσματικά εικόνες](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Αποδοτι<PERSON><PERSON> κωδικοποίηση εικόνων"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Πραγματικές διαστάσεις"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Διαστά<PERSON><PERSON>ις προβολής"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Οι εικόνες ήταν μεγαλύτερες από το εμφανιζόμενο μέγεθός τους"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Οι εικόνες ήταν κατάλληλες για το εμφανιζόμενο μέγεθός τους"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Συνιστάται η προβολή εικόνων κατάλληλου μεγέθους για την εξοικονόμηση δεδομένων κινητής τηλεφωνίας και τη βελτίωση του χρόνου φόρτωσης. [Μάθετε πώς να μπορείτε να αλλάξετε το μέγεθος των εικόνων](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Κατάλληλη προσαρμογή μεγέθους εικόνων"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Η προβολή των πόρων που βασίζονται σε κείμενο πρέπει να γίνεται με συμπίεση (gzip, deflate ή brotli), ώστε να ελαχιστοποιείται ο συνολικός όγκος byte δικτύου. [Μάθετε περισσότερα σχετικά με τη συμπίεση κειμένου](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Ενεργοποίηση συμπίεσης κειμένου"}, "core/audits/content-width.js | description": {"message": "Εάν το πλάτος του περιεχομένου της εφαρμογής σας δεν αντιστοιχεί στο πλάτος της θύρας προβολής, η εφαρμογή σας ενδέχεται να μην είναι βελτιστοποιημένη για οθόνες κινητών. [Μάθετε πώς μπορείτε να καθορίσετε το μέγεθος του περιεχομένου για τη θύρα προβολής](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Το μέγεθος {innerWidth}px της θύρας προβολής δεν αντιστοιχεί στο μέγεθος {outerWidth}px του παραθύρου."}, "core/audits/content-width.js | failureTitle": {"message": "Το μέγεθος του περιεχομένου δεν προσαρμόζεται σωστά για τη θύρα προβολής"}, "core/audits/content-width.js | title": {"message": "Το μέγεθος του περιεχομένου έχει προσαρμοστεί σωστά για τη θύρα προβολής"}, "core/audits/critical-request-chains.js | description": {"message": "Στις ακόλουθες αλυσίδες κρίσιμων αιτημάτων φαίνεται ποιοι πόροι φορτώνονται με υψηλή προτεραιότητα. Για τη βελτίωση της φόρτωσης των σελίδων, εξετάστε το ενδεχόμενο μείωσης του μεγέθους των αλυσίδων, μείωσης του μεγέθους λήψης πόρων ή καθυστέρησης λήψης των μη απαραίτητων πόρων. [Μάθετε πώς μπορείτε να αποφύγετε τη συσσώρευση κρίσιμων αιτημάτων](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 αλυσίδα}other{Βρέθηκαν # αλυσίδες}}"}, "core/audits/critical-request-chains.js | title": {"message": "Αποφύγετε τη συσσώρευση σημαντικών αιτημάτων"}, "core/audits/csp-xss.js | columnDirective": {"message": "Οδηγία"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Σοβαρότητα"}, "core/audits/csp-xss.js | description": {"message": "Μια ισχυρή Πολιτική ασφάλειας περιεχομένου (CSP) μειώνει σημαντικά τον κίνδυνο επιθέσεων μέσω δέσμης ενεργειών μεταξύ ιστοτόπων (XSS). [Μάθετε πώς μπορείτε να χρησιμοποιήσετε CSP για την αποφυγή XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Σύνταξη"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Η σελίδα περιέχει CSP που ορίζεται σε ετικέτα `<meta>`. Εξετάστε το ενδεχόμενο να μετακινήσετε το CSP σε μια κεφαλίδα HTTP ή να ορίσετε ένα άλλο αυστηρό CSP σε μια κεφαλίδα HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Δεν βρέθηκε CSP στη λειτουργία επιβολής"}, "core/audits/csp-xss.js | title": {"message": "Διασφαλίστε ότι το CSP είναι αποτελεσματικό έναντι επιθέσεων XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Κατάργηση / Προειδοποίηση"}, "core/audits/deprecations.js | columnLine": {"message": "Γραμμή"}, "core/audits/deprecations.js | description": {"message": "Τα καταργημένα API θα αφαιρεθούν κάποια στιγμή από το πρόγραμμα περιήγησης. [Μάθετε περισσότερα σχετικά με τα καταργημένα API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 προειδοποίηση}other{Βρέθηκαν # προειδοποιήσεις}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Χρησιμοποιεί καταργημένα API"}, "core/audits/deprecations.js | title": {"message": "Αποφυγή καταργημένων API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Απαιτείται δήλωση κωδικοποίησης χαρακτήρων. Μπορεί να πραγματοποιηθεί με μια ετικέτα `<meta>` στα πρώτα 1024 byte του κώδικα HTML ή στην κεφαλίδα απόκρισης HTTP τύπου περιεχομένου. [Μάθετε περισσότερα σχετικά με τη δήλωση της κωδικοποίησης χαρακτήρων](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Η δήλωση συνόλου χαρακτήρων λείπει ή εμφανίζεται πολύ αργά στον κώδικα HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Καθορίζει το σύνολο χαρακτήρων με τον σωστό τρόπο"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Ο ορισμός ενός τύπου εγγράφου (doctype) εμποδίζει τη μετάβαση του προγράμματος περιήγησης στη λειτουργία ιδιαιτεροτήτων. [Μάθ<PERSON>τε περισσότερα σχετικά με τη δήλωση τύπου εγγράφου (doctype)](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Το όνομα τύπου εγγράφου (doctype) πρέπει να είναι η συμβολοσειρά `html`."}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Το έγγραφ<PERSON> περιέχει ένα `doctype` που ενεργοποιεί τη λειτουργία `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Το έγγραφο πρέπει να περιέχει έναν τύπο εγγράφου (doctype)"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Το αναγνω<PERSON>ιστικ<PERSON> publicId αναμενόταν να είναι μια κενή συμβολοσειρά"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Το αναγνωριστικ<PERSON> systemId αναμενόταν να είναι μια κενή συμβολοσειρά"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Το έγγραφ<PERSON> περιέχει ένα `doctype` που ενεργοποιεί τη λειτουργία `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Από τη σελίδα λείπει ο τύπος εγγράφου (doctype) HTML, με αποτέλεσμα να ενεργοποιείται η λειτουργία ιδιαιτεροτήτων (quirks-mode)."}, "core/audits/dobetterweb/doctype.js | title": {"message": "Η σελίδα έχει τύπο εγγράφου (doctype) HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Στατιστικ<PERSON> στοιχείο"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Τιμή"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Ένα μεγάλο DOM μπορεί να αυξήσει τη χρήση της μνήμης, να προκαλέσει [υπολογισμούς στιλ](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) μεγαλύτερης διάρκειας και να δημιουργήσει [επαναλήψεις ροών διάταξης](https://developers.google.com/speed/articles/reflow) υψηλού κόστους. [Μάθετε πώς να μπορείτε να αποφύγετε το υπερβολικό μέγεθος DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 στοιχείο}other{# στοιχεία}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Αποφύγετε τα υπερβολικά μεγάλα μεγέθη DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Μέγιστο βάθος DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Σύνολο στοιχείων DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Μέγιστος αριθμός θυγατρικών στοιχείων"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Αποφεύγει τα υπερβολικά μεγάλα μεγέθη DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Οι ιστότοποι που απαιτούν τη γνωστοποίηση τοποθεσίας χωρίς προφανή αιτία προκαλούν σύγχυση ή φαίνονται ύποπτοι στους χρήστες. Συνιστάται τα αιτήματα να συνδέονται με τις ενέργειες των χρηστών. [Μάθετε περισσότερα σχετικά με την άδεια γεωγραφικής τοποθεσίας](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Αίτημα για άδεια εντοπισμού γεωγραφικής τοποθεσίας κατά τη φόρτωση σελίδων"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Αποφυγή αιτή<PERSON>α<PERSON>ος για άδεια εντοπισμού γεωγραφικής τοποθεσίας κατά τη φόρτωση σελίδων"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Κατηγο<PERSON><PERSON><PERSON> ζητήματος"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Τα ζητήματα που έχουν καταγ<PERSON>α<PERSON><PERSON><PERSON> στο πλαίσιο `Issues` στο Chrome Devtools υποδεικνύουν ότι υπάρχουν προβλήματα τα οποία δεν έχουν επιλυθεί. Μπορεί να σχετίζονται με σφάλματα αιτημάτων δικτύου, ανεπαρκ<PERSON> στοιχεία ελέγχου ασφαλείας ή με άλλα ζητήματα του προγράμματος περιήγησης. Ανοίξτε το πλαίσιο Ζητήματα στο Chrome DevTools για περισσότερες λεπτομέρειες σχετικά με κάθε ζήτημα."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Καταγρά<PERSON>ηκαν ζητήματα στο πλαίσιο `Issues` στο Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Αποκλείστηκε από την πολιτική διασταυρούμενων προελεύσεων."}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Αυξημένη χρήση πόρων από διαφημίσεις"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Δεν υπάρχουν ζητήματα στο πλαίσιο `Issues` στο Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Έκδοση"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Όλες οι βιβλιοθήκες JavaScript διεπαφής εντοπίστηκαν στη σελίδα. [Μάθετε περισσότερα σχετικά με αυτόν τον διαγνωστικό έλεγχο για τον εντοπισμό της βιβλιοθήκης JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Εντοπίστηκαν βιβλιοθήκες JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Για τους χρήστες με αργές συνδέσεις, η δυναμική εισαγωγή εξωτερικών σεναρίων μέσω `document.write()` μπορεί να καθυστερήσει τη φόρτωση των σελίδων για δεκάδες δευτερόλεπτα. [Μάθετε πώς να μπορείτε να αποφύγετε το document.writing()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Αποφυγή `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Αποφυγή `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Οι ιστότοποι που απαιτούν την αποστολή ειδοποιήσεων χωρίς προφανή αιτία προκαλούν σύγχυση ή φαίνονται ύποπτοι στους χρήστες. Συνιστάται τα αιτήματα να συνδέονται με τις κινήσεις των χρηστών. [Μάθετε περισσότερα σχετικά με την υπεύθυνη λήψη άδειας για τις ειδοποιήσεις](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Αίτημα για άδεια ειδοποίησης κατά τη φόρτωση σελίδων"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Αποφυγή αιτή<PERSON>α<PERSON>ος για άδεια ειδοποίησης κατά τη φόρτωση σελίδων"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Η αποτροπή της επικόλλησης σε πεδία εισαγωγής είναι μια κακή πρακτική για την εμπειρία χρήστη και υποβαθμίζει την ασφάλεια, απ<PERSON><PERSON><PERSON><PERSON>ίοντας τους διαχειριστές κωδικών πρόσβασης.[Μάθετε περισσότερα σχετικά με τα πεδία εισαγωγής που είναι φιλικά προς τον χρήστη](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Δεν επιτρέπει στους χρήστες την επικόλληση σε πεδία εισαγωγής"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Επιτρέπει στους χρήστες την επικόλληση σε πεδία εισαγωγής"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Πρωτόκολλο"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Το HTTP/2 παρέχει περισσότερα πλεονεκτήματα σε σχέση με το HTTP/1.1, όπως είναι οι δυαδικές κεφαλίδες και η πολυπλεξία. [Μάθετε περισσότερα σχετικά με το HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 αίτημα δεν εξυπηρετήθηκε μεσω HTTP/2}other{# αιτήματα δεν εξυπηρετήθηκαν μέσω HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Χρήση HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Εξετάστε το ενδεχόμενο να επισημάνετε τις λειτουργίες αναμονής συμβάντων χρήσης αφής και τροχού κύλισης ως `passive`, για να βελτιώσετε την απόδοση κύλισης της σελίδας σας. [Μάθετε περισσότερα σχετικά με την υιοθέτηση των παθητικών λειτουργιών αναμονής συμβάντων](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Δεν χρησιμοποιεί παθητικές λειτουργίες επεξεργασίας συμβάντων για τη βελτίωση της απόδοσης κύλισης"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Χρησιμοποι<PERSON><PERSON> παθητικές λειτουργίες επεξεργασίας συμβάντων για τη βελτίωση της απόδοσης κύλισης"}, "core/audits/errors-in-console.js | description": {"message": "Τα σφάλματα που έχουν καταγ<PERSON>α<PERSON><PERSON>ί στην κονσόλα υποδεικνύουν ότι υπάρχουν προβλήματα τα οποία δεν έχουν επιλυθεί. Μπορεί να σχετίζονται με σφάλματα αιτημάτων δικτύου και με άλλα ζητήματα του προγράμματος περιήγησης. [Μάθετε περισσότερα σχετικά με αυτά τα σφάλματα στον διαγνωστικό έλεγχο κονσόλας](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Έχουν καταγρα<PERSON>εί σφάλματα προγράμματος περιήγησης στην κονσόλα"}, "core/audits/errors-in-console.js | title": {"message": "Δεν έχουν καταγ<PERSON>α<PERSON><PERSON><PERSON> σφάλματα προγράμματος περιήγησης στην κονσόλα"}, "core/audits/font-display.js | description": {"message": "Αξιοποιήστε τη λειτουργία CSS `font-display`, για να διασφαλίσετε ότι το κείμενο είναι ορατό στους χρήστες κατά τη φόρτωση των γραμματοσειρών ιστοτόπου. [Μάθετε περισσότερα σχετικά με το `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Βεβαιωθείτε ότι το κείμενο παραμένει ορατό κατά τη διάρκεια της φόρτωσης γραμματοσειράς ιστοτόπου"}, "core/audits/font-display.js | title": {"message": "Όλο το κείμενο παραμένει ορατό κατά τη διάρκεια φορτώσεων γραμματοσειράς ιστοτόπου"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCount<PERSON><PERSON><PERSON><PERSON><PERSON>,plural, =1{Το Lighthouse δεν μπόρεσε να ελέγξει αυτόματα την τιμή `font-display` για την προέλευση {fontOrigin}.}other{Το Lighthouse δεν μπόρεσε να ελέγξει αυτόματα τις τιμές `font-display` για την προέλευση {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> διαστ<PERSON><PERSON>εω<PERSON> (Πραγματικ<PERSON><PERSON>)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON> δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Προβολή)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Οι διαστάσεις προβολής των εικόνων πρέπει να συμφωνούν με τον φυσικό λόγο διαστάσεων. [Μάθετε περισσότερα σχετικά με τον λόγο διαστάσεων εικόνας](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Προβάλλει τις εικόνες με εσφαλμένο λόγο διαστάσεων"}, "core/audits/image-aspect-ratio.js | title": {"message": "Προβάλλει τις εικόνες με τον σωστό λόγο διαστάσεων"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Πραγματικό μέγεθος"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Εμφανιζόμενο μέγεθος"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Αναμενόμενο μέγεθος"}, "core/audits/image-size-responsive.js | description": {"message": "Για μεγιστοποίηση της ευκρίνειας της εικόνας, οι φυσικές διαστάσεις της πρέπει να αναλογούν στο μέγεθος της οθόνης και τον λόγο διαστάσεων pixel. [Μάθετε πώς μπορείτε να παράσχετε αποκριτικές εικόνες](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Προβάλλει εικόνες με χαμηλή ανάλυση"}, "core/audits/image-size-responsive.js | title": {"message": "Προβάλλει εικόνες με κατάλληλη ανάλυση"}, "core/audits/installable-manifest.js | already-installed": {"message": "Η εφαρμογή έχει εγκατασταθεί ήδη."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Δεν ήταν δυνατή η λήψη ενός απαιτούμενου εικονιδίου από το μανιφέστο."}, "core/audits/installable-manifest.js | columnValue": {"message": "Αιτία αποτυχίας"}, "core/audits/installable-manifest.js | description": {"message": "Το service worker είναι η τεχνολογία που επιτρέπει στην εφαρμογή σας να χρησιμοποιεί πολλές λειτουργίες προηγμένων εφαρμογών ιστού, <PERSON><PERSON><PERSON><PERSON> είναι η λειτουργία εκτός σύνδεσης, η προσθήκη στην αρχική οθόνη και οι ειδοποιήσεις push. Με τη σωστή υλοποίηση service worker και μανιφέστου, τα προγράμματα περιήγησης μπορούν να ζητούν ενεργά από τους χρήστες να προσθέσουν την εφαρμογή σας στην αρχική οθόνη τους, κάτι που μπορεί να οδηγήσει σε μεγαλύτερη αφοσίωση. [Μάθετε περισσότερα σχετικά με τις απαιτήσεις για τη δυνατότητα εγκατάστασης μανιφέστου](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 αιτία}other{# αιτίες}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Το μανιφέστο της εφαρμογής ιστού ή το service worker δεν ικανοποιούν τις απαιτήσεις εγκαταστασιμότητας"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Το URL της εφαρμογής Play Store και το αναγνωριστικό του Play Store δεν ταιριάζουν."}, "core/audits/installable-manifest.js | in-incognito": {"message": "Η σελίδα φορτώθηκε σε ένα παράθυρο ανώνυμης περιήγησης."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Η ιδιότητα `display` του μανιφέστου πρέπει να είναι `standalone`, `fullscreen` ή `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Το μανιφέστο περιέχει το πεδίο \"display_override\" και η πρώτη υποστηριζόμενη μέθοδος εμφάνισης θα πρέπει να είναι μία από τις εξής \"standalone\", \"fullscreen\" ή \"minimal-ui\"."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Δεν ήταν δυνατή η ανάκτηση του μανιφέστου, το μανιφέστο είναι κενό ή δεν ήταν δυνατή η ανάλυσή του."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Το URL μανιφέστου άλλαξε κατά τη λήψη του μανιφέστου."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Το μανιφέστο δεν περιέχει πεδίο `name` ή `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Το μανιφέστο δεν περιέχει κατάλληλο εικονίδιο. Απαιτείται μορφή PNG, SVG ή WebP, μεγέθους τουλάχιστον {value0} px. Το χαρακτηριστικό μεγέθους θα πρέπει να οριστεί και το χαρακτηριστικό σκοπού, εάν οριστεί, θα πρέπει να περιλαμβάνει την τιμή \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Κανένα παρεχόμενο εικονίδιο δεν είναι τετράγωνο μεγέθους τουλάχιστον {value0} px σε μορφή PNG, SVG ή WebP, με το στοιχείο σκοπού μη ορισμένο ή ορισμένο σε \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Το εικονίδιο λήψης ήταν κενό ή κατεστραμμένο."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Δεν παρασχέθηκε αναγνωριστικό Play Store"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Η σελίδα δεν περιέχει <link> URL μανιφέστου"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο έλεγχος του service worker χω<PERSON><PERSON><PERSON> πεδίο \"start_url\" στο μανιφέστο."}, "core/audits/installable-manifest.js | noErrorId": {"message": "Το αναγνωριστικ<PERSON> σφάλματος εγκαταστασιμότητας \"{errorId}\" δεν αναγνωρίζεται."}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Η σελίδα δεν προβάλλεται από ασφαλή προέλευση."}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Η σελίδα δεν φορτώνεται στο κύριο πλαίσιο."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Η σελίδα δεν λειτουργεί εκτός σύνδεσης."}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Το PWA έχει απεγκατασταθεί και γίνεται επαναφορά των ελέγχων δυνατότητας εγκατάστασης."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Η καθορισμένη πλατφόρμα εφαρμογών δεν υποστηρίζεται σε Android."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Το μανιφέστο καθορίζει prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Η παράμετρος prefer_related_applications υποστηρίζεται μόνο σε κανάλια Chrome Beta και κανάλια σταθερής έκδοσης σε Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Το Lighthouse δεν μπόρεσε να προσδιορίσει αν η σελίδα έχει δυνατότητα εγκατάστασης. Δοκιμάστε με μια νεότερη έκδοση του Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Το URL έναρξης μανιφέστου δεν είναι έγκυρο"}, "core/audits/installable-manifest.js | title": {"message": "Το μανιφέστο της εφαρμογής ιστού και το service worker δεν ικανοποιούν τις απαιτήσεις εγκαταστασιμότητας"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Ένα URL στο μανιφέστο περιέχει όνομα χρήστη, κωδικ<PERSON> πρόσβασης ή θύρα."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Η σελίδα δεν λειτουργεί εκτός σύνδεσης. Η σελίδα δεν θα θεωρείται ως εγκαταστάσιμη μετά τη σταθερή κυκλοφορία της έκδοσης Chrome 93 τον Αύγουστο του 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Επιτρέπεται"}, "core/audits/is-on-https.js | blocked": {"message": "Αποκλεισμένο"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Μη ασφαλές URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Επίλυση αιτήματος"}, "core/audits/is-on-https.js | description": {"message": "Όλοι οι ιστότοποι πρέπει να προστατεύονται με HTTPS, ακόμα και αν δεν χειρίζονται ευαίσθητα δεδομένα. Αυτό περιλαμβάνει την αποφυγή [μικτού περιεχομένου](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), όπου ορισμένοι πόροι φορτώνονται μέσω HTTP παρά την εξυπηρέτηση του αρχικού αιτήματος μέσω HTTPS. Το HTTPS δεν επιτρέπει στους εισβολείς την αλλοίωση ή την παθητική ακρόαση των επικοινωνιών μεταξύ της εφαρμογής και των χρηστών σας. Επιπλέον, αποτελεί προαπαιτούμενο για το HTTP/2 και πολλά νέα API πλατφορμών ιστού. [Μάθετε περισσότερα σχετικά με το HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 μη ασφαλές αίτημα}other{Βρέθηκαν # μη ασφαλή αιτήματα}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Δεν χρησιμοποιεί HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Χρησιμοποιεί HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Αναβαθμίστηκε αυτόματα σε HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Επιτρέπεται με προειδοποίηση"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Φάση"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Χρονισ<PERSON><PERSON>ς"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Αυτό είναι το στοιχείο με τη μεγαλύτερη σχεδίαση με περιεχόμενο στη θύρα προβολής. [Μάθετε περισσότερα σχετικά με το στοιχείο Μεγαλύτερη σχεδίαση με περιεχόμενο](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Καθυστέρηση φόρτωσης"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> φόρτωσης"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Καθυστέρηση απόδοσης"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Στοιχ<PERSON><PERSON><PERSON>γαλύτερης σχεδίασης με περιεχόμενο"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Αντίκτυ<PERSON>ος αλλαγής διάταξης"}, "core/audits/layout-shift-elements.js | description": {"message": "Αυτά τα στοιχεία DOM επηρεάστηκαν περισσότερο από τις αλλαγές διάταξης. Ορισμένες αλλαγές στη διάταξη ενδέχεται να μην περιλαμβάνονται στην τιμή της μέτρησης CLS λόγω της [προσαρμογής σε παράθυρο](https://web.dev/articles/cls#what_is_cls). [Μάθετε πώς μπορείτε να βελτιώσετε το CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Αποφύγετε τις μεγάλες μετατοπίσεις διάταξης"}, "core/audits/layout-shifts.js | columnScore": {"message": "Βαθμολογία αλλαγής διάταξης"}, "core/audits/layout-shifts.js | description": {"message": "Αυτές είναι οι μεγαλύτερες αλλαγές διάταξης που παρατηρήθηκαν στη σελίδα. Κάθε στοιχείο πίνακα αντιπροσωπεύει μια μεμονωμένη αλλαγή διάταξης και εμφανίζει το στοιχείο που άλλαξε περισσότερο. Κάτω από κάθε στοιχείο, παρατίθενται οι πιθανές βασικές αιτίες που οδήγησαν στην αλλαγή διάταξης. Ορισμένες από αυτές τις αλλαγές διάταξης ενδέχεται να μην περιλαμβάνονται στην τιμή της μέτρησης CLS λόγω [προσαρμογής σε παράθυρο](https://web.dev/articles/cls#what_is_cls). [Μάθετε πώς μπορείτε να βελτιώσετε το CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{Βρέθηκε 1 αλλαγή διάταξης}other{Βρέθηκαν # αλλαγές διάταξης}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Φορτώθηκε η γραμματοσειρ<PERSON> ιστού"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Έγινε εισαγωγή iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Ένα αίτημα δικτύου που έχει καθυστερήσει προσάρμοσε τη διάταξη της σελίδας"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Λείπει σαφές μέγεθος από το στοιχείο μέσων"}, "core/audits/layout-shifts.js | title": {"message": "Αποφύγετε τις μεγάλες μετατοπίσεις διάταξης"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Οι εικόνες στο επάνω μέρος που φορτώνονται αργά αποδίδονται αργότερα στον κύκλο ζωής της σελίδας, κάτι που μπορεί να καθυστερήσει τη μεγαλύτερη σχεδίαση με περιεχόμενο. [Μάθετε περισσότερα σχετικά με τη βέλτιστη αργή φόρτωση](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Η εικόνα μεγαλύτερης σχεδίασης με περιεχόμενο φορτώθηκε αργά"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Η εικόνα μεγαλύτερης σχεδίασης με περιεχόμενο δεν φορτώθηκε αργά"}, "core/audits/long-tasks.js | description": {"message": "Παραθέτει τις μεγαλύτερες εργασίες στο κύριο νήμα, κάτι που είναι χρήσιμο για τον εντοπισμό των συνεισφερόντων που συμβάλλουν περισσότερο στην καθυστέρηση εισόδου. [Μάθετε πώς μπορείτε να αποφύγετε μεγάλες εργασίες στο κύριο νήμα](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε # μεγάλη εργασία}other{Βρέθηκαν # μεγάλες εργασίες}}"}, "core/audits/long-tasks.js | title": {"message": "Αποφύγετε μεγάλες εργασίες στο κύριο νήμα"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Κατηγορία"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Εξετάστε το ενδεχόμενο να μειώσετε τον χρόνο ανάλυσης, μεταγλώττισης και εκτέλεσης JS. Μπορεί να διαπιστώσετε ότι η προβολή μικρότερων φορτίων δεδομένων JS συμβάλλει προς αυτή την κατεύθυνση. [Μάθετε πώς μπορείτε να ελαχιστοποιήσετε την εργασία στο κύριο νήμα](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Ελαχιστοποίηση εργασίας κύριου νήματος"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Ελαχιστοποιεί την εργασία κύριου νήματος"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Για να προσελκύσουν τον μεγαλύτερο δυνατό αριθμό χρηστών, οι ιστότοποι θα πρέπει να λειτουργούν σε κάθε κύριο πρόγραμμα περιήγησης. [Μάθετε σχετικά με τη συμβατότητα μεταξύ προγραμμάτων περιήγησης](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Ο ιστότοπος λειτουργεί σε διαφορετικά προγράμματα περιήγησης"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Βεβαιωθείτε ότι οι μεμονωμένες σελίδες υποστηρίζουν συνδέσμους σε βάθος μέσω URL και ότι τα URL είναι μονα<PERSON><PERSON><PERSON><PERSON> με σκοπό την κοινή χρήση σε μέσα κοινωνικής δικτύωσης. [Μάθετε περισσότερα σχετικά με την παροχή συνδέσμων σε βάθος](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Κάθε σελίδα έχει URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Οι μεταβάσεις θα πρέπει να έχουν γρήγορη αίσθηση καθώς πατάτε σε διάφορα σημεία, ακόμη και σε ένα αργό δίκτυο. Αυτή η εμπειρία αποτελεί το κλειδί για την απόδοση που αντιλαμβάνεται ένας χρήστης. [Μάθετε περισσότερα σχετικά με τις μεταβάσεις σελίδας](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Οι μεταβάσεις σελίδας δεν δίνουν την αίσθηση ότι καθυστερούν στο δίκτυο"}, "core/audits/maskable-icon.js | description": {"message": "Ένα εικονίδιο με δυνατότητα μάσκας διασφαλίζει ότι η εικόνα θα γεμίζει ολόκληρο το σχήμα χωρίς να εμφανίζονται οριζόντιες μαύρες γραμμές κατά την εγκατάσταση της εφαρμογής σε μια συσκευή. [Μάθετε σχετικά με τα εικονίδια μανιφέστου με δυνατότητα μάσκας](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Το μανιφέστο δεν διαθέτει εικονίδιο με δυνατότητα μάσκας"}, "core/audits/maskable-icon.js | title": {"message": "Το μανιφέστο διαθέτει εικονίδιο με δυνατότητα μάσκας"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Οι Συνολικές αλλαγές διάταξης μετρούν την κίνηση των ορατών στοιχείων εντός της θύρας προβολής. [Μάθετε περισσότερα σχετικά με τη μέτρηση Συνολικές αλλαγές διάταξης](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Η Πρώτη σχεδίαση περιεχομένου (FCP) επισημαίνει πότε σχεδιάζεται το πρώτο κείμενο ή η πρώτη εικόνα. [<PERSON><PERSON><PERSON><PERSON><PERSON>ε περισσότερα σχετικά με τη μέτρηση Πρώτη σχεδίαση περιεχομένου (FCP)](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Η Πρώτη χρήσιμη μορφή υπολογίζει πότε καθίσταται ορατό το κύριο περιεχόμενο μιας σελίδας. [Μάθετε περισσότερα σχετικά με τη μέτρηση Πρώτη χρήσιμη μορφή](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Το Interaction to Next Paint μετρά την ανταπόκριση της σελίδας και τον χρόνο που χρειάζεται η σελίδα για να ανταποκριθεί εμφανώς στην εισαγωγή στοιχείων από τους χρήστες. [Μάθετε περισσότερα σχετικά με τη μέτρηση Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Ο χρόνος για Αλληλεπίδραση είναι το χρονικό διάστημα που απαιτείται προκειμένου η σελίδα να γίνει πλήρως διαδραστική. [Μάθετε περισσότερα σχετικά με τη μέτρηση Χρόνος για Αλληλεπίδραση](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Η μέτρηση Μεγαλύτερη σχεδίαση με περιεχόμενο, σηματοδοτεί τη χρονική στιγμή στην οποία σχεδιάζεται το μεγαλύτερο κείμενο ή η μεγαλύτερη εικόνα. [Μάθετε περισσότερα σχετικά με τη μέτρηση Μεγαλύτερη σχεδίαση με περιεχόμενο](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Η Μέγιστη δυνητική καθυστέρηση πρώτης εισόδου που θα μπορούσαν να αντιμετωπίσουν οι χρήστες είναι η διάρκεια της πιο χρονοβόρας εργασίας. [Μάθετε περισσότερα σχετικά με τη μέτρηση Μέγιστη δυνητική καθυστέρηση πρώτης εισόδου](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Το ευρετήριο ταχύτητας δηλώνει πόσο γρήγορα γίνεται ορατό το περιεχόμενο μιας σελίδας. [Μάθετε περισσότερα σχετικά με τη μέτρηση Δείκτης ταχύτητας](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Η συνολική διάρκεια, σε χιλιο<PERSON>τ<PERSON> δευτερολέπτου, όλω<PERSON> των χρονικών περιόδων από το FCP έως και τον Χρόνο για Αλληλεπίδραση, όταν η διάρκεια της εργασίας υπερβαίνει τα 50 χιλιοστά δευτερολέπτου. [Μάθετε περισσότερα σχετικά με τη μέτρηση Συνολικός χρόνος αποκλεισμού](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Οι χρόνοι αποστολής και επιστροφής δικτύου (RTT) έχουν μεγάλο αντίκτυπο στην απόδοση. Ε<PERSON>ν ο χρόνος RTT προς μια προέλευση είναι υψηλός, αυτ<PERSON> σημαίνει ότι η χρήση διακομιστών που είναι πιο κοντά στον χρήστη θα μπορούσε να βελτιώσει την απόδοση. [Μάθετε περισσότερα σχετικά με τον Χρόνο αποστολής και επιστροφής](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Χρόνοι αποστολής και επιστροφής δικτύου"}, "core/audits/network-server-latency.js | description": {"message": "Οι λανθάνοντες χρόνοι των διακομιστών μπορούν να επηρεάσουν την απόδοση στον ιστό. Εάν ο λανθάνων χρόνος του διακομιστή μιας προέλευσης είναι υψηλός, αυτ<PERSON> αποτελεί ένδειξη ότι ο διακομιστής είναι υπερφορτωμένος ή ότι έχει ανεπαρκή απόδοση backend. [Μάθετε περισσότερα για τον χρόνο απόκρισης διακομιστή](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Λα<PERSON>θ<PERSON><PERSON><PERSON><PERSON>τες χρόνοι συστημάτων υποστήριξης διακομιστή"}, "core/audits/no-unload-listeners.js | description": {"message": "Το συμβάν `unload` δεν ενεργοποιείται με αξιοπιστία και η ακρόαση για το συμβάν μπορεί να αποτρέψει τις βελτιστοποιήσεις προγράμματος περιήγησης, όπως την κρυφή μνήμη πίσω/εμπρός. Αντ' αυτού, χρησιμοποιήστε το `pagehide` ή το `visibilitychange`. [Μάθετε περισσότερα σχετικά με την κατάργηση φόρτωσης των λειτουργιών αναμονής συμβάντος](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Εγγράφει μια λειτουργία αναμονής `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Αποφεύγει λειτουργίες αναμονής συμβάντων `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Οι μη σύνθετες κινούμενες εικόνες ενδέχεται να είναι κακής ποιότητας και να αυξάνουν το CLS. [Μάθετε πώς μπορείτε να αποφύγετε τις μη σύνθετες κινούμενες εικόνες](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε # κινούμενο στοιχείο}other{Βρέθηκαν # κινούμενα στοιχεία}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Η ιδιότητα που σχετίζεται με φίλτρο μπορεί να μετακινήσει pixel."}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Η στόχευση έχει άλλη κινούμενη εικόνα που δεν είναι συμβατή."}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Το εφέ διαθέτει σύνθετη λειτουργία εκτός από replace."}, "core/audits/non-composited-animations.js | title": {"message": "Αποφύγετε τις μη σύνθετες κινούμενες εικόνες"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Η ιδιότητα που σχετίζεται με μετασχηματισμό εξαρτάται από το μέγεθος πλαισίου."}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Μη υποστηριζόμενη ιδιότητα CSS: {properties}}other{Μη υποστηριζόμενες ιδιότητες CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Το εφέ έχει μη υποστηριζόμενες παραμέτρους χρονισμού."}, "core/audits/performance-budget.js | description": {"message": "Διατηρήστε την ποσότητα και το μέγεθος των αιτημάτων δικτύου εντός των στόχων που ορίζονται από τον παρεχόμενο προϋπολογισμό απόδοσης. [Μάθετε περισσότερα σχετικά με τους προϋπολογισμούς απόδοσης](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 αίτημα}other{# αιτήματα}}"}, "core/audits/performance-budget.js | title": {"message": "Προϋπολογισμός απόδοσης"}, "core/audits/preload-fonts.js | description": {"message": "Προφορτώστε γραμματοσειρές `optional` για να μπορούν να τις χρησιμοποιούν οι νέοι επισκέπτες. [Μάθετε περισσότερα σχετικά με την προφόρτωση γραμματοσειρών](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Δεν γίνεται προφόρτωση των γραμματοσειρών με `font-display: optional`"}, "core/audits/preload-fonts.js | title": {"message": "Έχουν προφορτωθεί γραμματοσειρές με `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Αν το στοιχε<PERSON><PERSON> LCP προστίθεται δυναμικά στη σελίδα, θα πρέπει να προφορτώσετε την εικόνα ώστε να βελτιωθεί το LCP. [Μάθετε περισσότερα σχετικά με την προφόρτωση στοιχείων LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Προφόρτωση εικόνας Μεγαλύτερης σχεδίασης με περιεχόμενο"}, "core/audits/redirects.js | description": {"message": "Οι ανακατευθύνσεις προκαλούν πρόσθετες καθυστερήσεις στη φόρτωση της σελίδας. [Μάθετε πώς μπορείτε να αποφύγετε τις ανακατευθύνσεις σελίδας](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Αποφυγ<PERSON> ανακατευθύνσεων πολλών σελίδων"}, "core/audits/seo/canonical.js | description": {"message": "Οι κανονικοί σύνδεσμοι προτείνουν το URL που πρέπει να εμφανιστεί στα αποτελέσματα αναζήτησης. [Μάθετε περισσότερα σχετικά με τους κανονικούς συνδέσμους](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Πολλά URL σε διένεξη ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Μη έγκυρο URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Παραπέμπει σε μια άλλη τοποθεσία `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Δεν είναι απόλυτο URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Παραπέμπει στο ριζικό URL του τομέα (την αρχική σελίδα), αντί για μια αντίστοιχη σελίδα περιεχομένου"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Το έγγραφο δεν έχει ένα έγκυρο `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Το έγγραφο έχει ένα έγκυρο `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Μη ανιχν<PERSON><PERSON><PERSON>ιμος σύνδεσμος"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Οι μηχανές αναζήτησης ενδέχεται να χρησιμοποιούν χαρακτηριστικά `href` σε συνδέσμους για ανίχνευση ιστοτόπων. Βεβαιωθείτε ότι το χαρακτηριστικό `href` των στοιχείων αγκύρωσης συνδέεται με έναν κατάλληλο προορισμό, έτσι ώστε να είναι δυνατή η ανακάλυψη περισσότερων σελίδων του ιστοτόπου. [Μάθετε πώς μπορείτε να κάνετε ανιχνεύσιμους τους συνδέσμους](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Οι σύνδεσμοι δεν είναι ανιχνεύσιμοι"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Οι σύνδεσμοι είναι ανιχνεύσιμοι"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Επιπλέον δυσανάγνωστο κείμενο"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Μέγ<PERSON><PERSON>ος γραμματοσειράς"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% του κειμένου σελίδας"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Επιλογέας"}, "core/audits/seo/font-size.js | description": {"message": "Τα μεγέθη γραμματοσειράς κάτω από 12px είναι πολύ μικρά για να είναι ευανάγνωστα, με αποτέλεσμα οι επισκέπτες από κινητά να χρειάζεται να μεγεθύνουν με τα δάχτυλά τους τη σελίδα για να διαβάσουν το περιεχόμενο. Προσπαθήστε πάνω από το 60% του κειμένου της σελίδας να έχει μέγεθος μεγαλύτερο από ή ίσο με 12px. [Μάθετε περισσότερα σχετικά με τα ευανάγνωστα μεγέθη γραμματοσειράς](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} ευανάγνωστο κείμενο"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Το κείμενο είναι δυσανάγνωστο επειδή δεν υπάρχει βελτιστοποιημένη μεταετικέτα θύρα προβολής για οθόνες κινητών."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Το έγγραφ<PERSON> δεν χρησιμοποιεί ευανάγνωστα μεγέθη γραμματοσειράς"}, "core/audits/seo/font-size.js | legibleText": {"message": "Ευανάγ<PERSON>ω<PERSON>το κείμενο"}, "core/audits/seo/font-size.js | title": {"message": "Το έγγραφο χρησιμοποιεί ευανάγνωστα μεγέθη γραμματοσειράς"}, "core/audits/seo/hreflang.js | description": {"message": "Οι σύνδεσμοι hreflang ενημερώνουν τις μηχανές αναζήτησης σχετικά με την έκδοση σελίδας που θα πρέπει να αναφέρουν στα αποτελέσματα αναζήτησης για μια συγκεκριμένη γλώσσα ή περιοχή. [Μάθετε περισσότερα σχετικά με το `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Το έγγραφο δεν έχει ένα έγκυρο `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Τιμή σχετικού href"}, "core/audits/seo/hreflang.js | title": {"message": "Το έγγραφο έχει ένα έγκυρο `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Μη αναμεν<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικ<PERSON>ς γλώσσας"}, "core/audits/seo/http-status-code.js | description": {"message": "Οι σελίδες με ανεπιτυχείς κωδικούς κατάστασης HTTP μπορεί να μην καταλογοποιηθούν σωστά. [Μάθετε περισσότερα σχετικά με τους κωδικούς κατάστασης HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Η σελίδα αποκρίνεται με ανεπιτυχή κωδικό κατάστασης HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "Η σελίδα αποκρίνεται με επιτυχή κωδικό κατάστασης HTTP"}, "core/audits/seo/is-crawlable.js | description": {"message": "Οι μηχανές αναζήτησης δεν μπορούν να συμπεριλάβουν τις σελίδες σας στα αποτελέσματα αναζήτησης, εάν δεν έχουν άδεια για την εκτέλεση ανίχνευσης σε αυτές. [Μάθετε περισσότερα σχετικά με τις οδηγίες του ανιχνευτή](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Η σελίδα αποκλείεται από την δημιουργία καταλόγου"}, "core/audits/seo/is-crawlable.js | title": {"message": "Η σελίδα δεν αποκλείεται από την δημιουργία καταλόγου"}, "core/audits/seo/link-text.js | description": {"message": "Το περιγραφικ<PERSON> κείμενο συνδέσμων βοηθά τις μηχανές αναζήτησης να κατανοήσουν το περιεχόμενό σας. [Μ<PERSON><PERSON><PERSON><PERSON><PERSON> πώς μπορείτε να κάνετε τους συνδέσμους πιο προσβάσιμους](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 σύνδεσμος}other{Βρέθηκαν # σύνδεσμοι}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Οι σύνδεσμοι δεν έχουν περιγραφικό κείμενο"}, "core/audits/seo/link-text.js | title": {"message": "Οι σύνδεσμοι έχουν περιγραφικό κείμενο"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Εκτελέστε το [Εργαλείο δοκιμής δομημένων δεδομένων](https://search.google.com/structured-data/testing-tool/) και το [Structured Data Linter](http://linter.structured-data.org/), για να επικυρώσετε τα δομημένα δεδομένα. [Μάθετε περισσότερα σχετικά με τα δομημένα δεδομένα](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Τα δομημένα δεδομένα είναι έγκυρα"}, "core/audits/seo/meta-description.js | description": {"message": "Οι περιγραφές μεταδεδομένων μπορεί να συμπεριληφθούν στα αποτελέσματα αναζήτησης για τη συνόψιση του περιεχομένου των σελίδων. [Μάθετε περισσότερα σχετικά με την περιγραφή μεταδεδομένων](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Το κείμενο περιγραφής είναι κενό."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Το έγγραφο δεν έχει περιγραφή μεταδεδομένων"}, "core/audits/seo/meta-description.js | title": {"message": "Το έγγραφο έχει περιγραφή μεταδεδομένων"}, "core/audits/seo/plugins.js | description": {"message": "Οι μηχανές αναζήτησης δεν μπορούν να καταλογοποιήσουν το περιεχόμενο των προσθηκών, εν<PERSON> πολλές συσκευές περιορίζουν ή δεν υποστηρίζουν τις προσθήκες. [Μάθετε περισσότερα σχετικά με την αποφυγή προσθηκών](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Το έγγραφο χρησιμοποιεί προσθήκες"}, "core/audits/seo/plugins.js | title": {"message": "Το έγγραφο αποφεύγει τις προσθήκες"}, "core/audits/seo/robots-txt.js | description": {"message": "Εάν το αρχείο σας robots.txt έχει εσφαλμένη μορφή, οι ανιχνευτές ενδεχομένως να μην μπορούν να κατανοήσουν με ποιον τρόπο θέλετε να γίνεται η ανίχνευση ή η καταλογοποίηση του ιστοτόπου σας. [Μάθετε περισσότερα σχετικά με το robots.txt.](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Το αίτημα για το αρχείο robots.txt επέστρεψε τον κωδικό κατάστασης HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Βρέθηκε 1 σφάλμα}other{Βρέθηκαν # σφάλματα}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Το Lighthouse δεν ήταν δυνατό να κατεβάσει ένα αρχείο robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Το αρχείο robots.txt δεν είναι έγκυρο"}, "core/audits/seo/robots-txt.js | title": {"message": "Το αρχείο robots.txt είναι έγκυρο"}, "core/audits/seo/tap-targets.js | description": {"message": "Τα στοιχεία αλληλεπίδρασης, <PERSON><PERSON><PERSON><PERSON> είναι τα κουμπιά και οι σύνδεσμοι, θα πρέπει να είναι αρκετά μεγάλα (48x48px) ή με επαρκή χώρο γύρω τους, ώστε να μπορούν να πατηθούν εύκολα από τους χρήστες χωρίς να επικαλύπτουν άλλα στοιχεία. [Μάθετε περισσότερα σχετικά με τα στοιχεία που επιλέγονται με πάτημα](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "Το {decimalProportion, number, percent} των επιλεγόμενων με πάτημα στοιχείων έχουν κατάλληλο μέγεθος"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Τα στοιχεία που επιλέγονται με πάτημα είναι υπερβολικά μικρά γιατί δεν υπάρχει βελτιστοποιημένη μεταετικέτα θύρας προβολής για οθόνες κινητών"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Τα στοιχεία που επιλέγονται με πάτημα δεν έχουν κατάλληλο μέγεθος"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Αλληλεπικαλυπτόμενο στοιχείο επιλογής με πάτημα"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Στοιχ<PERSON><PERSON><PERSON> επιλογής με πάτημα"}, "core/audits/seo/tap-targets.js | title": {"message": "Τα στοιχεία που επιλέγονται με πάτημα έχουν το κατάλληλο μέγεθος"}, "core/audits/server-response-time.js | description": {"message": "Φροντίστε να παραμείνει σύντομος ο χρόνος απόκρισης διακομιστή για το κύριο έγγραφο, επειδή όλα τα άλλα αιτήματα εξαρτώνται από αυτόν. [Μάθετε περισσότερα σχετικά με τη μέτρηση Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Το ριζικό έγγραφο χρειάστηκε {timeInMs, number, milliseconds} χλστ.δ."}, "core/audits/server-response-time.js | failureTitle": {"message": "Μειώστε τον αρχικό χρόνο απόκρισης διακομιστή"}, "core/audits/server-response-time.js | title": {"message": "Ο αρχικός χρόνος απόκρισης διακομιστή ήταν σύντομος"}, "core/audits/splash-screen.js | description": {"message": "Μια θεματική οθόνη εκκίνησης διασφαλίζει μια εμπειρία υψηλής ποιότητας όταν οι χρήστες εκκινούν την εφαρμογή σας από την αρχική οθόνη τους. [Μάθετε περισσότερα σχετικά με τις οθόνες εκκίνησης](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Δεν έχει διαμορφωθεί για προσαρμοσμένη οθόνη εκκίνησης"}, "core/audits/splash-screen.js | title": {"message": "Διαμορφώνεται από προσαρμοσμένη οθόνη εκκίνησης"}, "core/audits/themed-omnibox.js | description": {"message": "Μπορείτε να προσθέσετε ένα θέμα στη γραμμή διευθύνσεων του προγράμματος περιήγησης, ώστε να ταιριάζει με τον ιστότοπό σας. [Μάθετε περισσότερα σχετικά με τη χρήση θεμάτων στη γραμμή διευθύνσεων](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Δεν ορίζει ένα χρώμα θέματος για τη γραμμή διευθύνσεων."}, "core/audits/themed-omnibox.js | title": {"message": "Ορίζει ένα χρώμα θέματος για τη γραμμή διευθύνσεων."}, "core/audits/third-party-cookies.js | description": {"message": "Η υποστήριξη για cookie τρίτου μέρους θα καταργηθεί σε μελλοντική έκδοση του Chrome. [Μάθετε περισσότερα σχετικά με τη σταδιακή κατάργηση των cookie τρίτου μέρους](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Βρέθηκε 1 cookie}other{Βρέθηκαν # cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Χρησιμοποιεί cookie τρίτου μέρους"}, "core/audits/third-party-cookies.js | title": {"message": "Αποφεύγει τη χρήση cookie τρίτου μέρους"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Επιτυχία πελάτη)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Μάρκ<PERSON><PERSON>ινγκ)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Κ<PERSON>ινωνικά δίκτυα)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Βίντεο)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Προϊόν"}, "core/audits/third-party-facades.js | description": {"message": "Ορισμένες ενσωματώσεις τρίτου μέρους μπορούν να φορτωθούν αργά. Μπορείτε να τις αντικαταστήσετε με μια πρόσοψη μέχρι να ζητηθούν. [Μάθετε πώς μπορείτε να αναβάλετε τρίτα μέρη με πρόσοψη](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# διαθέσιμη εναλλακτική επιλογή για την πρόσοψη}other{# διαθέσιμες εναλλακτικές επιλογές για την πρόσοψη}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Ορισμένοι πόροι τρίτου μέρους μπορούν να φορτωθούν αργά με μια πρόσοψη"}, "core/audits/third-party-facades.js | title": {"message": "Αργή φόρτωση πόρων τρίτου μέρους με προσόψεις"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Τρίτο μέρος"}, "core/audits/third-party-summary.js | description": {"message": "Ο κώδικας τρίτων παρόχων μπορεί να επηρεάσει σημαντικά την απόδοση φόρτωσης. Περιορίστε τον αριθμό των περιττών τρίτων παρόχων και προσπαθήστε η φόρτωση του κώδικα τρίτων παρόχων να γίνεται αφού πρώτα έχει ολοκληρωθεί η φόρτωση της σελίδας σας. [Μάθετε πώς μπορείτε να ελαχιστοποιήσετε τον αντίκτυπο τρίτου μέρους](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Ο κώδικας τρίτου μέρους απέκλεισε το κύριο νήμα για {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Μείωση αντίκτυπου του κώδικα τρίτου μέρους"}, "core/audits/third-party-summary.js | title": {"message": "Ελαχιστοποίηση χρήσης τρίτων μερών"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Μέτρηση"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Μέτρηση"}, "core/audits/timing-budget.js | description": {"message": "Ορίστε έναν προϋπολογισμό χρονισμού που θα σας βοηθήσει με την παρακολούθηση της απόδοσης στον ιστότοπό σας. Οι αποδοτικοί ιστότοποι φορτώνουν και αποκρίνονται με ταχύτητα στα συμβάντα εισαγωγής στοιχείων από τους χρήστες. [Μάθετε περισσότερα σχετικά με τους προϋπολογισμούς απόδοσης](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Προϋπολογισμός χρονισμού"}, "core/audits/unsized-images.js | description": {"message": "Ορίστε ένα αποκλειστικό πλάτος και ύψος στα στοιχεία εικόνας, για να μειώσετε τις μετατοπίσεις διάταξης και να βελτιώσετε το CLS. [Μάθετε πώς μπορείτε να ορίσετε τις διαστάσεις εικόνας](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Τα στοιχεία εικόνας δεν έχουν σαφές `width` και `height`"}, "core/audits/unsized-images.js | title": {"message": "Τα στοιχεία εικόνας έχουν σαφές `width` και `height`"}, "core/audits/user-timings.js | columnType": {"message": "Τύπος"}, "core/audits/user-timings.js | description": {"message": "Εξετάστε το ενδεχόμενο να προσθέσετε στην εφαρμογή σας το API Χρόνων χρήστη (User Timing API), για να μετράτε την πραγματική απόδοση της εφαρμογής σας κατά τη διάρκεια σημαντικών εμπειριών χρήστη. [Μάθετε περισσότερα σχετικά με τις ενδείξεις Χρόνοι χρήστη](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 χρόνος χρήστη}other{# χρόνοι χρήστη}}"}, "core/audits/user-timings.js | title": {"message": "Ενδείξεις και μετρήσεις Χρόνων χρήστη"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Βρέθηκε ένα `<link rel=preconnect>` για το {securityOrigin} αλλά δεν χρησιμοποιήθηκε από το πρόγραμμα περιήγησης. Ελέγξτε ότι χρησιμοποιείτε σωστά το χαρακτηριστικό `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Εξετάστε το ενδεχόμενο προσθήκης υποδείξεων πόρων `preconnect` ή `dns-prefetch`, για να δημιουργήσετε πρώιμες συνδέσεις σε σημαντικές προελεύσεις τρίτους μέρους. [Μάθετε πώς μπορείτε να συνδεθείτε εκ των προτέρων σε απαιτούμενες προελεύσεις](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Προσύνδεση σε απαιτούμενες προελεύσεις"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Βρέθηκαν περισσότερες από 2 συνδέσεις `<link rel=preconnect>`. Αυτές θα πρέπει να χρησιμοποιούνται με μέτρο και μόνο στις σημαντικότερες προελεύσεις."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Βρέθηκε ένα `<link rel=preconnect>` για το {securityOrigin} αλλά δεν χρησιμοποιήθηκε από το πρόγραμμα περιήγησης. Φροντίστε να χρησιμοποιείτε `preconnect` μόνο για σημαντικές προελεύσεις, τις οποίες η σελίδα θα ζητήσει σίγουρα."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Βρέθηκε ένα `<link>` προφόρτωσης για το {preloadURL} αλλά δεν χρησιμοποιήθηκε από το πρόγραμμα περιήγησης. Ελέγξτε ότι χρησιμοποιείτε σωστά το χαρακτηριστικό `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Εξετάστε το ενδεχόμενο χρήσης του `<link rel=preload>` για την προτεραιοποίηση της ανάκτησης των πόρων που τώρα ζητούνται αργότερα στη φόρτωση σελίδας. [Μάθετε πώς μπορείτε να προφορτώσετε σημαντικά αιτήματα](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Σημαντικ<PERSON> αιτήματα προφόρτωσης"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL αντιστοίχισης"}, "core/audits/valid-source-maps.js | description": {"message": "Οι αντιστοιχίσεις πηγής μεταφράζουν ελαχιστοποιημένο κώδικα στον αρχικό πηγαίο κώδικα. Αυτ<PERSON> βοηθά τους προγραμματιστές με τον εντοπισμό σφαλμάτων στην παραγωγή. Επίσης, το Lighthouse μπορεί να παράσχει περαιτέρω insight. Εξετάστε το ενδεχόμενο ανάπτυξης αντιστοιχίσεων πηγών, για να αξιοποιήσετε αυτά τα οφέλη. [Μάθετε περισσότερα σχετικά με τις αντιστοιχίσεις πηγών](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Λείπουν οι αντιστοιχίσεις πηγής για μεγάλες βιβλιοθήκες JavaScript πρώτου μέρους"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Λείπει μια αντιστοίχιση πηγής από το μεγάλο αρχείο JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Ειδοποίηση: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ένα στοιχείο στο χαρακτηριστικό `.sourcesContent`}other{Ειδοποίηση: Λείπουν # στοιχεία στο χαρακτηριστικό `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Η σελίδα έχει έγκυρες αντιστοιχίσεις πηγής"}, "core/audits/viewport.js | description": {"message": "Μια μεταετικέτα `<meta name=\"viewport\">` δεν βελτιστοπ<PERSON>ι<PERSON><PERSON> απλώς την εφαρμογή σας για μεγέθη οθόνης κινητών, αλλά αποτρέπει και [μια καθυστέρηση 300 χιλιοστών του δευτερολέπτου στην εισαγωγή στοιχείων από τους χρήστες](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Μάθετε περισσότερα σχετικά με τη χρήση της μεταετικέτας θύρας προβολής](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Δεν βρέθηκε ετικέτα `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Δεν έχει ετικέτα `<meta name=\"viewport\">` με `width` ή `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Έχει ετικέτα `<meta name=\"viewport\">` με `width` ή `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Αυτή είναι η εργασία αποκλεισμού νημάτων που πραγματοποιείται κατά τη μέτρηση Interaction to Next Paint. [Μάθετε περισσότερα σχετικά με τη μέτρηση Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "Δαπανήθηκαν {timeInMs, number, milliseconds} ms στο συμβάν {interactionType}."}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Στ<PERSON><PERSON><PERSON> συμβάντος"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Ελαχιστοποίηση εργασίας κατά την αλληλεπίδραση με πλήκτρα"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Καθυστέρηση εισόδου"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Καθυστέρηση παρουσίασης"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> επεξεργασίας"}, "core/audits/work-during-interaction.js | title": {"message": "Ελαχιστοποιεί την εργασία κατά την αλληλεπίδραση με πλήκτρα"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε τη χρήση των ARIA στην εφαρμογή σας, κάνοντας καλύτερη την εμπειρία χρήστη σε τεχνολογίες υποβοήθησης, ό<PERSON>ω<PERSON> στον αναγνώστη οθόνης."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Αυτές είναι ευκαιρίες να παρέχετε εναλλακτικό περιεχόμενο για ήχο και βίντεο. Αυτό μπορεί να βελτιώσει την εμπειρία για χρήστες με προβλήματα ακοής ή όρασης."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Ήχος και βίντεο"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Αυτά τα στοιχεία επισημαίνουν συνήθεις βέλτιστες πρακτικές για την προσβασιμότητα."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Βέλτιστες πρακτικές"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Αυτοί οι έλεγχοι επισημαίνουν ευκαιρίες για τη [βελτίωση της προσβασιμότητας της εφαρμογής ιστού σας](https://developer.chrome.com/docs/lighthouse/accessibility/). Ο αυτόματος εντοπισμός μπορεί να εντοπίσει μόνο ένα υποσύνολο προβλημάτων και δεν εγγυάται την προσβασιμότητα της εφαρμογής ιστού, επομένως συνίσταται επίσης η [μη αυτόματη δοκιμή](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Αυτά τα στοιχεία σχετίζονται με τομείς τους οποίους δεν μπορεί να καλύψει ένα εργαλείο αυτοματοποιημένων δοκιμών. Μάθετε περισσότερα στον οδηγό μας σχετικά με τη [διεξαγωγή ενός ελέγχου προσβασιμότητας](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Προσβασιμότητα"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε την αναγνωσιμότητα του περιεχομένου σας."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Αντίθεση"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε την ερμηνεία του περιεχομένου σας από χρήστες με διαφορετικές τοπικές ρυθμίσεις."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Διεθνοποίηση και τοπική προσαρμογή"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε τη σημασιολογία των στοιχείων ελέγχου στην εφαρμογή σας. Αυτό μπορεί να κάνει καλύτερη την εμπειρία για τους χρήστες τεχνολογίας υποβοήθησης, όπως του αναγνώστη οθόνης."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Ονόματα και ετικέτες"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε την πλοήγηση με πληκτρολόγιο στην εφαρμογή σας."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Πλοήγηση"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Αυτές είναι ευκαιρίες να βελτιώσετε την εμπειρία ανάγνωσης δεδομένων σε πίνακες ή λίστες με τη χρήση τεχνολογίας υποβοήθησης, όπως του αναγνώστη οθόνης."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Πίνακες και λίστες"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Συμβατότητα προγράμματος περιήγησης"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Βέλτιστες πρακτικές"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Γενικά"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Εμπιστοσύνη και ασφάλεια"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Εμπειρία χρήστη"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Οι προϋπολογισμοί απόδοσης θέτουν τα πρότυπα για την απόδοση του ιστοτόπου σας."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Προϋπολογισμοί"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Περισσότερες πληροφορίες σχετικά με την απόδοση της εφαρμογής σας. Αυτά τα δεδομένα δεν [επηρεάζουν άμεσα](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) τη βαθμολογία απόδοσης."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Διαγνωστι<PERSON><PERSON> στοιχεία"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Η πιο σημαντική πτυχή της απόδοσης είναι η ταχύτητα με την οποία αποδίδονται τα pixel στην οθόνη. Σημαντικές μετρήσεις: Πρώτη μορφή με περιεχόμενο, Πρώτη χρήσιμη μορφή"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Βελτιώ<PERSON><PERSON><PERSON>ς πρώτης μορφής"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Αυτές οι προτάσεις μπορούν να βοηθήσουν στην ταχύτερη φόρτωση της σελίδας σας. Δεν [επηρεάζουν άμεσα](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) τη βαθμολογία απόδοσης."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Ευκαιρίες"}, "core/config/default-config.js | metricGroupTitle": {"message": "Μετρήσεις"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Βελτιώστε τη συνολική εμπειρία φόρτωσης, για να μπορεί η σελίδα να ανταποκρίνεται και να είναι έτοιμη για χρήση το συντομότερο δυνατό. Σημαντικές μετρήσεις: Χρ<PERSON>νος για Αλληλεπίδραση, Ευρετήριο ταχύτητας"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Συνολικές βελτιώσεις"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Απόδοση"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Αυτοί οι έλεγχοι επαληθεύουν τα στοιχεία μιας προηγμένης εφαρμογή ιστού. [Μάθετε τι κάνει καλή μια Προηγμένη εφαρμογή ιστού](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Αυτοί οι έλεγχοι απαιτούνται από τη [Λίστα ελέγχου PWA](https://web.dev/articles/pwa-checklist) σημείου αναφοράς αλλά δεν ελέγχονται αυτόματα από το Lighthouse. Δεν επηρεάζουν τη βαθμολογία σας αλλά είναι σημαντικό να τους επαληθεύσετε με μη αυτόματο τρόπο."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Δυνατότητα εγκατάστασης"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Με βελτιστοποίηση PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Αυτοί οι έλεγχοι διασφαλίζουν ότι η σελίδα σας ακολουθεί τις βασικές συμβουλές βελτιστοποίησης μηχανών αναζήτησης. Υπάρχουν πολλοί επιπλέον παράγοντες τους οποίους το Lighthouse δεν βαθμολογεί εδώ και οι οποίοι ενδέχεται να επηρεάζουν την κατάταξη στην αναζήτηση, συμπεριλαμβανομένης της απόδοσης στο [Core Web Vitals](https://web.dev/explore/vitals). [Μάθετε περισσότερα σχετικά με τα Βασικά στοιχεία της Αναζήτησης Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Εκτελέστε αυτά τα πρόσθετα εργαλεία επικύρωσης, για να ελέγξετε περισσότερες βέλτιστες πρακτικές SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Μορφοποιήστε το HTML με τρόπο που να επιτρέπει στους ανιχνευτές να κατανοούν καλύτερα το περιεχόμενο της εφαρμογής σας."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Βέλτιστες πρακτικές περιεχομένου"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Για να εμφανίζεται στα αποτελέσματα αναζήτησης, οι ανιχνευτές χρειάζονται πρόσβαση στην εφαρμογή σας."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Ανίχνευση και δημιουργία ευρετηρίου"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Βεβαιωθείτε ότι οι σελίδες σας είναι φιλικές προς κινητά, ώστε οι χρήστες να μην χρειάζεται να πλησιάζουν τα δάχτυλά τους ή να κάνουν μεγέθυνση για να διαβάζουν τις σελίδες περιεχομένου. [Μάθετε πώς μπορείτε να κάνετε τις σελίδες φιλικές προς κινητά](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Φιλική προς κινητά"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Η δοκιμασμένη συσκευή φαίνεται να έχει πιο αργή CPU από ό,τι αναμένει το Lighthouse. Αυτό μπορεί να επηρεάσει αρνητικά τη βαθμολογία απόδοσης. Μάθετε περισσότερα σχετικά με τη [βαθμονόμηση ενός κατάλληλου πολλαπλασιαστή επιβράδυνσης CPU](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Η σελίδα μπορεί να μην φορτώνεται με τον αναμενόμενο τρόπο επειδή το URL δοκιμής ({requested}) ανακατευθύνθηκε στο {final}. Προσπαθήστε να δοκιμάσετε απευθείας το δεύτερο URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Η φόρτωση της σελίδας ήταν υπερβολικά αργή και δεν ολοκληρώθηκε εντός του χρονικού ορίου. Τα αποτελέσματα ενδέχεται να μην είναι ολοκληρωμένα."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Έληξε το χρονικό όριο για τη διαγραφή της κρυφής μνήμης του προγράμματος περιήγησης. Δοκιμάστε να ελέγξετε ξανά αυτήν τη σελίδα και υποβάλετε σφάλμα, εάν το πρόβλημα συνεχίσει να εμφανίζεται."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Ενδέχεται να υπάρχουν αποθηκευμένα δεδομένα που επηρεάζουν την απόδοση φόρτωσης σε αυτή την τοποθεσία: {locations}. Ελέγξτε αυτήν τη σελίδα σε παράθυρο για ανώνυμη περιήγηση για να εμποδίσετε αυτούς τους πόρους από το να επηρεάσουν τις βαθμολογίες σας.}other{Ενδέχεται να υπάρχουν αποθηκευμένα δεδομένα που επηρεάζουν την απόδοση φόρτωσης σε αυτές τις τοποθεσίες: {locations}. Ελέγξτε αυτήν τη σελίδα σε παράθυρο για ανώνυμη περιήγηση για να εμποδίσετε αυτούς τους πόρους από το να επηρεάσουν τις βαθμολογίες σας.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Το χρονικό όριο της διαγραφής δεδομένων προέλευσης έληξε. Δοκιμάστε να ελέγξετε ξανά αυτήν τη σελίδα και υποβάλετε σφάλμα, εάν το πρόβλημα συνεχίσει να εμφανίζεται."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Σφάλ<PERSON>α κατά την ανάλυση της κεφαλίδας `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Εντοπίστηκε πλοήγηση στις σελίδες κατά τη διάρκεια της εκτέλεσης. Δεν συνιστάται η χρήση της λειτουργίας χρονικού διαστήματος για τον έλεγχο των πλοηγήσεων στις σελίδες. Χρησιμοποιήστε τη λειτουργία πλοήγησης, για να ελέγχετε τις πλοηγήσεις στις σελίδες για καλύτερη απόδοση τρίτου μέρους και εντοπισμό κύριου νήματος."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Μόνο οι σελίδες που φορτώνονται μέσω αιτήματος GET είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Μόνο οι σελίδες με κωδικ<PERSON> κατάστασης 2XX μπορούν να αποθηκευτούν στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Το Chrome εντόπισε μια προσπάθεια εκτέλεσης JavaScript κατά την αποθήκευση στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Οι σελίδες που έχουν ζητήσει ένα AppBanner δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς είναι απενεργοποιημένη λόγω ενός αιτήματος μηνύματος keepalive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Η κρυφή μνήμη πίσω-εμ<PERSON>ρ<PERSON>ς απενεργοποιήθηκε από επισημάνσεις. Μεταβείτε στο chrome://flags/#back-forward-cache για να την ενεργοποιήσετε τοπικά σε αυτήν τη συσκευή."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς απενεργοποιήθηκε από τη γραμμή εντολής."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς απενεργοποιήθηκε λόγω ανεπαρκούς μνήμης."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς δεν υποστηρίζεται μέσω ανάθεσης."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς απενεργοποιήθηκε για προαπόδοση."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Δεν είναι δυνατή η αποθήκευση της σελίδας στην κρυφή μνήμη επειδή έχει μια παρουσία BroadcastChannel με εγγεγραμμένες λειτουργίες αναμονής."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Οι σελίδες με την κεφαλίδα cache-control:no-store δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Έγινε σκόπιμα διαγραφή της προσωρινής μνήμης."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη προκειμένου να επιτραπεί η αποθήκευση μιας άλλης σελίδας στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Οι σελίδες που περιέχουν προσθήκες δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Οι σελίδες που χρησιμοποιούν το FileChooser API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Οι σελίδες που χρησιμοποιούν το File System Access API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Οι σελίδες που χρησιμοποιούν διεκπεραιωτή συσκευής μέσων δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Γιν<PERSON><PERSON><PERSON><PERSON> αναπαραγωγή από ένα Media Player κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Οι σελίδες που χρησιμοποιούν το MediaSession API και ορίζουν κατάσταση αναπαραγωγής δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Οι σελίδες που χρησιμοποιούν το MediaSession API και ορίζουν δείκτες χειρισμού ενεργειών δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Η κρυφή μνήμη πίσω-εμ<PERSON>ρ<PERSON><PERSON> είναι απενεργοποιημένη λόγω του αναγνώστη οθόνης."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο Security<PERSON><PERSON><PERSON> δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Οι σελίδες που χρησιμοποιούν το Serial API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Οι σελίδες που χρησιμοποιούν το WebAuthetication API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Οι σελίδες που χρησιμοποιούν το WebBluetooth API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Οι σελίδες που χρησιμοποιούν το WebUSB API δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς είναι απενεργοποιημένη επειδή τα cookie έχουν απενεργοποιηθεί σε μια σελίδα που χρησιμοποιεί το `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Οι σελίδες που χρησιμοποιούν ένα ειδικό worker ή worklet δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Η φόρτωση του εγγράφου δεν ολοκληρώθηκε πριν από την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Το banner εφαρμογής υπήρχε κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Ο Διαχειριστ<PERSON><PERSON> κωδικών πρόσβασης Chrome υπήρχε κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Η διύλιση DOM βρισκόταν σε εξέλιξη κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Το εργαλείο προβολής DOM Distiller υπήρχε κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Η κρυφή μνήμη πίσω-εμ<PERSON>ρ<PERSON><PERSON> είναι απενεργοποιημένη λόγω επεκτάσεων που χρησιμοποιούν το API ανταλλαγής μηνυμάτων."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Οι επεκτάσεις με σύνδεση μεγάλης διάρκειας θα πρέπει να κλείνουν τη σύνδεση προτού γίνει εισαγωγή τους στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Έγινε απόπειρα αποστολής μηνυμάτων σε πλαίσια στην κρυφή μνήμη πίσω-εμπρ<PERSON><PERSON>, από επεκτάσεις με σύνδεση μεγάλης διάρκειας."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς απενεργοποιήθηκε λόγω επεκτάσεων."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Εμφανι<PERSON><PERSON><PERSON><PERSON><PERSON> βοηθητικό παράθυρο διαλόγου ως εκ νέου υποβολή φόρμας ή παράθυρο διαλόγου κωδικού πρόσβασης HTTP για τη σελίδα κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Η σελίδα εκτός σύνδεσης εμφανιζ<PERSON>ταν κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Η γραμμή παρέμβασης ανεπ<PERSON><PERSON>κ<PERSON>ύς μνήμης υπήρχε κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Υπήρχαν αιτήματα αδειών κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Το πρόγραμμα αποκλεισμού αναδυόμενων παραθύρων υπήρχε κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Εμφαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> λεπτομέρειες Ασφαλούς περιήγησης κατά την απομάκρυνση."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Σύμφωνα με την Ασφαλή περιήγηση, αυτή η σελίδα είναι καταχρηστική και το αναδυόμενο αποκλείστηκε."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Ένα service worker εν<PERSON>ργ<PERSON><PERSON>οιήθηκε ενώ η σελίδα βρισκόταν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON><PERSON> είναι απενεργοποιημένη λόγω σφάλματος στο έγγραφο."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Δεν είναι δυνατή η αποθήκευση σελίδων που χρησιμοποιούν FencedFrames στο bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη προκειμένου να επιτραπεί η αποθήκευση μιας άλλης σελίδας στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Οι σελίδες στις οποίες έχει εκχωρηθεί πρόσβαση για ροή μέσων δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Οι σελίδες που χρησιμοποιούν πύλες δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχεί<PERSON> Idle<PERSON>ger δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Οι σελίδες που έχουν ανοικτή σύνδεση IndexedDB δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς είναι απενεργοποιημένη λόγω ενός συμβάντος IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Χρησιμοποιήθηκαν ακατάλληλα API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Οι σελίδες στις οποίες εισάγεται `JavaScript` από επεκτάσεις δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Οι σελίδες στις οποίες εισάγεται `StyleSheet` από επεκτάσεις δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Εσωτερικό σφάλμα."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Η κρυφή μνήμη πίσω-εμπρ<PERSON>ς είναι απενεργοποιημένη λόγω ενός αιτήματος μηνύματος keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Οι σελίδες που χρησιμοποιούν το κλείδωμα πληκτρολογίου δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | loading": {"message": "Η φόρτωση της σελίδας δεν ολοκληρώθηκε πριν από την απομάκρυνση."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Οι σελίδες των οποίων ο κύριος πόρος έχει το στοιχείο cache-control:no-cache δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Οι σελίδες των οποίων ο κύριος πόρος έχει το στοιχείο cache-control:no-store δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Η πλοήγηση ακυρώθηκε πριν να είναι δυνατή η επαναφορά της σελίδας από την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη επειδή μια ενεργή σύνδεση δικτύου έλαβε πάρα πολλά δεδομένα. Το Chrome περιορίζει την ποσότητα δεδομένων που μπορεί να λάβει μια σελίδα ενώ είναι αποθηκευμένη στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Οι σελίδες που έχουν inflight fetch() ή XHR δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη πίσω-εμπρός επειδή ένα ενεργό αίτημα δικτύου περιλάμβανε ανακατεύθυνση."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη επειδή μια σύνδεση δικτύου ήταν ανοικτή για μεγάλο διάστημα. Το Chrome περιορίζει την ποσότητα χρόνου που μπορεί να λάβει δεδομένα μια σελίδα ενώ είναι αποθηκευμένη στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Οι σελίδες που δεν έχουν έγκυρη κεφαλίδα απόκρισης δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Η πλοήγηση έγινε σε πλαίσιο διαφορετικό από το κύριο πλαίσιο."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Οι σελίδες με καταλογοποιημένες συναλλαγές DB σε εξέλιξη δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Οι σελίδες με αίτημα δικτύου in-flight δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Οι σελίδες με αίτημα δικτύου ανάκτησης in-flight δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Οι σελίδες με αίτημα δικτύου in-flight δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Οι σελίδες με αίτημα δικτύου in-flight XHR δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο PaymentManager δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Οι σελίδες που χρησιμοποιούν τη λειτουργία picture-in-picture δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | portal": {"message": "Οι σελίδες που χρησιμοποιούν πύλες δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | printing": {"message": "Οι σελίδες που εμφανίζουν τη διεπαφή χρήστη εκτύπωσης δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Η σελίδα άνοιξε με χρήση του στοιχείου `window.open()` και μια άλλη καρτέλα περιέχει αναφορά σε αυτό ή η σελίδα άνοιξε ένα παράθυρο."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Η διεργασία της λειτουργ<PERSON><PERSON>ς απόδοσης για τη σελίδα στην κρυφή μνήμη πίσω-εμπρός παρουσίασε σφάλμα."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Η διεργασία της λειτουργ<PERSON><PERSON>ς απόδοσης για τη σελίδα στην κρυφή μνήμη πίσω-εμπρός τερματίστηκε."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Οι σελίδες που έχουν ζητήσει άδειες εγγραφής ήχου δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Οι σελίδες που έχουν ζητήσει άδειες αισθητήρα δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Οι σελίδες που έχουν ζητήσει άδειες συγχρονισμού ή ανάκτησης στο παρασκήνιο δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Οι σελίδες που έχουν ζητήσει άδειες MIDI δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Οι σελίδες που έχουν ζητήσει άδειες ειδοποίησης δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Οι σελίδες που έχουν ζητήσει πρόσβαση στον αποθηκευτικό χώρο δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Οι σελίδες που έχουν ζητήσει άδειες εγγραφής βίντεο δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Μόνο οι σελίδες των οποίων ο συνδυασμός URL είναι HTTP/HTTPS μπορούν να αποθηκευτούν στην κρυφή μνήμη."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Υποβλήθηκε αξίωση για αυτήν τη σελίδα από service worker κατά την αποθήκευση στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Ένα service worker προσπάθη<PERSON>ε να στείλει ένα `MessageEvent`στη σελίδα που βρίσκεται στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Η εγγραφή του ServiceWorker καταργήθηκε ενώ η σελίδα βρισκόταν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Η σελίδα καταργήθηκε από την κρυφή μνήμη πίσω-εμπρ<PERSON>ς λόγω ενεργοποίησης service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Έγινε επανεκκίνηση του Chrome και οι καταχωρίσεις της κρυφής μνήμης πίσω-εμπρός διαγράφηκαν."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχε<PERSON><PERSON> Shared<PERSON><PERSON>ker δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο SpeechRecog<PERSON>zer δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο SpeechSynthesis δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Ένα iframe στη σελίδα ξεκίνησε πλοήγηση που δεν ολοκληρώθηκε."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Οι σελίδες των οποίων ο δευτερεύων πόρος έχει το στοιχείο cache-control:no-cache δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Οι σελίδες των οποίων ο δευτερεύων πόρος έχει το στοιχείο cache-control:no-store δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Η σελίδα ξεπέρασε τον μέγιστο χρόνο στην κρυφή μνήμη πίσω-εμπρ<PERSON>ς και έληξε το χρονικό όριο."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Έληξε το χρονικό όριο της σελίδας κατά την εισαγωγή της στην κρυφή μνήμη πίσω-εμπρός (ενδεχομένως λόγω δεικτών χειρισμού pagehide που εκτελούνται για μεγάλο χρονικό διάστημα)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Η σελίδα έχει δείκτη χειρισμού unload στο κύριο πλαίσιο."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Η σελίδα έχει δείκτη χειρισμού unload στο δευτερεύον πλαίσιο."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Το πρόγραμμα περιήγησης άλλαξε την κεφαλίδα παράκαμψης παράγοντα χρήστη."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Οι σελίδες στις οποίες έχει εκχωρηθεί πρόσβαση για εγγραφή βίντεο ή ήχου δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebDatabase δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebHID δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Οι σελίδες που χρησιμοποιούν WebLocks δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebNfc δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebOTPService δεν είναι κατάλληλες για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Οι σελίδες με το στοιχείο WebRTC δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebShare δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Οι σελίδες με το στοιχείο WebSocket δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Οι σελίδες με το στοιχείο WebTransport δεν μπορούν να εισέλθουν στην κρυφή μνήμη πίσω-εμπρός."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Οι σελίδες που χρησιμοποιούν το στοιχείο WebXR δεν είναι κατάλληλες προς το παρόν για την κρυφή μνήμη πίσω-εμπρός."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Εξετάστε το ενδεχόμενο προσθήκης συνδυασμών URL https: και http: (παραβλέπονται από προγράμματα περιήγησης που υποστηρίζουν `'strict-dynamic'`) για συμβατότητα με παλαιότερες εκδόσεις προγραμμάτων περιήγησης."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Η οδηγία `disown-opener` καταργήθηκε από την έκδοση CSP3. Εναλλακτικά, χρησιμοποιήστε την κεφαλίδα Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Η οδηγία `referrer` καταργήθηκε από την έκδοση CSP2. Εναλλακτικά, χρησιμοποιήστε την κεφαλίδα Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Η οδηγία `reflected-xss` καταργήθηκε από την έκδοση CSP2. Εναλλακτικά, χρησιμοποιήστε την κεφαλίδα X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Η απουσία της οδηγίας `base-uri` επιτρέπει στις εισαγόμενες ετικέτες `<base>` να ορίζουν το βασικό URL για όλα τα σχετικά URL (π.χ. σενάρια) σε έναν τομέα που ελέγχεται από έναν υπεύθυνο επίθεσης. Εξετάστε το ενδεχόμενο να ορίσετε την οδηγία `base-uri` σε `'none'` ή `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Η απουσία της οδηγίας `object-src` επιτρέπει την εισαγωγή προσθηκών που εκτελούν μη ασφαλή σενάρια. Εάν μπορείτε, σκεφτείτε το ενδεχόμενο να ορίσετε την οδηγία `object-src` σε `'none'`."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Λείπει η οδηγία `script-src`. Η απουσία της οδηγίας μπορεί να επιτρέψει την εκτέλεση μη ασφαλών σεναρίων."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Μή<PERSON><PERSON><PERSON> ξεχάσατε το ερωτηματικό; Το {keyword} φαίνεται ότι είναι οδηγία, όχι λέξη-κλειδί."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Οι αριθμοί nonce πρέπει να χρησιμοποιούν το σύνολο χαρακτήρων base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Οι αριθμοί nonce πρέπει να αποτελούνται από τουλάχιστον 8 χαρακτήρες."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Αποφύγετε τη χρήση απλών σχημάτων URL ({keyword}) σε αυτή την οδηγία. Τα απλά σχήματα URL επιτρέπουν την άντληση σεναρίων από μη ασφαλή τομέα."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Αποφύγετε τη χρήση απλών χαρακτήρων μπαλαντέρ ({keyword}) σε αυτή την οδηγία. Οι απλοί χαρακτήρες μπαλαντέρ επιτρέπουν την άντληση σεναρίων από μη ασφαλή τομέα."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Ο προορισμός αναφοράς διαμορφώνεται μόνο μέσω της οδηγίας report-to. Αυτή η οδηγία υποστηρίζεται μόνο σε προγράμματα περιήγησης που βασίζονται στο Chromium και για αυτόν τον λόγο συνιστάται επίσης η χρήση μιας οδηγίας `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Δεν υπάρχει CSP που διαμορφώνει προορισμό αναφοράς. Αυτό δυσχεραίνει τη διατήρηση του CSP για μεγάλα διαστήματα και την παρακολούθηση για τυχόν προβλήματα."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Οι λίστες επιτρεπομένων κεντρικού υπολογιστή μπορούν συχνά να παρακαμφθούν. Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε αριθμούς nonce ή κατακερματισμούς CSP, μαζί με την οδηγία `'strict-dynamic'`, εάν αυτό είναι απαραίτητο."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Άγνωστη οδηγία CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "Το {keyword} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ότι είναι μη έγκυρη λέξη-κλειδί."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Η οδηγία `'unsafe-inline'` επιτρέπει την εκτέλεση μη ασφαλών σεναρίων στη σελίδα και δεικτών χειρισμού συμβάντων. Εξετάστε το ενδεχόμενο χρήσης αριθμών nonce ή κατακερματισμών CSP για να επιτρέπονται μεμονωμένα σενάρια."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Εξετάστε το ενδεχόμενο να προσθέσετε την οδηγία `'unsafe-inline'` (παραβλέπεται από προγράμματα περιήγησης που υποστηρίζουν αριθμούς nonce/κατακερματισμούς) για συμβατότητα με παλαιότερες εκδόσεις προγραμμάτων περιήγησης."}, "core/lib/deprecation-description.js | feature": {"message": "Ελέγξτε τη σελίδα κατάστασης λειτουργίας για περισσότερες λεπτομέρειες."}, "core/lib/deprecation-description.js | milestone": {"message": "Αυτή η αλλαγή θα τεθεί σε ισχύ με το ορόσημο {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Χρησιμοποιήθηκε λειτουργία που έχει καταργηθεί"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Η εξουσιοδότηση θα καλυφθεί από τον χαρακτήρα μπαλαντέρ (*) στον χειρισμό `Access-Control-Allow-Headers` CORS."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Το χαρακτηριστικ<PERSON> `disableRemotePlayback` θα πρέπει να χρησιμοποιείται για την απενεργοποίηση της προεπιλεγμένης ενσωμάτωσης μετάδοσης αντί να χρησιμοποιείται ο επιλογέας `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Τα αιτήματα πόρων των οποίων τα URL περιείχαν καταργημένους χαρακτήρες κενού διαστήματος `(n|r|t)` και χαρακτήρες \"λιγότερο από\" (`<`) αποκλείονται. Καταργήστε τις νέες γραμμές και κωδικοποιήστε τους χαρακτήρες \"λιγότερο από\" από μέρη, όπως οι τιμές χαρακτηριστικών στοιχείων, προκειμένου να γίνει φόρτωση αυτών των πόρων."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Το `chrome.loadTimes()` έχει καταργηθεί. Αντ' αυτού, χρησιμοποιήστε το τυποποιημένο API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Το `chrome.loadTimes()` έχει καταργηθεί. Αντ' αυτού χρησιμοποιήστε το τυποποιημένο API: <PERSON>t <PERSON>."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Το `chrome.loadTimes()` έχει καταργηθεί. Αντ' αυτού, χρησιμοποιήστε το τυποποιημένο API: `nextHopProtocol` στο Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Τα cookie που περιέχουν χαρακτήρα `(0|r|n)` θα απορριφθούν αντί να περικοπούν."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Η χαλάρωση της πολιτικής κοινής προέλευσης με ρύθμιση του `document.domain` έχει καταργηθεί και θα απενεργοποιηθεί από προεπιλογή. Αυτή η προειδοποίηση κατάργησης αφορά μια πρόσβαση διασταυρούμενων προελεύσεων που ενεργοποιήθηκε με τη ρύθμιση του `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Η ενεργοποίηση της συνάρτησης window.alerts απ<PERSON> iframe διασταυρούμενων προελεύσεων έχει καταργηθεί και θα αποσυρθεί στο μέλλον."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Η ενεργοποίηση του window.confirm από iframe διασταυρούμενων προελεύσεων έχει καταργηθεί και θα αποσυρθεί στο μέλλον."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Τα συμβάντα σίγασης D<PERSON>, συμπεριλαμβανομένων των `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` και `DOMCharacterDataModified` έχουν καταργηθεί (https://w3c.github.io/uievents/#legacy-event-types) και θα αποσυρθούν. Εναλλακτικά, χρησιμοποιήστε το `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Η υποστήριξη για το στοιχείο data: URLs in SVG <use> έχει καταργηθεί και θα αποσυρθεί στο μέλλον."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Η χαλάρωση της πολιτικής κοινής προέλευσης με ρύθμιση του `document.domain` έχει καταργηθεί και θα απενεργοποιηθεί από προεπιλογή. Για να συνεχίσετε να χρησιμοποιείτε αυτήν τη λειτουργία, εξαιρεθείτε από τα συμπλέγματα παραγόντων με κλειδιά προέλευσης, στέλνοντας μια κεφαλίδα `Origin-Agent-Cluster: ?0` μαζί με την απόκριση HTTP για το έγγραφο και τα πλαίσια. Ανατρέξτε στη σελίδα https://developer.chrome.com/blog/immutable-document-domain/ για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Η κεφαλίδα `Expect-CT` καταργήθηκε και θα αποσυρθεί. Το Chrome απαιτεί Διαφάνεια πιστοποιητικών για όλα τα πιστοποιητικά που θεωρούνται αξιόπιστα δημοσίως και έχουν εκδοθεί μετά τις 30 Απριλίου 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Τα `getCurrentPosition()` και `watchPosition()` δεν λειτουργούν πλέον σε μη ασφαλείς προελεύσεις. Για να χρησιμοποιήσετε αυτήν τη λειτουργία, θα πρέπει να εξετάσετε το ενδεχόμενο να αλλάξετε την εφαρμογή σας σε ασφαλή προέλευση, όπως HTTPS. Ανατρέξτε στη διεύθυνση https://goo.gle/chrome-insecure-origins για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Τα `getCurrentPosition()` και `watchPosition()` καταργήθηκαν σε μη ασφαλείς προελεύσεις. Για να χρησιμοποιήσετε αυτήν τη λειτουργία, θα πρέπει να εξετάσετε το ενδεχόμενο να αλλάξετε την εφαρμογή σας σε ασφαλή προέλευση, όπως HTTPS. Ανατρέξτε στη διεύθυνση https://goo.gle/chrome-insecure-origins για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "Το `getUserMedia()` δεν λειτουργεί πλέον σε μη ασφαλείς προελεύσεις. Για να χρησιμοποιήσετε αυτήν τη λειτουργία, θα πρέπει να εξετάσετε το ενδεχόμενο να αλλάξετε την εφαρμογή σας σε ασφαλή προέλευση, όπως HTTPS. Ανατρέξτε στη διεύθυνση https://goo.gle/chrome-insecure-origins για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Το `RTCPeerConnectionIceErrorEvent.hostCandidate` καταργήθηκε. Αντ' αυτού, χρησιμοποιήστε το `RTCPeerConnectionIceErrorEvent.address` ή το `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Η προέλευση εμπόρου και αυθαίρετα δεδομένα από το συμβάν service worker `canmakepayment` έχουν αποσυρθεί και θα καταργηθούν: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Ο ιστότοπος ζήτησε έναν δευτερεύοντα πόρο από ένα δίκτυο στο οποίο θα μπορούσε να αποκτήσει πρόσβαση μόνο λόγω της προνομιακής θέσης δικτύου των χρηστών του. Αυτά τα αιτήματα εκθέτουν τις μη δημόσιες συσκευές και τους μη δημόσιους διακομιστές στο διαδίκτυο, αυξάνοντας τον κίνδυνο επίθεσης τύπου cross-site request forgery (CSRF) ή/και διαρροής πληροφοριών. Για να μειωθούν αυτοί οι κίνδυνοι, το Chrome καταργεί τα αιτήματα σε μη δημόσιους δευτερεύοντες πόρους, <PERSON><PERSON><PERSON><PERSON> εκκινούνται από μη ασφαλή περιβάλλοντα και θα αρχίσει να τα αποκλείει."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Το πεδίο `dailyUpdateUrl` του `InterestGroups` που μεταβιβάζεται στο `joinAdInterestGroup()` μετονομάστηκε σε `updateUrl`, ώστε να αντικατοπτρίζει με μεγαλύτερη ακρίβεια τη συμπεριφορά του."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Δεν είναι δυνατή η φόρτωση του CSS από τα URL `file:`, εκτός εάν καταλήγουν σε επέκταση αρχείου `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Η χρήση του `SourceBuffer.abort()` για ακύρωση της ασύγχρονης κατάργησης εύρους του στοιχείου `remove()` έχει καταργηθεί λόγω αλλαγής προδιαγραφών. Η υποστήριξη θα καταργηθεί μελλοντικά. Εναλλακτικ<PERSON>, θα πρέπει να χρησιμοποιήσετε τη λειτουργία αναμονής για το συμβάν `updateend`. Το `abort()` προορίζεται μόνο για την ακύρωση της ασύγχρονης προσάρτησης μέσου ή την επαναφορά κατάστασης συντακτικού αναλυτή."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Η ρύθμιση του `MediaSource.duration` κάτω από την υψηλότερη χρονική σήμανση παρουσίασης τυχόν κωδικοποιημένων πλαισίων στην προσωρινή μνήμη έχει καταργηθεί λόγω αλλαγής προδιαγραφών. Η υποστήριξη για την έμμεση κατάργηση των περικομμένων μέσων στην προσωρινή μνήμη θα καταργηθεί στο μέλλον. Αντ' αυτού θα πρέπει να εκτελέσετε ρητή `remove(newDuration, oldDuration)` σε όλα τα `sourceBuffers`, όπου `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Το MIDI ιστού θα ζητήσει άδεια για χρήση ακόμα και αν το sysex δεν είναι καθορισμένο στο `MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Το παλαιότερο, μη τυποποιημένο χαρακτηριστικ<PERSON> `shadowroot` έχει καταργηθεί και *δεν θα λειτουργεί πλέον* στο M119. Εναλλακτικά, χρησιμοποιήστε το νέο τυποποιημένο χαρακτηριστικό `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Το Notification API δεν μπορεί πλέον να χρησιμοποιείται από μη ασφαλείς προελεύσεις. Θα πρέπει να εξετάσετε το ενδεχόμενο να αλλάξετε την εφαρμογή σας σε ασφαλή προέλευση, όπως HTTPS. Ανατρέξτε στη διεύθυνση https://goo.gle/chrome-insecure-origins για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Δεν επιτρέπεται πλέον να ζητείται άδεια για το Notification API από ένα iframe διασταυρούμενων προελεύσεων. Θα πρέπει να εξετάσετε το ενδεχόμενο να ζητήσετε άδεια από ένα πλαίσιο ανώτερου επιπέδου ή, εναλλακτικά, να ανοίξετε ένα νέο παράθυρο."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Η επιλογή `imageOrientation: 'none'` στο createImageBitmap έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το createImageBitmap με την επιλογή \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Ο συνεργάτης σας κάνει διαπραγμάτευση για μια παρωχημένη έκδοση (D)TLS. Επικοινωνήστε με τον συνεργάτη σας για την επίλυση αυτού του ζητήματος."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Ο καθορισμός του στοιχείου `overflow: visible` σε ετικέτες img, video και canvas μπορεί να έχει ως αποτέλεσμα τη δημιουργία οπτικού περιεχομένου εκτός των ορίων του στοιχείου. Ανατρέξτε στη διεύθυνση https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "Το `paymentManager.instruments` καταργήθηκε. Χρησιμοποιήστε αντ' αυτού την έγκαιρη εγκατάσταση για τους δείκτες χειρισμού πληρωμών."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Η κλήση `PaymentRequest` παρέβλεψε την οδηγία `connect-src` της Πολιτικής ασφάλειας περιεχομένου (CSP). Αυτή η παράκαμψη καταργήθηκε. Προσθέστε το αναγνωριστικό τρόπου πληρωμής από το `PaymentRequest` API (στο πεδίο `supportedMethods`) στην οδηγία `connect-src` της CSP."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Το `StorageType.persistent` έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το τυποποιημένο `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Το `<source src>` με γονικό στοιχείο `<picture>` δεν είναι έγκυρο και συνεπώς παραβλέφθηκε. Εναλλακτικά, χρησιμοποιήστε το `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Το webkitCancelAnimationFrame εξαρτάται από τον προμηθευτή. Εναλλακτικά, χρησιμοποιήστε το βασικό cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Το webkitRequestAnimationFrame εξαρτάται από τον προμηθευτή. Εναλλακτικά, χρησιμοποιήστε το βασικό requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Το HTMLVideoElement.webkitDisplayingFullscreen έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Το HTMLVideoElement.webkitEnterFullScreen() έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Το HTMLVideoElement.webkitEnterFullscreen() έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Το HTMLVideoElement.webkitExitFullScreen() έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Το HTMLVideoElement.webkitExitFullscreen() έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Το HTMLVideoElement.webkitSupportsFullscreen έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Το API `chrome.privacy.websites.privacySandboxEnabled` καταργείται αλλά θα παραμείνει ενεργό για συμβατότητα με παλιότερη έκδοση, έως την κυκλοφορία του M113. Εναλλακτικ<PERSON>, χρησιμοποιήστε τα `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` και `chrome.privacy.websites.adMeasurementEnabled`. Ανατρέξτε στη διεύθυνση https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ο περιορισμός `DtlsSrtpKeyAgreement` καταργήθηκε. Έχετε καθορίσει μια τιμή `false` για αυτόν τον περιορισμό, η οποία ερμηνεύεται ως προσπάθεια χρήσης της μεθόδου `SDES key negotiation` που καταργήθηκε. Αυτή η λειτουργία καταργήθηκε, χρησιμοποιήστε εναλλακτικά μια υπηρεσία που υποστηρίζει το `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ο περιορισμός `DtlsSrtpKeyAgreement` καταργήθηκε. Έχετε καθορίσει μια τιμή `true` για αυτόν τον περιορισμό, η οποία δεν είχε κάποια επίδραση, αλλά μπορείτε να καταργήσετε αυτόν τον περιορισμό για λόγους οργάνωσης."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Η μέθοδος getStats() που βασίζεται σε επανάκληση έχει καταργηθεί και θα αποσυρθεί. Εναλ<PERSON>ακτικ<PERSON>, χρησιμοποιήστε τη μέθοδο getStats() που συμμορφώνεται με τις προδιαγραφές."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Το Range.expand() έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε το Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Τα αιτήματα δευτερευόντων πόρων των οποίων τα URL περιέχουν ενσωματωμένα διαπιστευτήρια (π.χ. `**********************/`) είναι αποκλεισμένα."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Η επιλογή `rtcpMuxPolicy` καταργήθηκε και θα αποσυρθεί."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "Για το `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` θα απαιτηθεί απομόνωση διασταυρούμενων προελεύσεων. Ανατρέξτε στη διεύθυνση https://developer.chrome.com/blog/enabling-shared-array-buffer/ για περισσότερες λεπτομέρειες."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Το `speechSynthesis.speak()` χωρ<PERSON>ς ενεργοποίηση χρήστη καταργήθηκε και θα αποσυρθεί."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Οι επεκτάσεις θα πρέπει να συμμετέχουν στην απομόνωση διασταυρούμενων προελεύσεων, για να συνεχιστεί η χρήση του `SharedArrayBuffer`. Ανατρέξτε στη διεύθυνση https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Το SQL ιστού έχει καταργηθεί. Χρησιμοποιήστε το SQLite WebAssembly ή την καταλογοποιημένη βάση δεδομένων."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Το περιγρα<PERSON>ικ<PERSON> στοιχείο άδειας `window-placement` έχει καταργηθεί. Εναλλακτικά, χρησιμοποιήστε την πολιτική `window-management`. Για περισσότερη βοήθεια, ανατρέξτε στη διεύθυνση https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Η πολιτική άδειας `window-placement` έχει καταργηθεί. Εναλλακτικ<PERSON>, χρησιμοποιήστε την πολιτική `window-management`. Για περισσότερη βοήθεια, ανατρέξτε στη διεύθυνση https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "Το UTF-16 δεν υποστηρίζεται από το json απόκρισης στο `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Το συγχρονισμένο `XMLHttpRequest` στο κύριο νήμα έχει καταργηθεί λόγω των επιζήμιων συνεπειών του στην εμπειρία τελικού χρήστη. Για περισσότερη βοήθεια, ανατρέξτε στη διεύθυνση https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Το `supportsSession()` καταργήθηκε. Χρησιμοποιήστε το `isSessionSupported()` και ελέγξτε εναλλακτικά την τιμή boolean που έχει επιλυθεί."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> αποκλεισμού κύριου νήματος"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL κρυφής μνήμης"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Περιγραφή"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Διάρκεια"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Στοιχείο"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Στοιχεία που απέτυχαν"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Τοποθεσία"}, "core/lib/i18n/i18n.js | columnName": {"message": "Όνομα"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Υπέρβαση προϋπολογισμού"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Αιτήματα"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πόρου"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> πόρου"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Μέγεθος"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Πηγή"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Ώρα έναρξης"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON>ρ<PERSON><PERSON><PERSON> χρήσης"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μεταφορ<PERSON>ς"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Δυνητικές εξοικονομήσεις"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Δυνητικές εξοικονομήσεις"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Δυνητική εξοικονόμηση {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Βρέθηκε 1 στοιχείο}other{Βρέθηκαν # στοιχεία}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Δυνητική εξοικονόμηση {wastedMs, number, milliseconds} χλστ.δ."}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Έγγραφο"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Πρώτη χρήσιμη μορφή"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Γραμματοσειρά"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Εικόνα"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Υψηλή"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Χαμηλή"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Μέτρια"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Μέγ. δυνα<PERSON><PERSON> καθυστέρηση πρώτης εισόδου"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Μέσα"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} χλστ.δ."}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Άλλο"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Άλλοι πόροι"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Σενάριο"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} δ."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON>λ<PERSON><PERSON> στιλ"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Τρίτο μέρος"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Σύνολο"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON><PERSON> κάποιο πρόβλημα με την εγγραφή του ίχνους κατά τη φόρτωση της σελίδας σας. Εκτελέστε ξανά το Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Λήξη χρονικού ορίου κατά την αναμονή για αρχική σύνδεση του πρωτοκόλλου εργαλείου εντοπισμού σφαλμάτων."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Το Chrome δεν συγκέντρωσε στιγμιότυπα οθόνης κατά τη φόρτωση της σελίδας. Βεβαιωθείτε ότι υπάρχει ορατό περιεχόμενο στη σελίδα και έπειτα δοκιμάστε να εκτελέσετε ξανά το Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Οι διακομιστές DNS δεν ήταν δυνατό να επιλύσουν τον παρεχόμενο τομέα."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON><PERSON> κάποιο σφάλμα στη λειτουργία συγκέντρωσης για τον απαιτούμενο πόρο {artifactName}: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Προέκυψε εσωτερικό σφάλμα Chrome. Επανεκκινήστε το Chrome και δοκιμάστε να εκτελέσετε ξανά το Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Δεν εκτελέστηκε η λειτουργία συγκέντρωσης για τον απαιτούμενο πόρο {artifactName}."}, "core/lib/lh-error.js | noFcp": {"message": "Η σελίδα δεν σχεδιάζει κανένα περιεχόμενο. Βεβαιωθείτε ότι διατηρείτε το παράθυρο του προγράμματος περιήγησης στο προσκήνιο κατά τη φόρτωση και προσπαθήστε ξανά. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Η σελίδα δεν πρόβαλε περιεχόμενο που πληροί τις προϋποθέσεις ως Μεγαλύτερη σχεδίαση με περιεχόμενο (LCP). Βεβαιωθείτε ότι η σελίδα έχει έγκυρο στοιχείο LCP και, στη συνέχεια, δοκιμάστε ξανά. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Η σελίδα που καταχωρίσατε δεν είναι HTML (προβάλλεται ως τύπος MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Αυτή η έκδοση του Chrome είναι πολύ παλιά για να υποστηρίζει τη λειτουργία {featureName}. Χρησιμοποιήστε μια νεότερη έκδοση για να δείτε τα πλήρη αποτελέσματα."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Το Lighthouse δεν ήταν δυνατό να φορτώσει με αξιοπιστία τη σελίδα που ζητήσατε. Βεβαιωθείτε ότι ελέγχετε το σωστό URL και ότι ο διακομιστής ανταποκρίνεται σωστά σε όλα τα αιτήματα."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Το Lighthouse δεν ήταν δυνατό να φορτώσει με αξιοπιστία το URL που ζητήσατε, επειδή η σελίδα σταμάτησε να ανταποκρίνεται."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Το URL που παρείχατε δεν έχει ένα έγκυρο πιστοποιητικό ασφάλειας. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Το Chrome εμπόδισε την εμφάνιση μιας σελίδας με παρενθετική διαφήμιση. Βεβαιωθείτε ότι ελέγχετε το σωστό URL και ότι ο διακομιστής αποκρίνεται σωστά σε όλα τα αιτήματα."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Το Lighthouse δεν μπόρεσε να φορτώσει με αξιοπιστία τη σελίδα που ζητήσατε. Βεβαιωθείτε ότι ελέγχετε το σωστό URL και ότι ο διακομιστής αποκρίνεται σωστά σε όλα τα αιτήματα. (Λεπτομέρειες: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Το Lighthouse δεν μπόρεσε να φορτώσει με αξιοπιστία τη σελίδα που ζητήσατε. Βεβαιωθείτε ότι ελέγχετε το σωστό URL και ότι ο διακομιστής αποκρίνεται σωστά σε όλα τα αιτήματα. (Κω<PERSON><PERSON><PERSON><PERSON>ς κατάστασης: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Χρειάστηκε υπερβολικά πολύς χρόνος για τη φόρτωση της σελίδας σας. Συμβουλευτείτε τις ευκαιρίες στην αναφορά, για να μειώσετε τον χρόνο φόρτωσης της σελίδας, και έπειτα δοκιμάστε να εκτελέσετε ξανά το Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Η αναμονή για απόκριση του πρωτοκόλλου DevTools έχει υπερβεί τον μέγιστο επιτρεπόμενο χρόνο. (Μέθοδος: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Η λήψη περιεχομένου πόρων έχει υπερβεί τον εκχωρημένο χρόνο"}, "core/lib/lh-error.js | urlInvalid": {"message": "Το URL που παρείχατε φαίνεται ότι δεν είναι έγκυρο."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Το Lighthouse δεν μπόρεσε να φορτώσει με αξιοπιστία τη σελίδα που ζητήσατε. Βεβαιωθείτε ότι ελέγχετε το σωστό URL και ότι ο διακομιστής αποκρίνεται σωστά σε όλα τα αιτήματα. (Κω<PERSON><PERSON><PERSON><PERSON>ς κατάστασης: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Ο τύπος MIME της σελίδας είναι XHTML: Το Lighthouse δεν υποστηρίζει ρητά αυτόν τον τύπο εγγράφου"}, "core/user-flow.js | defaultFlowName": {"message": "Ροή χρήστη ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Αναφορά πλοήγησης ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Αναφορά σύνοψης ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Αναφορά χρονικού διαστήματος ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Όλες οι αναφορές"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Κατηγορίες"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Προσβασιμότητα"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Βέλτιστες πρακτικές"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Απόδοση"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Προηγμένη εφαρμογή ιστού"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Υπολογιστής"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Κατανόηση της αναφοράς ροής του Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Κατανόηση των ροών"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Χρησιμοποιήστε τις αναφορές των αναφορών πλοήγησης για να..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Χρησιμοποιήστε τις αναφορές Snapshot για να..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Χρησιμοποιήστε τις αναφορές χρονικού διαστήματος για να..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Λάβετε μια βαθμολογία απόδοσης του Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Υπολογίσετε τις μετρήσεις απόδοσης φόρτωσης σελίδας, όπω<PERSON>εγαλύτερη σχεδίαση με περιεχόμενο και Ευρετήριο ταχύτητας."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Αξιολογήσετε τις δυνατότητες προηγμένων εφαρμογών ιστού."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Εντοπίσετε ζητήματα προσβασιμότητας σε εφαρμογές μίας σελίδας ή σύνθετες φόρμες."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Αξιολογήσετε τις βέλτιστες πρακτικές των μενού και στοιχείων διεπαφής χρήστη που κρύβονται πίσω από αλληλεπιδράσεις."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Μετρήσετε τις αλλαγές διάταξης και τον χρόνο εκτέλεσης JavaScript σε μια σειρά αλληλεπιδράσεων."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Ανακαλύψετε ευκαιρίες απόδοσης για να βελτιώσετε την εμπειρία για σελίδες μεγάλης διάρκειας και εφαρμογές μίας σελίδας."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Υψηλότερος αντίκτυπος"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} πληροφοριακός έλεγχος}other{{numInformative} πληροφοριακοί έλεγχοι}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Κινητό"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Φόρτωση σελίδας"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Οι αναφορές πλοήγησης αναλύουν τη φόρτωση μίας σελίδας, ακριβώς όπως οι πρωτότυπες αναφορές του Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Αναφορά πλοήγησης"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} αναφορά πλοήγησης}other{{numNavigation} αναφορές πλοήγησης}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} έλεγχος που μπορεί να ολοκληρωθεί}other{{numPassableAudits} έλεγχοι που μπορούν να ολοκληρωθούν}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} έλεγχος ολοκληρώθηκε}other{{numPassed} έλεγχοι ολοκληρώθηκαν}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Μέτρια"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Σφάλμα"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Χαμηλή"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Καλή"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Αποθήκευση"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Καταγεγραμμένη κατάσταση σελίδας"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Οι αναφορές Snapshot αναλύουν τη σελίδα σε μια συγκεκριμένη κατάσταση, συνήθως μετά από αλληλεπιδράσεις χρηστών."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Αναφορά σύνοψης"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} αναφορά σύνοψης}other{{numSnapshot} αναφορές σύνοψης}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Σύνοψη"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Αλληλεπιδρ<PERSON><PERSON><PERSON>ις χρήστη"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Οι αναφορές χρονικού διαστήματος αναλύουν μια τυχαία χρονική περίοδο, η οποία συνήθως περιέχει αλληλεπιδράσεις χρηστών."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Αναφορά χρονικού διαστήματος"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} αναφορά χρονικού διαστήματος}other{{numTimespan} αναφορές χρονικού διαστήματος}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Αναφορά ροής χρήστη Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Για περιεχόμενο κινούμενης εικόνας, χρησιμοποιήστε το [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) για να ελαχιστοποιήσετε τη χρήση CPU όταν το περιεχόμενο είναι εκτός οθόνης."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Μπορείτε να εμφανίσετε όλα τα στοιχεία [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) σε μορφές WebP, ορίζοντας μια κατάλληλη εναλλακτική για άλλα προγράμματα περιήγησης. [Μάθετε περισσότερα](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Βεβαιωθείτε ότι χρησιμοποιείτε το στοιχείο [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) για εικόνες για αυτόματη αργή φόρτωση. [Μάθετε περισσότερα](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Χρησιμοποιήστε εργαλεία όπως το [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) για να [αποδώσετε διατάξεις AMP στον διακομιστή](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Ανατρέξτε στην [τεκμηρίωση AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) για να βεβαιωθείτε ότι υποστηρίζονται όλα τα στιλ."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Το στοιχείο [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) υποστηρίζει το χαρακτηριστικό [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) για να ορίσει ποια στοιχεία εικόνας θα χρησιμοποιούνται, βάσει μεγέθους οθόνης. [Μάθετε περισσότερα](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Μπορείτε να χρησιμοποιήσετε εικονική κύλιση με το Component Dev <PERSON> (CDK), ό<PERSON><PERSON><PERSON> αποδίδονται πολύ μεγάλες λίστες. [Μάθετε περισσότερα](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Εφαρμόστε [διαχωρισμ<PERSON> κώδικα σε επίπεδο δρομολόγησης](https://web.dev/route-level-code-splitting-in-angular/) για να ελαχιστοποιήσετε το μέγεθος των πακέτων JavaScript. Εξετάστε επίσης το ενδεχόμενο να πραγματοποιήσετε πρόωρη αποθήκευση στοιχείων στην κρυφή μνήμη με το[Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Εάν χρησιμοποιείτε το Angular CLI, βεβαιωθείτε ότι οι εκδόσεις δημιουργούνται σε λειτουργία παραγωγής. [Μάθετε περισσότερα](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Εάν χρησιμοποιείτε το Angular CLI, συμπεριλάβετε χάρτες πηγής στην έκδοση παραγωγής σας, για να ελέγξετε τα πακέτα σας. [Μάθετε περισσότερα](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Προφορτώστε τις διαδρομές εκ των προτέρων, για να επιταχύνετε την πλοήγηση. [Μάθετε περισσότερα](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Μπορείτε να χρησιμοποιήσετε το βοηθητικό πρόγραμμα `BreakpointObserver` στο Component Dev Kit (CDK) για να διαχειρίζεστε τα σημεία διακοπής εικόνας. [Μάθετε περισσότερα](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Εξετάστε το ενδεχόμενο να ανεβάσετε το GIF σε μια υπηρεσία η οποία θα το διαθέσει για ενσωμάτωση ως βίντεο HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Καθορίστε τη `@font-display` κατά τον προσδιορισμό προσαρμοσμένων γραμματοσειρών στο θέμα σας."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Εξετάστε το ενδεχόμενο διαμόρφωσης των [μορφών εικόνας WebP με ένα στιλ Μετατροπή εικόνας](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) στον ιστότοπό σας."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Εγκαταστήστε [μια λειτουργική μονάδα Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) η οποία μπορεί να εκτελέσει αργή φόρτωση εικόνων. Αυτού του είδους οι λειτουργικές μονάδες παρέχουν τη δυνατότητα αναβολής φόρτωσης των εικόνων εκτός οθόνης, για τη βελτίωση της απόδοσης."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια λειτουργική μονάδα για ενσωμάτωση σημαντικών στοιχείων CSS και JavaScript, και χρησιμοποιήστε το χαρακτηριστικό αναβολής για μη σημαντικά στοιχεία CSS ή JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Τα θέματα, οι ενότητες και οι προδιαγραφές διακομιστή συμβάλλουν στον χρόνο απόκρισης του διακομιστή. Αν θέλετε, μπορείτε να ψάξετε για ένα περισσότερο βελτιστοποιημένο θέμα, να επιλέξετε προσεκτικά μια ενότητα βελτιστοποίησης ή/και να αναβαθμίσετε τον διακομιστή σας. Οι διακομιστές φιλοξενίας σας θα πρέπει να χρησιμοποιούν την προσωρινή αποθήκευση κώδικα λειτουργίας (opcode) PHP, την προσωρινή αποθήκευση μνήμης για τη μείωση των χρόνων ερωτημάτων βάσης δεδομένων, όπως στα Redis και Memcached, καθώς και βελτιστοποιημένη λογική εφαρμογής για την πιο γρήγορη προετοιμασία σελίδων."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Εξετάστε το ενδεχόμενο χρήσης [Στιλ αποκριτικών εικόνων](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) για να μειώσετε το μέγεθος των εικόνων που φορτώνονται στη σελίδα σας. Εάν χρησιμοποιείτε τις Προβολές για την εμφάνιση πολλαπλών στοιχείων περιεχομένου σε μια σελίδα, εξετάστε το ενδεχόμενο ενσωμάτωσης σελιδοποίησης προκειμένου να περιορίσετε τον αριθμό των στοιχειών περιεχομένου σε μια συγκεκριμένη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Βεβαιωθείτε ότι έχετε ενεργοποιήσει τη Συγκέντρωση αρχείων CSS στη σελίδα Διαχείριση » Διαμόρφωση » Ανάπτυξη.  Βεβαιωθείτε ότι ο ιστότοπός σας στο Drupal εκτελείται τουλάχιστον σε έκδοση Drupal 10.1 για βελτιωμένη υποστήριξη συγκέντρωσης στοιχείων."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Βεβαιωθείτε ότι έχετε ενεργοποιήσει τη Συγκέντρωση αρχείων JavaScript στη σελίδα Διαχείριση » Διαμόρφωση » Ανάπτυξη.  Βεβαιωθείτε ότι ο ιστότοπός σας στο Drupal εκτελείται τουλάχιστον σε έκδοση Drupal 10.1 για βελτιωμένη υποστήριξη συγκέντρωσης στοιχείων."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Εξετάστε το ενδεχόμενο να καταργήσετε τους κανόνες CSS που δεν χρησιμοποιούνται και να προσαρτήσετε μόνο τις αναγκαίες βιβλιοθήκες Drupal στη σχετική σελίδα ή στο σχετικό στοιχείο σε μια σελίδα. Για λεπτομέρειες, ανατρέξτε στον [σύνδεσμο τεκμηρίωσης Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Για να προσδιορίσετε τις προσαρτημένες βιβλιοθήκες που προσθέτουν περιττό κώδικα CSS, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Μπορείτε να προσδιορίσετε το θέμα/την ενότητα που ευθύνεται μέσω του URL του φύλλου στιλ όταν η συγκέντρωση CSS είναι απενεργοποιημένη στον ιστότοπό σας στο Drupal. Αναζητήστε θέματα/ενότητες που διαθέτουν πολλά φύλλα στιλ στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Ένα θέμα/μια ενότητα θα πρέπει να τοποθετεί στην ουρά ένα φύλλο στιλ, μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Εξετάστε το ενδεχόμενο να καταργήσετε τα στοιχεία JavaScript που δεν χρησιμοποιούνται και να προσαρτήσετε μόνο τις αναγκαίες βιβλιοθήκες Drupal στη σχετική σελίδα ή στο σχετικό στοιχείο σε μια σελίδα. Για λεπτομέρειες, ανατρέξτε στον [σύνδεσμο τεκμηρίωσης Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Για να προσδιορίσετε τις προσαρτημένες βιβλιοθήκες που προσθέτουν περιττό κώδικα JavaScript, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Μπορείτε να προσδιορίσετε το θέμα/τη λειτουργική μονάδα που ευθύνεται μέσω του URL του σεναρίου όταν η συγκέντρωση JavaScript είναι απενεργοποιημένη στον ιστότοπό σας στο Drupal. Αναζητήστε θέματα/λειτουργικές μονάδες που διαθέτουν πολλά σενάρια στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Ένα θέμα/μια λειτουργική μονάδα θα πρέπει να τοποθετεί στην ουρά ένα σενάριο, μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Ορίστε τη Μέγιστη ηλικία κρυφής μνήμης διακομιστή μεσολάβησης προγράμματος περιήγησης στη σελίδα Διαχείριση » Διαμόρφωση » Ανάπτυξη. Διαβάστε σχετικά με την [Κρυφή μνήμη Drupal και βελτιστοποίηση για μεγαλύτερη απόδοση](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Εξετάστε το ενδεχόμενο χρήσης [μιας ενότητας](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) η οποία βελτιστοποιεί αυτόματα και μειώνει το μέγεθος των εικόνων που μεταφορτώνονται μέσω του ιστοτόπου, διατηρώντας την ποιότητα. Επιπλέον, βεβαιωθείτε ότι χρησιμοποιείτε τα εγγενή [Στιλ αποκριτικών εικόνων](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) που παρέχονται από το Drupal (διατίθενται στο Drupal 8 και σε νεότερες εκδόσεις) για όλες τις εικόνες που αποδίδονται στον ιστότοπο."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Υποδείξεις πόρων πρόωρης σύνδεσης ή πρόωρης λήψης dns μπορούν να προστεθούν μέσω της εγκατάστασης και της διαμόρφωσης [μιας ενότητας](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) η οποία παρέχει λειτουργίες για υποδείξεις πόρων παραγόντων χρήστη."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Βεβαιωθείτε ότι χρησιμοποιείτε τα εγγενή [Στιλ αποκριτικών εικόνων](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) που παρέχονται από το Drupal (διατίθενται στο Drupal 8 και σε νεότερες εκδόσεις). Χρησιμοποιήστε Στιλ αποκριτικών εικόνων κατά την απόδοση πεδίων εικόνων μέσω λειτουργιών προβολής, προβολών ή εικόνων που μεταφορτώθηκαν μέσω του εργαλείου επεξεργασίας WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Χρησιμοποιήστε το [E<PERSON> Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Optimize Fonts` για την αυτόματη αξιοποίηση της λειτουργίας `font-display` CSS, προκειμένου να είστε σίγουροι ότι το κείμενο θα είναι ορατό στους χρήστες όσο φορτώνουν οι γραμματοσειρές ιστοτόπου."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Next-Gen Formats` για τη μετατροπή εικόνων σε WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Χρησιμοποιήστε το [E<PERSON> Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `<PERSON><PERSON>ad Images` για την αναβολή της φόρτωσης των εικόνων εκτός οθόνης μέχρι τη στιγμή που η φόρτωσή τους θα είναι απαραίτητη."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Critical CSS` και το `<PERSON><PERSON><PERSON>` για την αναβολή JS/CSS δευτερεύουσας σημασίας."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Χρησιμοποιήστε το [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) για να προσθέσετε στην κρυφή μνήμη το περιεχόμενό σας από το παγκόσμιο δίκτυο, βελτιώνοντας τον χρόνο μέχρι το πρώτο byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Minify CSS` για την αυτόματη ελαχιστοποίηση του CSS, προκειμένου να μειωθούν τα μεγέθη φορτίων δικτύου."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Minify Javascript` για την αυτόματη ελαχιστοποίηση του JS, προκειμένου να μειωθούν τα μεγέθη φορτίων δικτύου."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Remove Unused CSS` για να συμβάλλετε στην επίλυση αυτού του προβλήματος. Θα προσδιορίσει τις κατηγορίες CSS που χρησιμοποιούνται πραγματικά σε κάθε σελίδα του ιστοτόπου σας και θα καταργήσει άλλες, προκειμένου να παραμείνει μικρό το μέγεθος του αρχείου."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Efficient Static Cache Policy` για να ορίσετε προτεινόμενες τιμές στην κεφαλίδα αποθήκευσης στην κρυφή μνήμη για στατικά στοιχεία."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Next-Gen Formats` για τη μετατροπή εικόνων σε WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Χρησιμοποιήστε το [E<PERSON> Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Pre-Connect Origins` για την αυτόματη προσθήκη υποδείξεων πόρων `preconnect`, προκειμένου να δημιουργηθούν πρώιμες συνδέσεις σε σημαντικές προελεύσεις τρίτου μέρους."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Χρησιμοποιήστε το [E<PERSON> Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Preload Fonts` και το `Preload Background Images` για να προσθέσετε συνδέσμους `preload`, προκειμένου να δοθεί προτεραιότητα στη λήψη πόρων που τώρα ζητούνται αργότερα κατά τη φόρτωση της σελίδας."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Χρησιμοποιήστε το [Ezoic Leap](https://pubdash.ezoic.com/speed) και ενεργοποιήστε το `Resize Images` για να αλλάξετε το μέγεθος των εικόνων στο κατάλληλο μέγεθος μιας συσκευής, μειώνοντας τα μεγέθη φορτίων δικτύου."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Χρησιμοποιήστε το στοιχείο `gatsby-plugin-image` αντί του `<img>` για την αυτόματη βελτιστοποίηση της μορφής εικόνας. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Χρησιμοποιήστε το στοιχεί<PERSON> `gatsby-plugin-image` αντί του `<img>` για την αυτόματη αργή φόρτωση των εικόνων. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Χρησιμοποιήστε το στοιχεί<PERSON> `gatsby-plugin-image` και ορίστε την ιδιότητα `loading` σε `eager`. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Χρησιμοποιήστε το στοιχείο `Gatsby Script API` για να αναβάλετε τη φόρτωση των σεναρίων τρίτου μέρους δευτερεύουσας σημασίας. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Χρησιμοποιήστε την προσθήκη `<PERSON>urgeCSS` `Gatsby` για να καταργήσετε τους κανόνες που δεν χρησιμοποιούνται από τα φύλλα στιλ. [Μάθετε περισσότερα](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Χρησιμοποιήστε το στοιχείο `Webpack Bundle Analyzer` για ανίχνευση κώδικα JavaScript που δεν χρησιμοποιείται. [Μάθετε περισσότερα](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Διαμόρφωση κρυφής μνήμης για αμετάβλητα στοιχεία. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Χρησιμοποιήστε το στοιχεί<PERSON> `gatsby-plugin-image` αντί του `<img>` για να προσαρμόσετε την ποιότητα εικόνας. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Χρησιμοποιήστε το στοιχεί<PERSON> `gatsby-plugin-image` για να ορίσετε το κατάλληλο `sizes`. [Μάθετε περισσότερα](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Εξετάστε το ενδεχόμενο να ανεβάσετε το GIF σε μια υπηρεσία η οποία θα το διαθέσει για ενσωμάτωση ως βίντεο HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια [προσθήκη](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ή μια υπηρεσία που θα μετατρέπει αυτόματα τις μεταφορτωμένες εικόνες σας στη βέλτιστη μορφή."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Εγκαταστήστε μια προσθήκη [<PERSON><PERSON><PERSON> αργής φόρτωσης](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) η οποία παρέχει τη δυνατότητα αναβολής φόρτωσης των εικόνων εκτός οθόνης. Εναλλακτικά, χρησιμοποιήστε ένα πρότυπο που παρέχει αυτήν τη δυνατότητα. Απ<PERSON> το Joomla 4.0, όλες οι νέες εικόνες θα λαμβάνουν [αυτόματα](https://github.com/joomla/joomla-cms/pull/30748) το χαρακτηριστικό `loading` από τη βασική ρύθμιση."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Υπάρχουν διάφορες προσθήκες Jo<PERSON>la που μπορούν να σας βοηθήσουν στην [ενσωμάτωση των σημαντικών στοιχείων](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ή στην [καθυστέρηση των λιγότερο σημαντικών πόρων](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Λάβετε υπόψη ότι οι βελτιστοποιήσεις που παρέχονται από αυτές τις προσθήκες μπορεί να προκαλέσουν προβλήματα στις λειτουργίες των προτύπων ή των προσθηκών σας. Συνεπώς, είναι πιθανό να χρειαστεί να τις ελέγξετε προσεκτικά."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Τα πρότυπα, οι επεκτάσεις και οι προδιαγραφές διακομιστή συμβάλλουν στον χρόνο απόκρισης του διακομιστή. Αν θέλετε, μπορείτε να ψάξετε για ένα περισσότερο βελτιστοποιημένο πρότυπο, να επιλέξετε προσεκτικά μια επέκταση βελτιστοποίησης ή/και να αναβαθμίσετε τον διακομιστή σας."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Εξετάστε το ενδεχόμενο εμφάνισης αποσπασμάτων στις κατηγορίες άρθρων σας (π.χ. μέσω της ετικέτας περισσότερα), μειώνοντας τον αριθμό των άρθρων που εμφανίζονται σε μια συγκεκριμένη σελίδα, χωρίζοντας τις μεγάλες αναρτήσεις σε πολλές σελίδες ή χρησιμοποιώντας μια προσθήκη για φόρτωση με καθυστέρηση των σχολίων."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Μερικές [επεκτ<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) μπορούν να επιταχύνουν τον ιστότοπό σας συνενώνοντας, ελαχιστοποιώντας και συμπιέζοντας τα στιλ σας. Υπάρχουν επίσης πρότυπα που παρέχουν αυτήν τη λειτουργία."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Μερικές [προσθή<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) μπορούν να επιταχύνουν τον ιστότοπό σας συνενώνοντας, ελαχιστοποιώντας και συμπιέζοντας τα σενάριά σας. Υπάρχουν επίσης πρότυπα που παρέχουν αυτήν τη λειτουργία."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Εξετάστε το ενδεχόμενο μείωσης ή αλλαγής των [επεκτά<PERSON>εων <PERSON>](https://extensions.joomla.org/) που φορτώνουν μη χρησιμοποιούμενα CSS στη σελίδα σας. Για να προσδιορίσετε τις επεκτάσεις που προσθέτουν περιττό κώδικα CSS, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Μπορείτε να προσδιορίσετε το θέμα ή την προσθήκη που ευθύνεται μέσω του URL του φύλλου στιλ. Αναζητήστε προσθήκες που διαθέτουν πολλά φύλλα στιλ στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια προσθήκη θα πρέπει να τοποθετεί στην ουρά ένα φύλλο στιλ, μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Εξετάστε το ενδεχόμενο μείωσης ή αλλαγής των [επεκτά<PERSON>εων <PERSON>](https://extensions.joomla.org/) που φορτώνουν μη χρησιμοποιούμενα JavaScript στη σελίδα σας. Για να προσδιορίσετε τις προσθήκες που προσθέτουν περιττό κώδικα JS, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Μπορείτε να προσδιορίσετε την επέκταση που ευθύνεται μέσω του URL του σεναρίου. Αναζητήστε επεκτάσεις που διαθέτουν πολλά σενάρια στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια επέκταση θα πρέπει να τοποθετεί στην ουρά ένα σενάριο, μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Διαβάστε σχετικά με την [Κρυφή μνήμη προγράμματος περιήγησης στο <PERSON>](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια [προσθήκη βελτιστοποίησης εικόνων](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) που συμπιέζει τις εικόνες διατηρώντας όμως την ποιότητά τους."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Εξετάστε το ενδεχόμενο χρήσης μιας [προσθήκης αποκριτικών εικόνων](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), για να χρησιμοποιείτε αποκριτικές εικόνες στο περιεχόμενό σας."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Μπορείτε να ενεργοποιήσετε τη συμπίεση κειμένου μέσω της ενεργοποίησης της Συμπίεσης σελίδας G<PERSON><PERSON> στο <PERSON> (Σύστημα > Καθολική διαμόρφωση > Διακομιστής)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Εάν δεν δημιουργείτε δικά σας στοιχε<PERSON><PERSON>, σκεφτείτε να χρησιμοποιήσετε [δεματοποιητή](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Απενεργοποιήστε την ενσωματωμένη [πακετοποίηση και ελαχιστοποίηση JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), και δοκιμάστε να χρησιμοποιήσετε αντ' αυτού τον [δεματοποιητή](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Καθορίστε το `@font-display` όταν [ορίζετε προσαρμοσμένες γραμματοσειρές](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Μπορείτε να πραγματοποιήσετε αναζήτηση στο [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) για διάφορες επεκτάσεις τρίτων μερών, ώστε να αξιοποιήσετε νεότερες μορφές εικόνας."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Μπορείτε να τροποποιήσετε το προϊόν σας και τα πρότυπα καταλόγου για να αξιοποιήσετε τη λειτουργία [αργής φόρτωσης](https://web.dev/native-lazy-loading) της πλατφόρμας ιστού."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Χρησιμοποιήστε την[ενσωμάτωση Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) του Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Ενεργοποιήστε την επιλογή Ελαχιστοποίηση αρχείων CSS στις ρυθμίσεις Προγραμματιστή του καταστήματός σας. [Μάθετε περισσότερα](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Χρησιμοποιήστε το [Terser](https://www.npmjs.com/package/terser) για να ελαχιστοποιήσετε όλα τα στοιχεία JavaScript από την ανάπτυξη στατικού περιεχομένου και να απενεργοποιήσετε την ενσωματωμένη λειτουργία ελαχιστοποίησης."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Απενεργοποιήστε την ενσωματωμένη [πακετοποίηση JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) του Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Μπορείτε να πραγματοποιήσετε αναζήτηση στο [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) για διάφορες επεκτάσεις τρίτου μέρους, για να βελτιστοποιήσετε τις εικόνες."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Υποδείξεις πόρων πρόωρης σύνδεσης ή πρόωρης λήψης dns μπορούν να προστεθούν [τροποποιώντας τη διάταξη ενός θέματος](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Οι ετικέτες `<link rel=preload>` μπορούν να προστεθούν [τροποποιώντας τη διάταξη ενός θέματος](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Χρησιμοποιήστε το στοιχεί<PERSON> `next/image` αντί του `<img>` για την αυτόματη βελτιστοποίηση της μορφής εικόνας. [Μάθετε περισσότερα](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Χρησιμοποιήστε το στοιχείο `next/image` αντί του `<img>` για την αυτόματη αργή φόρτωση των εικόνων. [Μάθετε περισσότερα](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Χρησιμοποιήστε το στοιχείο `next/image` και ορίστε το πεδίο προτεραιότητα σε αληθές για την προφόρτωση της εικόνας L<PERSON>. [Μάθετε περισσότερα](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Χρησιμοποιήστε το στοιχείο `next/script` για να αναβάλετε τη φόρτωση των σεναρίων τρίτου μέρους δευτερεύουσας σημασίας. [Μάθετε περισσότερα](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Χρησιμοποιήστε το στοιχείο `next/image` για να βεβαιωθείτε ότι οι εικόνες έχουν πάντα το κατάλληλο μέγεθος. [Μάθετε περισσότερα](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Εξετάστε το ενδεχόμενο να ρυθμίσετε το στοιχείο `PurgeCSS` στη διαμόρφωση `Next.js` για να καταργήσετε τους κανόνες που δεν χρησιμοποιούνται από τα φύλλα στιλ. [Μάθετε περισσότερα](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Χρησιμοποιήστε το στοιχείο `Webpack Bundle Analyzer` για ανίχνευση κώδικα JavaScript που δεν χρησιμοποιείται. [Μάθετε περισσότερα](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε το στοιχείο `Next.js Analytics` για να μετράτε την πραγματική απόδοση της εφαρμογής σας. [Μάθετε περισσότερα](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Διαμόρφωση κρυφής μνήμης για αμετάβλητα στοιχεία και σελίδες `Server-side Rendered` (SSR). [Μάθετε περισσότερα](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Χρησιμοποιήστε το στοιχείο `next/image` αντί του `<img>` για να προσαρμόσετε την ποιότητα εικόνας. [Μάθετε περισσότερα](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Χρησιμοποιήστε το στοιχείο `next/image` για να ρυθμίσετε το κατάλληλο `sizes`. [Μάθετε περισσότερα](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Ενεργοποιήστε τη συμπίεση στον διακομιστή Next.js. [Μάθετε περισσότερα](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Επικοινωνήστε με τον υπεύθυνο λογαριασμού για την ενεργοποίηση του [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Με τη διαμόρφωση της λειτουργίας, η απόδοση της σελίδας σας θα έχει προτεραιότητα και θα βελτιωθεί."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Χρησιμοποιήστε την επιλογή [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) στο Nitro<PERSON>ack για να ορίσετε μια επιθυμητή τιμή για τον κανόνα προβολής γραμματοσειράς CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Χρησιμοποιήστε τη λειτουργία [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) για αυτόματη μετατροπή των εικόνων σας σε WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Αναβάλετε εικόνες εκτός οθόνης ενεργοποιώντας τη λειτουργία [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Ενεργοποιήστε τη λειτουργία [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) στο NitroPack για ταχύτερους χρόνους αρχικής φόρτωσης."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Βελτιώστε τον χρόνο απόκρισης διακομιστή και βελτιστοποιήστε την αντιληπτή απόδοση, ενεργοποιώντας τη λειτουργία [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Ενεργοποιήστε τη λειτουργία [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) στις ρυθμίσεις της κρυφής μνήμης για να μειώσετε το μέγεθος των αρχείων CSS, HTML και JavaScript για ταχύτερους χρόνους φόρτωσης."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Ενεργοποιήστε τη λειτουργία [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) στις ρυθμίσεις της κρυφής μνήμης για να μειώσετε το μέγεθος των αρχείων JS, HTML και CSS για ταχύτερους χρόνους φόρτωσης."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Ενεργοποιήστε τη λειτουργία [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) για να καταργήσετε κανόνες CSS που δεν ισχύουν για αυτήν τη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Διαμορφώστε τη λειτουργία [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) στο NitroPack για να καθυστερήσετε τη φόρτωση σεναρίων έως ότου γίνουν απαραίτητα."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Μεταβείτε στη λειτουργία [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) στο μενού `Caching` και προσαρμόστε τον χρόνο λήξης της κρυφής μνήμης των σελίδων σας, προκειμένου να βελτιώσετε τους χρόνους φόρτωσης και την εμπειρία χρήστη."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Συμπιέστε αυτόματα, βελτιστοποιήστε και μετατρέψτε τις εικόνες σας σε WebP, ενεργοποιώντας τη ρύθμιση [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Ενεργοποιήστε τη λειτουργία [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) για να βελτιστοποιήσετε προληπτικά τις εικόνες σας και να τις προσαρμόσετε με τις διαστάσεις των κοντέινερ που εμφανίζονται σε όλες τις συσκευές."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Χρησιμοποιήστε τη λειτουργία [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) στο NitroPack για να μειώσετε το μέγεθος των αρχείων που αποστέλλονται στο πρόγραμμα περιήγησης."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και ορίστε το `format=\"webp\"`. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και ορίστε το `loading=\"lazy\"` για εικόνες εκτός οθόνης. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και καθορίστε το `preload` για την εικόνα LCP. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και καθορίστε ρητά τα `width` και `height`. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και ορίστε το κατάλληλο `quality`. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Χρησιμοποιήστε το στοιχείο `nuxt/image` και ορίστε το κατάλληλο `sizes`. [Μάθετε περισσότερα](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Αντικαταστήστε τις κινούμενες εικόνες GIF με βίντεο](https://web.dev/replace-gifs-with-videos/) για ταχύτερη φόρτωση ιστοσελίδας, ενώ μπορείτε επίσης να χρησιμοποιήσετε σύγχρονες μορφές αρχείων όπως [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ή [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) για τη βελτίωση της απόδοσης συμπίεσης κατά περισσότερο από 30% σε σχέση με τον τρέχοντα υπερσύγχρονο κωδικοποιητή βίντεο, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια [προσθήκη](https://octobercms.com/plugins?search=image) ή μια υπηρεσία που θα μετατρέπει αυτόματα τις μεταφορτωμένες εικόνες σας στη βέλτιστη μορφή. Οι [Εικόνες WebP χωρίς απώλειες](https://developers.google.com/speed/webp) έχουν 26% μικρότερο μέγεθος σε σχέση με τις εικόνες PNG και 25-34% μικρότερο μέγεθος σε σχέση με τις συγκρίσιμες εικόνες JPEG στον αντίστοιχο δείκτη ποιότητας SSIM. Μια άλλη μορφή εικόνας επόμενης γενιάς που μπορείτε να χρησιμοποιήσετε είναι η μορφή [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Μπορείτε να εγκαταστήσετε μια [προσθήκη αργής φόρτωσης εικόνας](https://octobercms.com/plugins?search=lazy) η οποία παρέχει τη δυνατότητα αναβολής φόρτωσης των εικόνων εκτός οθόνης. Εναλλακτικά, χρησιμοποιήστε ένα θέμα που παρέχει αυτήν τη δυνατότητα. Εξετάστε επίσης το ενδεχόμενο χρήσης της [προσθήκης AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Υπάρχουν πολλές προσθήκες που βοηθούν στην [ενσωμάτωση των σημαντικών στοιχείων](https://octobercms.com/plugins?search=css). Αυτές οι προσθήκες ενδέχεται να καταστρέψουν άλλα προσθήκες, επομένως θα πρέπει να τις ελέγξετε προσεκτικά."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Τα θέματα, οι προσθήκες και οι προδιαγραφές διακομιστή συμβάλλουν στον χρόνο απόκρισης του διακομιστή. Μπορείτε να αναζητήσετε ένα περισσότερο βελτιστοποιημένο θέμα, να επιλέξετε προσεκτικά μια προσθήκη βελτιστοποίησης ή/και να αναβαθμίσετε τον διακομιστή σας. Το CMS του Οκτωβρίου επιτρέπει επίσης στους προγραμματιστές να χρησιμοποιήσουν [`Queues`](https://octobercms.com/docs/services/queues) για την αναβολή της επεξεργασίας μιας χρονοβόρας εργασίας, όπως η αποστολή ενός μηνύματος ηλεκτρονικού ταχυδρομείου. Αυτό επιταχύνει σημαντικά τα αιτήματα ιστού."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Εξετάστε το ενδεχόμενο εμφάνισης αποσπασμάτων στις λίστες αναρτήσεών σας (π.χ. χρησιμοποιώντας ένα κουμπί `show more`), μειών<PERSON>ντας τον αριθμό των αναρτήσεων που εμφανίζονται σε μια συγκεκριμένη ιστοσελίδα, χωρίζοντας τις μεγάλες αναρτήσεις σε πολλές ιστοσελίδες ή χρησιμοποιώντας μια προσθήκη για φόρτωση με καθυστέρηση των σχολίων."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Υπάρχουν πολλές [προσθήκες](https://octobercms.com/plugins?search=css) που μπορούν να αυξήσουν την ταχύτητα του ιστοτόπου σας συνενώνοντας, ελαχιστοποιώντας και συμπιέζοντας τα στιλ. Η χρήση μιας διαδικασίας κατασκευής για την εκ των προτέρων πραγματοποίηση αυτής της ελαχιστοποίησης μπορεί να επιταχύνει την ανάπτυξη."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Υπάρχουν πολλές [προσθήκες](https://octobercms.com/plugins?search=javascript) που μπορούν να αυξήσουν την ταχύτητα του ιστοτόπου σας συνενώνοντας, ελα<PERSON><PERSON><PERSON>τ<PERSON>ποιώντας και συμπιέζοντας τα σενάρια. Η χρήση μιας διαδικασίας κατασκευής για την εκ των προτέρων πραγματοποίηση αυτής της ελαχιστοποίησης μπορεί να επιταχύνει την ανάπτυξη."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Μπορείτε να χρησιμοποιήσετε τις [προσθήκες](https://octobercms.com/plugins) που φορτώνουν μη χρησιμοποιούμενο κώδικα CSS στη σελίδα σας. Για να προσδιορίσετε τις προσθήκες που προσθέτουν περιττό κώδικα CSS, εκτελέστε την [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Εντοπίστε το θέμα/την προσθήκη που ευθύνεται μέσω του URL φύλλου στιλ. Αναζητήστε προσθήκες με πολλά φύλλα στιλ στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια προσθήκη θα πρέπει να προσθέτει ένα φύλλο στιλ, μόνο εάν χρησιμοποιείται στην ιστοσελίδα."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Μπορείτε να ελέγξετε τις [προσθήκες](https://octobercms.com/plugins?search=javascript) που φορτώνουν μη χρησιμοποιούμενα JavaScript στην ιστοσελίδα. Για να προσδιορίσετε τις προσθήκες που προσθέτουν περιττό κώδικα <PERSON>, εκτελέστε την [κάλυψη κώδικα](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) στο Chrome DevTools. Προσδιορίστε το θέμα/την προσθήκη που ευθύνεται μέσω του URL του σεναρίου. Αναζητήστε προσθήκες με πολλά σενάρια στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια προσθήκη θα πρέπει να προσθέτει ένα σενάριο, μόνο εάν χρησιμοποιείται στην ιστοσελίδα."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Διαβάστε σχετικά με την [πρόληψη των περιττών αιτημάτων δικτύου με την Κρυφή μνήμη HTTP](https://web.dev/http-cache/#caching-checklist). Υπάρχουν πολλές [προσθήκες](https://octobercms.com/plugins?search=Caching) που μπορούν να χρησιμοποιηθούν για την επιτάχυνση της αποθήκευσης στην κρυφή μνήμη."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια [προσθήκη βελτιστοποίησης εικόνων](https://octobercms.com/plugins?search=image) που συμπιέζει τις εικόνες διατηρώντας όμως την ποιότητά τους."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Ανεβάστε εικόνες απευθείας στη διαχείριση μέσων για να διασφαλίσετε ότι τα απαιτούμενα μεγέθη εικόνων είναι διαθέσιμα. Μπορείτε να χρησιμοποιήσετε το [φίλτρο αλλαγής μεγέθους](https://octobercms.com/docs/markup/filter-resize) ή μια [προσθήκη αλλαγής μεγέθους εικόνας](https://octobercms.com/plugins?search=image) για να διασφαλίσετε ότι χρησιμοποιούνται τα βέλτιστα μεγέθη εικόνων."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Ενεργοποίηση συμπίεσης κειμένου στη διαμόρφωση διακομιστή ιστού."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Μπορείτε να χρησιμοποιήσετε μια βιβλιοθήκη \"παραθύρων\", όπως η `react-window` για να ελαχιστοποιήσετε τον αριθμό κόμβων DOM που δημιουργήθηκαν, εάν αποδίδετε πολλά επαναλαμβανόμενα στοιχεία στη σελίδα. [Μάθετε περισσότερα](https://web.dev/virtualize-long-lists-react-window/). Επίσης, ελαχιστοποιήστε τις περιττές εκ νέου αποδόσεις, χρησιμοποιώντας το [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ή το [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) και [παραβλέψτε τα εφέ](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) μόνο μέχρι να αλλάξουν συγκεκριμένες εξαρτήσεις, εάν χρησιμοποιείτε το άγκιστρο `Effect` για να βελτιώσετε την απόδοση χρόνου εκτέλεσης (runtime)."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Εάν χρησιμοποιείτε το React Router, ελαχιστοποιήστε τη χρήση του στοιχείου `<Redirect>` για [πλοηγήσεις διαδρομής](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Εάν πραγματοποιείτε απόδοση στοιχείων React στον διακομιστή, μπορείτε να χρησιμοποιήσετε το `renderToPipeableStream()` ή το `renderToStaticNodeStream()` για να επιτρέψετε στον υπολογιστή-πελάτη να λαμβάνει και να ενισχύει διαφορετικά μέρη της σήμανσης, αντί για όλα μαζί. [Μάθετε περισσότερα](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Εάν το σύστημα έκδοσής σας ελαχιστοποιεί τα αρχεία CSS αυτόματα, βεβαιωθείτε ότι αναπτύσσετε την έκδοση παραγωγής της εφαρμογής σας. Μπορείτε να το ελέγξετε αυτό από την επέκταση React Developer Tools. [Μάθετε περισσότερα](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Εάν το σύστημα έκδοσής σας ελαχιστοποιεί τα αρχεία JS αυτόματα, βεβαιωθείτε ότι αναπτύσσετε την έκδοση παραγωγής της εφαρμογής σας. Μπορείτε να το ελέγξετε αυτό από την επέκταση React Developer Tools. [Μάθετε περισσότερα](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Εάν δεν πραγματοποιείτε απόδοση στον διακομιστή, [χωρίστε τα πακέτα JavaScript](https://web.dev/code-splitting-suspense/) με το `React.lazy()`. Διαφορετικ<PERSON>, χωρίστε με χρήση κώδικα, χρησιμοποιώντας μια βιβλιοθήκη τρίτου μέρους, όπως τα [στοιχεία με δυνατότητα φόρτωσης](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Χρησιμοποιήστε το React DevTools Profiler, το οποίο χρησιμοποιεί το API του Profiler, για να μετρήσετε τις επιδόσεις απόδοσης των στοιχείων σας. [Μάθετε περισσότερα.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Τοποθετήστε βίντεο εντός του `VideoBoxes`, προσαρμόστε τα χρησιμοποιώντας το `Video Masks` ή προσθέστε το `Transparent Videos`. [Μάθετε περισσότερα](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Ανεβάστε εικόνες χρησιμοποιώντας το `Wix Media Manager`, για να διασφαλίσετε ότι θα προβάλλονται αυτόματα ως WebP. Βρείτε [περισσότερους τρόπους βελτιστοποίησης](https://support.wix.com/en/article/site-performance-optimizing-your-media) των μέσων του ιστοτόπου σας."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Κατά την [προσθήκη κώδικα τρίτου μέρους](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) στην καρτέλα `Custom Code` του πίνακα ελέγχου του ιστοτόπου σας, βεβαιωθείτε ότι αναβάλλεται ή φορτώνεται στο τέλος του σώματος κώδικα. Όπου είναι δυνατόν, χρησιμοποιήστε τις [ενσωματώσεις](https://support.wix.com/en/article/about-marketing-integrations) του Wix, για να ενσωματώσετε εργαλεία μάρκετινγκ στον ιστότοπό σας. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Το Wix χρησιμοποιεί ΔΠΠ και αποθήκευση στην κρυφή μνήμη, για να προβάλλει απαντήσεις όσο το δυνατόν πιο γρήγορα. Σκεφτείτε το ενδεχόμενο [μη αυτόματης ενεργοποίησης της αποθήκευσης στην κρυφή μνήμη](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) για τον ιστότοπό σας, ειδικά αν χρησιμοποιείτε το `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Ελέγξτε τυχόν κώδικα τρίτου μέρους που έχετε προσθέσει στον ιστότοπό σας στην καρτέλα `Custom Code` του πίνακα ελέγχου του ιστοτόπου σας και διατηρήστε μόνο τις υπηρεσίες που είναι απαραίτητες για τον ιστότοπο. [Μάθετε περισσότερα](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Εξετάστε το ενδεχόμενο να ανεβάσετε το GIF σε μια υπηρεσία η οποία θα το διαθέσει για ενσωμάτωση ως βίντεο HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε την προσθήκη [Εργαστήριο απόδοσης](https://wordpress.org/plugins/performance-lab/) για την αυτόματη μετατροπή των μεταφορτωμένων εικόνων JPEG σε WebP, όπου υποστηρίζεται."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Εγκαταστήστε μια προσθήκη [WordPress αργής φόρτωσης](https://wordpress.org/plugins/search/lazy+load/) η οποία παρέχει τη δυνατότητα αναβολής φόρτωσης των εικόνων εκτός οθόνης. Εναλλακτικ<PERSON>, χρησιμοποιήστε ένα θέμα που παρέχει αυτή τη δυνατότητα. Εξετάστε επίσης το ενδεχόμενο χρήσης της [προσθήκης AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Υπάρχουν διάφορες προσθήκες WordPress που μπορούν να σας βοηθήσουν στην [ενσωμάτωση των σημαντικών στοιχείων](https://wordpress.org/plugins/search/critical+css/) ή στην [καθυστέρηση των λιγότερο σημαντικών πόρων](https://wordpress.org/plugins/search/defer+css+javascript/). Λάβετε υπόψη ότι οι βελτιστοποιήσεις που παρέχονται από αυτές τις προσθήκες ενδέχεται να προκαλέσουν προβλήματα στις λειτουργίες των θεμάτων ή των προσθηκών σας. Ως εκ τούτου, είναι πιθανό να χρειαστεί να κάνετε αλλαγές στον κώδικα."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Τα θέματα, οι προσθήκες και οι προδιαγραφές διακομιστή συμβάλλουν στον χρόνο απόκρισης του διακομιστή. Αν θέλετε, μπορείτε να ψάξετε για ένα περισσότερο βελτιστοποιημένο θέμα, να επιλέξετε προσεκτικά μια προσθήκη βελτιστοποίησης ή/και να αναβαθμίσετε τον διακομιστή σας."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Εξετάστε το ενδεχόμενο εμφάνισης αποσπασμάτων στις λίστες αναρτήσεών σας (π.χ. μέσω της ετικέτας \"περισσότερες\"), μει<PERSON><PERSON><PERSON>ντας τον αριθμό των αναρτήσεων που εμφανίζονται σε μια συγκεκριμένη σελίδα, χωρίζοντας τις μεγάλες αναρτήσεις σε πολλές σελίδες ή χρησιμοποιώντας μια προσθήκη για φόρτωση με καθυστέρηση των σχολίων."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Μερικές [προσθήκες WordPress](https://wordpress.org/plugins/search/minify+css/) μπορούν να επιταχύνουν τον ιστότοπό σας συνενώνοντας, ελ<PERSON><PERSON><PERSON><PERSON>τοποιώντας και συμπιέζοντας τα στιλ σας. Μπορείτε επίσης να χρησιμοποιήσετε μια διεργασία δόμησης για την εκ των προτέρων πραγματοποίηση αυτής της ελαχιστοποίησης, εφόσον υπάρχει αυτή η δυνατότητα."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Μερικές [προσθήκες WordPress](https://wordpress.org/plugins/search/minify+javascript/) μπορούν να επιταχύνουν τον ιστότοπό σας συνενώνοντας, ελα<PERSON>ιστοποιώντας και συμπιέζοντας τα σενάριά σας. Μπορείτε επίσης να χρησιμοποιήσετε μια διεργασία δόμησης για την εκ των προτέρων πραγματοποίηση αυτής της ελαχιστοποίησης, εφόσον υπάρχει αυτή η δυνατότητα."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Εξετάστε το ενδεχόμενο μείωσης ή αλλαγής των [προσθηκών WordPress](https://wordpress.org/plugins/) που φορτώνουν μη χρησιμοποιούμενα CSS στη σελίδα σας. Για να προσδιορίσετε τις προσθήκες που προσθέτουν περιττό κώδικα CSS, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developer.chrome.com/docs/devtools/coverage/) στο Chrome DevTools. Μπορείτε να προσδιορίσετε το θέμα ή την προσθήκη που ευθύνεται μέσω του URL του φύλλου στιλ. Αναζητήστε προσθήκες που διαθέτουν πολλά φύλλα στιλ στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια προσθήκη θα πρέπει να τοποθετεί στην ουρά ένα φύλλο στιλ, μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Εξετάστε το ενδεχόμενο μείωσης ή αλλαγής των [προσθηκών WordPress](https://wordpress.org/plugins/) που φορτώνουν μη χρησιμοποιούμενη JavaScript στη σελίδα σας. Για να προσδιορίσετε τις προσθήκες που προσθέτουν περιττό κώδικα JS, δοκιμάστε να εκτελέσετε [κάλυψη κώδικα](https://developer.chrome.com/docs/devtools/coverage/) στο Chrome DevTools. Μπορείτε να προσδιορίσετε το θέμα ή την προσθήκη που ευθύνεται μέσω του URL του σεναρίου. Αναζητήστε προσθήκες που διαθέτουν πολλά σενάρια στη λίστα, στα οποία ένα μεγάλο μέρος της κάλυψης κώδικα έχει κόκκινο χρώμα. Μια προσθήκη θα πρέπει να τοποθετεί στην ουρά ένα σενάριο μόνο αν χρησιμοποιείται στη σελίδα."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Διαβάστε σχετικά με την [Κρυφή μνήμη προγράμματος περιήγησης στο WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Εξετάστε το ενδεχόμενο να χρησιμοποιήσετε μια [προσθήκη βελτιστοποίησης εικόνων WordPress](https://wordpress.org/plugins/search/optimize+images/) που συμπιέζει τις εικόνες διατηρώντας όμως την ποιότητά τους."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Ανεβάστε εικόνες απευθείας μέσω της [βιβλιοθήκης μέσων](https://wordpress.org/support/article/media-library-screen/) για να διασφαλίσετε ότι τα απαιτούμενα μεγέθη εικόνων είναι διαθέσιμα. Στη συνέχεια, μπορείτε να τις εισαγάγετε από τη βιβλιοθήκη μέσων ή να χρησιμοποιήσετε το γραφικό στοιχείο εικόνων για να διασφαλίσετε ότι χρησιμοποιούνται τα βέλτιστα μεγέθη (συμπεριλαμβανομένων αυτών για τα αποκριτικά σημεία διακοπής). Αποφύγετε τη χρήση εικόνων `Full Size`, εκτός αν οι διαστάσεις είναι κατάλληλες για τη χρήση τους. [Μάθετε περισσότερα](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Μπορείτε να ενεργοποιήσετε τη συμπίεση κειμένου στη διαμόρφωση του διακομιστή ιστού σας."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Ενεργοποιήστε το Imagify από την καρτέλα Βελτιστοποίηση εικόνων στο WP Rocket, για να μετατρέψετε τις εικόνες σας σε WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Ενεργοποιήστε την επιλογή [Αργή φόρτωση](https://docs.wp-rocket.me/article/1141-lazyload-for-images) στο WP Rocket για να διορθώσετε το πρόβλημα που αναφέρει η πρόταση. Αυτή η λειτουργία καθυστερεί τη φόρτωση των εικόνων μέχρι ο επισκέπτης να κάνει κύλιση προς τα κάτω στη σελίδα και να είναι απαραίτητη η προβολή τους."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Ενεργοποιήστε τις επιλογές [Κατάργηση CSS που δεν χρησιμοποιείται](https://docs.wp-rocket.me/article/1529-remove-unused-css) και [Αναβολή φόρτωση<PERSON>](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) στο WP Rocket για να διορθώσετε το πρόβλημα που αναφέρει η πρόταση. Αυτές οι λειτουργίες θα βελτιστοποιούν αντίστοιχα τα αρχεία CSS και JavaScript, ώστε να μην εμποδίζουν την απόδοση της σελίδας σας."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Ενεργοποιήστε την επιλογή [Ελαχιστοποίηση αρχείων CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) στο WP Rocket για να διορθώσετε αυτό το πρόβλημα. Τυχόν χώροι και σχόλια στα αρχεία CSS του ιστοτόπου σας θα καταργηθούν, ώστε το μέγεθος του αρχείου να είναι μικρότερο και η λήψη του ταχύτερη."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Ενεργοποιήστε την επιλογή [Ελαχιστοποίηση αρχείων JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) στο WP Rocket για να διορθώσετε αυτό το πρόβλημα. Οι κενοί χώροι και τα σχόλια θα καταργηθούν από τα αρχεία JavaScript για να μειωθεί το μέγεθός τους και να γίνεται ταχύτερα η λήψη τους."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Ενεργοποιήστε την επιλογή [Κατάργηση CSS που δεν χρησιμοποιείται](https://docs.wp-rocket.me/article/1529-remove-unused-css) στο WP Rocket για να διορθώσετε αυτό το πρόβλημα. Μειώνει το μέγεθος της σελίδας καταργώντας όλα τα CSS και τα φύλλα στιλ που δεν χρησιμοποιούνται, διατηρώντας μόνο το CSS που χρησιμοποιείται για κάθε σελίδα."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Ενεργοποιήστε την επιλογή [Καθυστέρηση εκτέλεσης JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) στο WP Rocket για να διορθώσετε αυτό το πρόβλημα. Θα βελτιώσει τη φόρτωση της σελίδας σας, καθυστερώντας την εκτέλεση των σεναρίων μέχρι την αλληλεπίδραση με τον χρήστη. Αν ο ιστότοπός σας διαθέτει iframe, μπορείτε επίσης να χρησιμοποιήσετε τις επιλογές [Αργή φόρτωση για iframe και βίντεο](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) και [Αντικατάσταση iframe του YouTube με εικόνα προεπισκόπησης](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) του WP Rocket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Ενεργοποιήστε το Imagify από την καρτέλα Βελτιστοποίηση εικόνων στο WP Rocket και εκτελέστε τη Μαζική βελτιστοποίηση για να συμπιέσετε τις εικόνες."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Χρησιμοποιήστε [αιτήματα DNS προαναζήτησης](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) στο WP Rocket για να προσθέσετε το στοιχείο dns-prefetch και να επιταχύνετε τη σύνδεση με εξωτερικούς τομείς. Επίση<PERSON>, το WP Rocket προσθέτει αυτόματα την \"προσύνδεση\" στον [τομέα <PERSON> Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) και σε όλα τα CNAME που προστίθενται μέσω της λειτουργίας [Ενεργοποίηση ΔΠΠ](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Για να διορθώσετε αυτό το πρόβλημα σχετικά με τις γραμματοσειρές, ενεργοποιήστε την επιλογή [Κατάργηση CSS που δεν χρησιμοποιείται](https://docs.wp-rocket.me/article/1529-remove-unused-css) στο WP Rocket. Οι σημαντικές γραμματοσειρές του ιστοτόπου σας θα προφορτωθούν με προτεραιότητα."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Προβολή αριθμομηχανής."}, "report/renderer/report-utils.js | collapseView": {"message": "Σύμπτυξη προβολής"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Αρχική πλοήγηση"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Μέγιστ<PERSON> λανθάνων χρόνος κρίσιμης διαδρομής:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Αντιγραφή JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Εναλλαγή σκούρου θέματος"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Πλήρης εκτύπωση"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Συνοπτική εκτύπωση"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Αποθήκευση ως Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Αποθήκευση ως HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Αποθήκευση ως JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Προβολή ίχνους χωρίς περιορισμό"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Άνοιγμα στο πρόγραμμα προβολής"}, "report/renderer/report-utils.js | errorLabel": {"message": "Σφάλμα!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Σφάλμα αναφοράς: Δεν υπάρχουν πληροφορίες ελέγχου"}, "report/renderer/report-utils.js | expandView": {"message": "Ανάπτυξη προβολής"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "1ο μέρος"}, "report/renderer/report-utils.js | footerIssue": {"message": "Υποβολή προβλήματος"}, "report/renderer/report-utils.js | hide": {"message": "Απόκρυψη"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Εργαστηρια<PERSON><PERSON> δεδομένα"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Ανάλυση της τρέχουσας σελίδας από το [Lighthouse](https://developers.google.com/web/tools/lighthouse/) σε ένα προσομειωμένο δίκτυο κινητής τηλεφωνίας. Οι τιμές είναι κατ' εκτίμηση και μπορεί να διαφέρουν."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Επιπλέον στοιχεία για μη αυτόματο έλεγχο"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Δεν ισχύει"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Άνοιγμα σε νέα καρτέλα"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Ευκαιρία"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Εκτιμώμενες εξοικονομήσεις"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Έλεγχοι που ολοκληρώθηκαν επιτυχώς"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Σύμφωνα με τα [ενημερωμένα κριτήρια για τη δυνατότητα εγκατάστασης του Chrome](https://developer.chrome.com/blog/update-install-criteria), το Lighthouse θα καταργήσει την κατηγορία PWA σε μια μελλοντική έκδοση. Ανατρέξτε στην [ενημερωμένη τεκμηρίωση PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/) για μελλοντικές δοκιμές PWA."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Αρχική φόρτωση σελίδας"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Σύνοψη συγκεκριμένης χρονικής στιγμής"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Χρονικ<PERSON> διάστημα αλληλεπιδρά<PERSON>εων χρήστη"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Προσαρμοσμένη ρύθμιση ροής δεδομένων"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Προσομοιωμένη επιφάνεια εργασίας"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Προσομοιωμένο Mo<PERSON> G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> προσομοίωση"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "'Εκδοση Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Ισ<PERSON><PERSON><PERSON> CPU/μνήμης χωρίς περιορισμό"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Περιορισμ<PERSON>ς CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Συσκευή"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Περιορισμ<PERSON>ς δικτύου"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Προσομοίωση οθόνης"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Παρά<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χρήστη (δίκτυο)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Περί<PERSON><PERSON>ος σύνδεσης σε μία σελίδα"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Αυτά τα δεδομένα λαμβάνονται από μία περίοδο σύνδεσης σε μία σελίδα, σε αντίθεση με τα δεδομένα πεδίου που συνοψίζουν πολλές περιόδους σύνδεσης."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ρύθμιση ροής δεδομένων αργού 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Άγνωστη"}, "report/renderer/report-utils.js | show": {"message": "Εμφάνιση"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Εμφάνιση ελέγχων που σχετίζονται με:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Σύμπτυξη αποσπάσματος"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Ανάπτυξη αποσπάσματος"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Εμφάνιση πόρων τρίτων"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Παρέχετ<PERSON>ι από το περιβάλλον"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>αν ορισμένα ζητήματα τα οποία επηρεάζουν αυτή την εκτέλεση του Lighthouse:"}, "report/renderer/report-utils.js | unattributable": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> απόδοση"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Οι τιμές εκτιμώνται και μπορεί να ποικίλουν. Η [βαθμολογία απόδοσης υπολογίζεται](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) απευθείας από αυτές τις μετρήσεις."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Προβολή ίχνους"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Προβολή δενδροειδούς χάρτη"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Έλεγχοι που ολοκληρώθηκαν αλλά με προειδοποιήσεις"}, "report/renderer/report-utils.js | warningHeader": {"message": "Προειδοποιήσεις: "}, "treemap/app/src/util.js | allLabel": {"message": "Όλα"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Όλες οι γραφές"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Κάλυψη"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Διπλότυπες λειτουργικές μονάδες"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Byte πόρου"}, "treemap/app/src/util.js | tableColumnName": {"message": "Όνομα"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Εναλλαγ<PERSON> πίνακα"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Byte που δεν χρησιμοποιούνται"}}