{"core/audits/accessibility/accesskeys.js | description": {"message": "Binibigyang-daan ng mga access key ang mga user na mabilis na tumuon sa isang bahagi ng page. Para sa maayos na pag-navigate, natatangi dapat ang bawat access key. [Matuto pa tungkol sa mga access key](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Hindi natatangi ang mga value na `[accesskey]`"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Natatangi ang mga value ng `[accesskey]`"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Sinusuportahan ng bawat `role` ng ARIA ang isang partikular na subset ng mga attribute na `aria-*`. Kapag hindi pinagtugma ang mga ito, magiging invalid ang mga attribute na `aria-*`. [<PERSON><PERSON><PERSON> kung paano itugma ang mga attribute na ARIA sa kanilang mga tungkulin](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Hindi tumutugma ang mga attribute na `[aria-*]` sa mga tungkulin ng mga ito"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Tumutugma ang mga attribute na `[aria-*]` sa mga tungkulin ng mga ito"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "Nagbibigay-daan ang mga `role` ng ARIA para malaman ng mga pantulong na teknolohiya ang tungkulin ng bawat element sa web page. Kung ang mga value ng `role` ay mali ang pagbaybay, hindi value ng umiiral na `role` ng ARIA, o <PERSON> na tungkulin, hindi maipagbibigay-alam ang layunin ng element sa mga user ng mga pantulong na teknolohiya. [<PERSON>uto pa tungkol sa mga tungkulin ng ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Hindi mga valid na tungkulin ng ARIA ang mga value na itinalaga sa `role=\"\"`."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Mga valid na tungkulin ng ARIA ang mga value na itinalaga sa `role=\"\"`."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang element, iaanunsyo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Alamin kung paano gawing mas accessible ang mga element ng command](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga element ng `button`, `link`, at `menuitem`."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "May mga naa-access na pangalan ang mga element ng `button`, `link`, at `menuitem`"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Posibleng mapigilan ng mga element ng dialog ng ARIA na walang accessible na pangalan ang mga user ng screen reader na matukoy ang layunin ng mga element na ito. [Matuto kung paano gagawing mas naa-access ang mga element ng dialog ng ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Walang accessible na pangalan ang mga element na may `role=\"dialog\"` o `role=\"alertdialog\"`."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "May mga accessible na pangalan ang mga element na may `role=\"dialog\"` o `role=\"alertdialog\"`."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Ang mga pantulong na teknolohiya, tulad ng mga screen reader, ay hindi tuloy-tuloy na gumagana kapag nakatakda ang `aria-hidden=\"true\"` sa dokumentong `<body>`. [<PERSON><PERSON><PERSON> kung paano nakakaapekto ang `aria-hidden` sa nilalaman ng dokumento](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "May `[aria-hidden=\"true\"]` sa dokumentong `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Walang `[aria-hidden=\"true\"]` sa dokumentong `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Pinipigilan ng mga nafo-focus na descendent sa isang element na `[aria-hidden=\"true\"]` na maging available ang mga interactive na element iyon sa mga user ng mga pantulong na teknolohiya tulad ng mga screen reader. [<PERSON><PERSON><PERSON> kung paano nakakaapekto ang `aria-hidden` sa mga nafo-focus na element](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "May mga nafo-focus na descendent ang mga element na `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Walang nafo-focus na descendent ang mga element na `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang field ng input, i<PERSON>unsyo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Matuto pa tungkol sa mga label ng field ng input](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga field ng input ng ARIA"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "May mga naa-access na pangalan ang mga field ng input ng ARIA"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang element na meter, iaan<PERSON>yo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Alamin kung paano pangalanan ang mga element na `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga element ng `meter` ng ARIA."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "May mga naa-access na pangalan ang mga element ng `meter` ng ARIA"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang `progressbar` element, iaan<PERSON>yo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Alamin kung paano maglagay ng label sa mga element na `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga element ng `progressbar` ng ARIA."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "May mga naa-access na pangalan ang mga element ng `progressbar` ng ARIA"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Ang ilang tungkulin ng ARIA ay may mga kinakailangang attribute na naglalarawan sa status ng element sa mga screen reader. [Matuto pa tungkol sa mga tungkulin at kinakailangang attribute](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "May mga kulang na kinakailangang attribute na `[aria-*]` ang mga `[role]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "<PERSON><PERSON> ng lahat ng kinakailangang attribute na `[aria-*]` ang mga `[role]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Dapat maglaman ang ilang parent role ng ARIA ng mga partikular na child role para maisagawa nito ang mga nilalayong function sa accessibility. [Matuto pa tungkol sa mga tungkulin at kinakailangang child na element](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Ang mga element na may ARIA na `[role]` na nag-aatas sa mga child na maglaman ng partikular na `[role]` ay kulang ng ilan sa o lahat ng mga kinakailangang child na iyon."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Ang mga element na may ARIA na `[role]` na nag-aatas sa mga child na maglaman ng partikular na `[role]` ay mayroon ng lahat ng kinakailangang child."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dapat ang ilang child role ng ARIA sa mga partikular na parent role para maayos na maisagawa ang mga nilalayong function sa accessibility. [Matuto pa tungkol sa mga tungkulin ng ARIA at kinakailangang parent element](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Hindi nakapaloob ang mga `[role]` sa kinakailangang parent element ng mga ito"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>b ang mga `[role]` sa kinakailangang pangunahing element ng mga ito"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Dapat may mga valid na value ang mga tungkulin ng ARIA para maisagawa ang mga nilalayong function ng mga ito sa accessibility. [Matuto pa tungkol sa mga valid na tungkulin ng ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Hindi valid ang mga value ng `[role]`"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Valid ang mga value ng `[role]`"}, "core/audits/accessibility/aria-text.js | description": {"message": "Kapag nagdagdag ng `role=text` sa palibot ng isang text node split sa pamamagitan ng markup, mabi<PERSON>gyang-daan ang VoiceOver na ituring ito bilang isang parirala, pero hindi iaanunsyo ang mga nafo-focus na descendent ng element. [Matuto pa tungkol sa attribute na `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "May mga nafo-focus na descendent ang mga element na may attribute na `role=text`."}, "core/audits/accessibility/aria-text.js | title": {"message": "Walang nafo-focus na descendent ang mga element na may attribute na `role=text`."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang toggle field, iaanunsyo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Matuto pa tungkol sa mga toggle field](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga toggle field ng ARIA"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "May mga naa-access na pangalan ang mga toggle field ng ARIA"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang element na tooltip, i<PERSON><PERSON>yo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Alamin kung paano pangalanan ang mga element na `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga element ng `tooltip` ng ARIA."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "May mga naa-access na pangalan ang mga element ng `tooltip` ng ARIA"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang `treeitem` element, iaan<PERSON>yo ito ng mga screen reader gamit ang generic na pangalan, kaya hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Matuto pa tungkol sa paglalagay ng label sa mga element na `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga element ng `treeitem` ng ARIA."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "May mga naa-access na pangalan ang mga element ng `treeitem` ng ARIA"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Hindi mauunawaan ng mga pantulong na teknolohiya, tulad ng mga screen reader, ang mga attribute ng ARIA na may mga invalid na value. [Matuto pa tungkol sa mga valid na values para sa mga attribute na ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Walang valid na value ang mga attribute na`[aria-*]`"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "May valid na value ang mga attribute na `[aria-*]`"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Hindi mauunawaan ng mga pantulong na teknolohiya, tulad ng mga screen reader, ang mga attribute ng ARIA na may mga invalid na pangalan. [Matuto pa tungkol sa mga valid na attribute na ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Hindi valid o hindi mali ang spelling ng mga attribute na `[aria-*]`"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Valid at hindi mali ang spelling ng mga attribute na `[aria-*]`"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Mga Hindi Nakapasang Element"}, "core/audits/accessibility/button-name.js | description": {"message": "Kapag walang accessible na pangalan ang isang button, i<PERSON><PERSON>yo ito ng mga screen reader bilang \"button,\" kaya naman hindi ito magagamit ng mga user na umaasa sa mga screen reader. [Alamin kung paano gawing mas accessible ang mga button](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Walang naa-access na pangalan ang mga button"}, "core/audits/accessibility/button-name.js | title": {"message": "May naa-access na pangalan ang mga button"}, "core/audits/accessibility/bypass.js | description": {"message": "Kapag nagdagdag ng mga paraan para i-bypass ang paulit-ulit na content, mas madaling makakapag-navigate sa page ang mga user ng keyboard. [<PERSON>uto pa tungkol sa pag-bypass sa mga pag-block](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Walang heading, link ng paglaktaw, o rehiyon ng landmark ang page"}, "core/audits/accessibility/bypass.js | title": {"message": "Ang page ay naglalaman ng heading, link ng paglaktaw, o rehiyon ng landmark"}, "core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON><PERSON><PERSON> o imposibleng mabasa ng maraming user ang text na mababa ang contrast. [Alamin kung paano magbigay ng sapat na contrast ng kulay](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Kulang ang ratio ng contrast ng mga kulay ng background at foreground."}, "core/audits/accessibility/color-contrast.js | title": {"message": "May sapat na ratio ng contrast ang mga kulay ng background at foreground"}, "core/audits/accessibility/definition-list.js | description": {"message": "Kapag hindi naka-mark up nang maayos ang mga listahan ng kahulugan, puwedeng gumawa ng nakakalito o hindi tumpak na output ang mga screen reader. [Alamin kung paano isaayos nang tama ang mga listahan ng pagpapakahulugan](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Hindi lang mga grupo ng `<dt>` at `<dd>`, at mga element na `<script>`, `<template>` o `<div>` na nakaayos nang tama ang nilalaman ng `<dl>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Mga grupo lang ng `<dt>` at `<dd>`, at mga element na `<script>`, `<template>` o `<div>` na nakaayos nang tama ang nilalaman ng `<dl>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Na<PERSON>pal<PERSON>b dapat ang mga item sa listahan ng pagpapakahulugan (`<dt>` at `<dd>`) sa isang parent element na `<dl>` para matiyak na maayos na maiaanunsyo ng mga screen reader ang mga ito. [Alamin kung paano isaayos nang tama ang mga listahan ng pagpapakahulugan](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Hindi nakapaloob sa mga element na `<dl>` ang mga item sa listahan ng kahulugan"}, "core/audits/accessibility/dlitem.js | title": {"message": "Nakapaloob sa mga element na `<dl>` ang mga item sa listahan ng kahulugan"}, "core/audits/accessibility/document-title.js | description": {"message": "Binibigyan ng pamagat ang mga user ng screen reader ng pangkalahatang-ideya ng page, at lubos na umaasa rito ang mga user ng search engine para matukoy kung may kaugnayan ang isang page sa kanilang paghahanap. [Matuto pa tungkol sa mga pamagat ng dokumento](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Walang element na `<title>` ang dokumento"}, "core/audits/accessibility/document-title.js | title": {"message": "May `<title>` na element ang dokumento"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Ang lahat ng nafo-focus na element ay dapat na may natatanging `id` para matiyak na nakikita ang mga ito ng mga pantulong na teknolohiya. [Alamin kung paano mag-ayos ng mga duplicate na `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Hindi natatangi ang mga attribute na `[id]` sa aktibo at nafo-focus na element"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Natatangi ang mga attribute na `[id]` sa mga aktibo at nafo-focus na element"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Dapat ay natatangi ang value ng isang ARIA ID para hindi makaligtaan ng mga pantulong na teknolohiya ang iba pang instance. [Alamin kung paano ayusin ang mga duplicate na ARIA ID](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Hindi natatangi ang mga ARIA ID"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Natatangi ang mga ARIA ID"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Napipigilan ng heading na walang content o may hindi naa-access na text ang mga user ng screen reader na ma-access ang impormasyon sa structure ng page. [Matuto pa tungkol sa mga heading](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Walang lamang content ang mga element ng heading."}, "core/audits/accessibility/empty-heading.js | title": {"message": "May lamang content ang lahat ng element ng heading."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Ang mga field ng form na may maraming label ay puwedeng hindi sinasadyang ianunsyo ng mga pantulong na teknolohiya tulad ng mga screen reader na ginagamit ang una, huli, o lahat ng label. [Alamin kung paano gumamit ng mga label ng form](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "May maraming label ang mga field ng form"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Walang field ng form ang may maraming label"}, "core/audits/accessibility/frame-title.js | description": {"message": "<PERSON><PERSON><PERSON> ang mga user ng screen reader sa mga pamagat ng frame para ilarawan ang mga content ng mga frame. [<PERSON><PERSON> pa tungkol sa mga pamagat ng frame](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Walang pamagat ang mga element na`<frame>` o `<iframe>`"}, "core/audits/accessibility/frame-title.js | title": {"message": "May pamagat ang mga elemento na`<frame>` o `<iframe>`"}, "core/audits/accessibility/heading-order.js | description": {"message": "Ipinaparating ng mga mahusay na nakaayos na heading na hindi lumalaktaw ng mga antas ang semantic na istruktura ng page, na mas pinapadali ang pag-navigate at pag-unawa kapag gumagamit ng mga pantulong na teknolohiya. [Matuto pa tungkol sa pagkakasunod-sunod ng heading](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Hindi sunod-sunod na pababa ang pagkakaayos ng mga heading element"}, "core/audits/accessibility/heading-order.js | title": {"message": "Lumalabas ang mga heading element nang sunod-sunod na pababa"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Kung hindi tutukoy ng `lang` attribute ang isang page, ipagpapalagay ng screen reader na ang page ay nasa default na wikang pinili ng user noong sine-set up ang screen reader. Kung wala talaga sa default na wika ang page, puwedeng hindi maianunsyo nang tama ng screen reader ang text ng page. [Matuto pa tungkol sa attribute na `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Walang attribute na `[lang]` ang element na `<html>`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Ang `<html>` na element ay may attribute na `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Ang pagtukoy ng valid na [wika ng BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ay nakakatulong na maianunsyo nang maayos ang text. [<PERSON>amin kung paano gamitin ang attribute na `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Walang valid na value ang element na `<html>` para sa attribute nitong `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "May valid na value ang element na `<html>` para sa `[lang]` na attribute nito"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Kung walang tinutukoy na consistent na wika ang webpage, posibleng hindi maanunsyo nang tama ng screen reader ang text ng page. [<PERSON>uto pa tungkol sa attribute na `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Ang element na `<html>` ay walang attribute na `[xml:lang]` na may parehong batayang wika tulad ng attribute na `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Ang element na `<html>` ay may attribute na `[xml:lang]` na may parehong batayang wika tulad ng attribute na `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "May iisang paglal<PERSON>wan dapat ang mga link na may iisang destina<PERSON>on, para matulungan ang mga user na maunawaan ang layunin ng link at magdesisyon kung susundan ito. [Matuto pa tungkol sa magkakatulad na link](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Hindi magkakapareho ang layunin ng magkakatulad na link."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Magkakapareho ang layunin ng magkakatulad na link."}, "core/audits/accessibility/image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> dapat ng mga nagbibigay-impormasyong element na magkaroon ng maikli at naglalarawang alternatibong text. <PERSON><PERSON><PERSON><PERSON> balewalain ang mga decorative na element sa pamamagitan ng walang lamang kahaliling attribute. [Matuto pa tungkol sa attribute na `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Walang attribute na `[alt]` ang mga element ng larawan"}, "core/audits/accessibility/image-alt.js | title": {"message": "May mga attribute na `[alt]` ang mga element na larawan"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "<PERSON><PERSON><PERSON> dapat ng mga nagbibigay-impormasyong element na magkaroon ng maikli at naglalarawang alternatibong text. Potensyal na ikakalito ng mga user ng screen reader ang alternatibong text na parehong-pareho sa text na katabi ng link o larawan, dahil dalawang beses na mababasa ang text. [<PERSON>uto pa tungkol sa attribute na `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "May mga attribute na `[alt]` na umuulit na text ang mga element na larawan."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Walang attribute na `[alt]` na umuulit na text ang mga element na larawan."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Kapag nagdagdag ng natutukoy at accessible na text sa mga button ng input, posible nitong matulungan ang mga user ng screen reader na maunawaan ang layunin ng button ng input. [Matuto pa tungkol sa mga button ng input](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Walang natutukoy na text ang mga button ng input."}, "core/audits/accessibility/input-button-name.js | title": {"message": "May natutukoy na text ang mga button ng input."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Ka<PERSON>g gumagamit ng larawan bilang button na `<input>`, makakatulong sa mga user ng screen reader ang pagbibigay ng alternatibong text na maunawaan kung para saan ang button. [Matuto pa tungkol sa alt text ng input image](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Walang text na `[alt]` ang mga element na `<input type=\"image\">`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "May text na `[alt]` ang mga element na `<input type=\"image\">`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Puwedeng magresulta ang mga nakikitang label na text na hindi tumutugma sa accessible na pangalan sa isang nakakalitong experience para sa mga user ng screen reader. [Matuto pa tungkol sa mga accessible na pangalan](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Ang mga element na may mga nakikitang label na text ay walang katugmang accessible na pangalan."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Ang mga element na may mga nakikitang label na text ay may mga katugmang accessible na pangalan."}, "core/audits/accessibility/label.js | description": {"message": "Tinitiyak ng mga label na maayos na inaanunsyo ang mga kontrol ng form ng mga pantulong na teknolohiya, tulad ng mga screen reader. [Matuto pa tungkol sa mga label ng element ng form](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Walang nauugnay na label ang mga element ng form"}, "core/audits/accessibility/label.js | title": {"message": "May mga nauugnay na label ang mga element ng form"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Nakakatulong ang isang pangunahing landmark para ma-navigate ng mga user ng screen reader ang isang web page. [Matuto pa tungkol sa mga landmark](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Walang pangunahing landmark ang dokumento."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "May pangunahing landmark ang dokumento."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "<PERSON><PERSON><PERSON> o imposibleng mabasa ng maraming user ang text na mababa ang contrast. Pinapahusay ng text ng link na nakikita ang experience para sa mga user na may malabong paningin. [<PERSON><PERSON> kung paano gagawing natutukoy ang mga link](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> sa kulay para matukoy ang mga link."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Natutukoy ang mga link nang hindi umaasa sa kulay."}, "core/audits/accessibility/link-name.js | description": {"message": "Pinapahusay ng text ng link (at alternatibong text para sa mga larawan, kapag ginamit bilang mga link) na nakikita, natatangi, at nafo-focus ang experience sa navigation para sa mga user ng screen reader. [<PERSON>amin kung paano gawing accessible ang mga link](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Walang nakikitang pangalan ang mga link"}, "core/audits/accessibility/link-name.js | title": {"message": "May nakikitang pangalan ang mga link"}, "core/audits/accessibility/list.js | description": {"message": "May partikular na paraan ng pag-aanunsyo ng mga listahan ang mga screen reader. Makakatulong sa output ng screen reader ang pagtiyak na maayos ang istruktura ng listahan. [Matuto pa tungkol sa angkop na istruktura ng listahan](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Hindi lang naglalaman ang listahan ng mga element na `<li>` at element na sumusuporta sa script (`<script>` at `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Naglalaman lang ang mga listahan ng mga element na `<li>` at element na sumusuporta sa script (`<script>` at `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Kailangang nakapaloob sa parent `<ul>`, `<ol>`, o `<menu>` ang mga item sa listahan (`<li>`) para maayos itong maianunsyo ng mga screen reader. [Matuto pa tungkol sa angkop na istruktura ng listahan](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Hindi nakapaloob ang mga item sa listahan (`<li>`) sa mga parent element na `<ul>`, `<ol>`, o `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Nakapaloob ang mga item sa listahan (`<li>`) sa mga pangunahing element na `<ul>`, `<ol>`, o `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Hindi inaasahan ng mga user na awtomatikong magre-refresh ang isang page, at babalik sa itaas ng page ang focus kapag ginawa ito. Puwede itong gumawa ng nakakainis o nakakalitong experience. [Matuto pa tungkol sa pag-refresh ng meta tag](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Gumagamit ng `<meta http-equiv=\"refresh\">` ang dokumento"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Hindi gumagamit ng `<meta http-equiv=\"refresh\">` ang dokumento"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Nagdu<PERSON>lot ng problema ang pag-disable ng pag-zoom para sa mga user na malabo ang paningin na umaasa sa pag-magnify ng screen para maayos na makita ang mga content ng isang web page. [Matuto pa tungkol sa viewport meta tag](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Ginagamit ang `[user-scalable=\"no\"]` sa element na `<meta name=\"viewport\">` o `[maximum-scale]` na attribute na mas mababa sa 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Hindi ginagamit ang `[user-scalable=\"no\"]` sa element na `<meta name=\"viewport\">` at hindi mas mababa sa 5 ang attribute na `[maximum-scale]`."}, "core/audits/accessibility/object-alt.js | description": {"message": "Hindi makakapagsalin ng hindi text na content ang mga screen reader. Kapag nagdagdag ng alternatibong text sa mga element na `<object>`, matutulungan ang mga screen reader sa pagpaparating ng kahulugan sa mga user. [Matuto pa tungkol sa alt text para sa mga element na `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Walang alternatibong text ang mga element na `<object>`"}, "core/audits/accessibility/object-alt.js | title": {"message": "May alternatibong text ang mga element na `<object>`"}, "core/audits/accessibility/select-name.js | description": {"message": "Ang mga element ng form na walang epektibong label ay puwedeng magdulot ng mga nakakainis na experience para sa mga user ng screen reader. [Matuto pa tungkol sa element na `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Ang mga element ng select ay walang nauugnay na element ng label."}, "core/audits/accessibility/select-name.js | title": {"message": "Ang mga element ng select ay may mga nauugnay na element ng label."}, "core/audits/accessibility/skip-link.js | description": {"message": "Makakatulong ang paglalagay ng link sa paglaktaw para makalaktaw ang mga user sa pangunahing content para makatipid ng oras. [Matuto pa tungkol sa mga link sa paglaktaw](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Hindi nafo-focus ang mga link sa paglaktaw."}, "core/audits/accessibility/skip-link.js | title": {"message": "Nafo-focus ang mga link sa paglaktaw."}, "core/audits/accessibility/tabindex.js | description": {"message": "Nagpapahiwatig ng explicit na pagsasaayos ng navigation ang value na mas mataas sa 0. Bagama't kung tutuusin ay valid ito, madalas itong nagdudulot ng mga nakakainis na experience para sa mga user na umaasa sa mga pantulong na teknolohiya. [Matuto pa tungkol sa attribute na `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Ang ilang element ay may value ng `[tabindex]` na mas mataas sa 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Walang element na may value na `[tabindex]` na mas mataas sa 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Dapat na ilarawan ng attribute na buod ang istruktura ng talahanayan, habang nasa `<caption>` dapat ang pamagat sa screen. Nakakatulong ang tumpak na mark-up ng talahanayan sa mga user ng mga screen reader. [Matuto pa tungkol sa buod at caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Pareho ang content ng mga talahanayan sa attribute na buod at sa `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Magkaiba ang content ng mga talahanayan sa attribute na buod at sa `<caption>`"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "May mga feature ang mga screen reader na mas nagpapadali ng pag-navigate sa mga talahanayan. Kapag tiniyak na ginagamit ng mga talahanayan ang mga aktwal na element ng caption sa halip na ang mga cell na may attribute na `[colspan]`, posibleng mapaganda ang experience para sa mga user ng screen reader. [Matuto pa tungkol sa mga caption](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Hindi gumagamit ng `<caption>` ang mga talahanayan sa halip na mga cell na may attribute na `[colspan]` para magsaad ng caption."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Gumagamit ang mga talahanayan ng `<caption>` sa halip na mga cell na may attribute na `[colspan]` para magsaad ng caption."}, "core/audits/accessibility/target-size.js | description": {"message": "Nakakatulong ang mga pipinduting may sapat na laki at puwang sa mga user na posibleng nahihirapang mag-target ng maliliit na kontrol na i-activate ang mga target. [Matuto pa tungkol sa mga pipindutin](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Walang sapat na laki o puwang ang mga pipindutin."}, "core/audits/accessibility/target-size.js | title": {"message": "May sapat na laki at puwang ang mga pipindutin."}, "core/audits/accessibility/td-has-header.js | description": {"message": "May mga feature ang mga screen reader na mas nagpapadali ng pag-navigate sa mga talahanayan. Kapag tiniyak na may nauugnay na header ng talahanayan ang mga element na `<td>` sa isang malaking talahanayan (3 o higit pang cell sa lapad at taas), posibleng mapaganda ang experience para sa mga user ng screen reader. [Matuto pa tungkol sa mga header ng talahanayan](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Walang header ng talahanayan ang mga element na `<td>` sa isang malaking `<table>`."}, "core/audits/accessibility/td-has-header.js | title": {"message": "May isa o higit pang header ng talahanayan ang mga element na `<td>` sa isang malaking `<table>`."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "May mga feature ang mga screen reader na mas nagpapadali ng pag-navigate sa mga talahanayan. Kapag tiniyak na ang mga cell na `<td>` na gumagamit sa attribute na `[headers]` ay tumutukoy lang sa iba pang cell sa talahanayang ding iyon, puwedeng mapaganda ang experience para sa mga user ng screen reader. [Matuto pa tungkol sa attribute na `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Tumutukoy sa isang element na `id` na hindi makikita sa parehong talahanayan ang mga cell sa element na `<table>` na gumagamit ng attribute na `[headers]`."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Tumutukoy sa iba pang cell sa kaparehong talahanayan ang mga cell sa isang element na `<table>` na gumagamit ng attribute na `[headers]`."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "May mga feature ang mga screen reader na mas nagpapadali ng pag-navigate sa mga talahanayan. Kapag tiniyak na palaging tumutukoy sa ilang hanay ng mga cell ang mga header, puwedeng mapaganda ang experience para sa mga user ng screen reader. [Matuto pa tungkol sa mga header ng talahanayan](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Ang mga element na `<th>` at element na may `[role=\"columnheader\"/\"rowheader\"]` ay walang cell ng data na inilalarawan ng mga ito."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "May mga inilalarawang cell ng data ang mga element na `<th>` at element na may `[role=\"columnheader\"/\"rowheader\"]`."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ang pagtukoy ng valid na [wika ng BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) sa mga element ay nakakatulong sa pagtiyak na tama ang pagbigkas ng screen reader sa text. [<PERSON>amin kung paano gamitin ang attribute na `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Walang valid na value ang mga attribute na `[lang]`"}, "core/audits/accessibility/valid-lang.js | title": {"message": "May valid na value ang mga attribute na `[lang]`"}, "core/audits/accessibility/video-caption.js | description": {"message": "Kapag nagbigay ng caption ang isang video, mas madaling maa-access ng mga user na bingi at may problema sa pandinig ang impormasyon nito. [Matuto pa tungkol sa mga caption ng video](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Hindi naglalaman ng element na `<track>` na may `[kind=\"captions\"]` ang mga element ng `<video>`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Naglalaman ng element na `<track>` na may `[kind=\"captions\"]` ang mga element ng `<video>`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Kasalukuyang Value"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "I<PERSON>umungkahing Token"}, "core/audits/autocomplete.js | description": {"message": "Nakakatulong ang `autocomplete` sa mga user na magsumite ng mga form nang mas mabilis. Para mabawasan ang gawain ng user, pag-isipan ang pag-enable sa pamamagitan ng pagtatakda sa attribute na `autocomplete` sa isang valid na value. [Matuto pa tungkol sa `autocomplete` sa mga form](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Hindi tama ang mga attribute ng mga element ng `<input>` para sa `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Nangangailangan ng manual na pagsusuri"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Suriin ang pagkakasunod-sunod ng mga token"}, "core/audits/autocomplete.js | title": {"message": "Tama ang paggamit ng mga element ng `<input>` sa `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "(Mga) token ng `autocomplete`: Invalid ang \"{token}\" sa {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Suriin ang pagka<PERSON>unod-sunod ng mga token: \"{tokens}\" sa {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | description": {"message": "Maraming pag-navigate ang isinasagawa sa pamamagitan ng pagbalik sa nakaraang page, o pag-forward ulit. Puwedeng mapabilis ng back/forward cache (bfcache) ang mga return navigation na ito. [Matuto pa tungkol sa bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 dahilan ng pagpalya}one{# dahilan ng pagpalya}other{# na dahilan ng pagpalya}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Dahilan ng pagpalya"}, "core/audits/bf-cache.js | failureTitle": {"message": "Pinigilan ng page ang pag-restore ng back/forward cache"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Uri ng pagpalya"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Hindi maaaksyunan"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Nakabinbing suporta sa <PERSON>"}, "core/audits/bf-cache.js | title": {"message": "Hindi pinigilan ng page ang pag-restore ng back/forward cache"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Hindi mate-test ang back/forward cache sa lumang Headless Chrome (`--chrome-flags=\"--headless=old\"`). Para makakita ng mga resulta ng pag-audit, gamitin ang bagong Headless Chrome (`--chrome-flags=\"--headless=new\"`) o karaniwang Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Nagkaroon ng negatibong epekto ang mga extension ng Chrome sa performance ng pag-load ng page na ito. Subukang i-audit ang page sa incognito mode o mula sa isang profile sa Chrome nang walang extension."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Pagsusuri ng Script"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Pag-parse ng Script"}, "core/audits/bootup-time.js | columnTotal": {"message": "Kabuuang Oras ng CPU"}, "core/audits/bootup-time.js | description": {"message": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> bawasan ang oras na ginugugol sa pag-parse, pag-compile, at pag-execute ng JS. Posibleng mapansin mong nakakatulong dito ang pag-deliver ng mas maliliit na payload ng JS. [Alamin kung paano paikliin ang pag-execute ng Javascript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Pabilisin ang pagpapagana ng JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Bilis ng pagpapagana ng JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON> ang mga malaki at duplicate na module ng JavaScript mula sa mga bundle para mabawasan ang mga hindi kinakailangang byte na nakokonsumo ng aktibidad ng network. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Alisin ang mga duplicate na module sa mga bundle ng JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Hindi mahusay ang malalaking GIF sa pag-deliver ng animated na content. Pag-isipang gumamit ng mga MPEG4/WebM na video para sa mga animation at PNG/WebP para sa mga static na larawan sa halip na GIF para makatipid ng mga byte ng network. [Matuto pa tungkol sa mahuhusay na format ng video](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Gumamit ng mga format ng video para sa animated na content"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Nagbibigay-daan ang mga polyfill at transform na magamit ng mga legacy na browser ang mga bagong feature ng JavaScript. <PERSON><PERSON><PERSON><PERSON>, marami ang hindi kinakailangan para sa mga modernong browser. Para sa iyong naka-bundle na JavaScript, gumamit ng modernong diskarte sa deployment ng script gamit ang pag-detect ng feature na module/nomodule para mabawasan ang dami ng code na ipinapadala sa mga modernong browser, habang pinapanatili ang suporta para sa mga legacy na browser. [Alamin kung paano gamitin ang modernong JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> ang paghahatid ng legacy na JavaScript sa mga modernong browser"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Madalas na nakakapagbigay ang mga format ng imahe tulad ng WebP at AVIF ng mas mahusay na pag-compress kaysa sa PNG o JPEG, na nangangahulugang mas mabilis ang mga pag-download at mas kaunti ang magagamit na data. [Matuto pa tungkol sa mga modernong format ng imahe](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Maghatid ng mga larawan sa mga makabagong format"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Pag-isipang bagalan ang pag-load ng mga offscreen at naka-hide na larawan kapag tapos nang mag-load ang lahat ng mahalagang resource para mapabilis ang oras bago maging interactive. [Alamin kung paano ipagpaliban ang mga offscreen na larawan](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Ipagpaliban ang mga larawang wala sa screen"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Bina-block ng mga resource ang first paint ng iyong page. Pag-isipang i-deliver ang mahalagang JS/CSS inline at ipagpaliban ang lahat ng hindi mahalagang JS/istilo. [Alamin kung paano alisin ang mga resource na nagba-block ng pag-render](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Alisin ang mga resource na nagba-block ng pag-render"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Napapagastos ang mga user sa malalaking payload ng network, at malaki ang kaugnayan ng mga ito sa matagal na pag-load. [Alamin kung paano bawasan ang mga laki ng payload](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "<PERSON> kabuuang laki ay {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Iwasan ang malalaking payload ng network"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Umiiwas sa malalaking payload ng network"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Puwedeng bawasan ng pagpapaliit ng mga file ng CSS ang laki ng payload ng network. [Alamin kung paano paliitin ang CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Paliitin ang CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Kapag pinaliit ang mga JavaScript file, puwedeng mabawasan ang laki ng payload at oras ng pag-parse ng script. [Alamin kung paano paliitin ang JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Paliitin ang JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "<PERSON>wasan ang mga hindi ginagamit na panuntunan mula sa mga stylesheet at ipagpaliban ang CSS na hindi ginagamit para sa content sa itaas ng fold para mabawasan ang mga byte na kinokonsumo ng aktibidad ng network. [Alamin kung paano bawasan ang mga hindi ginagamit na CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "<PERSON><PERSON><PERSON> ang hindi ginagamit na CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "<PERSON><PERSON><PERSON> ang hindi ginagamit na JavaScript at ipagpaliban ang pag-load ng mga script hanggang sa kailanganin ang mga ito para mabawasan ang mga byte na kinokonsumo ng aktibidad ng network. [<PERSON>amin kung paano bawasan ang hindi ginagamit na JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> ang hindi ginagamit na JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Puwedeng mapabilis ng mahabang lifetime ng cache ang mga umuulit na pagbisita sa iyong page. [Matuto pa tungkol sa mahuhusay na patakaran sa cache](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Nakakita ng 1 resource}one{Nakakita ng # resource}other{Nakakita ng # na resource}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Maghatid ng mga static na asset nang may mahusay na patakaran sa cache"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Gumagamit ng mahusay na patakaran sa cache sa mga static na asset"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Mas mabilis mag-load ang mga na-optimize na larawan at mas kaunti ang nakokonsumong cellular data ng mga ito. [Alamin kung paano mahusay na mag-encode ng mga larawan](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Mahusay na mag-encode ng mga larawan"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Mga aktwal na dimensyon"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Mga ipinapakitang dimensyon"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Mas malaki ang mga larawan kaysa sa ipinapakitang laki ng mga ito"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Naaangkop ang mga larawan para sa ipinapakitang laki ng mga ito"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Maghatid ng mga larawang naaangkop ang laki para makatipid sa cellular data at mapabilis ang pag-load. [<PERSON>amin kung paano baguhin ang laki ng mga larawan](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Iangkop ang laki ng mga larawan"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Dapat ihatid ang mga text-based na resource nang may compression (gzip, deflate, o brotli) para mabawasan ang kabuuang byte ng network. [Matuto pa tungkol sa pag-compress ng text](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "I-enable ang compression ng text"}, "core/audits/content-width.js | description": {"message": "Kung hindi tumutugma ang lapad ng content ng iyong app sa lapad ng viewport, puwedeng hindi ma-optimize ang app mo para sa mga screen ng mobile. [Alamin kung paano baguhin ang laki ng content para sa viewport](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Hindi tumutugma ang laki ng viewport na {innerWidth}px sa laki ng window na {outerWidth}px."}, "core/audits/content-width.js | failureTitle": {"message": "Hindi tama ang laki ng content para sa viewport"}, "core/audits/content-width.js | title": {"message": "Tama ang laki ng content para sa viewport"}, "core/audits/critical-request-chains.js | description": {"message": "Ipinapakita sa iyo ng Mga Chain ng Mahahalagang Kahilingan kung anong mga resource ang nilo-load nang may mataas na priyoridad. Pag-isipang paikliin ang mga chain, paliitin ang mga dina-download na resource, o ipagpaliban ang pag-download ng mga hindi kinakailangang resource para mapabilis ang pag-load ng page. [<PERSON>amin kung paano iwasan ang pagsusunod-sunod ng mahahalagang kahilingan](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Nakakita ng 1 chain}one{Nakakita ng # chain}other{Nakakita ng # na chain}}"}, "core/audits/critical-request-chains.js | title": {"message": "<PERSON><PERSON><PERSON> ang pag<PERSON>d-sunod ng mga kritikal na kahilingan"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiba"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "Malaki ang nagagawa ng mahigpit na Patakaran sa Seguridad ng Content (Content Security Policy o CSP) para mabawasan ang panganib ng mga cross-site scripting (XSS) na pag-atake. [Alamin kung paano gumamit ng CSP para mapigilan ang XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntax"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Naglalaman ang page ng CSP na tinukoy sa isang `<meta>` tag. Pag-isipang ilipat ang CSP sa header ng HTTP o tumukoy ng ibang mahigpit na CSP sa header ng HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Walang nakitang CSP sa enforcement mode"}, "core/audits/csp-xss.js | title": {"message": "Tiyaking epektibo ang CSP laban sa mga XSS na pag-atake"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Paghinto sa Paggamit / Babala"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Aalisin sa browser ang mga hindi na ginagamit na API kalauanan. [Matuto pa tungkol sa mga hindi na ginagamit na API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{May nakitang 1 babala}one{May nakitang # babala}other{May nakitang # na babala}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Gumagamit ng mga hindi na ginagamit na API"}, "core/audits/deprecations.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang mga hindi na ginagamit na API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Kailangang mag-declare ng pag-encode ng character. Magagawa ito gamit ang isang `<meta>` tag sa unang 1024 na byte ng HTML o sa header ng sagot ng HTTP na Uri ng Content. [Matuto pa tungkol sa pag-declare ng pag-encode ng character](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Walang na-declare na charset o sa bandang dulo na ng HTML lumilitaw"}, "core/audits/dobetterweb/charset.js | title": {"message": "Maayos na tinutukoy ang charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Ang pagtukoy ng doctype ay pumipigil sa browser na lumipat sa quirks-mode. [Matuto pa tungkol sa pag-declare ng doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Ang string na `html` dapat ang pangalan ng doctype"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Naglalaman ang dokumento ng `doctype` na nagti-trigger ng `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dapat maglaman ng doctype ang dokumento"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Walang lamang string ang inaa<PERSON>hang publicId"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Walang lamang string ang inaasahang systemId"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Naglalaman ang dokumento ng `doctype` na nagti-trigger ng `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Walang HTML na doctype ang page, at dahil dito, na-trigger nito ang quirks-mode"}, "core/audits/dobetterweb/doctype.js | title": {"message": "May HTML na doctype ang page"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Istatistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Value"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "<PERSON><PERSON>g malaki ang <PERSON>, madaragdagan ang paggamit ng memory, magkakaroon ng mas mahahabang [pagkalkula ng istilo](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), at makakagawa ito ng mga magastos na [reflow ng layout](https://developers.google.com/speed/articles/reflow). [<PERSON>amin kung paano iwasan ang masyadong malaking DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}one{# element}other{# na element}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> sa masyadong malaking DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximum na Lalim ng DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Kabuuang Element ng DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximum na Mga Child na Elemento"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Umiiwas sa masyadong malaking DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Walang tiwala o nalilito ang mga user sa mga site na humihiling ng kanilang lokasyon nang walang konteksto. Sa halip ay pag-isipang iugnay ang kahilingan sa pagkilos ng user. [Matuto pa tungkol sa pahintulot sa geolocation](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Humihiling ng pahintulot sa geolocation sa pag-load ng page"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang paghiling ng pahintulot sa geolocation sa pag-load ng page"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON> ng isyu"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Nagsasaad ng mga hindi naresolbang problema ang mga isyung naka-log sa panel na `Issues`. Puwedeng magmula ang mga ito sa mga hindi naisagawang kahilingan sa network, hindi sapat na kontrol sa seguridad, at iba pang alalahanin sa browser. <PERSON>uksan ang panel na Mga Isyu sa Chrome DevTools para sa higit pang detalye tungkol sa bawat isyu."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Na-log ang mga isyu sa panel na `Issues` sa Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Na-block ng patakaran sa cross-origin"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Labis na paggamit ng resource ng mga ad"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Walang isyu sa panel na `Issues` sa Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Lahat ng front-end na library ng JavaScript na na-detect sa page. [<PERSON><PERSON> pa tungkol sa diagnostic na pag-audit ng pag-detect ng library ng JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Natukoy na mga library ng JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Para sa mga user na may mabagal na koneksyon, puwedeng maantala nang matagal ang pag-load ng page dahil sa mga external na script na dynamic na inilagay sa pamamagitan ng `document.write()`. [<PERSON>amin kung paano iwasan ang document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ang `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Umiiwas sa `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Walang tiwala o nalilito ang mga user sa mga site na humihiling na magpadala ng mga notification nang walang konteksto. Sa halip ay pag-isipang iugnay ang kahilingan sa mga galaw ng user. [Matuto pa tungkol sa responsableng pagkuha ng pahintulot para sa mga notification](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Humihiling ng pahintulot sa notification sa pag-load ng page"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang paghiling ng pahintulot sa notification sa pag-load ng page"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Ang pagpigil sa pag-paste ng input ay hindi magandang gawi para sa UX, at pinapahina nito ang seguridad sa pamamagitan ng pag-block ng mga password manager.[Matuto pa tungkol sa mga user-friendly na field ng input](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Pinipigilan ang mga user na mag-paste sa mga field ng input"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Pinapayagan ang mga user na mag-paste sa mga field ng input"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Nag-aalok ang HTTP/2 ng mas maraming benepisyo kaysa sa HTTP/1.1, kasama ang mga binary header at multiplexing. [Matuto pa tungkol sa HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 kahilingan ang hindi naihatid sa pamamagitan ng HTTP/2}one{# kahilingan ang hindi naihatid sa pamamagitan ng HTTP/2}other{# na kahilingan ang hindi naihatid sa pamamagitan ng HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Gumamit ng HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Pag-is<PERSON><PERSON> markahan ang iyong mga touch and wheel event listener bilang `passive` para mapahusay ang performance sa pag-scroll ng iyong page. [Matuto pa tungkol sa paggamit ng mga passive na event listener](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Hindi gumagamit ng mga passive na listener para pahusayin ang performance sa pag-scroll"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Gumagamit ng mga passive na listener para pahusayin ang performance sa pag-scroll"}, "core/audits/errors-in-console.js | description": {"message": "Nagsasaad ng mga hindi naresolbang problema ang mga error na naka-log sa console Puwedeng magmula ang mga ito sa mga hindi naisagawang kahilingan sa network at iba pang alalahanin sa browser. [Matuto pa tungkol sa mga error na ito sa diagnostic na pag-audit ng console](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Na-log sa console ang mga error sa browser"}, "core/audits/errors-in-console.js | title": {"message": "Walang naka-log na mga error sa browser sa console"}, "core/audits/font-display.js | description": {"message": "Gamitin ang feature ng CSS na `font-display` para matiyak na makikita ng user ang text habang naglo-load ang mga webfont. [Matuto pa tungkol sa `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Tiyaking patuloy na nakikita ang text sa pag-load ng webfont"}, "core/audits/font-display.js | title": {"message": "Patuloy na nakikita ang lahat ng text sa pag-load ng webfont"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Hindi awtomatikong nasuri ng Lighthouse ang `font-display` value para sa pinagmulang {fontOrigin}.}one{Hindi awtomatikong nasuri ng Lighthouse ang `font-display` value para sa pinagmulang {fontOrigin}.}other{Hindi awtomatikong nasuri ng Lighthouse ang `font-display` na value para sa pinagmulang {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Aspect Ratio (Aktwal)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Aspect Ratio (Ipinakita)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Dapat tumugma ang mga dimensyon ng display ng larawan sa natural na aspect ratio. [Matuto pa tungkol sa aspect ratio ng larawan](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Ipinapakita ang mga larawang may maling aspect ratio"}, "core/audits/image-aspect-ratio.js | title": {"message": "Ipinapakita ang mga larawang may tamang aspect ratio"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Aktwal na laki"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Ipinapakitang laki"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON><PERSON> laki"}, "core/audits/image-size-responsive.js | description": {"message": "Dapat ay kasukat ng mga natural na dimensyon ng larawan ang laki ng display at ratio ng pixel para ma-maximize ang pagiging malinaw ng larawan. [Alamin kung paano magbigay ng mga responsive na larawan](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Nagpapakita ng mga larawang may mababang resolution"}, "core/audits/image-size-responsive.js | title": {"message": "Nagpapakita ng mga larawang may naaangkop na resolution"}, "core/audits/installable-manifest.js | already-installed": {"message": "Naka-install na ang app"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Hindi ma-download ang kinakailangang icon mula sa manifest"}, "core/audits/installable-manifest.js | columnValue": {"message": "Dahilan ng pagpalya"}, "core/audits/installable-manifest.js | description": {"message": "Ang service worker ang tek<PERSON><PERSON><PERSON>yang nagbibigay-daan sa iyong app na gumamit ng maraming feature ng Progressive Web App, tulad ng offline, pagdaragdag sa homescreen, at mga push notification. Gamit ang naaangkop na service worker at mga pagpapatupad ng manifest, puwedeng proactive na i-prompt ng mga browser ang mga user na idagdag ang iyong app sa kanilang homescreen, na puwedeng magresulta sa mas maraming engagement. [Matuto pa tungkol sa mga requirement sa installability ng manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 dahilan}one{# dahilan}other{# na dahilan}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Hindi natutugunan ng manifest ng web app o service worker ang mga kinakailangan sa installability"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Hindi magkatugma ang URL ng app sa Play Store at Play Store ID"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Na-load ang page sa incognito window"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "ng property ng manifest na `display` ay dapat na isa sa `standalone`, `fullscreen`, o `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Naglalaman ang manifest ng field na 'display_override,' at ang unang sinusuportahang display mode ay dapat isa sa 'standalone,' 'fullscreen,' o 'minimal-ui'"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Hindi ma-fetch, walang laman, o hindi ma-parse ang manifest"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Nagbago ang manifest URL habang fine-fetch ang manifest."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Walang lamang field na `name` o `short_name` ang manifest"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Walang lamang naaangkop na icon ang manifest - kinakailangang nasa format na PNG, SVG, o WebP at hindi bababa sa {value0} px dapat nakatakda ang attribute ng mga laki, at kung nakatakda ang attribute ng layunin, may \"any.\""}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Walang ibinigay na icon na may sukat na kahit {value0} px square at nasa format na PNG, SVG, o WebP, na may attribute ng layunin na naka-unset o nakatakda sa \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Walang laman o sira ang na-download na icon"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Walang ibinigay na Play store ID"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Walang URL ng manifest <link> ang page"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Hindi masusuri ang service worker kapag walang field na 'start_url' sa manifest"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Hindi natutukoy ang installability error id na '{errorId}'"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Hindi inihahatid ang page mula sa isang secure na pinagmulan"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Hindi na-load ang page sa pangunahing frame"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Hindi gumagana offline ang page"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Na-uninstall ang PWA, at nire-reset ang mga pagsusuri sa installability."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Hindi sinusuportahan ang tinukoy na platform ng application sa Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Itinatakda ng manifest ang prefer_related_applications sa: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Sinusuportahan lang ang prefer_related_applications sa Chrome Beta at mga Stable na channel sa Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Hindi matukoy ng Lighthouse kung nai-install ang page. Pakisubukan sa mas bagong bersyon ng Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Hindi valid ang start URL ng manifest"}, "core/audits/installable-manifest.js | title": {"message": "Natutugunan ng manifest ng web app at service worker ang mga kinakailangan sa installability"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Naglalaman ang URL sa manifest ng username, password, o port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Hindi gumagana offline ang page. Hindi ituturing na nai-install ang page kapag nailunsad na ang stable release ng Chrome 93 sa Agosto 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON>gan"}, "core/audits/is-on-https.js | blocked": {"message": "Naka-block"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Hindi secure na URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Humiling ng Pasya"}, "core/audits/is-on-https.js | description": {"message": "Dapat protektahan gamit ang HTTPS ang lahat ng site, kahit ang mga hindi nangangasiwa ng sensitibong data. Kasama rito ang pag-iwas sa [halo-halong content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), kung saan nilo-load ang ilang resource sa pamamagitan ng HTTP kahit na inihahatid ang unang kahilingan sa pamamagitan ng HTTPS. Pinipigilan ng HTTPS ang mga nanghihimasok na makialam o tahimik na makinig sa mga pakikipag-ugnayan sa pagitan ng iyong app at mga user, at isa itong prerequisite para sa HTTP/2 at maraming bagong API ng web platform. [Matuto pa tungkol sa HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 hindi secure na kahilingan ang nakita}one{# hindi secure na kahilingan ang nakita}other{# na hindi secure na kahilingan ang nakita}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Hindi gumagamit ng HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Gumagamit ng HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Awtomatikong na-upgrade sa HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "<PERSON><PERSON><PERSON><PERSON> nang may babala"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% ng LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Phase"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Timing"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Ito ang pinakamalaking contentful element na na-paint sa viewport. [Matuto pa tungkol sa element na Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Pagkaantala sa Pag-load"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Oras ng Pag-load"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Pagkaantala sa Pag-render"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element na Largest Contentful Paint"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Epekto ng pagbabago sa layout"}, "core/audits/layout-shift-elements.js | description": {"message": "Ang mga element ng DOM na ito ang pinakanaapektuhan ng mga pagbabago sa layout. Posibleng hindi kasama ang ilang pagbabago sa layout sa metric value ng CLS dahil sa [pag-window](https://web.dev/articles/cls#what_is_cls). [<PERSON>amin kung paano pahusayin ang CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON> ang malalaking pagbabago ng layout"}, "core/audits/layout-shifts.js | columnScore": {"message": "Score ng pagbabago sa layout"}, "core/audits/layout-shifts.js | description": {"message": "Ang mga ito ang pinakamalaking pagbabago sa layout na naobserbahan sa page. Ang bawat item sa talahanayan ay kumakatawan sa isang pagbabago sa layout, at ipinapakita nito ang element na may pinakamalaking pagbabago. Nasa ibaba ng bawat item ang mga posibleng pinag-ugatang dahilan na humantong sa pagbabago sa layout. Posibleng hindi kasama ang ilan sa mga pagbabago sa layout na ito sa metric value ng CLS dahil sa [pag-window](https://web.dev/articles/cls#what_is_cls). [Alamin kung paano pahusayin ang CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{1 pagbabago sa layout ang nakita}one{# pagbabago sa layout ang nakita}other{# na pagbabago sa layout ang nakita}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Na-load na web font"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Na-inject na iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "In-adjust ng nahuling request sa network ang layout ng page"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Walang explicit na laki ang element ng media"}, "core/audits/layout-shifts.js | title": {"message": "<PERSON><PERSON><PERSON> ang malalaking pagbabago ng layout"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Sa lifecycle ng page, mas nahuhuling ma-render ang mga larawan sa itaas ng fold na mabagal ang pag-load, at puwede nitong maantala ang largest contentful paint. [Matuto pa tungkol sa mahusay na mabagal na pag-load](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Mabagal ang pag-load ng Largest Contentful Paint image"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Hindi mabagal ang pag-load ng Largest Contentful Paint image"}, "core/audits/long-tasks.js | description": {"message": "Inililista ang pinakamatatagal na gawain sa pangunahing thread, na kapaki-pakinabang para sa pagtukoy ng mga pinakanakakapagpatagal ng input delay. [Alamin kung paano iwasan ang matatagal na gawain sa pangunahing thread](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# matagal na gawain ang nakita}one{# matagal na gawain ang nakita}other{# na matagal na gawain ang nakita}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON> ang matatagal na gawain sa pangunahing thread"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorya"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Pag-is<PERSON><PERSON> bawasan ang oras na ginugugol sa pag-parse, pag-compile, at pag-execute ng JS. Posibleng mapansin mong nakakatulong dito ang pag-deliver ng mas maliliit na payload ng JS. [<PERSON>amin kung paano bawasan ang pangunahing thread na gawain](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ang gawain sa pangunahing thread"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang gawain sa pangunahing thread"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Para maabot ang pinakamaraming user, dapat gumana ang mga site sa lahat ng pangunahing browser. [Matuto pa tungkol sa cross-browser na compatibility](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Gumagana ang site sa iba't ibang browser"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Tiyaking puwedeng i-deep link ang mga indibidwal na page sa pamamagitan ng URL at natatangi ang mga URL para sa pagbabahagi sa social media. [Matuto pa tungkol sa pagbibigay ng mga deep link](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "May URL ang bawat page"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON><PERSON><PERSON> dapat ang mga transition habang nagta-tap ka, kahit sa mabagal na network. Ang experience na ito ay susi sa pananaw ng user sa performance. [Matuto pa tungkol sa mga transition ng page](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Mukhang hindi nagba-block sa network ang mga transition ng page"}, "core/audits/maskable-icon.js | description": {"message": "Tinitiyak ng nama-mask na icon na nasasakop ng larawan ang buong hugis nang hindi nagiging naka-letterbox kapag ini-install ang app sa isang device. [Matuto pa tungkol sa mga nama-mask na icon ng manifest](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Walang nama-mask na icon ang manifest"}, "core/audits/maskable-icon.js | title": {"message": "May nama-mask na icon ang manifest"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Sinusukat ng Cumulative Layout Shift ang paggalaw ng mga nakikitang element sa loob ng viewport. [Matuto pa tungkol sa sukatang Cumulative Layout Shift](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Minamarkahan ng First Contentful Paint ang tagal bago ma-paint ang unang text o larawan. [Matuto pa tungkol sa sukatang First Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Sinusukat ng First Meaningful Paint ang bilis ng pagpapakita ng pangunahing content ng isang page. [Matuto pa tungkol sa sukatang First Meaningful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Sinusukat ng Interaction to Next Paint ang pagiging responsive ng page, kung gaano katagal bago makitang tumutugon ang page sa input ng user. [Matuto pa tungkol sa sukatang Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Ang Oras bago maging Interactive ay ang panahon bago maging ganap na interactive ang page. [Matuto pa tungkol sa sukatang Oras bago maging Interactive](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Minamarkahan ng Largest Contentful Paint ang tagal bago ma-paint ang pinakamalaking text o larawan. [Matuto pa tungkol sa sukatang Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Ang maximum na potensyal na First Input Delay na puwedeng maranasan ng iyong mga user ay ang tagal ng pinakamatagal na gawain. [Matuto pa tungkol sa sukatang Maximum na Potensyal na First Input Delay](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Ipinapakita ng Speed Index kung gaano kabilis na napo-populate ang mga content ng page. [Matuto pa tungkol sa sukatang Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Kabuuan ng lahat ng yugto ng panahon sa pagitan ng FCP at Oras bago maging Interactive, kapag lumampas ang haba ng gawain sa 50ms, na ipinapahayag sa milliseconds. [Matuto pa tungkol sa sukatang Kabuuang Tagal ng Pag-block](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Malaki ang epekto ng mga round trip time (RTT) ng network sa performance. Kung mataas ang RTT sa isang origin, isa itong palatandaan na mapapahusay ng mga server na mas malapit sa user ang performance. [Matuto pa tungkol sa Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Mga Round Trip Time ng Network"}, "core/audits/network-server-latency.js | description": {"message": "Puwedeng makaapekto sa performance sa web ang mga latency ng server. Kung mataas ang latency ng server ng isang origin, palatandaan itong na-overload ang server o hindi mahusay ang performance nito sa backend. [Matuto pa tungkol sa tagal ng pagtugon ng server](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Mga Latency sa Backend ng Server"}, "core/audits/no-unload-listeners.js | description": {"message": "Hindi palaging gumagana nang maayos ang event na `unload` at puwedeng mapigilan ng paghihintay rito ang mga pag-optimize ng browser tulad ng Back-Forward Cache. Gamitin na lang ang event na `pagehide` o `visibilitychange`. [Matuto pa tungkol sa pag-unload ng mga event listener](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Nagrerehistro ng listener na `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Umiiwas sa mga event listener na `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Kapag hindi na-composite ang animation, posibleng hindi maganda ang kalidad ng mga ito at posible ring mapataas ng mga ito ang CLS. [Alamin kung paano iwasan ang mga hindi na-composite na animation](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animated na element ang nakita}one{# animated na element ang nakita}other{# na animated na element ang nakita}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Posibleng mapagalaw ng property na nauugnay sa filter ang mga pixel"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "May isa pang animation ang target na hindi compatible"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "May composite mode ang effect na bukod pa sa \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "<PERSON><PERSON><PERSON> ang mga hindi na-composite na animation"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Nakadepende sa laki ng box ang property na nauugnay sa pag-transform"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Hindi Sinusuportahang Property ng CSS: {properties}}one{Mga Hindi Sinusuportahang Property ng CSS: {properties}}other{Mga Hindi Sinusuportahang Property ng CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "May mga hindi sinusuportahang parameter ng timing ang effect"}, "core/audits/performance-budget.js | description": {"message": "Panatilihin ang dami at laki ng mga kahilingan sa network sa ilalim ng mga target na itinakda ng ibinigay na badyet sa performance. [Matuto pa tungkol sa mga badyet sa performance](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 kahilingan}one{# kahilingan}other{# na kahilingan}}"}, "core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON>t sa performance"}, "core/audits/preload-fonts.js | description": {"message": "Mag-preload ng mga `optional` na font para magamit ng mga unang beses na bisita ang mga ito. [Matuto pa tungkol sa pag-preload ng mga font](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Hindi na-preload ang mga font na may `font-display: optional`"}, "core/audits/preload-fonts.js | title": {"message": "Na-preload ang mga font na may `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Kung dynamic na idaragdag sa page ang element ng LCP, dapat mong i-preload ang larawan para pahusayin ang LCP. [Matuto pa tungkol sa pag-preload ng mga element ng LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "I-preload ang larawan ng Largest Contentful Paint"}, "core/audits/redirects.js | description": {"message": "Nagpapasimula ang mga pag-redirect ng mga karagdagang pagkaantala bago ma-load ang page. [<PERSON>amin kung paano iwasan ang mga pag-redirect ng page](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "<PERSON><PERSON>an ang mga pag-redirect sa maraming page"}, "core/audits/seo/canonical.js | description": {"message": "Iminumungkahi ng mga canonical na link kung aling URL ang ipapakita sa mga resulta ng paghahanap. [Matuto pa tungkol sa mga canonical na link](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Maraming URL ang hindi magkakatugma ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Invalid na URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Tumuturo sa ibang lokasyon ng `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Ay hindi isang buong URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Tumuturo sa root URL ng domain (ang homepage), sa halip na sa katumbas na page ng content"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Walang valid na `rel=canonical` ang dokumento"}, "core/audits/seo/canonical.js | title": {"message": "May valid na `rel=canonical` ang dokumento"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Hindi Nako-crawl na Link"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Puwedeng gumamit ng mga attribute na `href` ang mga search engine sa mga link para mag-crawl ng mga website. Tiyaking naka-link ang attribute na `href` ng mga anchor element sa isang naaangkop na destinasyon, para mas marami pang page ng site ang matuklasan. [Alamin kung paano gawing nako-crawl ang mga link](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Hindi nako-crawl ang mga link"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Nako-crawl ang mga link"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Karagdagang hindi nababasang text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Laki ng Font"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% ng Text ng Page"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Selector"}, "core/audits/seo/font-size.js | description": {"message": "Ang mga laki ng font na mas mababa sa 12px ay masyadong maliit para mabasa at kinakailangan ng mga bisita sa mobile na “mag-pinch para mag-zoom” para mabasa ito. Subukang gawing ≥12px ang >60% ng text sa page. [Matuto pa tungkol sa mga nababasang laki ng font](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} nababasang text"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Hindi nababasa ang text dahil walang viewport meta tag na naka-optimize para sa mga screen ng mobile."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Hindi gumagamit ng mga nababasang laki ng font ang dokumento"}, "core/audits/seo/font-size.js | legibleText": {"message": "Nababasang text"}, "core/audits/seo/font-size.js | title": {"message": "Gumagamit ng mga nababasang laki ng font ang dokumento"}, "core/audits/seo/hreflang.js | description": {"message": "Sinasabi ng mga link na hreflang sa mga search engine kung anong bersyon ng isang page ang dapat ilista ng mga ito sa mga resulta ng paghahanap para sa isang partikular na wika o rehiyon. [Matuto pa tungkol sa `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Walang valid na `hreflang` ang dokumento"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Kaugnay na value ng href"}, "core/audits/seo/hreflang.js | title": {"message": "May valid na `hreflang` ang dokumento"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Hindi inaasahang code ng wika"}, "core/audits/seo/http-status-code.js | description": {"message": "Puwedeng hindi ma-index nang maayos ang mga page na may mga hindi matagumpay na status code ng HTTP. [Matuto pa tungkol sa mga status code ng HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Hindi matagumpay ang status code ng HTML ng page"}, "core/audits/seo/http-status-code.js | title": {"message": "Matagumpay ang status code ng HTTP ng page"}, "core/audits/seo/is-crawlable.js | description": {"message": "Hindi maisasama ng mga search engine ang iyong mga page sa mga resulta ng paghahanap kung walang pahintulot ang mga ito na i-crawl ang mga iyon. [<PERSON>uto pa tungkol sa mga direktiba ng crawler](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Naka-block ang page mula sa pag-index"}, "core/audits/seo/is-crawlable.js | title": {"message": "Hindi naka-block ang page mula sa pag-index"}, "core/audits/seo/link-text.js | description": {"message": "Nakakatulong ang naglalarawang text ng link para maunawaan ng mga search engine ang iyong content. [Alamin kung paano gawing mas accessible ang mga link](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{May nakitang 1 link}one{May nakitang # link}other{May nakitang # na link}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Walang naglalarawang text ang mga link"}, "core/audits/seo/link-text.js | title": {"message": "May naglalarawang text ang mga link"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Patakbuhin ang [Tool sa Pag-test ng Structured Data](https://search.google.com/structured-data/testing-tool/) at ang [Structured Data Linter](http://linter.structured-data.org/) para i-validate ang structured data. [Matuto pa tungkol sa Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Valid ang structured data"}, "core/audits/seo/meta-description.js | description": {"message": "Puwedeng magsama ng mga paglalarawan ng meta sa mga resulta ng paghahanap para makapagbigay ng maikling buod ng content ng page. [Matuto pa tungkol sa paglalarawan ng meta](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Walang laman ang text ng paglalarawan."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Walang paglalarawan ng meta ang dokumento"}, "core/audits/seo/meta-description.js | title": {"message": "May paglal<PERSON>wan ng meta ang dokumento"}, "core/audits/seo/plugins.js | description": {"message": "Hindi nai-index ng mga search engine ang content ng plugin, at maraming device ang naglilimita sa mga plugin o hindi sumusuporta sa mga ito. [Matuto pa tungkol sa pag-iwas sa mga plugin](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Gumagamit ng mga plugin ang dokumento"}, "core/audits/seo/plugins.js | title": {"message": "Iniiwasan ng dokumento ang mga plugin"}, "core/audits/seo/robots-txt.js | description": {"message": "Kung sira ang iyong robots.txt, puwedeng hindi maunawaan ng mga crawler kung paano mo gustong ma-crawl o ma-index ang iyong website. [Matuto pa tungkol sa robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "<PERSON> para sa robots.txt ay nagbalik ng status ng HTTP na: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{May nakitang 1 error}one{May nakitang # error}other{May nakitang # na error}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Hindi nakapag-download ng robots.txt file ang Lighthouse"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Hindi valid ang robots.txt"}, "core/audits/seo/robots-txt.js | title": {"message": "Valid ang robots.txt"}, "core/audits/seo/tap-targets.js | description": {"message": "Sapat dapat ang laki (48x48px) o mayroon dapat sapat na espasyo sa paligid ang mga interactive na element tulad ng mga button at link, para madaling ma-tap ang mga ito nang hindi nag-o-overlap sa iba pang element. [Matuto pa tungkol sa mga target ng pag-tap](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ng mga target sa pag-tap ang may angkop na laki"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Masyadong maliit ang mga target ng pag-tap dahil walang viewport meta tag na naka-optimize para sa mga screen ng mobile"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Hindi angkop ang laki ng mga target ng pag-tap"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Nag-o-overlap na Target"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Target ng Pag-tap"}, "core/audits/seo/tap-targets.js | title": {"message": "Angkop ang laki ng mga target ng pag-tap"}, "core/audits/server-response-time.js | description": {"message": "Panatilihing mabilis ang pagtugon ng server para sa pangunahing dokumento dahil nakadepende rito ang lahat ng iba pang kahilingan. [Matuto pa tungkol sa sukatang Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Inabot nang {timeInMs, number, milliseconds} ms ang root na dokumento"}, "core/audits/server-response-time.js | failureTitle": {"message": "Pabilisin ang unang pagtugon ng server"}, "core/audits/server-response-time.js | title": {"message": "Ma<PERSON>is ang unang pagtugon ng server"}, "core/audits/splash-screen.js | description": {"message": "Tinitiyak ng splash screen na may tema na magkakaroon ng experience na may mataas na kalidad kapag inilulunsad ng mga user ang iyong app sa kanilang mga homescreen. [Matuto pa tungkol sa mga splash screen](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Hindi naka-configure para sa custom na splash screen"}, "core/audits/splash-screen.js | title": {"message": "Naka-configure para sa custom na splash screen"}, "core/audits/themed-omnibox.js | description": {"message": "Puwedeng lagyan ng tema ang address bar ng browser para tumugma sa iyong site. [Matuto pa tungkol sa pagtatakda ng tema ng address bar](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Hindi nagtatakda ng kulay ng tema para sa address bar."}, "core/audits/themed-omnibox.js | title": {"message": "Nagtatakda ng kulay ng tema para sa address bar."}, "core/audits/third-party-cookies.js | description": {"message": "<PERSON><PERSON>in ang suporta para sa third-party na cookies sa bersyon ng Chrome sa hinaharap. [Alamin pa ang tungkol sa pag-phase out ng third-party na cookies](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{May nakitang 1 cookie}one{May nakitang # cookies}other{May nakitang # na cookies}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Gumagamit ng third-party na cookies"}, "core/audits/third-party-cookies.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ang third-party na cookies"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Tagumpay ng Customer)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkto"}, "core/audits/third-party-facades.js | description": {"message": "Puwedeng bagalan ang pag-load ng ilang third-party na embed. Pag-isipang palitan ang mga ito ng facade hanggang sa kailanganin na ang mga ito. [Alamin kung paano ipagpaliban ang mga third party gamit ang isang facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternatibo sa facade ang available}one{# alternatibo sa facade ang available}other{# na alternatibo sa facade ang available}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Puwedeng mag-lazy load ng facade sa ilang third-party na resource"}, "core/audits/third-party-facades.js | title": {"message": "Mag-lazy load ng mga facade sa mga third-party na resource"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Third-Party"}, "core/audits/third-party-summary.js | description": {"message": "Puwedeng lubos na makaapekto ang third-party na code sa performance ng pag-load. Limitahan ang bilang ng mga paulit-ulit na third-party na provider at subukang i-load ang third-party na code pagkatapos ng pangunahing pag-load ng iyong page. [<PERSON><PERSON><PERSON> kung paano bawasan ang epekto ng third party](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Na-block ng code ng third party ang pangunahing thread sa loob ng {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Bawasan ang epekto ng code ng third party"}, "core/audits/third-party-summary.js | title": {"message": "Bawasan ang paggamit ng third-party"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Sukatan"}, "core/audits/timing-budget.js | description": {"message": "Magtakda ng badyet sa timing para matulungan kang subaybayan ang performance ng iyong site. Mabilis na naglo-load ang mga site na maayos na gumagana at mabilis na tumutugon ang mga ito sa mga input event ng user. [Matuto pa tungkol sa mga badyet sa performance](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Badyet sa timing"}, "core/audits/unsized-images.js | description": {"message": "Magtakda ng malinaw na lapad at taas sa mga element na larawan para mabawasan ang mga pagbabago sa layout at mapahusay ang CLS. [Alamin kung paano magtakda ng mga dimensyon ng larawan](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Walang tahasang `width` at `height` ang mga element na larawan"}, "core/audits/unsized-images.js | title": {"message": "May tahasang `width` at `height`ang mga element na larawan"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Pag-isipang gamitin ang User Timing API sa iyong app para sukatin ang aktwal na performance ng app mo sa mahahalagang experience ng user. [Matuto pa tungkol sa mga marka ng Timing ng User](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 timing ng user}one{# timing ng user}other{# na timing ng user}}"}, "core/audits/user-timings.js | title": {"message": "<PERSON><PERSON> marka at sukat ng User Timing"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "May nakitang `<link rel=preconnect>` para sa \"{securityOrigin}\" pero hindi ito ginamit ng browser. Tingnan kung ginagamit mo nang tama ang attribute na `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Pag-isipang magdagdag ng mga hint ng resource na `preconnect` o `dns-prefetch` para magtakda ng mga paunang koneksyon sa mahahalagang third-party na origin. [Alamin kung paano mag-preconnect sa mga kinakailangang origin](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Mag-preconnect sa mga kinakailangang origin"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Mahigit 2 koneksyong `<link rel=preconnect>` ang nakita. Bihira lang dapat gamitin ang mga ito at tanging sa mga pinakamahalagang pinagmulan lang."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "May nakitang `<link rel=preconnect>` para sa \"{securityOrigin}\" pero hindi ito ginamit ng browser. <PERSON><PERSON><PERSON> lang ang `preconnect` para sa mahahalagang pinagmulan na tiyak na hihilingin ng page."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "May nakitang preload na `<link>` para sa \"{preloadURL}\" pero hindi ito ginamit ng browser. Tingnan kung ginagamit mo nang tama ang attribute na `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Pag-isipang gumamit ng `<link rel=preload>` para mabigyang-priyoridad ang pagkuha ng mga resource na kasalukuyang hinihiling sa huling bahagi ng pag-load ng page. [Alamin kung paano mag-preload ng mahahalagang kahilingan](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "I-preload ang maha<PERSON>gang ka<PERSON>n"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL ng Map"}, "core/audits/valid-source-maps.js | description": {"message": "Tina-translate ng mga source map ang pinaliit na code sa orihinal na source code. Nakakatulong ito sa mga developer na mag-debug sa produksyon. Dagdag pa rito, nakakapagbigay ang Lighthouse ng higit pang insight. Pag-isipang mag-deploy ng mga source map para masulit ang mga benepisyong ito. [Matuto pa tungkol sa mga source map](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Nawawala ang mga source map para sa malaking first-party na JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "May nawawalang source map sa malaking JavaScript file"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Babala: may nawawalang 1 item sa `.sourcesContent`}one{Babala: may nawawalang # item sa `.sourcesContent`}other{Babala: may nawawalang # na item sa `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "May mga valid na source map ang page"}, "core/audits/viewport.js | description": {"message": "Hindi lang ino-optimize ng `<meta name=\"viewport\">` ang iyong app para sa mga laki ng mobile screen, pinipigilan din nito ang [300 millisecond na pagkaantala sa input ng user](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Matuto pa tungkol sa paggamit ng viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Walang nahanap na tag na `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Walang tag na `<meta name=\"viewport\">` na may `width` o `initial-scale`"}, "core/audits/viewport.js | title": {"message": "May tag na `<meta name=\"viewport\">` na may `width` o `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Ito ang pag-block ng thread na nangyayari sa panahon ng pagsukat sa Interaction to Next Paint. [Matuto pa tungkol sa sukatang Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms ang ginugol sa event '{interactionType}'"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Tina-target na event"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Bawasan ang gawain sa panahon ng mahalagang interaction"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Pagkaantala ng input"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Pagkaantala ng presentation"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tagal ng pagpoproseso"}, "core/audits/work-during-interaction.js | title": {"message": "Binabawasan ang gawain sa panahon ng mahalagang interaction"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Mga pagkakataon ito na pahusayin ang paggamit ng ARIA sa iyong application na maaaring mapahusay ang karanasan para sa mga user ng nakakatulong na teknolohiya, tulad ng screen reader."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ito ay mga pagkakataong magbigay ng alternatibong content para sa audio at video. Puwede nitong mapaganda ang karanasan para sa mga user na may mga problema sa paningin o pandinig."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio at video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Hina-highlight ng mga item na ito ang mga karaniwang pinakamahusay na kagawian sa pagiging accessible."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Pinakamahuhusay na kagawian"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Hina-highlight ng mga pagsusuring ito ang mga pagkakataong [pahusayin ang accessibility ng iyong web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Isang subset lang ng mga isyu ang made-detect ng awtomatikong pag-detect at hindi nito magagarantiya ang accessibility ng web app mo, kaya hinihikayat din ang [manual na pagsusuri](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Tinutugunan ng mga item na ito ang mga bahaging hindi masasakop ng naka-automate na tool sa pagsusuri. Matuto pa sa aming gabay sa [pagsasagawa ng pagsusuri sa pagiging accessible](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Pagiging accessible"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Mga pagkakataon ito na pahusayin ang pagiging nababasa ng iyong content."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Mga pagkakataon ito na pahusayin ang pagsasalin ng mga user sa iyong content sa iba't ibang lokal."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Pag-internationalize at pag-localize"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Mga pagkakataon ito ng pahusayin ang mga semantic ng mga kontrol sa iyong application. <PERSON><PERSON><PERSON> nitong pahusayin ang karanasan para sa mga user ng nakakatulong na teknolohiya, tulad ng screen reader."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON> pangalan at label"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ito ay mga pagkakataong pahusayin ang pag-navigate gamit ang keyboard sa iyong application."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ang mga ito ay mga pagkakataong mapahusay ang karanasan ng pagbabasa ng data na nasa talahanayan o listahan gamit ang pantulong na teknolohiya, gaya ng screen reader."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON> at listahan"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibility ng Browser"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Pinakamahuhusay na Ka<PERSON>wian"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Pangkalahatan"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Tiwala at Kaligtasan"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Karanasan ng User"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Nagtatakda ng mga pamantayan para sa performance ng iyong site ang mga badyet ng performance."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Mga Badyet"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Higit pang impormasyon tungkol sa performance ng iyong application. Ang mga numerong ito ay hindi [direktang makakaapekto ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) sa score sa Performance."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Mga Diagnostic"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Ang pinakamahalagang aspeto ng performance ay ang bilis ng pag-render ng mga pixel sa screen. Mahahalagang sukatan: First Contentful Paint, First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Mga Pagpapahusay sa First Paint"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Puwedeng makatulong ang mga suhestyon na ito na mapabilis ang pag-load ng iyong page. Hindi [direktang nakakaapekto](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) ang mga ito sa score sa Performance."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Mga Pagkakataon"}, "core/config/default-config.js | metricGroupTitle": {"message": "Mga Sukatan"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ang pangkalahatang karanasan sa pag-load para bumilis ang pagtugon ng page at magamit ito kaagad. Mahahalagang sukatan: Time to Interactive, Speed Index"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Mga Pangkalahatang Pagpapahusay"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performance"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Vina-validate ng mga pagsusuring ito ang mga aspeto ng isang Progressive Web App. [Alamin kung ano ang mayroon sa isang Progressive Web App](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Kinakailangan ang mga pagsusuring ito ng baseline na [Checklist ng PWA ](https://web.dev/articles/pwa-checklist) pero hindi ito awtomatikong sinusuri ng Lighthouse. Hindi nakakaapekto ang mga ito sa iyong score pero mahalagang ma-verify mo ang mga ito nang manual."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Nai-install"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Na-optimize ang PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Tinitiyak ng mga pagsusuring ito na nakakasunod ang iyong page sa pangunahing payo sa pag-optimize sa search engine. Marami pang karagdagang salik na hindi minamarkahan ng Lighthouse dito na posibleng makaapekto sa iyong ranking sa paghahanap, kabilang ang performance sa [Core Web Vitals](https://web.dev/explore/vitals). [Matuto pa tungkol sa Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Paganahin ang mga karagdagang validator na ito sa iyong site para tingnan ang karagdagang pinakamahuhusay na kagawian sa SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "I-format ang iyong HTML sa paraang nag-e-enable sa mga crawler na mas maunawaan ang content ng app mo."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Pinakamahuhusay na <PERSON> sa <PERSON>"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para lumabas sa mga resulta ng paghahanap, kailangan ng mga crawler ng access sa iyong app."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Pag-crawl at Pag-index"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Tiyaking pang-mobile ang iyong mga page para hindi na kailangang mag-pinch o mag-zoom in ng mga user para mabasa ang mga page ng content. [<PERSON><PERSON>n kung paano gawing pang-mobile ang mga page](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Pang-mobile"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "<PERSON>khang mas mabagal ang CPU ng tine-test na device kaysa sa inaasahan ng Lighthouse. Puwede itong magkaroon ng negatibong epekto sa score ng iyong performance. Matuto pa tungkol sa [pag-calibrate ng naaangkop na CPU slowdown multiplier](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Posibleng hindi naglo-load ang page gaya ng inaasahan dahil na-redirect ang iyong pansubok na URL ({requested}) sa {final}. Direktang subukan ang pangalawang URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Masyadong matagal ang pag-load ng page para matapos sa inilaang limitasyon sa oras. Posibleng hindi kumpleto ang mga resulta."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Nag-time out ang pag-clear sa cache ng browser. Subukang i-audit ulit ang page na ito at mag-file ng bug kung magpapatuloy ang isyu."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Posibleng may naka-store na data na nakakaapekto sa bilis ng pag-load sa lokasyong ito: {locations}. I-audit ang page na ito sa incognito window para hindi maapektuhan ng mga resource na iyon ang iyong mga score.}one{Posibleng may naka-store na data na nakakaapekto sa bilis ng pag-load sa mga lokasyong ito: {locations}. I-audit ang page na ito sa incognito window para hindi maapektuhan ng mga resource na iyon ang iyong mga score.}other{Posibleng may naka-store na data na nakakaapekto sa bilis ng pag-load sa mga lokasyong ito: {locations}. I-audit ang page na ito sa incognito window para hindi maapektuhan ng mga resource na iyon ang iyong mga score.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Nag-time out ang pag-clear sa data ng origin. Subukang i-audit ulit ang page na ito at mag-file ng bug kung magpapatuloy ang isyu."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Error sa pag-parse ng `link` header ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "May na-detect na nabigasyon ng page habang gumagana. Hindi inirerekomenda ang paggamit ng timespan mode para mag-audit ng mga nabigasyon ng page. Gamitin ang navigation mode para mag-audit ng mga nabigasyon ng page para sa mas mahusay na third-party na attribution at pag-detect ng pangunahing thread."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Ang mga page lang na nag-load sa pamamagitan ng request gamit ang GET ang kwalipikado para sa back/forward cache."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Ang mga page lang na may status code na 2XX ang puwedeng i-cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Naka-detect ang Chrome ng pagsubok na i-execute ang JavaScript habang nasa cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng AppBanner."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Naka-disable ang back/forward cache dahil sa isang keepalive na request."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "<PERSON><PERSON>-disable ng mga pag-flag ang back/forward cache. Bumisita sa chrome://flags/#back-forward-cache para lokal itong i-enable sa device na ito."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON>-disable ng command line ang back/forward cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Naka-disable ang back/forward cache dahil sa hindi sapat na memory."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Hindi sinusuportahan ng pag-delegate ang back/forward cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Naka-disable ang back/forward cache para sa prerenderer."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Hindi puwedeng i-cache ang page dahil mayroon itong instance na BroadcastChannel na may mga nakarehistrong listener."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may header na cache-control:no-store."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON> na-clear ang cache."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Inalis sa cache ang page para mapayagang ma-cache ang isa pang page."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na naglalaman ng mga plugin."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng FileChooser API."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng File System Access API."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Dispatcher ng Media Device."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "May nagpe-play na media player noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng MediaSession API at nagtakda ng status ng pag-playback."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng MediaSession API at nagtakda ng mga tagapangasiwa ng pagkilos."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Naka-disable ang back/forward cache dahil sa screen reader."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SecurityHandler."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Serial API."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebAuthentication API."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebBluetooth API."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebUSB API."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Naka-disable ang back/forward cache dahil naka-disable ang cookies sa isang page na gumagamit ng `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng nakalaang worker o worklet."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Hindi natapos mag-load ang dokumento bago nag-navigate paalis dito."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "May App Banner noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "May Password Manager ng Chrome noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Isinasagawa ang DOM distillation noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "May DOM Distiller Viewer noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Naka-disable ang back/forward cache dahil gumagamit ang mga extension ng messaging API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Dapat isara ng mga extension na may long-lived na koneksyon ang koneksyon bago pumasok sa back/forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Sinubukang magpadala ng mga extension na may long-lived na koneksyon ng mga mensahe sa mga frame sa back/forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Naka-disable ang back/forward cache dahil sa mga extension."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Ipinakita ang modal dialog, gaya ng dialog para sa pagsusumite ulit ng form o http password, para sa page noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Ipinakita ang offline na page noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "May Out-Of-Memory Intervention bar noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "May mga request sa pahintulot noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "May popup blocker noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Ipinakita ang mga detalye ng Ligtas na Pag-browse noong nag-navigate paalis."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Tinukoy ng Ligtas na Pag-browse ang page na ito bilang mapang-abuso at nag-block ito ng popup."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "May service worker na na-activate habang nasa back/forward cache ang page."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Naka-disable ang back/forward cache dahil sa isang error sa dokumento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Hindi puwedeng i-store sa bfcache ang mga page na gumagamit ng FencedFrames."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Inalis sa cache ang page para mapayagang ma-cache ang isa pang page."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagbigay ng access sa pag-stream ng media."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Kasal<PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng mga portal."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng IdleManager."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na may bukas na IndexedDBConnection."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Naka-disable ang back/forward cache dahil sa event na IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "May ginamit na mga hindi kwalipikadong API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page kung saan na-inject ng mga extension ang `JavaScript`."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page kung saan na-inject ng mga extension ang `StyleSheet`."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Internal na error."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Naka-disable ang back/forward cache dahil sa isang keepalive na request."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na gumagamit ng KeyboardLock."}, "core/lib/bf-cache-strings.js | loading": {"message": "Hindi natapos mag-load ang page bago nag-navigate paalis dito."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may pangunahing resource na may cache-control:no-cache."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may pangunahing resource na may cache-control:no-store."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON> ang pag-navigate bago ma-restore ang page mula sa back/forward cache."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Inalis sa cache ang page dahil may aktibong koneksyon ng network na nakatanggap ng masyadong maraming data. Nililimitahan ng Chrome ang dami ng data na puwedeng matanggap ng page habang naka-cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON><PERSON> hindi kwalipikado para sa back/forward cache ang mga page na may inflight na fetch() o XHR)."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Inalis sa back/forward cache ang page dahil may pag-redirect ang isang request sa aktibong network."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Inalis sa cache ang page dahil masyadong matagal na nakabukas ang isang koneksyon ng network. Nililimitahan ng Chrome ang haba ng oras na puwedeng makatanggap ng data ang isa page habang naka-cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na walang valid na header ng sagot."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON><PERSON><PERSON> ang pag-navigate sa frame bukod pa sa pangunahing frame."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang page na may mga kasalukuyang naka-index na transaksyon sa DB."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na request sa network."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na may in-flight na request sa fetch network."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na request sa network."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Kasalukuyang hindi available para sa back/forward cache ang mga page na may in-flight na request sa network gamit ang XHR."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng PaymentManager."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng Picture-in-Picture."}, "core/lib/bf-cache-strings.js | portal": {"message": "Kasal<PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng mga portal."}, "core/lib/bf-cache-strings.js | printing": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagpapakita ng UI sa Pag-print."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "<PERSON><PERSON><PERSON> ang page gamit ang '`window.open()`' at may ibang tab na tumutukoy rito, o may window na binuksan ang page."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Nag-crash ang proseso ng pag-render para sa page sa back/forward cache."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Inihinto ang proseso ng pag-render para sa page na nasa back/forward cache."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Kasal<PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa pag-capture ng audio."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa sensor."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa pag-sync o pag-fetch sa background."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa MIDI."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa mga notification."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng access sa storage."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nag-request ng mga pahintulot sa pag-capture ng video."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Ang mga page lang na may scheme ng URL na HTTP / HTTPS ang puwedeng i-cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Na-claim ng service worker ang page habang nasa back/forward cache ito."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "May service worker na sumubok na magpadala ng `MessageEvent` sa page na nasa back/forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Na-unregister ang ServiceWorker habang nasa back/forward cache ang isang page."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Inalis sa back/forward cache ang page dahil sa pag-activate ng service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Ni-restart at na-clear ng Chrome ang mga entry sa back/forward cache."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Kasal<PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SharedWorker."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SpeechRecognizer."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng SpeechSynthesis."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "May iframe sa page na nagsimula ng pag-navigate na hindi natapos."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may subresource na may cache-control:no-cache."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may subresource na may cache-control:no-store."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Lumampas ang page sa maximum na oras sa back/forward cache at nag-expire na ito."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Nag-time out ang page habang pumapasok sa back/forward cache (malamang na dahil sa mga matagal na tumatakbong tagapangasiwa ng pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Ang page ay may tagapangasiwa ng pag-unload sa pangunahing frame."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Ang page ay may tagapangasiwa ng pag-unload sa sub frame."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Binago ng browser ang header ng override ng user agent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na nagbigay ng access sa pag-record ng video o audio."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Kasalukuyang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebDatabase."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebHID."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebLocks."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebNfc."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Kasalukuyang hindi kwalipikado para sa bfcache ang mga page na gumagamit ng WebOTPService."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebRTC."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebShare."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebSocket."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Hindi puwedeng pumasok sa back/forward cache ang mga page na may WebTransport."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Ka<PERSON><PERSON>yang hindi kwalipikado para sa back/forward cache ang mga page na gumagamit ng WebXR."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Pag-isipang magdagdag ng https: at http: na URL scheme (binabalewala ng mga browser na sumusuporta sa `'strict-dynamic'`) para maging backward compatible sa mga lumang browser."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Hindi na ginagamit ang `disown-opener` simula sa CSP3. Pakigamit na lang ang header na Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Hindi na ginagamit ang `referrer` simula sa CSP2. Pakigamit na lang ang header na Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Hindi na ginagamit ang `reflected-xss` simula sa CSP2. Pakigamit na lang ang header na X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Binibigyang-daan ng nawawalang `base-uri` ang mga na-inject na `<base>` na tag na itakda ang base na URL para sa lahat ng relative URL (hal. mga script) sa isang domain na kinokontrol ng attacker. Pag-isipang itakda ang `base-uri` sa `'none'` o `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Pinapayagan ng kulang na `object-src` ang pag-inject ng mga plugin na nag-e-execute ng mga hindi ligtas na script. Pag-isipang itakda ang `object-src` sa `'none'` kung magagawa mo."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Nawawala ang direktibang `script-src`. Puwede nitong bigyang-daan ang pag-execute ng mga hindi ligtas na script."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "<PERSON><PERSON><PERSON><PERSON>n mo ba ang semicolon? Mukhang direktiba ang {keyword} at hindi isang keyword."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Dapat gamitin ng mga nonce ang base64 na charset."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Dapat ay hindi bababa sa 8 character ang haba ng mga nonce."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Iwasang gumamit ng mga plain na URL scheme ({keyword}) sa direktibang ito. Pinapayagan ng mga plain na URL scheme na ma-source ang mga script mula sa hindi ligtas na domain."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Iwasang gumamit ng mga plain na wildcard ({keyword}) sa direktibang ito. Pinapayagan ng mga plain na wildcard na ma-source ang mga script mula sa hindi ligtas na domain."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Nako-configure lang ang destinasyon ng pag-uulat sa pamamagitan ng report-to na direktiba. Sinusuportahan lang ang direktibang ito sa mga browser na batay sa Chromium kung kaya inirerekomendang gumamit din ng `report-uri` na direktiba."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Walang CSP na nagko-configure ng destinasyon ng pag-uulat. Pinapahirap nitong mapanatili ang CSP sa paglipas ng panahon at masubaybayan ang anumang pagkasira."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Puwed<PERSON> madalas na ma-bypass ang mga allowlist ng host. Pag-isipang gumamit ng mga CSP nonce o hash sa halip, kasama ng `'strict-dynamic'` kung kinakailangan."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Hindi kilalang direktiba ng CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON> invalid na keyword ang {keyword}."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Nagbibigay-daan ang `'unsafe-inline'` sa pag-execute ng mga hindi ligtas na in-page na script at tagapangasiwa ng event. Pag-isipang gumamit ng mga CSP nonce o hash para indibidwal na payagan ang mga script."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Pag-isipang magdagdag ng `'unsafe-inline'` (binabalewala ng mga browser na sumusuporta sa mga nonce/hash) para maging backward compatible sa mga lumang browser."}, "core/lib/deprecation-description.js | feature": {"message": "Tingnan ang page ng status ng feature para sa higit pang detalye."}, "core/lib/deprecation-description.js | milestone": {"message": "Magkakaroon ng bisa ang pagbabagong ito sa milestone na {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Gumagamit ng Hindi na Ginagamit na Feature"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Hindi sasaklawin ang pagpapahintulot ng simbolong wildcard (*) sa pangangasiwa ng `Access-Control-Allow-Headers` sa CORS."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Dapat gamitin ang attribute na `disableRemotePlayback` para i-disable ang default na integration ng Cast sa halip na gamitin ang selector na `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Naka-block ang mga kahiligan sa resource na may mga URL na naglalaman ng mga inalis na whitespace `(n|r|t)` character at less-than character (`<`). Pakialis ang mga newline at i-encode ang mga less-than character mula sa mga lugar tulad ng mga value na attribute ng element para ma-load ang mga resource na ito."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Hindi na ginagamit ang `chrome.loadTimes()`, standardized na API: Navigation Timing 2 na lang ang gamitin."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Hindi na ginagamit ang `chrome.loadTimes()`, standardized na API: Paint Timing na lang ang gamitin."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Hindi na ginagamit ang `chrome.loadTimes()`, standardized na API: `nextHopProtocol` sa Navigation Timing 2 na lang ang gamitin."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Tatanggihan sa halip na puputulin ang cookies na naglalaman ng `(0|r|n)` character."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Hindi na ginagamit, at madi-disable nang default ang pagpapahinga ng patakaran sa iisang origin sa pamamagitan ng pagtatakda sa `document.domain`. Ang babalang ito sa paghinto sa paggamit ay para sa isang cross-origin na access na na-enable sa pamamagitan ng pagtatakda sa `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Hindi na ginagamit ang pag-trigger ng window.alert mula sa mga cross origin na iframe at aalisin na ito sa hinaharap."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Hindi na ginagamit ang pag-trigger ng window.confirm mula sa mga cross origin na iframe at aalisin na ito sa hinaharap."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Hindi na ginagamit at aalisin na ang Mga Event ng DOM Mutation, kasama ang `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument`, at `DOMCharacterDataModified` (https://w3c.github.io/uievents/#legacy-event-types). `MutationObserver` na lang ang gamitin."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Suporta para sa data: Hindi na ginagamit ang mga URL sa SVG <use> na element at aalisin na ito sa hinaharap."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Hindi na ginagamit, at madi-disable nang default ang pagpapahinga ng patakaran sa iisang origin sa pamamagitan ng pagtatakda sa `document.domain`. Para patuloy na magamit ang feature na ito, mag-opt out sa mga origin-keyed agent cluster sa pamamagitan ng pagpapadala ng header na `Origin-Agent-Cluster: ?0` kasama ng sagot ng HTTP para sa dokumento at mga frame. Tingnan ang https://developer.chrome.com/blog/immutable-document-domain/ para sa higit pang detalye."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Hindi na ginagamit at aalisin na ang `Expect-CT` na header. Iniaatas ng Chrome ang Transparency ng Certificate para sa lahat ng pampublikong pinagkakatiwalaang certificate na ibinigay pagkalipas ng Abril 30, 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Hindi na gumagana ang `getCurrentPosition()` at `watchPosition()` sa mga hindi secure na origin. Para magamit ang feature na ito, pag-isipang ilipat ang iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Hindi na ginagamit ang `getCurrentPosition()` at `watchPosition()` sa mga hindi secure na origin. Para magamit ang feature na ito, pag-isipang ilipat ang iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "Hindi na gumagana ang `getUserMedia()` sa mga hindi secure na origin. Para magamit ang feature na ito, pag-isipang ilipat ang iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Hindi na ginagamit ang `RTCPeerConnectionIceErrorEvent.hostCandidate`. `RTCPeerConnectionIceErrorEvent.address` o `RTCPeerConnectionIceErrorEvent.port` na lang ang gamitin."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Ang origin ng merchant at arbitrary na data mula sa `canmakepayment` na event ng service worker ay hindi na ginagamit at aalisin: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Humiling ang website ng subresource mula sa isang network na maa-access lang nito dahil sa privileged na posisyon ng network ng mga user nito. Ine-expose ng mga kahilingang ito ang mga hindi pampublikong device at server sa internet, na nagpapataas sa panganib ng cross-site request forgery (CSRF) na pag-atake, at/o pag-leak ng impormasyon. Para mapigilan ang mga panganib na ito, hindi na gagamitin ng Chrome ang mga kahilingan sa mga hindi pampublikong subresource kapag sinimulan mula sa mga hindi secure na konteksto, at magsisimula itong i-block ang mga iyon."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Na-rename sa `updateUrl` ang field ng `dailyUpdateUrl` ng `InterestGroups` na ipinasa sa `joinAdInterestGroup()`, para mas tumpak na maipakita ang gawi nito."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Hindi ma-load ang CSS mula sa mga URL ng `file:` maliban na lang kung nagtatapos ang mga ito sa file extension na `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Hindi na ginagamit ang paggamit ng `SourceBuffer.abort()` para i-abort ang asynchronous na pag-aalis ng sakop ng `remove()` dahil sa pagbabago ng detalye. Aalisin ang suporta sa hinaharap. Dapat mong pakinggan na lang ang `updateend`. Nilalayon lang ng `abort()` na i-abort ang asynchronous na pagdaragdag ng media o i-reset ang status ng pang-parse."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Hindi na ginagamit ang pagtatakda ng `MediaSource.duration` sa ibaba ng pinakamataas na timestamp ng presentation ng anumang naka-buffer at naka-code na frame dahil sa pagbabago ng detalye. Aalisin ang suporta para sa direktang pag-aalis ng naputol at naka-buffer na media sa hinaharap. <PERSON> halip, dapat kang magsagawa ng direktang `remove(newDuration, oldDuration)` sa lahat ng `sourceBuffers`, kung saan `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Hihingi ng pahintulot ang Web MIDI sa paggamit kahit na hindi tinukoy ang sysex sa `MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Hindi na ginagamit ang luma at hindi standardized na attribute na `shadowroot` at *hindi na ito gagana* sa M119. Pakigamit na lang ang bago at standardized na attribute na `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Posibleng hindi na magamit ang Notification API mula sa mga hindi secure na origin. Pag-isipang ilipat ang iyong application sa isang secure na origin, tulad ng HTTPS. Tingnan ang https://goo.gle/chrome-insecure-origins para sa higit pang detalye."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Posibleng hindi na humiling ng pahintulot para sa Notification API mula sa isang cross-origin na iframe. Pag-isipang humiling ng pahintulot mula sa isang top-level na frame o magbukas na lang ng bagong window."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Hindi na ginagamit ang opsyong `imageOrientation: 'none'` sa createImageBitmap. Pakigamit na lang ang createImageBitmap na may opsyong \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Nagne-negotiate ang iyong partner ng lumang bersyon ng (D)TLS. Sumangguni sa iyong partner para maayos ito."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Kapag tinukoy ang `overflow: visible` sa mga tag ng img, video, at canvas, posibleng gumawa ang mga ito ng visual na content sa labas ng mga hangganan ng element. Tingnan ang https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "Hindi na ginagamit ang `paymentManager.instruments`. Gumamit na lang ng just-in-time na pag-install para sa mga tagapangasiwa ng pagbabayad."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Na-bypass ng iyong call na `PaymentRequest` ang direktibang `connect-src` ng Content-Security-Policy (CSP). Hindi na ginagamit ang pag-bypass na ito. Magdagdag ng identifier ng paraan ng pagbabayad mula sa `PaymentRequest` API (sa field na `supportedMethods`) sa iyong direktibang `connect-src` ng CSP."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Hindi na ginagamit ang `StorageType.persistent`. Pakigamit na lang ang standardized na `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Invalid at binabalewala ang `<source src>` na may parent na `<picture>`. `<source srcset>` na lang ang gamitin."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Partikular sa vendor ang webkitCancelAnimationFrame. Pakigamit na lang ang karaniwang cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Partikular sa vendor ang webkitRequestAnimationFrame. Pakigamit na lang ang karaniwang requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitDisplayingFullscreen. Pakigamit na lang ang Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitEnterFullScreen(). Pakigamit na lang ang Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitEnterFullscreen(). Pakigamit na lang ang Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitExitFullScreen(). Pakigamit na lang ang Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitExitFullscreen(). Pakigamit na lang ang Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Hindi na ginagamit ang HTMLVideoElement.webkitSupportsFullscreen. Pakigamit na lang ang Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Hindi na namin gagamitin ang API na `chrome.privacy.websites.privacySandboxEnabled`, pero mananatili itong aktibo para sa backward compatibility hanggang sa pag-release ng M113. Sa halip, pakigamit ang `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled`, at `chrome.privacy.websites.adMeasurementEnabled`. Tingnan ang https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Inalis ang paghihigpit na `DtlsSrtpKeyAgreement`. Tinukoy mo ang isang `false` na value para sa paghihigpit na ito, na itinuturing na pagsubok na gamitin ang inalis na paraan ng `SDES key negotiation`. Inalis ang functionality na ito; isang serbisyong sumusuporta sa `DTLS key negotiation` na lang ang gamitin."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Inalis ang paghihigpit na `DtlsSrtpKeyAgreement`. Tinukoy mo ang isang `true` na value para sa paghihigpit na ito, na walang epekto, pero puwede mong alisin ang paghihigpit na ito para sa kaayusan."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Hindi na ginagamit at aalisin na ang nakabatay sa callback na getStats(). Gamitin na lang ang sumusunod sa spec na getStats()."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Hindi na ginagamit ang Range.expand(). Pakigamit na lang ang Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Naka-block ang mga kahilingan sa subresource na may mga URL na naglalaman ng mga naka-embed na kredensyal (hal. `**********************/`)."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Hindi na ginagamit at aalisin na ang opsyong `rtcpMuxPolicy`."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "Mangangailangan ang `SharedArrayBuffer` ng pag-isolate ng cross-origin. Tingnan ang https://developer.chrome.com/blog/enabling-shared-array-buffer/ para sa higit pang detalye."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Hindi na ginagamit at aalisin ang `speechSynthesis.speak()` nang walang pag-activate ng user."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Dapat mag-opt in ang mga extension sa mga pag-isolate ng cross-origin para patuloy na magamit ang `SharedArrayBuffer`. Tingnan ang https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Hindi na ginagamit ang Web SQL. Gumamit ng SQLite WebAssembly o Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Hindi na ginagamit ang `window-placement` na descriptor ng pahintulot. `window-management` na lang ang gamitin. Para sa higit pang tulong, tingnan ang https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Hindi na ginagamit ang `window-placement` na patakaran sa pahintulot. `window-management` na lang ang gamitin. Para sa higit pang tulong, tingnan ang https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "Hindi sinusuportahan ang UTF-16 ng sagot na json sa `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Hindi na ginagamit ang synchronous na `XMLHttpRequest` sa pangunahing thread dahil sa mga nakakapinsalang epekto nito sa experience ng end user. Para sa higit pang tulong, tingnan ang https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Hindi na ginagamit ang `supportsSession()`. <PERSON> halip, pakigamit ang `isSessionSupported()` at tingnan ang nalutas na boolean value."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Oras ng Pag-block ng Pangunahing Thread"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL ng Cache"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Tagal"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Mga Hindi Nakapasang Element"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Lokasyon"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Lampas sa Badyet"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Laki ng Resource"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON>ri ng Resource"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Pinagmulan"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Oras ng Pagsisimula"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Oras na Ginugol"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Laki ng Paglipat"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Puwedeng makatipid ng {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element ang nakita}one{# element ang nakita}other{# na element ang nakita}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "<PERSON><PERSON><PERSON><PERSON> makatipid ng {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Doku<PERSON>"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Mababa"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Max na Potensyal na First Input Delay"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Iba pa"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Iba pang resource"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Third-party"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Kabu<PERSON>"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Nagkaroon ng problema sa pag-record ng trace sa pag-load ng iyong page. Paganahin ulit ang Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Nag-timeout habang nagh<PERSON> para sa paunang koneksyon sa Protocol ng Debugger."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Hindi nangolekta ang Chrome ng anumang screenshot habang nilo-load ang page. Pakitiyak na may nakikitang content sa page, at pagkatapos ay subukang paganahin ulit ang Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Hindi malutas ng mga DNS server ang ibinigay na domain."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON> kinakailangang gatherer na {artifactName} ay nagkaroon ng error: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Nagkaroon ng internal na error sa Chrome. Paki-restart ang Chrome at subukan ulit na paganahin ang Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Hindi tumakbo ang kinakailangang gatherer na {artifactName}."}, "core/lib/lh-error.js | noFcp": {"message": "Hindi nag-paint ang page ng anumang content. Pakitiyak na papanatilihin mong nasa foreground ang window ng browser habang naglo-load at subukan ulit. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Hindi nagpakita ang page ng content na kwalipikado bilang Largest Contentful Paint (LCP). Tiyaking may valid na element ng LCP ang page at pagkatapos ay subukan ulit. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Hindi HTML ang ibinigay na page (inihatid bilang uri ng MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Masyadong luma ang bersyong ito ng Chrome para suportahan ang '{featureName}.' Gumamit ng mas bagong bersyon para makita ang mga buong resulta."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Hindi na-load nang maayos ng Lighthouse ang page na hiniling mo. Tiyaking tamang URL ang sinusubukan mo at tumutugon nang maayos ang server sa lahat ng kahilingan."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Hindi na-load nang maayos ng Lighthouse ang URL na hiniling mo dahil huminto sa pagtugon ang page."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Walang valid na panseguridad na certificate ang URL na ibinigay mo. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Pinigilan ng Chrome ang pag-load ng page gamit ang interstitial. Tiyaking tamang URL ang sinusubukan mo at tumutugon nang maayos ang server sa lahat ng kahilingan."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Hindi na-load nang maayos ng Lighthouse ang page na hiniling mo. Tiyaking tamang URL ang sinusubukan mo at tumutugon nang maayos ang server sa lahat ng kahilingan. (Mga Detalye: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Hindi na-load nang maayos ng Lighthouse ang page na ni-request mo. Tiyaking tamang URL ang sinusubukan mo at tumutugon nang maayos ang server sa lahat ng request. (Status code: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Masyadong matagal na na-load ang iyong page. Pakisunod ang mga pagkakataon sa ulat para mabawasan ang tagal ng pag-load ng iyong page, at pagkatapos ay paganahin ulit ang Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Lumampas na sa nakalaang oras ang paghihintay ng tugon ng DevTools protocol. (Pamamaraan: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Lumampas na sa nakalaang panahon ang pag-fetch ng content ng resource"}, "core/lib/lh-error.js | urlInvalid": {"message": "Mukhang invalid ang URL na ibinigay mo."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Hindi na-load nang maayos ng Lighthouse ang page na ni-request mo. Tiyaking tamang URL ang sinusubukan mo at tumutugon nang maayos ang server sa lahat ng request. (Status code: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "XHTML ang uri ng MIME ng page: Hindi explicit na sinusuportahan ng Lighthouse ang uri ng dokumentong ito"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON><PERSON> user ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON> ng pag-navigate ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Ulat ng snapshot ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Ulat ng tagal ng panahon ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Lahat ng Ulat"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Mga Kategorya"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Pagiging accessible"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Pinakamahuhusay na Ka<PERSON>wian"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performance"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Desktop"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Pag-unawa sa Ulat ng Daloy ng Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Pag-unawa sa Mga Daloy"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "G<PERSON><PERSON> ang Mga ulat ng pag-navigate para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "G<PERSON>tin ang Mga ulat ng snapshot para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "G<PERSON>tin ang Mga ulat ng tagal ng panahon para..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Makakuha ng score sa Performance sa Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON><PERSON> ang mga sukatan ng Performance ng pag-load ng page gaya ng Largest Contentful Paint at Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Suriin ang mga kakayahan ng Progressive Web App."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Maghanap ng mga isyu sa accessibility sa mga single page application o kumplikadong form."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Suriin ang mga pinakamahuhusay na kagawian ng mga menu at element ng UI na nakatago sa likod ng pakikipag-ugnayan."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "<PERSON><PERSON><PERSON> ang mga pagbabago sa layout at tagal ng pag-execute sa JavaScript sa isang serye ng mga pakikipag-ugnayan."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Tumuklas ng mga pagkakataon sa performance para pagandahin ang karanasan para sa mga long-lived na page at single-page application."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Pinakamalaking epekto"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} nagbibigay impormasyong audit}one{{numInformative} nagbibigay impormasyong audit}other{{numInformative} na nagbibigay impormasyong audit}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobile"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Pag-load ng page"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Nagsusuri ang mga ulat ng pag-navigate ng isang pag-load ng page, na eksaktong kagaya ng mga orihinal na ulat ng Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON> ng pag-navigate"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} ulat ng pag-navigate}one{{numNavigation} ulat ng pag-navigate}other{{numNavigation} na ulat ng pag-navigate}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} maipapasang audit}one{{numPassableAudits} maipapasang audit}other{{numPassableAudits} na maipapasang audit}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audit ang pumasa}one{{numPassed} audit ang pumasa}other{{numPassed} na audit ang pumasa}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Average"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Pangit"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Maganda"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "I-save"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Na-capture na status ng page"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Sinusuri ng mga ulat ng snapshot ang page sa isang partikular na status, na karaniwang pagkatapos ng mga pakikipag-ugnayan ng user."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "<PERSON><PERSON> ng snapshot"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} ulat ng snapshot}one{{numSnapshot} ulat ng snapshot}other{{numSnapshot} na ulat ng snapshot}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Buod"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Mga pakikipag-ugnayan ng user"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Nagsusuri ang mga ulat ng tagal ng panahon ng abitrary na yugto ng panahon, na karaniwang naglalaman ng mga pakikipag-ugnayan ng user."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Ulat ng tagal ng panahon"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} ulat ng tagal ng panahon}one{{numTimespan} ulat ng tagal ng panahon}other{{numTimespan} na ulat ng tagal ng panahon}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Ulat ng Daloy ng User ng Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Para sa animated na content, gamitin ang [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) para mabawasan ang paggamit ng CPU kapag offscreen ang content."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Pag-isipang ipakita ang lahat ng bahagi ng [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) sa mga WebP na format habang tumutukoy ng naaangkop na fallback para sa iba pang browser. [Matuto pa](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Tiyaking ginagamit mo ang [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) para sa mga larawan para awtomatikong mag-lazy load. [Matuto pa](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Gumamit ng mga tool na gaya ng [Optimizer ng AMP](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) para [ma-render sa server-side ang mga layout ng AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Suman<PERSON><PERSON> sa [dokumentasyon ng AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) para matiyak na sinusuportahan ang lahat ng istilo."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Sinusuportahan ng bahaging [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ang attribute na [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) para tukuyin kung aling mga asset na larawan ang gagamitin batay sa laki ng screen. [Matuto pa](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Pag-isipan ang virtual na pag-scroll sa Component Dev Kit (CDK) kung napakalaki ng mga listahang nire-render. [Matuto pa](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Ilapat ang [paghati ng code sa antas ng ruta](https://web.dev/route-level-code-splitting-in-angular/) para mabawasan ang laki ng iyong mga bundle ng JavaScript. Gayundin, pag-isipang i-precache ang mga asset sa pamamagitan ng [service worker sa Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Kung gumagamit ka ng Angular CLI, tiyaking binubuo ang mga build sa production mode. [Matuto pa](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Kung gumagamit ka ng Angular CLI, magsama ng mga mapa ng source sa build ng iyong produksyon para masuri ang mga bundle mo. [<PERSON>uto pa](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "I-preload ang mga ruta nang maaga para mapabilis ang pag-navigate. [<PERSON>uto pa](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Pag-isipang gamitin ang utility na `BreakpointObserver` sa Component Dev Kit (CDK) para pamahalaan ang mga breakpoint ng imahe. [Matuto pa](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Pag-isipang i-upload ang iyong GIF sa isang serbisyo kung saan gagawin itong available para i-embed bilang HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "<PERSON><PERSON><PERSON><PERSON> ang `@font-display` kapag nagtuturo ng mga custom na font sa iyong tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Subukang mag-configure ng [mga WebP na format ng larawan gamit ang Convert na istilo ng larawan](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) sa iyong site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Mag-install [ng module ng Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) na puwedeng mag-lazy load ng mga larawan. Nagbibigay ang mga module na iyon ng kakayahang ipagpaliban ang anumang offscreen na larawan para mapahusay ang performance."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Pag-isipang gumamit ng module para i-inline ang critical na CSS at JavaScript, at gamitin ang attribute na ipagpaliban para sa non-critical na CSS o JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Nakakaapekto ang mga tema, module, at detalye ng server sa oras ng pagtugon ng server. Pag-isipang maghanap ng mas naka-optimize na tema, maingat na pumili ng module sa pag-optimize, at/o i-upgrade ang iyong server. Dapat gamitin ng iyong mga nagho-host na server ang pag-cache ng PHP opcode at pag-cache ng memory para mabawasan ang mga oras ng query sa database gaya ng Redis o Memcached, pati na ang naka-optimize na logic ng application para mas mabilis na maihanda ang mga page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Pag-isipang gumamit ng [Mga Istilo ng Responsive na Larawan](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) para mabawasan ang laki ng mga larawang nilo-load sa iyong page. Kung gumagamit ka ng Views para magpakita ng maraming item ng content sa isang page, pag-isipang magpatupad ng pagination para limitahan ang bilang ng mga item ng content na ipinapakita sa isang page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Tiyaking na-enable mo ang \"I-aggregate ang mga CSS file\" sa page na \"Pangangasiwa » Configuration » Pag-develop.\"  Tiyaking gumagamit ang iyong Drupal site ng kahit man lang Drupal 10.1 para sa pinahusay na suporta sa pag-aggregate ng asset."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Tiyaking na-enable mo ang \"I-aggregate ang mga JavaScript file\" sa page na \"Pangangasiwa » Configuration » Pag-develop.\"  Tiyaking gumagamit ang iyong Drupal site ng kahit man lang Drupal 10.1 para sa pinahusay na suporta sa pag-aggregate ng asset."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Pag-isipang alisin ang mga hindi ginagamit na panuntunan sa CSS at i-attach lang ang mga kinakailangang library ng Drupal sa kaugnay na page o component sa isang page. Tingnan ang [link ng dokumentasyon ng Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para sa mga detalye. Para tukuyin ang mga naka-attach na library na nagdaragdag ng hindi nauugnay na CSS, subukang patakbuhin ang [sakop ng code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Puwede mong tukuyin ang tema/module na responsable mula sa URL ng stylesheet kapag naka-disable ang pagsasama-sama sa CSS sa iyong site ng Drupal. Abangan ang mga tema/module na maraming stylesheet sa listahang mayroong maraming pula sa sakop ng code. Dapat lang i-enqueue ng tema/module ang isang stylesheet kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Pag-isipang alisin ang mga hindi ginagamit na JavaScript asset at i-attach lang ang mga kinakailangang library ng Drupal sa kaugnay na page o bahagi sa isang page. Tingnan ang [link ng dokumentasyon ng Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para sa mga detalye. Para tukuyin ang mga naka-attach na library na nagdaragdag ng hindi nauugnay na JavaScript, subukang patakbuhin ang [sakop ng code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Puwede mong tukuyin ang tema/module na responsable mula sa URL ng script kapag naka-disable ang pagsasama-sama ng JavaScript sa iyong site ng Drupal. Abangan ang mga tema/module na maraming script sa listahang mayroong maraming pula sa sakop ng code. Dapat lang i-enqueue ng tema/module ang isang script kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Itakda ang \"Maximum na tagal ng cache ng browser at proxy\" sa page na \"Pangangasiwa » Configuration » Pag-develop.\" Magbasa tungkol sa [Cache ng Drupal at pag-optimize para sa performance](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Pag-isipang gumamit [ng module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) na awtomatikong nag-o-optimize at nagbabawas sa laki ng mga larawang na-upload sa pamamagitan ng site habang pinapanatili ang kalidad. Tiyakin ding ginagamit mo ang native na [Mga Istilo ng Responsive na Larawan](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) na inihahatid mula sa Drupal (available sa Drupal 8 at mas bago) para sa lahat ng larawang na-render sa site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Puwedeng idagdag ang mga hint sa resource na preconnect o dns-prefetch resource sa pamamagitan ng pag-install at pag-configure [ng module](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) na nagbibigay ng mga pasilidad para sa mga hint sa resource ng user agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Tiyaking ginagamit mo ang native na [Mga Istilo ng Responsive na Larawan](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) na inihahatid mula sa Drupal (available sa Drupal 8 at mas bago). Gamitin ang Mga Istilo ng Responsive na Larawan kapag nagre-render ng mga field ng larawan sa pamamagitan ng mga view mode, view, o larawang na-upload sa pamamagitan ng WYSIWYG editor."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "G<PERSON><PERSON> ang [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Optimize Fonts` para awtomatikong magamit ang feature na `font-display` ng CSS para matiyak na nakikita ng user ang text habang nilo-load ang mga webfont."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Gamiton ang [E<PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Next-Gen Formats` para mag-convert ng mga larawan sa WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "G<PERSON><PERSON> ang [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Lazy Load Images` para ipagpaliban ang pag-load ng mga off-screen na larawan hanggang sa kailanganin ang mga ito."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "G<PERSON>tin ang [E<PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Critical CSS` at `Script Delay` para ipagpaliban ang hindi mahalagang JS/CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "G<PERSON><PERSON> ang [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) para i-cache ang iyong content sa aming network kahit saan sa mundo, na nagpapahusay sa oras sa unang byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "G<PERSON><PERSON> ang [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Minify CSS` para awtomatikong paliitin ang iyong CSS para mabawasan ang mga laki ng payload ng network."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "G<PERSON><PERSON> ang [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Minify Javascript` para awtomatikong paliitin ang iyong JS para mabawasan ang mga laki ng payload ng network."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "G<PERSON>tin ang [Ezoic Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Remove Unused CSS` para makatulong sa isyung ito. Tutukuyin nito ang mga klase ng CSS na talagang ginagamit sa bawat page ng iyong site, at alisin ang anupaman para mapanatiling maliit ang laki ng file."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "G<PERSON>tin ang [E<PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Efficient Static Cache Policy` para itakda ang mga inirerekomendang value sa header ng pag-cache para sa mga static na asset."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Gamiton ang [E<PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Next-Gen Formats` para mag-convert ng mga larawan sa WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "G<PERSON>tin ang [Ezoic Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Pre-Connect Origins` para awtomatikong magdagdag ng mga hint sa resource na `preconnect` para magkaroon ng maaagang koneksyon sa mahahalagang third-party na pinagmulan."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "G<PERSON><PERSON> ang [Ezoic Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Preload Fonts` at `Preload Background Images` para magdagdag ng mga link na `preload` para bigyang-priyoridad ang pag-fetch ng mga resource na kasalukuyang hinihiling sa ibang pagkakataon sa pag-load ng page."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "G<PERSON><PERSON> ang [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) at i-enable ang `Resize Images` para i-resize ang mga larawan sa laking naaangkop sa device, na nagbabawas sa mga laki ng payload ng network."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Gamitin ang bahaging `gatsby-plugin-image` sa halip na `<img>` para awtomatikong i-optimize ang format ng larawan. [Matuto pa](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Gamitin ang bahaging `gatsby-plugin-image` sa halip na `<img>` para awtomatikong i-load nang mabagal ang mga larawan. [Matuto pa](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Gamitin ang bahaging `gatsby-plugin-image` at itakda sa `eager` ang prop na `loading`. [Mat<PERSON> pa](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Gamitin ang `Gatsby Script API` para ipagpaliban ang pag-load ng hindi mahahalagang third-party na script. [Matuto pa](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Gamitin ang plugin na `PurgeCSS` `Gatsby` para alisin sa mga stylesheet ang mga hindi ginagamit na panuntunan. [Matuto pa](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Gamitin ang `Webpack Bundle Analyzer` para mag-detect ng hindi ginagamit na JavaScript code. [Matuto pa](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "I-configure ang pag-cache para sa mga hindi nababagong asset. [<PERSON>uto pa](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Gamitin ang bahaging `gatsby-plugin-image` sa halip na `<img>` para i-adjust ang kalidad ng larawan. [Matuto pa](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "G<PERSON><PERSON> ang bahaging `gatsby-plugin-image` para itakda ang naaangkop na `sizes`. [<PERSON><PERSON> pa](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Pag-isipang i-upload ang iyong GIF sa isang serbisyo kung saan gagawin itong available para i-embed bilang HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Pag-isipang gumamit ng [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) o serbisyong awtomatikong magko-convert ng iyong mga na-upload na larawan sa mga optimal na format."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Mag-install ng [lazy-load na plugin sa Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) na nagbibigay ng kakayahang ipagpaliban ang anumang offscreen na larawan, o lumipat sa isang template na nagbibigay ng functionality na iyon. Simula sa Joomla 4.0, [awtomatikong](https://github.com/joomla/joomla-cms/pull/30748) kukunin ng lahat ng bagong larawan ang attribute na `loading` mula sa core."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "May ilang plugin sa Joomla na makakatulong sa iyong [i-inline ang mahahalagang asset](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) o [ipagpaliban ang hindi masyadong mahahalagang resource](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Tandaang puwedeng makasira sa mga feature ng iyong mga template o plugin ang mga pag-optimize na mula sa mga plugin na ito, kaya kakailanganin mong subukang mabuti ang mga ito."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Nakakaapekto ang mga template, extension, at detalye ng server sa oras ng pagtugon ng server. Pag-isipang maghanap ng mas naka-optimize na template, maingat na pumili ng extension sa pag-optimize, at/o i-upgrade ang iyong server."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Pag-isipang magpakita ng mga sipi sa iyong mga kategorya ng artikulo (hal. sa pamamagitan ng link ng magbasa pa), bawasan ang bilang ng mga artikulong ipinapakita sa isang page, hatiin ang mahahaba mong post sa maraming page, o gumamit ng plugin para sa mga mag-lazy load na komento."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Mapapabilis ng ilang [extension ng Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ang iyong site sa pamamagitan ng pag-concatenate, pagpapaliit, at pag-compress sa mga css style mo. Mayroon ding mga template na nagbibigay ng functionality na ito."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Mapapabilis ng ilang [extension ng Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ang iyong site sa pamamagitan ng pag-concatenate, pagpapaliit, at pag-compress sa mga script mo. Mayroon ding mga template na nagbibigay ng functionality na ito."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, o baguh<PERSON>, ang bilang ng [mga extension ng Joomla](https://extensions.joomla.org/) na naglo-load ng hindi ginagamit na CSS sa iyong page. Para tukuyin ang mga extension na nagdaragdag ng hindi nauugnay na CSS, subukang patakbuhin ang [sakop ng code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Puwede mong tukuyin ang tema/plugin na responsable mula sa URL ng stylesheet. Abangan ang mga plugin na maraming stylesheet sa listahang mayroong maraming pula sa sakop ng code. Dapat lang i-enqueue ng plugin ang isang stylesheet kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, o baguhin, ang bilang ng [mga extension ng Joomla](https://extensions.joomla.org/) na naglo-load ng hindi ginagamit na JavaScript sa iyong page. Para tukuyin ang mga plugin na nagdaragdag ng hindi nauugnay na JS, subukang patakbuhin ang [sakop ng code](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Puwede mong tukuyin ang extension na responsable mula sa URL ng script. Abangan ang mga extension na maraming script sa listahang mayroong maraming pula sa sakop ng code. Dapat lang i-enqueue ng extension ang isang script kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Magbasa tungkol sa [Pag-cache ng Browser sa Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Pag-isipang gumamit ng [plugin para sa pag-optimize ng larawan](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) na nagko-compress ng iyong mga larawan habang pinapanatili ang kalidad."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Pag-isipang gumamit ng [plugin para sa mga responsive na larawan](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) para makagamit ng mga responsive na larawan sa iyong content."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Puwede mong i-enable ang pag-compress ng text sa pamamagitan ng pag-enable sa Pag-compress ng Page ng Gzip sa Joomla (System > Pangkalahatang configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Kung hindi mo iba-bundle ang iyong mga asset ng JavaScript, pag-isipang gamitin ang [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "I-disable ang built-in na [pag-bundle at pagpapaliit ng JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ng Magento, at pag-isipang gamitin na lang ang [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "<PERSON><PERSON><PERSON><PERSON> ang `@font-display` kapag [tumutukoy ng mga custom na font](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Pag-isipang maghanap sa [Marketplace ng Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) para sa iba't ibang third-party na extension para magamit ang mga mas bagong format ng imahe."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "<PERSON><PERSON>-is<PERSON>ang baguhin ang iyong mga template ng produkto at catalog para magamit ang feature na [pag-lazy load](https://web.dev/native-lazy-loading) ng web platform."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "G<PERSON><PERSON> ang [pagsa<PERSON><PERSON> ng Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) ng Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "I-enable ang opsyong \"Paliitin ang Mga CSS File\" sa mga setting ng Developer ng iyong store. [Matuto pa](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> ang [Terser](https://www.npmjs.com/package/terser) para mapaliit ang lahat ng asset ng JavaScript mula sa deployment ng static na content, at i-disable ang built-in na feature ng pagpapaliit."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "I-disable ang built-in na [pag-bundle ng JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ng Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Pag-isipang maghanap sa [Marketplace ng Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) para sa iba't ibang third party na extension para ma-optimize ang mga imahe."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Maidaragdag ang mga hint sa resource na preconnect o dns-prefetch sa pamamagitan ng [pagbabago ng layout ng tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Maidaragdag ang mga tag ng `<link rel=preload>` sa pamamagitan ng [pagbabago ng layout ng tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Gamitin ang bahaging `next/image` sa halip na `<img>` para awtomatikong i-optimize ang format ng larawan. [Matuto pa](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "G<PERSON>tin ang bahaging `next/image` sa halip na `<img>` para awtomatikong i-load nang mabagal ang mga larawan. [Matuto pa](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "G<PERSON><PERSON> ang bahaging `next/image` at itakda ang \"priyoridad\" sa true para i-preload ang larawan ng LCP. [Matuto pa](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> ang bahaging `next/script` para ipagpaliban ang pag-load ng hindi mahahalagang third-party na script. [Matuto pa](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON> ang bahaging `next/image` para tiyaking palaging naaang<PERSON>p ang laki ng mga larawan. [Matuto pa](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Pag-is<PERSON><PERSON> i-set up ang `PurgeCSS` sa configuration ng `Next.js` para mag-alis ng mga hindi ginagamit na panuntunan sa mga stylesheet. [<PERSON>uto pa](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "G<PERSON>tin ang `Webpack Bundle Analyzer` para mag-detect ng hindi ginagamit na JavaScript code. [Matuto pa](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON>-is<PERSON><PERSON> gamitin ang `Next.js Analytics` para sukatin ang aktwal na performance ng iyong app. [Matuto pa](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "I-configure ang pag-cache para sa mga hindi nababagong asset at `Server-side Rendered` (SSR) page. [Matuto pa](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "G<PERSON><PERSON> ang bahaging `next/image` sa halip na `<img>` para i-adjust ang kalidad ng larawan. [<PERSON>uto pa](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "G<PERSON><PERSON> ang bahaging `next/image` para itakda ang naaangkop na `sizes`. [Matuto pa](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "I-enable ang pag-compress sa iyong Next.js server. [<PERSON>uto pa](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Makipag-ugnayan sa iyong account manager para i-enable ang [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/**************). Mabibigyang-priyoridad at mao-optimize ng pag-configure nito ang performance sa pag-render ng iyong page."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "G<PERSON><PERSON> ang opsyong [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/**************) sa NitroPack para magtakda ng gustong value para sa panuntunan ng font-display ng CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Gamitin ang [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/**************) para awtomatikong i-convert ang iyong mga larawan sa WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Ipagpaliban ang mga offscreen na larawan sa pamamagitan ng pag-enable sa [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "I-enable ang [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) sa NitroPack para sa mas mabibilis na paunang oras ng pag-load."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> ang bilis ng pagtugon ng server at i-optimize ang nakikitang performance sa pamamagitan ng pag-activate sa [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "I-enable ang [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) sa mga setting ng Caching mo para mabawasan ang laki ng iyong mga CSS, HTML, at JavaScript file para sa mas mabibilis na oras ng pag-load."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "I-enable ang [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) sa mga setting ng Caching mo para mabawasan ang laki ng iyong mga JS, HTML, at CSS file para sa mas mabibilis na oras ng pag-load."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "I-enable ang [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) para alisin ang mga panuntunan sa CSS na hindi naaangkop sa page na ito."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "I-configure ang [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) sa NitroPack para maantala ang pag-load ng mga script hangga't hindi pa kailangan ang mga ito."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Pumunta sa feature na [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) sa menu ng `Caching` at i-adjust ang tagal bago mag-expire ng cache ng page mo para pahusayin ang mga oras ng pag-load at experience ng user."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Awtomatikong i-compress, i-optimize, at i-convert ang iyong mga larawan sa WebP sa pamamagitan ng pag-enable sa setting ng [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "I-enable ang [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) para preemptive na i-optimize ang iyong mga larawan at maitugma ang mga ito sa mga dimensyon ng mga container kung saan ipinapakita ang mga ito sa lahat ng device."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "G<PERSON>tin ang [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) sa NitroPack para mabawasan ang laki ng mga file na ipinapadala sa browser."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "G<PERSON>tin ang bahaging `nuxt/image` at itakda ang `format=\"webp\"`. [Matuto pa](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "G<PERSON><PERSON> ang bahaging `nuxt/image` at itakda ang `loading=\"lazy\"` para sa mga offscreen na larawan. [Matuto pa](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "G<PERSON><PERSON> ang bahaging `nuxt/image` at tukuyin ang `preload` para sa larawan ng LCP. [Matuto pa](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "G<PERSON><PERSON> ang bahaging `nuxt/image` at tukuyin ang partikular na `width` at `height`. [<PERSON><PERSON> pa](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> ang bahaging `nuxt/image` at itakda ang naaangkop na `quality`. [Matuto pa](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "G<PERSON><PERSON> ang bahaging `nuxt/image` at itakda ang naaangkop na `sizes`. [Matuto pa](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Palitan ang mga animated na GIF ng video](https://web.dev/replace-gifs-with-videos/) para sa mas mabibilis na pag-load ng web page at pag-isipang gumamit ng mga modernong format ng file gaya ng [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) o [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) para mapahusay ang efficiency ng pag-compress nang mahigit 30% kaysa sa kasalukuyang state-of-the-art na video codec na VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Pag-isipang gumamit ng [plugin](https://octobercms.com/plugins?search=image) o serbisyong awtomatikong magko-convert ng mga na-upload na larawan sa mga pinakamagandang format. Mas maliit nang 26% ang [mga WebP lossless na larawan](https://developers.google.com/speed/webp) kaysa sa mga PNG at 25-34% mas maliit kaysa sa mga JPEG na larawan sa katumbas ng index ng kalidad ng SSIM. Ang isa pang next-gen na format ng larawan na dapat pag-isipang gamitin ay ang [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Pag-isipang mag-install ng [plugin sa pag-lazy load ng larawan](https://octobercms.com/plugins?search=lazy) na nagbibigay ng kakayahang ipagpaliban ang anumang offscreen na larawan, o lumipat sa isang temang nagbibigay ng naturang functionality. Pag-isipan ding gamitin [ang AMP na plugin](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Maraming plugin ang makakatulong na [i-inline ang mahahalagang asset](https://octobercms.com/plugins?search=css). Puwedeng masira ng mga plugin na ito ang iba pang plugin, kaya dapat kang mag-test nang masinsinan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Nakakaapekto ang mga tema, plugin, at detalye ng server sa tagal ng pagtugon ng server. Pag-isipang maghanap ng mas naka-optimize na tema, maingat na pumili ng plugin sa pag-optimize, at/o i-upgrade ang server. Binibigyang-daan din ng October CMS ang mga developer na gamitin ang [`Queues`](https://octobercms.com/docs/services/queues) para ipagpaliban ang pagpoproseso ng gawaing nakakaubos ng oras, gaya ng pagpapadala ng e-mail. Talagang pinapabilis nito ang mga kahilingan sa web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Pag-isipang magpakita ng mga sipi sa mga listahan ng post (hal. gamit ang button na `show more`), bawasan ang bilang ng mga post na ipinapakita sa isang web page, o gumamit ng plugin para mag-lazy load ng mga komento."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Maraming [plugin](https://octobercms.com/plugins?search=css) na makakapagpabilis ng website sa pamamagitan ng pag-concatenate, pagpapaliit, at pag-compress sa mga istilo. Kapag gumamit ng proseso ng build para gawin ang pagpapaliit na ito nang mas maaga, puwedeng mapabilis ang pag-develop."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Maraming [plugin](https://octobercms.com/plugins?search=javascript) na makakapagpabilis ng website sa pamamagitan ng pag-concatenate, pagpapaliit, at pag-compress sa mga script. Kapag gumamit ng proseso ng build para gawin ang pagpapaliit na ito nang mas maaga, puwedeng mapabilis ang pag-develop."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Pag-isipang suriin ang [mga plugin](https://octobercms.com/plugins) na naglo-load ng hindi ginagamit na CSS sa website. Para matukoy ang mga plugin na nagdaragdag ng hindi kinakailangang CSS, patak<PERSON>hin ang [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Tukuyin ang tema/plugin na responsable mula sa URL ng stylesheet. Hanapin ang mga plugin na maraming stylesheet na may napakaraming pula sa code coverage. Dapat lang magdagdag ang isang plugin ng stylesheet kung talagang ginagamit ito sa web page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Pag-isipang suriin ang [mga plugin](https://octobercms.com/plugins?search=javascript) na naglo-load ng hindi ginagamit na JavaScript sa web page. Para matukoy ang mga plugin na nagdaragdag ng hindi kinakailangang JavaScript, patakbuhin ang [code coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) sa Chrome DevTools. Tukuyin ang tema/plugin na responsable mula sa URL ng script. Hanapin ang mga plugin na maraming script na may napakaraming pula sa code coverage. Dapat lang magdagdag ang isang plugin ng script kung talagang ginagamit ito sa web page."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Magbasa tungkol sa [pagpigil sa mga hindi kinakailangang kahilingan sa network gamit ang HTTP Cache](https://web.dev/http-cache/#caching-checklist). Maraming [plugin](https://octobercms.com/plugins?search=Caching) na magagamit para mapabilis ang pag-cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Pag-isipang gumamit ng [plugin sa pag-optimize ng larawan](https://octobercms.com/plugins?search=image) para mag-compress ng mga larawan habang pinapanatili ang kalidad."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Direktang i-upload ang mga larawan sa media manager para matiyak na available ang mga kinakailangang laki ng larawan. Pag-isipang gumamit ng [filter na i-resize](https://octobercms.com/docs/markup/filter-resize) o [plugin sa pag-resize ng larawan](https://octobercms.com/plugins?search=image) para matiyak na ginagamit ang mga pinakanaaangkop na laki ng larawan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "I-enable ang pag-compress ng text sa configuration ng server sa web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Pag-isipang gumamit ng \"windowing\" library tulad ng `react-window` para mabawasan ang bilang ng mga node ng DOM na nagagawa kung nagre-render ka ng maraming umuulit na element sa page. [Matuto pa](https://web.dev/virtualize-long-lists-react-window/). <PERSON><PERSON><PERSON>, bawasan ang mga hindi kinakailangang pag-render ulit gamit ang [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent), o [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) at [laktawan lang ang mga effect](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) hanggang sa may ilang partikular na dependency na magbago kung ginagamit mo ang hook na `Effect` para mapahusay ang performance sa runtime."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Kung gumagamit ka ng Router ng React, bawasan ang paggamit ng bahaging `<Redirect>` para sa [mga pag-navigate sa ruta](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Kung may nire-render ka sa server-side na anumang bahagi ng React, pag-isipang gamitin ang `renderToPipeableStream()` o `renderToStaticNodeStream()` para payagan ang client na makatanggap at mag-hydrate ng iba't ibang bahagi ng markup sa halip na sabay-sabay. [Matuto pa](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Kung awtomatikong pinapaliit ng iyong system ng build ang mga CSS file, tiyaking ide-deploy mo ang build ng produksyon ng iyong application. Puwede mo itong suriin sa pamamagitan ng extension na Mga Tool ng Developer ng React. [Matuto pa](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Kung awtomatikong pinapaliit ng iyong system ng build ang mga JS file, tiyaking ide-deploy mo ang build ng produksyon ng iyong application. Puwede mo itong suriin sa pamamagitan ng extension na Mga Tool ng Developer ng React. [Matuto pa](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Kung hindi ka nagre-render sa server-side, [hatiin ang iyong mga bundle ng JavaScript](https://web.dev/code-splitting-suspense/) gamit ang `React.lazy()`. Kung hindi naman, hatiin ang code gamit ang isang third-party na library na gaya ng [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Gamitin ang React DevTools Profiler, na gumagamit ng Profiler API para sukatin ang performance sa pag-render ng iyong mga bahagi. [Matuto pa.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Maglagay ng mga video sa loob ng `VideoBoxes`, i-customize ang mga ito gamit ang `Video Masks`, o magdagdag ng `Transparent Videos`. [Matuto pa](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Mag-upload ng mga larawan gamit ang `Wix Media Manager` para matiyak na awtomatikong maihahatid ang mga ito bilang WebP. Makahanap ng [higit pang paraan para i-optimize](https://support.wix.com/en/article/site-performance-optimizing-your-media) ang media ng iyong site."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Kapag [nagdaragdag ng third-party na code](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) sa tab na `Custom Code` ng dashboard ng iyong site, tiyaking naka-defer o naka-load ito sa dulo ng body ng code. Kapag posible, gamitin ang [mga pag-integrate](https://support.wix.com/en/article/about-marketing-integrations) ng Wix para mag-embed ng mga tool sa marketing sa iyong site. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Gumagamit ang Wix ng mga CDN at pag-cache para maghatid ng mga sagot nang mabilis hangga't posible para sa karamihan ng mga bisita. Pag-isipang [manual na i-enable ang pag-cache](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) para sa iyong site, lalo na kung gumagamit ng `<PERSON>elo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Suriin ang anumang third-party na code na idinagdag mo sa iyong site sa tab na `Custom Code` ng dashboard ng site mo at panatilihin lang ang mga serbisyong kailangan sa iyong site. [Alamin ang higit pa](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Pag-isipang i-upload ang iyong GIF sa isang serbisyo kung saan gagawin itong available para i-embed bilang HTML5 video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Pag-isipang gamitin ang plugin na [Performance Lab](https://wordpress.org/plugins/performance-lab/) para awtomatikong i-convert ang iyong mga na-upload na larawang JPEG sa WebP, saanman ito sinusuportahan."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Mag-install ng [lazy-load na plugin sa WordPress](https://wordpress.org/plugins/search/lazy+load/) na nagbibigay ng kakayahang ipagpaliban ang anumang offscreen na larawan, o lumipat sa isang temang nagbibigay ng functionality. Pag-isipan ding gamiting [ang AMP na plugin](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "May ilang plugin sa WordPress na makakatulong sa iyong [i-inline ang mahahalagang asset](https://wordpress.org/plugins/search/critical+css/) o [ipagpaliban ang hindi masyadong mahahalagang resource](https://wordpress.org/plugins/search/defer+css+javascript/). Tandaang puwedeng makasira sa mga feature ng iyong tema o mga plugin ang mga pag-optimize na mula sa mga plugin na ito, kaya malamang na kakailanganin mong gumawa ng mga pagbabago sa code."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Nakakaapekto ang mga tema, plugin, at detalye ng server sa oras ng pagtugon ng server. Pag-isipang maghanap ng mas naka-optimize na tema, maingat na pumili ng plugin sa pag-optimize, at/o i-upgrade ang iyong server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Pag-isipang magpakita ng mga sipi sa iyong mga listahan ng post (hal. sa pamamagitan ng tag na higit pa), bawasan ang bilang ng post na ipinapakita sa isang page, hatiin ang mahahaba mong post sa maraming page, o gumamit ng plugin sa mga lazy-load na komento."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Puwedeng pabilisin ng ilang [plugin sa WordPress](https://wordpress.org/plugins/search/minify+css/) ang iyong site sa pamamagitan ng pagsasama-sama, pagpapaliit, at pagko-compress ng mga istilo mo. Puwede ka ring gumamit ng proseso ng pagbuo para gawin ang pagpapaliit na ito nang mas maaga kung posible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Puwedeng pabilisin ng ilang [plugin sa WordPress](https://wordpress.org/plugins/search/minify+javascript/) ang iyong site sa pamamagitan ng pagsasama-sama, pagpapaliit, at pagko-compress ng mga script mo. Puwede ka ring gumamit ng proseso ng pagbuo para gawin ang pagpapaliit na ito nang mas maaga kung posible."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, o baguhin, ang bilang ng [mga plugin sa WordPress](https://wordpress.org/plugins/) na naglo-load ng mga hindi ginagamit na CSS sa iyong page. Para tukuyin ang mga plugin na nagdaragdag ng mga hindi nauugnay na CSS, subukang patakbuhin ang [sakop ng code](https://developer.chrome.com/docs/devtools/coverage/) sa Chrome DevTools. Puwede mong tukuyin ang tema/plugin na sanhi nito mula sa URL ng stylesheet. Abangan ang mga plugin na maraming stylesheet sa listahang maraming pula sa sakop ng code. Dapat lang i-enqueue ng plugin ang isang stylesheet kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, o baguhin, ang bilang ng [mga plugin sa WordPress](https://wordpress.org/plugins/) na naglo-load ng mga hindi ginagamit na JavaScript sa iyong page. Para tukuyin ang mga plugin na nagdaragdag ng mga hindi nauugnay na JS, subukang patakbuhin ang [sakop ng code](https://developer.chrome.com/docs/devtools/coverage/) sa Chrome DevTools. Puwede mong tukuyin ang tema/plugin na sanhi nito mula sa URL ng script. Abangan ang mga plugin na maraming script sa listahang may maraming pula sa sakop ng code. Dapat lang i-enqueue ng plugin ang isang script kung talagang ginagamit ito sa page."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Magbasa tungkol sa [Pag-cache ng Browser sa WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Pag-isipang gumamit ng [plugin sa WordPress para sa pag-optimize ng larawan](https://wordpress.org/plugins/search/optimize+images/) na nagko-compress ng iyong mga larawan habang pinapanatili ang kalidad."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Direktang i-upload ang mga larawan sa pamamagitan ng [library ng media](https://wordpress.org/support/article/media-library-screen/) para tiyaking available ang mga kinakailangang laki ng larawan, at pagkatapos ay ilagay ang mga ito mula sa library ng media o gamitin ang widget ng larawan para tiyaking ginagamit ang mga pinakaangkop na laki ng larawan (kabilang ang para sa mga tumutugong breakpoint). Iwasang gamitin ang mga larawang nasa `Full Size` maliban kung sapat ang mga dimensyon para sa paggamit ng mga ito. [Matuto Pa](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Puwede mong i-enable ang pag-compress ng text sa configuration ng iyong server sa web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "I-enable ang 'Imagify' mula sa tab na Pag-optimize ng Larawan sa 'WP Rocket' para i-convert ang iyong mga larawan sa WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "I-enable ang [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) sa WP Rocket para ayusin ang rekomendasyong ito. Inaantala ng feature na ito ang pag-load ng mga larawan hanggang sa mag-scroll pababa ang bisita sa page at talagang kailangan na niyang makita ang mga ito."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "I-enable ang [<PERSON><PERSON> ang Hindi Ginagamit na CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) at [I-load nang naka-defer ang JavaScript](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) sa 'WP Rocket' para matugunan ang rekomendasyong ito. Io-optimize ng mga feature na ito ang mga CSS at JavaScript file para hindi ma-block ng mga ito ang pag-render ng iyong page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "I-enable ang [Paliitin ang mga CSS file](https://docs.wp-rocket.me/article/1350-css-minify-combine) sa 'WP Rocket' para ayusin ang isyung ito. Aalisin ang anumang espasyo at komento sa mga CSS file ng iyong site para mas mapaliit ang file at mas mapabilis ang pag-download nito."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "I-enable ang [Paliitin ang mga JavaScript file](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) sa 'WP Rocket' para ayusin ang isyung ito. Aalisin ang mga bakanteng espasyo at komento sa mga JavaScript file para mas mapaliit ang mga ito at mas mapabilis ang pag-download ng mga ito."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "I-enable ang [<PERSON><PERSON> ang Hindi Ginagamit na CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) sa 'WP Rocket' para ayusin ang isyung ito. Binabawasan nito ang laki ng page sa pamamagitan ng pag-aalis ng lahat ng CSS at stylesheet na hindi ginagamit habang pinapanatili lang ang ginagamit na CSS para sa bawat page."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "I-enable ang [<PERSON><PERSON><PERSON> ang pag-execute ng JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) sa 'WP Rocket' para ayusin ang problemang ito. Papahus<PERSON>in nito ang pag-load ng iyong page sa pamamagitan ng pag-aantala sa pag-execute ng mga script hanggang sa interaction ng user. Kung may mga iframe ang iyong site, puwede mong gamtin ang [LazyLoad para sa mga iframe at video](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) at puwede mo ring [Palitan ng preview image ang iframe ng YouTube](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "I-enable ang 'Imagify' mula sa tab na Pag-optimize ng larawan sa 'WP Rocket' at patakbuhin ang Maramihang Pag-optimize para i-compress ang iyong mga larawan."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Gamitin ang [Mga Request para I-prefetch ang DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) sa 'WP Rocket' para maidagdag ang \"dns-prefetch\" at mapabilis ang koneksyon sa mga external na domain. Awtomatiko ring idinaragdag ng 'WP Rocket' ang \"preconnect\" sa [domain ng Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) at anumang CNAME na idinagdag sa pamamagitan ng feature na [I-enable ang CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Para ayusin ang isyung ito para sa mga font, i-enable ang [<PERSON><PERSON> ang Hindi Ginagamit na CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) sa 'WP Rocket'. Uunahing i-preload ang mahahalagang font ng iyong site."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Tingnan ang calculator."}, "report/renderer/report-utils.js | collapseView": {"message": "I-collapse ang view"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Unang Navigation"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximum na latency ng critical path:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON><PERSON><PERSON> ang JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "I-toggle ang <PERSON> na <PERSON>"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Pinalawak ang Pag-print"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Buod sa Pag-print"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "I-save <PERSON><PERSON> Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "I-save bilang HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "I-save bilang JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "<PERSON><PERSON><PERSON> ang Unthrottled na Trace"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | errorLabel": {"message": "Nagka-error!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Error sa ulat: walang impormasyon sa pag-audit"}, "report/renderer/report-utils.js | expandView": {"message": "Palawakin ang view"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "1st party"}, "report/renderer/report-utils.js | footerIssue": {"message": "Mag-file ng isyu"}, "report/renderer/report-utils.js | hide": {"message": "Itago"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Data ng Lab"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON> pag<PERSON>uri ng [Lighthouse](https://developers.google.com/web/tools/lighthouse/) ng kasalukuyang page sa isang na-emulate na mobile network. Tinantya at puwedeng mag-iba ang mga value."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Mga karagdagang item na manual na susuriin"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Hindi naaangkop"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "<PERSON><PERSON><PERSON> sa bagong tab"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Pagkakataon"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Mga pumasang pag-audit"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Alinsunod sa [Na-update na Mga Pamantayan sa Installability ng Chrome](https://developer.chrome.com/blog/update-install-criteria), hindi na gagamitin ng Lighthouse ang kategorya ng PWA sa release sa hinaharap. Sumangguni sa [na-update na dokumentasyon ng PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/) para sa pag-test ng PWA sa hinaharap."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Paunang pag-load ng page"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Snapshot ng isang sandali"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Tagal ng panahon ng mga interaction ng user"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Custom na pag-throttle"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Na-emulate na Desktop"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Naka-emulate na Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Walang pag-emulate"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Bersyon ng Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Hindi Na-throttle na Power ng CPU/Memory"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Pag-throttle ng CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Pag-throttle ng network"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Pag-emulate ng screen"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User agent (network)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Session sa isang page"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Kinuha ang data na ito mula sa session sa isang page, kumpara sa data ng field na nagbubuod sa maraming session."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Pag-throttle sa mabagal na 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Hindi alam"}, "report/renderer/report-utils.js | show": {"message": "Ipa<PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Ipakita ang mga audit na may kaugnayan sa:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "I-collapse ang snippet"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "I-expand ang snippet"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Ipakita ang mga resource ng 3rd party"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Ibinigay ng environment"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "May mga isyung nak<PERSON>to sa pagpapatakbong ito ng Lighthouse:"}, "report/renderer/report-utils.js | unattributable": {"message": "Hindi ma-attribute"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "<PERSON><PERSON><PERSON> at puwedeng mag-iba ang mga value. [Kinakalkula ang score ng performance](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) nang direkta mula sa mga sukatang ito."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Tingnan ang Trace"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON><PERSON> ang <PERSON>"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Pumasa sa mga pag-audit ngunit may mga babala"}, "report/renderer/report-utils.js | warningHeader": {"message": "Mga Babala: "}, "treemap/app/src/util.js | allLabel": {"message": "Lahat"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Lahat ng Script"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Sakop"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Mga Duplicate na Module"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Mga Byte ng Resource"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "I-toggle ang <PERSON>"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Mga Hindi Nagamit na Byte"}}