{"core/audits/accessibility/accesskeys.js | description": {"message": "[Åç<PERSON><PERSON><PERSON><PERSON> ķéýš ļéţ ûšéŕš qûîçķļý ƒöçûš å þåŕţ öƒ ţĥé þåĝé. Föŕ þŕöþéŕ ñåvîĝåţîöñ, éåçĥ åççéšš ķéý mûšţ бé ûñîqûé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ åççéšš ķéýšᐅ](https://dequeuniversity.com/rules/axe/4.8/accesskeys)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "[ᐅ`[accesskey]`ᐊ våļûéš åŕé ñöţ ûñîqûé one two three four five six]"}, "core/audits/accessibility/accesskeys.js | title": {"message": "[ᐅ`[accesskey]`ᐊ våļûéš åŕé ûñîqûé one two three four five]"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "[Éåçĥ ÅŔÎÅ ᐅ`role`ᐊ šûþþöŕţš å šþéçîƒîç šûбšéţ öƒ ᐅ`aria-*`ᐊ åţţŕîбûţéš. Mîšmåţçĥîñĝ ţĥéšé îñvåļîðåţéš ţĥé ᐅ`aria-*`ᐊ åţţŕîбûţéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måţçĥ ÅŔÎÅ åţţŕîбûţéš ţö ţĥéîŕ ŕöļéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš ðö ñöţ måţçĥ ţĥéîŕ ŕöļéš one two three four five six seven eight]"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš måţçĥ ţĥéîŕ ŕöļéš one two three four five six seven]"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "[ÅŔÎÅ ᐅ`role`ᐊš éñåбļé åššîšţîvé ţéçĥñöļöĝîéš ţö ķñöŵ ţĥé ŕöļé öƒ éåçĥ éļéméñţ öñ ţĥé ŵéб þåĝé. Îƒ ţĥé ᐅ`role`ᐊ våļûéš åŕé mîššþéļļéð, ñöţ éxîšţîñĝ ÅŔÎÅ ᐅ`role`ᐊ våļûéš, öŕ åбšţŕåçţ ŕöļéš, ţĥéñ ţĥé þûŕþöšé öƒ ţĥé éļéméñţ ŵîļļ ñöţ бé çömmûñîçåţéð ţö ûšéŕš öƒ åššîšţîvé ţéçĥñöļöĝîéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ÅŔÎÅ ŕöļéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix]"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "[V<PERSON><PERSON><PERSON><PERSON><PERSON> åššîĝñéð ţö ᐅ`role=\"\"`ᐊ åŕé ñöţ våļîð ÅŔÎÅ ŕöļéš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "[V<PERSON><PERSON><PERSON><PERSON><PERSON> åššîĝñéð ţö ᐅ`role=\"\"`ᐊ åŕé våļîð ÅŔÎÅ ŕöļéš. one two three four five six seven eight nine]"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "[Ŵĥéñ åñ éļéméñţ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé çömmåñð éļéméñţš möŕé åççéššîбļéᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "[ᐅ`button`ᐊ, ᐅ`link`ᐊ, åñð ᐅ`menuitem`ᐊ éļéméñţš ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "[ᐅ`button`ᐊ, ᐅ`link`ᐊ, åñð ᐅ`menuitem`ᐊ éļéméñţš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "[ÅŔÎÅ ðîåļöĝ éļéméñţš ŵîţĥöûţ åççéššîбļé ñåméš måý þŕévéñţ šçŕééñ ŕéåðéŕš ûšéŕš ƒŕöm ðîšçéŕñîñĝ ţĥé þûŕþöšé öƒ ţĥéšé éļéméñţš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé ÅŔÎÅ ðîåļöĝ éļéméñţš möŕé åççéššîбļéᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "[Éļé<PERSON><PERSON><PERSON>š ŵîţĥ ᐅ`role=\"dialog\"`ᐊ öŕ ᐅ`role=\"alertdialog\"`ᐊ ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "[Éļé<PERSON><PERSON><PERSON>š ŵîţĥ ᐅ`role=\"dialog\"`ᐊ öŕ ᐅ`role=\"alertdialog\"`ᐊ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "[Åš<PERSON>îšţîvé ţéçĥñöļöĝîéš, ļîķé šçŕééñ ŕéåðéŕš, ŵöŕķ îñçöñšîšţéñţļý ŵĥéñ ᐅ`aria-hidden=\"true\"`ᐊ îš šéţ öñ ţĥé ðöçûméñţ ᐅ`<body>`ᐊ. ᐅ[ᐊĻéåŕñ ĥöŵ ᐅ`aria-hidden`ᐊ åƒƒéçţš ţĥé ðöçûméñţ бöðýᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "[ᐅ`[aria-hidden=\"true\"]`ᐊ îš þŕéšéñţ öñ ţĥé ðöçûméñţ ᐅ`<body>`ᐊ one two three four five six seven]"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "[ᐅ`[aria-hidden=\"true\"]`ᐊ îš ñöţ þŕéšéñţ öñ ţĥé ðöçûméñţ ᐅ`<body>`ᐊ one two three four five six seven eight]"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "[Föçûšåбļé ðéšçéñðéñţš ŵîţĥîñ åñ ᐅ`[aria-hidden=\"true\"]`ᐊ éļéméñţ þŕévéñţ ţĥöšé îñţéŕåçţîvé éļéméñţš ƒŕöm бéîñĝ åvåîļåбļé ţö ûšéŕš öƒ åššîšţîvé ţéçĥñöļöĝîéš ļîķé šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ᐅ`aria-hidden`ᐊ åƒƒéçţš ƒöçûšåбļé éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "[ᐅ`[aria-hidden=\"true\"]`ᐊ éļéméñţš çöñţåîñ ƒöçûšåбļé ðéšçéñðéñţš one two three four five six seven eight nine]"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "[ᐅ`[aria-hidden=\"true\"]`ᐊ éļéméñţš ðö ñöţ çöñţåîñ ƒöçûšåбļé ðéšçéñðéñţš one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "[Ŵĥéñ åñ îñþûţ ƒîéļð ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ îñþûţ ƒîéļð ļåбéļšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "[ÅŔÎÅ îñþûţ ƒîéļðš ðö ñöţ ĥåvé åççéššîбļé ñåméš one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "[ÅŔÎÅ îñþûţ ƒîéļðš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "[Ŵĥéñ å méţéŕ éļéméñţ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ñåmé ᐅ`meter`ᐊ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "[ÅŔÎÅ ᐅ`meter`ᐊ éļéméñţš ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "[ÅŔÎÅ ᐅ`meter`ᐊ éļéméñţš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "[Ŵĥéñ å ᐅ`progressbar`ᐊ éļéméñţ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ļåбéļ ᐅ`progressbar`ᐊ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "[ÅŔÎÅ ᐅ`progressbar`ᐊ éļéméñţš ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "[ÅŔÎÅ ᐅ`progressbar`ᐊ éļéméñţš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "[Šömé ÅŔÎÅ ŕöļéš ĥåvé ŕéqûîŕéð åţţŕîбûţéš ţĥåţ ðéšçŕîбé ţĥé šţåţé öƒ ţĥé éļéméñţ ţö šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ŕöļéš åñð ŕéqûîŕéð åţţŕîбûţéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "[ᐅ`[role]`ᐊš ðö ñöţ ĥåvé åļļ ŕéqûîŕéð ᐅ`[aria-*]`ᐊ åţţŕîбûţéš one two three four five six seven eight nine]"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "[ᐅ`[role]`ᐊš ĥåvé åļļ ŕéqûîŕéð ᐅ`[aria-*]`ᐊ åţţŕîбûţéš one two three four five six seven eight]"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "[Šömé ÅŔÎÅ þåŕéñţ ŕöļéš mûšţ çöñţåîñ šþéçîƒîç çĥîļð ŕöļéš ţö þéŕƒöŕm ţĥéîŕ îñţéñðéð åççéššîбîļîţý ƒûñçţîöñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ŕöļéš åñð ŕéqûîŕéð çĥîļðŕéñ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "[Éļéméñţš ŵîţĥ åñ ÅŔÎÅ ᐅ`[role]`ᐊ ţĥåţ ŕéqûîŕé çĥîļðŕéñ ţö çöñţåîñ å šþéçîƒîç ᐅ`[role]`ᐊ åŕé mîššîñĝ šömé öŕ åļļ öƒ ţĥöšé ŕéqûîŕéð çĥîļðŕéñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/accessibility/aria-required-children.js | title": {"message": "[Éļémé<PERSON><PERSON>š ŵîţĥ åñ ÅŔÎÅ ᐅ`[role]`ᐊ ţĥåţ ŕéqûîŕé çĥîļðŕéñ ţö çöñţåîñ å šþéçîƒîç ᐅ`[role]`ᐊ ĥåvé åļļ ŕéqûîŕéð çĥîļðŕéñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "[Šömé ÅŔÎÅ çĥîļð ŕöļéš mûšţ бé çöñţåîñéð бý šþéçîƒîç þåŕéñţ ŕöļéš ţö þŕöþéŕļý þéŕƒöŕm ţĥéîŕ îñţéñðéð åççéššîбîļîţý ƒûñçţîöñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ÅŔÎÅ ŕöļéš åñð ŕéqûîŕéð þåŕéñţ éļéméñţᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "[ᐅ`[role]`ᐊš åŕé ñöţ çöñţåîñéð бý ţĥéîŕ ŕéqûîŕéð þåŕéñţ éļéméñţ one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "[ᐅ`[role]`ᐊš åŕé çöñţåîñéð бý ţĥéîŕ ŕéqûîŕéð þåŕéñţ éļéméñţ one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/aria-roles.js | description": {"message": "[ÅŔÎÅ ŕöļéš mûšţ ĥåvé våļîð våļûéš îñ öŕðéŕ ţö þéŕƒöŕm ţĥéîŕ îñţéñðéð åççéššîбîļîţý ƒûñçţîöñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ våļîð ÅŔÎÅ ŕöļéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-roles)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "[ᐅ`[role]`ᐊ våļûéš åŕé ñöţ våļîð one two three four five]"}, "core/audits/accessibility/aria-roles.js | title": {"message": "[ᐅ`[role]`ᐊ våļûéš åŕé våļîð one two three four five]"}, "core/audits/accessibility/aria-text.js | description": {"message": "[Åððîñĝ ᐅ`role=text`ᐊ åŕöûñð å ţéxţ ñöðé šþļîţ бý måŕķûþ éñåбļéš VöîçéÖvéŕ ţö ţŕéåţ îţ åš öñé þĥŕåšé, бûţ ţĥé éļéméñţ'š ƒöçûšåбļé ðéšçéñðéñţš ŵîļļ ñöţ бé åññöûñçéð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`role=text`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-text)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "[Éļé<PERSON><PERSON><PERSON>š ŵîţĥ ţĥé ᐅ`role=text`ᐊ åţţŕîбûţé ðö ĥåvé ƒöçûšåбļé ðéšçéñðéñţš. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/aria-text.js | title": {"message": "[Éļé<PERSON><PERSON><PERSON>š ŵîţĥ ţĥé ᐅ`role=text`ᐊ åţţŕîбûţé ðö ñöţ ĥåvé ƒöçûšåбļé ðéšçéñðéñţš. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "[Ŵĥéñ å ţöĝĝļé ƒîéļð ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţöĝĝļé ƒîéļðšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "[ÅŔÎÅ ţöĝĝļé ƒîéļðš ðö ñöţ ĥåvé åççéššîбļé ñåméš one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "[ÅŔÎÅ ţöĝĝļé ƒîéļðš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "[Ŵĥéñ å ţööļţîþ éļéméñţ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ñåmé ᐅ`tooltip`ᐊ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "[ÅŔÎÅ ᐅ`tooltip`ᐊ éļéméñţš ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "[ÅŔÎÅ ᐅ`tooltip`ᐊ éļéméñţš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "[Ŵĥéñ å ᐅ`treeitem`ᐊ éļéméñţ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ ŵîţĥ å ĝéñéŕîç ñåmé, måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ļåбéļîñĝ ᐅ`treeitem`ᐊ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "[ÅŔÎÅ ᐅ`treeitem`ᐊ éļéméñţš ðö ñöţ ĥåvé åççéššîбļé ñåméš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "[ÅŔÎÅ ᐅ`treeitem`ᐊ éļéméñţš ĥåvé åççéššîбļé ñåméš one two three four five six seven eight]"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "[Åš<PERSON>îšţîvé ţéçĥñöļöĝîéš, ļîķé šçŕééñ ŕéåðéŕš, çåñ'ţ îñţéŕþŕéţ ÅŔÎÅ åţţŕîбûţéš ŵîţĥ îñvåļîð våļûéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ våļîð våļûéš ƒöŕ ÅŔÎÅ åţţŕîбûţéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš ðö ñöţ ĥåvé våļîð våļûéš one two three four five six seven eight]"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš ĥåvé våļîð våļûéš one two three four five six seven]"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "[Åš<PERSON>îšţîvé ţéçĥñöļöĝîéš, ļîķé šçŕééñ ŕéåðéŕš, çåñ'ţ îñţéŕþŕéţ ÅŔÎÅ åţţŕîбûţéš ŵîţĥ îñvåļîð ñåméš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ våļîð ÅŔÎÅ åţţŕîбûţéšᐅ](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš åŕé ñöţ våļîð öŕ mîššþéļļéð one two three four five six seven eight nine]"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "[ᐅ`[aria-*]`ᐊ åţţŕîбûţéš åŕé våļîð åñð ñöţ mîššþéļļéð one two three four five six seven eight nine]"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "[Fåîļîñĝ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/accessibility/button-name.js | description": {"message": "[Ŵĥéñ å бûţţöñ ðöéšñ'ţ ĥåvé åñ åççéššîбļé ñåmé, šçŕééñ ŕéåðéŕš åññöûñçé îţ åš \"бûţţöñ\", måķîñĝ îţ ûñûšåбļé ƒöŕ ûšéŕš ŵĥö ŕéļý öñ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé бûţţöñš möŕé åççéššîбļéᐅ](https://dequeuniversity.com/rules/axe/4.8/button-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "[Бûţ<PERSON><PERSON><PERSON><PERSON> ðö ñöţ ĥåvé åñ åççéššîбļé ñåmé one two three four five six seven eight]"}, "core/audits/accessibility/button-name.js | title": {"message": "[Бûţ<PERSON><PERSON><PERSON><PERSON> ĥåvé åñ åççéššîбļé ñåmé one two three four five six seven]"}, "core/audits/accessibility/bypass.js | description": {"message": "[Åððîñĝ ŵåýš ţö бýþåšš ŕéþéţîţîvé çöñţéñţ ļéţš ķéýбöåŕð ûšéŕš ñåvîĝåţé ţĥé þåĝé möŕé éƒƒîçîéñţļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ бýþåšš бļöçķšᐅ](https://dequeuniversity.com/rules/axe/4.8/bypass)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "[Ţĥé þåĝé ðöéš ñöţ çöñţåîñ å ĥéåðîñĝ, šķîþ ļîñķ, öŕ ļåñðmåŕķ ŕéĝîöñ one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/bypass.js | title": {"message": "[Ţĥé þåĝé çöñţåîñš å ĥéåðîñĝ, šķîþ ļîñķ, öŕ ļåñðmåŕķ ŕéĝîöñ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/color-contrast.js | description": {"message": "[Ļöŵ-çöñţŕåšţ ţéxţ îš ðîƒƒîçûļţ öŕ îmþöššîбļé ƒöŕ måñý ûšéŕš ţö ŕéåð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö þŕövîðé šûƒƒîçîéñţ çöļöŕ çöñţŕåšţᐅ](https://dequeuniversity.com/rules/axe/4.8/color-contrast)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "[Бåçķĝŕöûñð åñð ƒöŕéĝŕöûñð çöļöŕš ðö ñöţ ĥåvé å šûƒƒîçîéñţ çöñţŕåšţ ŕåţîö. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/color-contrast.js | title": {"message": "[Бåçķĝŕöûñð åñð ƒöŕéĝŕöûñð çöļöŕš ĥåvé å šûƒƒîçîéñţ çöñţŕåšţ ŕåţîö one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/definition-list.js | description": {"message": "[Ŵĥéñ ðéƒîñîţîöñ ļîšţš åŕé ñöţ þŕöþéŕļý måŕķéð ûþ, šçŕééñ ŕéåðéŕš måý þŕöðûçé çöñƒûšîñĝ öŕ îñåççûŕåţé öûţþûţ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö šţŕûçţûŕé ðéƒîñîţîöñ ļîšţš çöŕŕéçţļýᐅ](https://dequeuniversity.com/rules/axe/4.8/definition-list)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "[ᐅ`<dl>`ᐊ'š ðö ñöţ çöñţåîñ öñļý þŕöþéŕļý-öŕðéŕéð ᐅ`<dt>`ᐊ åñð ᐅ`<dd>`ᐊ ĝŕöûþš, ᐅ`<script>`ᐊ, ᐅ`<template>`ᐊ öŕ ᐅ`<div>`ᐊ éļéméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/accessibility/definition-list.js | title": {"message": "[ᐅ`<dl>`ᐊ'š çöñţåîñ öñļý þŕöþéŕļý-öŕðéŕéð ᐅ`<dt>`ᐊ åñð ᐅ`<dd>`ᐊ ĝŕöûþš, ᐅ`<script>`ᐊ, ᐅ`<template>`ᐊ öŕ ᐅ`<div>`ᐊ éļéméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/dlitem.js | description": {"message": "[Ðéƒîñîţîöñ ļîšţ îţémš (ᐅ`<dt>`ᐊ åñð ᐅ`<dd>`ᐊ) mûšţ бé ŵŕåþþéð îñ å þåŕéñţ ᐅ`<dl>`ᐊ éļéméñţ ţö éñšûŕé ţĥåţ šçŕééñ ŕéåðéŕš çåñ þŕöþéŕļý åññöûñçé ţĥém. ᐅ[ᐊĻéåŕñ ĥöŵ ţö šţŕûçţûŕé ðéƒîñîţîöñ ļîšţš çöŕŕéçţļýᐅ](https://dequeuniversity.com/rules/axe/4.8/dlitem)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "[Ðéƒîñîţîöñ ļîšţ îţémš åŕé ñöţ ŵŕåþþéð îñ ᐅ`<dl>`ᐊ éļéméñţš one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/dlitem.js | title": {"message": "[Ðéƒîñîţîöñ ļîšţ îţémš åŕé ŵŕåþþéð îñ ᐅ`<dl>`ᐊ éļéméñţš one two three four five six seven eight nine ten]"}, "core/audits/accessibility/document-title.js | description": {"message": "[Ţĥé ţîţļé ĝîvéš šçŕééñ ŕéåðéŕ ûšéŕš åñ övéŕvîéŵ öƒ ţĥé þåĝé, åñð šéåŕçĥ éñĝîñé ûšéŕš ŕéļý öñ îţ ĥéåvîļý ţö ðéţéŕmîñé îƒ å þåĝé îš ŕéļévåñţ ţö ţĥéîŕ šéåŕçĥ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ðöçûméñţ ţîţļéšᐅ](https://dequeuniversity.com/rules/axe/4.8/document-title)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "[Ðöçûméñţ ðöéšñ'ţ ĥåvé å ᐅ`<title>`ᐊ éļéméñţ one two three four five six seven eight]"}, "core/audits/accessibility/document-title.js | title": {"message": "[Ðöçûméñţ ĥåš å ᐅ`<title>`ᐊ éļéméñţ one two three four five six]"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "[Åļļ ƒöçûšåбļé éļéméñţš mûšţ ĥåvé å ûñîqûé ᐅ`id`ᐊ ţö éñšûŕé ţĥåţ ţĥéý'ŕé vîšîбļé ţö åššîšţîvé ţéçĥñöļöĝîéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ƒîx ðûþļîçåţé ᐅ`id`ᐊšᐅ](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "[ᐅ`[id]`ᐊ åţţŕîбûţéš öñ åçţîvé, ƒöçûšåбļé éļéméñţš åŕé ñöţ ûñîqûé one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "[ᐅ`[id]`ᐊ åţţŕîбûţéš öñ åçţîvé, ƒöçûšåбļé éļéméñţš åŕé ûñîqûé one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "[Ţĥé våļûé öƒ åñ ÅŔÎÅ ÎÐ mûšţ бé ûñîqûé ţö þŕévéñţ öţĥéŕ îñšţåñçéš ƒŕöm бéîñĝ övéŕļööķéð бý åššîšţîvé ţéçĥñöļöĝîéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ƒîx ðûþļîçåţé ÅŔÎÅ ÎÐšᐅ](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "[ÅŔÎÅ ÎÐš åŕé ñöţ ûñîqûé one two three four five]"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "[ÅŔÎÅ ÎÐš åŕé ûñîqûé one two three four]"}, "core/audits/accessibility/empty-heading.js | description": {"message": "[Å ĥéåðîñĝ ŵîţĥ ñö çöñţéñţ öŕ îñåççéššîбļé ţéxţ þŕévéñţ šçŕééñ ŕéåðéŕ ûšéŕš ƒŕöm åççéššîñĝ îñƒöŕmåţîöñ öñ ţĥé þåĝé'š šţŕûçţûŕé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ĥéåðîñĝšᐅ](https://dequeuniversity.com/rules/axe/4.8/empty-heading)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "[Ĥéåðîñĝ éļéméñţš ðö ñöţ çöñţåîñ çöñţéñţ. one two three four five six seven eight]"}, "core/audits/accessibility/empty-heading.js | title": {"message": "[Åļļ ĥéåðîñĝ éļéméñţš çöñţåîñ çöñţéñţ. one two three four five six seven eight]"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "[Föŕm ƒîéļðš ŵîţĥ mûļţîþļé ļåбéļš çåñ бé çöñƒûšîñĝļý åññöûñçéð бý åššîšţîvé ţéçĥñöļöĝîéš ļîķé šçŕééñ ŕéåðéŕš ŵĥîçĥ ûšé éîţĥéŕ ţĥé ƒîŕšţ, ţĥé ļåšţ, öŕ åļļ öƒ ţĥé ļåбéļš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ûšé ƒöŕm ļåбéļšᐅ](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "[Föŕm ƒîéļðš ĥåvé mûļţîþļé ļåб<PERSON>ļ<PERSON> one two three four five six seven]"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "[Ñö ƒöŕm ƒîéļðš ĥåvé mûļţîþļé ļåбéļš one two three four five six seven eight]"}, "core/audits/accessibility/frame-title.js | description": {"message": "[Šçŕééñ ŕéåðéŕ ûšéŕš ŕéļý öñ ƒŕåmé ţîţļéš ţö ðéšçŕîбé ţĥé çöñţéñţš öƒ ƒŕåméš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ƒŕåmé ţîţļéšᐅ](https://dequeuniversity.com/rules/axe/4.8/frame-title)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "[ᐅ`<frame>`ᐊ öŕ ᐅ`<iframe>`ᐊ éļéméñţš ðö ñöţ ĥåvé å ţîţļé one two three four five six seven eight]"}, "core/audits/accessibility/frame-title.js | title": {"message": "[ᐅ`<frame>`ᐊ öŕ ᐅ`<iframe>`ᐊ éļéméñţš ĥåvé å ţîţļé one two three four five six seven]"}, "core/audits/accessibility/heading-order.js | description": {"message": "[Þŕöþéŕļý öŕðéŕéð ĥéåðîñĝš ţĥåţ ðö ñöţ šķîþ ļévéļš çöñvéý ţĥé šémåñţîç šţŕûçţûŕé öƒ ţĥé þåĝé, måķîñĝ îţ éåšîéŕ ţö ñåvîĝåţé åñð ûñðéŕšţåñð ŵĥéñ ûšîñĝ åššîšţîvé ţéçĥñöļöĝîéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ĥéåðîñĝ öŕðéŕᐅ](https://dequeuniversity.com/rules/axe/4.8/heading-order)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "[Ĥéåðîñĝ éļéméñţš åŕé ñöţ îñ å šéqûéñţîåļļý-ðéšçéñðîñĝ öŕðéŕ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/heading-order.js | title": {"message": "[Ĥéåðîñĝ éļéméñţš åþþéåŕ îñ å šéqûéñţîåļļý-ðéšçéñðîñĝ öŕðéŕ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "[Îƒ å þåĝé ðöéšñ'ţ šþéçîƒý å ᐅ`lang`ᐊ åţţŕîбûţé, å šçŕééñ ŕéåðéŕ åššûméš ţĥåţ ţĥé þåĝé îš îñ ţĥé ðéƒåûļţ ļåñĝûåĝé ţĥåţ ţĥé ûšéŕ çĥöšé ŵĥéñ šéţţîñĝ ûþ ţĥé šçŕééñ ŕéåðéŕ. Îƒ ţĥé þåĝé îšñ'ţ åçţûåļļý îñ ţĥé ðéƒåûļţ ļåñĝûåĝé, ţĥéñ ţĥé šçŕééñ ŕéåðéŕ mîĝĥţ ñöţ åññöûñçé ţĥé þåĝé'š ţéxţ çöŕŕéçţļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`lang`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight]"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ðöéš ñöţ ĥåvé å ᐅ`[lang]`ᐊ åţţŕîбûţé one two three four five six seven eight nine]"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ĥåš å ᐅ`[lang]`ᐊ åţţŕîбûţé one two three four five six seven]"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "[Šþéçîƒýîñĝ å våļîð ᐅ[ᐊБÇÞ 47 ļåñĝûåĝéᐅ](https://www.w3.org/International/questions/qa-choosing-language-tags#question)ᐊ ĥéļþš šçŕééñ ŕéåðéŕš åññöûñçé ţéxţ þŕöþéŕļý. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ûšé ţĥé ᐅ`lang`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ðöéš ñöţ ĥåvé å våļîð våļûé ƒöŕ îţš ᐅ`[lang]`ᐊ åţţŕîбûţé. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ĥåš å våļîð våļûé ƒöŕ îţš ᐅ`[lang]`ᐊ åţţŕîбûţé one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "[Îƒ ţĥé ŵéбþåĝé ðöéš ñöţ šþéçîƒý å çöñšîšţéñţ ļåñĝûåĝé, ţĥéñ ţĥé šçŕééñ ŕéåðéŕ mîĝĥţ ñöţ åññöûñçé ţĥé þåĝé'š ţéxţ çöŕŕéçţļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`lang`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ðöéš ñöţ ĥåvé åñ ᐅ`[xml:lang]`ᐊ åţţŕîбûţé ŵîţĥ ţĥé šåmé бåšé ļåñĝûåĝé åš ţĥé ᐅ`[lang]`ᐊ åţţŕîбûţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "[ᐅ`<html>`ᐊ éļéméñţ ĥåš åñ ᐅ`[xml:lang]`ᐊ åţţŕîбûţé ŵîţĥ ţĥé šåmé бåšé ļåñĝûåĝé åš ţĥé ᐅ`[lang]`ᐊ åţţŕîбûţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "[Ļîñķš ŵîţĥ ţĥé šåmé ðéšţîñåţîöñ šĥöûļð ĥåvé ţĥé šåmé ðéšçŕîþţîöñ, ţö ĥéļþ ûšéŕš ûñðéŕšţåñð ţĥé ļîñķ'š þûŕþöšé åñð ðéçîðé ŵĥéţĥéŕ ţö ƒöļļöŵ îţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ îðéñţîçåļ ļîñķšᐅ](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "[Îðéñţîçåļ ļîñķš ðö ñöţ ĥåvé ţĥé šåmé þûŕþöšé. one two three four five six seven eight nine]"}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "[Îðéñţîçåļ ļîñķš ĥåvé ţĥé šåmé þûŕþöšé. one two three four five six seven eight]"}, "core/audits/accessibility/image-alt.js | description": {"message": "[Îñƒöŕmåţîvé éļéméñţš šĥöûļð åîm ƒöŕ šĥöŕţ, ðéšçŕîþţîvé åļţéŕñåţé ţéxţ. Ðéçöŕåţîvé éļéméñţš çåñ бé îĝñöŕéð ŵîţĥ åñ émþţý åļţ åţţŕîбûţé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`alt`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/image-alt)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "[Îmåĝé éļéméñţš ðö ñöţ ĥåvé ᐅ`[alt]`ᐊ åţţŕîбûţéš one two three four five six seven eight nine]"}, "core/audits/accessibility/image-alt.js | title": {"message": "[Îmåĝé éļéméñţš ĥåvé ᐅ`[alt]`ᐊ åţţŕîбûţéš one two three four five six seven]"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "[Îñƒöŕmåţîvé éļéméñţš šĥöûļð åîm ƒöŕ šĥöŕţ, ðéšçŕîþţîvé åļţéŕñåţîvé ţéxţ. Åļţéŕñåţîvé ţéxţ ţĥåţ îš éxåçţļý ţĥé šåmé åš ţĥé ţéxţ åðĵåçéñţ ţö ţĥé ļîñķ öŕ îmåĝé îš þöţéñţîåļļý çöñƒûšîñĝ ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš, бéçåûšé ţĥé ţéxţ ŵîļļ бé ŕéåð ţŵîçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`alt`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive]"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "[Îmåĝé éļéméñţš ĥåvé ᐅ`[alt]`ᐊ åţţŕîбûţéš ţĥåţ åŕé ŕéðûñðåñţ ţéxţ. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "[Îmåĝé éļéméñţš ðö ñöţ ĥåvé ᐅ`[alt]`ᐊ åţţŕîбûţéš ţĥåţ åŕé ŕéðûñðåñţ ţéxţ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/input-button-name.js | description": {"message": "[Åððîñĝ ðîšçéŕñåбļé åñð åççéššîбļé ţéxţ ţö îñþûţ бûţţöñš måý ĥéļþ šçŕééñ ŕéåðéŕ ûšéŕš ûñðéŕšţåñð ţĥé þûŕþöšé öƒ ţĥé îñþûţ бûţţöñ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ îñþûţ бûţţöñšᐅ](https://dequeuniversity.com/rules/axe/4.8/input-button-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "[Îñþûţ бûţţöñš ðö ñöţ ĥåvé ðîšçéŕñîбļé ţéxţ. one two three four five six seven eight nine]"}, "core/audits/accessibility/input-button-name.js | title": {"message": "[Îñþûţ бûţţöñš ĥåvé ðîšçéŕñîбļé ţéxţ. one two three four five six seven eight]"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "[Ŵĥéñ åñ îmåĝé îš бéîñĝ ûšéð åš åñ ᐅ`<input>`ᐊ бûţţöñ, þŕövîðîñĝ åļţéŕñåţîvé ţéxţ çåñ ĥéļþ šçŕééñ ŕéåðéŕ ûšéŕš ûñðéŕšţåñð ţĥé þûŕþöšé öƒ ţĥé бûţţöñ. ᐅ[ᐊĻéåŕñ åбöûţ îñþûţ îmåĝé åļţ ţéxţᐅ](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "[ᐅ`<input type=\"image\">`ᐊ éļéméñţš ðö ñöţ ĥåvé ᐅ`[alt]`ᐊ ţéxţ one two three four five six seven]"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "[ᐅ`<input type=\"image\">`ᐊ éļéméñţš ĥåvé ᐅ`[alt]`ᐊ ţéxţ one two three four five six]"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "[Vîšîбļé ţéxţ ļåбéļš ţĥåţ ðö ñöţ måţçĥ ţĥé åççéššîбļé ñåmé çåñ ŕéšûļţ îñ å çöñƒûšîñĝ éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ åççéššîбļé ñåméšᐅ](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "[Éļéméñţš ŵîţĥ vîšîбļé ţéxţ ļåбéļš ðö ñöţ ĥåvé måţçĥîñĝ åççéššîбļé ñåméš. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "[Éļé<PERSON>ñţš ŵîţĥ vîšîбļé ţéxţ ļåбéļš ĥåvé måţçĥîñĝ åççéššîбļé ñåméš. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/label.js | description": {"message": "[Ļåбéļš éñšûŕé ţĥåţ ƒöŕm çöñţŕöļš åŕé åññöûñçéð þŕöþéŕļý бý åššîšţîvé ţéçĥñöļöĝîéš, ļîķé šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ƒöŕm éļéméñţ ļåбéļšᐅ](https://dequeuniversity.com/rules/axe/4.8/label)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/accessibility/label.js | failureTitle": {"message": "[Föŕm éļé<PERSON><PERSON><PERSON>š ðö ñöţ ĥåvé åššöçîåţéð ļåб<PERSON><PERSON>š one two three four five six seven eight nine]"}, "core/audits/accessibility/label.js | title": {"message": "[Föŕm éļéméñţš ĥåvé åššöçîåţéð ļåбéļš one two three four five six seven eight]"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "[Öñ<PERSON> måîñ ļåñðmåŕķ ĥéļþš šçŕééñ ŕéåðéŕ ûšéŕš ñåvîĝåţé å ŵéб þåĝé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ļåñðmåŕķšᐅ](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "[Ðöçûméñţ ð<PERSON><PERSON><PERSON> ñöţ ĥåvé å måîñ ļåñðmåŕķ. one two three four five six seven eight]"}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "[Ðöçûméñţ ĥåš å måîñ ļåñðmåŕķ. one two three four five six seven]"}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "[Ļöŵ-çöñţŕåšţ ţéxţ îš ðîƒƒîçûļţ öŕ îmþöššîбļé ƒöŕ måñý ûšéŕš ţö ŕéåð. Ļîñķ ţéxţ ţĥåţ îš ðîšçéŕñîбļé îmþŕövéš ţĥé éxþéŕîéñçé ƒöŕ ûšéŕš ŵîţĥ ļöŵ vîšîöñ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé ļîñķš ðîšţîñĝûîšĥåбļéᐅ](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "[Ļîñķš ŕéļý öñ çöļöŕ ţö бé ðîšţîñĝûîšĥåбļé. one two three four five six seven eight nine]"}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "[Ļîñķš åŕé ðîšţîñĝûîšĥåбļé ŵîţĥöûţ ŕéļýîñĝ öñ çöļöŕ. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/link-name.js | description": {"message": "[Ļîñķ ţéxţ (åñð åļţéŕñåţé ţéxţ ƒöŕ îmåĝéš, ŵĥéñ ûšéð åš ļîñķš) ţĥåţ îš ðîšçéŕñîбļé, ûñîqûé, åñð ƒöçûšåбļé îmþŕövéš ţĥé ñåvîĝåţîöñ éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé ļîñķš åççéššîбļéᐅ](https://dequeuniversity.com/rules/axe/4.8/link-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "[Ļîñķš ðö ñöţ ĥåvé å ðîšçéŕñîбļé ñåmé one two three four five six seven eight]"}, "core/audits/accessibility/link-name.js | title": {"message": "[Ļîñķš ĥåvé å ðîšçéŕñîбļé ñåmé one two three four five six seven]"}, "core/audits/accessibility/list.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ĥåvé å šþéçîƒîç ŵåý öƒ åññöûñçîñĝ ļîšţš. Éñšûŕîñĝ þŕöþéŕ ļîšţ šţŕûçţûŕé åîðš šçŕééñ ŕéåðéŕ öûţþûţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þŕöþéŕ ļîšţ šţŕûçţûŕéᐅ](https://dequeuniversity.com/rules/axe/4.8/list)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/list.js | failureTitle": {"message": "[Ļîšţš ðö ñöţ çöñţåîñ öñļý ᐅ`<li>`ᐊ éļéméñţš åñð šçŕîþţ šûþþöŕţîñĝ éļéméñţš (ᐅ`<script>`ᐊ åñð ᐅ`<template>`ᐊ). one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/accessibility/list.js | title": {"message": "[Ļîšţš çöñţåîñ öñļý ᐅ`<li>`ᐊ éļéméñţš åñð šçŕîþţ šûþþöŕţîñĝ éļéméñţš (ᐅ`<script>`ᐊ åñð ᐅ`<template>`ᐊ). one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/listitem.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ŕéqûîŕé ļîšţ îţémš (ᐅ`<li>`ᐊ) ţö бé çöñţåîñéð ŵîţĥîñ å þåŕéñţ ᐅ`<ul>`ᐊ, ᐅ`<ol>`ᐊ öŕ ᐅ`<menu>`ᐊ ţö бé åññöûñçéð þŕöþéŕļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þŕöþéŕ ļîšţ šţŕûçţûŕéᐅ](https://dequeuniversity.com/rules/axe/4.8/listitem)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "[Ļîšţ îţé<PERSON><PERSON> (ᐅ`<li>`ᐊ) åŕé ñöţ çöñţåîñéð ŵîţĥîñ ᐅ`<ul>`ᐊ, ᐅ`<ol>`ᐊ öŕ ᐅ`<menu>`ᐊ þåŕéñţ éļéméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/listitem.js | title": {"message": "[Ļîšţ îţém<PERSON> (ᐅ`<li>`ᐊ) åŕé çöñţåîñéð ŵîţĥîñ ᐅ`<ul>`ᐊ, ᐅ`<ol>`ᐊ öŕ ᐅ`<menu>`ᐊ þåŕéñţ éļéméñţš one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "[Ûšéŕš ðö ñöţ éxþéçţ å þåĝé ţö ŕéƒŕéšĥ åûţömåţîçåļļý, åñð ðöîñĝ šö ŵîļļ mövé ƒöçûš бåçķ ţö ţĥé ţöþ öƒ ţĥé þåĝé. Ţĥîš måý çŕéåţé å ƒŕûšţŕåţîñĝ öŕ çöñƒûšîñĝ éxþéŕîéñçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ŕéƒŕéšĥ méţå ţåĝᐅ](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "[Ţĥé ðöçûméñţ ûšéš ᐅ`<meta http-equiv=\"refresh\">`ᐊ one two three four five]"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "[Ţĥé ðöçûméñţ ðöéš ñöţ ûšé ᐅ`<meta http-equiv=\"refresh\">`ᐊ one two three four five six seven]"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "[Ðîšåбļîñĝ žöömîñĝ îš þŕöбļémåţîç ƒöŕ ûšéŕš ŵîţĥ ļöŵ vîšîöñ ŵĥö ŕéļý öñ šçŕééñ måĝñîƒîçåţîöñ ţö þŕöþéŕļý šéé ţĥé çöñţéñţš öƒ å ŵéб þåĝé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé vîéŵþöŕţ méţå ţåĝᐅ](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "[ᐅ`[user-scalable=\"no\"]`ᐊ îš ûšéð îñ ţĥé ᐅ`<meta name=\"viewport\">`ᐊ éļéméñţ öŕ ţĥé ᐅ`[maximum-scale]`ᐊ åţţŕîбûţé îš ļéšš ţĥåñ 5. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "[ᐅ`[user-scalable=\"no\"]`ᐊ îš ñöţ ûšéð îñ ţĥé ᐅ`<meta name=\"viewport\">`ᐊ éļéméñţ åñð ţĥé ᐅ`[maximum-scale]`ᐊ åţţŕîбûţé îš ñöţ ļéšš ţĥåñ 5. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/object-alt.js | description": {"message": "[Šçŕééñ ŕéåðéŕš çåññöţ ţŕåñšļåţé ñöñ-ţéxţ çöñţéñţ. Åððîñĝ åļţéŕñåţé ţéxţ ţö ᐅ`<object>`ᐊ éļéméñţš ĥéļþš šçŕééñ ŕéåðéŕš çöñvéý méåñîñĝ ţö ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ åļţ ţéxţ ƒöŕ ᐅ`object`ᐊ éļéméñţšᐅ](https://dequeuniversity.com/rules/axe/4.8/object-alt)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "[ᐅ`<object>`ᐊ éļéméñţš ðö ñöţ ĥåvé åļţéŕñåţé ţéxţ one two three four five six seven eight]"}, "core/audits/accessibility/object-alt.js | title": {"message": "[ᐅ`<object>`ᐊ éļéméñţš ĥåvé åļţéŕñåţé ţéxţ one two three four five six seven]"}, "core/audits/accessibility/select-name.js | description": {"message": "[Föŕm éļéméñţš ŵîţĥöûţ éƒƒéçţîvé ļåбéļš çåñ çŕéåţé ƒŕûšţŕåţîñĝ éxþéŕîéñçéš ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`select`ᐊ éļéméñţᐅ](https://dequeuniversity.com/rules/axe/4.8/select-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "[Š<PERSON><PERSON><PERSON><PERSON><PERSON> éļéméñţš ðö ñöţ ĥåvé åššöçîåţéð ļåбéļ éļéméñţš. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/select-name.js | title": {"message": "[Š<PERSON><PERSON><PERSON><PERSON><PERSON> éļéméñţš ĥåvé åššöçîåţéð ļåбéļ éļéméñţš. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/skip-link.js | description": {"message": "[Îñçļûðîñĝ å šķîþ ļîñķ çåñ ĥéļþ ûšéŕš šķîþ ţö ţĥé måîñ çöñţéñţ ţö šåvé ţîmé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ šķîþ ļîñķšᐅ](https://dequeuniversity.com/rules/axe/4.8/skip-link)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "[Šķîþ ļîñķš åŕé ñöţ ƒöçûšåбļé. one two three four five six seven]"}, "core/audits/accessibility/skip-link.js | title": {"message": "[Šķîþ ļîñķš åŕé ƒöçûšåбļé. one two three four five six]"}, "core/audits/accessibility/tabindex.js | description": {"message": "[Å våļûé ĝŕéåţéŕ ţĥåñ 0 îmþļîéš åñ éxþļîçîţ ñåvîĝåţîöñ öŕðéŕîñĝ. Åļţĥöûĝĥ ţéçĥñîçåļļý våļîð, ţĥîš öƒţéñ çŕéåţéš ƒŕûšţŕåţîñĝ éxþéŕîéñçéš ƒöŕ ûšéŕš ŵĥö ŕéļý öñ åššîšţîvé ţéçĥñöļöĝîéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`tabindex`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/tabindex)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "[<PERSON>ö<PERSON>ļ<PERSON>méñţš ĥåvé å ᐅ`[tabindex]`ᐊ våļûé ĝŕéåţéŕ ţĥåñ 0 one two three four five six seven eight nine]"}, "core/audits/accessibility/tabindex.js | title": {"message": "[Ñö éļéméñţ ĥåš å ᐅ`[tabindex]`ᐊ våļûé ĝŕéåţéŕ ţĥåñ 0 one two three four five six seven eight nine]"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "[Ţĥé šûmmåŕý åţţŕîбûţé šĥöûļð ðéšçŕîбé ţĥé ţåбļé šţŕûçţûŕé, ŵĥîļé ᐅ`<caption>`ᐊ šĥöûļð ĥåvé ţĥé öñšçŕééñ ţîţļé. Åççûŕåţé ţåбļé måŕķ-ûþ ĥéļþš ûšéŕš öƒ šçŕééñ ŕéåðéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ šûmmåŕý åñð çåþţîöñᐅ](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "[Ţå<PERSON><PERSON><PERSON>š ĥåvé ţĥé šåmé çöñţéñţ îñ ţĥé šûmmåŕý åţţŕîбûţé åñð ᐅ`<caption>.`ᐊ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "[Ţå<PERSON><PERSON><PERSON>š ĥåvé ðîƒƒéŕéñţ çöñţéñţ îñ ţĥé šûmmåŕý åţţŕîбûţé åñð ᐅ`<caption>`ᐊ. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ĥåvé ƒéåţûŕéš ţö måķé ñåvîĝåţîñĝ ţåбļéš éåšîéŕ. Éñšûŕîñĝ ţĥåţ ţåбļéš ûšé ţĥé åçţûåļ çåþţîöñ éļéméñţ îñšţéåð öƒ çéļļš ŵîţĥ ţĥé ᐅ`[colspan]`ᐊ åţţŕîбûţé måý îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ çåþţîöñšᐅ](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone]"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "[Ţ<PERSON><PERSON><PERSON><PERSON><PERSON> ðö ñöţ ûšé ᐅ`<caption>`ᐊ îñšţéåð öƒ çéļļš ŵîţĥ ţĥé ᐅ`[colspan]`ᐊ åţţŕîбûţé ţö îñðîçåţé å çåþţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "[Ţåбļ<PERSON>š ûšé ᐅ`<caption>`ᐊ îñšţéåð öƒ çéļļš ŵîţĥ ţĥé ᐅ`[colspan]`ᐊ åţţŕîбûţé ţö îñðîçåţé å çåþţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/accessibility/target-size.js | description": {"message": "[Ţöûçĥ ţåŕĝéţš ŵîţĥ šûƒƒîçîéñţ šîžé åñð šþåçîñĝ ĥéļþ ûšéŕš ŵĥö måý ĥåvé ðîƒƒîçûļţý ţåŕĝéţîñĝ šmåļļ çöñţŕöļš ţö åçţîvåţé ţĥé ţåŕĝéţš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţöûçĥ ţåŕĝéţšᐅ](https://dequeuniversity.com/rules/axe/4.8/target-size)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "[Ţöûçĥ ţåŕĝéţš ðö ñöţ ĥåvé šûƒƒîçîéñţ šîžé öŕ šþåçîñĝ. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/target-size.js | title": {"message": "[Ţöûçĥ ţåŕĝéţš ĥåvé šûƒƒîçîéñţ šîžé åñð šþåçîñĝ. one two three four five six seven eight nine ten]"}, "core/audits/accessibility/td-has-header.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ĥåvé ƒéåţûŕéš ţö måķé ñåvîĝåţîñĝ ţåбļéš éåšîéŕ. Éñšûŕîñĝ ţĥåţ ᐅ`<td>`ᐊ éļéméñţš îñ å ļåŕĝé ţåбļé (3 öŕ möŕé çéļļš îñ ŵîðţĥ åñð ĥéîĝĥţ) ĥåvé åñ åššöçîåţéð ţåбļé ĥéåðéŕ måý îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţåбļé ĥéåðéŕšᐅ](https://dequeuniversity.com/rules/axe/4.8/td-has-header)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree]"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "[ᐅ`<td>`ᐊ éļéméñţš îñ å ļåŕĝé ᐅ`<table>`ᐊ ðö ñöţ ĥåvé ţåбļé ĥéåðéŕš. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/td-has-header.js | title": {"message": "[ᐅ`<td>`ᐊ éļéméñţš îñ å ļåŕĝé ᐅ`<table>`ᐊ ĥåvé öñé öŕ möŕé ţåбļé ĥéåðéŕš. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ĥåvé ƒéåţûŕéš ţö måķé ñåvîĝåţîñĝ ţåбļéš éåšîéŕ. Éñšûŕîñĝ ᐅ`<td>`ᐊ çéļļš ûšîñĝ ţĥé ᐅ`[headers]`ᐊ åţţŕîбûţé öñļý ŕéƒéŕ ţö öţĥéŕ çéļļš îñ ţĥé šåmé ţåбļé måý îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ᐅ`headers`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone]"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "[Çéļļš îñ å ᐅ`<table>`ᐊ éļéméñţ ţĥåţ ûšé ţĥé ᐅ`[headers]`ᐊ åţţŕîбûţé ŕéƒéŕ ţö åñ éļéméñţ ᐅ`id`ᐊ ñöţ ƒöûñð ŵîţĥîñ ţĥé šåmé ţåбļé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "[Çéļļš îñ å ᐅ`<table>`ᐊ éļéméñţ ţĥåţ ûšé ţĥé ᐅ`[headers]`ᐊ åţţŕîбûţé ŕéƒéŕ ţö ţåбļé çéļļš ŵîţĥîñ ţĥé šåmé ţåбļé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "[Šçŕééñ ŕéåðéŕš ĥåvé ƒéåţûŕéš ţö måķé ñåvîĝåţîñĝ ţåбļéš éåšîéŕ. Éñšûŕîñĝ ţåбļé ĥéåðéŕš åļŵåýš ŕéƒéŕ ţö šömé šéţ öƒ çéļļš måý îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ šçŕééñ ŕéåðéŕ ûšéŕš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţåбļé ĥéåðéŕšᐅ](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "[ᐅ`<th>`ᐊ éļéméñţš åñð éļéméñţš ŵîţĥ ᐅ`[role=\"columnheader\"/\"rowheader\"]`ᐊ ðö ñöţ ĥåvé ðåţå çéļļš ţĥéý ðéšçŕîбé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "[ᐅ`<th>`ᐊ éļéméñţš åñð éļéméñţš ŵîţĥ ᐅ`[role=\"columnheader\"/\"rowheader\"]`ᐊ ĥåvé ðåţå çéļļš ţĥéý ðéšçŕîбé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/accessibility/valid-lang.js | description": {"message": "[Šþéçîƒýîñĝ å våļîð ᐅ[ᐊБÇÞ 47 ļåñĝûåĝéᐅ](https://www.w3.org/International/questions/qa-choosing-language-tags#question)ᐊ öñ éļéméñţš ĥéļþš éñšûŕé ţĥåţ ţéxţ îš þŕöñöûñçéð çöŕŕéçţļý бý å šçŕééñ ŕéåðéŕ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ûšé ţĥé ᐅ`lang`ᐊ åţţŕîбûţéᐅ](https://dequeuniversity.com/rules/axe/4.8/valid-lang)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "[ᐅ`[lang]`ᐊ åţţŕîбûţéš ðö ñöţ ĥåvé å våļîð våļûé one two three four five six seven eight]"}, "core/audits/accessibility/valid-lang.js | title": {"message": "[ᐅ`[lang]`ᐊ åţţŕîбûţéš ĥåvé å våļîð våļûé one two three four five six seven]"}, "core/audits/accessibility/video-caption.js | description": {"message": "[Ŵĥéñ å vîðéö þŕövîðéš å çåþţîöñ îţ îš éåšîéŕ ƒöŕ ðéåƒ åñð ĥéåŕîñĝ îmþåîŕéð ûšéŕš ţö åççéšš îţš îñƒöŕmåţîöñ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ vîðéö çåþţîöñšᐅ](https://dequeuniversity.com/rules/axe/4.8/video-caption)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "[ᐅ`<video>`ᐊ éļéméñţš ðö ñöţ çöñţåîñ å ᐅ`<track>`ᐊ éļéméñţ ŵîţĥ ᐅ`[kind=\"captions\"]`ᐊ. one two three four five six seven eight nine ten eleven]"}, "core/audits/accessibility/video-caption.js | title": {"message": "[ᐅ`<video>`ᐊ éļéméñţš çöñţåîñ å ᐅ`<track>`ᐊ éļéméñţ ŵîţĥ ᐅ`[kind=\"captions\"]`ᐊ one two three four five six seven eight nine]"}, "core/audits/autocomplete.js | columnCurrent": {"message": "[Çûŕŕéñţ V<PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "[Šûĝĝéšţéð Ţöķéñ one two]"}, "core/audits/autocomplete.js | description": {"message": "[ᐅ`autocomplete`ᐊ ĥéļþš ûšéŕš šûбmîţ ƒöŕmš qûîçķéŕ. Ţö ŕéðûçé ûšéŕ éƒƒöŕţ, çöñšîðéŕ éñåбļîñĝ бý šéţţîñĝ ţĥé ᐅ`autocomplete`ᐊ åţţŕîбûţé ţö å våļîð våļûé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ᐅ`autocomplete`ᐊ îñ ƒöŕmšᐅ](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/autocomplete.js | failureTitle": {"message": "[ᐅ`<input>`ᐊ éļéméñţš ðö ñöţ ĥåvé çöŕŕéçţ ᐅ`autocomplete`ᐊ åţţŕîбûţéš one two three four five six seven eight nine ten]"}, "core/audits/autocomplete.js | manualReview": {"message": "[Ŕéqûîŕéš måñûåļ ŕévîéŵ one two three]"}, "core/audits/autocomplete.js | reviewOrder": {"message": "[Ŕévîéŵ öŕðéŕ öƒ ţöķéñš one two three four five]"}, "core/audits/autocomplete.js | title": {"message": "[ᐅ`<input>`ᐊ éļéméñţš çöŕŕéçţļý ûšé ᐅ`autocomplete`ᐊ one two three four five six seven]"}, "core/audits/autocomplete.js | warningInvalid": {"message": "[ᐅ`autocomplete`ᐊ ţöķéñ(š): \"ᐅ{token}ᐊ\" îš îñvåļîð îñ ᐅ{snippet}ᐊ one two three four five six seven eight]"}, "core/audits/autocomplete.js | warningOrder": {"message": "[Ŕévîéŵ öŕðéŕ öƒ ţöķéñš: \"ᐅ{tokens}ᐊ\" îñ ᐅ{snippet}ᐊ one two three four five six seven eight]"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "[Åç<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/bf-cache.js | description": {"message": "[Måñý ñåvîĝåţîöñš åŕé þéŕƒöŕméð бý ĝöîñĝ бåçķ ţö å þŕévîöûš þåĝé, öŕ ƒöŕŵåŕðš åĝåîñ. Ţĥé бåçķ/ƒöŕŵåŕð çåçĥé (бƒçåçĥé) çåñ šþééð ûþ ţĥéšé ŕéţûŕñ ñåvîĝåţîöñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé бƒçåçĥéᐅ](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{[1 ƒåîļûŕé ŕéåšöñ one two]}other{[# ƒåîļûŕé ŕéåšöñš one two three]}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "[Fåîļûŕé ŕéåšöñ one two]"}, "core/audits/bf-cache.js | failureTitle": {"message": "[Þåĝé þŕévéñţéð бåçķ/ƒöŕŵåŕð çåçĥé ŕéšţöŕåţîöñ one two three four five six seven eight nine]"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "[Fåîļûŕé ţýþé one two]"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "[<PERSON><PERSON><PERSON>ñ<PERSON>ļ<PERSON> one two]"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "[Þéñðîñĝ бŕöŵšéŕ šûþþöŕţ one two three]"}, "core/audits/bf-cache.js | title": {"message": "[Þåĝé ðîðñ'ţ þŕévéñţ бåçķ/ƒöŕŵåŕð çåçĥé ŕéšţöŕåţîöñ one two three four five six seven eight nine ten eleven]"}, "core/audits/bf-cache.js | warningHeadless": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé çåññöţ бé ţéšţéð îñ öļð Ĥéåðļéšš Çĥŕömé (ᐅ`--chrome-flags=\"--headless=old\"`ᐊ). Ţö šéé åûðîţ ŕéšûļţš, ûšé ţĥé ñéŵ Ĥéåðļéšš Çĥŕömé (ᐅ`--chrome-flags=\"--headless=new\"`ᐊ) öŕ šţåñðåŕð Çĥŕömé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "[Çĥŕömé éxţéñšîöñš ñéĝåţîvéļý åƒƒéçţéð ţĥîš þåĝé'š ļöåð þéŕƒöŕmåñçé. Ţŕý åûðîţîñĝ ţĥé þåĝé îñ îñçöĝñîţö möðé öŕ ƒŕöm å Çĥŕömé þŕöƒîļé ŵîţĥöûţ éxţéñšîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/bootup-time.js | columnScriptEval": {"message": "[Šçŕîþţ É<PERSON><PERSON><PERSON><PERSON>åţîöñ one two three]"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "[Šçŕîþţ Þåŕšé one two]"}, "core/audits/bootup-time.js | columnTotal": {"message": "[Ţöţ<PERSON>ļ ÇÞÛ Ţîmé one two]"}, "core/audits/bootup-time.js | description": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ ţĥé ţîmé šþéñţ þåŕšîñĝ, çömþîļîñĝ, åñð éxéçûţîñĝ ĴŠ. Ýöû måý ƒîñð ðéļîvéŕîñĝ šmåļļéŕ ĴŠ þåýļöåðš ĥéļþš ŵîţĥ ţĥîš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ŕéðûçé Ĵåvåšçŕîþţ éxéçûţîöñ ţîméᐅ](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/bootup-time.js | failureTitle": {"message": "[Ŕéðûçé ĴåvåŠçŕîþţ éxéçûţîöñ ţîmé one two three four five six seven]"}, "core/audits/bootup-time.js | title": {"message": "[ĴåvåŠçŕîþţ éxéçûţîöñ ţîmé one two three]"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "[Ŕémövé ļåŕĝé, ðûþļîçåţé ĴåvåŠçŕîþţ möðûļéš ƒŕöm бûñðļéš ţö ŕéðûçé ûññéçéššåŕý бýţéš çöñšûméð бý ñéţŵöŕķ åçţîvîţý.  one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "[Ŕémövé ðûþļî<PERSON><PERSON><PERSON><PERSON> möðûļéš îñ ĴåvåŠçŕîþţ бûñðļéš one two three four five six seven eight nine ten]"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "[Ļåŕĝé ĜÎFš åŕé îñéƒƒîçîéñţ ƒöŕ ðéļîvéŕîñĝ åñîmåţéð çöñţéñţ. Çöñšîðéŕ ûšîñĝ MÞÉĜ4/ŴéбM vîðéöš ƒöŕ åñîmåţîöñš åñð ÞÑĜ/ŴéбÞ ƒöŕ šţåţîç îmåĝéš îñšţéåð öƒ ĜÎF ţö šåvé ñéţŵöŕķ бýţéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ éƒƒîçîéñţ vîðéö ƒöŕmåţšᐅ](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "[Ûšé vîðéö ƒöŕmåţš ƒöŕ åñîmåţéð çöñţéñţ one two three four five six seven eight]"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "[Þöļýƒîļļš åñð ţŕåñšƒöŕmš éñåбļé ļéĝåçý бŕöŵšéŕš ţö ûšé ñéŵ ĴåvåŠçŕîþţ ƒéåţûŕéš. Ĥöŵévéŕ, måñý åŕéñ'ţ ñéçéššåŕý ƒöŕ möðéŕñ бŕöŵšéŕš. Föŕ ýöûŕ бûñðļéð ĴåvåŠçŕîþţ, åðöþţ å möðéŕñ šçŕîþţ ðéþļöýméñţ šţŕåţéĝý ûšîñĝ möðûļé/ñömöðûļé ƒéåţûŕé ðéţéçţîöñ ţö ŕéðûçé ţĥé åmöûñţ öƒ çöðé šĥîþþéð ţö möðéŕñ бŕöŵšéŕš, ŵĥîļé ŕéţåîñîñĝ šûþþöŕţ ƒöŕ ļéĝåçý бŕöŵšéŕš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ûšé möðéŕñ ĴåvåŠçŕîþţᐅ](https://web.dev/articles/publish-modern-javascript)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine]"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "[Åvöîð šéŕvîñĝ ļéĝåçý ĴåvåŠçŕîþţ ţö möðéŕñ бŕöŵšéŕš one two three four five six seven eight nine ten eleven]"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "[Îmåĝé ƒöŕmåţš ļîķé ŴéбÞ åñð ÅVÎF öƒţéñ þŕövîðé бéţţéŕ çömþŕéššîöñ ţĥåñ ÞÑĜ öŕ ĴÞÉĜ, ŵĥîçĥ méåñš ƒåšţéŕ ðöŵñļöåðš åñð ļéšš ðåţå çöñšûmþţîöñ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ möðéŕñ îmåĝé ƒöŕmåţšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "[Šéŕvé îmåĝéš îñ ñéxţ-ĝéñ ƒöŕmåţš one two three four five six seven]"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "[Çöñšîðéŕ ļåžý-ļöåðîñĝ öƒƒšçŕééñ åñð ĥîððéñ îmåĝéš åƒţéŕ åļļ çŕîţîçåļ ŕéšöûŕçéš ĥåvé ƒîñîšĥéð ļöåðîñĝ ţö ļöŵéŕ ţîmé ţö îñţéŕåçţîvé. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ðéƒéŕ öƒƒšçŕééñ îmåĝéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "[Ðéƒéŕ öƒƒšçŕééñ îmåĝéš one two three]"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "[Ŕéšöûŕçéš åŕé бļöçķîñĝ ţĥé ƒîŕšţ þåîñţ öƒ ýöûŕ þåĝé. Çöñšîðéŕ ðéļîvéŕîñĝ çŕîţîçåļ ĴŠ/ÇŠŠ îñļîñé åñð ðéƒéŕŕîñĝ åļļ ñöñ-çŕîţîçåļ ĴŠ/šţýļéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö éļîmîñåţé ŕéñðéŕ-бļöçķîñĝ ŕéšöûŕçéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "[Éļîm<PERSON><PERSON><PERSON><PERSON><PERSON> ŕéñðéŕ-бļöçķîñĝ ŕéšöûŕçéš one two three four]"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "[Ļåŕĝé ñéţŵöŕķ þåýļöåðš çöšţ ûšéŕš ŕéåļ möñéý åñð åŕé ĥîĝĥļý çöŕŕéļåţéð ŵîţĥ ļöñĝ ļöåð ţîméš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ŕéðûçé þåýļöåð šîžéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON> šîžé <PERSON> ᐅ{totalBytes, number, bytes}ᐊ ĶîБ one two three four five]"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "[Åvöîð éñöŕmöûš ñéţŵöŕķ þå<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two three four five six seven]"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> éñöŕmöûš ñéţŵöŕķ þå<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two three four five six seven]"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "[Mîñîƒýîñĝ ÇŠŠ ƒîļéš çåñ ŕéðûçé ñéţŵöŕķ þåýļöåð šîžéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö mîñîƒý ÇŠŠᐅ](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "[<PERSON>î<PERSON><PERSON><PERSON><PERSON> ÇŠŠ one two]"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "[Mîñîƒýîñĝ ĴåvåŠçŕîþţ ƒîļéš çåñ ŕéðûçé þåýļöåð šîžéš åñð šçŕîþţ þåŕšé ţîmé. ᐅ[ᐊĻéåŕñ ĥöŵ ţö mîñîƒý ĴåvåŠçŕîþţᐅ](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "[Mîñîƒý ĴåvåŠçŕîþţ one two three]"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "[Ŕéðûçé ûñûšéð ŕûļéš ƒŕöm šţýļéšĥééţš åñð ðéƒéŕ ÇŠŠ ñöţ ûšéð ƒöŕ åбövé-ţĥé-ƒöļð çöñţéñţ ţö ðéçŕéåšé бýţéš çöñšûméð бý ñéţŵöŕķ åçţîvîţý. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ŕéðûçé ûñûšéð ÇŠŠᐅ](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "[Ŕéðûçé ûñ<PERSON><PERSON><PERSON>ð ÇŠŠ one two three]"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "[Ŕéðûçé ûñûšéð ĴåvåŠçŕîþţ åñð ðéƒéŕ ļöåðîñĝ šçŕîþţš ûñţîļ ţĥéý åŕé ŕéqûîŕéð ţö ðéçŕéåšé бýţéš çöñšûméð бý ñéţŵöŕķ åçţîvîţý. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ŕéðûçé ûñûšéð ĴåvåŠçŕîþţᐅ](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "[Ŕéðûçé ûñûšéð ĴåvåŠçŕîþţ one two three]"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "[Å ļöñĝ çåçĥé ļîƒéţîmé çåñ šþééð ûþ ŕéþéåţ vîšîţš ţö ýöûŕ þåĝé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ éƒƒîçîéñţ çåçĥé þöļîçîéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{[1 ŕéšöûŕçé ƒöûñð one two]}other{[# ŕéšöûŕçéš ƒöûñð one two three]}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "[Šéŕvé šţåţîç åššéţš ŵîţĥ åñ éƒƒîçîéñţ çåçĥé þöļîçý one two three four five six seven eight nine ten eleven]"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "[Ûšéš éƒƒîçîéñţ çåçĥé þöļîçý öñ šţåţîç åššéţš one two three four five six seven eight nine]"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "[Öþţîmîžéð îmåĝéš ļöåð ƒåšţéŕ åñð çöñšûmé ļéšš çéļļûļåŕ ðåţå. ᐅ[ᐊĻéåŕñ ĥöŵ ţö éƒƒîçîéñţļý éñçöðé îmåĝéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "[Éƒƒîçîéñţļý éñçöðé îmåĝéš one two three]"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ðîméñšî<PERSON><PERSON><PERSON> one two three]"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "[Ðîšþļåýéð ðîméñ<PERSON><PERSON><PERSON><PERSON><PERSON> one two three]"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "[Îmåĝéš ŵéŕé ļåŕĝéŕ ţĥåñ ţĥéîŕ ðîšþļåýéð šîžé one two three four five six seven eight nine]"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "[Îmåĝéš ŵéŕé åþþŕöþŕîåţé ƒöŕ ţĥéîŕ ðîšþļåýéð šîžé one two three four five six seven eight nine ten]"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "[Šéŕvé îmåĝéš ţĥåţ åŕé åþþŕöþŕîåţéļý-šîžéð ţö šåvé çéļļûļåŕ ðåţå åñð îmþŕövé ļöåð ţîmé. ᐅ[ᐊĻéåŕñ ĥöŵ ţö šîžé îmåĝéšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "[Þŕöþéŕļý šîžé îmåĝéš one two three]"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "[Ţéxţ-бåšéð ŕéšöûŕçéš šĥöûļð бé šéŕvéð ŵîţĥ çömþŕéššîöñ (ĝžîþ, ðéƒļåţé öŕ бŕöţļî) ţö mîñîmîžé ţöţåļ ñéţŵöŕķ бýţéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţéxţ çömþŕéššîöñᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "[É<PERSON><PERSON><PERSON><PERSON><PERSON> ţ<PERSON> çömþŕéššîöñ one two three]"}, "core/audits/content-width.js | description": {"message": "[Îƒ ţĥé ŵîðţĥ öƒ ýöûŕ åþþ'š çöñţéñţ ðöéšñ'ţ måţçĥ ţĥé ŵîðţĥ öƒ ţĥé vîéŵþöŕţ, ýöûŕ åþþ mîĝĥţ ñöţ бé öþţîmîžéð ƒöŕ möбîļé šçŕééñš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö šîžé çöñţéñţ ƒöŕ ţĥé vîéŵþöŕţᐅ](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/content-width.js | explanation": {"message": "[Ţĥé vîéŵþöŕţ šîžé öƒ ᐅ{innerWidth}ᐊþx ðöéš ñöţ måţçĥ ţĥé ŵîñðöŵ šîžé öƒ ᐅ{outerWidth}ᐊþx. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/content-width.js | failureTitle": {"message": "[Çöñ<PERSON><PERSON><PERSON><PERSON> îš ñöţ šîžéð çöŕŕéçţļý ƒöŕ ţĥé vîéŵþöŕţ one two three four five six seven eight nine ten]"}, "core/audits/content-width.js | title": {"message": "[Çöñ<PERSON><PERSON><PERSON><PERSON> îš šîžéð çöŕŕéçţļý ƒöŕ ţĥé vîéŵþöŕţ one two three four five six seven eight nine]"}, "core/audits/critical-request-chains.js | description": {"message": "[Ţĥé Çŕîţîçåļ Ŕéqûéšţ Çĥåîñš бéļöŵ šĥöŵ ýöû ŵĥåţ ŕéšöûŕçéš åŕé ļöåðéð ŵîţĥ å ĥîĝĥ þŕîöŕîţý. Çöñšîðéŕ ŕéðûçîñĝ ţĥé ļéñĝţĥ öƒ çĥåîñš, ŕéðûçîñĝ ţĥé ðöŵñļöåð šîžé öƒ ŕéšöûŕçéš, öŕ ðéƒéŕŕîñĝ ţĥé ðöŵñļöåð öƒ ûññéçéššåŕý ŕéšöûŕçéš ţö îmþŕövé þåĝé ļöåð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð çĥåîñîñĝ çŕîţîçåļ ŕéqûéšţšᐅ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix]"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{[1 çĥåîñ ƒöûñð one two]}other{[# çĥåîñš ƒöûñð one two]}}"}, "core/audits/critical-request-chains.js | title": {"message": "[Åvöîð çĥåîñîñĝ çŕîţîçåļ ŕéqûéšţš one two three four five six seven]"}, "core/audits/csp-xss.js | columnDirective": {"message": "[Ðîŕéçţîvé one two]"}, "core/audits/csp-xss.js | columnSeverity": {"message": "[Šévéŕîţý one]"}, "core/audits/csp-xss.js | description": {"message": "[Å šţŕöñĝ Çöñţéñţ Šéçûŕîţý Þöļîçý (ÇŠÞ) šîĝñîƒîçåñţļý ŕéðûçéš ţĥé ŕîšķ öƒ çŕöšš-šîţé šçŕîþţîñĝ (XŠŠ) åţţåçķš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ûšé å ÇŠÞ ţö þŕévéñţ XŠŠᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "[Šýñţåx one]"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "[Ţĥé þåĝé çöñţåîñš å ÇŠÞ ðéƒîñéð îñ å ᐅ`<meta>`ᐊ ţåĝ. Çöñšîðéŕ mövîñĝ ţĥé ÇŠÞ ţö åñ ĤŢŢÞ ĥéåðéŕ öŕ ðéƒîñîñĝ åñöţĥéŕ šţŕîçţ ÇŠÞ îñ åñ ĤŢŢÞ ĥéåðéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/csp-xss.js | noCsp": {"message": "[Ñö ÇŠÞ ƒöûñð îñ éñƒöŕçéméñţ möðé one two three four five six seven]"}, "core/audits/csp-xss.js | title": {"message": "[Éñšûŕé ÇŠÞ îš éƒƒéçţîvé åĝåîñšţ XŠŠ åţţåçķš one two three four five six seven eight nine]"}, "core/audits/deprecations.js | columnDeprecate": {"message": "[Ðéþŕéçåţîöñ / Ŵåŕñîñĝ one two three]"}, "core/audits/deprecations.js | columnLine": {"message": "[Ļîñé one]"}, "core/audits/deprecations.js | description": {"message": "[Ðéþŕéçåţéð ÅÞÎš ŵîļļ évéñţûåļļý бé ŕémövéð ƒŕöm ţĥé бŕöŵšéŕ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ðéþŕéçåţéð ÅÞÎšᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{[1 ŵåŕñîñĝ ƒöûñð one two]}other{[# ŵåŕñîñĝš ƒöûñð one two]}}"}, "core/audits/deprecations.js | failureTitle": {"message": "[Ûšéš ðéþŕéçåţéð ÅÞÎš one two three]"}, "core/audits/deprecations.js | title": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ðéþŕéçåţéð ÅÞÎš one two three]"}, "core/audits/dobetterweb/charset.js | description": {"message": "[Å çĥåŕåçţéŕ éñçöðîñĝ ðéçļåŕåţîöñ îš ŕéqûîŕéð. Îţ çåñ бé ðöñé ŵîţĥ å ᐅ`<meta>`ᐊ ţåĝ îñ ţĥé ƒîŕšţ 1024 бýţéš öƒ ţĥé ĤŢMĻ öŕ îñ ţĥé Çöñţéñţ-Ţýþé ĤŢŢÞ ŕéšþöñšé ĥéåðéŕ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ðéçļåŕîñĝ ţĥé çĥåŕåçţéŕ éñçöðîñĝᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "[Çĥåŕšéţ ðéçļåŕåţîöñ îš mîššîñĝ öŕ öççûŕš ţöö ļåţé îñ ţĥé ĤŢMĻ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/dobetterweb/charset.js | title": {"message": "[Þŕöþéŕļý ðéƒîñéš çĥåŕšéţ one two three]"}, "core/audits/dobetterweb/doctype.js | description": {"message": "[Šþéçîƒýîñĝ å ðöçţýþé þŕévéñţš ţĥé бŕöŵšéŕ ƒŕöm šŵîţçĥîñĝ ţö qûîŕķš-möðé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ðöçţýþé ðéçļåŕåţîöñᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "[Ðöçţýþé ñåmé mûšţ бé ţĥé šţŕîñĝ ᐅ`html`ᐊ one two three four five six seven eight]"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "[Ðöçûméñţ çöñţåîñš å ᐅ`doctype`ᐊ ţĥåţ ţŕîĝĝéŕš ᐅ`limited-quirks-mode`ᐊ one two three four five six seven eight nine]"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "[Ðöçûméñţ mûšţ çöñţåîñ å ðöçţýþé one two three four five six seven]"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "[Éxþéçţéð þûбļîçÎð ţö бé åñ émþţý šţŕîñĝ one two three four five six seven eight]"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "[Éxþéçţéð šýšţémÎð ţö бé åñ émþţý šţŕîñĝ one two three four five six seven eight]"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "[Ðöçûméñţ çöñţåîñš å ᐅ`doctype`ᐊ ţĥåţ ţŕîĝĝéŕš ᐅ`quirks-mode`ᐊ one two three four five six seven eight nine]"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "[Þåĝé ļåçķš ţĥé ĤŢMĻ ðöçţýþé, ţĥûš ţŕîĝĝéŕîñĝ qûîŕķš-möðé one two three four five six seven eight nine ten eleven]"}, "core/audits/dobetterweb/doctype.js | title": {"message": "[Þåĝé ĥåš ţĥé ĤŢMĻ ðöçţýþé one two three four five six]"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "[Šţ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "[Våļ<PERSON>é one]"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "[Å ļåŕĝé ÐÖM ŵîļļ îñçŕéåšé mémöŕý ûšåĝé, çå<PERSON><PERSON><PERSON> ļöñĝéŕ ᐅ[ᐊšţýļé çåļçûļåţîöñšᐅ](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)ᐊ, åñð þŕöðûçé çöšţļý ᐅ[ᐊļåýöûţ ŕéƒļöŵšᐅ](https://developers.google.com/speed/articles/reflow)ᐊ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð åñ éxçéššîvé ÐÖM šîžéᐅ](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{[1 éļéméñţ one two]}other{[# éļéméñţš one two]}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "[Åvöîð åñ éxçéššîvé ÐÖM šîžé one two three four five six]"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "[Måxîmûm ÐÖM Ðéþţĥ one two three]"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON> ÐÖM Éļéméñţš one two three]"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "[Måxîmûm Çĥîļð Éļéméñţš one two three]"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "[Å<PERSON><PERSON><PERSON><PERSON><PERSON> åñ éxçéššîvé ÐÖM šîžé one two three four five six]"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "[Ûšéŕš åŕé mîšţŕûšţƒûļ öƒ öŕ çöñƒûšéð бý šîţéš ţĥåţ ŕéqûéšţ ţĥéîŕ ļöçåţîöñ ŵîţĥöûţ çöñţéxţ. Çöñšîðéŕ ţýîñĝ ţĥé ŕéqûéšţ ţö å ûšéŕ åçţîöñ îñšţéåð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé ĝéöļöçåţîöñ þéŕmîššîöñᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "[Ŕéqûéšţš ţĥé ĝéöļöçåţîöñ þéŕmîššîöñ öñ þåĝé ļöåð one two three four five six seven eight nine ten]"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "[Åvö<PERSON><PERSON>š ŕéqûéšţîñĝ ţĥé ĝéöļöçåţîöñ þéŕmîššîöñ öñ þåĝé ļöåð one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "[Î<PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "[Îššûéš ļöĝĝéð ţö ţĥé ᐅ`Issues`ᐊ þåñéļ îñ Çĥŕömé Ðévţööļš îñðîçåţé ûñŕéšöļvéð þŕöбļémš. Ţĥéý çåñ çömé ƒŕöm ñéţŵöŕķ ŕéqûéšţ ƒåîļûŕéš, îñšûƒƒîçîéñţ šéçûŕîţý çöñţŕöļš, åñð öţĥéŕ бŕöŵšéŕ çöñçéŕñš. Öþéñ ûþ ţĥé Îššûéš þåñéļ îñ Çĥŕömé ÐévŢööļš ƒöŕ möŕé ðéţåîļš öñ éåçĥ îššûé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree]"}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "[Îššûéš ŵéŕé ļöĝĝéð îñ ţĥé ᐅ`Issues`ᐊ þåñéļ îñ Çĥŕömé Ðévţööļš one two three four five six seven eight nine ten eleven]"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "[Бļöçķéð бý çŕöšš-öŕîĝîñ þöļîçý one two three four five six seven]"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "[Ĥéåvý ŕéšöûŕçé ûšåĝé бý åð<PERSON> one two three four five six]"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "[Ñö îššûéš îñ ţĥé ᐅ`Issues`ᐊ þåñéļ îñ Çĥŕömé Ðévţööļš one two three four five six seven eight nine]"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "[Véŕšîöñ one]"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "[Åļļ ƒŕöñţ-éñð ĴåvåŠçŕîþţ ļîбŕåŕîéš ðéţéçţéð öñ ţĥé þåĝé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥîš ĴåvåŠçŕîþţ ļîбŕåŕý ðéţéçţîöñ ðîåĝñöšţîç åûðîţᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "[Ðéţéçţéð ĴåvåŠçŕîþţ ļîбŕåŕîéš one two three four]"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "[Föŕ ûšéŕš öñ šļöŵ çöññéçţîöñš, éxţéŕñåļ šçŕîþţš ðýñåmîçåļļý îñĵéçţéð vîå ᐅ`document.write()`ᐊ çåñ ðéļåý þåĝé ļöåð бý ţéñš öƒ šéçöñðš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð ðöçûméñţ.ŵŕîţé()ᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "[Åvöîð ᐅ`document.write()`ᐊ one two three]"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ᐅ`document.write()`ᐊ one two three]"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "[Ûšéŕš åŕé mîšţŕûšţƒûļ öƒ öŕ çöñƒûšéð бý šîţéš ţĥåţ ŕéqûéšţ ţö šéñð ñöţîƒîçåţîöñš ŵîţĥöûţ çöñţéxţ. Çöñšîðéŕ ţýîñĝ ţĥé ŕéqûéšţ ţö ûšéŕ ĝéšţûŕéš îñšţéåð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ŕéšþöñšîбļý ĝéţţîñĝ þéŕmîššîöñ ƒöŕ ñöţîƒîçåţîöñšᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "[Ŕéqûéšţš ţĥé ñöţîƒîçåţîöñ þéŕmîššîöñ öñ þåĝé ļöåð one two three four five six seven eight nine ten]"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "[Åvöîðš ŕéqûéšţîñĝ ţĥé ñöţîƒîçåţîöñ þéŕmîššîöñ öñ þåĝé ļöåð one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "[Þŕévéñţîñĝ îñþûţ þåšţîñĝ îš å бåð þŕåçţîçé ƒöŕ ţĥé ÛX, åñð ŵéåķéñš šéçûŕîţý бý бļöçķîñĝ þåššŵöŕð måñåĝéŕš.ᐅ[ᐊĻéåŕñ möŕé åбöûţ ûšéŕ-ƒŕîéñðļý îñþûţ ƒîéļðšᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "[Þŕévéñţš ûšéŕš ƒŕöm þåšţîñĝ îñţö îñþûţ ƒîéļðš one two three four five six seven eight nine]"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "[Åļļöŵš ûšéŕš ţö þåšţé îñţö îñþûţ ƒîéļðš one two three four five six seven eight]"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "[Þŕöţöçöļ one]"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "[ĤŢŢÞ/2 öƒƒéŕš måñý бéñéƒîţš övéŕ ĤŢŢÞ/1.1, îñçļûðîñĝ бîñåŕý ĥéåðéŕš åñð mûļţîþļéxîñĝ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ĤŢŢÞ/2ᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{[1 ŕéqûéšţ ñöţ šéŕvéð vîå ĤŢŢÞ/2 one two three four five six seven]}other{[# ŕéqûéšţš ñöţ šéŕvéð vîå ĤŢŢÞ/2 one two three four five six seven]}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "[Ûšé ĤŢŢÞ/2 one two]"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "[Çöñšîðéŕ måŕķîñĝ ýöûŕ ţöûçĥ åñð ŵĥééļ évéñţ ļîšţéñéŕš åš ᐅ`passive`ᐊ ţö îmþŕövé ýöûŕ þåĝé'š šçŕöļļ þéŕƒöŕmåñçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ åðöþţîñĝ þåššîvé évéñţ ļîšţéñéŕšᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "[Ðöéš ñöţ ûšé þåššîvé ļîšţéñéŕš ţö îmþŕövé šçŕöļļîñĝ þéŕƒöŕmåñçé one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "[Ûšéš þåššîvé ļîšţéñéŕš ţö îmþŕövé šçŕöļļîñĝ þéŕƒöŕmåñçé one two three four five six seven eight nine ten eleven]"}, "core/audits/errors-in-console.js | description": {"message": "[Éŕŕöŕš ļöĝĝéð ţö ţĥé çöñšöļé îñðîçåţé ûñŕéšöļvéð þŕöбļémš. Ţĥéý çåñ çömé ƒŕöm ñéţŵöŕķ ŕéqûéšţ ƒåîļûŕéš åñð öţĥéŕ бŕöŵšéŕ çöñçéŕñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥîš éŕŕöŕš îñ çöñšöļé ðîåĝñöšţîç åûðîţᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/errors-in-console.js | failureTitle": {"message": "[Бŕöŵšéŕ éŕŕöŕš ŵéŕé ļöĝĝéð ţö ţĥé çöñšöļé one two three four five six seven eight nine]"}, "core/audits/errors-in-console.js | title": {"message": "[Ñö бŕöŵšéŕ éŕŕöŕš ļöĝĝéð ţö ţĥé çöñšöļé one two three four five six seven eight]"}, "core/audits/font-display.js | description": {"message": "[Ļévéŕåĝé ţĥé ᐅ`font-display`ᐊ ÇŠŠ ƒéåţûŕé ţö éñšûŕé ţéxţ îš ûšéŕ-vîšîбļé ŵĥîļé ŵéбƒöñţš åŕé ļöåðîñĝ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ᐅ`font-display`ᐊᐅ](https://developer.chrome.com/docs/lighthouse/performance/font-display/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/font-display.js | failureTitle": {"message": "[Éñšûŕé ţéxţ ŕémåîñš vîšîбļé ðûŕîñĝ ŵéбƒöñţ ļöåð one two three four five six seven eight nine ten]"}, "core/audits/font-display.js | title": {"message": "[Åļ<PERSON> ţéxţ ŕémåîñš vîšîбļé ðûŕîñĝ ŵéбƒöñţ ļö<PERSON>ð<PERSON> one two three four five six seven eight nine]"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö åûţömåţîçåļļý çĥéçķ ţĥé ᐅ`font-display`ᐊ våļûé ƒöŕ ţĥé öŕîĝîñ ᐅ{fontOrigin}ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]}other{[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö åûţömåţîçåļļý çĥéçķ ţĥé ᐅ`font-display`ᐊ våļûéš ƒöŕ ţĥé öŕîĝîñ ᐅ{fontOrigin}ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "[Å<PERSON><PERSON><PERSON><PERSON><PERSON> Ŕåţîö (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) one two three]"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "[Åš<PERSON><PERSON><PERSON>ţ Ŕåţîö (Ðîšþļåýéð) one two three]"}, "core/audits/image-aspect-ratio.js | description": {"message": "[Îmåĝé ðîšþļåý ðîméñšîöñš šĥöûļð måţçĥ ñåţûŕåļ åšþéçţ ŕåţîö. ᐅ[ᐊĻéåŕñ möŕé åбöûţ îmåĝé åšþéçţ ŕåţîöᐅ](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "[Ðîšþļåýš îmåĝéš ŵîţĥ îñçöŕŕéçţ åšþéçţ ŕåţîö one two three four five six seven eight nine]"}, "core/audits/image-aspect-ratio.js | title": {"message": "[Ðîšþļåýš îmåĝéš ŵîţĥ çöŕŕéçţ åšþéçţ ŕåţîö one two three four five six seven eight nine]"}, "core/audits/image-size-responsive.js | columnActual": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šîžé one two]"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "[Ðîšþļåýéð šîžé one two]"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "[Éxþ<PERSON><PERSON><PERSON>éð šîžé one two]"}, "core/audits/image-size-responsive.js | description": {"message": "[Îmåĝé ñåţûŕåļ ðîméñšîöñš šĥöûļð бé þŕöþöŕţîöñåļ ţö ţĥé ðîšþļåý šîžé åñð ţĥé þîxéļ ŕåţîö ţö måxîmîžé îmåĝé çļåŕîţý. ᐅ[ᐊĻéåŕñ ĥöŵ ţö þŕövîðé ŕéšþöñšîvé îmåĝéšᐅ](https://web.dev/articles/serve-responsive-images)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "[Šéŕvéš îmåĝéš ŵîţĥ ļöŵ ŕéšöļûţîöñ one two three four five six seven]"}, "core/audits/image-size-responsive.js | title": {"message": "[Šéŕvéš îmåĝéš ŵîţĥ åþþŕöþŕîåţé ŕéšöļûţîöñ one two three four five six seven eight nine]"}, "core/audits/installable-manifest.js | already-installed": {"message": "[Ţĥé åþþ îš åļŕéåðý îñšţåļļéð one two three four five six]"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "[Çöûļð ñöţ ðöŵñļöåð å ŕéqûîŕéð îçöñ ƒŕöm ţĥé måñîƒéšţ one two three four five six seven eight nine ten eleven]"}, "core/audits/installable-manifest.js | columnValue": {"message": "[Fåîļûŕé ŕéåšöñ one two]"}, "core/audits/installable-manifest.js | description": {"message": "[Šéŕvîçé ŵöŕķéŕ îš ţĥé ţéçĥñöļöĝý ţĥåţ éñåбļéš ýöûŕ åþþ ţö ûšé måñý Þŕöĝŕéššîvé Ŵéб Åþþ ƒéåţûŕéš, šûçĥ åš öƒƒļîñé, åðð ţö ĥöméšçŕééñ, åñð þûšĥ ñöţîƒîçåţîöñš. Ŵîţĥ þŕöþéŕ šéŕvîçé ŵöŕķéŕ åñð måñîƒéšţ îmþļéméñţåţîöñš, бŕöŵšéŕš çåñ þŕöåçţîvéļý þŕömþţ ûšéŕš ţö åðð ýöûŕ åþþ ţö ţĥéîŕ ĥöméšçŕééñ, ŵĥîçĥ çåñ ļéåð ţö ĥîĝĥéŕ éñĝåĝéméñţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ måñîƒéšţ îñšţåļļåбîļîţý ŕéqûîŕéméñţšᐅ](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine]"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{[1 ŕéåšöñ one]}other{[# ŕéåšöñš one two]}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "[Ŵéб åþþ måñîƒéšţ öŕ šéŕvîçé ŵöŕķéŕ ðö ñöţ mééţ ţĥé îñšţåļļåбîļîţý ŕéqûîŕéméñţš one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "[Ţĥé Þļåý Šţöŕé åþþ ÛŔĻ åñð Þļåý Šţöŕé ÎÐ ðö ñöţ måţçĥ one two three four five six seven eight nine ten eleven]"}, "core/audits/installable-manifest.js | in-incognito": {"message": "[Þåĝé îš ļöåðéð îñ åñ îñçöĝñîţö ŵîñðöŵ one two three four five six seven eight]"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "[Måñîƒéšţ ᐅ`display`ᐊ þŕöþéŕţý mûšţ бé öñé öƒ ᐅ`standalone`ᐊ, ᐅ`fullscreen`ᐊ, öŕ ᐅ`minimal-ui`ᐊ one two three four five six seven eight nine ten eleven]"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "[Måñîƒéš<PERSON> çöñţåîñ<PERSON> 'ðîš<PERSON>ļåý_övéŕŕîðé' ƒîé<PERSON><PERSON>, åñð ţĥé ƒîŕšţ šûþþöŕţéð ðîšþļåý möðé mûšţ бé öñé öƒ 'šţåñðåļöñé', 'ƒûļļšçŕééñ', öŕ 'mîñîmåļ-ûî' one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "[Måñîƒéšţ çöûļð ñöţ бé ƒéţçĥéð, îš émþţý, öŕ çöûļð ñöţ бé þåŕšéð one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "[Måñîƒéšţ ÛŔĻ çĥåñĝéð ŵĥîļé ţĥé måñîƒéšţ ŵåš бéîñĝ ƒéţçĥéð. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "[Måñîƒ<PERSON><PERSON><PERSON> ðöéš ñöţ çöñţåîñ å ᐅ`name`ᐊ öŕ ᐅ`short_name`ᐊ ƒîéļð one two three four five six seven eight nine]"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "[Måñîƒéš<PERSON> ðöéš ñöţ çöñţåîñ å šûîţåбļé îçöñ - ÞÑĜ, ŠVĜ öŕ ŴéбÞ ƒöŕmåţ öƒ åţ ļéåšţ ᐅ{value0}ᐊ þx îš ŕéqûîŕéð, ţĥé šîžéš åţţŕîбûţé mûšţ бé šéţ, åñð ţĥé þûŕþöšé åţţŕîбûţé, îƒ šéţ, mûšţ îñçļûðé \"åñý\". one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "[Ñö šûþþļîéð îçöñ îš åţ ļéåšţ ᐅ{value0}ᐊ þx šqûåŕé îñ ÞÑĜ, ŠVĜ öŕ ŴéбÞ ƒöŕmåţ, ŵîţĥ ţĥé þûŕþöšé åţţŕîбûţé ûñšéţ öŕ šéţ ţö \"åñý\" one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "[Ðöŵñļöåðéð îçö<PERSON> ŵåš <PERSON>mþţý öŕ çöŕŕûþţéð one two three four five six seven eight]"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "[Ñö Þļåý šţöŕé ÎÐ þŕövîðéð one two three four five six]"}, "core/audits/installable-manifest.js | no-manifest": {"message": "[Þåĝé ĥåš ñö måñîƒéšţ <link> ÛŔĻ one two three four five six]"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "[Çöûļð ñöţ çĥéçķ šéŕvîçé ŵöŕķéŕ ŵîţĥöûţ å 'šţåŕţ_ûŕļ' ƒîéļð îñ ţĥé måñîƒéšţ one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/audits/installable-manifest.js | noErrorId": {"message": "[Îñšţåļļåбîļîţý éŕŕöŕ îð 'ᐅ{errorId}ᐊ' îš <PERSON>öţ ŕéçöĝñîžéð one two three four five six seven eight nine ten]"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "[Þåĝé îš ñöţ šéŕvéð ƒŕöm å šéçûŕé öŕîĝîñ one two three four five six seven eight]"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "[Þåĝé îš <PERSON>öţ <PERSON>ðéð îñ ţĥé måîñ ƒŕåmé one two three four five six seven eight]"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "[Þåĝé ðöéš ñöţ ŵöŕķ öƒƒļîñé one two three four five six]"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "[ÞŴÅ ĥåš бééñ ûñîñšţåļļéð åñð îñšţåļļåбîļîţý çĥéçķš ŕéšéţţîñĝ. one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "[Ţĥé šþéçîƒîéð åþþļîçåţîöñ þļåţƒöŕm îš ñöţ šûþþöŕţéð öñ Åñðŕöîð one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "[Måñîƒéšţ šþéçîƒîéš þŕéƒéŕ_ŕéļåţéð_åþþļîçåţîöñš: ţŕûé one two three four five six seven eight nine ten eleven]"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "[þŕéƒéŕ_ŕéļåţéð_åþþļî<PERSON>åţîöñš îš öñļý šûþþöŕţéð öñ Çĥŕömé Бéţå åñð Šţåбļé çĥåññéļš öñ Åñðŕöîð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "[Ļîĝĥţĥöûšé çöûļð ñöţ ðéţéŕmîñé îƒ ţĥé þåĝé îš îñšţåļļåбļé. Þļéåšé ţŕý ŵîţĥ å ñéŵéŕ véŕšîöñ öƒ Çĥŕömé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "[Måñîƒéšţ šţåŕţ ÛŔĻ îš ñöţ våļîð one two three four five six seven]"}, "core/audits/installable-manifest.js | title": {"message": "[Ŵéб åþþ måñîƒéšţ åñð šéŕvîçé ŵöŕķéŕ mééţ ţĥé îñšţåļļåбîļîţý ŕéqûîŕéméñţš one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "[Å ÛŔĻ îñ ţĥé måñîƒéšţ çöñţåîñš å ûšéŕñåmé, þåššŵöŕð, öŕ þöŕţ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "[Þåĝé ðöéš ñöţ ŵöŕķ öƒƒļîñé. Ţĥé þåĝé ŵîļļ ñöţ бé ŕéĝåŕðéð åš îñšţåļļåбļé åƒţéŕ Çĥŕömé 93, šţåбļé ŕéļéåšé Åûĝûšţ 2021. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/is-on-https.js | allowed": {"message": "[Åļļöŵéð one]"}, "core/audits/is-on-https.js | blocked": {"message": "[Бļöçķéð one]"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "[Îñšéçûŕé ÛŔĻ one two]"}, "core/audits/is-on-https.js | columnResolution": {"message": "[Ŕéqûéšţ Ŕéšöļûţîöñ one two three]"}, "core/audits/is-on-https.js | description": {"message": "[Å<PERSON><PERSON> šîţéš šĥöûļð бé þŕöţéçţéð ŵîţĥ ĤŢŢÞŠ, évéñ öñéš ţĥåţ ðöñ'ţ ĥåñðļé šéñšîţîvé ðåţå. Ţĥîš îñçļûðéš åvöîðîñĝ ᐅ[ᐊmîxéð çöñţéñţᐅ](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)ᐊ, ŵĥéŕé šömé ŕéšöûŕçéš åŕé ļöåðéð övéŕ ĤŢŢÞ ðéšþîţé ţĥé îñîţîåļ ŕéqûéšţ бéîñĝ šéŕvéð övéŕ ĤŢŢÞŠ. ĤŢŢÞŠ þŕévéñţš îñţŕûðéŕš ƒŕöm ţåmþéŕîñĝ ŵîţĥ öŕ þåššîvéļý ļîšţéñîñĝ îñ öñ ţĥé çömmûñîçåţîöñš бéţŵééñ ýöûŕ åþþ åñð ýöûŕ ûšéŕš, åñð îš å þŕéŕéqûîšîţé ƒöŕ ĤŢŢÞ/2 åñð måñý ñéŵ ŵéб þļåţƒöŕm ÅÞÎš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ĤŢŢÞŠᐅ](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{[1 îñšéçûŕé ŕéqûéšţ ƒöûñð one two three four five]}other{[# îñšéçûŕé ŕéqûéšţš ƒöûñð one two three four five six]}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "[Ðöéš ñöţ ûšé ĤŢŢÞŠ one two three four]"}, "core/audits/is-on-https.js | title": {"message": "[Ûšéš ĤŢŢÞŠ one two]"}, "core/audits/is-on-https.js | upgraded": {"message": "[Åûţömåţîçåļļý ûþĝŕåðéð ţö ĤŢŢÞŠ one two three four five six seven]"}, "core/audits/is-on-https.js | warning": {"message": "[Åļ<PERSON><PERSON>ŵéð ŵîţĥ ŵåŕñîñĝ one two three]"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "[% oƒ ĻÇÞ one]"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "[Þĥåšé one]"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "[Ţîmîñĝ one]"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "[Ţĥîš îš ţĥé ļåŕĝéšţ çöñţéñţƒûļ éļéméñţ þåîñţéð ŵîţĥîñ ţĥé vîéŵþöŕţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ éļéméñţᐅ](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "[Ļöåð Ðéļåý one two]"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "[Ļöåð <PERSON>îmé one two]"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "[Ŕéñðéŕ Ðéļåý one two]"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "[ŢŢFБ one]"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "[Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ éļéméñţ one two three four five six seven]"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "[Ļåýöûţ šĥîƒţ îmþ<PERSON><PERSON><PERSON> one two three]"}, "core/audits/layout-shift-elements.js | description": {"message": "[Ţĥéšé ÐÖM éļéméñţš ŵéŕé möšţ åƒƒéçţéð бý ļåýöûţ šĥîƒţš. Šö<PERSON> ļåýöûţ šĥîƒţš måý ñöţ бé îñçļûðéð îñ ţĥé ÇĻŠ méţŕîç våļûé ðûé ţö ᐅ[ᐊŵîñðöŵîñĝᐅ](https://web.dev/articles/cls#what_is_cls)ᐊ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö îmþŕövé ÇĻŠᐅ](https://web.dev/articles/optimize-cls)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/layout-shift-elements.js | title": {"message": "[Åvöîð ļåŕĝé ļåýöûţ šĥîƒţš one two three four five six]"}, "core/audits/layout-shifts.js | columnScore": {"message": "[Ļåýöûţ šĥîƒţ šçöŕé one two three]"}, "core/audits/layout-shifts.js | description": {"message": "[Ţĥéšé åŕé ţĥé ļåŕĝéšţ ļåýöûţ šĥîƒţš öбšéŕvéð öñ ţĥé þåĝé. Éåçĥ ţåбļé îţém ŕéþŕéšéñţš å šîñĝļé ļåýöûţ šĥîƒţ, åñð šĥöŵš ţĥé éļéméñţ ţĥåţ šĥîƒţéð ţĥé möšţ. Бéļöŵ éåçĥ îţém åŕé þöššîбļé ŕööţ çåûšéš ţĥåţ ļéð ţö ţĥé ļåýöûţ šĥîƒţ. Šömé öƒ ţĥéšé ļåýöûţ šĥîƒţš måý ñöţ бé îñçļûðéð îñ ţĥé ÇĻŠ méţŕîç våļûé ðûé ţö ᐅ[ᐊŵîñðöŵîñĝᐅ](https://web.dev/articles/cls#what_is_cls)ᐊ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö îmþŕövé ÇĻŠᐅ](https://web.dev/articles/optimize-cls)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two]"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{[1 ļåýöûţ šĥîƒţ ƒöûñð one two three four five]}other{[# ļåýöûţ šĥîƒţš ƒöûñð one two three four five]}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "[Ŵéб <PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "[Îñĵéçţéð îƒŕåmé one two]"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "[Å ļåţé ñéţŵöŕķ ŕéqûéšţ åðĵûšţéð ţĥé þåĝé ļåýöûţ one two three four five six seven eight nine ten]"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "[Méðîå éļéméñţ ļåçķîñĝ åñ éxþļîçîţ šîžé one two three four five six seven eight]"}, "core/audits/layout-shifts.js | title": {"message": "[Åvöîð ļåŕĝé ļåýöûţ šĥîƒţš one two three four five six]"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "[Åбövé-ţĥé-ƒöļð îmåĝéš ţĥåţ åŕé ļåžîļý ļöåðéð ŕéñðéŕ ļåţéŕ îñ ţĥé þåĝé ļîƒéçýçļé, ŵĥîçĥ çåñ ðéļåý ţĥé ļåŕĝéšţ çöñţéñţƒûļ þåîñţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ öþţîmåļ ļåžý ļöåðîñĝᐅ](https://web.dev/articles/lcp-lazy-loading)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "[Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ îmåĝé ŵåš ļåžîļý ļöåðéð one two three four five six seven eight nine ten]"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "[Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ îmåĝé ŵåš ñöţ ļåžîļý ļöåðéð one two three four five six seven eight nine ten eleven]"}, "core/audits/long-tasks.js | description": {"message": "[Ļîšţš ţĥé ļöñĝéšţ ţåšķš öñ ţĥé måîñ ţĥŕéåð, ûšéƒûļ ƒöŕ îðéñţîƒýîñĝ ŵöŕšţ çöñţŕîбûţöŕš ţö îñþûţ ðéļåý. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð ļöñĝ måîñ-ţĥŕéåð ţåšķšᐅ](https://web.dev/articles/long-tasks-devtools)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{[# ļöñĝ ţåšķ ƒöûñð one two three four]}other{[# ļöñĝ ţåšķš ƒöûñð one two three four]}}"}, "core/audits/long-tasks.js | title": {"message": "[Åvöîð ļöñĝ måîñ-ţĥŕéåð ţåšķš one two three four five six]"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "[Çåţéĝöŕý one]"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ ţĥé ţîmé šþéñţ þåŕšîñĝ, çömþîļîñĝ åñð éxéçûţîñĝ ĴŠ. Ýöû måý ƒîñð ðéļîvéŕîñĝ šmåļļéŕ ĴŠ þåýļöåðš ĥéļþš ŵîţĥ ţĥîš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö mîñîmîžé måîñ-ţĥŕéåð ŵöŕķᐅ](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "[Mî<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> måîñ-ţĥŕéåð ŵöŕķ one two three]"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "[Mî<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> måîñ-ţĥŕéåð ŵöŕķ one two three]"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "[Ţö ŕéåçĥ ţĥé möšţ ñûmбéŕ öƒ ûšéŕš, šî<PERSON><PERSON>š šĥöûļð ŵöŕķ åçŕöšš évéŕý måĵöŕ бŕöŵšéŕ. ᐅ[ᐊĻéåŕñ åбöûţ çŕöšš-бŕöŵšéŕ çömþåţîбîļîţýᐅ](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "[Šîţé ŵöŕķš çŕöšš-бŕöŵšéŕ one two three]"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "[Éñšûŕé îñðîvîðûåļ þåĝéš åŕé ðééþ ļîñķåбļé vîå ÛŔĻ åñð ţĥåţ ÛŔĻš åŕé ûñîqûé ƒöŕ ţĥé þûŕþöšé öƒ šĥåŕéåбîļîţý öñ šöçîåļ méðîå. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þŕövîðîñĝ ðééþ ļîñķšᐅ](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "[Éåçĥ þåĝé ĥåš å ÛŔĻ one two three four]"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "[Ţŕåñšîţîöñš šĥöûļð ƒééļ šñåþþý åš ýöû ţåþ åŕöûñð, évéñ öñ å šļöŵ ñéţŵöŕķ. Ţĥîš éxþéŕîéñçé îš ķéý ţö å ûšéŕ'š þéŕçéþţîöñ öƒ þéŕƒöŕmåñçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þåĝé ţŕåñšîţîöñšᐅ](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "[Þåĝé ţŕåñšîţîöñš ðöñ'ţ ƒééļ ļîķé ţĥéý бļöçķ öñ ţĥé ñéţŵöŕķ one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/maskable-icon.js | description": {"message": "[Å måšķåбļé îçöñ éñšûŕéš ţĥåţ ţĥé îmåĝé ƒîļļš ţĥé éñţîŕé šĥåþé ŵîţĥöûţ бéîñĝ ļéţţéŕбöxéð ŵĥéñ îñšţåļļîñĝ ţĥé åþþ öñ å ðévîçé. ᐅ[ᐊĻéåŕñ åбöûţ måšķåбļé måñîƒéšţ îçöñšᐅ](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/maskable-icon.js | failureTitle": {"message": "[Måñîƒéšţ ðöéšñ'ţ ĥåvé å måšķåбļé îçöñ one two three four five six seven eight]"}, "core/audits/maskable-icon.js | title": {"message": "[Måñîƒéšţ ĥåš å måšķåбļé îçöñ one two three four five six]"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "[Çûmûļåţîvé Ļåýöûţ Šĥîƒţ méåšûŕéš ţĥé mövéméñţ öƒ vîšîбļé éļéméñţš ŵîţĥîñ ţĥé vîéŵþöŕţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Çûmûļåţîvé Ļåýöûţ Šĥîƒţ méţŕîçᐅ](https://web.dev/articles/cls)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "[Fîŕšţ Çöñţéñţƒûļ Þåîñţ måŕķš ţĥé ţîmé åţ ŵĥîçĥ ţĥé ƒîŕšţ ţéxţ öŕ îmåĝé îš þåîñţéð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Fîŕšţ Çöñţéñţƒûļ Þåîñţ méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "[Fîŕšţ Méåñîñĝƒûļ Þåîñţ méåšûŕéš ŵĥéñ ţĥé þŕîmåŕý çöñţéñţ öƒ å þåĝé îš vîšîбļé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Fîŕšţ Méåñîñĝƒûļ Þåîñţ méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "[Îñţéŕåçţîöñ ţö Ñéxţ Þåîñţ méåšûŕéš þåĝé ŕéšþöñšîvéñéšš, ĥöŵ ļöñĝ îţ ţåķéš ţĥé þåĝé ţö vîšîбļý ŕéšþöñð ţö ûšéŕ îñþûţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Îñţéŕåçţîöñ ţö Ñéxţ Þåîñţ méţŕîçᐅ](https://web.dev/articles/inp)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/metrics/interactive.js | description": {"message": "[Ţîmé ţö Îñţéŕåçţîvé îš ţĥé åmöûñţ öƒ ţîmé îţ ţåķéš ƒöŕ ţĥé þåĝé ţö бéçömé ƒûļļý îñţéŕåçţîvé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ţîmé ţö Îñţéŕåçţîvé méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/interactive/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "[Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ måŕķš ţĥé ţîmé åţ ŵĥîçĥ ţĥé ļåŕĝéšţ ţéxţ öŕ îmåĝé îš þåîñţéð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "[Ţĥé måxîmûm þöţéñţîåļ Fîŕšţ Îñþûţ Ðéļåý ţĥåţ ýöûŕ ûšéŕš çöûļð éxþéŕîéñçé îš ţĥé ðûŕåţîöñ öƒ ţĥé ļöñĝéšţ ţåšķ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Måxîmûm Þöţéñţîåļ Fîŕšţ Îñþûţ Ðéļåý méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/audits/metrics/speed-index.js | description": {"message": "[Šþééð Îñðéx šĥöŵš ĥöŵ qûîçķļý ţĥé çöñţéñţš öƒ å þåĝé åŕé vîšîбļý þöþûļåţéð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Šþééð Îñðéx méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "[Šûm öƒ åļļ ţîmé þéŕîöðš бéţŵééñ FÇÞ åñð Ţîmé ţö Îñţéŕåçţîvé, ŵĥéñ ţåšķ ļéñĝţĥ éxçééðéð 50mš, éxþŕéššéð îñ mîļļîšéçöñðš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ţöţåļ Бļöçķîñĝ Ţîmé méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/network-rtt.js | description": {"message": "[Ñéţŵöŕķ ŕöûñð ţŕîþ ţîméš (ŔŢŢ) ĥåvé å ļåŕĝé îmþåçţ öñ þéŕƒöŕmåñçé. Îƒ ţĥé ŔŢŢ ţö åñ öŕîĝîñ îš ĥîĝĥ, îţ'š åñ îñðîçåţîöñ ţĥåţ šéŕvéŕš çļöšéŕ ţö ţĥé ûšéŕ çöûļð îmþŕövé þéŕƒöŕmåñçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ŕöûñð Ţŕîþ Ţîméᐅ](https://hpbn.co/primer-on-latency-and-bandwidth/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "core/audits/network-rtt.js | title": {"message": "[Ñéţŵöŕķ Ŕöûñð Ţŕîþ Ţîméš one two three four five]"}, "core/audits/network-server-latency.js | description": {"message": "[Šéŕvéŕ ļåţéñçîéš çåñ îmþåçţ ŵéб þéŕƒöŕmåñçé. Îƒ ţĥé šéŕvéŕ ļåţéñçý öƒ åñ öŕîĝîñ îš ĥîĝĥ, îţ'š åñ îñðîçåţîöñ ţĥé šéŕvéŕ îš övéŕļöåðéð öŕ ĥåš þööŕ бåçķéñð þéŕƒöŕmåñçé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ šéŕvéŕ ŕéšþöñšé ţîméᐅ](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/network-server-latency.js | title": {"message": "[Šéŕvéŕ Бåçķéñð Ļåţéñçîéš one two three]"}, "core/audits/no-unload-listeners.js | description": {"message": "[Ţĥé ᐅ`unload`ᐊ évé<PERSON><PERSON> ðöéš ñöţ ƒîŕé ŕéļîåбļý åñð ļîšţéñîñĝ ƒöŕ îţ çåñ þŕévéñţ бŕöŵšéŕ öþţîmîžåţîöñš ļîķé ţĥé Бåçķ-Föŕŵåŕð Çåçĥé. Ûšé ᐅ`pagehide`ᐊ öŕ ᐅ`visibilitychange`ᐊ évéñţš îñšţéåð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ûñļöåð évéñţ ļîšţéñéŕšᐅ](https://web.dev/articles/bfcache#never_use_the_unload_event)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "[Ŕéĝîšţéŕš åñ ᐅ`unload`ᐊ ļîšţéñéŕ one two three four five six]"}, "core/audits/no-unload-listeners.js | title": {"message": "[Åv<PERSON><PERSON><PERSON><PERSON> ᐅ`unload`ᐊ évéñţ ļîšţéñéŕš one two three four five six]"}, "core/audits/non-composited-animations.js | description": {"message": "[Åñîm<PERSON><PERSON>îöñš ŵĥîçĥ åŕé ñöţ çömþöšîţéð çåñ бé ĵåñķý åñð îñçŕéåšé ÇĻŠ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð ñöñ-çömþöšîţéð åñîmåţîöñšᐅ](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{[# åñîmåţéð éļéméñţ ƒöûñð one two three four five]}other{[# åñîmåţéð éļéméñţš ƒöûñð one two three four five six]}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "[Fîļţéŕ-ŕéļåţéð þŕöþéŕţý må<PERSON> mövé þîx<PERSON><PERSON> one two three four five six seven eight]"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "[Ţåŕĝéţ ĥåš åñöţĥéŕ åñîmåţîöñ ŵĥîçĥ îš îñçömþåţîбļé one two three four five six seven eight nine ten eleven]"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "[Éƒƒéçţ ĥåš çömþöšîţé möðé öţĥéŕ ţĥåñ \"ŕéþļåçé\" one two three four five six seven eight nine ten]"}, "core/audits/non-composited-animations.js | title": {"message": "[Åvöîð ñöñ-çömþöšîţéð åñîmåţîöñ<PERSON> one two three four]"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "[Ţŕåñšƒöŕm-ŕéļåţéð þŕöþéŕţý ðéþéñðš öñ бöx šîžé one two three four five six seven eight nine ten]"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{[Ûñšûþþöŕţéð ÇŠŠ Þŕöþéŕţý: ᐅ{properties}ᐊ one two three four five six seven]}other{[Ûñšûþþöŕţéð ÇŠŠ Þŕöþéŕţîéš: ᐅ{properties}ᐊ one two three four five six seven]}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "[Éƒƒéçţ ĥåš ûñšûþþöŕţéð ţîmîñĝ þåŕåméţéŕš one two three four five six seven eight]"}, "core/audits/performance-budget.js | description": {"message": "[Ķééþ ţĥé qûåñţîţý åñð šîžé öƒ ñéţŵöŕķ ŕéqûéšţš ûñðéŕ ţĥé ţåŕĝéţš šéţ бý ţĥé þŕövîðéð þéŕƒöŕmåñçé бûðĝéţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þéŕƒöŕmåñçé бûðĝéţšᐅ](https://developers.google.com/web/tools/lighthouse/audits/budgets)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{[1 ŕéqûéšţ one two]}other{[# ŕéqûéšţš one two]}}"}, "core/audits/performance-budget.js | title": {"message": "[Þéŕƒöŕmåñçé бûðĝéţ one two three]"}, "core/audits/preload-fonts.js | description": {"message": "[Þŕéļöåð ᐅ`optional`ᐊ ƒöñţš šö ƒîŕšţ-ţîmé vîšîţöŕš måý ûšé ţĥém. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þŕéļöåðîñĝ ƒöñţšᐅ](https://web.dev/articles/preload-optional-fonts)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/audits/preload-fonts.js | failureTitle": {"message": "[Föñţš ŵîţĥ ᐅ`font-display: optional`ᐊ åŕé ñöţ þŕéļöåðéð one two three four five six seven]"}, "core/audits/preload-fonts.js | title": {"message": "[Föñţš ŵîţĥ ᐅ`font-display: optional`ᐊ åŕé þŕéļöåðéð one two three four five six]"}, "core/audits/prioritize-lcp-image.js | description": {"message": "[Îƒ ţĥé ĻÇÞ éļéméñţ îš ðýñåmîçåļļý åððéð ţö ţĥé þåĝé, ýöû šĥöûļð þŕéļöåð ţĥé îmåĝé îñ öŕðéŕ ţö îmþŕövé ĻÇÞ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þŕéļöåðîñĝ ĻÇÞ éļéméñţšᐅ](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/prioritize-lcp-image.js | title": {"message": "[Þŕéļöåð Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ îmåĝé one two three four five six seven eight]"}, "core/audits/redirects.js | description": {"message": "[Ŕéðîŕéçţš îñţŕöðûçé åððîţ<PERSON><PERSON><PERSON><PERSON><PERSON> ðéļåýš бéƒöŕé ţĥé þåĝé çåñ бé ļöåðéð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö åvöîð þåĝé ŕéðîŕéçţšᐅ](https://developer.chrome.com/docs/lighthouse/performance/redirects/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/redirects.js | title": {"message": "[Åv<PERSON><PERSON><PERSON> mûļţîþļé þåĝé ŕéðîŕéçţš one two three four five six seven]"}, "core/audits/seo/canonical.js | description": {"message": "[Çåñöñîçåļ ļîñķš šûĝĝéšţ ŵĥîçĥ ÛŔĻ ţö šĥöŵ îñ šéåŕçĥ ŕéšûļţš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ çåñöñîçåļ ļîñķšᐅ](https://developer.chrome.com/docs/lighthouse/seo/canonical/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "[Mû<PERSON><PERSON><PERSON>þļé çöñƒļîçţîñĝ ÛŔĻš (ᐅ{urlList}ᐊ) one two three four five six seven]"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "[Îñvåļîð ÛŔĻ (ᐅ{url}ᐊ) one two three four]"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "[Þöîñţš ţö åñöţĥéŕ ᐅ`hreflang`ᐊ ļöçåţîöñ (ᐅ{url}ᐊ) one two three four five six seven eight]"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "[Îš ñöţ åñ åбšöļûţé ÛŔĻ (ᐅ{url}ᐊ) one two three four five six]"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "[Þöîñţš ţö ţĥé ðömåîñ'š ŕööţ ÛŔĻ (ţĥé ĥöméþåĝé), îñšţéåð öƒ åñ éqûîvåļéñţ þåĝé öƒ çöñţéñţ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/seo/canonical.js | failureTitle": {"message": "[Ðöçûméñţ <PERSON><PERSON><PERSON><PERSON>ö<PERSON> ĥåvé å våļîð ᐅ`rel=canonical`ᐊ one two three four five six seven]"}, "core/audits/seo/canonical.js | title": {"message": "[Ðöçûméñţ ĥåš å våļîð ᐅ`rel=canonical`ᐊ one two three four five]"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "[Ûñçŕåŵļåбļé Ļîñķ one two]"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "[Šéåŕçĥ éñĝîñéš måý ûšé ᐅ`href`ᐊ åţţŕîбûţéš öñ ļîñķš ţö çŕåŵļ ŵéбšîţéš. Éñšûŕé ţĥåţ ţĥé ᐅ`href`ᐊ åţţŕîбûţé öƒ åñçĥöŕ éļéméñţš ļîñķš ţö åñ åþþŕöþŕîåţé ðéšţîñåţîöñ, šö möŕé þåĝéš öƒ ţĥé šîţé çåñ бé ðîšçövéŕéð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé ļîñķš çŕåŵļåбļéᐅ](https://support.google.com/webmasters/answer/9112205)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty]"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "[Ļîñķš åŕé ñöţ çŕåŵļåбļé one two three four five]"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "[Ļîñķš åŕé çŕåŵļåбļé one two three]"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "[<PERSON><PERSON><PERSON>'<PERSON> îļļéĝîбļé ţéxţ one two three]"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "[<PERSON><PERSON><PERSON><PERSON> one two]"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "[% oƒ Þåĝé Ţéxţ one two three]"}, "core/audits/seo/font-size.js | columnSelector": {"message": "[Šéļéçţöŕ one]"}, "core/audits/seo/font-size.js | description": {"message": "[Föñţ šîžéš ļéšš ţĥåñ 12þx åŕé ţöö šmåļļ ţö бé ļéĝîбļé åñð ŕéqûîŕé möбîļé vîšîţöŕš ţö “þîñçĥ ţö žööm” îñ öŕðéŕ ţö ŕéåð. Šţŕîvé ţö ĥåvé >60% oƒ þåĝé ţéxţ ≥12þx. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ļéĝîбļé ƒöñţ šîžéšᐅ](https://developer.chrome.com/docs/lighthouse/seo/font-size/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/audits/seo/font-size.js | displayValue": {"message": "[ᐅ{decimalProportion, number, extendedPercent}ᐊ ļéĝîбļé ţéxţ one two three four]"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "[Ţéxţ îš îļļéĝîбļé бéçåûšé ţĥéŕé'š ñö vîéŵþöŕţ méţå ţåĝ öþţîmîžéð ƒöŕ möбîļé šçŕééñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/audits/seo/font-size.js | failureTitle": {"message": "[Ðöçûméñţ ðöéšñ'ţ ûšé ļéĝîбļé ƒöñţ šîžéš one two three four five six seven eight]"}, "core/audits/seo/font-size.js | legibleText": {"message": "[Ļéĝîбļé ţéxţ one two]"}, "core/audits/seo/font-size.js | title": {"message": "[Ðöçûméñţ ûšéš ļéĝîбļé ƒöñţ šîžéš one two three four five six seven]"}, "core/audits/seo/hreflang.js | description": {"message": "[ĥŕéƒļåñĝ ļîñķš ţéļļ šéåŕçĥ éñĝîñéš ŵĥåţ véŕšîöñ öƒ å þåĝé ţĥéý šĥöûļð ļîšţ îñ šéåŕçĥ ŕéšûļţš ƒöŕ å ĝîvéñ ļåñĝûåĝé öŕ ŕéĝîöñ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ᐅ`hreflang`ᐊᐅ](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "[Ðöçûméñţ ðöéšñ'ţ ĥåvé å våļîð ᐅ`hreflang`ᐊ one two three four five six seven]"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "[Ŕéļåţîvé ĥŕéƒ våļ<PERSON><PERSON> one two three]"}, "core/audits/seo/hreflang.js | title": {"message": "[Ðöçûméñţ ĥåš å våļîð ᐅ`hreflang`ᐊ one two three four five]"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "[Ûñéxþéçţéð ļåñĝûåĝé çöðé one two three]"}, "core/audits/seo/http-status-code.js | description": {"message": "[Þåĝéš ŵîţĥ ûñšûççéššƒûļ ĤŢŢÞ šţåţûš çöðéš måý ñöţ бé îñðéxéð þŕöþéŕļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ĤŢŢÞ šţåţûš çöðéšᐅ](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "[Þåĝé ĥåš ûñšûççéššƒûļ ĤŢŢÞ šţåţûš çöðé one two three four five six seven eight]"}, "core/audits/seo/http-status-code.js | title": {"message": "[Þåĝé ĥåš šûççéššƒûļ ĤŢŢÞ šţåţûš çöðé one two three four five six seven eight]"}, "core/audits/seo/is-crawlable.js | description": {"message": "[Šéåŕçĥ éñĝîñéš åŕé ûñåбļé ţö îñçļûðé ýöûŕ þåĝéš îñ šéåŕçĥ ŕéšûļţš îƒ ţĥéý ðöñ'ţ ĥåvé þéŕmîššîöñ ţö çŕåŵļ ţĥém. ᐅ[ᐊĻéåŕñ möŕé åбöûţ çŕåŵļéŕ ðîŕéçţîvéšᐅ](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "[Þåĝé îš бļöçķéð ƒŕöm îñðéxîñĝ one two three four five six seven]"}, "core/audits/seo/is-crawlable.js | title": {"message": "[Þåĝé îšñ’ţ бļöçķéð ƒŕöm îñðéxîñĝ one two three four five six seven]"}, "core/audits/seo/link-text.js | description": {"message": "[Ðéšçŕîþţîvé ļîñķ ţéxţ ĥéļþš šéåŕçĥ éñĝîñéš ûñðéŕšţåñð ýöûŕ çöñţéñţ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé ļîñķš möŕé åççéššîбļéᐅ](https://developer.chrome.com/docs/lighthouse/seo/link-text/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{[1 ļîñķ ƒöûñð one two]}other{[# ļîñķš ƒöûñð one two]}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "[Ļîñķš ðö ñöţ ĥåvé ðéšçŕîþţîvé ţéxţ one two three four five six seven]"}, "core/audits/seo/link-text.js | title": {"message": "[Ļîñķš ĥåvé ðéšçŕîþţîvé ţéxţ one two three four five six]"}, "core/audits/seo/manual/structured-data.js | description": {"message": "[Ŕûñ ţĥé ᐅ[ᐊŠţŕûçţûŕéð Ðåţå Ţéšţîñĝ Ţööļᐅ](https://search.google.com/structured-data/testing-tool/)ᐊ åñð ţĥé ᐅ[ᐊŠţŕûçţûŕéð Ðåţå Ļîñţéŕᐅ](http://linter.structured-data.org/)ᐊ ţö våļîðåţé šţŕûçţûŕéð ðåţå. ᐅ[ᐊĻéåŕñ möŕé åбöûţ Šţŕûçţûŕéð Ðåţåᐅ](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/seo/manual/structured-data.js | title": {"message": "[Šţŕûçţûŕéð <PERSON><PERSON><PERSON><PERSON> îš våļîð one two three four five]"}, "core/audits/seo/meta-description.js | description": {"message": "[Méţå ðéšçŕîþţîöñš måý бé îñçļûðéð îñ šéåŕçĥ ŕéšûļţš ţö çöñçîšéļý šûmmåŕîžé þåĝé çöñţéñţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé méţå ðéšçŕîþţîöñᐅ](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/seo/meta-description.js | explanation": {"message": "[Ðéšçŕîþţîöñ ţéxţ îš <PERSON>. one two three four five six]"}, "core/audits/seo/meta-description.js | failureTitle": {"message": "[Ðöçûméñţ <PERSON><PERSON><PERSON><PERSON>ö<PERSON> ĥåvé å méţå ðéšçŕîþţîöñ one two three four five six seven eight nine]"}, "core/audits/seo/meta-description.js | title": {"message": "[Ðöçûméñţ ĥåš å méţå ðéšçŕîþţîöñ one two three four five six seven]"}, "core/audits/seo/plugins.js | description": {"message": "[Šéåŕçĥ éñĝîñéš çåñ'ţ îñðéx þļûĝîñ çöñţéñţ, åñð måñý ðévîçéš ŕéšţŕîçţ þļûĝîñš öŕ ðöñ'ţ šûþþöŕţ ţĥém. ᐅ[ᐊĻéåŕñ möŕé åбöûţ åvöîðîñĝ þļûĝîñšᐅ](https://developer.chrome.com/docs/lighthouse/seo/plugins/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/seo/plugins.js | failureTitle": {"message": "[Ðöçûméñţ û<PERSON><PERSON>š þļûĝîñš one two three]"}, "core/audits/seo/plugins.js | title": {"message": "[Ðöçûméñţ åv<PERSON><PERSON>ðš þļûĝîñš one two three]"}, "core/audits/seo/robots-txt.js | description": {"message": "[Îƒ ýöûŕ ŕöбöţš.ţxţ ƒîļé îš måļƒöŕméð, çŕåŵļéŕš måý ñöţ бé åбļé ţö ûñðéŕšţåñð ĥöŵ ýöû ŵåñţ ýöûŕ ŵéбšîţé ţö бé çŕåŵļéð öŕ îñðéxéð. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ŕöбöţš.ţxţᐅ](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "[Ŕéqûéšţ ƒöŕ ŕöбöţš.ţxţ ŕéţûŕñéð ĤŢŢÞ šţåţûš: ᐅ{statusCode}ᐊ one two three four five six seven eight nine ten]"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{[1 éŕŕöŕ ƒöûñð one two]}other{[# éŕŕöŕš ƒöûñð one two]}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ðöŵñļöåð å ŕöбöţš.ţxţ ƒîļé one two three four five six seven eight nine ten eleven]"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "[ŕöбöţš.ţx<PERSON> îš <PERSON> våļîð one two three four five]"}, "core/audits/seo/robots-txt.js | title": {"message": "[ŕöбöţš.ţx<PERSON> îš våļîð one two three]"}, "core/audits/seo/tap-targets.js | description": {"message": "[Îñţéŕåçţîvé éļéméñţš ļîķé бûţţöñš åñð ļîñķš šĥöûļð бé ļåŕĝé éñöûĝĥ (48x48þx), öŕ ĥåvé éñöûĝĥ šþåçé åŕöûñð ţĥém, ţö бé éåšý éñöûĝĥ ţö ţåþ ŵîţĥöûţ övéŕļåþþîñĝ öñţö öţĥéŕ éļéméñţš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţåþ ţåŕĝéţšᐅ](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "[ᐅ{decimalProportion, number, percent}ᐊ åþþŕöþŕîåţéļý šîžéð ţåþ ţåŕĝéţš one two three four five six seven eight]"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "[Ţåþ ţåŕĝéţš åŕé ţöö šmåļļ бéçåûšé ţĥéŕé'š ñö vîéŵþöŕţ méţå ţåĝ öþţîmîžéð ƒöŕ möбîļé šçŕééñš one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "[Ţåþ ţåŕĝéţš åŕé ñöţ šîžéð åþþŕöþŕîåţéļý one two three four five six seven eight]"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "[Övéŕļåþþîñĝ Ţåŕĝéţ one two three]"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "[<PERSON><PERSON><PERSON> Ţåŕĝéţ one two]"}, "core/audits/seo/tap-targets.js | title": {"message": "[Ţåþ ţåŕĝéţš åŕé šîžéð åþþŕöþŕîåţéļý one two three four five six seven eight]"}, "core/audits/server-response-time.js | description": {"message": "[Ķééþ ţĥé šéŕvéŕ ŕéšþöñšé ţîmé ƒöŕ ţĥé måîñ ðöçûméñţ šĥöŕţ бéçåûšé åļļ öţĥéŕ ŕéqûéšţš ðéþéñð öñ îţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Ţîmé ţö Fîŕšţ Бýţé méţŕîçᐅ](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/server-response-time.js | displayValue": {"message": "[Ŕööţ ðöçûméñţ ţööķ ᐅ{timeInMs, number, milliseconds}ᐊ mš one two three four five six]"}, "core/audits/server-response-time.js | failureTitle": {"message": "[Ŕéðûçé îñîţîåļ šéŕvéŕ ŕéšþöñšé ţîmé one two three four five six seven eight]"}, "core/audits/server-response-time.js | title": {"message": "[Îñîţîåļ šéŕvéŕ ŕéšþöñšé ţîmé ŵåš šĥöŕţ one two three four five six seven eight]"}, "core/audits/splash-screen.js | description": {"message": "[Å ţĥéméð šþļåšĥ šçŕééñ éñšûŕéš å ĥîĝĥ-qûåļîţý éxþéŕîéñçé ŵĥéñ ûšéŕš ļåûñçĥ ýöûŕ åþþ ƒŕöm ţĥéîŕ ĥöméšçŕééñš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ šþļåšĥ šçŕééñšᐅ](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/audits/splash-screen.js | failureTitle": {"message": "[Îš ñöţ çöñƒîĝûŕéð ƒöŕ å çûšţöm šþļåšĥ šçŕééñ one two three four five six seven eight nine]"}, "core/audits/splash-screen.js | title": {"message": "[Çöñƒîĝûŕéð ƒöŕ å çûšţöm šþļåšĥ šçŕééñ one two three four five six seven eight]"}, "core/audits/themed-omnibox.js | description": {"message": "[Ţĥé бŕöŵšéŕ åððŕéšš бåŕ çåñ бé ţĥéméð ţö måţçĥ ýöûŕ šîţé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥémîñĝ ţĥé åððŕéšš бåŕᐅ](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "[Ðöéš ñöţ šéţ å ţĥémé çöļöŕ ƒöŕ ţĥé åððŕéšš бåŕ. one two three four five six seven eight nine ten]"}, "core/audits/themed-omnibox.js | title": {"message": "[<PERSON><PERSON><PERSON><PERSON> å ţĥémé çöļöŕ ƒöŕ ţĥé åððŕéšš бåŕ. one two three four five six seven eight]"}, "core/audits/third-party-cookies.js | description": {"message": "[Šûþþöŕţ ƒöŕ ţĥîŕð-þåŕţý çööķîéš ŵîļļ бé ŕémövéð îñ å ƒûţûŕé véŕšîöñ öƒ Çĥŕömé. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þĥåšîñĝ öûţ ţĥîŕð-þåŕţý çööķîéšᐅ](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{[1 çööķîé ƒöûñð one two]}other{[# çööķîéš ƒöûñð one two]}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "[Ûšéš ţĥîŕð-þåŕţý çööķîéš one two three]"}, "core/audits/third-party-cookies.js | title": {"message": "[Åv<PERSON><PERSON><PERSON><PERSON> ţĥîŕð-þåŕţý çööķîéš one two three]"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "[ᐅ{productName}ᐊ (Çûšţöméŕ Šûççéšš) one two three four five]"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "[ᐅ{productName}ᐊ (Måŕķéţîñĝ) one two three four]"}, "core/audits/third-party-facades.js | categorySocial": {"message": "[ᐅ{productName}ᐊ (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) one two three]"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "[ᐅ{productName}ᐊ (Vîðéö) one two three]"}, "core/audits/third-party-facades.js | columnProduct": {"message": "[Þŕöðûçţ one]"}, "core/audits/third-party-facades.js | description": {"message": "[Šömé ţĥîŕð-þåŕţý émб<PERSON>ðš çåñ бé ļåžý ļöåðéð. Çöñšîðéŕ ŕéþļåçîñĝ ţĥém ŵîţĥ å ƒåçåðé ûñţîļ ţĥéý åŕé ŕéqûîŕéð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö ðéƒéŕ ţĥîŕð-þåŕţîéš ŵîţĥ å ƒåçåðéᐅ](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{[# ƒåçåðé åļţéŕñåţîvé åvåîļåбļé one two three four five six seven]}other{[# ƒåçåðé åļţéŕñåţîvéš åvåîļåбļé one two three four five six seven]}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "[Šömé ţĥîŕð-þåŕţý ŕéšöûŕçéš çåñ бé ļåžý ļöåðéð ŵîţĥ å ƒåçåðé one two three four five six seven eight nine ten eleven twelve]"}, "core/audits/third-party-facades.js | title": {"message": "[Ļåžý ļöåð ţĥîŕð-þåŕţý ŕéšöûŕçéš ŵîţĥ ƒåçåðéš one two three four five six seven eight nine]"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "[Ţĥîŕð-Þåŕţý one two]"}, "core/audits/third-party-summary.js | description": {"message": "[Ţĥîŕð-þåŕţý çöðé çåñ šîĝñîƒîçåñţļý îmþåçţ ļöåð þéŕƒöŕmåñçé. Ļîmîţ ţĥé ñûmбéŕ öƒ ŕéðûñðåñţ ţĥîŕð-þåŕţý þŕövîðéŕš åñð ţŕý ţö ļöåð ţĥîŕð-þåŕţý çöðé åƒţéŕ ýöûŕ þåĝé ĥåš þŕîmåŕîļý ƒîñîšĥéð ļöåðîñĝ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö mîñîmîžé ţĥîŕð-þåŕţý îmþåçţᐅ](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone]"}, "core/audits/third-party-summary.js | displayValue": {"message": "[Ţĥîŕð-þåŕţý çöðé бļöçķéð ţĥé måîñ ţĥŕéåð ƒöŕ ᐅ{timeInMs, number, milliseconds}ᐊ mš one two three four five six seven eight nine ten eleven]"}, "core/audits/third-party-summary.js | failureTitle": {"message": "[Ŕéðûçé ţĥé îmþåçţ öƒ ţĥîŕð-þåŕţý çöðé one two three four five six seven eight]"}, "core/audits/third-party-summary.js | title": {"message": "[Mîñîm<PERSON><PERSON><PERSON> ţĥîŕð-þåŕţý ûšåĝé one two three]"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "[Méåšûŕéméñţ one two]"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "[Méţŕîç one]"}, "core/audits/timing-budget.js | description": {"message": "[Šéţ å ţîmîñĝ бûðĝéţ ţö ĥéļþ ýöû ķééþ åñ éýé öñ ţĥé þéŕƒöŕmåñçé öƒ ýöûŕ šîţé. Þéŕƒöŕmåñţ šîţéš ļöåð ƒåšţ åñð ŕéšþöñð ţö ûšéŕ îñþûţ évéñţš qûîçķļý. ᐅ[ᐊĻéåŕñ möŕé åбöûţ þéŕƒöŕmåñçé бûðĝéţšᐅ](https://developers.google.com/web/tools/lighthouse/audits/budgets)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/audits/timing-budget.js | title": {"message": "[Ţîmîñĝ бûðĝéţ one two]"}, "core/audits/unsized-images.js | description": {"message": "[Šéţ åñ éxþļîçîţ ŵîðţĥ åñð ĥéîĝĥţ öñ îmåĝé éļéméñţš ţö ŕéðûçé ļåýöûţ šĥîƒţš åñð îmþŕövé ÇĻŠ. ᐅ[ᐊĻéåŕñ ĥöŵ ţö šéţ îmåĝé ðîméñšîöñšᐅ](https://web.dev/articles/optimize-cls#images_without_dimensions)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/unsized-images.js | failureTitle": {"message": "[Îmåĝé éļéméñţš ðö ñöţ ĥåvé éxþļîçîţ ᐅ`width`ᐊ åñð ᐅ`height`ᐊ one two three four five six seven eight nine ten]"}, "core/audits/unsized-images.js | title": {"message": "[Îmåĝé éļéméñţš ĥåvé éxþļîçîţ ᐅ`width`ᐊ åñð ᐅ`height`ᐊ one two three four five six seven eight]"}, "core/audits/user-timings.js | columnType": {"message": "[Ţ<PERSON>þ<PERSON> one]"}, "core/audits/user-timings.js | description": {"message": "[Çöñšîðéŕ îñšţŕûméñţîñĝ ýöûŕ åþþ ŵîţĥ ţĥé Ûšéŕ Ţîmîñĝ ÅÞÎ ţö méåšûŕé ýöûŕ åþþ'š ŕéåļ-ŵöŕļð þéŕƒöŕmåñçé ðûŕîñĝ ķéý ûšéŕ éxþéŕîéñçéš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ Ûšéŕ Ţîmîñĝ måŕķšᐅ](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{[1 ûšéŕ ţîmîñĝ one two]}other{[# ûšéŕ ţîmîñĝš one two]}}"}, "core/audits/user-timings.js | title": {"message": "[Ûšéŕ Ţîmîñĝ måŕķš åñð méåšûŕéš one two three four five six seven]"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "[Å ᐅ`<link rel=preconnect>`ᐊ ŵåš ƒöûñð ƒöŕ \"ᐅ{securityOrigin}ᐊ\" бûţ ŵåš ñöţ ûšéð бý ţĥé бŕöŵšéŕ. Çĥéçķ ţĥåţ ýöû åŕé ûšîñĝ ţĥé ᐅ`crossorigin`ᐊ åţţŕîбûţé þŕöþéŕļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/uses-rel-preconnect.js | description": {"message": "[Çöñšîðéŕ åððîñĝ ᐅ`preconnect`ᐊ öŕ ᐅ`dns-prefetch`ᐊ ŕéšöûŕçé ĥîñţš ţö éšţåбļîšĥ éåŕļý çöññéçţîöñš ţö îmþöŕţåñţ ţĥîŕð-þåŕţý öŕîĝîñš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö þŕéçöññéçţ ţö ŕéqûîŕéð öŕîĝîñšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/uses-rel-preconnect.js | title": {"message": "[Þŕéçöññéçţ ţö ŕéqûîŕéð öŕîĝîñš one two three four five six seven]"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "[Möŕé ţĥåñ 2 ᐅ`<link rel=preconnect>`ᐊ çöññéçţîöñš ŵéŕé ƒöûñð. Ţĥéšé šĥöûļð бé ûšéð šþåŕîñĝļý åñð öñļý ţö ţĥé möšţ îmþöŕţåñţ öŕîĝîñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "[Å ᐅ`<link rel=preconnect>`ᐊ ŵåš ƒöûñð ƒöŕ \"ᐅ{securityOrigin}ᐊ\" бûţ ŵåš ñöţ ûšéð бý ţĥé бŕöŵšéŕ. Öñļý ûšé ᐅ`preconnect`ᐊ ƒöŕ îmþöŕţåñţ öŕîĝîñš ţĥåţ ţĥé þåĝé ŵîļļ çéŕţåîñļý ŕéqûéšţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "[Å þŕéļöåð ᐅ`<link>`ᐊ ŵåš ƒöûñð ƒöŕ \"ᐅ{preloadURL}ᐊ\" бûţ ŵåš ñöţ ûšéð бý ţĥé бŕöŵšéŕ. Çĥéçķ ţĥåţ ýöû åŕé ûšîñĝ ţĥé ᐅ`crossorigin`ᐊ åţţŕîбûţé þŕöþéŕļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/audits/uses-rel-preload.js | description": {"message": "[Çöñšîðéŕ ûšîñĝ ᐅ`<link rel=preload>`ᐊ ţö þŕîöŕîţîžé ƒéţçĥîñĝ ŕéšöûŕçéš ţĥåţ åŕé çûŕŕéñţļý ŕéqûéšţéð ļåţéŕ îñ þåĝé ļöåð. ᐅ[ᐊĻéåŕñ ĥöŵ ţö þŕéļöåð ķéý ŕéqûéšţšᐅ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/audits/uses-rel-preload.js | title": {"message": "[Þŕéļöåð ķéý ŕéqûéšţš one two three]"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "[Måþ ÛŔĻ one]"}, "core/audits/valid-source-maps.js | description": {"message": "[Šöûŕçé måþš ţŕåñšļåţé mîñîƒîéð çöðé ţö ţĥé öŕîĝîñåļ šöûŕçé çöðé. Ţĥîš ĥéļþš ðévéļöþéŕš ðéбûĝ îñ þŕöðûçţîöñ. Îñ åððîţîöñ, Ļîĝĥţĥöûšé îš åбļé ţö þŕövîðé ƒûŕţĥéŕ îñšîĝĥţš. Çöñšîðéŕ ðéþļöýîñĝ šöûŕçé måþš ţö ţåķé åðvåñţåĝé öƒ ţĥéšé бéñéƒîţš. ᐅ[ᐊĻéåŕñ möŕé åбöûţ šöûŕçé måþšᐅ](https://developer.chrome.com/docs/devtools/javascript/source-maps/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour]"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "[Mîššîñĝ šöûŕçé måþš ƒöŕ ļåŕĝé ƒîŕšţ-þåŕţý ĴåvåŠçŕîþţ one two three four five six seven eight nine ten eleven]"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "[Ļåŕĝé ĴåvåŠçŕîþţ ƒîļé îš mîššîñĝ å šöûŕçé måþ one two three four five six seven eight nine]"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{[Ŵåŕñîñĝ: mîššîñĝ 1 îţém îñ ᐅ`.sourcesContent`ᐊ one two three four five six seven]}other{[Ŵåŕñîñĝ: mîššîñĝ # îţémš îñ ᐅ`.sourcesContent`ᐊ one two three four five six seven]}}"}, "core/audits/valid-source-maps.js | title": {"message": "[Þåĝé ĥåš våļîð šöûŕçé måþš one two three four five six]"}, "core/audits/viewport.js | description": {"message": "[Å ᐅ`<meta name=\"viewport\">`ᐊ ñöţ öñļý öþţîmîžéš ýöûŕ åþþ ƒöŕ möбîļé šçŕééñ šîžéš, бûţ åļšö þŕévéñţš ᐅ[ᐊå 300 mîļļîšéçöñð ðéļåý ţö ûšéŕ îñþûţᐅ](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)ᐊ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ûšîñĝ ţĥé vîéŵþöŕţ méţå ţåĝᐅ](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/audits/viewport.js | explanationNoTag": {"message": "[Ñö ᐅ`<meta name=\"viewport\">`ᐊ ţåĝ ƒöûñð one two three four]"}, "core/audits/viewport.js | failureTitle": {"message": "[Ðöéš ñöţ ĥåvé å ᐅ`<meta name=\"viewport\">`ᐊ ţåĝ ŵîţĥ ᐅ`width`ᐊ öŕ ᐅ`initial-scale`ᐊ one two three four five six seven eight]"}, "core/audits/viewport.js | title": {"message": "[Ĥåš å ᐅ`<meta name=\"viewport\">`ᐊ ţåĝ ŵîţĥ ᐅ`width`ᐊ öŕ ᐅ`initial-scale`ᐊ one two three four five six seven]"}, "core/audits/work-during-interaction.js | description": {"message": "[Ţĥîš îš ţĥé ţĥŕéåð-бļöçķîñĝ ŵöŕķ öççûŕŕîñĝ ðûŕîñĝ ţĥé Îñţéŕåçţîöñ ţö Ñéxţ Þåîñţ méåšûŕéméñţ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ ţĥé Îñţéŕåçţîöñ ţö Ñéxţ Þåîñţ méţŕîçᐅ](https://web.dev/articles/inp)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/audits/work-during-interaction.js | displayValue": {"message": "[ᐅ{timeInMs, number, milliseconds}ᐊ mš šþéñţ öñ évéñţ 'ᐅ{interactionType}ᐊ' one two three four five six]"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "[Évé<PERSON><PERSON> ţåŕĝéţ one two]"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "[Mîñîmîžé ŵöŕķ ðûŕîñĝ ķéý îñţéŕåçţîöñ one two three four five six seven eight]"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "[Îñ<PERSON><PERSON><PERSON> ð<PERSON> one two]"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "[Þŕéšéñţåţîöñ <PERSON><PERSON><PERSON><PERSON><PERSON> one two three]"}, "core/audits/work-during-interaction.js | processingTime": {"message": "[Þŕöçéššîñĝ ţîmé one two]"}, "core/audits/work-during-interaction.js | title": {"message": "[Mîñîmîž<PERSON>š ŵöŕķ ðûŕîñĝ ķéý îñţéŕåçţîöñ one two three four five six seven eight]"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé ûšåĝé öƒ ÅŔÎÅ îñ ýöûŕ åþþļîçåţîöñ ŵĥîçĥ måý éñĥåñçé ţĥé éxþéŕîéñçé ƒöŕ ûšéŕš öƒ åššîšţîvé ţéçĥñöļöĝý, ļîķé å šçŕééñ ŕéåðéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "[ÅŔÎÅ one]"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö þŕövîðé åļţéŕñåţîvé çöñţéñţ ƒöŕ åûðîö åñð vîðéö. Ţĥîš måý îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ ûšéŕš ŵîţĥ ĥéåŕîñĝ öŕ vîšîöñ îmþåîŕméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "[Åûð<PERSON>ö åñð vîðéö one two]"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "[Ţĥéšé îţémš ĥîĝĥļîĝĥţ çömmöñ åççéššîбîļîţý бéšţ þŕåçţîçéš. one two three four five six seven eight nine ten eleven twelve]"}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "[Б<PERSON><PERSON><PERSON> þŕåçţîçéš one two]"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "[Ţĥéšé çĥéçķš ĥîĝĥļîĝĥţ öþþöŕţûñîţîéš ţö ᐅ[ᐊîmþŕövé ţĥé åççéššîбîļîţý öƒ ýöûŕ ŵéб åþþᐅ](https://developer.chrome.com/docs/lighthouse/accessibility/)ᐊ. Åûţömåţîç ðéţéçţîöñ çåñ öñļý ðéţéçţ å šûбšéţ öƒ îššûéš åñð ðöéš ñöţ ĝûåŕåñţéé ţĥé åççéššîбîļîţý öƒ ýöûŕ ŵéб åþþ, šö ᐅ[ᐊmåñûåļ ţéšţîñĝᐅ](https://web.dev/articles/how-to-review)ᐊ îš åļšö éñçöûŕåĝéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone]"}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "[Ţĥéšé îţémš åððŕéšš åŕéåš ŵĥîçĥ åñ åûţömåţéð ţéšţîñĝ ţööļ çåññöţ çövéŕ. Ļéåŕñ möŕé îñ öûŕ ĝûîðé öñ ᐅ[ᐊçöñðûçţîñĝ åñ åççéššîбîļîţý ŕévîéŵᐅ](https://web.dev/articles/how-to-review)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/config/default-config.js | a11yCategoryTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>бîļîţý one two]"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé ļéĝîбîļîţý öƒ ýöûŕ çöñţéñţ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "[Çöñţŕåšţ one]"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé îñţéŕþŕéţåţîöñ öƒ ýöûŕ çöñţéñţ бý ûšéŕš îñ ðîƒƒéŕéñţ ļöçåļéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "[Îñţéŕñåţîöñåļîžåţîöñ åñð ļöçåļîžåţîöñ one two three four]"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé šémåñţîçš öƒ ţĥé çöñţŕöļš îñ ýöûŕ åþþļîçåţîöñ. Ţĥîš måý éñĥåñçé ţĥé éxþéŕîéñçé ƒöŕ ûšéŕš öƒ åššîšţîvé ţéçĥñöļöĝý, ļîķé å šçŕééñ ŕéåðéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON> one two]"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ķéýбöåŕð ñåvîĝåţîöñ îñ ýöûŕ åþþļîçåţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "[Ñåvîĝåţîöñ one two]"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "[Ţĥéšé åŕé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé éxþéŕîéñçé öƒ ŕéåðîñĝ ţåбûļåŕ öŕ ļîšţ ðåţå ûšîñĝ åššîšţîvé ţéçĥñöļöĝý, ļîķé å šçŕééñ ŕéåðéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> å<PERSON>ð <PERSON> one two]"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "[Бŕöŵšéŕ Çömþåţîбîļîţý one two three]"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "[Б<PERSON><PERSON><PERSON> Þŕåçţîçéš one two]"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "[Ĝéñéŕåļ one]"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "[Ţŕûšţ <PERSON><PERSON><PERSON> one two]"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "[Ûšéŕ Éxþéŕîéñçé one two]"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "[Þéŕƒöŕmåñçé бûðĝéţš šéţ šţåñðåŕðš ƒöŕ ţĥé þéŕƒöŕmåñçé öƒ ýöûŕ šîţé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/config/default-config.js | budgetsGroupTitle": {"message": "[Бûðĝéţš one]"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "[Möŕé îñƒöŕmåţîöñ åбöûţ ţĥé þéŕƒöŕmåñçé öƒ ýöûŕ åþþļîçåţîöñ. Ţĥéšé ñûmбéŕš ðöñ'ţ ᐅ[ᐊðîŕéçţļý åƒƒéçţᐅ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)ᐊ ţĥé Þéŕƒöŕmåñçé šçöŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "[Ðîåĝñöšţîçš one two]"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "[Ţĥé möšţ çŕîţîçåļ åšþéçţ öƒ þéŕƒöŕmåñçé îš ĥöŵ qûîçķļý þîxéļš åŕé ŕéñðéŕéð öñšçŕééñ. Ķéý méţŕîçš: Fîŕšţ Çöñţéñţƒûļ Þåîñţ, Fîŕšţ Méåñîñĝƒûļ Þåîñţ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "[Fîŕšţ Þåîñţ Îmþŕövéméñţš one two three]"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "[Ţĥéšé šûĝĝéšţîöñš çåñ ĥéļþ ýöûŕ þåĝé ļöåð ƒåšţéŕ. Ţĥéý ðöñ'ţ ᐅ[ᐊðîŕéçţļý åƒƒéçţᐅ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)ᐊ ţĥé Þéŕƒöŕmåñçé šçöŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "[Öþþöŕţûñîţîéš one two]"}, "core/config/default-config.js | metricGroupTitle": {"message": "[Méţŕîçš one]"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "[Éñĥåñçé ţĥé övéŕåļļ ļöåðîñĝ éxþéŕîéñçé, šö ţĥé þåĝé îš ŕéšþöñšîvé åñð ŕéåðý ţö ûšé åš šööñ åš þöššîбļé. Ķéý méţŕîçš: Ţîmé ţö Îñţéŕåçţîvé, Šþééð Îñðéx one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "[Övéŕåļļ Îmþŕövéméñţš one two three]"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "[Þéŕƒöŕmåñçé one two]"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "[Ţĥéšé çĥéçķš våļîðåţé ţĥé åšþéçţš öƒ å Þŕöĝŕéššîvé Ŵéб Åþþ. ᐅ[ᐊĻéåŕñ ŵĥåţ måķéš å ĝööð Þŕöĝŕéššîvé Ŵéб Åþþᐅ](https://web.dev/articles/pwa-checklist)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "[Ţĥéšé çĥéçķš åŕé ŕéqûîŕéð бý ţĥé бåšéļîñé ᐅ[ᐊÞŴÅ Çĥéçķļîšţᐅ](https://web.dev/articles/pwa-checklist)ᐊ бûţ åŕé ñöţ åûţömåţîçåļļý çĥéçķéð бý Ļîĝĥţĥöûšé. Ţĥéý ðö ñöţ åƒƒéçţ ýöûŕ šçöŕé бûţ îţ'š îmþöŕţåñţ ţĥåţ ýöû véŕîƒý ţĥém måñûåļļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/config/default-config.js | pwaCategoryTitle": {"message": "[ÞŴÅ one]"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "[ÞŴÅ Öþ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ð one two]"}, "core/config/default-config.js | seoCategoryDescription": {"message": "[Ţĥéšé çĥéçķš éñšûŕé ţĥåţ ýöûŕ þåĝé îš ƒöļļöŵîñĝ бåšîç šéåŕçĥ éñĝîñé öþţîmîžåţîöñ åðvîçé. Ţĥéŕé åŕé måñý åððîţîöñåļ ƒåçţöŕš Ļîĝĥţĥöûšé ðöéš ñöţ šçöŕé ĥéŕé ţĥåţ måý åƒƒéçţ ýöûŕ šéåŕçĥ ŕåñķîñĝ, îñçļûðîñĝ þéŕƒöŕmåñçé öñ ᐅ[ᐊÇöŕé Ŵéб Vîţåļšᐅ](https://web.dev/explore/vitals)ᐊ. ᐅ[ᐊĻéåŕñ möŕé åбöûţ Ĝööĝļé Šéåŕçĥ Éššéñţîåļšᐅ](https://support.google.com/webmasters/answer/35769)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive]"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "[Ŕûñ ţĥéšé åððîţîöñåļ våļîðåţöŕš öñ ýöûŕ šîţé ţö çĥéçķ åððîţîöñåļ ŠÉÖ бéšţ þŕåçţîçéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/config/default-config.js | seoCategoryTitle": {"message": "[ŠÉÖ one]"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "[Föŕmåţ ýöûŕ ĤŢMĻ îñ å ŵåý ţĥåţ éñåбļéš çŕåŵļéŕš ţö бéţţéŕ ûñðéŕšţåñð ýöûŕ åþþ’š çöñţéñţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/config/default-config.js | seoContentGroupTitle": {"message": "[Çö<PERSON><PERSON><PERSON><PERSON><PERSON> Þŕåçţîçéš one two three]"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "[Ţö åþþéåŕ îñ šéåŕçĥ ŕéšûļţš, çŕåŵļéŕš ñééð åççéšš ţö ýöûŕ åþþ. one two three four five six seven eight nine ten eleven twelve]"}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "[Çŕåŵļîñĝ åñð Îñðéxîñĝ one two three]"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "[Måķé šûŕé ýöûŕ þåĝéš åŕé möбîļé ƒŕîéñðļý šö ûšéŕš ðöñ’ţ ĥåvé ţö þîñçĥ öŕ žööm îñ öŕðéŕ ţö ŕéåð ţĥé çöñţéñţ þåĝéš. ᐅ[ᐊĻéåŕñ ĥöŵ ţö måķé þåĝéš möбîļé-ƒŕîéñðļýᐅ](https://developers.google.com/search/mobile-sites/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Fŕîéñðļý one two]"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "[Ţĥé ţéšţéð ðévîçé åþþéåŕš ţö ĥåvé å šļöŵéŕ ÇÞÛ ţĥåñ  Ļîĝĥţĥöûšé éxþéçţš. Ţĥîš çåñ ñéĝåţîvéļý åƒƒéçţ ýöûŕ þéŕƒöŕmåñçé šçöŕé. Ļéåŕñ möŕé åбöûţ ᐅ[ᐊçåļîбŕåţîñĝ åñ åþþŕöþŕîåţé ÇÞÛ šļöŵðöŵñ mûļţîþļîéŕᐅ](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "[Ţĥé þåĝé måý ñöţ бé ļöåðîñĝ åš éxþéçţéð бéçåûšé ýöûŕ ţéšţ ÛŔĻ (ᐅ{requested}ᐊ) ŵåš ŕéðîŕéçţéð ţö ᐅ{final}ᐊ. Ţŕý ţéšţîñĝ ţĥé šéçöñð ÛŔĻ ðîŕéçţļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/gather/driver/navigation.js | warningTimeout": {"message": "[Ţĥé þåĝé ļöåðéð ţöö šļöŵļý ţö ƒîñîšĥ ŵîţĥîñ ţĥé ţîmé ļîmîţ. Ŕéšûļţš måý бé îñçömþļéţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "[Çļéåŕîñĝ ţĥé бŕöŵšéŕ çåçĥé ţîméð öûţ. Ţŕý åûðîţîñĝ ţĥîš þåĝé åĝåîñ åñð ƒîļé å бûĝ îƒ ţĥé îššûé þéŕšîšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{[Ţĥéŕé måý бé šţöŕéð ðåţå åƒƒéçţîñĝ ļöåðîñĝ þéŕƒöŕmåñçé îñ ţĥîš ļöçåţîöñ: ᐅ{locations}ᐊ. Åûðîţ ţĥîš þåĝé îñ åñ îñçöĝñîţö ŵîñðöŵ ţö þŕévéñţ ţĥöšé ŕéšöûŕçéš ƒŕöm åƒƒéçţîñĝ ýöûŕ šçöŕéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]}other{[Ţĥéŕé måý бé šţöŕéð ðåţå åƒƒéçţîñĝ ļöåðîñĝ þéŕƒöŕmåñçé îñ ţĥéšé ļöçåţîöñš: ᐅ{locations}ᐊ. Åûðîţ ţĥîš þåĝé îñ åñ îñçöĝñîţö ŵîñðöŵ ţö þŕévéñţ ţĥöšé ŕéšöûŕçéš ƒŕöm åƒƒéçţîñĝ ýöûŕ šçöŕéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "[Çļéåŕîñĝ ţĥé öŕîĝîñ ðåţå ţîméð öûţ. Ţŕý åûðîţîñĝ ţĥîš þåĝé åĝåîñ åñð ƒîļé å бûĝ îƒ ţĥé îššûé þéŕšîšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "[Éŕŕöŕ þåŕšîñĝ ᐅ`link`ᐊ ĥéåðéŕ (ᐅ{error}ᐊ): ᐅ`{header}`ᐊ one two three four five six seven eight]"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "[Å þåĝé ñåvîĝåţîöñ ŵåš ðéţéçţéð ðûŕîñĝ ţĥé ŕûñ. Ûšîñĝ ţîméšþåñ möðé ţö åûðîţ þåĝé ñåvîĝåţîöñš îš ñöţ ŕéçömméñðéð. Ûšé ñåvîĝåţîöñ möðé ţö åûðîţ þåĝé ñåvîĝåţîöñš ƒöŕ бéţţéŕ ţĥîŕð-þåŕţý åţţŕîбûţîöñ åñð måîñ ţĥŕéåð ðéţéçţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "[Öñļý þåĝéš ļöåðéð vîå å ĜÉŢ ŕéqûéšţ åŕé éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "[Öñļý þåĝéš ŵîţĥ å šţåţûš çöðé öƒ 2XX çåñ бé çåçĥéð. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "[Çĥŕömé ðéţéçţéð åñ åţţémþţ ţö éxéçûţé ĴåvåŠçŕîþţ ŵĥîļé îñ ţĥé çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | appBanner": {"message": "[Þåĝéš ţĥåţ ŕéqûéšţéð åñ ÅþþБåññéŕ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö å ķééþåļîvé ŕéqûéšţ. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð бý ƒļåĝš. Vîšîţ çĥŕömé://ƒļåĝš/#бåçķ-ƒöŕŵåŕð-çåçĥé ţö éñåбļé îţ ļöçåļļý öñ ţĥîš ðévîçé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð бý ţĥé çömmåñð ļîñé. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö îñšûƒƒîçîéñţ mémöŕý. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ñöţ šûþþöŕţéð бý ðéļéĝåţé. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ƒöŕ þŕéŕéñðéŕéŕ. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "[Ţĥé þåĝé çåññöţ бé çåçĥéð бéçåûšé îţ ĥåš å БŕöåðçåšţÇĥåññéļ îñšţåñçé ŵîţĥ ŕéĝîšţéŕéð ļîšţéñéŕš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "[Þåĝéš ŵîţĥ çåçĥé-çöñţŕöļ:ñö-šţöŕé ĥéåðéŕ çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "[Ţĥé çåçĥé ŵåš îñţéñţîöñåļļý çļéåŕéð. one two three four five six seven eight]"}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm ţĥé çåçĥé ţö åļļöŵ åñöţĥéŕ þåĝé ţö бé çåçĥéð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "[Þåĝéš çöñţåîñîñĝ þļûĝîñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "[Þåĝéš ţĥåţ ûšé FîļéÇĥööšéŕ ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "[Þåĝéš ţĥåţ ûšé Fîļé Šýšţém Åççéšš ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "[Þåĝéš ţĥåţ ûšé <PERSON> Ðévîçé Ðîšþåţçĥéŕ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "[Å méðîå þļåýéŕ ŵåš þļåýîñĝ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "[Þåĝéš ţĥåţ ûšé MéðîåŠéššîöñ ÅÞÎ åñð šéţ å þļåýбåçķ šţåţé åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "[Þåĝéš ţĥåţ ûšé M<PERSON>ðîåŠéššîöñ ÅÞÎ åñð šéţ åçţîöñ ĥåñðļéŕš åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö šçŕééñ ŕéåðéŕ. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "[Þåĝéš ţĥåţ ûšé ŠéçûŕîţýĤåñðļéŕ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "[Þåĝéš ţĥåţ ûšé Šéŕîåļ ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбÅûţĥéţîçåţîöñ ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбБļûéţööţĥ ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбÛŠБ ÅÞÎ åŕé ñöţ éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð бéçåûšé çööķîéš åŕé ðîšåбļéð öñ å þåĝé ţĥåţ ûšéš ᐅ`Cache-Control: no-store`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "[Þåĝéš ţĥåţ ûšé å ðéðîçåţéð ŵöŕķéŕ öŕ ŵöŕķļéţ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "[Ţĥé ðöçûméñţ ðîð ñöţ ƒîñîšĥ ļöåðîñĝ бéƒöŕé ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "[Åþþ Бåññéŕ ŵåš þŕéšéñţ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine]"}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "[Çĥŕömé Þåššŵöŕð Måñåĝéŕ ŵåš þŕéšéñţ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "[ÐÖM ðîšţîļļåţîöñ ŵåš îñ þŕöĝŕéšš ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "[ÐÖM Ðîšţîļļéŕ Vîéŵéŕ ŵåš þŕéšéñţ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö éxţéñšîöñš ûšîñĝ méššåĝîñĝ ÅÞÎ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "[Éxţéñšîöñš ŵîţĥ ļöñĝ-ļîvéð çöññéçţîöñ šĥöûļð çļöšé ţĥé çöññéçţîöñ бéƒöŕé éñţéŕîñĝ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "[Éxţéñšîöñš ŵîţĥ ļöñĝ-ļîvéð çöññéçţîöñ åţţémþţéð ţö šéñð méššåĝéš ţö ƒŕåméš îñ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö éxţéñšîöñš. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "[Möð<PERSON><PERSON> ðîåļöĝ šûçĥ åš ƒöŕm ŕéšûбmîššîöñ öŕ ĥţţþ þåššŵöŕð ðîåļöĝ ŵåš šĥöŵñ ƒöŕ ţĥé þåĝé ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "[Ţĥé öƒƒļîñé þåĝé ŵåš šĥöŵñ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "[Öûţ-Öƒ-Mémöŕý Îñţéŕvéñţîöñ бåŕ ŵåš þŕéšéñţ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "[Ţĥéŕé ŵéŕé þéŕmîššîöñ ŕéqûéšţš ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "[Þöþûþ бļöçķéŕ ŵåš þŕéšéñţ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "[Šåƒé Бŕöŵšîñĝ ðéţåîļš ŵéŕé šĥöŵñ ûþöñ ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "[Šåƒé Бŕöŵšîñĝ çöñšîðéŕéð ţĥîš þåĝé ţö бé åбûšîvé åñð бļöçķéð þöþûþ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "[Å šéŕvîçé ŵöŕķéŕ ŵåš åçţîvåţéð ŵĥîļé ţĥé þåĝé ŵåš îñ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö å ðöçûméñţ éŕŕöŕ. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "[Þåĝéš ûšîñĝ FéñçéðFŕåméš çåññöţ бé šţöŕéð îñ бƒçåçĥé. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm ţĥé çåçĥé ţö åļļöŵ åñöţĥéŕ þåĝé ţö бé çåçĥéð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "[Þåĝéš ţĥåţ ĥåvé ĝŕåñţéð méðîå šţŕéåm åççéšš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "[Þåĝéš ţĥåţ ûšé þöŕţåļš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | idleManager": {"message": "[Þåĝéš ţĥåţ ûšé ÎðļéMåñåĝéŕ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "[Þåĝéš ţĥåţ ĥåvé åñ öþéñ ÎñðéxéðÐБ çöññéçţîöñ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö åñ ÎñðéxéðÐБ évéñţ. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "[Îñéļîĝîбļé ÅÞÎš ŵéŕé ûšéð. one two three four five six]"}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "[Þåĝéš ţĥåţ ᐅ`JavaScript`ᐊ îš îñĵéçţéð îñţö бý éxţéñšîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "[Þåĝéš ţĥåţ å ᐅ`StyleSheet`ᐊ îš îñĵéçţéð îñţö бý éxţéñšîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | internalError": {"message": "[Îñţéŕñåļ éŕŕöŕ. one two]"}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "[Бåçķ/ƒöŕŵåŕð çåçĥé îš ðîšåбļéð ðûé ţö å ķééþåļîvé ŕéqûéšţ. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "[Þåĝéš ţĥåţ ûšé Ķéýбöåŕð ļöçķ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | loading": {"message": "[Ţĥé þåĝé ðîð ñöţ ƒîñîšĥ ļöåðîñĝ бéƒöŕé ñåvîĝåţîñĝ åŵåý. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "[Þåĝéš ŵĥöšé måîñ ŕéšöûŕçé ĥåš çåçĥé-çöñţŕöļ:ñö-çåçĥé çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "[Þåĝéš ŵĥöšé måîñ ŕéšöûŕçé ĥåš çåçĥé-çöñţŕöļ:ñö-šţöŕé çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "[Ñåvîĝåţîöñ ŵåš çåñçéļļéð бéƒöŕé ţĥé þåĝé çöûļð бé ŕéšţöŕéð ƒŕöm бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm ţĥé çåçĥé бéçåûšé åñ åçţîvé ñéţŵöŕķ çöññéçţîöñ ŕéçéîvéð ţöö mûçĥ ðåţå. Çĥŕömé ļîmîţš ţĥé åmöûñţ öƒ ðåţå ţĥåţ å þåĝé måý ŕéçéîvé ŵĥîļé çåçĥéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "[Þåĝéš ţĥåţ ĥåvé îñƒļîĝĥţ ƒéţçĥ() öŕ XĤŔ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm бåçķ/ƒöŕŵåŕð çåçĥé бéçåûšé åñ åçţîvé ñéţŵöŕķ ŕéqûéšţ îñvöļvéð å ŕéðîŕéçţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm ţĥé çåçĥé бéçåûšé å ñéţŵöŕķ çöññéçţîöñ ŵåš öþéñ ţöö ļöñĝ. Çĥŕömé ļîmîţš ţĥé åmöûñţ öƒ ţîmé ţĥåţ å þåĝé måý ŕéçéîvé ðåţå ŵĥîļé çåçĥéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "[Þåĝéš ţĥåţ ðö ñöţ ĥåvé å våļîð ŕéšþöñšé ĥéåð çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "[Ñåvîĝåţîöñ ĥåþþéñéð îñ å ƒŕåmé öţĥéŕ ţĥåñ ţĥé måîñ ƒŕåmé. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "[Þåĝé ŵîţĥ öñĝöîñĝ îñðéxéð ÐБ ţŕåñšåçţîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "[Þåĝéš ŵîţĥ åñ îñ-ƒļîĝĥţ ñéţŵöŕķ ŕéqûéšţ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "[Þåĝéš ŵîţĥ åñ îñ-ƒļîĝĥţ ƒéţçĥ ñéţŵöŕķ ŕéqûéšţ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "[Þåĝéš ŵîţĥ åñ îñ-ƒļîĝĥţ ñéţŵöŕķ ŕéqûéšţ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "[Þåĝéš ŵîţĥ åñ îñ-ƒļîĝĥţ XĤŔ ñéţŵöŕķ ŕéqûéšţ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "[Þåĝéš ţĥåţ ûšé ÞåýméñţMåñåĝéŕ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "[Þåĝéš ţĥåţ ûšé Þîçţûŕé-îñ-Þîçţûŕé åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | portal": {"message": "[Þåĝéš ţĥåţ ûšé þöŕţåļš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | printing": {"message": "[Þåĝéš ţĥåţ šĥöŵ Þŕîñţîñĝ ÛÎ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "[Ţĥé þåĝé ŵåš öþéñéð ûšîñĝ 'ᐅ`window.open()`ᐊ' åñð åñöţĥéŕ ţåб ĥåš å ŕéƒéŕéñçé ţö îţ, öŕ ţĥé þåĝé öþéñéð å ŵîñðöŵ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "[Ţĥé ŕéñðéŕéŕ þŕöçéšš ƒöŕ ţĥé þåĝé îñ бåçķ/ƒöŕŵåŕð çåçĥé çŕåšĥéð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "[Ţĥé ŕéñðéŕéŕ þŕöçéšš ƒöŕ ţĥé þåĝé îñ бåçķ/ƒöŕŵåŕð çåçĥé ŵåš ķîļļéð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð åûðîö çåþţûŕé þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð šéñšöŕ þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð бåçķĝŕöûñð šýñç öŕ ƒéţçĥ þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð MÎÐÎ þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð ñöţîƒîçåţîöñš þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð šţöŕåĝé åççéšš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "[Þåĝéš ţĥåţ ĥåvé ŕéqûéšţéð vîðéö çåþţûŕé þéŕmîššîöñš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "[Öñļý þåĝéš ŵĥöšé ÛŔĻ šçĥémé îš ĤŢŢÞ / ĤŢŢÞŠ çåñ бé çåçĥéð. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "[Ţĥé þåĝé ŵåš çļåîméð бý å šéŕvîçé ŵöŕķéŕ ŵĥîļé îţ îš îñ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "[Å šéŕvîçé ŵöŕķéŕ åţţémþţéð ţö šéñð ţĥé þåĝé îñ бåçķ/ƒöŕŵåŕð çåçĥé å ᐅ`MessageEvent`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "[ŠéŕvîçéŴöŕķéŕ ŵåš ûñŕéĝîšţéŕéð ŵĥîļé å þåĝé ŵåš îñ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "[Ţĥé þåĝé ŵåš évîçţéð ƒŕöm бåçķ/ƒöŕŵåŕð çåçĥé ðûé ţö å šéŕvîçé ŵöŕķéŕ åçţîvåţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "[Çĥŕömé ŕéšţåŕţéð åñð çļéåŕéð ţĥé бåçķ/ƒöŕŵåŕð çåçĥé éñţŕîéš. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "[Þåĝéš ţĥåţ ûšé ŠĥåŕéðŴöŕķéŕ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "[Þåĝéš ţĥåţ ûšé ŠþééçĥŔéçöĝñîžéŕ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "[Þåĝéš ţĥåţ ûšé ŠþééçĥŠýñţĥéšîš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "[Åñ îƒŕåmé öñ ţĥé þåĝé šţåŕţéð å ñåvîĝåţîöñ ţĥåţ ðîð ñöţ çömþļéţé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "[Þåĝéš ŵĥöšé šûбŕéšöûŕçé ĥåš çåçĥé-çöñţŕöļ:ñö-çåçĥé çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "[Þåĝéš ŵĥöšé šûбŕéšöûŕçé ĥåš çåçĥé-çöñţŕöļ:ñö-šţöŕé çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/bf-cache-strings.js | timeout": {"message": "[Ţĥé þåĝé éxçééðéð ţĥé måxîmûm ţîmé îñ бåçķ/ƒöŕŵåŕð çåçĥé åñð ŵåš éxþîŕéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "[Ţĥé þåĝé ţîméð öûţ éñţéŕîñĝ бåçķ/ƒöŕŵåŕð çåçĥé (ļîķéļý ðûé ţö ļöñĝ-ŕûññîñĝ þåĝéĥîðé ĥåñðļéŕš). one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "[Ţĥé þåĝé ĥåš åñ ûñļöåð ĥåñðļéŕ îñ ţĥé måîñ ƒŕåmé. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "[Ţĥé þåĝé ĥåš åñ ûñļöåð ĥåñðļéŕ îñ å šûб ƒŕåmé. one two three four five six seven eight nine ten]"}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "[Бŕöŵšéŕ ĥåš çĥåñĝéð ţĥé ûšéŕ åĝéñţ övéŕŕîðé ĥéåðéŕ. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "[Þåĝéš ţĥåţ ĥåvé ĝŕåñţéð åççéšš ţö ŕéçöŕð vîðéö öŕ åûðîö åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбÐåţåбåšé åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | webHID": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбĤÎÐ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | webLocks": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбĻöçķš åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | webNfc": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбÑƒç åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбÖŢÞŠéŕvîçé åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бƒçåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/bf-cache-strings.js | webRTC": {"message": "[Þåĝéš ŵîţĥ ŴéбŔŢÇ çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | webShare": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбŠĥåŕé åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/bf-cache-strings.js | webSocket": {"message": "[Þåĝéš ŵîţĥ ŴéбŠöçķéţ çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | webTransport": {"message": "[Þåĝéš ŵîţĥ ŴéбŢŕåñšþöŕţ çåññöţ éñţéŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven]"}, "core/lib/bf-cache-strings.js | webXR": {"message": "[Þåĝéš ţĥåţ ûšé ŴéбXŔ åŕé ñöţ çûŕŕéñţļý éļîĝîбļé ƒöŕ бåçķ/ƒöŕŵåŕð çåçĥé. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "[Çöñšîðéŕ åððîñĝ ĥţţþš: åñð ĥţţþ: ÛŔĻ šçĥéméš (îĝñöŕéð бý бŕöŵšéŕš šûþþöŕţîñĝ ᐅ`'strict-dynamic'`ᐊ) ţö бé бåçķŵåŕð çömþåţîбļé ŵîţĥ öļðéŕ бŕöŵšéŕš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "[ᐅ`disown-opener`ᐊ îš ðéþŕéçåţéð šîñçé ÇŠÞ3. Þ<PERSON><PERSON><PERSON><PERSON><PERSON>, ûšé ţĥé Çŕöšš-Öŕîĝîñ-Öþéñéŕ-Þöļîçý ĥéåðéŕ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "[ᐅ`referrer`ᐊ îš ðéþŕéçåţéð šîñçé ÇŠÞ2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ûšé ţĥé Ŕéƒéŕŕéŕ-Þöļîçý ĥéåðéŕ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "[ᐅ`reflected-xss`ᐊ îš ðéþŕéçåţéð šîñçé ÇŠÞ2. Þ<PERSON><PERSON><PERSON><PERSON><PERSON>, ûšé ţĥé X-XŠŠ-Þŕöţéçţîöñ ĥéåðéŕ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "[Mîššîñĝ ᐅ`base-uri`ᐊ åļļöŵš îñĵéçţéð ᐅ`<base>`ᐊ ţåĝš ţö šéţ ţĥé бåšé ÛŔĻ ƒöŕ åļļ ŕéļåţîvé ÛŔĻš (é.ĝ. šçŕîþţš) ţö åñ åţţåçķéŕ çöñţŕöļļéð ðömåîñ. Çöñšîðéŕ šéţţîñĝ ᐅ`base-uri`ᐊ ţö ᐅ`'none'`ᐊ öŕ ᐅ`'self'`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "[Mîššîñĝ ᐅ`object-src`ᐊ åļļöŵš ţĥé îñĵéçţîöñ öƒ þļûĝîñš ţĥåţ éxéçûţé ûñšåƒé šçŕîþţš. Çöñšîðéŕ šéţţîñĝ ᐅ`object-src`ᐊ ţö ᐅ`'none'`ᐊ îƒ ýöû çåñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "[ᐅ`script-src`ᐊ ðîŕéçţîvé îš mîššîñĝ. Ţĥîš çåñ åļļöŵ ţĥé éxéçûţîöñ öƒ ûñšåƒé šçŕîþţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "[Ðîð ýöû ƒöŕĝéţ ţĥé šémîçöļöñ¿ ᐅ{keyword}ᐊ šéémš ţö бé å ðîŕéçţîvé, ñöţ å ķéýŵöŕð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "[Ñö<PERSON><PERSON><PERSON><PERSON> šĥöûļð ûšé ţĥé бåšé64 çĥåŕšéţ. one two three four five six seven eight]"}, "core/lib/csp-evaluator.js | nonceLength": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šĥöûļð бé åţ ļéåšţ 8 çĥåŕåçţéŕš ļöñĝ. one two three four five six seven eight nine]"}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "[Åvöîð ûšîñĝ þļåîñ ÛŔĻ šçĥéméš (ᐅ{keyword}ᐊ) îñ ţĥîš ðîŕéçţîvé. Þļåîñ ÛŔĻ šçĥéméš åļļöŵ šçŕîþţš ţö бé šöûŕçéð ƒŕöm åñ ûñšåƒé ðömåîñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "[Åvöîð ûšîñĝ þļåîñ ŵîļðçåŕðš (ᐅ{keyword}ᐊ) îñ ţĥîš ðîŕéçţîvé. Þļåîñ ŵîļðçåŕðš åļļöŵ šçŕîþţš ţö бé šöûŕçéð ƒŕöm åñ ûñšåƒé ðömåîñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "[Ţĥé ŕéþöŕţîñĝ ðéšţîñåţîöñ îš öñļý çöñƒîĝûŕéð vîå ţĥé ŕéþöŕţ-ţö ðîŕéçţîvé. Ţĥîš ðîŕéçţîvé îš öñļý šûþþöŕţéð îñ Çĥŕömîûm-бåšéð бŕöŵšéŕš šö îţ îš ŕéçömméñðéð ţö åļšö ûšé å ᐅ`report-uri`ᐊ ðîŕéçţîvé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "[Ñö ÇŠÞ çöñƒîĝûŕéš å ŕéþöŕţîñĝ ðéšţîñåţîöñ. Ţĥîš måķéš îţ ðîƒƒîçûļţ ţö måîñţåîñ ţĥé ÇŠÞ övéŕ ţîmé åñð möñîţöŕ ƒöŕ åñý бŕéåķåĝéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "[Ĥöšţ åļļöŵļîšţš çåñ ƒŕéqûéñţļý бé бýþåššéð. Çöñšîðéŕ ûšîñĝ ÇŠÞ ñöñçéš öŕ ĥåšĥéš îñšţéåð, åļöñĝ ŵîţĥ ᐅ`'strict-dynamic'`ᐊ îƒ ñéçéššåŕý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "[Ûñķñöŵñ ÇŠÞ ðîŕéçţîvé. one two three]"}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "[ᐅ{keyword}ᐊ šéémš ţö бé åñ îñvåļîð ķéýŵöŕð. one two three four five six seven eight]"}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "[ᐅ`'unsafe-inline'`ᐊ åļļöŵš ţĥé éxéçûţîöñ öƒ ûñšåƒé îñ-þåĝé šçŕîþţš åñð évéñţ ĥåñðļéŕš. Çöñšîðéŕ ûšîñĝ ÇŠÞ ñöñçéš öŕ ĥåšĥéš ţö åļļöŵ šçŕîþţš îñðîvîðûåļļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "[Çöñšîðéŕ åððîñĝ ᐅ`'unsafe-inline'`ᐊ (îĝñöŕéð бý бŕöŵšéŕš šûþþöŕţîñĝ ñöñçéš/ĥåšĥéš) ţö бé бåçķŵåŕð çömþåţîбļé ŵîţĥ öļðéŕ бŕöŵšéŕš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/lib/deprecation-description.js | feature": {"message": "[Çĥéçķ ţĥé ƒéåţûŕé šţåţûš þåĝé ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten]"}, "core/lib/deprecation-description.js | milestone": {"message": "[Ţĥîš çĥåñĝé ŵîļļ ĝö îñţö éƒƒéçţ ŵîţĥ mîļéšţöñé ᐅ{milestone}ᐊ. one two three four five six seven eight nine ten eleven]"}, "core/lib/deprecation-description.js | title": {"message": "[Ðéþŕéçåţéð Féåţûŕé Ûšéð one two three]"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "[Åûţĥöŕîžåţîöñ ŵîļļ ñöţ бé çövéŕéð бý ţĥé ŵîļðçåŕð šýmбöļ (*) îñ ÇÖŔŠ ᐅ`Access-Control-Allow-Headers`ᐊ ĥåñðļîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "[Ţĥé ᐅ`disableRemotePlayback`ᐊ åţţŕîбûţé šĥöûļð бé ûšéð îñ öŕðéŕ ţö ðîšåбļé ţĥé ðéƒåûļţ Çåšţ îñţéĝŕåţîöñ îñšţéåð öƒ ûšîñĝ ᐅ`-internal-media-controls-overlay-cast-button`ᐊ šéļéçţöŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "[Ŕéšöûŕçé ŕéqûéšţš ŵĥöšé ÛŔĻš çöñţåîñéð бöţĥ ŕémövéð ŵĥîţéšþåçé ᐅ`(n|r|t)`ᐊ çĥåŕåçţéŕš åñð ļéšš-ţĥåñ çĥåŕåçţéŕš (ᐅ`<`ᐊ) åŕé бļöçķéð. Þļéåšé ŕémövé ñéŵļîñéš åñð éñçöðé ļéšš-ţĥåñ çĥåŕåçţéŕš ƒŕöm þļåçéš ļîķé éļéméñţ åţţŕîбûţé våļûéš îñ öŕðéŕ ţö ļöåð ţĥéšé ŕéšöûŕçéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "[ᐅ`chrome.loadTimes()`ᐊ îš ðéþŕéçåţéð, îñšţéåð ûšé šţåñðåŕðîžéð ÅÞÎ: Ñåvîĝåţîöñ Ţîmîñĝ 2. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "[ᐅ`chrome.loadTimes()`ᐊ îš ðéþŕéçåţéð, îñšţéåð ûšé šţåñðåŕðîžéð ÅÞÎ: Þåîñţ Ţîmîñĝ. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "[ᐅ`chrome.loadTimes()`ᐊ îš ðéþŕéçåţéð, îñšţéåð ûšé šţåñðåŕðîžéð ÅÞÎ: ᐅ`nextHopProtocol`ᐊ îñ Ñåvîĝåţîöñ Ţîmîñĝ 2. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "[Çööķîéš çöñţåîñîñĝ å ᐅ`(0|r|n)`ᐊ çĥåŕåçţéŕ ŵîļļ бé ŕéĵéçţéð îñšţéåð öƒ ţŕûñçåţéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "[Ŕéļåxîñĝ ţĥé šåmé-öŕîĝîñ þöļîçý бý šéţţîñĝ ᐅ`document.domain`ᐊ îš ðéþŕéçåţéð, åñð ŵîļļ бé ðîšåбļéð бý ðéƒåûļţ. Ţĥîš ðéþŕéçåţîöñ ŵåŕñîñĝ îš ƒöŕ å çŕöšš-öŕîĝîñ åççéšš ţĥåţ ŵåš éñåбļéð бý šéţţîñĝ ᐅ`document.domain`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "[Ţŕîĝĝéŕîñĝ ŵîñðöŵ.åļéŕţ ƒŕöm çŕöšš öŕîĝîñ îƒŕåméš ĥåš бééñ ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð îñ ţĥé ƒûţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "[Ţŕîĝĝéŕîñĝ ŵîñðöŵ.çöñƒîŕm ƒŕöm çŕöšš öŕîĝîñ îƒŕåméš ĥåš бééñ ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð îñ ţĥé ƒûţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "[ÐÖM Mûţåţîöñ Évéñţš, îñçļûðîñĝ ᐅ`DOMSubtreeModified`ᐊ, ᐅ`DOMNodeInserted`ᐊ, ᐅ`DOMNodeRemoved`ᐊ, ᐅ`DOMNodeRemovedFromDocument`ᐊ, ᐅ`DOMNodeInsertedIntoDocument`ᐊ, åñð ᐅ`DOMCharacterDataModified`ᐊ åŕé ðéþŕéçåţéð (ĥţţþš://ŵ3ç.ĝîţĥûб.îö/ûîévéñţš/#ļéĝåçý-évéñţ-ţýþéš) åñð ŵîļļ бé ŕémövéð. Þļéåšé ûšé ᐅ`MutationObserver`ᐊ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "[Šûþþöŕţ ƒöŕ ðåţå: ÛŔĻš îñ ŠVĜ <use> éļéméñţ îš ðéþŕéçåţéð åñð îţ ŵîļļ бé ŕémövéð îñ ţĥé ƒûţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "[Ŕéļåxîñĝ ţĥé šåmé-öŕîĝîñ þöļîçý бý šéţţîñĝ ᐅ`document.domain`ᐊ îš ðéþŕéçåţéð, åñð ŵîļļ бé ðîšåбļéð бý ðéƒåûļţ. Ţö çöñţîñûé ûšîñĝ ţĥîš ƒéåţûŕé, þļéåšé öþţ-öûţ öƒ öŕîĝîñ-ķéýéð åĝéñţ çļûšţéŕš бý šéñðîñĝ åñ ᐅ`Origin-Agent-Cluster: ?0`ᐊ ĥéåðéŕ åļöñĝ ŵîţĥ ţĥé ĤŢŢÞ ŕéšþöñšé ƒöŕ ţĥé ðöçûméñţ åñð ƒŕåméš. Šéé ĥţţþš://ðévéļöþéŕ.çĥŕömé.çöm/бļöĝ/îmmûţåбļé-ðöçûméñţ-ðömåîñ/ ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty]"}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "[Ţĥé ᐅ`Expect-CT`ᐊ ĥéåðéŕ îš ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð. Çĥŕömé ŕéqûîŕéš Çéŕţîƒîçåţé Ţŕåñšþåŕéñçý ƒöŕ åļļ þûбļîçļý ţŕûšţéð çéŕţîƒîçåţéš îššûéð åƒţéŕ Åþŕîļ 30, 2018. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "[ᐅ`getCurrentPosition()`ᐊ åñð ᐅ`watchPosition()`ᐊ ñö ļöñĝéŕ ŵöŕķ öñ îñšéçûŕé öŕîĝîñš. Ţö ûšé ţĥîš ƒéåţûŕé, ýöû šĥöûļð çöñšîðéŕ šŵîţçĥîñĝ ýöûŕ åþþļîçåţîöñ ţö å šéçûŕé öŕîĝîñ, šûçĥ åš ĤŢŢÞŠ. Šéé ĥţţþš://ĝöö.ĝļé/çĥŕömé-îñšéçûŕé-öŕîĝîñš ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "[ᐅ`getCurrentPosition()`ᐊ åñð ᐅ`watchPosition()`ᐊ åŕé ðéþŕéçåţéð öñ îñšéçûŕé öŕîĝîñš. Ţö ûšé ţĥîš ƒéåţûŕé, ýöû šĥöûļð çöñšîðéŕ šŵîţçĥîñĝ ýöûŕ åþþļîçåţîöñ ţö å šéçûŕé öŕîĝîñ, šûçĥ åš ĤŢŢÞŠ. Šéé ĥţţþš://ĝöö.ĝļé/çĥŕömé-îñšéçûŕé-öŕîĝîñš ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "[ᐅ`getUserMedia()`ᐊ ñö ļöñĝéŕ ŵöŕķš öñ îñšéçûŕé öŕîĝîñš. Ţö ûšé ţĥîš ƒéåţûŕé, ýöû šĥöûļð çöñšîðéŕ šŵîţçĥîñĝ ýöûŕ åþþļîçåţîöñ ţö å šéçûŕé öŕîĝîñ, šûçĥ åš ĤŢŢÞŠ. Šéé ĥţţþš://ĝöö.ĝļé/çĥŕömé-îñšéçûŕé-öŕîĝîñš ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "[ᐅ`RTCPeerConnectionIceErrorEvent.hostCandidate`ᐊ îš ðéþŕéçåţéð. Þļéåšé ûšé ᐅ`RTCPeerConnectionIceErrorEvent.address`ᐊ öŕ ᐅ`RTCPeerConnectionIceErrorEvent.port`ᐊ îñšţéåð. one two three four five six seven eight nine ten]"}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "[Ţĥé méŕçĥåñţ öŕîĝîñ åñð åŕбîţŕåŕý ðåţå ƒŕöm ţĥé ᐅ`canmakepayment`ᐊ šéŕvîçé ŵöŕķéŕ évéñţ åŕé ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð: ᐅ`topOrigin`ᐊ, ᐅ`paymentRequestOrigin`ᐊ, ᐅ`methodData`ᐊ, ᐅ`modifiers`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "[Ţĥé ŵéбšîţé ŕéqûéšţéð å šûбŕéšöûŕçé ƒŕöm å ñéţŵöŕķ ţĥåţ îţ çöûļð öñļý åççéšš бéçåûšé öƒ îţš ûšéŕš' þŕîvîļéĝéð ñéţŵöŕķ þöšîţîöñ. Ţĥéšé ŕéqûéšţš éxþöšé ñöñ-þûбļîç ðévîçéš åñð šéŕvéŕš ţö ţĥé îñţéŕñéţ, îñçŕéåšîñĝ ţĥé ŕîšķ öƒ å çŕöšš-šîţé ŕéqûéšţ ƒöŕĝéŕý (ÇŠŔF) åţţåçķ, åñð/öŕ îñƒöŕmåţîöñ ļéåķåĝé. Ţö mîţîĝåţé ţĥéšé ŕîšķš, Çĥŕömé ðéþŕéçåţéš ŕéqûéšţš ţö ñöñ-þûбļîç šûбŕéšöûŕçéš ŵĥéñ îñîţîåţéð ƒŕöm ñöñ-šéçûŕé çöñţéxţš, åñð ŵîļļ šţåŕţ бļöçķîñĝ ţĥém. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "[Ţĥé ᐅ`dailyUpdateUrl`ᐊ ƒîéļð öƒ ᐅ`InterestGroups`ᐊ þåššéð ţö ᐅ`joinAdInterestGroup()`ᐊ ĥåš бééñ ŕéñåméð ţö ᐅ`updateUrl`ᐊ, ţö möŕé åççûŕåţéļý ŕéƒļéçţ îţš бéĥåvîöŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "[ÇŠŠ çåññöţ бé ļöåðéð ƒŕöm ᐅ`file:`ᐊ ÛŔĻš ûñļéšš ţĥéý éñð îñ å ᐅ`.css`ᐊ ƒîļé éxţéñšîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "[Ûšîñĝ ᐅ`SourceBuffer.abort()`ᐊ ţö åбöŕţ ᐅ`remove()`ᐊ'š åšýñçĥŕöñöûš ŕåñĝé ŕémövåļ îš ðéþŕéçåţéð ðûé ţö šþéçîƒîçåţîöñ çĥåñĝé. Šûþþöŕţ ŵîļļ бé ŕémövéð îñ ţĥé ƒûţûŕé. Ýöû šĥöûļð ļîšţéñ ţö ţĥé ᐅ`updateend`ᐊ évéñţ îñšţéåð. ᐅ`abort()`ᐊ îš îñţéñðéð ţö öñļý åбöŕţ åñ åšýñçĥŕöñöûš méðîå åþþéñð öŕ ŕéšéţ þåŕšéŕ šţåţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree]"}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "[Šéţţîñĝ ᐅ`MediaSource.duration`ᐊ бéļöŵ ţĥé ĥîĝĥéšţ þŕéšéñţåţîöñ ţîméšţåmþ öƒ åñý бûƒƒéŕéð çöðéð ƒŕåméš îš ðéþŕéçåţéð ðûé ţö šþéçîƒîçåţîöñ çĥåñĝé. Šûþþöŕţ ƒöŕ îmþļîçîţ ŕémövåļ öƒ ţŕûñçåţéð бûƒƒéŕéð méðîå ŵîļļ бé ŕémövéð îñ ţĥé ƒûţûŕé. Ýöû šĥöûļð îñšţéåð þéŕƒöŕm éxþļîçîţ ᐅ`remove(newDuration, oldDuration)`ᐊ öñ åļļ ᐅ`sourceBuffers`ᐊ, ŵĥéŕé ᐅ`newDuration < oldDuration`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour]"}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "[Ŵéб MÎÐÎ ŵîļļ åšķ å þéŕmîššîöñ ţö ûšé évéñ îƒ ţĥé šýšéx îš ñöţ šþéçîƒîéð îñ ţĥé ᐅ`MIDIOptions`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "[Ţĥé öļðéŕ, ñöñ-šţåñðåŕðîžéð ᐅ`shadowroot`ᐊ åţţŕîбûţé îš ðéþŕéçåţéð, åñð ŵîļļ *ñö ļöñĝéŕ ƒûñçţîöñ* îñ M119. Þļéåšé ûšé ţĥé ñéŵ, šţåñðåŕðîžéð ᐅ`shadowrootmode`ᐊ åţţŕîбûţé îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "[Ţĥé Ñöţîƒîçåţîöñ ÅÞÎ måý ñö ļöñĝéŕ бé ûšéð ƒŕöm îñšéçûŕé öŕîĝîñš. Ýöû šĥöûļð çöñšîðéŕ šŵîţçĥîñĝ ýöûŕ åþþļîçåţîöñ ţö å šéçûŕé öŕîĝîñ, šûçĥ åš ĤŢŢÞŠ. Šéé ĥţţþš://ĝöö.ĝļé/çĥŕömé-îñšéçûŕé-öŕîĝîñš ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "[Þéŕmîššîöñ ƒöŕ ţĥé Ñöţîƒîçåţîöñ ÅÞÎ måý ñö ļöñĝéŕ бé ŕéqûéšţéð ƒŕöm å çŕöšš-öŕîĝîñ îƒŕåmé. Ýöû šĥöûļð çöñšîðéŕ ŕéqûéšţîñĝ þéŕmîššîöñ ƒŕöm å ţöþ-ļévéļ ƒŕåmé öŕ öþéñîñĝ å ñéŵ ŵîñðöŵ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "[Öþţîöñ ᐅ`imageOrientation: 'none'`ᐊ îñ çŕéåţéÎmåĝéБîţmåþ îš ðéþŕéçåţéð. Þļéåšé ûšé çŕéåţéÎmåĝéБîţmåþ ŵîţĥ öþţîöñ \\{îmåĝéÖŕîéñţåţîöñ: 'ƒŕöm-îmåĝé'\\} îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "[Ýöûŕ þåŕţñéŕ îš ñéĝöţîåţîñĝ åñ öбšöļéţé (Ð)ŢĻŠ véŕšîöñ. Þļéåšé çĥéçķ ŵîţĥ ýöûŕ þåŕţñéŕ ţö ĥåvé ţĥîš ƒîxéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "[Šþéçîƒýîñĝ ᐅ`overflow: visible`ᐊ öñ îmĝ, vîðéö åñð çåñvåš ţåĝš måý çåûšé ţĥém ţö þŕöðûçé vîšûåļ çöñţéñţ öûţšîðé öƒ ţĥé éļéméñţ бöûñðš. Šéé ĥţţþš://ĝîţĥûб.çöm/ŴÎÇĜ/šĥåŕéð-éļéméñţ-ţŕåñšîţîöñš/бļöб/måîñ/ðéбûĝĝîñĝ_övéŕƒļöŵ_öñ_îmåĝéš.mð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "[ᐅ`paymentManager.instruments`ᐊ îš ðéþŕéçåţéð. Þļéåšé ûšé ĵûšţ-îñ-ţîmé îñšţåļļ ƒöŕ þåýméñţ ĥåñðļéŕš îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "[Ýöûŕ ᐅ`PaymentRequest`ᐊ çåļļ бýþå<PERSON><PERSON><PERSON>ð Çöñţéñţ-Šéçûŕîţý-Þöļîçý (ÇŠÞ) ᐅ`connect-src`ᐊ ðîŕéçţîvé. Ţĥîš бýþåšš îš ðéþŕéçåţéð. Þļéåšé åðð ţĥé þåýméñţ méţĥöð îðéñţîƒîéŕ ƒŕöm ţĥé ᐅ`PaymentRequest`ᐊ ÅÞÎ (îñ ᐅ`supportedMethods`ᐊ ƒîéļð) ţö ýöûŕ ÇŠÞ ᐅ`connect-src`ᐊ ðîŕéçţîvé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "[ᐅ`StorageType.persistent`ᐊ îš ðéþŕéçåţéð. Þļéåšé ûšé šţåñðåŕðîžéð ᐅ`navigator.storage`ᐊ îñšţéåð. one two three four five six seven eight nine ten eleven]"}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "[ᐅ`<source src>`ᐊ ŵîţĥ å ᐅ`<picture>`ᐊ þåŕéñţ îš îñvåļîð åñð ţĥéŕéƒöŕé îĝñöŕéð. Þļéåšé ûšé ᐅ`<source srcset>`ᐊ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "[ŵéбķîţÇåñçéļÅñîmåţîöñFŕåmé îš véñðöŕ-šþéçîƒîç. Þļéåšé ûšé ţĥé šţåñðåŕð çåñçéļÅñîmåţîöñFŕåmé îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "[ŵéбķîţŔéqûéšţÅñîmåţîöñFŕåmé îš véñðöŕ-šþéçîƒîç. Þļéåšé ûšé ţĥé šţåñðåŕð ŕéqûéšţÅñîmåţîöñFŕåmé îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţÐîšþļåýîñĝFûļļšçŕééñ îš ðéþŕéçåţéð. Þ<PERSON><PERSON><PERSON><PERSON>é ûšé Ðöçûméñţ.ƒûļļšçŕééñÉļéméñţ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţÉñţéŕFûļļŠçŕééñ() îš ðéþŕéçåţéð. Þļéåšé ûšé Éļéméñţ.ŕéqûéšţFûļļšçŕééñ() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţÉñţéŕFûļļšçŕééñ() îš ðéþŕéçåţéð. Þļéåšé ûšé Éļéméñţ.ŕéqûéšţFûļļšçŕééñ() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţÉxîţFûļļŠçŕééñ() îš ðéþŕéçåţéð. Þļéåšé ûšé Ðöçûméñţ.éxîţFûļļšçŕééñ() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţÉxîţFûļļšçŕééñ() îš ðéþŕéçåţéð. Þļéåšé ûšé Ðöçûméñţ.éxîţFûļļšçŕééñ() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "[ĤŢMĻVîðéöÉļéméñţ.ŵéбķîţŠûþþöŕţšFûļļšçŕééñ îš ðéþŕéçåţéð. Þ<PERSON><PERSON><PERSON><PERSON>é ûšé Ðöçûméñţ.ƒûļļšçŕééñÉñåбļéð îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "[Ŵé'ŕé ðéþŕéçåţîñĝ ţĥé ÅÞÎ ᐅ`chrome.privacy.websites.privacySandboxEnabled`ᐊ, ţĥöûĝĥ îţ ŵîļļ ŕémåîñ åçţîvé ƒöŕ бåçķŵåŕð çömþåţîбîļîţý ûñţîļ ŕéļéåšé M113. Îñ<PERSON><PERSON><PERSON><PERSON><PERSON>, þļéåšé ûšé ᐅ`chrome.privacy.websites.topicsEnabled`ᐊ, ᐅ`chrome.privacy.websites.fledgeEnabled`ᐊ åñð ᐅ`chrome.privacy.websites.adMeasurementEnabled`ᐊ. Šéé ĥţţþš://ðévéļöþéŕ.çĥŕömé.çöm/ðöçš/éxţéñšîöñš/ŕéƒéŕéñçé/þŕîvåçý/#þŕöþéŕţý-ŵéбšîţéš-þŕîvåçýŠåñðбöxÉñåбļéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "[Ţĥé çöñšţŕåîñţ ᐅ`DtlsSrtpKeyAgreement`ᐊ îš ŕémövéð. Ýöû ĥåvé šþéçîƒîéð å ᐅ`false`ᐊ våļûé ƒöŕ ţĥîš çöñšţŕåîñţ, ŵĥîçĥ îš îñţéŕþŕéţéð åš åñ åţţémþţ ţö ûšé ţĥé ŕémövéð ᐅ`SDES key negotiation`ᐊ méţĥöð. Ţĥîš ƒûñçţîöñåļîţý îš ŕémövéð; ûšé å šéŕvîçé ţĥåţ šûþþöŕţš ᐅ`DTLS key negotiation`ᐊ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "[Ţĥé çöñšţŕåîñţ ᐅ`DtlsSrtpKeyAgreement`ᐊ îš ŕémövéð. Ýöû ĥåvé šþéçîƒîéð å ᐅ`true`ᐊ våļûé ƒöŕ ţĥîš çöñšţŕåîñţ, ŵĥîçĥ ĥåð ñö éƒƒéçţ, бûţ ýöû çåñ ŕémövé ţĥîš çöñšţŕåîñţ ƒöŕ ţîðîñéšš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "[Ţĥé çåļļбåçķ-бåš<PERSON>ð ĝéţŠţåţš() îš ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð. Ûšé ţĥé šþéç-çömþļîåñţ ĝéţŠţåţš() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "[Ŕåñĝé.éxþåñð() îš ðéþŕéçåţéð. Þļéåšé ûšé <PERSON>éçţîöñ.möðîƒý() îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "[Šûбŕéšöûŕçé ŕéqûéšţš ŵĥöšé ÛŔĻš çöñţåîñ émбéððéð çŕéðéñţîåļš (é.ĝ. ᐅ`**********************/`ᐊ) åŕé бļöçķéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "[Ţĥé ᐅ`rtcpMuxPolicy`ᐊ öþţîöñ îš ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð. one two three four five six seven eight nine ten]"}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "[ᐅ`SharedArrayBuffer`ᐊ ŵîļļ ŕéqûîŕé çŕöšš-öŕîĝîñ îšöļåţîöñ. Šéé ĥţţþš://ðévéļöþéŕ.çĥŕömé.çöm/бļöĝ/éñåбļîñĝ-šĥåŕéð-åŕŕåý-бûƒƒéŕ/ ƒöŕ möŕé ðéţåîļš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "[ᐅ`speechSynthesis.speak()`ᐊ ŵîţĥöûţ ûšéŕ åçţîvåţîöñ îš ðéþŕéçåţéð åñð ŵîļļ бé ŕémövéð. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "[Éxţéñšîöñš šĥöûļð öþţ îñţö çŕöšš-öŕîĝîñ îšöļåţîöñ ţö çöñţîñûé ûšîñĝ ᐅ`SharedArrayBuffer`ᐊ. Šéé ĥţţþš://ðévéļöþéŕ.çĥŕömé.çöm/ðöçš/éxţéñšîöñš/mv3/çŕöšš-öŕîĝîñ-îšöļåţîöñ/. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/deprecations-strings.js | WebSQL": {"message": "[Ŵéб ŠQĻ îš ðéþŕéçåţéð. Þ<PERSON><PERSON><PERSON><PERSON>é ûšé ŠQĻîţé ŴéбÅššémбļý öŕ Îñðéxéð Ðåţåбåšé one two three four five six seven eight nine ten eleven twelve thirteen]"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "[Ţĥé þéŕmîššîöñ ðéšçŕîþţöŕ ᐅ`window-placement`ᐊ îš ðéþŕéçåţéð. Ûšé ᐅ`window-management`ᐊ îñšţéåð. Föŕ möŕé ĥéļþ, çĥéçķ ĥţţþš://бîţ.ļý/ŵîñðöŵ-þļåçéméñţ-ŕéñåmé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "[Ţĥé þéŕmîššîöñ þöļîçý ᐅ`window-placement`ᐊ îš ðéþŕéçåţéð. Ûšé ᐅ`window-management`ᐊ îñšţéåð. Föŕ möŕé ĥéļþ, çĥéçķ ĥţţþš://бîţ.ļý/ŵîñðöŵ-þļåçéméñţ-ŕéñåmé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "[ÛŢF-16 îš ñöţ šûþþöŕţéð бý ŕéšþöñšé ĵšöñ îñ ᐅ`XMLHttpRequest`ᐊ one two three four five six seven eight nine ten]"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "[Šýñçĥŕöñöûš ᐅ`XMLHttpRequest`ᐊ öñ ţĥé måîñ ţĥŕéåð îš ðéþŕéçåţéð бéçåûšé öƒ îţš ðéţŕîméñţåļ éƒƒéçţš ţö ţĥé éñð ûšéŕ'š éxþéŕîéñçé. Föŕ möŕé ĥéļþ, çĥéçķ ĥţţþš://xĥŕ.šþéç.ŵĥåţŵĝ.öŕĝ/. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "[ᐅ`supportsSession()`ᐊ îš ðéþŕéçåţéð. Þļéåšé ûšé ᐅ`isSessionSupported()`ᐊ åñð çĥéçķ ţĥé ŕéšöļvéð бööļéåñ våļûé îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "[Måîñ-Ţĥŕéåð Бļöçķîñĝ Ţîmé one two three]"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "[Çåçĥé ŢŢĻ one two]"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "[Ðéšçŕîþţîöñ one two]"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "[Ðûŕåţîöñ one]"}, "core/lib/i18n/i18n.js | columnElement": {"message": "[É<PERSON><PERSON><PERSON>ñţ one]"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "[Fåîļîñĝ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "[Ļöçåţîöñ one]"}, "core/lib/i18n/i18n.js | columnName": {"message": "[Ñåmé one]"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "[Övéŕ Бûðĝéţ one two]"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "[Ŕéqûéšţš one]"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "[Ŕéšöûŕçé <PERSON><PERSON><PERSON>é one two]"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "[Ŕéšöûŕçé <PERSON><PERSON><PERSON><PERSON> one two]"}, "core/lib/i18n/i18n.js | columnSize": {"message": "[Šîžé one]"}, "core/lib/i18n/i18n.js | columnSource": {"message": "[Šöûŕçé one]"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "[Šţåŕţ <PERSON>îmé one two]"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "[<PERSON><PERSON><PERSON> one two]"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "[Ţŕåñšƒéŕ <PERSON><PERSON><PERSON>é one two]"}, "core/lib/i18n/i18n.js | columnURL": {"message": "[ÛŔĻ one]"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "[Þ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Šåvîñĝš one two three]"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "[Þ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Šåvîñĝš one two three]"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "[Çûmûļåţîvé Ļåýöûţ Šĥîƒţ one two three]"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "[Þöţéñţîåļ šåvîñĝš öƒ ᐅ{wastedBytes, number, bytes}ᐊ ĶîБ one two three four five six]"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{[1 éļéméñţ ƒöûñð one two]}other{[# éļéméñţš ƒöûñð one two]}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "[Þöţé<PERSON>ţîåļ šåvîñĝš öƒ ᐅ{wastedMs, number, milliseconds}ᐊ mš one two three four five six]"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "[Ðöçûméñţ one]"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "[Fîŕšţ Çöñţéñţƒûļ Þåîñţ one two three]"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "[Fîŕšţ Méåñîñĝƒûļ Þåîñţ one two three]"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "[Föñ<PERSON> one]"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "[Îmåĝé one]"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "[Îñţéŕåçţîöñ ţö Ñéxţ Þåîñ<PERSON> one two three four five six]"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "[Ţîmé ţö Îñţéŕåçţîvé one two three]"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "[Ĥîĝĥ one]"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "[Ļöŵ one]"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "[Méðîûm one]"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "[Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ one two three]"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "[Måx Þöţéñţîåļ Fîŕšţ Îñþûţ Ðéļåý one two three four five six seven]"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "[Méðîå one]"}, "core/lib/i18n/i18n.js | ms": {"message": "[ᐅ{timeInMs, number, milliseconds}ᐊ mš one two]"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "[Öţĥéŕ one]"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "[Öţĥéŕ ŕéšöûŕçéš one two]"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "[Šçŕîþţ one]"}, "core/lib/i18n/i18n.js | seconds": {"message": "[ᐅ{timeInMs, number, seconds}ᐊ š one two]"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "[Šţýļéšĥééţ one two]"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "[Ţĥîŕð-þåŕţý one two]"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "[Ţöţåļ Бļöçķîñĝ Ţîmé one two three]"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "[Ţöţåļ one]"}, "core/lib/lh-error.js | badTraceRecording": {"message": "[Šöméţĥîñĝ ŵéñţ ŵŕöñĝ ŵîţĥ ŕéçöŕðîñĝ ţĥé ţŕåçé övéŕ ýöûŕ þåĝé ļöåð. Þļéåšé ŕûñ Ļîĝĥţĥöûšé åĝåîñ. (ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "core/lib/lh-error.js | criTimeout": {"message": "[Ţîméöûţ ŵåîţîñĝ ƒöŕ îñîţîåļ Ðéбûĝĝéŕ Þŕöţöçöļ çöññéçţîöñ. one two three four five six seven eight nine ten eleven twelve]"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "[Çĥŕömé ðîðñ'ţ çöļļéçţ åñý šçŕééñšĥöţš ðûŕîñĝ ţĥé þåĝé ļöåð. Þļéåšé måķé šûŕé ţĥéŕé îš çöñţéñţ vîšîбļé öñ ţĥé þåĝé, åñð ţĥéñ ţŕý ŕé-ŕûññîñĝ Ļîĝĥţĥöûšé. (ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/lh-error.js | dnsFailure": {"message": "[ÐÑŠ šéŕvéŕš çöûļð ñöţ ŕéšöļvé ţĥé þŕövîðéð ðömåîñ. one two three four five six seven eight nine ten eleven]"}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "[Ŕéqûîŕéð ᐅ{artifactName}ᐊ ĝåţĥéŕéŕ éñçöûñţéŕéð åñ éŕŕöŕ: ᐅ{errorMessage}ᐊ one two three four five six seven eight nine ten]"}, "core/lib/lh-error.js | internalChromeError": {"message": "[Åñ îñţéŕñåļ Çĥŕömé éŕŕöŕ öççûŕŕéð. Þ<PERSON><PERSON><PERSON><PERSON>é ŕéšţåŕţ Çĥŕömé åñð ţŕý ŕé-ŕûññîñĝ Ļîĝĥţĥöûšé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "[Ŕéqûîŕéð ᐅ{artifactName}ᐊ ĝåţĥéŕéŕ ðîð ñöţ ŕûñ. one two three four five six seven]"}, "core/lib/lh-error.js | noFcp": {"message": "[Ţĥé þåĝé ðîð ñöţ þåîñţ åñý çöñţéñţ. Þļéåšé éñšûŕé ýöû ķééþ ţĥé бŕöŵšéŕ ŵîñðöŵ îñ ţĥé ƒöŕéĝŕöûñð ðûŕîñĝ ţĥé ļöåð åñð ţŕý åĝåîñ. (ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "core/lib/lh-error.js | noLcp": {"message": "[Ţĥé þåĝé ðîð ñöţ ðîšþļåý çöñţéñţ ţĥåţ qûåļîƒîéš åš å Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ (ĻÇÞ). Éñšûŕé ţĥé þåĝé ĥåš å våļîð ĻÇÞ éļéméñţ åñð ţĥéñ ţŕý åĝåîñ. (ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/lh-error.js | notHtml": {"message": "[Ţĥé þåĝé þŕövîðéð îš ñöţ ĤŢMĻ (šéŕvéð åš MÎMÉ ţýþé ᐅ{mimeType}ᐊ). one two three four five six seven eight nine ten eleven]"}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "[Ţĥîš véŕšîöñ öƒ Çĥŕömé îš ţöö öļð ţö šûþþöŕţ 'ᐅ{featureName}ᐊ'. Ûšé å ñéŵéŕ véŕšîöñ ţö šéé ƒûļļ ŕéšûļţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/lh-error.js | pageLoadFailed": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ŕéļîåбļý ļöåð ţĥé þåĝé ýöû ŕéqûéšţéð. Måķé šûŕé ýöû åŕé ţéšţîñĝ ţĥé çöŕŕéçţ ÛŔĻ åñð ţĥåţ ţĥé šéŕvéŕ îš þŕöþéŕļý ŕéšþöñðîñĝ ţö åļļ ŕéqûéšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ŕéļîåбļý ļöåð ţĥé ÛŔĻ ýöû ŕéqûéšţéð бéçåûšé ţĥé þåĝé šţöþþéð ŕéšþöñðîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "[Ţĥé ÛŔĻ ýöû ĥåvé þŕövîðéð ðöéš ñöţ ĥåvé å våļîð šéçûŕîţý çéŕţîƒîçåţé. ᐅ{securityMessages}ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "[Çĥŕömé þŕévéñţéð þåĝé ļöåð ŵîţĥ åñ îñţéŕšţîţîåļ. Måķé šûŕé ýöû åŕé ţéšţîñĝ ţĥé çöŕŕéçţ ÛŔĻ åñð ţĥåţ ţĥé šéŕvéŕ îš þŕöþéŕļý ŕéšþöñðîñĝ ţö åļļ ŕéqûéšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ŕéļîåбļý ļöåð ţĥé þåĝé ýöû ŕéqûéšţéð. Måķé šûŕé ýöû åŕé ţéšţîñĝ ţĥé çöŕŕéçţ ÛŔĻ åñð ţĥåţ ţĥé šéŕvéŕ îš þŕöþéŕļý ŕéšþöñðîñĝ ţö åļļ ŕéqûéšţš. (Ðéţåîļš: ᐅ{errorDetails}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ŕéļîåбļý ļöåð ţĥé þåĝé ýöû ŕéqûéšţéð. Måķé šûŕé ýöû åŕé ţéšţîñĝ ţĥé çöŕŕéçţ ÛŔĻ åñð ţĥåţ ţĥé šéŕvéŕ îš þŕöþéŕļý ŕéšþöñðîñĝ ţö åļļ ŕéqûéšţš. (Šţåţûš çöðé: ᐅ{statusCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "[Ýöûŕ þåĝé ţööķ ţöö ļöñĝ ţö ļöåð. Þļéåšé ƒöļļöŵ ţĥé öþþöŕţûñîţîéš îñ ţĥé ŕéþöŕţ ţö ŕéðûçé ýöûŕ þåĝé ļöåð ţîmé, åñð ţĥéñ ţŕý ŕé-ŕûññîñĝ Ļîĝĥţĥöûšé. (ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "core/lib/lh-error.js | protocolTimeout": {"message": "[Ŵåîţîñĝ ƒöŕ ÐévŢööļš þŕöţöçöļ ŕéšþöñšé ĥåš éxçééðéð ţĥé åļļöţţéð ţîmé. (Méţĥöð: ᐅ{protocolMethod}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "[Féţçĥîñĝ ŕéšöûŕçé çöñţéñţ ĥåš éxçééðéð ţĥé åļļöţţéð ţîmé one two three four five six seven eight nine ten eleven]"}, "core/lib/lh-error.js | urlInvalid": {"message": "[Ţĥé ÛŔĻ ýöû ĥåvé þŕövîðéð åþþéåŕš ţö бé îñvåļîð. one two three four five six seven eight nine ten]"}, "core/lib/navigation-error.js | warningStatusCode": {"message": "[Ļîĝĥţĥöûšé ŵåš ûñåбļé ţö ŕéļîåбļý ļöåð ţĥé þåĝé ýöû ŕéqûéšţéð. Måķé šûŕé ýöû åŕé ţéšţîñĝ ţĥé çöŕŕéçţ ÛŔĻ åñð ţĥåţ ţĥé šéŕvéŕ îš þŕöþéŕļý ŕéšþöñðîñĝ ţö åļļ ŕéqûéšţš. (Šţåţûš çöðé: ᐅ{errorCode}ᐊ) one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "core/lib/navigation-error.js | warningXhtml": {"message": "[Ţĥé þåĝé MÎMÉ ţýþé îš XĤŢMĻ: Ļîĝĥţĥöûšé ðöéš ñöţ éxþļîçîţļý šûþþöŕţ ţĥîš ðöçûméñţ ţýþé one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "core/user-flow.js | defaultFlowName": {"message": "[Ûšéŕ ƒļöŵ (ᐅ{url}ᐊ) one two three four]"}, "core/user-flow.js | defaultNavigationName": {"message": "[Ñåvîĝåţîöñ ŕéþöŕţ (ᐅ{url}ᐊ) one two three four five]"}, "core/user-flow.js | defaultSnapshotName": {"message": "[Šñåþšĥöţ ŕéþöŕţ (ᐅ{url}ᐊ) one two three four five]"}, "core/user-flow.js | defaultTimespanName": {"message": "[Ţîmé<PERSON>þåñ ŕéþöŕţ (ᐅ{url}ᐊ) one two three four five]"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "[<PERSON><PERSON><PERSON> Ŕéþöŕţš one two]"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "[Çåţéĝöŕîéš one two]"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>бîļîţý one two]"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "[Б<PERSON><PERSON><PERSON> Þŕåçţîçéš one two]"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "[Þéŕƒöŕmåñçé one two]"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "[Þŕöĝŕéššîvé Ŵéб Åþþ one two three]"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "[ŠÉÖ one]"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "[Ðéšķţöþ one]"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "[Ûñðéŕšţåñðîñĝ ţĥé Ļîĝĥţĥöûšé Fļöŵ Ŕéþöŕţ one two three four five six seven eight]"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "[Ûñðéŕšţåñðîñĝ <PERSON><PERSON><PERSON><PERSON><PERSON> one two three]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "[Ûšé Ñåvîĝåţîöñ ŕéþöŕţš ţö... one two three four five six]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "[Ûšé Šñåþšĥöţ ŕéþöŕţš ţö... one two three four five six]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "[Ûšé Ţîméšþåñ ŕéþöŕţš ţö... one two three four five six]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "[Ö<PERSON><PERSON><PERSON><PERSON>ñ å Ļîĝĥţĥöûšé Þéŕƒöŕmåñçé šçöŕé. one two three four five six seven eight]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "[Méåšûŕé þåĝé ļöåð Þéŕƒöŕmåñçé méţŕîçš šûçĥ åš Ļåŕĝéšţ Çöñţéñţƒûļ Þåîñţ åñð Šþééð Îñðéx. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Þŕöĝŕéššîvé Ŵéб Åþþ çåþåбîļîţîéš. one two three four five six seven eight]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "[Fîñð åççéššîбîļîţý îššûéš îñ šîñĝļé þåĝé åþþļîçåţîöñš öŕ çömþļéx ƒöŕmš. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "[Évåļû<PERSON><PERSON>é бéšţ þŕåçţîçéš öƒ méñûš åñð ÛÎ éļéméñţš ĥîððéñ бéĥîñð îñţéŕåçţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "[Méåšûŕé ļåýöûţ šĥîƒţš åñð ĴåvåŠçŕîþţ éxéçûţîöñ ţîmé öñ å šéŕîéš öƒ îñţéŕåçţîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "[Ðîšçövéŕ þéŕƒöŕmåñçé öþþöŕţûñîţîéš ţö îmþŕövé ţĥé éxþéŕîéñçé ƒöŕ ļöñĝ-ļîvéð þåĝéš åñð šîñĝļé-þåĝé åþþļîçåţîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "[Ĥîĝĥéšţ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{[ᐅ{numInformative}ᐊ îñƒöŕmåţîvé åûðîţ one two three four five]}other{[ᐅ{numInformative}ᐊ îñƒöŕmåţîvé åûðîţš one two three four five]}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one]"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "[Þåĝé <PERSON><PERSON><PERSON><PERSON> one two]"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "[Ñåvîĝåţîöñ ŕéþöŕţš åñåļ<PERSON><PERSON><PERSON> å šîñĝļé þåĝé ļöåð, éxåçţļý ļîķé ţĥé öŕîĝîñåļ Ļîĝĥţĥöûšé ŕéþöŕţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "[Ñåvîĝåţîöñ ŕéþöŕţ one two three]"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{[ᐅ{numNavigation}ᐊ ñåvîĝåţîöñ ŕéþöŕţ one two three four five]}other{[ᐅ{numNavigation}ᐊ ñåvîĝåţîöñ ŕéþöŕţš one two three four five]}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{[ᐅ{numPassableAudits}ᐊ þåššåбļé åûðîţ one two three four]}other{[ᐅ{numPassableAudits}ᐊ þåššåбļé åûðîţš one two three four]}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{[ᐅ{numPassed}ᐊ åûðîţ þåššéð one two three four]}other{[ᐅ{numPassed}ᐊ åûðîţš þåššéð one two three four]}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "[Åvéŕåĝé one]"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "[Éŕŕöŕ one]"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "[Þööŕ one]"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "[Ĝööð one]"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "[Šåvé one]"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "[Çåþţûŕéð šţåţé öƒ þåĝé one two three four five]"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "[Šñåþšĥöţ ŕéþöŕţš åñåļýžé ţĥé þåĝé îñ å þåŕţîçûļåŕ šţåţé, ţýþîçåļļý åƒţéŕ ûšéŕ îñţéŕåçţîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "[Šñåþšĥöţ ŕéþöŕţ one two]"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{[ᐅ{numSnapshot}ᐊ šñåþšĥöţ ŕéþöŕţ one two three four]}other{[ᐅ{numSnapshot}ᐊ šñåþšĥöţ ŕéþöŕţš one two three four five]}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "[Šûmmåŕý one]"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "[Ûšéŕ îñţéŕåçţîöñš one two three]"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "[Ţîméšþåñ ŕéþöŕţš åñåļýžé åñ åŕбîţŕåŕý þéŕîöð öƒ ţîmé, ţýþîçåļļý çöñţåîñîñĝ ûšéŕ îñţéŕåçţîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "[Ţî<PERSON><PERSON><PERSON><PERSON><PERSON> ŕéþöŕţ one two]"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{[ᐅ{numTimespan}ᐊ ţîméšþåñ ŕéþöŕţ one two three four]}other{[ᐅ{numTimespan}ᐊ ţîméšþåñ ŕéþöŕţš one two three four five]}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "[Ļîĝĥţĥöûšé Ûšéŕ Fļöŵ Ŕéþöŕţ one two three four five six]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "[Föŕ åñîmåţéð çöñţéñţ, ûšé ᐅ[ᐊᐅ`amp-anim`ᐊᐅ](https://amp.dev/documentation/components/amp-anim/)ᐊ ţö mîñîmîžé ÇÞÛ ûšåĝé ŵĥéñ ţĥé çöñţéñţ îš öƒƒšçŕééñ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "[Çöñšîðéŕ ðîšþļåýîñĝ åļļ ᐅ[ᐊᐅ`amp-img`ᐊᐅ](https://amp.dev/documentation/components/amp-img/?format=websites)ᐊ çömþöñéñţš îñ ŴéбÞ ƒöŕmåţš ŵĥîļé šþéçîƒýîñĝ åñ åþþŕöþŕîåţé ƒåļļбåçķ ƒöŕ öţĥéŕ бŕöŵšéŕš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "[Éñšûŕé ţĥåţ ýöû åŕé ûšîñĝ ᐅ[ᐊᐅ`amp-img`ᐊᐅ](https://amp.dev/documentation/components/amp-img/?format=websites)ᐊ ƒöŕ îmåĝéš ţö åûţömåţîçåļļý ļåžý-ļöåð. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[Ûšé ţööļš šûçĥ åš ᐅ[ᐊÅMÞ Öþţîmîžéŕᐅ](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)ᐊ ţö ᐅ[ᐊšéŕvéŕ-šîðé ŕéñðéŕ ÅMÞ ļåýöûţšᐅ](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "[Ŕéƒéŕ ţö ţĥé ᐅ[ᐊÅMÞ ðöçûméñţåţîöñᐅ](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)ᐊ ţö éñšûŕé åļļ šţýļéš åŕé šûþþöŕţéð. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[Ţĥé ᐅ[ᐊᐅ`amp-img`ᐊᐅ](https://amp.dev/documentation/components/amp-img/?format=websites)ᐊ çömþöñéñţ šûþþöŕţš ţĥé ᐅ[ᐊᐅ`srcset`ᐊᐅ](https://web.dev/use-srcset-to-automatically-choose-the-right-image/)ᐊ åţţŕîбûţé ţö šþéçîƒý ŵĥîçĥ îmåĝé åššéţš ţö ûšé бåšéð öñ ţĥé šçŕééñ šîžé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "[Çöñšîðéŕ vîŕţûåļ šçŕöļļîñĝ ŵîţĥ ţĥé Çömþöñéñţ Ðév Ķîţ (ÇÐĶ) îƒ véŕý ļåŕĝé ļîšţš åŕé бéîñĝ ŕéñðéŕéð. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://web.dev/virtualize-lists-with-angular-cdk/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "[Åþþļý ᐅ[ᐊŕöûţé-ļévéļ çöðé šþļîţţîñĝᐅ](https://web.dev/route-level-code-splitting-in-angular/)ᐊ ţö mîñîmîžé ţĥé šîžé öƒ ýöûŕ ĴåvåŠçŕîþţ бûñðļéš. Åļš<PERSON>, çöñšîðéŕ þŕéçåçĥîñĝ åššéţš ŵîţĥ ţĥé ᐅ[ᐊÅñĝûļåŕ šéŕvîçé ŵöŕķéŕᐅ](https://web.dev/precaching-with-the-angular-service-worker/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "[Îƒ ýöû åŕé ûšîñĝ Åñĝûļåŕ ÇĻÎ, éñšûŕé ţĥåţ бûîļðš åŕé ĝéñéŕåţéð îñ þŕöðûçţîöñ möðé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://angular.io/guide/deployment#enable-runtime-production-mode)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "[Îƒ ýöû åŕé ûšîñĝ Åñĝûļåŕ ÇĻÎ, îñçļûðé šöûŕçé måþš îñ ýöûŕ þŕöðûçţîöñ бûîļð ţö îñšþéçţ ýöûŕ бûñðļéš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://angular.io/guide/deployment#inspect-the-bundles)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "[Þŕéļöåð ŕöûţéš åĥéåð öƒ ţîmé ţö šþééð ûþ ñåvîĝåţîöñ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://web.dev/route-preloading-in-angular/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "[Çöñšîðéŕ ûšîñĝ ţĥé ᐅ`BreakpointObserver`ᐊ ûţîļîţý îñ ţĥé Çömþöñéñţ Ðév Ķîţ (ÇÐĶ) ţö måñåĝé îmåĝé бŕéåķþöîñţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://material.angular.io/cdk/layout/overview)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "[Çöñšîðéŕ ûþļöåðîñĝ ýöûŕ ĜÎF ţö å šéŕvîçé ŵĥîçĥ ŵîļļ måķé îţ åvåîļåбļé ţö émбéð åš åñ ĤŢMĻ5 vîðéö. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "[Šþéçîƒý ᐅ`@font-display`ᐊ ŵĥéñ ðéƒîñîñĝ çûšţöm ƒöñţš îñ ýöûŕ ţĥémé. one two three four five six seven eight nine ten eleven]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "[Çöñšîðéŕ çöñƒîĝûŕîñĝ ᐅ[ᐊŴéбÞ îmåĝé ƒöŕmåţš ŵîţĥ å Çöñvéŕţ îmåĝé šţýļéᐅ](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)ᐊ öñ ýöûŕ šîţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "[Îñšţåļļ ᐅ[ᐊå Ðŕûþåļ möðûļéᐅ](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search)ᐊ ţĥåţ çåñ ļåžý ļöåð îmåĝéš. Šûçĥ möðûļéš þŕövîðé ţĥé åбîļîţý ţö ðéƒéŕ åñý öƒƒšçŕééñ îmåĝéš ţö îmþŕövé þéŕƒöŕmåñçé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "[Çöñšîðéŕ ûšîñĝ å möðûļé ţö îñļîñé çŕîţîçåļ ÇŠŠ åñð ĴåvåŠçŕîþţ, åñð ûšé ţĥé ðéƒéŕ åţţŕîбûţé ƒöŕ ñöñ-çŕîţîçåļ ÇŠŠ öŕ ĴåvåŠçŕîþţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "[Ţĥéméš, möðûļ<PERSON><PERSON>, åñð šéŕvéŕ šþéçîƒîçåţîöñš åļļ çöñţŕîбûţé ţö šéŕvéŕ ŕéšþöñšé ţîmé. Çöñšîðéŕ ƒîñðîñĝ å möŕé öþţîmîžéð ţĥémé, çåŕéƒûļļý šéļéçţîñĝ åñ öþţîmîžåţîöñ möðûļé, åñð/öŕ ûþĝŕåðîñĝ ýöûŕ šéŕvéŕ. Ýöûŕ ĥöšţîñĝ šéŕvéŕš šĥöûļð måķé ûšé öƒ ÞĤÞ öþçöðé çåçĥîñĝ, mémöŕý-çåçĥîñĝ ţö ŕéðûçé ðåţåбåšé qûéŕý ţîméš šûçĥ åš Ŕéðîš öŕ Mémçåçĥéð, åš ŵéļļ åš öþţîmîžéð åþþļîçåţîöñ ļöĝîç ţö þŕéþåŕé þåĝéš ƒåšţéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "[Çöñšîðéŕ ûšîñĝ ᐅ[ᐊŔéšþöñšîvé Îmåĝé Šţýļéšᐅ](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ᐊ ţö ŕéðûçé ţĥé šîžé öƒ îmåĝéš ļöåðéð öñ ýöûŕ þåĝé. Îƒ ýöû åŕé ûšîñĝ Vîéŵš ţö šĥöŵ mûļţîþļé çöñţéñţ îţémš öñ å þåĝé, çöñšîðéŕ îmþļéméñţîñĝ þåĝîñåţîöñ ţö ļîmîţ ţĥé ñûmбéŕ öƒ çöñţéñţ îţémš šĥöŵñ öñ å ĝîvéñ þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "[Éñšûŕé ýöû ĥåvé éñåбļéð \"Åĝĝŕéĝåţé ÇŠŠ ƒîļéš\" îñ ţĥé \"Åðmîñîšţŕåţîöñ » Çöñƒîĝûŕåţîöñ » Ðévéļöþméñţ\" þåĝé.  Éñšûŕé ýöûŕ Ðŕûþåļ šîţé îš ŕûññîñĝ åţ ļéåšţ Ðŕûþåļ 10.1 ƒöŕ îmþŕövéð åššéţ åĝĝŕéĝåţîöñ šûþþöŕţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "[Éñšûŕé ýöû ĥåvé éñå<PERSON>ļéð \"Åĝĝŕéĝåţé ĴåvåŠçŕîþţ ƒîļéš\" îñ ţĥé \"Åðmîñîšţŕåţîöñ » Çöñƒîĝûŕåţîöñ » Ðévéļöþméñţ\" þåĝé.  Éñšûŕé ýöûŕ Ðŕûþåļ šîţé îš ŕûññîñĝ åţ ļéåšţ Ðŕûþåļ 10.1 ƒöŕ îmþŕövéð åššéţ åĝĝŕéĝåţîöñ šûþþöŕţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "[Çöñšîðéŕ ŕémövîñĝ ûñûšéð ÇŠŠ ŕûļéš åñð öñļý åţţåçĥ ţĥé ñééðéð Ðŕûþåļ ļîбŕåŕîéš ţö ţĥé ŕéļévåñţ þåĝé öŕ çömþöñéñţ îñ å þåĝé. Šéé ţĥé ᐅ[ᐊÐŕûþåļ ðöçûméñţåţîöñ ļîñķᐅ](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)ᐊ ƒöŕ ðéţåîļš. Ţö îðéñţîƒý åţţåçĥéð ļîбŕåŕîéš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ÇŠŠ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé ţĥémé/möðûļé ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šţýļéšĥééţ ŵĥéñ ÇŠŠ åĝĝŕéĝåţîöñ îš ðîšåбļéð îñ ýöûŕ Ðŕûþåļ šîţé. Ļööķ öûţ ƒöŕ ţĥéméš/möðûļéš ţĥåţ ĥåvé måñý šţýļéšĥééţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Å ţĥémé/möðûļé šĥöûļð öñļý éñqûéûé å šţýļéšĥééţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "[Çöñšîðéŕ ŕémövîñĝ ûñûšéð ĴåvåŠçŕîþţ åššéţš åñð öñļý åţţåçĥ ţĥé ñééðéð Ðŕûþåļ ļîбŕåŕîéš ţö ţĥé ŕéļévåñţ þåĝé öŕ çömþöñéñţ îñ å þåĝé. Šéé ţĥé ᐅ[ᐊÐŕûþåļ ðöçûméñţåţîöñ ļîñķᐅ](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)ᐊ ƒöŕ ðéţåîļš. Ţö îðéñţîƒý åţţåçĥéð ļîбŕåŕîéš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ĴåvåŠçŕîþţ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé ţĥémé/möðûļé ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šçŕîþţ ŵĥéñ ĴåvåŠçŕîþţ åĝĝŕéĝåţîöñ îš ðîšåбļéð îñ ýöûŕ Ðŕûþåļ šîţé. Ļööķ öûţ ƒöŕ ţĥéméš/möðûļéš ţĥåţ ĥåvé måñý šçŕîþţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Å ţĥémé/möðûļé šĥöûļð öñļý éñqûéûé å šçŕîþţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "[Šéţ ţĥé \"Бŕöŵšéŕ åñð þŕöxý çåçĥé måxîmûm åĝé\" îñ ţĥé \"Åðmîñîšţŕåţîöñ » Çöñƒîĝûŕåţîöñ » Ðévéļöþméñţ\" þåĝé. Ŕéåð åбöûţ ᐅ[ᐊÐŕûþåļ çåçĥé åñð öþţîmîžîñĝ ƒöŕ þéŕƒöŕmåñçéᐅ](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "[Çöñšîðéŕ ûšîñĝ ᐅ[ᐊå möðûļéᐅ](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)ᐊ ţĥåţ åûţömåţîçåļļý öþţîmîžéš åñð ŕéðûçéš ţĥé šîžé öƒ îmåĝéš ûþļöåðéð ţĥŕöûĝĥ ţĥé šîţé ŵĥîļé ŕéţåîñîñĝ qûåļîţý. Åļšö, éñšûŕé ýöû åŕé ûšîñĝ ţĥé ñåţîvé ᐅ[ᐊŔéšþöñšîvé Îmåĝé Šţýļéšᐅ](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ᐊ þŕövîðéð ƒŕöm Ðŕûþåļ (åvåîļåбļé îñ Ðŕûþåļ 8 åñð åбövé) ƒöŕ åļļ îmåĝéš ŕéñðéŕéð öñ ţĥé šîţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "[Þŕéçöññéçţ öŕ ðñš-þŕéƒéţçĥ ŕéšöûŕçé ĥîñţš çåñ бé åððéð бý îñšţåļļîñĝ åñð çöñƒîĝûŕîñĝ ᐅ[ᐊå möðûļéᐅ](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)ᐊ ţĥåţ þŕövîðéš ƒåçîļîţîéš ƒöŕ ûšéŕ åĝéñţ ŕéšöûŕçé ĥîñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree]"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "[Éñšûŕé ţĥåţ ýöû åŕé ûšîñĝ ţĥé ñåţîvé ᐅ[ᐊŔéšþöñšîvé Îmåĝé Šţýļéšᐅ](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)ᐊ þŕövîðéð ƒŕöm Ðŕûþåļ (åvåîļåбļé îñ Ðŕûþåļ 8 åñð åбövé). Ûšé ţĥé Ŕéšþöñšîvé Îmåĝé Šţýļéš ŵĥéñ ŕéñðéŕîñĝ îmåĝé ƒîéļðš ţĥŕöûĝĥ vîéŵ möðéš, vîéŵš, öŕ îmåĝéš ûþļöåðéð ţĥŕöûĝĥ ţĥé ŴÝŠÎŴÝĜ éðîţöŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Optimize Fonts`ᐊ ţö åûţömåţîçåļļý ļévéŕåĝé ţĥé ᐅ`font-display`ᐊ ÇŠŠ ƒéåţûŕé ţö éñšûŕé ţéxţ îš ûšéŕ-vîšîбļé ŵĥîļé ŵéбƒöñţš åŕé ļöåðîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Next-Gen Formats`ᐊ ţö çöñvéŕţ îmåĝéš ţö ŴéбÞ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Lazy Load Images`ᐊ ţö ðéƒéŕ ļöåðîñĝ öƒƒ-šçŕééñ îmåĝéš ûñţîļ ţĥéý åŕé ñééðéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Critical CSS`ᐊ åñð ᐅ`Script Delay`ᐊ ţö ðéƒéŕ ñöñ-çŕîţîçåļ ĴŠ/ÇŠŠ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "[Ûšé ᐅ[ᐊÉžöîç Çļöûð Çåçĥîñĝᐅ](https://pubdash.ezoic.com/speed/caching)ᐊ ţö çåçĥé ýöûŕ çöñţéñţ åçŕöšš öûŕ ŵöŕļð ŵîðé ñéţŵöŕķ, îmþŕövîñĝ ţîmé ţö ƒîŕšţ бýţé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Minify CSS`ᐊ ţö åûţömåţîçåļļý mîñîƒý ýöûŕ ÇŠŠ ţö ŕéðûçé ñéţŵöŕķ þåýļöåð šîžéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Minify Javascript`ᐊ ţö åûţömåţîçåļļý mîñîƒý ýöûŕ ĴŠ ţö ŕéðûçé ñéţŵöŕķ þåýļöåð šîžéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Remove Unused CSS`ᐊ ţö ĥéļþ ŵîţĥ ţĥîš îššûé. Îţ ŵîļļ îðéñţîƒý ţĥé ÇŠŠ çļåššéš ţĥåţ åŕé åçţûåļļý ûšéð öñ éåçĥ þåĝé öƒ ýöûŕ šîţé, åñð ŕémövé åñý öţĥéŕš ţö ķééþ ţĥé ƒîļé šîžé šmåļļ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Efficient Static Cache Policy`ᐊ ţö šéţ ŕéçömméñðéð våļûéš îñ ţĥé çåçĥîñĝ ĥéåðéŕ ƒöŕ šţåţîç åššéšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Next-Gen Formats`ᐊ ţö çöñvéŕţ îmåĝéš ţö ŴéбÞ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Pre-Connect Origins`ᐊ ţö åûţömåţîçåļļý åðð ᐅ`preconnect`ᐊ ŕéšöûŕçé ĥîñţš ţö éšţåбļîšĥ éåŕļý çöññéçţîöñš ţö îmþöŕţåñţ ţĥîŕð-þåŕţý öŕîĝîñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Preload Fonts`ᐊ åñð ᐅ`Preload Background Images`ᐊ ţö åðð ᐅ`preload`ᐊ ļîñķš ţö þŕîöŕîţîžé ƒéţçĥîñĝ ŕéšöûŕçéš ţĥåţ åŕé çûŕŕéñţļý ŕéqûéšţéð ļåţéŕ îñ þåĝé ļöåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "[Ûšé ᐅ[ᐊÉžöîç Ļéåþᐅ](https://pubdash.ezoic.com/speed)ᐊ åñð éñåбļé ᐅ`Resize Images`ᐊ ţö ŕéšîžé îmåĝéš ţö å ðévîçé åþþŕöþŕîåţé šîžé, ŕéðûçîñĝ ñéţŵöŕķ þåýļöåð šîžéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "[Ûšé ţĥé ᐅ`gatsby-plugin-image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åûţömåţîçåļļý öþţîmîžé îmåĝé ƒöŕmåţ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "[Ûšé ţĥé ᐅ`gatsby-plugin-image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åûţömåţîçåļļý ļåžý-ļöåð îmåĝéš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "[Ûšé ţĥé ᐅ`gatsby-plugin-image`ᐊ çömþöñéñţ åñð šéţ ţĥé ᐅ`loading`ᐊ þŕöþ ţö ᐅ`eager`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "[Ûšé ţĥé ᐅ`Gatsby Script API`ᐊ ţö ðéƒéŕ ļöåðîñĝ öƒ ñöñ-çŕîţîçåļ ţĥîŕð-þåŕţý šçŕîþţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "[Ûšé ţĥé ᐅ`PurgeCSS`ᐊ ᐅ`Gatsby`ᐊ þļûĝîñ ţö ŕémövé ûñûšéð ŕûļéš ƒŕöm šţýļéšĥééţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://purgecss.com/plugins/gatsby.html)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "[Ûšé ᐅ`Webpack Bundle Analyzer`ᐊ ţö ðéţéçţ ûñûšéð ĴåvåŠçŕîþţ çöðé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)ᐊ one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "[Çöñƒîĝûŕé çåçĥîñĝ ƒöŕ îmmûţåбļé åššéţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)ᐊ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "[Ûšé ţĥé ᐅ`gatsby-plugin-image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åðĵûšţ îmåĝé qûåļîţý. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "[Ûšé ţĥé ᐅ`gatsby-plugin-image`ᐊ çömþöñéñţ ţö šéţ åþþŕöþŕîåţé ᐅ`sizes`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)ᐊ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "[Çöñšîðéŕ ûþļöåðîñĝ ýöûŕ ĜÎF ţö å šéŕvîçé ŵĥîçĥ ŵîļļ måķé îţ åvåîļåбļé ţö émбéð åš åñ ĤŢMĻ5 vîðéö. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "[Çöñšîðéŕ ûšîñĝ å ᐅ[ᐊþļûĝîñᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp)ᐊ öŕ šéŕvîçé ţĥåţ ŵîļļ åûţömåţîçåļļý çöñvéŕţ ýöûŕ ûþļöåðéð îmåĝéš ţö ţĥé öþţîmåļ ƒöŕmåţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "[Îñšţåļļ å ᐅ[ᐊļåžý-ļöåð Ĵöömļå þļûĝîñᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading)ᐊ ţĥåţ þŕövîðéš ţĥé åбîļîţý ţö ðéƒéŕ åñý öƒƒšçŕééñ îmåĝéš, öŕ šŵîţçĥ ţö å ţémþļåţé ţĥåţ þŕövîðéš ţĥåţ ƒûñçţîöñåļîţý. Šţåŕţîñĝ ŵîţĥ Ĵöömļå 4.0, åļļ ñéŵ îmåĝéš ŵîļļ ᐅ[ᐊåûţömåţîçåļļýᐅ](https://github.com/joomla/joomla-cms/pull/30748)ᐊ ĝéţ ţĥé ᐅ`loading`ᐊ åţţŕîбûţé ƒŕöm ţĥé çöŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "[Ţĥéŕé åŕé å ñûmбéŕ öƒ Ĵöömļå þļûĝîñš ţĥåţ çåñ ĥéļþ ýöû ᐅ[ᐊîñļîñé çŕîţîçåļ åššéţšᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ᐊ öŕ ᐅ[ᐊðéƒéŕ ļéšš îmþöŕţåñţ ŕéšöûŕçéšᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ᐊ. Бéŵåŕé ţĥåţ öþţîmîžåţîöñš þŕövîðéð бý ţĥéšé þļûĝîñš måý бŕéåķ ƒéåţûŕéš öƒ ýöûŕ ţémþļåţéš öŕ þļûĝîñš, šö ýöû ŵîļļ ñééð ţö ţéšţ ţĥéšé ţĥöŕöûĝĥļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "[Ţém<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, éx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, åñð šéŕvéŕ šþéçîƒîçåţîöñš åļļ çöñţŕîбûţé ţö šéŕvéŕ ŕéšþöñšé ţîmé. Çöñšîðéŕ ƒîñðîñĝ å möŕé öþţîmîžéð ţémþļåţé, çåŕéƒûļļý šéļéçţîñĝ åñ öþţîmîžåţîöñ éxţéñšîöñ, åñð/öŕ ûþĝŕåðîñĝ ýöûŕ šéŕvéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "[Çöñšîðéŕ šĥöŵîñĝ éxçéŕþţš îñ ýöûŕ åŕţîçļé çåţéĝöŕîéš (é.ĝ. vîå ţĥé ŕéåð möŕé ļîñķ), ŕéðûçîñĝ ţĥé ñûmбéŕ öƒ åŕţîçļéš šĥöŵñ öñ å ĝîvéñ þåĝé, бŕéåķîñĝ ýöûŕ ļöñĝ þöšţš îñţö mûļţîþļé þåĝéš, öŕ ûšîñĝ å þļûĝîñ ţö ļåžý-ļöåð çömméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "[Å ñûmбéŕ öƒ ᐅ[ᐊĴöömļå éxţéñšîöñšᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ᐊ çåñ šþééð ûþ ýöûŕ šîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ, åñð çömþŕéššîñĝ ýöûŕ çšš šţýļéš. Ţĥéŕé åŕé åļšö ţémþļåţéš ţĥåţ þŕövîðé ţĥîš ƒûñçţîöñåļîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "[Å ñûmбéŕ öƒ ᐅ[ᐊĴöömļå éxţéñšîöñšᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ᐊ çåñ šþééð ûþ ýöûŕ šîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ, åñð çömþŕéššîñĝ ýöûŕ šçŕîþţš. Ţĥéŕé åŕé åļšö ţémþļåţéš ţĥåţ þŕövîðé ţĥîš ƒûñçţîöñåļîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ, öŕ šŵîţçĥîñĝ, ţĥé ñûmбéŕ öƒ ᐅ[ᐊĴöömļå éxţéñšîöñšᐅ](https://extensions.joomla.org/)ᐊ ļöåðîñĝ ûñûšéð ÇŠŠ îñ ýöûŕ þåĝé. Ţö îðéñţîƒý éxţéñšîöñš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ÇŠŠ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé ţĥémé/þļûĝîñ ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šţýļéšĥééţ. Ļööķ öûţ ƒöŕ þļûĝîñš ţĥåţ ĥåvé måñý šţýļéšĥééţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Å þļûĝîñ šĥöûļð öñļý éñqûéûé å šţýļéšĥééţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ, öŕ šŵîţçĥîñĝ, ţĥé ñûmбéŕ öƒ ᐅ[ᐊĴöömļå éxţéñšîöñšᐅ](https://extensions.joomla.org/)ᐊ ļöåðîñĝ ûñûšéð ĴåvåŠçŕîþţ îñ ýöûŕ þåĝé. Ţö îðéñţîƒý þļûĝîñš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ĴŠ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé éxţéñšîöñ ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šçŕîþţ. Ļööķ öûţ ƒöŕ éxţéñšîöñš ţĥåţ ĥåvé måñý šçŕîþţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Åñ éxţéñšîöñ šĥöûļð öñļý éñqûéûé å šçŕîþţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "[Ŕéåð åбöûţ ᐅ[ᐊБŕöŵšéŕ Çåçĥîñĝ îñ Ĵöömļåᐅ](https://docs.joomla.org/Cache)ᐊ. one two three four five six seven eight nine]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "[Çöñšîðéŕ ûšîñĝ åñ ᐅ[ᐊîmåĝé öþţîmîžåţîöñ þļûĝîñᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ᐊ ţĥåţ çömþŕéššéš ýöûŕ îmåĝéš ŵĥîļé ŕéţåîñîñĝ qûåļîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "[Çöñšîðéŕ ûšîñĝ å ᐅ[ᐊŕéšþöñšîvé îmåĝéš þļûĝîñᐅ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)ᐊ ţö ûšé ŕéšþöñšîvé îmåĝéš îñ ýöûŕ çöñţéñţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "[Ýöû çåñ éñåбļé ţéxţ çömþŕéššîöñ бý éñåбļîñĝ Ĝžîþ Þåĝé Çömþŕéššîöñ îñ Ĵöömļå (Šýšţém > Ĝļöбåļ çöñƒîĝûŕåţîöñ > Šéŕvéŕ). one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "[Îƒ ýöû åŕé ñöţ бûñðļîñĝ ýöûŕ ĴåvåŠçŕîþţ åššéţš, çöñšîðéŕ ûšîñĝ ᐅ[ᐊбåļéŕᐅ](https://github.com/magento/baler)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "[Ðîšåбļé Måĝéñţö'š бûîļţ-îñ ᐅ[ᐊĴåvåŠçŕîþţ бûñðļîñĝ åñð mîñîƒîçåţîöñᐅ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)ᐊ, åñð çöñšîðéŕ ûšîñĝ ᐅ[ᐊбåļéŕᐅ](https://github.com/magento/baler/)ᐊ îñšţéåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[Šþéçîƒý ᐅ`@font-display`ᐊ ŵĥéñ ᐅ[ᐊðéƒîñîñĝ çûšţöm ƒöñţšᐅ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)ᐊ. one two three four five six seven eight nine]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "[Çöñšîðéŕ šéåŕçĥîñĝ ţĥé ᐅ[ᐊMåĝéñţö Måŕķéţþļåçéᐅ](https://marketplace.magento.com/catalogsearch/result/?q=webp)ᐊ ƒöŕ å våŕîéţý öƒ ţĥîŕð-þåŕţý éxţéñšîöñš ţö ļévéŕåĝé ñéŵéŕ îmåĝé ƒöŕmåţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "[Çöñšîðéŕ möðîƒýîñĝ ýöûŕ þŕöðûçţ åñð çåţåļöĝ ţémþļåţéš ţö måķé ûšé öƒ ţĥé ŵéб þļåţƒöŕm'š ᐅ[ᐊļåžý ļöåðîñĝᐅ](https://web.dev/native-lazy-loading)ᐊ ƒéåţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "[Ûšé Måĝéñţö'š ᐅ[ᐊVåŕñîšĥ îñţéĝŕåţîöñᐅ](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)ᐊ. one two three four five six seven eight]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "[Éñåбļé ţĥé \"Mîñîƒý ÇŠŠ Fîļéš\" öþţîöñ îñ ýöûŕ šţöŕé'š Ðévéļöþéŕ šéţţîñĝš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "[Ûšé ᐅ[ᐊŢéŕšéŕᐅ](https://www.npmjs.com/package/terser)ᐊ ţö mîñîƒý åļļ ĴåvåŠçŕîþţ åššéţš ƒŕöm šţåţîç çöñţéñţ ðéþļöýméñţ, åñð ðîšåбļé ţĥé бûîļţ-îñ mîñîƒîçåţîöñ ƒéåţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "[Ðîšåбļé Måĝéñţö'š бûîļţ-îñ ᐅ[ᐊĴåvåŠçŕîþţ бûñðļîñĝᐅ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)ᐊ. one two three four five six seven eight nine ten eleven]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "[Çöñšîðéŕ šéåŕçĥîñĝ ţĥé ᐅ[ᐊMåĝéñţö Måŕķéţþļåçéᐅ](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image)ᐊ ƒöŕ å våŕîéţý öƒ ţĥîŕð þåŕţý éxţéñšîöñš ţö öþţîmîžé îmåĝéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[Þŕéçöññéçţ öŕ ðñš-þŕéƒéţçĥ ŕéšöûŕçé ĥîñţš çåñ бé åððéð бý ᐅ[ᐊmöðîƒýîñĝ å ţĥéméš'š ļåýöûţᐅ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "[ᐅ`<link rel=preload>`ᐊ ţåĝš çåñ бé åððéð бý ᐅ[ᐊmöðîƒýîñĝ å ţĥéméš'š ļåýöûţᐅ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)ᐊ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åûţömåţîçåļļý öþţîmîžé îmåĝé ƒöŕmåţ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/basic-features/image-optimization)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åûţömåţîçåļļý ļåžý-ļöåð îmåĝéš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/basic-features/image-optimization)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ åñð šéţ \"þŕîöŕîţý\" ţö ţŕûé ţö þŕéļöåð ĻÇÞ îmåĝé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/api-reference/next/image#priority)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "[Ûšé ţĥé ᐅ`next/script`ᐊ çömþöñéñţ ţö ðéƒéŕ ļöåðîñĝ öƒ ñöñ-çŕîţîçåļ ţĥîŕð-þåŕţý šçŕîþţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/basic-features/script)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ ţö måķé šûŕé îmåĝéš åŕé åļŵåýš šîžéð åþþŕöþŕîåţéļý. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/api-reference/next/image#width)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "[Çöñšîðéŕ šéţţîñĝ ûþ ᐅ`PurgeCSS`ᐊ îñ ᐅ`Next.js`ᐊ çöñƒîĝûŕåţîöñ ţö ŕémövé ûñûšéð ŕûļéš ƒŕöm šţýļéšĥééţš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://purgecss.com/guides/next.html)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "[Ûšé ᐅ`Webpack Bundle Analyzer`ᐊ ţö ðéţéçţ ûñûšéð ĴåvåŠçŕîþţ çöðé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)ᐊ one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "[Çöñšîðéŕ ûšîñĝ ᐅ`Next.js Analytics`ᐊ ţö méåšûŕé ýöûŕ åþþ'š ŕéåļ-ŵöŕļð þéŕƒöŕmåñçé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/advanced-features/measuring-performance)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "[Çöñƒîĝûŕé çåçĥîñĝ ƒöŕ îmmûţåбļé åššéţš åñð ᐅ`Server-side Rendered`ᐊ (ŠŠŔ) þåĝéš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/going-to-production#caching)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ îñšţéåð öƒ ᐅ`<img>`ᐊ ţö åðĵûšţ îmåĝé qûåļîţý. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/basic-features/image-optimization)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "[Ûšé ţĥé ᐅ`next/image`ᐊ çömþöñéñţ ţö šéţ ţĥé åþþŕöþŕîåţé ᐅ`sizes`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/api-reference/next/image#sizes)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "[Éñåбļé çömþŕéššîöñ öñ ýöûŕ Ñéxţ.ĵš šéŕvéŕ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://nextjs.org/docs/api-reference/next.config.js/compression)ᐊ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "[Çöñ<PERSON>å<PERSON>ţ ýöûŕ åççöûñţ måñåĝéŕ ţö éñåбļé ᐅ[ᐊᐅ`HTML Lazy Load`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/17144942904337)ᐊ. Çöñƒîĝûŕîñĝ îţ ŵîļļ þŕîöŕîţîžé åñð öþţîmîžé ýöûŕ þåĝé ŕéñðéŕîñĝ þéŕƒöŕmåñçé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "[Ûšé ţĥé ᐅ[ᐊᐅ`Override Font Rendering Behavior`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/16547358865041)ᐊ öþţîöñ îñ ÑîţŕöÞåçķ ţö šéţ å ðéšîŕéð våļûé ƒöŕ ţĥé ÇŠŠ ƒöñţ-ðîšþļåý ŕûļé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "[Ûšé ᐅ[ᐊᐅ`Image Optimization`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/16547237162513)ᐊ ţö åûţömåţîçåļļý çöñvéŕţ ýöûŕ îmåĝéš ţö ŴéбÞ. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "[Ðéƒéŕ öƒƒšçŕééñ îmåĝéš бý éñåбļîñĝ ᐅ[ᐊᐅ`Automatic Image Lazy Loading`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)ᐊ. one two three four five six seven eight nine]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "[Éñåбļé ᐅ[ᐊᐅ`Remove render-blocking resources`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack)ᐊ îñ ÑîţŕöÞåçķ ƒöŕ ƒåšţéŕ îñîţîåļ ļöåð ţîméš. one two three four five six seven eight nine ten eleven twelve]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "[Îmþŕövé šéŕvéŕ ŕéšþöñšé ţîmé åñð öþţîmîžé þéŕçéîvéð þéŕƒöŕmåñçé бý åçţîvåţîñĝ ᐅ[ᐊᐅ`Instant Load`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/16547340617361)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "[Éñåбļé ᐅ[ᐊᐅ`Minify resources`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources)ᐊ îñ ýöûŕ Çåçĥîñĝ šéţţîñĝš ţö ŕéðûçé ţĥé šîžé öƒ ýöûŕ ÇŠŠ, ĤŢMĻ, åñð ĴåvåŠçŕîþţ ƒîļéš ƒöŕ ƒåšţéŕ ļöåð ţîméš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "[Éñåбļé ᐅ[ᐊᐅ`Minify resources`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources)ᐊ îñ ýöûŕ Çåçĥîñĝ šéţţîñĝš ţö ŕéðûçé ţĥé šîžé öƒ ýöûŕ ĴŠ, ĤŢMĻ, åñð ÇŠŠ ƒîļéš ƒöŕ ƒåšţéŕ ļöåð ţîméš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "[Éñåбļé ᐅ[ᐊᐅ`Reduce Unused CSS`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS)ᐊ ţö ŕémövé ÇŠŠ ŕûļéš ţĥåţ åŕé ñöţ åþþļîçåбļé ţö ţĥîš þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "[Çöñƒîĝûŕé ᐅ[ᐊᐅ`Delayed Scripts`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts)ᐊ îñ ÑîţŕöÞåçķ ţö ðéļåý ļöåðîñĝ öƒ šçŕîþţš ûñţîļ ţĥéý åŕé ñééðéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "[Ĝö ţö ţĥé ᐅ[ᐊᐅ`Improve Server Response Time`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time)ᐊ ƒéåţûŕé îñ ţĥé ᐅ`Caching`ᐊ méñû åñð åðĵûšţ ýöûŕ þåĝé çåçĥé éxþîŕåţîöñ ţîmé ţö îmþŕövé ļöåðîñĝ ţîméš åñð ûšéŕ éxþéŕîéñçé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "[Åûţöm<PERSON>ţî<PERSON><PERSON>ļļý çömþŕéšš, öþ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, åñð çöñvéŕţ ýöûŕ îmåĝéš îñţö ŴéбÞ бý éñåбļîñĝ ţĥé ᐅ[ᐊᐅ`Image Optimization`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)ᐊ šéţţîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "[Éñåбļé ᐅ[ᐊᐅ`Adaptive Image Sizing`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site)ᐊ ţö þŕéémþţîvéļý öþţîmîžé ýöûŕ îmåĝéš åñð måķé ţĥém måţçĥ ţĥé ðîméñšîöñš öƒ ţĥé çöñţåîñéŕš ţĥéý’ŕé ðîšþļåýéð îñ åçŕöšš åļļ ðévîçéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "[Ûšé ᐅ[ᐊᐅ`Gzip compression`ᐊᐅ](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression)ᐊ îñ ÑîţŕöÞåçķ ţö ŕéðûçé ţĥé šîžé öƒ ţĥé ƒîļéš ţĥåţ åŕé šéñţ ţö ţĥé бŕöŵšéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šéţ ᐅ`format=\"webp\"`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#format)ᐊ. one two three four five six seven eight nine ten eleven]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šéţ ᐅ`loading=\"lazy\"`ᐊ ƒöŕ öƒƒšçŕééñ îmåĝéš. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#loading)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šþéçîƒý ᐅ`preload`ᐊ ƒöŕ ĻÇÞ îmåĝé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#preload)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šþéçîƒý éxþļîçîţ ᐅ`width`ᐊ åñð ᐅ`height`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#width-height)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šéţ ţĥé åþþŕöþŕîåţé ᐅ`quality`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#quality)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "[Ûšé ţĥé ᐅ`nuxt/image`ᐊ çömþöñéñţ åñð šéţ ţĥé åþþŕöþŕîåţé ᐅ`sizes`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://image.nuxt.com/usage/nuxt-img#sizes)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[ᐅ[ᐊŔéþļåçé åñîmåţéð ĜÎFš ŵîţĥ vîðéöᐅ](https://web.dev/replace-gifs-with-videos/)ᐊ ƒöŕ ƒåšţéŕ ŵéб þåĝé ļöåðš åñð çöñšîðéŕ ûšîñĝ möðéŕñ ƒîļé ƒöŕmåţš šûçĥ åš ᐅ[ᐊŴéбMᐅ](https://web.dev/replace-gifs-with-videos/#create-webm-videos)ᐊ öŕ ᐅ[ᐊÅV1ᐅ](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)ᐊ ţö îmþŕövé çömþŕéššîöñ éƒƒîçîéñçý бý ĝŕéåţéŕ ţĥåñ 30% ovéŕ ţĥé çûŕŕéñţ šţåţé-öƒ-ţĥé-åŕţ vîðéö çöðéç, VÞ9. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "[Çöñšîðéŕ ûšîñĝ å ᐅ[ᐊþļûĝîñᐅ](https://octobercms.com/plugins?search=image)ᐊ öŕ šéŕvîçé ţĥåţ ŵîļļ åûţömåţîçåļļý çöñvéŕţ ţĥé ûþļöåðéð îmåĝéš ţö ţĥé öþţîmåļ ƒöŕmåţš. ᐅ[ᐊŴéбÞ ļöššļéšš îmåĝéšᐅ](https://developers.google.com/speed/webp)ᐊ åŕé 26% småļļéŕ îñ šîžé çömþåŕéð ţö ÞÑĜš åñð 25-34% småļļéŕ ţĥåñ çömþåŕåбļé ĴÞÉĜ îmåĝéš åţ ţĥé éqûîvåļéñţ ŠŠÎM qûåļîţý îñðéx. Åñöţĥéŕ ñéxţ-ĝéñ îmåĝé ƒöŕmåţ ţö çöñšîðéŕ îš ᐅ[ᐊÅVÎFᐅ](https://jakearchibald.com/2020/avif-has-landed/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "[Çöñšîðéŕ îñšţåļļîñĝ åñ ᐅ[ᐊîmåĝé ļåžý ļöåðîñĝ þļûĝîñᐅ](https://octobercms.com/plugins?search=lazy)ᐊ ţĥåţ þŕövîðéš ţĥé åбîļîţý ţö ðéƒéŕ åñý öƒƒšçŕééñ îmåĝéš, öŕ šŵîţçĥ ţö å ţĥémé ţĥåţ þŕövîðéš ţĥåţ ƒûñçţîöñåļîţý. Åļšö çöñšîðéŕ ûšîñĝ ᐅ[ᐊţĥé ÅMÞ þļûĝîñᐅ](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "[Ţĥéŕé åŕé måñý þļûĝîñš ţĥåţ ĥéļþ ᐅ[ᐊîñļîñé çŕîţîçåļ åššéţšᐅ](https://octobercms.com/plugins?search=css)ᐊ. Ţĥéšé þļûĝîñš måý бŕéåķ öţĥéŕ þļûĝîñš, šö ýöû šĥöûļð ţéšţ ţĥöŕöûĝĥļý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "[Ţĥéméš, þļûĝîñš åñð šéŕvéŕ šþéçîƒîçåţîöñš åļļ çöñţŕîбûţé ţö ţĥé šéŕvéŕ ŕéšþöñšé ţîmé. Çöñšîðéŕ ƒîñðîñĝ å möŕé öþţîmîžéð ţĥémé, çåŕéƒûļļý šéļéçţîñĝ åñ öþţîmîžåţîöñ þļûĝîñ åñð/öŕ ûþĝŕåðé ţĥé šéŕvéŕ. Öçţöбéŕ ÇMŠ åļšö åļļöŵš ðévéļöþéŕš ţö ûšé ᐅ[ᐊᐅ`Queues`ᐊᐅ](https://octobercms.com/docs/services/queues)ᐊ ţö ðéƒéŕ ţĥé þŕöçéššîñĝ öƒ å ţîmé çöñšûmîñĝ ţåšķ, šûçĥ åš šéñðîñĝ åñ é-måîļ. Ţĥîš ðŕåšţîçåļļý šþééðš ûþ ŵéб ŕéqûéšţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "[Çöñšîðéŕ šĥöŵîñĝ éxçéŕþţš îñ ţĥé þöšţ ļîšţš (é.ĝ. ûšîñĝ å ᐅ`show more`ᐊ бûţţöñ), ŕéðûçîñĝ ţĥé ñûmбéŕ öƒ þöšţš šĥöŵñ öñ å ĝîvéñ ŵéб þåĝé, бŕéåķîñĝ ļöñĝ þöšţš îñţö mûļţîþļé ŵéб þåĝéš, öŕ ûšîñĝ å þļûĝîñ ţö ļåžý-ļöåð çömméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "[Ţĥéŕé åŕé måñý ᐅ[ᐊþļûĝîñšᐅ](https://octobercms.com/plugins?search=css)ᐊ ţĥåţ çåñ šþééð ûþ å ŵéбšîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ åñð çömþŕéššîñĝ ţĥé šţýļéš. Ûšîñĝ å бûîļð þŕöçéšš ţö ðö ţĥîš mîñîƒîçåţîöñ ûþ-ƒŕöñţ çåñ šþééð ûþ ðévéļöþméñţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "[Ţĥéŕé åŕé måñý ᐅ[ᐊþļûĝîñšᐅ](https://octobercms.com/plugins?search=javascript)ᐊ ţĥåţ çåñ šþééð ûþ å ŵéбšîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ åñð çömþŕéššîñĝ ţĥé šçŕîþţš. Ûšîñĝ å бûîļð þŕöçéšš ţö ðö ţĥîš mîñîƒîçåţîöñ ûþ-ƒŕöñţ çåñ šþééð ûþ ðévéļöþméñţ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "[Çöñšîðéŕ ŕévîéŵîñĝ ţĥé ᐅ[ᐊþļûĝîñšᐅ](https://octobercms.com/plugins)ᐊ ļöåðîñĝ ûñûšéð ÇŠŠ öñ ţĥé ŵéбšîţé. Ţö îðéñţîƒý þļûĝîñš ţĥåţ åðð ûññéçéššåŕý ÇŠŠ, ŕûñ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Îðéñţîƒý ţĥé ţĥémé/þļûĝîñ ŕéšþöñšîбļé ƒŕöm ţĥé šţýļéšĥééţ ÛŔĻ. Ļööķ ƒöŕ þļûĝîñš ŵîţĥ måñý šţýļéšĥééţš ŵîţĥ ļöţš öƒ ŕéð îñ çöðé çövéŕåĝé. Å þļûĝîñ šĥöûļð öñļý åðð å šţýļéšĥééţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé ŵéб þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "[Çöñšîðéŕ ŕévîéŵîñĝ ţĥé ᐅ[ᐊþļûĝîñšᐅ](https://octobercms.com/plugins?search=javascript)ᐊ ţĥåţ ļöåð ûñûšéð ĴåvåŠçŕîþţ îñ ţĥé ŵéб þåĝé. Ţö îðéñţîƒý þļûĝîñš ţĥåţ åðð ûññéçéššåŕý ĴåvåŠçŕîþţ, ŕûñ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ᐊ îñ Çĥŕömé ÐévŢööļš. Îðéñţîƒý ţĥé ţĥémé/þļûĝîñ ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šçŕîþţ. Ļööķ ƒöŕ þļûĝîñš ŵîţĥ måñý šçŕîþţš ŵîţĥ ļöţš öƒ ŕéð îñ çöðé çövéŕåĝé. Å þļûĝîñ šĥöûļð öñļý åðð å šçŕîþţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé ŵéб þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "[Ŕéåð åбöûţ ᐅ[ᐊþŕévéñţîñĝ ûññéçéššåŕý ñéţŵöŕķ ŕéqûéšţš ŵîţĥ ţĥé ĤŢŢÞ Çåçĥéᐅ](https://web.dev/http-cache/#caching-checklist)ᐊ. Ţĥéŕé åŕé måñý ᐅ[ᐊþļûĝîñšᐅ](https://octobercms.com/plugins?search=Caching)ᐊ ţĥåţ çåñ бé ûšéð ţö šþééð ûþ çåçĥîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "[Çöñšîðéŕ ûšîñĝ åñ ᐅ[ᐊîmåĝé öþţîmîžåţîöñ þļûĝîñᐅ](https://octobercms.com/plugins?search=image)ᐊ ţö çömþŕéššéš îmåĝéš ŵĥîļé ŕéţåîñîñĝ ţĥé qûåļîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "[Ûþļöåð îmåĝéš ðîŕéçţļý îñ ţĥé méðîå måñåĝéŕ ţö éñšûŕé ţĥé ŕéqûîŕéð îmåĝé šîžéš åŕé åvåîļåбļé. Çöñšîðéŕ ûšîñĝ ţĥé ᐅ[ᐊŕéšîžé ƒîļţéŕᐅ](https://octobercms.com/docs/markup/filter-resize)ᐊ öŕ åñ ᐅ[ᐊîmåĝé ŕéšîžîñĝ þļûĝîñᐅ](https://octobercms.com/plugins?search=image)ᐊ ţö éñšûŕé ţĥé öþţîmåļ îmåĝé šîžéš åŕé ûšéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight]"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "[Éñåбļé ţéxţ çömþŕéššîöñ îñ ţĥé ŵéб šéŕvéŕ çöñƒîĝûŕåţîöñ. one two three four five six seven eight nine ten eleven]"}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "[Çöñšîðéŕ ûšîñĝ å \"ŵîñðöŵîñĝ\" ļîбŕåŕý ļîķé ᐅ`react-window`ᐊ ţö mîñîmîžé ţĥé ñûmбéŕ öƒ ÐÖM ñöðéš çŕéåţéð îƒ ýöû åŕé ŕéñðéŕîñĝ måñý ŕéþéåţéð éļéméñţš öñ ţĥé þåĝé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://web.dev/virtualize-long-lists-react-window/)ᐊ. Åļšö, mîñîmîžé ûññéçéššåŕý ŕé-ŕéñðéŕš ûšîñĝ ᐅ[ᐊᐅ`shouldComponentUpdate`ᐊᐅ](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action)ᐊ, ᐅ[ᐊᐅ`PureComponent`ᐊᐅ](https://reactjs.org/docs/react-api.html#reactpurecomponent)ᐊ, öŕ ᐅ[ᐊᐅ`React.memo`ᐊᐅ](https://reactjs.org/docs/react-api.html#reactmemo)ᐊ åñð ᐅ[ᐊšķîþ éƒƒéçţšᐅ](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects)ᐊ öñļý ûñţîļ çéŕţåîñ ðéþéñðéñçîéš ĥåvé çĥåñĝéð îƒ ýöû åŕé ûšîñĝ ţĥé ᐅ`Effect`ᐊ ĥööķ ţö îmþŕövé ŕûñţîmé þéŕƒöŕmåñçé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven]"}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "[Îƒ ýöû åŕé ûšîñĝ Ŕéåçţ Ŕöûţéŕ, mîñîmîžé ûšåĝé öƒ ţĥé ᐅ`<Redirect>`ᐊ çömþöñéñţ ƒöŕ ᐅ[ᐊŕöûţé ñåvîĝåţîöñšᐅ](https://reacttraining.com/react-router/web/api/Redirect)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "[Îƒ ýöû åŕé šéŕvéŕ-šîðé ŕéñðéŕîñĝ åñý Ŕéåçţ çömþöñéñţš, çöñšîðéŕ ûšîñĝ ᐅ`renderToPipeableStream()`ᐊ öŕ ᐅ`renderToStaticNodeStream()`ᐊ ţö åļļöŵ ţĥé çļîéñţ ţö ŕéçéîvé åñð ĥýðŕåţé ðîƒƒéŕéñţ þåŕţš öƒ ţĥé måŕķûþ îñšţéåð öƒ åļļ åţ öñçé. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "[Îƒ ýöûŕ бûîļð šýšţém mîñîƒîéš ÇŠŠ ƒîļéš åûţömåţîçåļļý, éñšûŕé ţĥåţ ýöû åŕé ðéþļöýîñĝ ţĥé þŕöðûçţîöñ бûîļð öƒ ýöûŕ åþþļîçåţîöñ. Ýöû çåñ çĥéçķ ţĥîš ŵîţĥ ţĥé Ŕéåçţ Ðévéļöþéŕ Ţööļš éxţéñšîöñ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "[Îƒ ýöûŕ бûîļð šýšţém mîñîƒîéš ĴŠ ƒîļéš åûţömåţîçåļļý, éñšûŕé ţĥåţ ýöû åŕé ðéþļöýîñĝ ţĥé þŕöðûçţîöñ бûîļð öƒ ýöûŕ åþþļîçåţîöñ. Ýöû çåñ çĥéçķ ţĥîš ŵîţĥ ţĥé Ŕéåçţ Ðévéļöþéŕ Ţööļš éxţéñšîöñ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "[Îƒ ýöû åŕé ñöţ šéŕvéŕ-šîðé ŕéñðéŕîñĝ, ᐅ[ᐊšþļîţ ýöûŕ ĴåvåŠçŕîþţ бûñðļéšᐅ](https://web.dev/code-splitting-suspense/)ᐊ ŵîţĥ ᐅ`React.lazy()`ᐊ. Öţĥéŕŵîšé, çöðé-šþļîţ ûšîñĝ å ţĥîŕð-þåŕţý ļîбŕåŕý šûçĥ åš ᐅ[ᐊļöåðåбļé-çömþöñéñţšᐅ](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "[Ûšé ţĥé Ŕéåçţ ÐévŢööļš Þŕöƒîļéŕ, ŵĥîçĥ måķéš ûšé öƒ ţĥé Þŕöƒîļéŕ ÅÞÎ, ţö méåšûŕé ţĥé ŕéñðéŕîñĝ þéŕƒöŕmåñçé öƒ ýöûŕ çömþöñéñţš. ᐅ[ᐊĻéåŕñ möŕé.ᐅ](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)ᐊ one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo]"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "[Þļåçé vîðéöš îñšîðé ᐅ`VideoBoxes`ᐊ, çûšţömîžé ţĥém ûšîñĝ ᐅ`Video Masks`ᐊ öŕ åðð ᐅ`Transparent Videos`ᐊ. ᐅ[ᐊĻéåŕñ möŕéᐅ](https://support.wix.com/en/article/wix-video-about-wix-video)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen]"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "[Ûþļöåð îmåĝéš ûšîñĝ ᐅ`Wix Media Manager`ᐊ ţö éñšûŕé ţĥéý åŕé åûţömåţîçåļļý šéŕvéð åš ŴéбÞ. Fîñð ᐅ[ᐊmöŕé ŵåýš ţö öþţîmîžéᐅ](https://support.wix.com/en/article/site-performance-optimizing-your-media)ᐊ ýöûŕ šîţé'š méðîå. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "[Ŵĥéñ ᐅ[ᐊåððîñĝ ţĥîŕð-þåŕţý çöðéᐅ](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site)ᐊ îñ ţĥé ᐅ`Custom Code`ᐊ ţåб öƒ ýöûŕ šîţé'š ðåšĥбöåŕð, måķé šûŕé îţ'š ðéƒéŕŕéð öŕ ļöåðéð åţ ţĥé éñð öƒ ţĥé çöðé бöðý. Ŵĥéŕé þöššîбļé, ûš<PERSON> Ŵîx’š ᐅ[ᐊîñţéĝŕåţîöñšᐅ](https://support.wix.com/en/article/about-marketing-integrations)ᐊ ţö émбéð måŕķéţîñĝ ţööļš öñ ýöûŕ šîţé.  one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine]"}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "[Ŵîx ûţîļîžéš ÇÐÑš åñð çåçĥîñĝ ţö šéŕvé ŕéšþöñšéš åš ƒåšţ åš þöššîбļé ƒöŕ möšţ vîšîţöŕš. Çöñšîðéŕ ᐅ[ᐊmåñûåļļý éñåбļîñĝ çåçĥîñĝᐅ](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed)ᐊ ƒöŕ ýöûŕ šîţé, éšþéçîåļļý îƒ ûšîñĝ ᐅ`Velo`ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "[Ŕévîéŵ åñý ţĥîŕð-þåŕţý çöðé ýöû'vé åððéð ţö ýöûŕ šîţé îñ ţĥé ᐅ`Custom Code`ᐊ ţåб öƒ ýöûŕ šîţé'š ðåšĥбöåŕð åñð öñļý ķééþ ţĥé šéŕvîçéš ţĥåţ åŕé ñéçéššåŕý ţö ýöûŕ šîţé. ᐅ[ᐊFîñð öûţ möŕéᐅ](https://support.wix.com/en/article/site-performance-removing-unused-javascript)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "[Çöñšîðéŕ ûþļöåðîñĝ ýöûŕ ĜÎF ţö å šéŕvîçé ŵĥîçĥ ŵîļļ måķé îţ åvåîļåбļé ţö émбéð åš åñ ĤŢMĻ5 vîðéö. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "[Çöñšîðéŕ ûšîñĝ ţĥé ᐅ[ᐊÞéŕƒöŕmåñçé Ļåбᐅ](https://wordpress.org/plugins/performance-lab/)ᐊ þļûĝîñ ţö åûţömåţîçåļļý çöñvéŕţ ýöûŕ ûþļöåðéð ĴÞÉĜ îmåĝéš îñţö ŴéбÞ, ŵĥéŕévéŕ šûþþöŕţéð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "[Îñšţåļļ å ᐅ[ᐊļåžý-ļöåð ŴöŕðÞŕéšš þļûĝîñᐅ](https://wordpress.org/plugins/search/lazy+load/)ᐊ ţĥåţ þŕövîðéš ţĥé åбîļîţý ţö ðéƒéŕ åñý öƒƒšçŕééñ îmåĝéš, öŕ šŵîţçĥ ţö å ţĥémé ţĥåţ þŕövîðéš ţĥåţ ƒûñçţîöñåļîţý. Åļšö çöñšîðéŕ ûšîñĝ ᐅ[ᐊţĥé ÅMÞ þļûĝîñᐅ](https://wordpress.org/plugins/amp/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "[Ţĥéŕé åŕé å ñûmбéŕ öƒ ŴöŕðÞŕéšš þļûĝîñš ţĥåţ çåñ ĥéļþ ýöû ᐅ[ᐊîñļîñé çŕîţîçåļ åššéţšᐅ](https://wordpress.org/plugins/search/critical+css/)ᐊ öŕ ᐅ[ᐊðéƒéŕ ļéšš îmþöŕţåñţ ŕéšöûŕçéšᐅ](https://wordpress.org/plugins/search/defer+css+javascript/)ᐊ. Бéŵåŕé ţĥåţ öþţîmîžåţîöñš þŕövîðéð бý ţĥéšé þļûĝîñš måý бŕéåķ ƒéåţûŕéš öƒ ýöûŕ ţĥémé öŕ þļûĝîñš, šö ýöû ŵîļļ ļîķéļý ñééð ţö måķé çöðé çĥåñĝéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "[Ţĥéméš, þļûĝîñš, åñð šéŕvéŕ šþéçîƒîçåţîöñš åļļ çöñţŕîбûţé ţö šéŕvéŕ ŕéšþöñšé ţîmé. Çöñšîðéŕ ƒîñðîñĝ å möŕé öþţîmîžéð ţĥémé, çåŕéƒûļļý šéļéçţîñĝ åñ öþţîmîžåţîöñ þļûĝîñ, åñð/öŕ ûþĝŕåðîñĝ ýöûŕ šéŕvéŕ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "[Çöñšîðéŕ šĥöŵîñĝ éxçéŕþţš îñ ýöûŕ þöšţ ļîš<PERSON>š (é.ĝ. vîå ţĥé möŕé ţåĝ), ŕéðûçîñĝ ţĥé ñûmбéŕ öƒ þöšţš šĥöŵñ öñ å ĝîvéñ þåĝé, бŕéåķîñĝ ýöûŕ ļöñĝ þöšţš îñţö mûļţîþļé þåĝéš, öŕ ûšîñĝ å þļûĝîñ ţö ļåžý-ļöåð çömméñţš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "[Å ñûmбéŕ öƒ ᐅ[ᐊŴöŕðÞŕéšš þļûĝîñšᐅ](https://wordpress.org/plugins/search/minify+css/)ᐊ çåñ šþééð ûþ ýöûŕ šîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ, åñð çömþŕéššîñĝ ýöûŕ šţýļéš. Ýöû måý åļšö ŵåñţ ţö ûšé å бûîļð þŕöçéšš ţö ðö ţĥîš mîñîƒîçåţîöñ ûþ-ƒŕöñţ îƒ þöššîбļé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "[Å ñûmбéŕ öƒ ᐅ[ᐊŴöŕðÞŕéšš þļûĝîñšᐅ](https://wordpress.org/plugins/search/minify+javascript/)ᐊ çåñ šþééð ûþ ýöûŕ šîţé бý çöñçåţéñåţîñĝ, mîñîƒýîñĝ, åñð çömþŕéššîñĝ ýöûŕ šçŕîþţš. Ýöû måý åļšö ŵåñţ ţö ûšé å бûîļð þŕöçéšš ţö ðö ţĥîš mîñîƒîçåţîöñ ûþ ƒŕöñţ îƒ þöššîбļé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ, öŕ šŵîţçĥîñĝ, ţĥé ñûmбéŕ öƒ ᐅ[ᐊŴöŕðÞŕéšš þļûĝîñšᐅ](https://wordpress.org/plugins/)ᐊ ļöåðîñĝ ûñûšéð ÇŠŠ îñ ýöûŕ þåĝé. Ţö îðéñţîƒý þļûĝîñš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ÇŠŠ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developer.chrome.com/docs/devtools/coverage/)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé ţĥémé/þļûĝîñ ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šţýļéšĥééţ. Ļööķ öûţ ƒöŕ þļûĝîñš ţĥåţ ĥåvé måñý šţýļéšĥééţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Å þļûĝîñ šĥöûļð öñļý éñqûéûé å šţýļéšĥééţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "[Çöñšîðéŕ ŕéðûçîñĝ, öŕ šŵîţçĥîñĝ, ţĥé ñûmбéŕ öƒ ᐅ[ᐊŴöŕðÞŕéšš þļûĝîñšᐅ](https://wordpress.org/plugins/)ᐊ ļöåðîñĝ ûñûšéð ĴåvåŠçŕîþţ îñ ýöûŕ þåĝé. Ţö îðéñţîƒý þļûĝîñš ţĥåţ åŕé åððîñĝ éxţŕåñéöûš ĴŠ, ţŕý ŕûññîñĝ ᐅ[ᐊçöðé çövéŕåĝéᐅ](https://developer.chrome.com/docs/devtools/coverage/)ᐊ îñ Çĥŕömé ÐévŢööļš. Ýöû çåñ îðéñţîƒý ţĥé ţĥémé/þļûĝîñ ŕéšþöñšîбļé ƒŕöm ţĥé ÛŔĻ öƒ ţĥé šçŕîþţ. Ļööķ öûţ ƒöŕ þļûĝîñš ţĥåţ ĥåvé måñý šçŕîþţš îñ ţĥé ļîšţ ŵĥîçĥ ĥåvé å ļöţ öƒ ŕéð îñ çöðé çövéŕåĝé. Å þļûĝîñ šĥöûļð öñļý éñqûéûé å šçŕîþţ îƒ îţ îš åçţûåļļý ûšéð öñ ţĥé þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "[Ŕéåð åбöûţ ᐅ[ᐊБŕöŵšéŕ Çåçĥîñĝ îñ ŴöŕðÞŕéššᐅ](https://wordpress.org/support/article/optimization/#browser-caching)ᐊ. one two three four five six seven eight nine ten]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "[Çöñšîðéŕ ûšîñĝ åñ ᐅ[ᐊîmåĝé öþţîmîžåţîöñ ŴöŕðÞŕéšš þļûĝîñᐅ](https://wordpress.org/plugins/search/optimize+images/)ᐊ ţĥåţ çömþŕéššéš ýöûŕ îmåĝéš ŵĥîļé ŕéţåîñîñĝ qûåļîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "[Ûþļöåð îmåĝéš ðîŕéçţļý ţĥŕöûĝĥ ţĥé ᐅ[ᐊméðîå ļîбŕåŕýᐅ](https://wordpress.org/support/article/media-library-screen/)ᐊ ţö éñšûŕé ţĥåţ ţĥé ŕéqûîŕéð îmåĝé šîžéš åŕé åvåîļåбļé, åñð ţĥéñ îñšéŕţ ţĥém ƒŕöm ţĥé méðîå ļîбŕåŕý öŕ ûšé ţĥé îmåĝé ŵîðĝéţ ţö éñšûŕé ţĥé öþţîmåļ îmåĝé šîžéš åŕé ûšéð (îñçļûðîñĝ ţĥöšé ƒöŕ ţĥé ŕéšþöñšîvé бŕéåķþöîñţš). Åvöîð ûšîñĝ ᐅ`Full Size`ᐊ îmåĝéš ûñļéšš ţĥé ðîméñšîöñš åŕé åðéqûåţé ƒöŕ ţĥéîŕ ûšåĝé. ᐅ[ᐊĻéåŕñ Möŕéᐅ](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)ᐊ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight thirtynine forty one two three four five]"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "[Ýöû çåñ éñåбļé ţéxţ çömþŕéššîöñ îñ ýöûŕ ŵéб šéŕvéŕ çöñƒîĝûŕåţîöñ. one two three four five six seven eight nine ten eleven twelve thirteen]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "[Éñåбļé 'Îmåĝîƒý' ƒŕöm ţĥé Îmåĝé Öþţîmîžåţîöñ ţåб îñ 'ŴÞ Ŕöçķéţ' ţö çöñvéŕţ ýöûŕ îmåĝéš ţö ŴéбÞ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "[Éñåб<PERSON><PERSON> ᐅ[ᐊĻåžýĻöåðᐅ](https://docs.wp-rocket.me/article/1141-lazyload-for-images)ᐊ îñ ŴÞ Ŕöçķéţ ţö ƒîx ţĥîš ŕéçömméñðåţîöñ. Ţĥîš ƒéåţûŕé ðéļåýš ţĥé ļöåðîñĝ öƒ ţĥé îmåĝéš ûñţîļ ţĥé vîšîţöŕ šçŕöļļš ðöŵñ ţĥé þåĝé åñð åçţûåļļý ñééðš ţö šéé ţĥém. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "[Éñåбļé ᐅ[ᐊŔémövé Ûñûšéð ÇŠŠᐅ](https://docs.wp-rocket.me/article/1529-remove-unused-css)ᐊ åñð ᐅ[ᐊĻöåð ĴåvåŠçŕîþţ ðéƒéŕŕéðᐅ](https://docs.wp-rocket.me/article/1265-load-javascript-deferred)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö åððŕéšš ţĥîš ŕéçömméñðåţîöñ. Ţĥéšé ƒéåţûŕéš ŵîļļ ŕéšþéçţîvéļý öþţîmîžé ţĥé ÇŠŠ åñð ĴåvåŠçŕîþţ ƒîļéš šö ţĥåţ ţĥéý ðöñ'ţ бļöçķ ţĥé ŕéñðéŕîñĝ öƒ ýöûŕ þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "[Éñåбļé ᐅ[ᐊMîñîƒý ÇŠŠ ƒîļéšᐅ](https://docs.wp-rocket.me/article/1350-css-minify-combine)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö ƒîx ţĥîš îššûé. Åñý šþåçéš åñð çömméñţš îñ ýöûŕ šîţé'š ÇŠŠ ƒîļéš ŵîļļ бé ŕémövéð ţö måķé ţĥé ƒîļé šîžé šmåļļéŕ åñð ƒåšţéŕ ţö ðöŵñļöåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "[Éñåбļé ᐅ[ᐊMîñîƒý ĴåvåŠçŕîþţ ƒîļéšᐅ](https://docs.wp-rocket.me/article/1351-javascript-minify-combine)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö ƒîx ţĥîš îššûé. Émþţý šþåçéš åñð çömméñţš ŵîļļ бé ŕémövéð ƒŕöm ĴåvåŠçŕîþţ ƒîļéš ţö måķé ţĥéîŕ šîžé šmåļļéŕ åñð ƒåšţéŕ ţö ðöŵñļöåð. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "[Éñåбļé ᐅ[ᐊŔémövé Ûñûšéð ÇŠŠᐅ](https://docs.wp-rocket.me/article/1529-remove-unused-css)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö ƒîx ţĥîš îššûé. Îţ ŕéðûçéš þåĝé šîžé бý ŕémövîñĝ åļļ ÇŠŠ åñð šţýļéšĥééţš ţĥåţ åŕé ñöţ ûšéð ŵĥîļé ķééþîñĝ öñļý ţĥé ûšéð ÇŠŠ ƒöŕ éåçĥ þåĝé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "[Éñåбļé ᐅ[ᐊÐéļåý ĴåvåŠçŕîþţ éxéçûţîöñᐅ](https://docs.wp-rocket.me/article/1349-delay-javascript-execution)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö ƒîx ţĥîš þŕöбļém. Îţ ŵîļļ îmþŕövé ţĥé ļöåðîñĝ öƒ ýöûŕ þåĝé бý ðéļåýîñĝ ţĥé éxéçûţîöñ öƒ šçŕîþţš ûñţîļ ûšéŕ îñţéŕåçţîöñ. Îƒ ýöûŕ šîţé ĥåš îƒŕåméš, ýöû çåñ ûšé ŴÞ Ŕöçķéţ'š ᐅ[ᐊĻåžýĻöåð ƒöŕ îƒŕåméš åñð vîðéöšᐅ](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)ᐊ åñð ᐅ[ᐊŔéþļåçé ÝöûŢûбé îƒŕåmé ŵîţĥ þŕévîéŵ îmåĝéᐅ](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)ᐊ åš ŵéļļ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo thirtythree thirtyfour thirtyfive thirtysix thirtyseven thirtyeight]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "[Éñåбļé 'Îmåĝîƒý' ƒŕöm ţĥé Îmåĝé Öþţîmîžåţîöñ ţåб îñ 'ŴÞ Ŕöçķéţ' åñð ŕûñ Бûļķ Öþţîmîžåţîöñ ţö çömþŕéšš ýöûŕ îmåĝéš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "[Ûšé ᐅ[ᐊÞŕéƒéţçĥ ÐÑŠ Ŕéqûéšţšᐅ](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)ᐊ îñ 'ŴÞ Ŕöçķéţ' ţö åðð \"ðñš-þŕéƒéţçĥ\" åñð šþééð ûþ ţĥé çöññéçţîöñ ŵîţĥ éxţéŕñåļ ðömåîñš. Åļšö, 'ŴÞ Ŕöçķéţ' åûţömåţîçåļļý åððš \"þŕéçöññéçţ\" ţö ᐅ[ᐊĜööĝļé Föñţš ðömåîñᐅ](https://docs.wp-rocket.me/article/1312-optimize-google-fonts)ᐊ åñð åñý ÇÑÅMÉ(Š) åððéð vîå ţĥé ᐅ[ᐊÉñåбļé ÇÐÑᐅ](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)ᐊ ƒéåţûŕé. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven twentyeight twentynine thirty thirtyone thirtytwo]"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "[Ţö ƒîx ţĥîš îššûé ƒöŕ ƒöñţš, é<PERSON><PERSON><PERSON>ļ<PERSON> ᐅ[ᐊŔémövé Ûñûšéð ÇŠŠᐅ](https://docs.wp-rocket.me/article/1529-remove-unused-css)ᐊ îñ 'ŴÞ Ŕöçķéţ'. Ýöûŕ šîţé'š çŕîţîçåļ ƒöñţš ŵîļļ бé þŕéļöåðéð ŵîţĥ þŕîöŕîţý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone]"}, "report/renderer/report-utils.js | calculatorLink": {"message": "[Šéé çåļçûļåţöŕ. one two]"}, "report/renderer/report-utils.js | collapseView": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vîéŵ one two]"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "[Îñî<PERSON><PERSON><PERSON><PERSON> Ñåvîĝåţîöñ one two three]"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "[Måxîmûm çŕîţîçåļ þåţĥ ļåţéñçý: one two three four five six seven]"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "[<PERSON><PERSON><PERSON><PERSON> ĴŠÖÑ one two]"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "[Ţöĝĝļé Ðåŕķ Ţĥémé one two three]"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "[Þŕîñţ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "[Þŕîñţ Šûmmåŕý one two]"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "[<PERSON><PERSON><PERSON><PERSON> Ĝîšţ one two]"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "[<PERSON><PERSON><PERSON><PERSON> ĤŢMĻ one two]"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "[<PERSON><PERSON><PERSON><PERSON> ĴŠÖÑ one two]"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "[Vîéŵ Ûñţĥŕöţţļéð Ţŕåçé one two three]"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "[Öþ<PERSON>ñ îñ Vîéŵéŕ one two]"}, "report/renderer/report-utils.js | errorLabel": {"message": "[Éŕŕöŕ¡ one]"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "[Ŕéþöŕţ éŕŕöŕ: ñö åûðîţ îñƒöŕmåţîöñ one two three four five six seven]"}, "report/renderer/report-utils.js | expandView": {"message": "[É<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> one two]"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "[1šţ þåŕţý one two]"}, "report/renderer/report-utils.js | footerIssue": {"message": "[<PERSON>î<PERSON><PERSON> åñ îššûé one two]"}, "report/renderer/report-utils.js | hide": {"message": "[Ĥîðé one]"}, "report/renderer/report-utils.js | labDataTitle": {"message": "[Ļåб Ðåţå one]"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[ᐅ[ᐊĻîĝĥţĥöûšéᐅ](https://developers.google.com/web/tools/lighthouse/)ᐊ åñåļýšîš öƒ ţĥé çûŕŕéñţ þåĝé öñ åñ émûļåţéð möбîļé ñéţŵöŕķ. Våļûéš åŕé éšţîmåţéð åñð måý våŕý. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen]"}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "[Åððîţî<PERSON><PERSON><PERSON><PERSON> îţémš ţö måñûåļļý çĥéçķ one two three four five six seven]"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "[<PERSON><PERSON><PERSON>þļ<PERSON><PERSON><PERSON><PERSON> one two]"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "[Öþéñ îñ å ñéŵ ţåб one two three four]"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "[Öþþöŕţûñîţý one two]"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "[Éš<PERSON>îmåţéð Šåvîñĝš one two three]"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> åûðîţš one two]"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "[Åš þéŕ ᐅ[ᐊÇĥŕömé’š ûþðåţéð Îñšţåļļåбîļîţý Çŕîţéŕîåᐅ](https://developer.chrome.com/blog/update-install-criteria)ᐊ, Ļîĝĥţĥöûšé ŵîļļ бé ðéþŕéçåţîñĝ ţĥé ÞŴÅ çåţéĝöŕý îñ å ƒûţûŕé ŕéļéåšé. Þļéåšé ŕéƒéŕ ţö ţĥé ᐅ[ᐊûþðåţéð ÞŴÅ ðöçûméñţåţîöñᐅ](https://developer.chrome.com/docs/devtools/progressive-web-apps/)ᐊ ƒöŕ ƒûţûŕé ÞŴÅ ţéšţîñĝ. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen eighteen nineteen twenty twentyone twentytwo twentythree twentyfour twentyfive twentysix twentyseven]"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "[Îñî<PERSON><PERSON><PERSON>ļ þåĝé ļöåð one two three]"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "[Þöîñţ-îñ-ţîmé šñåþšĥöţ one two three]"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "[Ûšéŕ îñţéŕåçţîöñš ţîméšþåñ one two three]"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "[Ç<PERSON>šţöm ţĥŕöţţļîñĝ one two three]"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "[Émû<PERSON><PERSON>ţ<PERSON>ð Ðéšķţöþ one two]"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "[É<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ð Möţö Ĝ Þöŵéŕ one two three four five]"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "[Ñö émûļåţîöñ one two]"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "[Åx<PERSON> véŕšîöñ one two]"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "[Ûñţĥŕöţţļéð ÇÞÛ/Mémöŕý Þöŵéŕ one two three]"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "[ÇÞÛ ţĥŕöţţļîñĝ one two]"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "[Ðévîçé one]"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "[Ñéţŵöŕķ ţĥŕöţţļîñĝ one two three]"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "[Šçŕééñ émûļåţîöñ one two]"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "[Ûšéŕ åĝéñţ (ñéţŵöŕķ) one two three]"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "[Šîñĝļé þåĝé šéššîöñ one two three]"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "[Ţĥîš ðåţå îš ţåķéñ ƒŕöm å šîñĝļé þåĝé šéššîöñ, åš öþþöšéð ţö ƒîéļð ðåţå šûmmåŕîžîñĝ måñý šéššîöñš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "[Šļö<PERSON> 4Ĝ ţĥŕöţţļîñĝ one two three]"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "[Ûñķñöŵñ one]"}, "report/renderer/report-utils.js | show": {"message": "[Šĥöŵ one]"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "[Šĥöŵ åûðîţš ŕéļévåñţ ţö: one two three four five]"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "[Çö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šñîþþéţ one two]"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "[Éxþå<PERSON>ð šñîþþéţ one two]"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "[Šĥöŵ 3ŕð-þåŕţý ŕéšöûŕçéš one two three]"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "[Þŕövîðéð бý éñvîŕöñméñţ one two three]"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "[Ţĥéŕé ŵéŕé îššûéš åƒƒéçţîñĝ ţĥîš ŕûñ öƒ Ļîĝĥţĥöûšé: one two three four five six seven eight nine ten eleven]"}, "report/renderer/report-utils.js | unattributable": {"message": "[Ûñåţţŕîбûţåбļé one two]"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "[Våļûéš åŕé éšţîmåţéð åñð måý våŕý. Ţĥé ᐅ[ᐊþéŕƒöŕmåñçé šçöŕé îš çåļçûļåţéðᐅ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)ᐊ ðîŕéçţļý ƒŕöm ţĥéšé méţŕîçš. one two three four five six seven eight nine ten eleven twelve thirteen fourteen fifteen sixteen seventeen]"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "[Vîéŵ Ţŕåçé one two]"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "[Vîéŵ Ţŕéémåþ one two]"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "[Þ<PERSON><PERSON><PERSON><PERSON><PERSON> åûðîţš бûţ ŵîţĥ ŵåŕñîñĝš one two three four five six seven]"}, "report/renderer/report-utils.js | warningHeader": {"message": "[Ŵåŕñîñĝš:  one two]"}, "treemap/app/src/util.js | allLabel": {"message": "[<PERSON><PERSON><PERSON> one]"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "[<PERSON><PERSON><PERSON> Šçŕîþţš one two]"}, "treemap/app/src/util.js | coverageColumnName": {"message": "[Çövéŕåĝé one]"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "[Ðûþļîçåţé <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> one two three]"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "[Ŕéšöûŕçé <PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "treemap/app/src/util.js | tableColumnName": {"message": "[Ñåmé one]"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "[Ţöĝĝļé <PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "[Ûñûšéð <PERSON><PERSON><PERSON><PERSON><PERSON> one two]"}}