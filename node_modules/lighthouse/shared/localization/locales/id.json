{"core/audits/accessibility/accesskeys.js | description": {"message": "Kunci akses memungkinkan pengguna memfokuskan bagian halaman dengan cepat. Untuk navigasi yang tepat, setiap kunci akses harus unik. [<PERSON><PERSON>jari lebih lanjut kunci akses](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON> `[accesskey]` tidak unik."}, "core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON> `[accesskey]` bersifat unik"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> `role` ARIA mendukung subset tertentu dari atribut `aria-*`. Membatalkan pencocokan ini akan membuat atribut `aria-*` menjadi tidak valid. [Pelajari cara mencocokkan atribut ARIA dengan perannya](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atribut `[aria-*]` tidak cocok dengan perannya"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atribut `[aria-*]` cocok dengan perannya"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "`role` ARIA memungkinkan teknologi pendukung mengetahui peran setiap elemen di halaman web. <PERSON><PERSON> nilai `role` salah eja, bukan nilai `role` <PERSON> yang sudah ada, atau peran abstrak, maka tujuan elemen tersebut tidak akan disampaikan kepada pengguna teknologi pendukung. [Pelajari lebih lanjut peran ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "<PERSON><PERSON> yang ditetapkan untuk `role=\"\"` bukan peran ARIA yang valid."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "<PERSON><PERSON> yang ditetapkan untuk `role=\"\"` adalah peran ARIA yang valid."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Jika elemen tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari cara membuat elemen perintah agar lebih mudah diakses](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elemen `button`, `link`, dan `menuitem` tidak memiliki nama yang dapat diakses."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elemen `button`, `link`, dan `menuitem` memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Elemen \"dialog\" ARIA tanpa label aksesibilitas dapat membuat pengguna pembaca layar tidak memahami fungsi elemen ini. [Pelajari cara membuat elemen \"dialog\" ARIA yang lebih mudah diakses](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> den<PERSON> `role=\"dialog\"` atau `role=\"alertdialog\"` tidak memiliki label aksesibilitas."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "<PERSON><PERSON><PERSON> den<PERSON> `role=\"dialog\"` atau `role=\"alertdialog\"` memiliki label aksesibilitas."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Teknologi pendukung seperti pembaca layar tidak berfungsi secara konsisten jika `aria-hidden=\"true\"` disetel pada`<body>` dokumen. [Pelajari cara `aria-hidden` memengaruhi isi dokumen](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` tersedia di `<body>` dokumen"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` tidak tersedia di `<body>` dokumen"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "<PERSON><PERSON><PERSON> yang dapat difokuskan dalam elemen `[aria-hidden=\"true\"]` mencegah elemen interaktif tersebut tersedia bagi pengguna teknologi pendukung seperti pembaca layar. [Pelajari cara `aria-hidden` memengaruhi elemen yang dapat difokuskan](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elemen `[aria-hidden=\"true\"]` memuat turunan yang dapat difokuskan"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elemen `[aria-hidden=\"true\"]` tidak memuat turunan yang dapat difokuskan"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Jika kolom input tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari lebih lanjut label kolom input](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ma<PERSON>kan <PERSON> tidak memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "<PERSON><PERSON><PERSON> masukan <PERSON> memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Jika elemen pengukur tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari cara memberi nama elemen `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Elemen `meter` ARIA tidak memiliki nama yang dapat diakses."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Elemen `meter` ARIA memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Jika elemen `progressbar` tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari cara memberi label pada elemen `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Elemen `progressbar` ARIA tidak memiliki nama yang dapat diakses."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Elemen `progressbar` ARIA memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Beberapa peran ARIA memiliki atribut wajib yang menjelaskan status elemen ke pembaca layar. [<PERSON><PERSON>jari lebih lanjut peran dan atribut yang diperlukan](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` tidak memiliki semua atribut `[aria-*]` yang dip<PERSON>an"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` memiliki semua atribut `[aria-*]` yang dip<PERSON>an"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Beberapa peran induk ARIA harus memuat peran turunan tertentu agar dapat menjalankan fungsi aksesibilitas yang diinginkan. [Pelajari lebih lanjut peran dan elemen turunan yang diperlukan](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> dengan `[role]` ARIA yang mewajibkan turunannya berisi `[role]` te<PERSON>tu tidak memiliki beberapa atau semua turunan yang dip<PERSON>an."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> dengan `[role]` ARIA yang mewajibkan turunannya berisi `[role]` tertentu memiliki semua turunan yang diperlukan."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Beberapa peran turunan ARIA harus dimuat oleh peran induk tertentu agar dapat menjalankan fungsi aksesibilitas yang diinginkan dengan tepat. [Pelajari lebih lanjut peran ARIA dan elemen induk yang diperlukan](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` tidak dimuat oleh elemen induk wajibnya"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` dimuat oleh elemen induk wajibnya"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Peran ARIA harus memiliki nilai yang valid agar dapat menjalankan fungsi aksesibilitas yang diinginkan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut peran ARIA yang valid](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON> `[role]` tidak valid"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON> `[role]` valid"}, "core/audits/accessibility/aria-text.js | description": {"message": "Menambahkan `role=text` di sekitar node teks yang dipisahkan oleh markup memungkinkan VoiceOver memperlakukannya sebagai satu frasa, tetapi turunan elemen yang dapat difokuskan tidak akan diucapkan. [Pelajari atribut `role=text` lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "<PERSON><PERSON>en dengan atribut `role=text` memiliki turunan yang dapat difokuskan."}, "core/audits/accessibility/aria-text.js | title": {"message": "<PERSON><PERSON>en dengan atribut `role=text` tidak memiliki turunan yang dapat difokuskan."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Jika kolom tombol tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari lebih lanjut kolom tombol](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> be<PERSON> tidak memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "<PERSON><PERSON><PERSON> be<PERSON> memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Jika elemen tooltip tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari cara memberi nama elemen `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Elemen `tooltip` ARIA tidak memiliki nama yang dapat diakses."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Elemen `tooltip` ARIA memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Jika elemen `treeitem` tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya dengan nama umum, sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari lebih lanjut cara memberi label pada elemen `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Elemen `treeitem` ARIA tidak memiliki nama yang dapat diakses."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Elemen `treeitem` ARIA memiliki nama yang dapat diakses"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Teknologi penduku<PERSON>, se<PERSON>i pem<PERSON> layar, tidak dapat menafsirkan atribut ARIA dengan nilai yang tidak valid. [Pelajari lebih lanjut nilai yang valid untuk atribut ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atribut `[aria-*]` tidak memiliki nilai yang valid"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atribut `[aria-*]` memiliki nilai yang valid"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Teknologi penduku<PERSON>, se<PERSON>i pem<PERSON> layar, tidak dapat menafsirkan atribut ARIA dengan nama yang tidak valid. [Pelajari lebih lanjut atribut ARIA yang valid](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atribut `[aria-*]` tidak valid atau salah eja"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atribut `[aria-*]` valid dan tidak salah eja"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "core/audits/accessibility/button-name.js | description": {"message": "Jika tombol tidak memiliki label aksesibilitas, pembaca layar akan mengucapkannya sebagai \"tombol\", sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari cara membuat tombol lebih mudah diakses](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Tombol tidak memiliki nama yang dapat diakses"}, "core/audits/accessibility/button-name.js | title": {"message": "Tombol memiliki nama yang dapat diakses"}, "core/audits/accessibility/bypass.js | description": {"message": "Dengan menambahkan cara untuk mengabaikan konten be<PERSON>lang, pengguna keyboard dapat membuka halaman dengan lebih efisien. [<PERSON><PERSON><PERSON>i lebih lanjut cara mengabaikan blok konten](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Halaman ini tidak memuat judul, link le<PERSON>, atau wilayah landmark"}, "core/audits/accessibility/bypass.js | title": {"message": "Halaman ini memuat judul, link le<PERSON>, atau wilayah landmark"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Teks yang memiliki kontras rendah akan sulit atau tidak mungkin dibaca oleh kebanyakan pengguna. [Pelajari cara memberikan kontras warna yang memadai](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON>na latar belakang dan latar depan tidak memiliki rasio kontras yang cukup."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>na latar belakang dan latar depan memiliki rasio kontras yang cukup"}, "core/audits/accessibility/definition-list.js | description": {"message": "Jika daftar definisi tidak di-markup dengan tepat, pembaca layar dapat menghasilkan output yang membingungkan atau tidak akurat. [Pelajari cara menyusun daftar definisi dengan benar](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` tidak hanya memuat grup `<dt>` dan `<dd>`, `<script>`, `<template>`, atau elemen `<div>` yang diurutkan dengan tepat."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` hanya memuat grup `<dt>` dan `<dd>`, `<script>`, `<template>`, atau elemen `<div>` yang diurutkan dengan tepat."}, "core/audits/accessibility/dlitem.js | description": {"message": "Item daftar definisi (`<dt>` dan `<dd>`) harus tergabung dalam elemen `<dl>` induk untuk memastikan bahwa pembaca layar dapat mengucapkannya dengan tepat. [Pelajari cara menyusun daftar definisi dengan benar](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Item daftar definisi tidak tergabung dalam elemen `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Item daftar definisi tergabung dalam elemen `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "<PERSON><PERSON>l ini menunjukkan ringkasan halaman kepada pengguna pembaca layar, dan pengguna mesin telusur sangat mengandalkan judul tersebut untuk menentukan apakah halaman relevan dengan penelusurannya atau tidak. [Pelajari lebih lanjut judul dokumen](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumen tidak memiliki elemen `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumen memiliki elemen `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Semua elemen yang dapat difokuskan harus memiliki `id` unik agar dapat dilihat oleh teknologi pendukung. [Pelajari cara memperbaiki `id` duplikat](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atribut `[id]` pada elemen aktif yang dapat difokuskan tidak unik"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atribut `[id]` pada elemen aktif yang dapat difokuskan unik"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Nilai ID ARIA harus unik untuk mencegah instance lain terabaikan oleh teknologi pendukung. [Pelajari cara memperbaiki ID ARIA duplikat](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ID ARIA tidak unik"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ID ARIA unik"}, "core/audits/accessibility/empty-heading.js | description": {"message": "<PERSON><PERSON><PERSON> tanpa konten atau teks yang tidak dapat diakses membuat pengguna pembaca layar tidak dapat mengakses informasi di struktur halaman. [Pelajari judul lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Elemen \"heading\" tidak berisi konten."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Se<PERSON>a elemen \"heading\" be<PERSON>i konten."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Teknologi pendukung seperti pembaca layar yang menggunakan label pertama, terakhir, atau semua label mungkin akan salah mengucapkan kolom formulir yang memiliki beberapa label. [Pelajari cara menggunakan label formulir](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Kolom formulir memiliki beberapa label"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Tidak ada kolom formulir yang memiliki beberapa label"}, "core/audits/accessibility/frame-title.js | description": {"message": "Pengguna pembaca layar mengandalkan judul bingkai untuk menjelaskan isi bingkai. [Pelajari lebih lanjut judul bingkai](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elemen `<frame>` atau `<iframe>` tidak memiliki judul"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elemen `<frame>` atau `<iframe>` memiliki judul"}, "core/audits/accessibility/heading-order.js | description": {"message": "<PERSON><PERSON><PERSON> yang diurutkan dengan benar yang tidak melewati level akan menyampaikan struktur semantik halaman, membuatnya lebih mudah dilihat dan dipahami ketika menggunakan teknologi pendukung. [Pelajari lebih lanjut urutan judul](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elemen judul muncul tidak dalam urutan menurun yang berurutan"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elemen judul muncul dalam urutan menurun yang berurutan"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Jika halaman tidak menentukan atribut `lang`, pembaca layar akan mengasumsikan bahwa halaman menggunakan bahasa default yang dipilih pengguna saat menyiapkan pembaca layar. Jika halaman tidak dalam bahasa default, pembaca layar mungkin tidak dapat mengucapkan teks di halaman tersebut dengan benar. [Pelajari lebih atribut lanjut `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elemen `<html>` tidak memiliki atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Elemen `<html>` memiliki atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Menentukan [bahasa BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) yang valid akan membantu pembaca layar mengucapkan teks dengan tepat. [Pelajari cara menggunakan atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elemen `<html>` tidak memiliki nilai yang valid untuk atribut `[lang]`-nya."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Elemen `<html>` memiliki nilai yang valid untuk atribut `[lang]`-nya"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Jika halaman tidak menentukan bahasa yang konsisten, pembaca layar mungkin tidak dapat membacakan teks di halaman tersebut dengan benar. [Pelajari atribut lebih lanjut `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Elemen `<html>` tidak memiliki atribut `[xml:lang]` dengan bahasa dasar yang sama dengan atribut `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Elemen `<html>` memiliki atribut `[xml:lang]` dengan bahasa dasar yang sama dengan atribut `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Link dengan tujuan yang sama harus memiliki deskripsi yang sama, untuk membantu pengguna memahami tujuan link dan memutuskan apakah akan mengikutinya atau tidak. [Pelajari tentang link yang identik lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Link yang identik tidak memiliki fungsi yang sama."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Link yang identik memiliki fungsi yang sama."}, "core/audits/accessibility/image-alt.js | description": {"message": "Elemen informatif harus memberikan teks alternatif yang singkat dan deskriptif. Elemen dekoratif dapat diabaikan dengan atribut alt kosong. [Pelajari atribut lebih lanjut `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elemen gambar tidak memiliki atribut `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elemen halaman memiliki atribut `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Elemen informatif harus memberikan teks alternatif yang singkat dan deskriptif. Teks alternatif yang sama persis dengan teks di samping link atau gambar berpotensi membingungkan pengguna pembaca layar, karena teks akan dibaca dua kali. [Pelajari atribut lebih lanjut `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Elemen gambar memiliki atribut `[alt]` yang merupakan teks berlebihan."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Elemen gambar tidak memiliki atribut `[alt]` yang merupakan teks berlebihan."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Menambahkan teks yang jelas dan mudah diakses ke tombol input dapat membantu pengguna pembaca layar memahami tujuan tombol input. [Pelajari tombol input lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Tombol input tidak memiliki teks yang jelas."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Tombol input memiliki teks yang jelas."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Saat suatu gambar digunakan sebagai tombol `<input>`, penyediaan teks alternatif dapat membantu pengguna pembaca layar memahami fungsi tombol tersebut. [Pelajari teks alternatif gambar input](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elemen `<input type=\"image\">` tidak memiliki teks `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elemen `<input type=\"image\">` memiliki teks `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Label teks yang terlihat dan tidak cocok dengan label aksesibilitas dapat menyebabkan pengalaman yang membingungkan bagi pengguna pembaca layar. [Pelajari lebih lanjut label aksesibilitas](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Elemen dengan label teks yang terlihat tidak memiliki label aksesibilitas yang cocok."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Elemen dengan label teks yang terlihat memiliki label aksesibilitas yang cocok."}, "core/audits/accessibility/label.js | description": {"message": "Label memastikan bahwa kontrol bentuk diucapkan dengan tepat oleh teknologi pendukung, seperti pembaca layar. [Pelajari lebih lanjut label elemen formulir](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Elemen formulir tidak memiliki label yang terkait"}, "core/audits/accessibility/label.js | title": {"message": "Elemen formulir memiliki label yang terkait"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "<PERSON>tu penanda utama membantu pengguna pembaca layar membuka halaman web. [Pelajari penanda lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokumen tidak memiliki penanda utama."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokumen memiliki penanda utama."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Teks yang memiliki kontras rendah akan sulit atau tidak mungkin dibaca oleh kebanyakan pengguna. Teks link yang jelas meningkatkan pengalaman bagi pengguna dengan gangguan penglihatan. [Pelajari cara membuat link yang mudah dibedakan](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "<PERSON> bergantung pada warna agar dapat dibedakan."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "<PERSON> dapat dibedakan tanpa bergantung pada warna."}, "core/audits/accessibility/link-name.js | description": {"message": "Teks link (dan teks alternatif untuk gambar, saat digunakan sebagai link) yang mudah dilihat, unik, dan dapat difokuskan akan meningkatkan kualitas pengalaman navigasi bagi pengguna pembaca layar. [Pelajari cara membuat link dapat diakses](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Link tidak memiliki nama yang dapat dikenali"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON> memiliki nama yang dapat dikenali"}, "core/audits/accessibility/list.js | description": {"message": "Pembaca layar memiliki cara tertentu untuk membacakan daftar. Memastikan struktur daftar dengan tepat akan membantu output pembaca layar. [Pelajari lebih lanjut struktur daftar yang tepat](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Daftar tidak hanya berisi elemen `<li>` dan skrip yang mendukung elemen (`<script>` dan `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Daftar hanya memuat elemen `<li>` dan skrip yang mendukung elemen (`<script>` dan `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Pembaca layar mengharuskan item daftar (`<li>`) untuk dimuat dalam `<ul>`, `<ol>`, atau `<menu>` induk agar dapat diucapkan dengan tepat. [Pelajari lebih lanjut struktur daftar yang tepat](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Daftar item (`<li>`) tidak dimuat dalam elemen induk `<ul>`, `<ol>`, atau `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Item daftar (`<li>`) dimuat dalam elemen induk `<ul>`, `<ol>`, atau `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Pengguna tidak mengharapkan halaman dimuat ulang secara otomatis, karena tindakan tersebut akan memindahkan fokus kembali ke bagian atas halaman. Hal ini dapat menimbulkan pengalaman yang menjengkelkan atau membingungkan. [Pelajari lebih lanjut tag meta refresh](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Do<PERSON>men menggunakan `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumen tidak menggunakan `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Menonaktifkan zoom akan menimbulkan masalah bagi pengguna dengan gangguan penglihatan yang mengandalkan pembesaran layar untuk melihat konten halaman dengan baik. [Pelajari lebih lanjut tag meta viewport](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` digunakan dalam elemen `<meta name=\"viewport\">` atau atribut `[maximum-scale]` kurang dari 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` tidak digunakan dalam elemen `<meta name=\"viewport\">` dan atribut `[maximum-scale]` tidak kurang dari 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Pembaca layar tidak dapat menerjemahkan konten yang bukan teks. Menambahkan teks alternatif ke elemen `<object>` akan membantu pembaca layar menyampaikan makna kepada pengguna. [Pelajari lebih lanjut teks alternatif untuk elemen `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elemen `<object>` tidak memiliki teks alternatif"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elemen `<object>` memiliki teks alternatif"}, "core/audits/accessibility/select-name.js | description": {"message": "Elemen \"form\" tanpa label yang efektif dapat menciptakan pengalaman yang membingungkan bagi pengguna pembaca layar. [Pelajari elemen `select` lebih lanjut ](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Elemen \"select\" tidak memiliki elemen label terkait."}, "core/audits/accessibility/select-name.js | title": {"message": "Elemen \"select\" memiliki elemen label terkait."}, "core/audits/accessibility/skip-link.js | description": {"message": "Menyertakan link le<PERSON> dapat membantu pengguna melewati konten utama untuk menghemat waktu. [Pelajari lebih lanjut link lewati](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "<PERSON> tidak dapat difokuskan."}, "core/audits/accessibility/skip-link.js | title": {"message": "<PERSON> dapat difokuskan."}, "core/audits/accessibility/tabindex.js | description": {"message": "<PERSON><PERSON> yang lebih besar dari 0 menunjukkan pengurutan navigasi eksplisit. Walaupun secara teknis valid, hal ini sering menciptakan pengalaman yang membingungkan bagi pengguna yang mengandalkan teknologi pendukung. [Pelajari atribut lebih lanjut `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Beberapa elemen memiliki nilai `[tabindex]` yang lebih besar dari 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Tidak ada elemen yang memiliki nilai `[tabindex]` lebih besar dari 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Atribut ringkasan harus mendeskripsikan struktur tabel, sedangkan `<caption>` harus memiliki judul di layar. Markup tabel yang akurat akan membantu pengguna pembaca layar. [<PERSON><PERSON><PERSON>i lebih lanjut ringkasan dan teks](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Tabel memiliki konten yang sama di atribut ringkasan dan `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Tabel memiliki konten yang berbeda di atribut ringkasan dan `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan tabel menggunakan elemen teks yang sebenarnya, bukan sel dengan atribut `[colspan]`, dapat meningkatkan pengalaman bagi pengguna pembaca layar. [Pelajari teks lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Tabel tidak menggunakan `<caption>`, bukan sel dengan atribut `[colspan]`, untuk menunjukkan teks."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "<PERSON><PERSON> mengguna<PERSON> `<caption>`, bukan sel dengan atribut `[colspan]` untuk menunjukkan teks."}, "core/audits/accessibility/target-size.js | description": {"message": "Target sentuh dengan ukuran dan spasi yang memadai membantu pengguna yang kesulitan mengetuk kontrol kecil untuk mengaktifkan target. [Pelajari target sentuh lebih lanjut](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Target sentuh tidak memiliki ukuran atau spasi yang memadai."}, "core/audits/accessibility/target-size.js | title": {"message": "Target sentuh memiliki ukuran dan spasi yang memadai."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan elemen `<td>` dalam tabel besar (lebar atau tinggi 3 sel atau lebih) memiliki header tabel terkait dapat meningkatkan kualitas pengalaman pengguna pembaca layar. [Pelajari lebih lanjut header tabel](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Elemen `<td>` dalam `<table>` besar tidak memiliki header tabel."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Elemen `<td>` dalam `<table>` besar memiliki satu atau beberapa header tabel."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan sel `<td>` yang menggunakan atribut `[headers]` hanya merujuk ke sel lain dalam tabel yang sama dapat meningkatkan kualitas pengalaman bagi pengguna pembaca layar. [Pelajari lebih lanjut atribut `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Sel di elemen `<table>` yang menggunakan atribut `[headers]` yang merujuk pada elemen `id` tidak ditemukan dalam tabel yang sama."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Sel dalam elemen `<table>` yang menggunakan atribut `[headers]` merujuk pada sel tabel dalam tabel yang sama."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan header tabel selalu merujuk ke sekumpulan sel dapat meningkatkan kualitas pengalaman bagi pengguna pembaca layar. [Pelajari lebih lanjut header tabel](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elemen `<th>` dan elemen dengan `[role=\"columnheader\"/\"rowheader\"]` tidak memiliki sel data yang dideskripsikannya."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elemen `<th>` dan elemen dengan `[role=\"columnheader\"/\"rowheader\"]` memiliki sel data yang didesk<PERSON>."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Menentukan [bahasa BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) yang valid pada elemen membantu memastikan bahwa teks diucapkan dengan benar oleh pembaca layar. [Pelajari cara menggunakan atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atribut `[lang]` tidak memiliki nilai yang valid"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atribut `[lang]` memiliki nilai yang valid"}, "core/audits/accessibility/video-caption.js | description": {"message": "Video yang menyediakan teks akan memudahkan pengguna yang menyandang gangguan pendengaran dan tunarungu untuk mengakses informasinya. [P<PERSON>jari lebih lanjut teks video](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elemen `<video>` tidak memuat elemen `<track>` dengan `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elemen `<video>` memuat elemen `<track>` dengan `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON>"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Token yang <PERSON>"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` membantu pengguna mengirim formulir lebih cepat. Untuk mempermudah pengguna, sebaiknya lakukan pengaktifan dengan menyetel atribut `autocomplete` ke nilai yang valid. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut `autocomplete` dalam formulir](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Elemen `<input>` tidak memiliki atribut `autocomplete` yang benar"}, "core/audits/autocomplete.js | manualReview": {"message": "Memerlukan tinjauan manual"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Tinjau urutan token"}, "core/audits/autocomplete.js | title": {"message": "Elemen `<input>` menggunakan `autocomplete` dengan benar"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Token `autocomplete`: \"{token}\" tidak valid dalam {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Tinjau urutan token: \"{tokens}\" di {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | description": {"message": "Banyak navigasi dilakukan dengan kembali ke halaman sebelumnya, atau ke halaman berikutnya lagi. Back-forward cache (bfcache) dapat mempercepat navigasi kembali ini. [Pelajari bfcache lebih lanjut](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 alasan kegagalan}other{# alasan kegagalan}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Alasan kegagalan"}, "core/audits/bf-cache.js | failureTitle": {"message": "Halaman mencegah pemulihan back-forward cache"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Tidak dapat di<PERSON>ti"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Dukungan browser tertunda"}, "core/audits/bf-cache.js | title": {"message": "Halaman tidak mencegah pemulihan back-forward cache"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Back-forward cache tidak dapat diuji di Chrome Headless versi lama (`--chrome-flags=\"--headless=old\"`). Untuk melihat hasil audit, gunakan Chrome Headless (`--chrome-flags=\"--headless=new\"`) baru atau Chrome standar."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Ekstensi Chrome berpengaruh negatif terhadap performa pemuatan halaman ini. Coba audit halaman dalam mode samaran atau dari profil Chrome tanpa ekstensi."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Waktu CPU Total"}, "core/audits/bootup-time.js | description": {"message": "Sebaiknya kurangi waktu yang dihabiskan untuk mengurai, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan mengeksekusi JS. Coba kirim payload JS yang lebih kecil untuk membantu mengurangi waktu. [Pelajari cara mengurangi waktu eksekusi JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Mengurangi waktu eksekusi JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Waktu eksekusi JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Menghapus modul JavaScript duplikat yang besar dari paket untuk mengurangi byte tidak perlu yang digunakan oleh aktivitas jaringan. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Hapus modul duplikat di paket JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF berukuran besar tidak efisien untuk menayangkan konten animasi. Sebaiknya gunakan video MPEG4/WebM sebagai animasi dan PNG/WebP sebagai gambar statis untuk menggantikan GIF guna menghemat byte jaringan. [Pelajari lebih lanjut format video yang efisien](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Gunakan format video untuk konten animasi"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill dan transformasi memungkinkan browser lama menggunakan fitur JavaScript yang baru. Namun, banyak di antara fitur tersebut yang tidak diperlukan browser modern. Untuk paket JavaScript Anda, terapkan strategi deployment skrip modern menggunakan deteksi fitur modul/nomodule untuk mengurangi jumlah kode yang dikirimkan ke browser modern, sambil terus menyediakan dukungan untuk browser lama. [Pelajari cara menggunakan JavaScript modern](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Hindari penayangan JavaScript lama di browser modern"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Format gambar seperti WebP dan AVIF sering kali memberikan kompresi yang lebih baik daripada PNG atau JPEG, yang berarti proses download lebih cepat dan konsumsi data lebih sedikit. [Pelajari lebih lanjut format gambar modern](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Tayangkan gambar dalam format generasi berikutnya"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Sebaiknya lakukan pemuatan lambat di balik layar dan gambar tersembunyi setelah semua resource penting selesai dimuat guna mengurangi waktu untuk interaktif. [Pelajari cara menunda gambar di balik layar](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON>nda gambar di balik layar"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resource memblokir first paint halaman. Sebaiknya kirim inline JS/CSS penting dan tunda semua JS/gaya yang tidak penting. [Pelajari cara menghilangkan resource yang memblokir rendering](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Hilangkan resource yang memblokir render"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Payload jaringan yang besar menimbulkan biaya yang tinggi bagi pengguna dan berkorelasi erat dengan waktu pemuatan yang lama. [Pelajari cara mengurangi ukuran payload](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Ukuran total adalah {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Menghindari payload jaringan yang sangat besar"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Menghindari payload jaringan yang sangat besar"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Meminifikasi file CSS dapat mengurangi ukuran payload jaringan. [Pelajari cara meminifikasi CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Kecilkan CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Meminifikasi file JavaScript dapat mengurangi ukuran payload dan waktu penguraian skrip. [Pelajari cara meminifikasi JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Kecilkan ukuran JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Mengurangi aturan yang tidak digunakan dari stylesheet dan menunda CSS yang tidak digunakan untuk konten paruh atas guna menurunkan byte yang digunakan oleh aktivitas jaringan. [Pelajari cara mengurangi CSS yang tidak digunakan](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Kurangi CSS yang tidak digunakan"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Kurangi JavaScript yang tidak digunakan dan tunda pemuatan skrip sampai diperlukan untuk menurunkan byte yang digunakan oleh aktivitas jaringan. [Pelajari cara mengurangi JavaScript yang tidak digunakan](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Kurangi JavaScript yang tidak digunakan"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Durasi cache yang panjang dapat mempercepat kunjungan berulang ke halaman Anda. [Pelajari lebih lanjut kebijakan cache yang efisien](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 resource ditemukan}other{# resource ditemukan}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Tayangkan aset statis dengan kebijakan cache yang efisien"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Menggunakan kebijakan cache yang efisien pada aset statis"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Gambar yang dioptimalkan dimuat lebih cepat dan menghabiskan lebih sedikit data seluler. [Pelajari cara mengenkode gambar secara efisien](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Enkode gambar secara efisien"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON> yang seben<PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON> yang di<PERSON>an"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> lebih besar dari ukuran yang di<PERSON>an"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON><PERSON> se<PERSON>ai dengan ukuran yang ditampilkan"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Tayangkan gambar dengan ukuran yang sesuai untuk menghemat kuota dan mempercepat waktu pemuatan. [Pelajari cara menyesuaikan ukuran gambar](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Ubah ukuran gambar dengan tepat"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Resource berbasis teks harus ditayangkan dengan kompresi (gzip, deflate, atau brotli) untuk meminimalkan total byte jaringan. [Pelajari lebih lanjut kompresi teks](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Aktifkan kompresi teks"}, "core/audits/content-width.js | description": {"message": "Jika lebar konten aplikasi Anda tidak cocok dengan lebar area pandang, aplikasi mungkin tidak dioptimalkan untuk layar perangkat seluler. [Pelajari cara menyesuaikan ukuran konten untuk area pandang](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Ukuran area pandang {innerWidth} piksel tidak cocok dengan ukuran jendela {outerWidth} piksel."}, "core/audits/content-width.js | failureTitle": {"message": "Ukuran konten untuk area pandang tidak tepat"}, "core/audits/content-width.js | title": {"message": "Ukuran konten untuk area pandang sudah tepat"}, "core/audits/critical-request-chains.js | description": {"message": "Rantai Permintaan Penting di bawah menampilkan resource apa saja yang dimuat dengan prioritas tinggi. Sebaiknya kurangi panjang rantai, kurangi ukuran download resource, atau tunda download resource yang tidak penting untuk mempercepat pemuatan halaman. [Pelajari cara menghindari rantai permintaan penting](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 rantai ditemukan}other{# rantai ditemukan}}"}, "core/audits/critical-request-chains.js | title": {"message": "<PERSON><PERSON><PERSON> per<PERSON>aian permintaan penting"}, "core/audits/csp-xss.js | columnDirective": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> (CSP) yang kuat mengurangi risiko serangan pembuatan skrip lintas situs (XSS) secara signifikan. [Pelajari cara menggunakan CSP untuk mencegah XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Halaman ini memuat CSP yang ditentukan dalam tag `<meta>`. Sebaiknya pindahkan CSP ke header HTTP atau tentukan CSP ketat lainnya di header HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "CSP tidak ditemukan dalam mode penerapan"}, "core/audits/csp-xss.js | title": {"message": "Pastikan CSP efektif melawan serangan XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Penghentian/Peringatan"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "API yang tidak digunakan lagi pada akhirnya akan dihapus dari browser. [Pelajari lebih lanjut API yang tidak digunakan lagi](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 peringatan ditemukan}other{# peringatan ditemukan}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Menggunakan API yang tidak digunakan lagi"}, "core/audits/deprecations.js | title": {"message": "Menghindari API yang tidak digunakan lagi"}, "core/audits/dobetterweb/charset.js | description": {"message": "Deklarasi encoding karakter diperlukan. Tindakan ini dapat dilakukan dengan tag `<meta>` pada 1024 byte pertama HTML atau di header respons HTTP Jenis Konten. [Pelajari lebih lanjut cara mendeklarasikan encoding karakter](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "De<PERSON><PERSON>i charset tidak ada atau terlambat muncul di HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Charset yang ditentukan dengan benar"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Menentukan doctype akan mencegah browser be<PERSON><PERSON> ke quirks mode. [Pelajari lebih lanjut deklarasi doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Nama doctype harus berupa string `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "<PERSON><PERSON><PERSON> berisi `doctype` yang memicu `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumen harus berisi doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "PublicId diperkirakan menjadi string kosong"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "SystemId diperkirakan menjadi string kosong"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "<PERSON><PERSON><PERSON> berisi `doctype` yang memicu `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Halaman tidak memiliki doctype HTML sehingga memicu quirks mode"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Halaman memiliki doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistik"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "DOM yang berukuran besar akan meningkatkan penggunaan memori, men<PERSON><PERSON><PERSON><PERSON> [penghit<PERSON><PERSON> gaya](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) yang le<PERSON>h lama, dan men<PERSON><PERSON> [pengu<PERSON>an posisi tata letak](https://developers.google.com/speed/articles/reflow) yang mahal. [Pelajari cara menghindari ukuran DOM yang berlebihan](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemen}other{# elemen}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Menghindari ukuran DOM yang be<PERSON>han"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Kedalaman DOM Maksimum"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Elemen DOM Total"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Menghindari ukuran DOM yang be<PERSON>han"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Pengguna tidak percaya atau bingung dengan situs yang meminta lokasi mereka tanpa konteks. Sebaiknya kaitkan permintaan dengan tindakan pengguna. [Pelajari lebih lanjut izin geolokasi](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Meminta izin geolokasi pada pemuatan halaman"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Menghindari meminta izin geolokasi pada pemuatan halaman"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Masalah yang dicatat pada panel `Issues` di Chrome DevTools menunjukkan masalah yang belum terselesaikan. Error dapat berasal dari kegagalan permintaan jaringan, kont<PERSON> keamanan yang tidak cukup, dan masalah browser lainnya. Buka panel Issues di Chrome DevTools untuk melihat detail setiap masalah selengkapnya."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Masalah dicatat di panel `Issues` di Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "<PERSON><PERSON><PERSON><PERSON>r o<PERSON>h kebijakan lintas asal"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Penggunaan resource yang berat oleh iklan"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Tidak ada masalah pada panel `Issues` di Chrome DevTools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Semua library JavaScript front-end terdeteksi di halaman. [Pelajari lebih lanjut audit diagnostik deteksi library JavaScript ini](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Library JavaScript yang terdeteksi"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Untuk pengguna dengan koneksi la<PERSON>, skrip eksternal yang secara dinamis dimasukkan melalui `document.write()` dapat menunda pemuatan halaman selama puluhan detik. [Pelajari cara menghindari document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Hi<PERSON>ri `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Pengguna tidak percaya atau bingung dengan situs yang meminta untuk mengirim pemberitahuan tanpa konteks. Sebaiknya kaitkan permintaan dengan gestur pengguna. [Pelajari lebih lanjut cara mendapatkan izin untuk notifikasi secara bertanggung jawab](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Meminta izin notifikasi pada pemuatan halaman"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Mengh<PERSON>ri meminta izin notifikasi pada pemuatan halaman"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Mencegah penempelan input adalah praktik yang buruk untuk UX, serta melemahkan keamanan dengan memblokir pengelola sandi.[Pelajari lebih lanjut kolom input yang mudah digunakan](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Mencegah pengguna menempelkan konten ke kolom input"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Mengizinkan pengguna menempelkan konten ke kolom input"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 memiliki banyak manfaat dibandingkan HTTP/1.1, termasuk header biner dan multiplexing. [Pelajari lebih lanjut HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 permintaan tidak dilayani melalui HTTP/2}other{# permintaan tidak dilayani melalui HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Gunakan HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Se<PERSON>iknya tandai pemroses peristiwa sentuh dan scroll sebagai `passive` untuk meningkatkan kualitas performa scroll halaman Anda. [Pelajari lebih lanjut cara menggunakan pemroses peristiwa pasif](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Tidak menggunakan pemroses pasif untuk menyempurnakan performa scroll"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Menggunakan pemroses pasif untuk menyempurnakan performa scroll"}, "core/audits/errors-in-console.js | description": {"message": "Error yang dicatat di konsol menunjukkan masalah yang belum terselesaikan. Error dapat berasal dari permintaan jaringan yang gagal dan masalah browser lainnya. [Pelajari lebih lanjut error ini dalam audit diagnostik konsol](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Error browser dicatat di konsol"}, "core/audits/errors-in-console.js | title": {"message": "Tidak ada error browser yang dicatat ke konsol"}, "core/audits/font-display.js | description": {"message": "Manfaatkan fitur CSS `font-display` untuk memastikan teks terlihat oleh pengguna saat font web dimuat. [Pelajari lebih lanjut `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Pastikan teks tetap terlihat selama pemuatan font web"}, "core/audits/font-display.js | title": {"message": "Semua teks tetap terlihat selama pemuatan font web"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse tidak dapat secara otomatis memeriksa nilai `font-display` untuk {fontOrigin} asal.}other{Lighthouse tidak dapat secara otomatis memeriksa nilai `font-display` untuk {fontOrigin} asal.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON> (Aktual)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (Ditampilkan)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Dimensi tampilan gambar harus cocok dengan rasio aspek natural. [Pelajari lebih lanjut rasio aspek gambar](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Menampilkan gambar dengan rasio tinggi lebar yang salah"}, "core/audits/image-aspect-ratio.js | title": {"message": "Menampilkan gambar dengan rasio tinggi lebar yang benar"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Ukuran sebenarnya"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> yang di<PERSON>an"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON>n yang diharapkan"}, "core/audits/image-size-responsive.js | description": {"message": "Di<PERSON>si alami gambar sebaiknya proporsional dengan ukuran tampilan dan rasio piksel untuk memaksimalkan kejelasan gambar. [Pelajari cara memberikan gambar yang responsif](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Menampilkan gambar dengan resolusi rendah"}, "core/audits/image-size-responsive.js | title": {"message": "Menampilkan gambar dengan resolusi yang sesuai"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikasi sudah diinstal"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Tidak dapat mendownload ikon yang diperlukan dari manifes"}, "core/audits/installable-manifest.js | columnValue": {"message": "Alasan kegagalan"}, "core/audits/installable-manifest.js | description": {"message": "Peker<PERSON> layanan adalah teknologi yang memungkinkan aplikasi Anda menggunakan banyak fitur Progressive Web App, seperti fitur offline, tambahkan ke layar utama, dan notifikasi push. Dengan implementasi pekerja layanan dan manifes yang benar, browser dapat meminta pengguna secara proaktif untuk menambahkan aplikasi Anda ke layar utama pengguna sehingga tingkat interaksi menjadi lebih tinggi. [Pelajari lebih lanjut persyaratan penginstalan manifes](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 alasan}other{# alasan}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifes aplikasi web atau pekerja layanan tidak memenuhi persyaratan kemampuan pen<PERSON>talan"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL aplikasi Play Store dan ID Play Store tidak cocok"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Halaman dimuat di j<PERSON><PERSON>"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Properti `display` manifes harus berupa salah satu dari `standalone`, `fullscreen`, atau `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifes berisi kolom 'display_override', dan nilai pertama di kolom tersebut harus salah satu dari 'standalone', 'fullscreen', atau 'minimal-ui'"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifes tidak dapat diambil, kosong, atau tidak dapat diurai"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL manifes berubah saat manifes sedang diambil."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifes tidak berisi kolom `name` atau `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifes tidak berisi ikon yang cocok - perlu format PNG, SVG, atau WebP minimal {value0} px, atribut sizes harus disetel, dan atribut purpose harus menyertakan \"any\" jika disetel."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Tidak ada ikon yang disediakan berukuran minimal {value0} px persegi dalam format PNG, SVG, atau WebP, dengan atribut purpose yang tidak disetel atau disetel ke \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "<PERSON><PERSON> yang didownload kosong atau rusak"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "ID Play Store tidak ada"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Halaman tidak memiliki URL <link> manifes"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Tidak dapat memeriksa pekerja layanan tanpa kolom 'start_url' di manifes"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID error kema<PERSON><PERSON> pen<PERSON> '{errorId}' tidak dikenali"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Halaman tidak ditayangkan dari asal yang aman"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Halaman tidak dimuat di frame utama"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Halaman tidak berfungsi saat offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA telah di-uninstal dan pemeriksaan kemampuan penginstalan sedang direset."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Platform aplikasi yang ditentukan tidak didukung di Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifes menentukan prefer_related_applications: benar (true)"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications hanya didukung di saluran Beta dan Stabil Chrome di Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse tidak dapat menentukan apakah halaman dapat diinstal. Coba lagi dengan versi Chrome yang lebih baru."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "URL mulai manifes tidak valid"}, "core/audits/installable-manifest.js | title": {"message": "Manifes aplikasi web dan pekerja layanan memenuhi persyaratan kemampuan pen<PERSON>an"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL di manifes berisi nama pengguna, sandi, atau port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Halaman tidak berfungsi saat offline. Halaman tidak akan dianggap sebagai dapat diinstal setelah rilis stabil Chrome 93 pada Agustus 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Diblokir"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL yang tidak aman"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Semua situs harus dilindungi dengan HTTPS, termasuk situs-situs yang tidak menangani data sensitif. Tindakan tersebut termasuk menghindari [konten campuran](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), tempat beberapa resource dimuat melalui HTTP meskipun permintaan awal dilayani melalui HTTPS. HTTPS mencegah penyusup memodifikasi atau mendengarkan secara pasif komunikasi antara aplikasi dan pengguna, serta merupakan prasyarat untuk HTTP/2 dan banyak API platform web baru. [Pelajari HTTPS lebih lanjut](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 permintaan tidak aman ditemukan}other{# permintaan tidak aman ditemukan}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Tidak menggunakan HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Menggunakan HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Diupgrade secara otomatis ke HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Diizinkan dengan per<PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Fase"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Pen<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Ini adalah elemen contentful terbesar yang di-paint dalam area pandang. [Pelajari lebih lanjut elemen Largest Contentful Paint (LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Penundaan <PERSON>em<PERSON>"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Waktu Pemuatan"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elemen Largest Contentful Paint (LCP)"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Dampak pergeseran tata letak"}, "core/audits/layout-shift-elements.js | description": {"message": "Elemen DOM ini paling dipengaruhi oleh pergeseran tata letak. Beberapa pergeseran tata letak mungkin tidak disertakan dalam nilai metrik CLS karena adanya [windowing](https://web.dev/articles/cls#what_is_cls). [Pelajari cara meningkatkan CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON> per<PERSON>han tata letak be<PERSON><PERSON>n besar"}, "core/audits/layout-shifts.js | columnScore": {"message": "Skor pergeseran tata letak"}, "core/audits/layout-shifts.js | description": {"message": "<PERSON><PERSON><PERSON> adalah pergeseran tata letak terbesar yang diamati di halaman. Setiap item tabel mewakili satu pergeseran tata letak, dan menampilkan elemen yang paling banyak bergeser. Di bawah setiap item adalah kemungkinan penyebab utama dari pergeseran tata letak. Beberapa pergeseran tata letak ini mungkin tidak disertakan dalam nilai metrik CLS karena [normalisasi](https://web.dev/articles/cls#what_is_cls). [Pelajari cara meningkatkan CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{1 pergeseran tata letak ditemukan}other{# pergeseran tata letak ditemukan}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Font web dimuat"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Memasukkan if<PERSON>e"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Permintaan jaringan yang terlambat menyesuaikan tata letak halaman"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Elemen media tidak memiliki ukuran yang jelas"}, "core/audits/layout-shifts.js | title": {"message": "<PERSON><PERSON><PERSON> per<PERSON>han tata letak be<PERSON><PERSON>n besar"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "<PERSON><PERSON><PERSON> paruh atas yang dimuat dengan lambat akan dirender nanti di siklus proses halaman, yang dapat menunda Largest Contentful Paint. [Pelajari pemuatan lambat yang optimal](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Gambar Largest Contentful Paint dimuat dengan lambat"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Gambar Largest Contentful Paint tidak dimuat dengan lambat"}, "core/audits/long-tasks.js | description": {"message": "Mencantumkan tugas yang berjalan paling lama di thread utama, berguna untuk mengidentifikasi kontributor utama penundaan input. [P<PERSON>jari cara menghindari tugas thread utama yang berjalan lama](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tugas berjalan lama}other{# tugas berjalan lama}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON>ri tugas thread utama yang ber<PERSON><PERSON> lama"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Sebaiknya kurangi waktu yang dihabiskan untuk mengurai, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan mengeks<PERSON>usi JS. Coba kirim payload JS yang lebih kecil untuk membantu mengurangi waktu. [Pelajari cara meminimalkan pekerjaan thread utama](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalkan peker<PERSON>an thread utama"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Meminimalkan peker<PERSON>an thread utama"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Untuk menjangkau jumlah pengguna terbanyak, situs harus berjalan di seluruh browser utama. [Pelajari kompatibilitas lintas browser](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Situs dapat ber<PERSON><PERSON> lintas-browser"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Pastikan setiap halaman dapat dijadikan deep link melalui URL, dan URL tersebut unik agar dapat dibagikan di media sosial. [P<PERSON><PERSON><PERSON> lebih lanjut cara menyediakan deep link](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Setiap halaman memiliki URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Transisi harus terasa cepat ketika Anda men<PERSON>-ngetuk, bahkan saat jaringan lambat. Pengalaman ini berperan penting pada persepsi pengguna terhadap performa. [Pelajari lebih lanjut transisi halaman](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Transisi halaman sepertinya tidak diblokir di jaringan"}, "core/audits/maskable-icon.js | description": {"message": "Ikon maskable memastikan bahwa gambar mengisi bentuk secara penuh tanpa efek tampilan lebar saat aplikasi diinstal pada perangkat. [Pelajari ikon manifes maskable](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifes tidak memiliki ikon maskable"}, "core/audits/maskable-icon.js | title": {"message": "Manifes memiliki ikon maskable"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Pergeseran Tata Letak Kumulatif (CLS) mengukur perpindahan elemen yang terlihat dalam area pandang. [Pelajari lebih lanjut metrik Pergeseran Tata Letak Kumulatif](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint menandai waktu saat teks atau gambar pertama di-paint. [Pelajari lebih lanjut metrik First Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint mengukur waktu saat konten utama halaman terlihat. [Pelajari lebih lanjut metrik First Meaningful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint mengukur responsivitas halaman, yaitu waktu yang diperlukan halaman untuk merespons input pengguna secara jelas. [P<PERSON>jari lebih lanjut metrik Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Waktu untuk Interaktif adalah lamanya waktu yang diperlukan halaman untuk menjadi interaktif sepenuhnya. [Pelajari lebih lanjut metrik Waktu untuk Interaktif](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint menunjukkan waktu saat teks atau gambar terbesar di-paint. [Pelajari lebih lanjut metrik Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Potensi maksimal Penundaan Input Pertama yang dapat dialami pengguna Anda adalah durasi tugas terpanjang. [Pelajari lebih lanjut metrik Potensi Maksimal Penundaan Input Pertama](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index menunjukkan seberapa cepat konten halaman terlihat terisi lengkap. [Pelajari lebih lanjut metrik Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "<PERSON><PERSON><PERSON> semua jangka waktu antara FCP dan Waktu untuk Interaktif, ketika durasi tugas melebihi 50 md, dinyatakan dalam milidetik. [Pelajari lebih lanjut metrik Total Blocking Time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Waktu round trip (RTT) jaringan berdampak besar pada performa. RTT ke origin yang tinggi merupakan indikasi bahwa server yang berjarak lebih dekat ke pengguna dapat meningkatkan performa. [Pelajari lebih lanjut Waktu Round Trip](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Waktu Round Trip Jaringan"}, "core/audits/network-server-latency.js | description": {"message": "Latensi server dapat memengaruhi performa web. Latensi server untuk suatu origin yang tinggi merupakan indikasi bahwa server kele<PERSON>han muatan atau memiliki performa backend yang buruk. [Pelajari lebih lanjut waktu respons server](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON><PERSON>end <PERSON>"}, "core/audits/no-unload-listeners.js | description": {"message": "Peristiwa `unload` tidak diaktifkan dengan lancar dan jika diproses dapat mencegah upaya pengoptimalan browser seperti Back-Forward Cache. Sebagai gantinya, gunakan peristiwa `pagehide` atau `visibilitychange`. [Pelajari lebih lanjut cara menghapus muatan pemroses peristiwa](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Mendaftarkan pemroses `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> pemroses peristiwa `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animasi yang tidak digabungkan dapat tersendat dan menambah CLS. [Pelajari cara menghindari animasi non-gabungan](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# elemen animasi ditemukan}other{# elemen animasi ditemukan}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Properti terkait filter dapat memindahkan piksel"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Target memiliki animasi lain yang tidak kompatibel"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efek memiliki mode gabung<PERSON> selain \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Mengh<PERSON>ri animasi yang tidak digabungkan"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Properti terkait transformasi bergantung pada ukuran kotak"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Properti CSS Tidak Didukung: {properties}}other{Properti CSS Tidak Didukung: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efek memiliki parameter waktu yang tidak didukung"}, "core/audits/performance-budget.js | description": {"message": "Pertahankan jumlah dan ukuran permintaan jaringan di bawah target yang ditetapkan oleh anggaran performa yang disediakan. [Pelajari lebih lanjut anggaran performa](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 permintaan}other{# permintaan}}"}, "core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON><PERSON> performa"}, "core/audits/preload-fonts.js | description": {"message": "Pramuat font `optional` agar pengunjung kali pertama dapat menggunakannya. [Pelajari lebih lanjut cara pramuat font](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Font dengan `font-display: optional` tidak dipramuat"}, "core/audits/preload-fonts.js | title": {"message": "Font dengan `font-display: optional` telah dipramuat"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Jika elemen LCP ditambahkan secara dinamis ke halaman, <PERSON><PERSON> harus melakukan pramuat gambar untuk mengoptimalkan waktu LCP. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut cara melakukan pramuat elemen LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Pramuat gambar Largest Contentful Paint (LCP)"}, "core/audits/redirects.js | description": {"message": "Pengalihan mencakup penundaan tambahan sebelum halaman dapat dimuat. [Pelajari cara menghindari pengalihan halaman](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "<PERSON><PERSON><PERSON> pengalihan lebih dari satu halaman"}, "core/audits/seo/canonical.js | description": {"message": "Link kanonis menyarankan URL yang akan ditampilkan dalam hasil penelusuran. [Pelajari lebih lanjut link kanonis](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON> beberapa URL yang berten<PERSON>gan ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL tidak valid ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Mengarahkan ke lokasi `hreflang` lain ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Bukan URL absolut ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Mengarah ke URL root domain (halaman beranda), bukan ke halaman yang setara untuk konten itu"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumen tidak memiliki `rel=canonical` yang valid"}, "core/audits/seo/canonical.js | title": {"message": "Dokumen memiliki `rel=canonical` yang valid"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Link yang Tidak Dapat Di-Crawl"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Mesin telusur dapat menggunakan atribut `href` pada link untuk meng-crawl situs. Pastikan atribut `href` elemen anchor tertaut ke tujuan yang se<PERSON>, se<PERSON>ga lebih banyak halaman situs yang dapat ditemukan. [Pelajari cara membuat link dapat di-crawl](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON> tidak dapat di-crawl"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON> dapat di-crawl"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Teks tambahan yang tidak terbaca"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Ukuran Font"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% Teks <PERSON>aman"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Ukuran font di bawah 12 piksel terlalu kecil untuk dapat dibaca dan memaksa pengunjung seluler “mencubit untuk memperbesar\" agar dapat membacanya. Usahakan untuk menampilkan >60% teks halaman dalam ukuran ≥12 piksel. [Pelajari lebih lanjut ukuran font yang dapat dibaca](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "Teks yang dapat dibaca {decimalProportion, number, extendedPercent}"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Teks tidak terbaca karena tidak ada tag meta viewport yang dioptimalkan untuk layar seluler."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumen tidak menggunakan ukuran font yang terbaca"}, "core/audits/seo/font-size.js | legibleText": {"message": "Teks yang dapat dibaca"}, "core/audits/seo/font-size.js | title": {"message": "Dokumen menggunakan ukuran font yang terbaca"}, "core/audits/seo/hreflang.js | description": {"message": "Link hreflang memberi tahu mesin telusur versi halaman yang harus dicantumkan dalam hasil penelusuran untuk wilayah atau bahasa tertentu. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumen tidak memiliki `hreflang` yang valid"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "<PERSON><PERSON> href relatif"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumen memiliki `hreflang` yang valid"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "<PERSON>de bahasa tidak terduga"}, "core/audits/seo/http-status-code.js | description": {"message": "Halaman dengan kode status HTTP yang tidak berhasil mungkin tidak akan diindeks dengan tepat. [Pelajari lebih lanjut kode status HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Halaman memiliki kode status HTTP yang tidak berhasil"}, "core/audits/seo/http-status-code.js | title": {"message": "Halaman memiliki kode status HTTP yang berhasil"}, "core/audits/seo/is-crawlable.js | description": {"message": "Mesin telusur tidak dapat menyertakan halaman Anda dalam hasil penelusuran jika tidak memiliki izin untuk meng-crawl halaman tersebut. [P<PERSON>jari lebih lanjut perintah crawler](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON> di<PERSON> dari <PERSON>"}, "core/audits/seo/is-crawlable.js | title": {"message": "Halaman tidak diblokir dari pen<PERSON>"}, "core/audits/seo/link-text.js | description": {"message": "Teks link deskriptif membantu mesin telusur memahami konten <PERSON>. [Pelajari cara membuat link lebih mudah diakses](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link ditemukan}other{# link ditemukan}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Link tidak memiliki teks deskriptif"}, "core/audits/seo/link-text.js | title": {"message": "Link memiliki teks deskriptif"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Jalankan [Alat Pengujian Data Terstruktur](https://search.google.com/structured-data/testing-tool/) dan [Linter Data Terstruktur](http://linter.structured-data.org/) untuk memvalidasi data terstruktur. [Pelajari lebih lanjut Data Terstruktur](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Data terstruktur valid"}, "core/audits/seo/meta-description.js | description": {"message": "Deskripsi meta mungkin disertakan dalam hasil penelusuran untuk merangkum isi halaman dengan singkat. [Pelajari lebih lanjut deskripsi meta](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Teks deskripsi kosong."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumen tidak memiliki deskripsi meta"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumen memiliki deskripsi meta"}, "core/audits/seo/plugins.js | description": {"message": "Mesin telusur tidak dapat mengindeks konten plugin, dan banyak perangkat yang membatasi plugin atau tidak mendukungnya. [Pelajari lebih lanjut cara menghindari plugin](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumen menggunakan plugin"}, "core/audits/seo/plugins.js | title": {"message": "<PERSON><PERSON><PERSON> plugin"}, "core/audits/seo/robots-txt.js | description": {"message": "Jika file robots.txt Anda salah format, crawler mungkin tidak dapat memahami cara crawling atau pengindeksan situs yang Anda inginkan. [Pelajari lebih lanjut robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Permintaan untuk robots.txt menampilkan status HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 error ditemukan}other{# error ditemukan}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse tidak dapat mendownload file robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt tidak valid"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt valid"}, "core/audits/seo/tap-targets.js | description": {"message": "Elemen interaktif seperti tombol dan link harus berukuran cukup besar (48x48 piksel), atau memiliki cukup ruang di sekelilingnya, agar cukup mudah diketuk tanpa tumpang-tindih dengan elemen lain. [Pelajari lebih lanjut target ketuk](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} target ketuk memiliki ukuran yang tepat"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Target ketuk terlalu kecil karena tidak ada tag meta viewport yang dioptimalkan untuk layar seluler"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Target ketuk tidak memiliki ukuran yang tepat"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Target Tumpang-Tindih"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Target Ketuk"}, "core/audits/seo/tap-targets.js | title": {"message": "Target ketuk memiliki ukuran yang tepat"}, "core/audits/server-response-time.js | description": {"message": "Pertahankan waktu respons server untuk dokumen utama tetap singkat karena semua permintaan lain bergantung padanya. [Pelajari lebih lanjut metrik Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Dokumen root memerlukan waktu {timeInMs, number, milliseconds} md"}, "core/audits/server-response-time.js | failureTitle": {"message": "Kurangi waktu respons server awal"}, "core/audits/server-response-time.js | title": {"message": "Respons server awal memakan waktu singkat"}, "core/audits/splash-screen.js | description": {"message": "Layar pembuka yang bertema akan memberikan pengalaman berkualitas tinggi saat pengguna meluncurkan aplikasi dari layar utama. [<PERSON><PERSON><PERSON>i lebih lanjut layar pembuka](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Tidak dikonfigurasi untuk layar pembuka khusus"}, "core/audits/splash-screen.js | title": {"message": "Dikonfigurasi untuk layar pembuka khusus"}, "core/audits/themed-omnibox.js | description": {"message": "Kolom URL browser dapat diberi tema agar cocok dengan situs Anda. [Pelajari lebih lanjut cara memberi tema pada kolom URL](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Tidak menyetel warna tema untuk kolom URL."}, "core/audits/themed-omnibox.js | title": {"message": "Menyetel warna tema untuk kolom URL."}, "core/audits/third-party-cookies.js | description": {"message": "Dukungan untuk cookie pihak ketiga akan dihapus di Chrome versi mendatang. [Pelajari lebih lanjut penghentian bertahap penggunaan cookie pihak ketiga](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{1 cookie ditemukan}other{# cookie ditemukan}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Menggunakan cookie pihak ketiga"}, "core/audits/third-party-cookies.js | title": {"message": "Menghindari cookie pihak ketiga"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Keberhasilan Dukungan Pelanggan)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (<PERSON><PERSON><PERSON><PERSON>)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Media Sosial)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produk"}, "core/audits/third-party-facades.js | description": {"message": "Beberapa sematan pihak ketiga dapat lambat dimuat. Sebaiknya ganti sematan dengan facade sampai diperlukan. [Pelajari cara menunda pihak ketiga dengan facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# alternatif fasad tersedia}other{# alternatif fasad tersedia}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Beberapa resource pihak ketiga dapat lambat dimuat dengan fasad"}, "core/audits/third-party-facades.js | title": {"message": "Memuat lambat resource pihak ketiga dengan fasad"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "Kode pihak ketiga dapat memberikan dampak signifikan terhadap performa pemuatan. Batasi jumlah penyedia pihak ketiga yang berlebihan dan coba muat kode pihak ketiga terutama setelah halaman selesai dimuat. [P<PERSON>jari cara meminimalkan dampak pihak ketiga](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Kode pihak ketiga memblokir thread utama untuk {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Kurang<PERSON> dampak kode pihak ketiga"}, "core/audits/third-party-summary.js | title": {"message": "Meminimalkan penggunaan pihak ketiga"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Pengu<PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Setel anggaran waktu untuk membantu memantau performa situs Anda. Situs dengan performa yang baik akan dimuat dengan cepat dan merespons peristiwa input pengguna dengan cepat. [Pelajari lebih lanjut anggaran performa](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Anggara<PERSON> waktu"}, "core/audits/unsized-images.js | description": {"message": "<PERSON><PERSON> lebar dan tinggi yang jelas pada elemen gambar untuk mengurangi pergeseran tata letak dan memperbaiki CLS. [Pelajari cara menyetel dimensi gambar](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Elemen gambar tidak memiliki `width` dan `height` yang jelas"}, "core/audits/unsized-images.js | title": {"message": "Elemen gambar memiliki `width` dan `height` yang jelas"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Sebaiknya lengkapi aplikasi Anda dengan User Timing API untuk mengukur performa aplikasi Anda yang sebenarnya selama pengalaman pengguna utama. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut tanda User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 waktu pengguna}other{# waktu pengguna}}"}, "core/audits/user-timings.js | title": {"message": "Tanda dan ukuran <PERSON>"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Sebuah `<link rel=preconnect>` ditemukan untuk \"{securityOrigin}\", tetapi tidak digunakan oleh browser. Pastikan Anda menggunakan atribut `crossorigin` dengan benar."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Sebaiknya tambahkan petunjuk resource `preconnect` atau `dns-prefetch` untuk membuat koneksi awal ke origin pihak ketiga yang penting. [Pelajari cara melakukan preconnect ke origin yang diperlukan](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Sambungkan terlebih dahulu ke nama domain yang diperlukan"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Ditemukan lebih dari 2 koneksi `<link rel=preconnect>`. Koneksi tersebut sebaiknya tidak sering digunakan dan hanya untuk asal yang paling penting."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Se<PERSON><PERSON> `<link rel=preconnect>` ditemukan untuk \"{securityOrigin}\", tetapi tidak digunakan oleh browser. <PERSON><PERSON> gun<PERSON>n `preconnect` untuk asal yang penting dan pasti akan diminta halaman."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Sebu<PERSON> `<link>` pramuat ditemukan untuk \"{preloadURL}\", tetapi tidak digunakan oleh browser. Pastikan Anda menggunakan atribut `crossorigin` dengan benar."}, "core/audits/uses-rel-preload.js | description": {"message": "Se<PERSON><PERSON><PERSON> gunakan `<link rel=preload>` untuk memprioritaskan pengambilan resource yang saat ini diminta selama pemuatan halaman. [Pelajari cara melakukan pramuat permintaan kunci](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON>at permintaan utama terlebih dahulu"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL Peta"}, "core/audits/valid-source-maps.js | description": {"message": "Peta sumber menerjemahkan kode yang diminifikasi ke kode sumber asli. Fungsi ini akan membantu developer melakukan debug dalam produksi. <PERSON><PERSON> itu, Lighthouse dapat memberikan data lebih lanjut. Sebaiknya deploy peta sumber untuk mendapatkan manfaat yang optimal. [Pelajari lebih lanjut peta sumber](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Tidak ada peta sumber untuk JavaScript pihak pertama berukuran besar"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "File JavaScript berukuran besar tidak memiliki peta sumber"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Peringatan: kurang 1 item dalam `.sourcesContent`}other{Peringatan: kurang # item dalam `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Halaman memiliki peta sumber yang valid"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` tidak hanya mengoptimalkan aplikasi untuk ukuran layar perangkat seluler, tetapi juga mencegah [penundaan 300 milidetik pada input pengguna](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Pelajari lebih lanjut cara menggunakan tag meta viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Tag `<meta name=\"viewport\">` tidak ditemukan"}, "core/audits/viewport.js | failureTitle": {"message": "Tidak memiliki tag `<meta name=\"viewport\">` dengan `width` atau `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Memiliki tag `<meta name=\"viewport\">` dengan `width` atau `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Ini merupakan tugas pemblokiran thread yang terjadi selama pengukuran Interaction to Next Paint. [Pelajari lebih lanjut metrik Interaction to Next Paint](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} md dihabiskan untuk peristiwa '{interactionType}'"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Target peristiwa"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimalkan peker<PERSON>an selama interaksi utama"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Penundaan input"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON><PERSON> presentasi"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | title": {"message": "Meminimalkan peker<PERSON>an selama interaksi utama"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON>i adalah peluang untuk meningkatkan penggunaan ARIA pada aplikasi Anda, yang dapat meningkatkan pengalaman bagi pengguna teknologi asistif, seperti pembaca layar."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ini adalah peluang untuk memberikan konten alternatif untuk audio dan video. Hal ini dapat meningkatkan pengalaman bagi pengguna yang menyandang gangguan penglihatan atau pendengaran."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio dan video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Item berikut memperjelas praktik terbaik yang umum untuk aksesibilitas."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Praktik terbaik"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Pemeriksaan ini menandai peluang untuk [meningkatkan aksesibilitas aplikasi web Anda](https://developer.chrome.com/docs/lighthouse/accessibility/). Deteksi otomatis hanya dapat mendeteksi sebagian masalah dan tidak menjamin aksesibilitas aplikasi web Anda, sehingga [pengujian manual](https://web.dev/articles/how-to-review) juga dianjurkan."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Item berikut ini menangani area yang tidak dapat dicakup oleh fitur pengujian otomatis. Pelajari lebih lanjut dalam panduan kami tentang [menjalankan tinjauan aksesibilitas](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Aksesibilitas"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ini adalah peluang untuk meningkatkan keterbacaan konten Anda."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Kontras"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ini adalah peluang untuk menyempurnakan interpretasi konten Anda oleh pengguna dalam lokal yang berbeda."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internasionalisasi dan pelokalan"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ini adalah peluang untuk meningkatkan semantik kontrol dalam aplikasi Anda. Hal ini dapat menyempurnakan pengalaman bagi pengguna teknologi asistif, seperti pembaca layar."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nama dan label"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ini adalah peluang untuk menyempurnakan navigasi keyboard pada aplikasi Anda."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Na<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ini adalah peluang untuk menyempurnakan pengalaman membaca data dalam format tabel atau daftar menggunakan teknologi pendukung, seperti pembaca layar."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON> dan daftar"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilitas Browser"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Praktik Terbaik"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON>"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Pengalaman Pengguna"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Ang<PERSON><PERSON> performa menetapkan standar performa situs Anda."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Informasi selengkapnya tentang performa aplikasi Anda. Angka ini tidak [secara langsung memengaruhi](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) skor Performa."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostik"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Aspek terpenting dari performa adalah seberapa cepat piksel dirender di layar. <PERSON><PERSON> <PERSON>: First Contentful Paint, First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Penyempurnaan First Paint"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Saran ini dapat membantu pemuatan halaman menjadi lebih cepat. Saran tersebut tidak [secara langsung memengaruhi](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) skor Performa."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Menyempurnakan pengalaman pemuatan halaman k<PERSON>, se<PERSON><PERSON> halaman responsif dan siap untuk digunakan secepatnya. Metrik utama: <PERSON>aktu untu<PERSON>ak<PERSON>f, <PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Performa"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Pemeriksaan ini memvalidasi aspek-aspek Progressive Web App. [Pelajari kriteria Progressive Web App yang bagus](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Pemeriksaan ini diperlukan oleh [Checklist PWA](https://web.dev/articles/pwa-checklist) untuk dasar pengukuran tetapi tidak otomatis diperiksa oleh Lighthouse. Pemeriksaan tersebut tidak memengaruhi skor, tetapi penting karena hal ini berarti Anda memverifikasi situs-situs secara manual."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Dapat <PERSON>"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA yang Dioptimalkan"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Pemeriksaan ini memastikan bahwa halaman Anda mengikuti saran pengoptimalan mesin telusur dasar. Ada banyak faktor tambahan yang tidak diperhitungkan oleh Lighthouse di sini yang mungkin memengaruhi peringkat penelusuran Anda, termasuk performa di [Data Web Inti](https://web.dev/explore/vitals). [Pelajari lebih lanjut Google Penelusuran Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Jalankan validator tambahan ini di situs Anda untuk memeriksa praktik terbaik SEO lainnya."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Format HTML Anda dengan cara yang memungkinkan crawler untuk lebih memahami konten aplikasi Anda."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Praktik Terbaik Konten"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "<PERSON>gar muncul di hasil penel<PERSON><PERSON>n, crawler perlu menga<PERSON><PERSON> aplik<PERSON>."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawling dan <PERSON>"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Pastikan halaman Anda mobile-friendly agar pengguna tidak perlu mencubit atau memperbesar untuk membaca halaman konten. [Pelajari cara membuat halaman mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobile Friendly"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Perangkat yang diuji sepertinya memiliki CPU yang lebih lambat daripada perkiraan Lighthouse. Hal ini dapat berdampak buruk pada skor performa Anda. Pelajari lebih lanjut [cara mengalibrasi pengali pelambatan CPU yang sesuai](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Halaman tersebut mungkin tidak dimuat sesuai harapan karena URL uji Anda ({requested}) dialihkan ke {final}. Coba uji URL kedua secara langsung."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Pemuatan halaman terlalu lambat untuk bisa selesai tepat waktu. <PERSON><PERSON> mungkin tidak lengkap."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Waktu penghapusan cache browser habis. Coba audit halaman ini lagi dan laporkan bug jika masalah berlan<PERSON>t."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Mungkin terdapat data tersimpan yang memengaruhi performa pemuatan di lokasi ini: {locations}. Audit halaman ini di jendela Samaran untuk mencegah resource tersebut memengaruhi performa Anda.}other{Mungkin terdapat data tersimpan yang memengaruhi performa pemuatan di lokasi ini {locations}. Audit halaman ini di jendela Samaran untuk mencegah resource tersebut memengaruhi performa Anda.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Waktu penghapusan data origin habis. Coba audit halaman ini lagi dan laporkan bug jika masalah berlan<PERSON>t."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Terjadi error saat menguraikan header `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Navigasi halaman terdeteksi selama proses run. Menggunakan mode rentang waktu untuk mengaudit navigasi halaman tidak direkomendasikan. Gunakan mode navigasi untuk mengaudit navigasi halaman guna mendapatkan atribusi pihak ketiga dan deteksi thread utama yang lebih baik."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON> halaman yang dimuat melalui permintaan GET yang dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Hanya halaman dengan kode status 2XX yang dapat di-cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome mendeteksi upaya untuk mengeksekusi JavaScript saat berada dalam cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Halaman yang meminta AppBanner saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> karena permintaan keepalive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Back-forward cache dinonaktifkan oleh tanda. Buka chrome://flags/#back-forward-cache untuk mengaktifkannya secara lokal di perangkat ini."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Back-forward cache dinonaktifkan oleh command line."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Back-forward cache dinonakt<PERSON><PERSON> karena memori tidak cukup."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Back-forward cache tidak didukung oleh penerima delegasi."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Back-forward cache dinonaktifkan untuk pra-rendering."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Halaman tidak dapat di-cache karena memiliki instance BroadcastChannel dengan pemroses terdaftar."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Halaman dengan header cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON> senga<PERSON>."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Halaman dikeluarkan dari cache untuk memungkinkan halaman lain di-cache."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Halaman yang berisi plugin saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Halaman yang menggunakan FileChooser API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Halaman yang menggunakan File System Access API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Halaman yang menggunakan Dispatcher Perangkat Media tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Pemutar media sedang berputar saat pengguna menutupnya."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Halaman yang menggunakan MediaSession API dan menyetel status pemutaran tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Halaman yang menggunakan MediaSession API dan menyetel pengendali tindakan tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> karena pemba<PERSON> layar."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Halaman yang menggunakan SecurityHandler tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Halaman yang menggunakan Serial API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Halaman yang menggunakan WebAuthentication API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Halaman yang menggunakan WebBluetooth API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Halaman yang menggunakan WebUSB API tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Back-forward cache dinonaktifkan karena cookie dinonaktifkan di halaman yang menggunakan `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Halaman yang menggunakan pekerja atau worklet khusus saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokumen belum selesai dimuat sebelum pengguna menutupnya."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Ada Banner Aplikasi saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Ada Pengelola Sandi Chrome saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Distilasi DOM sedang berlangsung saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Ada Penampil DOM Distiller saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Back-forward cache dinonaktifkan karena ekstensi menggunakan API pesan."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Ekstensi yang memiliki koneksi persisten harus memutuskan koneksi tersebut sebelum disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Eks<PERSON>i yang memiliki koneksi persisten mencoba mengirim pesan ke frame dalam back-forward cache."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> ka<PERSON>."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Dialog modal seperti pengiriman ulang formulir atau dialog sandi HTTP ditampilkan untuk halaman saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Halaman offline ditampilkan saat pengguna menutupnya."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Ada panel Intervensi Memori Habis saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Ada permintaan izin saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON> pemblokir pop-up saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Detail Safe Browsing ditampilkan saat pengguna menutup halaman."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Safe Browsing menganggap halaman ini melanggar dan memblokir pop-up."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON> layanan diakti<PERSON>kan saat halaman berada dalam back-forward cache."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Back-forward cache dinonakt<PERSON><PERSON> karena error dokumen."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Halaman yang menggunakan FencedFrames tidak dapat disimpan dalam bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Halaman dikeluarkan dari cache untuk memungkinkan halaman lain di-cache."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Halaman yang diberikan akses streaming media saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Halaman yang menggunakan portal saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Halaman yang menggunakan IdleManager saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Halaman yang memiliki IndexedDB connection terbuka saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Back-forward cache dinonaktifkan karena peristiwa IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "API yang tidak memenuhi syarat telah digunakan."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Halaman yang disertai `JavaScript` oleh ekstensi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Halaman yang disertai `StyleSheet` oleh ekstensi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Error internal."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Back-forward cache din<PERSON><PERSON><PERSON><PERSON> karena permintaan keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Halaman yang menggunakan kunci Keyboard saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | loading": {"message": "Halaman belum selesai dimuat sebelum pengguna menutupnya."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Halaman yang resource utamanya memiliki cache-control:no-cache tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Halaman yang resource utamanya memiliki cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigasi dibatalkan sebelum halaman dapat dipulihkan dari back-forward cache."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Halaman dikeluarkan dari cache karena koneksi jaringan aktif menerima terlalu banyak data. Chrome membatasi jumlah data yang dapat diterima halaman saat di-cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Halaman yang memiliki fetch() atau XHR yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Halaman dikeluarkan dari back-forward cache karena permin<PERSON>an jaringan aktif men<PERSON> pengal<PERSON>."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Halaman dikeluarkan dari cache karena koneksi jaringan terbuka terlalu lama. Chrome membatasi jumlah waktu untuk suatu halaman dapat menerima data saat di-cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Halaman yang tidak memiliki header respons yang valid tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON> ter<PERSON><PERSON> di frame selain frame utama."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Halaman dengan indexed DB transactions yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Halaman dengan permintaan jaringan yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Halaman dengan permintaan jaringan fetch yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Halaman dengan permintaan jaringan yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Halaman dengan permintaan jaringan XHR yang sedang berlangsung saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Halaman yang menggunakan PaymentManager saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Halaman yang menggunakan Picture-in-Picture saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | portal": {"message": "Halaman yang menggunakan portal saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | printing": {"message": "Halaman yang menampilkan UI Pencetakan saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Halaman dibuka menggunakan '`window.open()`' dan tab lain memiliki referensi ke halaman tersebut, atau halaman membuka jendela."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Proses rendering untuk halaman dalam back-forward cache mengalami error."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Proses rendering untuk halaman dalam back-forward cache dihentikan."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Halaman yang telah meminta izin perekaman audio saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Halaman yang telah meminta izin sensor saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Halaman yang telah meminta izin sinkronisasi atau pengambilan di latar belakang saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Halaman yang telah meminta izin MIDI saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Halaman yang telah meminta izin notifikasi saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Halaman yang telah meminta akses penyimpanan saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Halaman yang telah meminta izin perekaman video saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "<PERSON><PERSON> halaman yang skema URL-nya adalah HTTP/HTTPS yang dapat di-cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON><PERSON> diklaim oleh pekerja layanan saat berada dalam back-forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> layanan mencoba mengirimkan `MessageEvent` ke halaman yang berada dalam back-forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorker menjadi tidak terdaftar saat halaman berada dalam back-forward cache."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Halaman dikeluarkan dari back-forward cache ka<PERSON> akt<PERSON> p<PERSON><PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome dimulai ulang dan entri back-forward cache dihapus."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Halaman yang menggunakan SharedWorker saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Halaman yang menggunakan SpeechRecognizer saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Halaman yang menggunakan SpeechSynthesis saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "<PERSON><PERSON><PERSON> di halaman memulai navigasi yang tidak selesai."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Halaman yang subresource-nya memiliki cache-control:no-cache tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Halaman yang subresource-nya memiliki cache-control:no-store tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON><PERSON> melebihi waktu maksimum dalam back-forward cache dan masa berlakunya telah berak<PERSON>."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "<PERSON><PERSON><PERSON> halaman habis saat disimpan dalam back-forward cache (kemungkinan karena pengendali pagehide yang berjalan lama)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Halaman memiliki pengendali penghapus muatan di frame utama."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Halaman memiliki pengendali penghapus muatan di sub frame."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Browser te<PERSON> men<PERSON> header penggantian agen pengguna."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Halaman yang diberikan akses untuk merekam video atau audio saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Halaman yang menggunakan WebDatabase saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Halaman yang menggunakan WebHID saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Halaman yang menggunakan WebLocks saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Halaman yang menggunakan WebNfc saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Halaman yang menggunakan WebOTPService saat ini tidak dapat disimpan dengan benar dalam bfcache."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Halaman dengan WebRTC tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Halaman yang menggunakan WebShare saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Halaman dengan WebSocket tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Halaman dengan WebTransport tidak dapat disimpan dalam back-forward cache."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Halaman yang menggunakan WebXR saat ini tidak dapat disimpan dengan benar dalam back-forward cache."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Sebaiknya tambahkan skema URL https: dan http: (diabaikan oleh browser yang mendukung `'strict-dynamic'`) agar kompatibel dengan browser lama."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "`disown-opener` tidak digunakan lagi sejak CSP3. Sebagai gantinya, gunakan header Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "`referrer` tidak digunakan lagi sejak CSP2. Sebagai gantinya, gunakan header Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "`reflected-xss` tidak digunakan lagi sejak CSP2. Sebagai gantinya, gunakan header X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "`base-uri` yang tidak ada memungkinkan tag `<base>` yang dimasukkan guna menyetel URL dasar untuk semua URL relatif (mis. skrip) ke domain yang dikontrol penyerang. Sebaiknya tetapkan `base-uri` ke `'none'` atau `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "`object-src` yang tidak ada memungkinkan injeksi plugin yang mengeksekusi skrip yang tidak aman. Sebaiknya tetapkan `object-src` ke `'none'` jika Anda bi<PERSON>."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Perintah `script-src` tidak ada. Hal ini dapat mengizinkan eksekusi skrip yang tidak aman."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda lupa tanda titik koma? Tampaknya {keyword} adalah perintah, bukan kata kunci."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonce harus menggunakan charset base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "<PERSON>ce harus terdiri dari minimal 8 karakter."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "<PERSON><PERSON><PERSON> penggunaan skema URL biasa ({keyword}) dalam perintah ini. Skema URL biasa memungkinkan skrip berasal dari domain yang tidak aman."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "<PERSON><PERSON><PERSON> penggunaan karakter pengganti biasa ({keyword}) dalam perintah ini. <PERSON><PERSON>er pengganti biasa memungkinkan skrip berasal dari domain yang tidak aman."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Tujuan pelaporan hanya dikonfigurasi melalui perintah report-to. Perintah ini hanya didukung di browser berbasis Chromium. Jadi, sebaiknya gunakan juga perintah `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Tidak ada CSP yang mengon<PERSON>gurasi tujuan pelaporan. Mempertahankan CSP dari waktu ke waktu dan memantau setiap kerusakan menjadi sulit dilakukan."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Daftar host yang di<PERSON><PERSON>an sering kali dapat diabaikan. <PERSON><PERSON><PERSON><PERSON> gunakan nonce atau hash CSP, beserta `'strict-dynamic'` jika perlu."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Perintah CSP tidak dikenal."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "Tampaknya {keyword} adalah kata kunci yang tidak valid."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` memungkinkan eksekusi skrip dalam halaman dan pengendali peristiwa yang tidak aman. Sebaiknya gunakan nonce atau hash CSP untuk mengizinkan skrip satu per satu."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Sebaiknya tambahkan `'unsafe-inline'` (diabaikan oleh browser yang mendukung nonce/hash) agar kompatibel dengan browser lama."}, "core/lib/deprecation-description.js | feature": {"message": "Periksa halaman status fitur untuk mendapatkan detail selengkapnya."}, "core/lib/deprecation-description.js | milestone": {"message": "<PERSON>bahan ini akan berlaku dengan milestone {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Menggunakan Fitur yang Tidak Digunakan Lagi"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Otorisasi tidak akan dicakup oleh simbol karakter pengganti (*) dalam penanganan `Access-Control-Allow-Headers` CORS."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Dar<PERSON>ada menggunakan pemilih `-internal-media-controls-overlay-cast-button`, se<PERSON><PERSON><PERSON> gunakan `disableRemotePlayback` untuk menonaktifkan integrasi Cast default."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Permintaan resource yang URL-nya berisi karakter `(n|r|t)` spasi kosong yang dihapus dan karakter kurang dari (`<`) akan diblokir. Hapus baris baru dan enkode karakter kurang dari, dari tempat seperti nilai atribut elemen untuk memuat resource ini."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` tidak digunakan lagi. Sebagai gantinya, gunakan API standar: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` tidak digunakan lagi. Sebagai gantinya, gunakan API standar: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` tidak digunakan lagi. Sebagai gantinya, gunakan API standar: `nextHopProtocol` di Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "<PERSON><PERSON> yang berisi karakter `(0|r|n)` akan di<PERSON>, bukan dipotong."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Melonggarkan kebijakan origin yang sama dengan menyetel `document.domain` sudah tidak digunakan lagi, dan akan dinonaktifkan secara default. Peringatan penghentian ini ditujukan untuk akses lintas origin yang diaktifkan dengan menyetel `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Memicu window.alert dari iframe lintas origin tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Memicu window.confirm dari iframe lintas origin tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "<PERSON><PERSON><PERSON><PERSON>, term<PERSON>uk `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument`, dan `DOMCharacterDataModified` tidak digunakan lagi (https://w3c.github.io/uievents/#legacy-event-types) dan akan dihapus. Sebagai gantinya, gunakan `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Dukungan untuk data: URL dalam elemen <use> SVG tidak digunakan lagi dan akan dihapus pada masa mendatang."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Melonggarkan kebijakan origin yang sama dengan menyetel `document.domain` sudah tidak digunakan lagi, dan akan dinonaktifkan secara default. Untuk terus menggunakan fitur ini, pilih untuk tidak menggunakan cluster agen dengan kunci origin dengan mengirimkan header `Origin-Agent-Cluster: ?0` bersama respons HTTP untuk dokumen dan bingkai. Lihat https://developer.chrome.com/blog/immutable-document-domain/ untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Header `Expect-CT` tidak digunakan lagi dan akan dihapus. Chrome mewajibkan Transparansi Sertifikat untuk semua sertifikat tepercaya publik yang diterbitkan setelah 30 April 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "`getCurrentPosition()` dan `watchPosition()` tidak lagi berfungsi pada origin yang tidak aman. Untuk menggunakan fitur ini, sebaiknya alihkan aplikasi ke origin yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` dan `watchPosition()` tidak digunakan lagi karena origin yang tidak aman. Untuk menggunakan fitur ini, sebaiknya alihkan aplikasi ke origin yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` tidak lagi berfungsi pada origin yang tidak aman. Untuk menggunakan fitur ini, sebaiknya alihkan aplikasi ke origin yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` tidak digunakan lagi. Sebagai gantinya, gunakan `RTCPeerConnectionIceErrorEvent.address` atau `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Data arbitrer dan origin penjual dari peristiwa pekerja layanan `canmakepayment` tidak digunakan lagi dan akan dihapus: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Situs meminta subresource dari jaringan yang hanya dapat diakses karena posisi jaringan istimewa penggunanya. Permintaan ini mengekspos perangkat dan server non-publik ke internet, yang meningkatkan risiko serangan pemalsuan permintaan lintas situs (CSRF), dan/atau kebocoran informasi. Untuk mengurangi risiko tersebut, Chrome menghentikan permintaan ke subresource non-publik saat dimulai dari konteks yang tidak aman, dan akan mulai memblokirnya."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "<PERSON><PERSON>m `dailyUpdateUrl` dari `InterestGroups` yang di<PERSON> ke `joinAdInterestGroup()` telah diganti namanya menjadi `updateUrl`, untuk mencerminkan perilakunya dengan lebih akurat."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "CSS tidak dapat dimuat dari URL `file:` kecuali jika diakhiri dengan ekstensi file `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Penggunaan `SourceBuffer.abort()` untuk membatalkan penghapusan rentang asinkron `remove()` tidak digunakan lagi karena perubahan spesifikasi. Dukungan akan dihapus pada masa mendatang. Sebagai gantinya, Anda harus memproses peristiwa `updateend`. `abort()` dimaksudkan untuk hanya membatalkan penambahan media asinkron atau mereset status parser."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Menyetel `MediaSource.duration` di bawah stempel waktu presentasi tertinggi dari semua bingkai berkode dan yang di-buffer kini tidak digunakan lagi karena perubahan spesifikasi. Dukungan untuk penghapusan implisit media yang di-buffer dan terpotong akan dihapus pada masa mendatang. Anda harus menjalankan `remove(newDuration, oldDuration)` eksplisit pada semua `sourceBuffers`, saat status menunjukkan `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI akan meminta izin untuk menggunakan sysex meskipun sysex tidak ditentukan dalam `MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Atribut `shadowroot` lama yang tidak distandardisasi tidak digunakan lagi, dan *tidak lagi berfungsi* di M119. Sebagai gantinya, gunakan atribut `shadowrootmode` baru yang distandardisasi."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Notification API mungkin tidak lagi digunakan dari origin yang tidak aman. Sebaiknya Anda mengalihkan aplikasi Anda ke origin yang aman, seperti HTTPS. Lihat https://goo.gle/chrome-insecure-origins untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Izin untuk Notification API mungkin tidak lagi diminta dari iframe lintas asal. Sebaiknya Anda meminta izin dari bingkai level teratas atau membuka jendela baru."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opsi `imageOrientation: 'none'` di createImageBitmap tidak digunakan lagi. Sebagai gantinya, gunakan createImageBitmap dengan opsi \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Partner <PERSON><PERSON>g menegosia<PERSON><PERSON> versi (D)TLS yang usang. Hubungi partner <PERSON><PERSON>."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Menentukan `overflow: visible` pada tag img, video, dan canvas dapat menyebabkannya menghasilkan konten visual di luar batas elemen. Lihat https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "`paymentManager.instruments` tidak digunakan lagi. Sebagai gantinya, gunakan penginstalan tepat waktu untuk pengendali pembayaran."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Panggilan `PaymentRequest` mengabaikan perintah `connect-src` <PERSON><PERSON><PERSON><PERSON> (CSP). Pengabaian ini tidak digunakan lagi. Tambahkan ID metode pembayaran dari `PaymentRequest` API (di kolom `supportedMethods`) ke perintah `connect-src` CSP."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "`StorageType.persistent` tidak digunakan lagi. Se<PERSON><PERSON> g<PERSON>, gun<PERSON><PERSON> `navigator.storage` standar."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "`<source src>` dengan induk `<picture>` tidak valid dan akan diabaikan. Sebagai gantinya, gunakan `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame adalah metode khusus vendor. Sebagai gantinya, gunakan cancelAnimationFrame standar."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame adalah metode khusus vendor. Sebagai gantinya, gunakan requestAnimationFrame standar."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen tidak digunakan lagi. Sebagai gantinya, gunakan Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() tidak digunakan lagi. Sebagai gantinya, gunakan Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() tidak digunakan lagi. Sebagai gantinya, gunakan Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() tidak digunakan lagi. Sebagai gantinya, gunakan Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() tidak digunakan lagi. Sebagai gantinya, gunakan Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen tidak digunakan lagi. Sebagai gantinya, gunakan Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "<PERSON><PERSON> pengg<PERSON>an API `chrome.privacy.websites.privacySandboxEnabled`, meskipun akan tetap aktif untuk kompatibilitas mundur hingga rilis M113. Sebagai gantinya, gunakan `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled`, dan `chrome.privacy.websites.adMeasurementEnabled`. Lihat https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Batasan `DtlsSrtpKeyAgreement` dihapus. <PERSON>a telah menentukan nilai `false` untuk batasan ini, yang ditafsirkan sebagai upaya untuk menggunakan metode `SDES key negotiation` yang dihapus. Fungsi ini dihapus. Sebagai gantinya, gunakan layanan yang mendukung `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Batasan `DtlsSrtpKeyAgreement` dihapus. Anda telah menentukan nilai `true` untuk batasan ini, yang tidak be<PERSON>, tetapi <PERSON>a dapat menghapus batasan ini agar rapi."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "GetStats() berbasis callback tidak digunakan lagi dan akan dihapus. Sebagai gantinya, gunakan getStats() yang sesuai dengan spesifikasi."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Range.expand() tidak digunakan lagi. Sebagai gantinya, gunakan Selection.Modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Permintaan subresource yang URL-nya berisi kredensial yang disematkan (mis. `**********************/`) akan diblokir."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Opsi `rtcpMuxPolicy` tidak digunakan lagi dan akan di<PERSON>pus."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` akan me<PERSON><PERSON>an isolasi lintas asal. Lihat https://developer.chrome.com/blog/enabling-shared-array-buffer/ untuk detail selengkapnya."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` tanpa aktivasi pengguna tidak digunakan lagi dan akan dihapus."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Ekstensi harus ikut serta dalam isolasi lintas asal untuk terus menggunakan `SharedArrayBuffer`. Lihat https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Web SQL tidak digunakan lagi. Gunakan SQLite WebAssembly atau Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Desk<PERSON><PERSON> izin `window-placement` tidak digunakan lagi. Sebagai gantinya, gunakan `window-management`. Untuk mendapatkan bantuan lebih lan<PERSON>t, buka https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "<PERSON><PERSON><PERSON><PERSON> izin `window-placement` tidak digunakan lagi. Sebagai gantinya, gunakan `window-management`. Untuk mendapatkan bantuan lebih lan<PERSON>t, buka https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "UTF-16 tidak didukung oleh respons JSON di `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "`XMLHttpRequest` sinkron pada thread utama tidak digunakan lagi karena efeknya yang merugikan terhadap pengalaman pengguna akhir. Untuk mendapatkan bantuan lebih lanjut, buka https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "`supportsSession()` tidak digunakan lagi. Sebagai gantinya, gunakan `isSessionSupported()` dan periksa nilai boolean yang di-resolve."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL Cache"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemen"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Ukuran Resource"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Ukuran"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Sumber"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON> ya<PERSON>"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Ukuran Transfer"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potensi Penghematan"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potensi Penghematan"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potensi penghematan {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elemen ditemukan}other{# elemen ditemukan}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potensi penghematan {wastedMs, number, milliseconds} md"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumen"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Gambar"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Tingg<PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Rendah"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Sedang"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Potensi Maks<PERSON> In<PERSON> Pertama"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} md"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Resource lainnya"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} dtk"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON> ketiga"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Terjadi error saat merekam jejak selama pemuatan halaman <PERSON>. Harap jalankan Lighthouse kembali. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "W<PERSON>tu tunggu untuk sambungan Protokol Debugger awal berakhir."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome tidak men<PERSON><PERSON><PERSON>an screenshot apa pun selama pemuatan halaman. Pastikan terdapat konten yang terlihat pada halaman, kemudian coba jalankan kembali Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Server DNS tidak dapat menetapkan domain yang disediakan."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Terjadi error pada pengumpul {artifactName} wajib: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Terjadi error Chrome internal. Harap mulai ulang Chrome dan coba jalankan kembali Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Pengumpul {artifactName} yang diperlukan tidak berjalan."}, "core/lib/lh-error.js | noFcp": {"message": "Halaman tidak menampilkan konten apa pun. Pastikan jendela browser tetap di latar depan selama pemuatan, lalu coba lagi. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Halaman tidak menampilkan konten yang memenuhi syarat sebagai Largest Contentful Paint (LCP). Pastikan halaman memiliki elemen LCP yang valid, lalu coba lagi. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Halaman yang diberikan bukan HTML (ditayangkan sebagai jenis MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Versi Chrome ini terlalu lama untuk mendukung '{featureName}'. Gunakan versi yang lebih baru untuk melihat hasil lengkap."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse tidak dapat memuat URL yang Anda minta dengan lancar karena halaman berhenti merespons."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL yang Anda berikan tidak memiliki sertifikat keamanan yang valid. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome mencegah pemuatan halaman dengan interstisial. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik. (Detail: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik. (Kode status: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Waktu pemuatan halaman Anda terlalu lama. <PERSON><PERSON> ikuti peluang dalam laporan untuk mengurangi waktu muat halaman Anda, kemudian coba jalankan kembali Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "<PERSON><PERSON><PERSON> tunggu respons protokol DevTools telah melampaui waktu yang dialokasikan. (Metode: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Pengambilan konten resource telah melampaui waktu yang dialokasikan"}, "core/lib/lh-error.js | urlInvalid": {"message": "URL yang Anda berikan tampaknya tidak valid."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik. (Kode status: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Jenis MIME halaman adalah XHTML: Lighthouse tidak secara eksplisit mendukung jenis dokumen ini"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON><PERSON> navigasi ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Laporan snapshot ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "La<PERSON><PERSON> rentang waktu ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Aksesibilitas"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Praktik Terbaik"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Performa"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Desktop"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Memahami Laporan Alur Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "<PERSON><PERSON><PERSON> laporan Navigasi untuk ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "<PERSON><PERSON><PERSON> laporan Snapshot untuk ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Gunakan laporan Rentang Waktu untuk ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Mendapatkan skor Performa Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mengukur metrik Performa pemuatan halaman seperti Largest Contentful Paint dan Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Menilai kemampuan Progressive Web App."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Menemukan masalah aksesibilitas dalam aplikasi web satu halaman atau formulir yang rumit."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Mengevaluasi praktik terbaik menu dan elemen UI yang tersembunyi di balik interaksi."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "<PERSON><PERSON><PERSON><PERSON> pergeseran tata letak dan waktu eksekusi JavaScript pada serangkaian interaksi."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "<PERSON><PERSON>ukan peluang performa guna meningkatkan pengalaman untuk halaman yang dibuka dalam waktu lama dan aplikasi web satu halaman."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Dampak tertinggi"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} audit informatif}other{{numInformative} audit informatif}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Pemua<PERSON> halaman"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Laporan Navigasi menganalisis pemuatan satu halaman, persis seperti laporan Lighthouse asli."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON> navigasi"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} laporan navigasi}other{{numNavigation} laporan navigasi}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} audit yang dapat diluluskan}other{{numPassableAudits} audit yang dapat diluluskan}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audit lulus}other{{numPassed} audit lulus}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Biasa"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Error"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Buruk"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Baik"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Simpan"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Status halaman yang ditang<PERSON>p"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "<PERSON><PERSON><PERSON> Snapshot menganalisis halaman dalam status tertentu, <PERSON><PERSON>a setelah interaksi pengguna."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Laporan snapshot"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} laporan snapshot}other{{numSnapshot} laporan snapshot}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interaksi pen<PERSON>una"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Laporan Ren<PERSON>g Waktu menganalisis periode waktu yang arbitrer, <PERSON><PERSON>a yang berisi interaksi pengguna."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Laporan rentang waktu"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} laporan rentang waktu}other{{numTimespan} laporan rentang waktu}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Laporan Alur Pengguna Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Untuk konten animasi, gunakan [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) untuk meminimalkan penggunaan CPU saat konten tidak berada di layar."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Sebaiknya tampilkan semua komponen [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) dalam format WebP saat menentukan fallback yang tepat untuk browser lainnya. [Pelajari lebih lanjut](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Pastikan <PERSON> [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) untuk gambar agar otomatis dimuat dengan lambat. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> alat semacam [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) untuk [merender sistem server tata letak AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [dokumentasi AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) untuk memastikan semua gaya didukung."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponen [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) mendukung atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) untuk menentukan aset gambar yang akan digunakan berdasarkan ukuran layar. [Pela<PERSON><PERSON> lebih lanjut](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Pertimbangkan untuk menggunakan scroll virtual dengan Component Dev Kit (CDK) jika daftar yang sangat besar sedang dirender. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Terapkan [pem<PERSON>han kode tingkat rute](https://web.dev/route-level-code-splitting-in-angular/) untuk meminimalkan ukuran paket JavaScript Anda. Selain itu, sebaik<PERSON> simpan aset ke cache terlebih dahulu menggunakan [pek<PERSON><PERSON> layanan <PERSON>](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Jika Anda menggunakan Angular CLI, pastikan bahwa build dibuat dalam mode produksi. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Jika Anda menggunakan Angular CLI, sertakan peta sumber dalam build produksi untuk memeriksa paket Anda. [<PERSON><PERSON>jari lebih lanjut](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Pramuat rute terlebih dahulu untuk mempercepat navigasi. [Pelajari lebih lanjut](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Pertimbangkan untuk menggunakan utilitas `BreakpointObserver` dalam Component Dev Kit (CDK) guna mengelola titik henti image sementara. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Sebaiknya upload GIF ke layanan yang akan menyediakannya untuk disematkan sebagai video HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Sebutkan `@font-display` saat menentukan font khusus di tema Anda."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Se<PERSON>ik<PERSON> konfigurasi [format gambar WebP dengan Konversi gaya gambar](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) di situs Anda."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instal [modul Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) yang dapat memuat gambar dengan lambat. Modul tersebut mampu menunda pemuatan gambar di bagian halaman yang belum ditampilkan untuk meningkatkan performa."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Sebaiknya gunakan modul untuk menyejajarkan CSS dan JavaScript penting, dan gunakan atribut tunda untuk CSS atau JavaScript yang tidak penting."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> spesif<PERSON> tema, modul, dan server berkontribusi pada waktu respons server. Sebaiknya cari tema yang lebih optimal, pilih modul pengoptimalan dengan hati-hati, dan/atau upgrade server Anda. Server hosting Anda harus memanfaatkan cache opcode dan cache memori PHP untuk mengurangi waktu kueri database, seperti Redis atau Memcached, serta logika aplikasi yang dioptimalkan guna menyiapkan halaman dengan lebih cepat."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Se<PERSON><PERSON><PERSON> gunakan [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) untuk mengurangi ukuran gambar yang dimuat di halaman Anda. Jika Anda menggunakan Views untuk menampilkan beberapa item konten di halaman, sebaiknya implementasikan penomoran halaman guna membatasi jumlah item konten yang ditampilkan di halaman tersebut."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Pastikan Anda mengaktifkan \"Aggregate CSS files\" di halaman \"Administration » Configuration » Development\".  Pastikan situs Drupal Anda menjalankan setidaknya Drupal 10.1 untuk mendapatkan dukungan penggabungan aset yang ditingkatkan."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Pastikan Anda mengaktifkan \"Aggregate JavaScript files\" di halaman \"Administration » Configuration » Development\".  Pastikan situs Drupal Anda menjalankan setidaknya Drupal 10.1 untuk mendapatkan dukungan penggabungan aset yang ditingkatkan."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Sebaiknya hapus aturan CSS yang tidak digunakan dan hanya lampirkan library Drupal yang diperlukan ke halaman atau komponen yang relevan di halaman. Buka [Link dokumentasi Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) untuk mengetahui detailnya. Untuk mengidentifikasi library yang dilampirkan yang menambahkan CSS tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi tema/modul yang bertanggung jawab dari URL stylesheet saat agregasi CSS dinonaktifkan di situs Drupal Anda. Cari tema/modul dengan banyak stylesheet dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Tema/modul sebaiknya hanya menambahkan stylesheet ke antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Sebaiknya hapus aset JavaScript yang tidak digunakan dan hanya lampirkan library Drupal yang diperlukan ke halaman atau komponen yang relevan di halaman. Buka [Link dokumentasi Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) untuk mengetahui detailnya. Untuk mengidentifikasi library yang dilampirkan yang menambahkan JavaScript tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi tema/modul yang bertanggung jawab dari URL skrip saat agregasi JavaScript dinonaktifkan di situs Drupal Anda. Cari tema/modul dengan banyak skrip dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Tema/modul sebaiknya hanya menambahkan skrip ke antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Setel \"Browser and proxy cache maximum age\" di halaman \"Administration » Configuration » Development\". Baca [<PERSON>ache <PERSON>al dan mengopt<PERSON> performa](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Se<PERSON><PERSON><PERSON> gunakan [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) yang otomatis mengoptimalkan dan mengurangi ukuran gambar yang diupload melalui situs dengan tetap mempertahankan kualitas. Selain itu, pastikan Anda menggunakan [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) native yang disediakan oleh Drupal (tersedia di Drupal 8 dan yang lebih tinggi) untuk semua gambar yang dirender di halaman."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Petunjuk resource preconnect atau dns-prefetch dapat ditambahkan dengan menginstal dan mengonfigurasi [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) yang memberikan fasilitas untuk petunjuk resource agen pengguna."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Pastikan Anda men<PERSON> [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) native yang disediakan oleh Drupal (tersedia di Drupal 8 dan yang lebih tinggi). Gunakan Responsive Image Styles saat merender kolom gambar melalui mode tampilan, tampilan, atau gambar yang diupload melalui editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "<PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Optimize Fonts` untuk otomatis memanfaatkan fitur CSS `font-display` guna memastikan teks dapat dilihat oleh pengguna saat font web dimuat."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Next-Gen Formats` untuk mengonversi gambar ke WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Lazy <PERSON>ad Images` untuk menunda pemuatan gambar di luar layar sampai gambar diperlukan."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Critical CSS` serta `Script Delay` untuk menunda JS/CSS yang tidak kritis."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Gunakan [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) untuk menyimpan cache konten di berbagai jaringan di seluruh dunia, yang mempersingkat waktu untuk menerima byte data pertama."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Minify CSS` untuk otomatis meminifikasi CSS guna mengurangi ukuran payload jaringan."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Minify Javascript` untuk otomatis meminifikasi JS guna mengurangi ukuran payload jaringan."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Gunakan [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Remove Unused CSS` untuk membantu menyelesaikan masalah ini. Tindakan ini akan mengidentifikasi class CSS yang sebenarnya digunakan di setiap halaman situs Anda, dan menghapus class CSS lainnya untuk membuat ukuran file tetap kecil."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Efficient Static Cache Policy` untuk menyetel nilai yang direkomendasikan dalam header cache bagi aset statis."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Next-Gen Formats` untuk mengonversi gambar ke WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Gunakan [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Pre-Connect Origins` untuk otomatis menambahkan petunjuk resource `preconnect` guna melakukan koneksi awal ke asal pihak ketiga yang penting."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Gunakan [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Preload Fonts` serta `Preload Background Images` guna menambahkan link `preload` untuk memprioritaskan pengambilan resource yang saat ini diminta pada pemuatan halaman."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) dan aktifkan `Resize Images` untuk mengubah ukuran gambar menjadi ukuran yang sesuai dengan perangkat, se<PERSON>ga mengurangi ukuran payload jaringan."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> komponen `gatsby-plugin-image`, bukan `<img>`, untuk mengoptimalkan format gambar secara otomatis. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> komponen `gatsby-plugin-image`, bukan `<img>`, untuk otomatis memuat gambar secara lambat. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponen `gatsby-plugin-image` dan setel properti `loading` ke `eager`. [<PERSON><PERSON><PERSON><PERSON> le<PERSON> lan<PERSON>](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Gunakan `Gatsby Script API` untuk menunda pemuatan skrip pihak ketiga yang tidak penting. [P<PERSON>jar<PERSON> lebih lanjut](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Gunakan plugin `PurgeCSS` `Gatsby` untuk menghapus aturan yang tidak digunakan dari stylesheet. [P<PERSON>jar<PERSON> lebih lanjut](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Gunakan `Webpack Bundle Analyzer` untuk mendeteksi kode JavaScript yang tidak digunakan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Mengonfigurasi cache untuk aset yang tidak dapat diubah. [Pelajari lebih lanjut](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> komponen `gatsby-plugin-image`, bukan `<img>`, untuk menyesuaikan kualitas gambar. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponen `gatsby-plugin-image` untuk menyetel `sizes` yang benar. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Sebaiknya upload GIF ke layanan yang akan menyediakannya untuk disematkan sebagai video HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) atau layanan yang otomatis mengonversi gambar yang diupload ke format optimal."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instal [plugin pemuatan lambat di <PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) yang menyediakan kemampuan untuk menunda pemuatan gambar di bagian halaman yang belum ditampilkan, atau beralihlah ke template yang menyediakan fungsi tersebut. Mulai dari Joomla 4.0, se<PERSON>a gambar baru akan [otomatis](https://github.com/joomla/joomla-cms/pull/30748) mendapatkan atribut `loading` dari core."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Terdapat sejumlah plugin di Joomla yang dapat membantu Anda [menyejajarkan aset penting](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) atau [menunda resource yang kurang penting](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Harap berhati-hati karena pengoptimalan yang disediakan oleh plugin ini dapat merusak fitur template atau plugin, sehingga Anda akan perlu mengujinya secara menyeluruh."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> spesif<PERSON> template, ekstensi, dan server berkontribusi pada waktu respons server. Sebaiknya cari template yang lebih optimal, pilih ekstensi pengoptimalan dengan hati-hati, dan/atau upgrade server And<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Sebaiknya tampilkan kutipan dalam kategori artikel (misal<PERSON> melalui link baca selengkapnya), kurangi jumlah artikel yang ditampilkan pada halaman yang ada, bagi postingan panjang menjadi beberapa halaman, atau gunakan plugin untuk memuat komentar dengan lambat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [e<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) dapat mempercepat situs Anda dengan men<PERSON>, me<PERSON><PERSON><PERSON><PERSON>, dan mengompresi gaya CSS. Ada juga template yang memberikan fungsi ini."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [e<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) dapat mempercepat situs Anda dengan men<PERSON>, me<PERSON><PERSON><PERSON><PERSON>, dan mengompresi skrip. Ada juga template yang memberikan fungsi ini."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Sebaiknya kurangi atau ubah jumlah [eks<PERSON><PERSON>](https://extensions.joomla.org/) yang memuat CSS yang tidak digunakan di halaman Anda. Untuk mengidentifikasi ekstensi yang menambahkan CSS tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi tema/plugin yang bertanggung jawab dari URL stylesheet. Cari plugin yang memiliki banyak stylesheet dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan stylesheet ke antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Sebaiknya kurangi atau ubah jumlah [eks<PERSON><PERSON>](https://extensions.joomla.org/) yang memuat JavaScript yang tidak digunakan di halaman Anda. Untuk mengidentifikasi plugin yang menambahkan JS tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi ekstensi yang bertanggung jawab dari URL skrip. Cari ekstensi dengan banyak skrip dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Ekstensi sebaiknya hanya menambahkan skrip ke antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Baca [<PERSON><PERSON>](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin pengoptimalan gambar](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) yang mengompresi gambar Anda dengan tetap mempertahankan kualitas."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin gambar responsif](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) untuk menggunakan gambar yang responsif pada konten Anda."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "<PERSON><PERSON> dapat mengaktifkan kompresi teks dengan mengaktifkan Gzip Page Compression di Joomla (System > Global configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Jika Anda tidak memaketkan aset JavaScript, pertimbangkan untuk menggunakan [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Nonaktifkan [paket dan minifikasi JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) bawaan <PERSON>, dan pertimbangkan untuk menggunakan [baler](https://github.com/magento/baler/) saja."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Tetap<PERSON> `@font-display` saat [menentukan font kustom](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Pertimbangkan untuk menelusuri [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) guna menemukan berbagai ekstensi pihak ketiga untuk memanfaatkan format gambar terbaru."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Pertimbangkan untuk mengubah template produk dan katalog Anda untuk memanfaatkan fitur [pemuatan lambat](https://web.dev/native-lazy-loading) platform web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> [Varnish integration](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) dari <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Aktifkan opsi \"Minifikasi File CSS\" di setelan Developer toko <PERSON>. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [Terser](https://www.npmjs.com/package/terser) untuk meminifikasi semua aset JavaScript dari deployment konten statis, dan nonaktifkan fitur minifikasi bawaan."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Nonaktifkan [paket <PERSON>Script](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) b<PERSON>an <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Pertimbangkan untuk menelusuri [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) guna menemukan berbagai ekstensi pihak ketiga untuk mengoptimalkan gambar."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Petunjuk resource preconnect atau dns-prefetch dapat ditambahkan dengan [mengubah tata letak tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Tag `<link rel=preload>` dapat ditambahkan dengan [mengubah tata letak tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> komponen `next/image`, bukan `<img>`, untuk mengoptimalkan format gambar secara otomatis. [Pelajari lebih lanjut](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> komponen `next/image`, bukan `<img>`, untuk otomatis memuat gambar secara lambat. [P<PERSON>jar<PERSON> lebih lanjut](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponen `next/image` dan setel \"priority\" ke benar (true) untuk melakukan pramuat gambar LCP. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON> komponen `next/script` untuk menunda pemuatan skrip pihak ketiga yang tidak kritis. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON> komponen `next/image` untuk memastikan gambar selalu memiliki ukuran yang sesuai. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Se<PERSON>ik<PERSON> siapkan `PurgeCSS` di konfigurasi `Next.js` untuk menghapus aturan yang tidak digunakan dari stylesheet. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Gunakan `Webpack Bundle Analyzer` untuk mendeteksi kode JavaScript yang tidak digunakan. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan `Next.js Analytics` untuk mengukur performa aplikasi Anda yang sebenarnya. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurasi cache untuk aset dan halaman `Server-side Rendered` (SSR) yang tidak dapat diubah. [Pelajari lebih lanjut](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> komponen `next/image`, bukan `<img>`, untuk menyesuaikan kualitas gambar. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponen `next/image` untuk menyetel `sizes` yang benar. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Aktifkan kompresi di server Next.js Anda. [<PERSON><PERSON><PERSON><PERSON> le<PERSON> lan<PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Hubungi pengelola akun Anda untuk mengaktifkan [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Mengonfigurasinya akan memprioritaskan dan mengoptimalkan performa rendering halaman Anda."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Gunakan opsi [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) di NitroPack untuk menetapkan nilai yang diinginkan untuk aturan tampilan font CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) untuk otomatis mengonversi gambar ke WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Tunda gambar di luar layar dengan mengaktifkan [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Aktifkan [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) di NitroPack untuk waktu pemuatan awal yang lebih cepat."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Tingkatkan waktu respons server dan optimalkan performa yang dirasakan dengan mengaktifkan [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Aktifkan [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) di setelan Cache untuk mengurangi ukuran file CSS, HTML, dan <PERSON> Anda agar waktu pemuatan menjadi lebih cepat."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Aktifkan [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) di setelan Cache untuk mengurangi ukuran file JS, HTML, dan CSS Anda agar waktu pemuatan menjadi lebih cepat."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Aktifkan [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) untuk menghapus aturan CSS yang tidak berlaku untuk halaman ini."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) di NitroPack untuk menunda pemuatan skrip hingga diperlukan."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "<PERSON>uka fitur [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) di menu `Caching` dan sesuaikan waktu habis masa berlaku cache halaman Anda untuk meningkatkan waktu pemuatan dan pengalaman pengguna."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dan mengon<PERSON>i gambar menjadi WebP dengan mengaktifkan setelan [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Aktifkan [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) untuk mengoptimalkan gambar Anda secara preemptive dan membuatnya cocok dengan dimensi penampung yang menampilkannya di semua perangkat."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Gunakan [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) di NitroPack untuk mengurangi ukuran file yang dikirim ke browser."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan setel `format=\"webp\"`. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan setel `loading=\"lazy\"` untuk gambar di bagian halaman yang belum ditampilkan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan tentukan `preload` untuk gambar LCP. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan tentukan `width` dan `height` eksplisit. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan setel `quality` yang se<PERSON>. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponen `nuxt/image` dan setel `sizes` yang se<PERSON>. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Ganti animasi GIF dengan video](https://web.dev/replace-gifs-with-videos/) untuk pemuatan halaman yang lebih cepat dan sebaiknya gunakan format file modern seperti [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) atau [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) untuk meningkatkan efisiensi kompresi lebih dari 30% dibandingkan codec video tercanggih saat ini, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Se<PERSON><PERSON><PERSON> gunakan [plugin](https://octobercms.com/plugins?search=image) atau layanan yang otomatis mengonversi gambar yang diupload ke format optimal. [Gambar WebP lossless](https://developers.google.com/speed/webp) berukuran 26% lebih kecil daripada PNG dan 25-34% lebih kecil daripada gambar JPEG yang sebanding pada indeks kualitas SSIM yang setara. [AVIF](https://jakearchibald.com/2020/avif-has-landed/) juga bisa dipertimbangkan sebagai format gambar generasi berikutnya."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Sebaiknya instal [plugin pemuatan lambat gambar](https://octobercms.com/plugins?search=lazy) yang menyediakan kemampuan untuk menunda pemuatan gambar di bagian halaman yang belum ditampilkan, atau beralihlah ke tema yang menyediakan fungsi tersebut. <PERSON><PERSON> itu, sebaik<PERSON> gunakan [plugin AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Terdapat banyak plugin yang membantu [menyejajarkan aset penting](https://octobercms.com/plugins?search=css). Plugin ini dapat merusak plugin lain, se<PERSON><PERSON> Anda perlu mengujinya secara menyeluruh."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> spesif<PERSON> tema, plugin, dan server berkontribusi pada waktu respons server. Sebaiknya cari tema yang lebih optimal, pilih plugin pengoptimalan dengan hati-hati, dan/atau upgrade server. CMS Oktober juga memungkinkan developer men<PERSON><PERSON>kan [`Queues`](https://octobercms.com/docs/services/queues) untuk menunda pemrosesan tugas yang memakan waktu lama, seperti pengiriman email. Ini mempercepat permintaan web secara drastis."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Sebaiknya tampilkan kutipan dalam daftar postingan (misalnya menggunakan tombol `show more`), kurangi jumlah postingan yang ditampilkan pada halaman web yang ada, bagi postingan panjang menjadi beberapa halaman web, atau gunakan plugin untuk memuat komentar dengan lambat."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Terdapat banyak [plugin](https://octobercms.com/plugins?search=css) yang dapat mempercepat situs dengan menyambungkan, memini<PERSON><PERSON>i, dan mengompresi gaya. Menggunakan proses build untuk melakukan minifikasi ini di tahap awal dapat mempercepat pengembangan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Terdapat banyak [plugin](https://octobercms.com/plugins?search=javascript) yang dapat mempercepat situs dengan menyambungkan, memini<PERSON>kasi, dan mengompresi skrip. Menggunakan proses build untuk melakukan minifikasi ini di tahap awal dapat mempercepat pengembangan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Sebaiknya tinjau [plugin](https://octobercms.com/plugins) yang memuat CSS yang tidak digunakan di situs. Untuk mengidentifikasi plugin yang menambahkan CSS yang tidak diperlukan, jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Identifikasi tema/plugin yang bertanggung jawab dari URL stylesheet. Cari plugin dengan banyak stylesheet yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan stylesheet jika memang benar digunakan di halaman web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Sebaiknya tinjau [plugin](https://octobercms.com/plugins?search=javascript) yang memuat JavaScript yang tidak digunakan di halaman web. Untuk mengidentifikasi plugin yang menambahkan JavaScript yang tidak diperlukan, jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Identifikasi tema/plugin yang bertanggung jawab dari URL skrip. Cari plugin dengan banyak skrip yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan skrip jika memang benar digunakan di halaman web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Baca [cara mencegah permintaan jaringan yang tidak diperlukan dengan Cache HTTP](https://web.dev/http-cache/#caching-checklist). Terdapat banyak [plugin](https://octobercms.com/plugins?search=Caching) yang dapat digunakan untuk mempercepat proses penyimpanan ke cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin pengoptimalan gambar](https://octobercms.com/plugins?search=image) untuk mengompresi gambar dengan tetap mempertahankan kualitas."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Upload gambar langsung di pengelola media untuk memastikan ukuran gambar yang diperlukan tersedia. Sebaiknya gunakan [filter ubah ukuran](https://octobercms.com/docs/markup/filter-resize) atau [plugin pengubah ukuran gambar](https://octobercms.com/plugins?search=image) untuk memastikan penggunaan ukuran gambar yang optimal."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Aktifkan kompresi teks di konfigurasi server web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Sebaiknya gunakan library “windowing” seperti `react-window` untuk meminimalkan jumlah node DOM yang dibuat jika Anda merender banyak elemen berulang pada halaman. [Pelajari lebih lanjut](https://web.dev/virtualize-long-lists-react-window/). Selain itu, minimalkan render ulang yang tidak perlu menggunakan [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent), atau [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) dan [<PERSON><PERSON> e<PERSON>k](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) hanya sampai dependensi tertentu berubah jika Anda menggunakan hook `Effect` untuk meningkatkan performa runtime."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Jika Anda menggunakan React Router, minimalkan penggunaan komponen `<Redirect>` untuk [navigasi rute](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Jika Anda merender komponen React pada sistem server, pertimbangkan untuk menggunakan `renderToPipeableStream()` atau `renderToStaticNodeStream()` agar klien dapat menerima dan mengisi beberapa bagian markup yang berbeda, bukan sekaligus. [Pelajari lebih lanjut](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Jika sistem build otomatis meminifikasi file CSS, pastikan <PERSON>a men-deploy build produksi aplikasi Anda. Anda dapat memeriksanya dengan ekstensi React Developer Tools. [Pelajari lebih lanjut](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Jika sistem build otomatis meminifikasi file J<PERSON>, pastikan <PERSON>a men-deploy build produksi aplikasi Anda. Anda dapat memeriksanya dengan ekstensi React Developer Tools. [Pelajari lebih lanjut](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Jika Anda tidak melakukan rendering sistem server, [pisahkan paket JavaScript Anda](https://web.dev/code-splitting-suspense/) dengan `React.lazy()`. Atau, pisahkan kode menggunakan library pihak ketiga seperti [komponen yang dapat dimuat](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Gunakan React DevTools Profiler, yang memanfaatkan Profiler API untuk mengukur performa rendering komponen Anda. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Tempatkan video di dalam `VideoBoxes`, sesuaikan menggunakan `Video Masks` atau tambahkan `Transparent Videos`. [Pelajar<PERSON> lebih lan<PERSON>t](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Upload gambar menggunakan `Wix Media Manager` untuk memastikan gambar tersebut otomatis ditayangkan sebagai WebP. Temukan [banyak cara untuk mengoptimalkan](https://support.wix.com/en/article/site-performance-optimizing-your-media) media di situs Anda."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Saat [menambahkan kode pihak ketiga](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) di tab `Custom Code` pada dasbor situs, pastikan kode ditangguhkan atau dimuat di akhir isi kode. <PERSON><PERSON> memu<PERSON>, gunakan [integrasi](https://support.wix.com/en/article/about-marketing-integrations) Wix untuk menyematkan alat pemasaran di situs Anda. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix menggunakan CDN dan cache untuk memberikan respons secepat mungkin bagi sebagian besar pengunjung. Sebaiknya [aktifkan cache secara manual](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) untuk situs Anda, terutama jika menggunakan `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Tinjau kode pihak ketiga yang telah Anda tambahkan ke situs di tab `Custom Code` pada dasbor situs dan hanya pertahankan layanan yang diperlukan bagi situs Anda. [Ke<PERSON><PERSON> selengkapnya](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Sebaiknya upload GIF ke layanan yang akan menyediakannya untuk disematkan sebagai video HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Se<PERSON><PERSON><PERSON> gunakan plugin [Performance Lab](https://wordpress.org/plugins/performance-lab/) untuk mengonversi gambar JPEG yang diupload secara otomatis menjadi WebP, jika didukung."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instal [plugin pemuatan lambat di WordPress ](https://wordpress.org/plugins/search/lazy+load/) yang menyediakan kemampuan untuk menunda pemuatan gambar di bagian halaman yang belum ditampilkan, atau beralihlah ke tema yang menyediakan fungsi tersebut. Sebaiknya juga gunakan [plugin AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Terdapat sejumlah plugin di WordPress yang dapat membantu Anda [menyejajarkan aset penting](https://wordpress.org/plugins/search/critical+css/) atau [menunda resource yang tidak penting](https://wordpress.org/plugins/search/defer+css+javascript/). Harap berhati-hati karena pengoptimalan yang disediakan oleh plugin ini dapat merusak fitur tema atau plugin, sehingga Anda cenderung perlu mengubah kode."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON> spes<PERSON> tema, plugin, dan server berkontribusi pada waktu respons server. Sebaiknya cari tema yang lebih optimal, pilih plugin pengopt<PERSON><PERSON> dengan hati-hati, dan/atau upgrade server <PERSON><PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Sebaiknya tampilkan kutipan dalam daftar postingan (misal<PERSON> melalui tag lainnya), kurangi jumlah postingan yang ditampilkan pada halaman yang ada, bagi postingan panjang menjadi beberapa halaman, atau gunakan plugin untuk menunda pemuatan (lazy-load) komentar."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [plugin WordPress](https://wordpress.org/plugins/search/minify+css/) dapat mempercepat situs Anda dengan menggab<PERSON>kan, memini<PERSON><PERSON><PERSON>, dan mengompresi gaya Anda. Anda juga dapat menggunakan proses pembuatan build untuk melakukan minifikasi di tahap awal jika memungkinkan."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [plugin WordPress](https://wordpress.org/plugins/search/minify+javascript/) dapat mempercepat situs Anda dengan menggabungkan, memini<PERSON><PERSON>i, dan mengompresi skrip. Anda juga dapat menggunakan proses pembuatan build untuk melakukan minifikasi di awal jika mungkin."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Sebaiknya kurangi atau ubah jumlah [plugin WordPress](https://wordpress.org/plugins/) yang memuat CSS yang tidak digunakan di halaman Anda. Untuk mengidentifikasi plugin yang menambahkan CSS tidak relevan, coba jalankan [cakupan kode](https://developer.chrome.com/docs/devtools/coverage/) di Chrome DevTools. Anda dapat mengidentifikasi tema/plugin yang bertanggung jawab dari URL stylesheet. Cari plugin yang memiliki banyak stylesheet dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan stylesheet ke antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Sebaiknya kurangi atau ubah jumlah [plugin WordPress](https://wordpress.org/plugins/) yang memuat JavaScript yang tidak digunakan di halaman Anda. Untuk mengidentifikasi plugin yang menambahkan JS tidak relevan, coba jalankan [cakupan kode](https://developer.chrome.com/docs/devtools/coverage/) di Chrome DevTools. Anda dapat mengidentifikasi tema/plugin yang bertanggung jawab dari URL skrip. Cari plugin dengan banyak skrip dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan skrip ke dalam antrean jika memang benar digunakan di halaman."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Baca [<PERSON><PERSON> di WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin WordPress untuk pengoptimalan gambar](https://wordpress.org/plugins/search/optimize+images/) yang mengompresi gambar Anda dengan tetap mempertahankan kualitas."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Upload gambar langsung melalui [koleksi media](https://wordpress.org/support/article/media-library-screen/) untuk memastikan ukuran gambar yang diperlukan tersedia, lalu masukkan koleksi media atau gunakan widget gambar untuk memastikan ukuran gambar optimal digunakan (termasuk untuk titik henti sementara responsif). Hindari menggunakan gambar `Full Size`, kecuali dimensinya memungkinkan untuk digunakan. [Pelajari Lebih Lanjut](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Anda dapat mengaktifkan kompresi teks di konfigurasi server web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Akt<PERSON><PERSON> 'Imagify' dari tab Pengoptimalan Gambar di 'WP Rocket' untuk mengonversi gambar Anda ke WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Aktifkan [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) di WP Rocket untuk memperbaiki rekomendasi ini. Fitur ini menunda pemuatan gambar hingga pengunjung men-scroll halaman ke bawah dan benar-benar perlu melihatnya."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Aktifkan [Hapus CSS yang Tidak Digunakan](https://docs.wp-rocket.me/article/1529-remove-unused-css) dan [Muat JavaScript yang ditangguhkan](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) di 'WP Rocket' untuk mengatasi rekomendasi ini. Fitur ini masing-masing akan mengoptimalkan file CSS dan JavaScript agar tidak memblokir rendering halaman Anda."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Aktifkan [Minifikasi file CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) di 'WP Rocket' untuk memperbaiki masalah ini. Ruang dan komentar apa pun di file CSS situs akan dihapus agar ukuran file lebih kecil dan lebih cepat didownload."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Aktifkan [Minifikasi file JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) di 'WP Rocket' untuk memperbaiki masalah ini. Ruang kosong dan komentar akan dihapus dari file JavaScript agar ukuran file lebih kecil dan lebih cepat didownload."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Aktifkan [Hapus CSS yang Tidak Digunakan](https://docs.wp-rocket.me/article/1529-remove-unused-css) di 'WP Rocket' untuk memperbaiki masalah ini. Tindakan ini mengurangi ukuran halaman dengan menghapus semua CSS dan stylesheet yang tidak digunakan dan hanya mempertahankan CSS yang digunakan untuk setiap halaman."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Aktifkan [Tunda eksekusi JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) di 'WP Rocket' untuk memperbaiki masalah ini. Tindakan ini akan meningkatkan pemuatan halaman Anda dengan menunda eksekusi skrip hingga interaksi pengguna. Jika situs Anda memiliki iframe, <PERSON>a dapat menggunakan [LazyLoad untuk iframe dan video](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) serta [Ganti iframe YouTube dengan gambar pratinjau](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) dari <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Aktifkan 'Imagify' dari tab Pengoptimalan Gambar di 'WP Rocket' dan jalankan Pengoptimalan Massal untuk mengompresi gambar Anda."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Gun<PERSON>n [Ambil Data Permintaan DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) di 'WP Rocket' untuk menambahkan \"dns-prefetch\" dan mempercepat koneksi dengan domain eksternal. <PERSON><PERSON> it<PERSON>, 'WP Rocket' secara otomatis menambahkan \"preconnect\" ke [domain Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) dan <PERSON>(S) apa pun yang ditambahkan melalui fitur [Aktifkan CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Untuk memperbaiki masalah terkait font ini, aktifkan [Hapus CSS yang Tidak Digunakan](https://docs.wp-rocket.me/article/1529-remove-unused-css) di 'WP Rocket'. Font penting di situs Anda akan mendapatkan prioritas pramuat."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "Ciutkan tampilan"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latensi jalur kritis maksimal:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Salin J<PERSON>"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Tombol Tema Gelap"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Cetak Lengkap"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Cetak <PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Simpan sebagai Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Simpan sebagai HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Simpan sebagai JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Lihat Trace yang Tidak Di-throttle"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Buka di Penampil"}, "report/renderer/report-utils.js | errorLabel": {"message": "Error!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Error laporan: tidak ada informasi audit"}, "report/renderer/report-utils.js | expandView": {"message": "Luaskan tampilan"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "<PERSON><PERSON>a"}, "report/renderer/report-utils.js | footerIssue": {"message": "Laporkan masalah"}, "report/renderer/report-utils.js | hide": {"message": "Sembunyikan"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Data Lab"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON>is [Lighthouse](https://developers.google.com/web/tools/lighthouse/) untuk halaman saat ini di jaringan seluler teremulasi. <PERSON><PERSON> adalah hasil perkiraan dan dapat berbeda-beda."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON>em tambahan untuk diperiksa secara manual"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Tidak berlaku"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "<PERSON>uka di tab baru"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Lulus audit"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> yang diperbarui di Chrome](https://developer.chrome.com/blog/update-install-criteria), Lighthouse akan menghentikan penggunaan kategori PWA pada rilis mendatang. Lihat [dokumentasi PWA yang telah diperbarui](https://developer.chrome.com/docs/devtools/progressive-web-apps/) untuk pengujian PWA mendatang."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON><PERSON> halaman awal"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Snapshot waktu tertentu"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Rentang waktu interaksi pengguna"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Throttle kustom"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Desktop Emulasi"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Moto G Power Teremulasi"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Tidak ada emulasi"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versi <PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Daya CPU/Memori yang tidak di-throttle"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Throttling CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Throttling jaringan"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON> layar"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Agen pen<PERSON>una (jaringan)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Sesi satu halaman"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Data ini diambil dari sesi satu halaman, tidak seperti data kolom yang merangkum banyak sesi."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Throttle 4G lambat"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Tidak dikenal"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Tampilkan audit yang relevan dengan:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Ciutkan cuplikan"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Luask<PERSON> cup<PERSON>an"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Tampilkan resource pihak ketiga"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Disediakan oleh lingkungan"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Ada masalah yang memengaruhi jalannya Lighthouse ini:"}, "report/renderer/report-utils.js | unattributable": {"message": "Tidak dapat diatribusikan"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "<PERSON><PERSON> ad<PERSON>h hasil perkiraan dan dapat bervariasi. [Skor performa dihitung](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) secara langsung dari metrik ini."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Lihat Trace"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Lulus audit tetapi dengan per<PERSON>"}, "report/renderer/report-utils.js | warningHeader": {"message": "Peringatan: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Modul <PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Byte Resource"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON>pilkan/Sembunyikan Tabel"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Byte yang Tidak Di<PERSON>n"}}