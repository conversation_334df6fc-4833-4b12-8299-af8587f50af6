{"core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON>law<PERSON><PERSON> dostępu umożliwiają szybkie ustawienie fokusu na określonej części strony. Aby nawigacja działała dobrze, każdy klawisz dostępu musi być unikalny. [Więcej informacji o kluczach dostępu](https://dequeuniversity.com/rules/axe/4.8/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` nie są unikalne"}, "core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` są unikalne"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> `role` ARIA obsługuje podzbiór atrybutów `aria-*`. Brak ich dopasowania skutkuje niepoprawnością atrybutów `aria-*`. [Jak dopasowywać atrybuty ARIA do ich ról](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atrybuty `[aria-*]` nie pasują do swoich ról"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atrybuty `[aria-*]` od<PERSON><PERSON><PERSON><PERSON><PERSON> swoim rolom"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> atrybutom `role` ARIA technologie wspomagające osoby z niepełnosprawnością umożliwiają poznanie roli każdego elementu na stronie internetowej. <PERSON><PERSON><PERSON> warto<PERSON> `role` są błędnie zapisane, nie są istniejącymi wartościami ARIA `role` ani rolami abstrakcyjnymi, to użytkownicy technologii wspomagających nie otrzymają informacji o przeznaczeniu danego elementu. [Więcej informacji o rolach ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Wartości przypisane do atrybutu `role=\"\"` nie są prawidłowymi rolami ARIA."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Wartości przypisane do `role=\"\"` są prawidłowymi rolami ARIA."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Gdy element nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go nazwą ogólną, przez co jest on bezużyteczny dla ich użytkowników. [Jak ułatwić dostęp do elementów poleceń](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementy `button`, `link` i `menuitem` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementy `button`, `link` i `menuitem` mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Elementy okien ARIA, które nie zawierają nazw na potrzeby ułatwień dostępu, mogą uniemożliwiać użytkownikom czytników ekranu rozpoznanie zastosowania tych elementów. [Więcej informacji o ułatwieniach dostępu w elementach okien ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Elementy z atrybutami `role=\"dialog\"` lub `role=\"alertdialog\"` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Elementy z atrybutami `role=\"dialog\"` lub `role=\"alertdialog\"` mają nazwy na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Technologie wspomagające, takie jak czy<PERSON>niki e<PERSON>nu, m<PERSON><PERSON> d<PERSON><PERSON>, gdy dokument `<body>` ma ustawiony atrybut `aria-hidden=\"true\"`. [Jak atrybut `aria-hidden` wpływa na treść dokumentu](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Dokument `<body>` zawiera atrybut `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Dokument `<body>` nie zaw<PERSON> atrybutu `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Element `[aria-hidden=\"true\"]` zawiera interaktywne elementy podrzędne z możliwością zaznaczenia, które są niedostępne dla użytkowników technologii wspomagających, takich jak czytniki ekranu. [Jak element `aria-hidden` wpływa na elementy, które można zaznaczyć](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementy `[aria-hidden=\"true\"]` zawierają elementy podrzędne, które można zaznaczyć"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elementy `[aria-hidden=\"true\"]` nie zawierają elementów podrzędnych, które można zaznaczyć"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Gdy pole do wprowadzania danych nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają je nazwą ogólną, przez co jest ono bezużyteczne dla ich użytkowników. [Więcej informacji o etykietach pól do wprowadzania danych](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Pola do wprowadzania danych ARIA nie mają nazw na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Pola do wprowadzania danych ARIA mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Gdy element meter nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go nazwą ogólną, przez co jest on bezużyteczny dla ich użytkowników. [<PERSON><PERSON><PERSON> si<PERSON>, jak nazywać elementy `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Elementy ARIA `meter` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Elementy ARIA `meter` mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Gdy element `progressbar` nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go nazwą ogólną, przez co jest on bezużyteczny dla ich użytkowników. [Jak dodawać etykiety do elementów `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Elementy ARIA `progressbar` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Elementy ARIA `progressbar` mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Niekt<PERSON>re role ARIA mają atrybuty wymagane, które opisują stan elementu na potrzeby czytników ekranu. [Więcej informacji o rolach i wymaganych atrybutach](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementy z atrybutem `[role]` nie mają wszystkich wymaganych atrybutów `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Elementy `[role]` mają wszystkie wymagane atrybuty `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Niektóre role nadrz<PERSON>dne ARIA muszą zawierać określone role podrzędne, aby poprawnie realizować funkcje ułatwień dostępu. [Więcej informacji o rolach i wymaganych elementach podrzędnych](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementy z atrybutem ARIA `[role]`, których elementy podrzędne muszą zawierać określony atrybut `[role]`, nie mają niektórych lub wszystkich tych wymaganych elementów podrzędnych."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementy z atrybutem ARIA `[role]`, których elementy podrzędne muszą zawierać określony atrybut `[role]`, mają wszystkie wymagane elementy podrzędne."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Niektóre role podrzędne ARIA muszą znajdować się wewnątrz określonych ról nadrzędnych, aby poprawnie realizować funkcje ułatwień dostępu. [Więcej informacji o rolach ARIA i wymaganych elementach nadrzędnych](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Elementy `[role]` nie znajdują się wewnątrz wymaganych elementów nadrzędnych"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Elementy `[role]` zna<PERSON><PERSON><PERSON><PERSON> się wewnątrz wymaganych elementów nadrzędnych"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Role ARIA muszą mieć p<PERSON>, aby poprawnie realizować funkcje ułatwień dostępu. [Więcej informacji o prawidłowych rolach ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` s<PERSON>e"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` s<PERSON>e"}, "core/audits/accessibility/aria-text.js | description": {"message": "Dodanie atrybutu `role=text` wokół węzła tekstowego ujętego w znaczniki umożliwi funkcji VoiceOver rozpoznanie go jako 1 wyrażenia. Możliwe do zaznaczenia elementy podrzędne nie będą jednak odczytywane. [Więcej informacji o atrybucie `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Elementy z atrybutem `role=text` mają możliwe do zaznaczenia elementy podrzędne."}, "core/audits/accessibility/aria-text.js | title": {"message": "Elementy z atrybutem `role=text` nie mają możliwych do zaznaczenia elementów podrzędnych."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Gdy pole przełączania nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają je nazwą ogólną, przez co jest ono bezużyteczne dla ich użytkowników. [Więcej informacji o polach przełączania](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Pola przełączania ARIA nie mają nazw na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Pola przełączania ARIA mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Gdy element tooltip nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go nazwą ogólną, przez co jest on bezużyteczny dla ich użytkowników. [<PERSON><PERSON><PERSON> si<PERSON>, jak nazywać elementy `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Elementy ARIA `tooltip` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Elementy ARIA `tooltip` mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Gdy element `treeitem` nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go nazwą ogólną, przez co jest on bezużyteczny dla ich użytkowników. [Więcej informacji o dodawaniu etykiet do elementów `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Elementy ARIA `treeitem` nie mają nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Elementy ARIA `treeitem` mają nazwy na potrzeby ułatwień dostępu"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Technologie wspomagające, takie jak czytniki ekranu, nie potrafią interpretować atrybutów ARIA o nieprawidłowej wartości. [Więcej informacji o prawidłowych wartościach atrybutów ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atrybuty `[aria-*]` nie maj<PERSON> p<PERSON> war<PERSON>"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atrybuty `[aria-*]` maj<PERSON> p<PERSON>e warto<PERSON>ci"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Technologie wspomagające, takie jak czytniki ekranu, nie potrafią interpretować atrybutów ARIA o nieprawidłowych nazwach. [Więcej informacji o prawidłowych atrybutach ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atrybuty `[aria-*]` są niep<PERSON>idłowe lub są w nich literówki"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atrybuty `[aria-*]` są prawidłowe i nie ma w nich literówek"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Nieprawidłowe elementy"}, "core/audits/accessibility/button-name.js | description": {"message": "Gdy przycisk nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go jako „przycisk”, przez co jest on bezużyteczny dla ich użytkowników. [Jak ułatwić dostęp do przycisków](https://dequeuniversity.com/rules/axe/4.8/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Przyciski nie mają nazw dostępnych dla czytników ekranu"}, "core/audits/accessibility/button-name.js | title": {"message": "Przyciski mają nazwy dostępne dla czytników ekranu"}, "core/audits/accessibility/bypass.js | description": {"message": "Dodanie sposobu na ominięcie powtarzających się treści ułatwia nawigację na stronie za pomocą klawiatury. [Więcej informacji o blokach omijania](https://dequeuniversity.com/rules/axe/4.8/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Strona nie zawiera nagłówka, linku pomijającego ani regionu orientacyjnego"}, "core/audits/accessibility/bypass.js | title": {"message": "Strona zawiera nagłówek, link pomijający lub region orientacyjny"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Wielu użytkowników ma problemy z czytaniem tekstu o niskim kontraście. [Jak zapewnić odpowiedni kontrast kolorów](https://dequeuniversity.com/rules/axe/4.8/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Kolory tła i pierwszego planu mają niewystarczający współczynnik kontrastu."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Kolory tła i pierwszego planu mają wystarczający współczynnik kontrastu"}, "core/audits/accessibility/definition-list.js | description": {"message": "Gdy listy definicji nie mają właś<PERSON><PERSON>j struktury, czytniki ekranu mogą odczytywać je niedokładnie lub błędnie. [Jak tworzyć listy definicji o prawidłowej strukturze](https://dequeuniversity.com/rules/axe/4.8/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementy `<dl>` nie zawierają tylko właściwie uporządkowanych grup elementów `<dt>` i `<dd>` oraz elementów `<script>`, `<template>` lub `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Elementy `<dl>` zawierają tylko właściwie uporządkowane grupy elementów `<dt>` i `<dd>` oraz elementy `<script>`, `<template>` lub `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Elementy listy definicji (`<dt>` i `<dd>`) muszą znajdować się wewnątrz nadrzędnego elementu `<dl>`, aby czytniki ekranu mogły je poprawnie odczytać. [Jak tworz<PERSON>ć listy definicji o prawidłowej strukturze](https://dequeuniversity.com/rules/axe/4.8/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementy listy definicji nie znajdują się wewnątrz elementów `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Elementy listy definicji znajdują się wewnątrz elementów `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Tytuł informuje użytkowników czytnika ekranu o ogólnej zawartości strony, a użytkownicy wyszukiwarki mogą dowiedzieć się z niego, czy strona zawiera szukane informacje. [Więcej informacji o tytułach dokumentów](https://dequeuniversity.com/rules/axe/4.8/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "W dokumencie nie ma elementu `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument zawiera element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Wszystkie elementy, kt<PERSON>re moż<PERSON>, mus<PERSON><PERSON> mieć unikalny atrybut `id`, aby były widoczne dla technologii wspomagających. [<PERSON><PERSON> duplikaty elementów `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atrybuty `[id]` aktywnych elementów, które można zaznaczyć, nie są unikalne"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atrybuty `[id]` aktywnych elementów, które można zaznaczyć, są unikalne"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Wartość identyfikatora ARIA musi by<PERSON>, aby technologie wspomagające nie pominęły innych wystąpień. [<PERSON><PERSON> zduplikowane identyfikatory ARIA](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Identyfikatory ARIA nie są unikalne"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Identyfikatory ARIA są unikalne"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Nagłówki bez treści lub z tekstem bez ułatwień dostępu uniemożliwiają użytkownikom czytników ekranu poznanie struktury strony. [Więcej informacji o nagłówkach](https://dequeuniversity.com/rules/axe/4.8/empty-heading)"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Elementy nagłówka nie zawierają treści."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Wszystkie elementy nagłówków zawierają treść."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Technologie wspomagające, takie jak czytniki ekranu, kt<PERSON><PERSON> używaj<PERSON> pier<PERSON>, ostat<PERSON><PERSON> lub wszyst<PERSON>ch etykiet, mogą błędnie interpretować pola formularzy z wieloma etykietami. [Jak korzystać z etykiet formularzy](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Pola formularza mają wiele etykiet"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Żadne pola formularza nie mają wielu etykiet"}, "core/audits/accessibility/frame-title.js | description": {"message": "Tytuły ramek służą użytkownikom czytników ekranu jako opisy zawartości ramek. [Więcej informacji o tytułach ramek](https://dequeuniversity.com/rules/axe/4.8/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Element `<frame>` lub `<iframe>` nie ma tytułu"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementy `<frame>` i `<iframe>` mają tytuł"}, "core/audits/accessibility/heading-order.js | description": {"message": "Nagłówki w prawidłowej kole<PERSON>, które nie pomijają poziomów, odwzorow<PERSON>ją semantyczną strukturę strony. Dzięki temu poruszanie się po nich i korzystanie z ich treści za pomocą technologii wspomagających jest łatwiejsze. [Więcej informacji o kolejności nagłówków](https://dequeuniversity.com/rules/axe/4.8/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elementy nagłówków nie pojawiają się w kolejności malejącej"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elementy nagłówków pojawiają się w kolejności malejącej"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Je<PERSON><PERSON> strona nie ma atrybutu `lang`, czytnik ekranu przyj<PERSON>, że strona jest w języku do<PERSON>, kt<PERSON>ry użytkownik wybrał podczas konfigurowania czytnika. Jeśli strona nie jest w języku domy<PERSON>, czytnik ekranu może niepoprawnie wymawiać tekst strony. [Więcej informacji o atrybucie `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nie ma atrybutu `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ma atrybut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Określenie prawidłowego [języka w formacie BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaga czytnikom ekranu prawidłowo wymawiać tekst. [<PERSON><PERSON> kor<PERSON> z atrybutu `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` nie ma prawidłowej wartości atrybutu `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ma prawidłową wartość atrybutu `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "<PERSON><PERSON><PERSON> język strony internetowej nie jest jednoznacznie określony, czytnik ekranu może niepoprawnie wypowiadać dostępny na niej tekst. [Więcej informacji o atrybucie `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Element `<html>` nie ma atrybutu `[xml:lang]` z tym samym językiem podstawowym co atrybut `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Element `<html>` ma atrybut `[xml:lang]` z tym samym językiem podstawowym co atrybut `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Linki z tym samym miejscem docelowym powinny mieć ten sam opis, aby użytkownicy mogli łatwiej zrozumieć jego zastosowanie i zdecydować, czy chcą go kliknąć. [Więcej informacji o identycznych linkach](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identyczne linki nie mają tego samego zastosowania."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Identyczne linki mają to samo zastosowanie."}, "core/audits/accessibility/image-alt.js | description": {"message": "Elementy informacyjne powinny mi<PERSON> k<PERSON>, opisowy tekst zastępczy. Elementy dekoracyjne można zignoro<PERSON>, podając pusty atrybut alt. [Więcej informacji o atrybucie `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementy graficzne nie mają atrybutów `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elementy graficzne mają atrybuty `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Elementy informacyjne powinny mieć kr<PERSON>, opisowy tekst zastępczy. Te<PERSON><PERSON> zastępczy, kt<PERSON>ry jest taki sam jak tekst znajdujący się obok linku lub obrazu, mo<PERSON><PERSON> być myl<PERSON>cy dla użytkowników czytników ekranu, ponieważ zostanie odczytany dwukrotnie. [Więcej informacji o atrybucie `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Elementy graficzne mają atrybuty `[alt]`, które są nadmiarowym tekstem."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Elementy graficzne nie mają atrybutów `[alt]`, które są nadmiarowym tekstem."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Dobrze widoczny i wyraźny tekst dodany do przycisków wprowadzania danych może ułatwić użytkownikom czytnika ekranu zrozumienie funkcji danego przycisku. [Więcej informacji o przyciskach wprowadzania danych](https://dequeuniversity.com/rules/axe/4.8/input-button-name)"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Przyciski wprowadzania danych nie mają dobrze widocznego tekstu."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Przyciski wprowadzania danych mają dobrze widoczny tekst."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON> jako przy<PERSON> `<input>` <PERSON><PERSON><PERSON><PERSON><PERSON> jest obraz, warto dodać tekst zastę<PERSON>czy, aby u<PERSON><PERSON><PERSON><PERSON> użytkownikom czytnika ekranu zrozumienie, do czego ten przycisk służy. [Więcej informacji o tekście alternatywnym obrazu](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementy `<input type=\"image\">` nie mają teks<PERSON> `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementy `<input type=\"image\">` mają tekst `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Widoczne etykiety tekstowe, które nie są zgodne z nazwą na potrzeby ułatwień dostępu, mogą być mylące dla użytkowników czytników ekranu. [Więcej informacji o nazwach na potrzeby ułatwień dostępu](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Elementy z widocznymi etykietami tekstowymi nie mają pasujących nazw na potrzeby ułatwień dostępu."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Elementy z widocznymi etykietami tekstowymi mają pasujące nazwy na potrzeby ułatwień dostępu."}, "core/audits/accessibility/label.js | description": {"message": "Etykiety zapewniają prawidłowe odczytywanie kontrolek formularzy przez technologie wspomagające takie jak czytniki ekranu. [Więcej informacji o etykietach elementów formularzy](https://dequeuniversity.com/rules/axe/4.8/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Z elementami formularzy nie są powiązane etykiety"}, "core/audits/accessibility/label.js | title": {"message": "Z elementami formularzy są powiązane etykiety"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Główny punkt orientacyjny ułatwia użytkownikom czytników ekranu poruszanie się po stronie internetowej. [Więcej informacji o punktach orientacyjnych](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokument nie ma głównego punktu orientacyjnego."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokument ma główny punkt orientacyjny."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Wielu użytkowników ma problemy z czytaniem tekstu o niskim kontraście. Łatwy do odróżnienia tekst linku ma duże znaczenie dla osób niedowidzących. [Więcej informacji o wyróżnianiu linków](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Linki można odróżnić głównie na podstawie koloru."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Linki można odróżnić bez użycia koloru."}, "core/audits/accessibility/link-name.js | description": {"message": "Tekst linków (i tekst zastępczy obrazów używanych jako linki), kt<PERSON>ry jest charakterystyczny, unikalny i możliwy do wybrania, ułatwia nawigację użytkownikom czytników ekranu. [J<PERSON> ułatwić dostęp do linków](https://dequeuniversity.com/rules/axe/4.8/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Linki nie mają wyróżniających je nazw"}, "core/audits/accessibility/link-name.js | title": {"message": "Linki mają wyróżniające je nazwy"}, "core/audits/accessibility/list.js | description": {"message": "Czytniki ekranu odczytują listy w specjalny sposób. Właściwa struktura list pomaga czytnikom poprawnie odczytać tekst. [Więcej informacji o odpowiedniej strukturze list](https://dequeuniversity.com/rules/axe/4.8/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listy nie zawierają tylko elementów `<li>` i elementów skryptowych (`<script>` i `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Listy zawierają tylko elementy `<li>` i elementy skryptowe (`<script>` i `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Elementy list (`<li>`) muszą być zawarte w elementach nadrzędnych `<ul>`, `<ol>` lub `<menu>`, aby czytniki ekranu mogły je poprawnie odczytać. [Więcej informacji o odpowiedniej strukturze list](https://dequeuniversity.com/rules/axe/4.8/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementy list (`<li>`) nie znajdują się wewnątrz elementów nadrzędnych `<ul>`, `<ol>` lub `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Elementy list (`<li>`) znajdują się wewnątrz elementów nadrzędnych `<ul>`, `<ol>` lub `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Użytkownicy nie spodziewają się automatycznego odświeżania strony – powoduje ono powrót zaznaczenia na jej początek. Może to dezorientować i irytować użytkowników. [Więcej informacji o metatagu odświeżania](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument używa tagu `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument nie używa tagu `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Wyłączenie powiększania to problem dla użytkowników niedow<PERSON>, którzy muszą korzysta<PERSON> z powiększenia ekranu, aby dobrze widzieć zawartoś<PERSON> stron internetowych. [Więcej informacji o metatagu viewport](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "W elemencie `<meta name=\"viewport\">` jest u<PERSON><PERSON><PERSON>y atrybut `[user-scalable=\"no\"]` lub atrybut `[maximum-scale]` ma warto<PERSON>ć mniejszą niż 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "W elemencie `<meta name=\"viewport\">` nie jest używany atrybut `[user-scalable=\"no\"]`, a atrybut `[maximum-scale]` ma wartość nie mniejszą niż 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Czytniki ekranu nie potrafią tłumaczyć treści innych niż tekst. Dodanie do elementów `<object>` tekstu zastępczego pomaga czytnikom ekranu w przekazywaniu użytkownikom właściwego znaczenia. [Więcej informacji o tekście alternatywnym elementów `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementy `<object>` nie mają tekstu zastę<PERSON>czego"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementy `<object>` mają tekst zastępczy"}, "core/audits/accessibility/select-name.js | description": {"message": "Elementy formularzy bez odpowiednich etykiet mogą utrudniać korzystanie z witryny użytkownikom czytników ekranu. [Więcej informacji o elemencie `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Elementy do wybrania nie mają powiązanych z nimi elementów etykiet."}, "core/audits/accessibility/select-name.js | title": {"message": "Elementy do wybrania mają powiązane z nimi elementy etykiet."}, "core/audits/accessibility/skip-link.js | description": {"message": "Dołączenie linku pomijania może pomóc użytkownikom szybciej przechodzić do głównej treści. [Więcej informacji o linkach pomijania](https://dequeuniversity.com/rules/axe/4.8/skip-link)"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Linków pomijania nie można zaznaczyć."}, "core/audits/accessibility/skip-link.js | title": {"message": "Linki pomijania można zaznaczać."}, "core/audits/accessibility/tabindex.js | description": {"message": "Wartość większa niż 0 implikuje określoną wprost kolejność nawigacji. Chociaż takie rozwiązanie jest technicznie poprawne, często powoduje frustrację użytkowników technologii wspomagających. [Więcej informacji o atrybucie `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Niektóre elementy mają atrybut `[tabindex]` o wartości większej niż 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Żaden element nie ma wartości atrybutu `[tabindex]` większej niż 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Atrybut podsumowania powinien opisywać strukturę tabeli, a element `<caption>` powinien mieć tytuł ekranowy. Dokładne znaczniki tabeli pomagają użytkownikom czytników ekranu. [Więcej informacji o podsumowaniu i napisach](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Tabele zawierają tę samą treść w atrybucie podsumowania i w elemencie `<caption>.`."}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Tabele mają różne treści w atrybucie podsumowania i w elemencie `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. A<PERSON> poprawić wrażenia użytkowników czytnika ekranu, w tabelach używaj rzeczywistego elementu podpisu zamiast komórek z atrybutem `[colspan]`. [Więcej informacji o podpisach](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Do wskazywania podpisu tabele nie używają tagu `<caption>` zamiast komórek z atrybutem `[colspan]`."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Do wskazywania podpisu tabele używają elementu `<caption>` zamiast komórek z atrybutem `[colspan]`."}, "core/audits/accessibility/target-size.js | description": {"message": "Docelowe elementy dotykowe o odpowiedniej wielkości i z właściwymi odstępami są ważne dla użytkowników, którzy mają trudności z aktywowaniem niewielkich elementów sterujących. [Więcej informacji o docelowych elementach dotykowych](https://dequeuniversity.com/rules/axe/4.8/target-size)"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Docelowe elementy dotykowe nie mają odpowiedniej wielkości lub właściwych odstępów."}, "core/audits/accessibility/target-size.js | title": {"message": "Docelowe elementy dotykowe mają odpowiednią wielkość i właściwe odstępy."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. Aby poprawić wrażenia użytkowników czytnika ekranu, zadbaj o to, ż<PERSON>y elementy `<td>` w dużej tabeli (wysoki<PERSON> i szerokiej na co najmniej 3 komórki) miały powiązany nagłówek tabeli. [Więcej informacji o nagłówkach tabel](https://dequeuniversity.com/rules/axe/4.8/td-has-header)"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Elementy `<td>` w dużym elemencie `<table>` nie mają nagłówków tabel."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Elementy `<td>` w dużym elemencie `<table>` mają co najmniej 1 nagłówek tabeli."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. Gdy komórki `<td>` używające atrybutu `[headers]` odwołują się tylko do innych komórek w tej samej tabeli, użytkownicy czytników ekranu mogą wygodniej korzystać z tabel. [Więcej informacji o atrybucie `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Komórki w elemencie `<table>`, które używają atrybutu `[headers]`, odwołują się do elementu `id`, którego nie znaleziono w tej samej tabeli."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Komórki w elemencie `<table>`, które używają atrybutu `[headers]`, odwołują się do komórek w tej samej tabeli."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. Gdy nagłówki tabel zawsze odwołują się do jakiegoś zbioru komórek, użytkownicy czytników ekranu mogą wygodniej korzystać z tabel. [Więcej informacji o nagłówkach tabel](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON>e istnieją komórki danych opisywane przez elementy `<th>` i elementy z atrybutem `[role=\"columnheader\"/\"rowheader\"]`."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Istnieją komórki danych opisywane przez elementy `<th>` i elementy z atrybutem `[role=\"columnheader\"/\"rowheader\"]`."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Określenie w elementach prawidłowego [tagu języka w formacie BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaga zapewnić prawidłową wymowę tekstu przez czytnik ekranu. [<PERSON><PERSON> k<PERSON> z atrybutu `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atrybuty `[lang]` nie maj<PERSON> praw<PERSON>ł<PERSON>"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atrybuty `[lang]` maj<PERSON> p<PERSON> wartość"}, "core/audits/accessibility/video-caption.js | description": {"message": "Filmy z napisami są bardziej dostępne dla osób niesłyszących i niedosłyszących. [Więcej informacji o napisach do filmów](https://dequeuniversity.com/rules/axe/4.8/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementy `<video>` nie zaw<PERSON> elementu `<track>` z atrybutem `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementy `<video>` zawierają element `<track>` z atrybutem `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Bieżąca <PERSON>"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Sugerowany token"}, "core/audits/autocomplete.js | description": {"message": "Atrybut `autocomplete` ułatwia użytkownikom szybkie wypełnianie formularzy. Aby pomóc użytkownikom, ustaw prawidłową wartość atrybutu `autocomplete`. [Więcej informacji o atrybucie `autocomplete` w formularzach](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Elementy `<input>` nie maj<PERSON> prawidłowych atrybutów `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Wymaga ręcznego sprawdzenia"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Sprawdź kolejność tokenów"}, "core/audits/autocomplete.js | title": {"message": "Elementy `<input>` prawidłowo wykorzystują `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Token(y) `autocomplete`: „{token}” jest ni<PERSON><PERSON><PERSON>ł<PERSON>y w {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Spra<PERSON>dź kolejność tokenów: „{tokens}” w {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Wymagające wykonania czynności"}, "core/audits/bf-cache.js | description": {"message": "Wiele elementów nawigacyjnych prowadzi do poprzedniej lub kolejnej strony. <PERSON><PERSON><PERSON>ć podręczna stanu strony internetowej (bfcache) może przyspieszyć powrót na poprzednią stronę. [Dow<PERSON>z się więcej o pamięci podręcznej stanu strony internetowej](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 przyczyna niepowodzenia}few{# przyczyny niepowodzenia}many{# przyczyn niepowodzenia}other{# przyczyny niepowodzenia}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Przyczyna niepowodzenia"}, "core/audits/bf-cache.js | failureTitle": {"message": "Strona uniemożliwiła przywrócenie pamięci podręcznej stanu strony internetowej"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Nie umożliwiające wykonania czynności"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Oczekiwanie na obsługę przeglądarki"}, "core/audits/bf-cache.js | title": {"message": "Strona nie uniemożliwiła przywrócenia pamięci podręcznej stanu strony internetowej"}, "core/audits/bf-cache.js | warningHeadless": {"message": "W starej wersji Chrome bez interfejsu graficznego (`--chrome-flags=\"--headless=old\"`) nie można przetestować pamięci podręcznej stanu strony internetowej. Aby zobaczyć wyniki kontroli, użyj nowej wersji Chrome bez interfejsu graficznego (`--chrome-flags=\"--headless=new\"`) lub wersji standardowej Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Rozszerzenia Chrome pogorszyły szybkość ładowania tej strony. Przeprowadź audyt strony w trybie incognito lub w profilu Chrome bez rozszerzeń."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON> skryptu"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON>za s<PERSON>ów"}, "core/audits/bootup-time.js | columnTotal": {"message": "Łączny czas pracy procesora"}, "core/audits/bootup-time.js | description": {"message": "Pomyśl o skróceniu czasu poświęcanego na analizowanie, kompilowanie i wykonywanie kodu JS. Może w tym pomóc dostarczanie mniejszych ładunków JS. [<PERSON><PERSON> skr<PERSON><PERSON>ć czas wykonywania kodu JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> czas wykonywania JavaScriptu"}, "core/audits/bootup-time.js | title": {"message": "Czas wykonania JavaScriptu"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Usuń z pakietów duplikaty dużych modułów JavaScript, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> il<PERSON> danych niepotrzebnie przesyłanych w sieci. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Usuń zduplikowane moduły w pakietach JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Duże pliki GIF są nieefektywnym sposobem dostarczania animacji. Proponujemy użyć zamiast nich filmów MPEG4/WebM (animacje) lub plików PNG/WebP (obrazy statyczne), aby zmniej<PERSON><PERSON><PERSON> ilość przesyłanych danych. [Więcej informacji o efektywnych formatach wideo](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Użyj formatów wideo dla animacji"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Elementy polyfill i przekształcenia umożliwiają obsługę nowych funkcji JavaScript przez starsze przeglądarki. Jednak nowoczesne przeglądarki nie potrzebują wielu takich elementów. W przypadku pakietów JavaScript zastosuj nowoczesną strategię wdrażania skryptów opartą na wykrywaniu ich atrybutów „module” i „nomodule”. Pozwala to ograniczyć ilość kodu przesyłanego do nowoczesnych przeglądarek i jednocześnie umożliwia obsługę przeglądarek starszych. [Jak używać nowoczesnego kodu JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Unikaj wyświetlania starszych skryptów JavaScript w nowoczesnych przeglądarkach"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formaty plików graficznych takie jak WebP i AVIF często umożliwiają lepszą kompresję niż PNG lub JPEG. Dzięki temu można je pobierać szybciej, zużywając przy tym mniej danych. [Więcej informacji o nowoczesnych formatach plików graficznych](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Wyświetlaj obrazy w formatach nowej generacji"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> czas do pełnej interaktywności, warto skorzystać z leniwego ładowania. Dzięki temu najpierw będą ładowane wszystkie zasoby kluczowe, a dopiero potem obrazy ukryte i znajdujące się poza ekranem. [Jak opóźnić wyświetlanie obrazów poza ekranem](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odłóż ładowanie obrazów poza ekranem"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Zasoby blokują pierwsze wyrenderowanie strony. Sugerujemy umieszczenie krytycznego kodu JS/CSS w kodzie strony i opóźnienie ładowania wszystkich niekrytycznych plików JS i stylów. [<PERSON><PERSON> wye<PERSON><PERSON>wać zasoby blokujące renderowanie](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Wyeliminuj zasoby blokujące renderowanie"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Duże ładunki sieciowe powodują wyższe koszty dla użytkowników i są mocno powiązane z długim czasem ładowania. [Jak zmniejszyć ładunki](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Łączny rozmiar to {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Unikaj bardzo dużych ładunków sieciowych"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Unikaj bardzo dużych ładunków sieciowych"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifikacja plików CSS może zmniejszyć ładunki sieciowe. [Jak zmniejszać pliki CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifikuj CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifikacja plików JavaScript może zmniejszyć ładunki i skrócić czas analizowania skryptów. [Jak minifikować kod JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifikuj JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Ogranicz nieużywane reguły z arkuszy stylów i opóźnij ładowanie kodu CSS, który nie jest używany w części strony widocznej na ekranie. W ten sposób zmniejszysz ilość danych przesyłanych w sieci. [<PERSON><PERSON> ograni<PERSON>ć ilość nieużywanego kodu CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Ogranicz nieużywany kod CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Ogranicz nieużywany JavaScript i opóźnij ładowanie skryptów do momentu, aż będą wymagane. W ten sposób zmniejsz<PERSON>z ilość danych przesyłanych w sieci. [Jak zmniejszyć ilość nieużywanego kodu JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Ogranicz nieużywany JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Długi czas przechowywania w pamięci podręcznej może przyspieszyć ponowne otwarcie strony. [Więcej informacji o zasadach dotyczących efektywnej pamięci podręcznej](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 zasób}few{Znaleziono # zasoby}many{Znaleziono # zasobów}other{Znaleziono # zasobu}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Wyświetlaj zasoby statyczne, stosując efektywne zasady pamięci podręcznej"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Stosuje efektywne zasady pamięci podręcznej dla zasobów statycznych"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Zoptymalizowane obrazy ładują się szybciej i wykorzystują mniej komórkowej transmisji danych. [Jak wydajnie kodować obrazy](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Użyj efektywnego kodowania obrazów"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Rzeczywiste wymiary"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Wyświetlane wymiary"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> są wię<PERSON><PERSON> ni<PERSON> roz<PERSON>, w jakim są wyświetlane"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON><PERSON> pasują do rozmiaru, w jakim są wyświetlane"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Wyświetlaj obrazy o odpowiednim rozmiarze, aby o<PERSON><PERSON><PERSON>ć komórkową transmisję danych i przyspieszyć ładowanie. [Jak dopasowywać rozmiary obrazów](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Zmień rozmiar obrazów"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Zasoby tekstowe powinny by<PERSON> komp<PERSON> (gzip, deflate lub brotli), aby <PERSON><PERSON><PERSON><PERSON><PERSON> ilość danych przesyłanych w sieci. [Więcej informacji o kompresji tekstu](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Włącz kompresję tekstu"}, "core/audits/content-width.js | description": {"message": "<PERSON><PERSON><PERSON> szeroko<PERSON> zawartości aplikacji nie odpowiada szerokości widocznego obszaru, aplikacja może nie być zoptymalizowana pod kątem ekranów urządzeń mobilnych. [Jak dopasować rozmiar treści do widocznego obszaru](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Rozmiar widocznego obszaru ({innerWidth} piks.) nie odpowiada rozmiarowi okna ({outerWidth} piks.)."}, "core/audits/content-width.js | failureTitle": {"message": "Zawart<PERSON>ść nie jest odpowiednio dopasowana do widocznego obszaru"}, "core/audits/content-width.js | title": {"message": "Z<PERSON>rt<PERSON><PERSON><PERSON> jest odpowiednio dopasowana do widocznego obszaru"}, "core/audits/critical-request-chains.js | description": {"message": "Poniższe łańcuchy żądań krytycznych pokazują zasoby ładowane z wysokim priorytetem. Aby przys<PERSON><PERSON>ć ładowanie strony, m<PERSON><PERSON><PERSON><PERSON> skr<PERSON><PERSON>ć ła<PERSON>, zmniejszyć rozmiar pobieranych zasobów lub opóźnić pobieranie zasobów, które nie są niezbędne. [Jak unikać łańcuchów żądań krytycznych](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 łańcuch}few{Znaleziono # łańcuchy}many{Znaleziono # łańcuchów}other{Znaleziono # łańcucha}}"}, "core/audits/critical-request-chains.js | title": {"message": "Unikaj tworzenia łańcuchów żądań krytycznych"}, "core/audits/csp-xss.js | columnDirective": {"message": "Dyrektywa"}, "core/audits/csp-xss.js | columnSeverity": {"message": "W<PERSON>"}, "core/audits/csp-xss.js | description": {"message": "Ścisłe przestrzeganie standardu Content Security Policy (CSP) znacznie zmniejsza ryzyko ataków typu cross-site scripting (XSS). [J<PERSON> zapob<PERSON><PERSON> atakom XSS za pomocą standardu CSP](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Składnia"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Strona zawiera definicję CSP w tagu `<meta>`. Zastanów się nad przeniesieniem CSP do nagłówka HTTP lub zdefiniowaniem innego CSP w nagłówku HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "W trybie egzekwowania nie znaleziono CSP"}, "core/audits/csp-xss.js | title": {"message": "Skonfiguruj CSP pod kątem ochrony przed atakami typu XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Wycofanie/ostrzeżenie"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Wycofane interfejsy API zostaną w przyszłości usunięte z przeglądarki. [Więcej informacji o wycofanych interfejsach API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 ostrzeżenie}few{Znaleziono # ostrzeżenia}many{Znaleziono # ostrzeżeń}other{Znaleziono # ostrzeżenia}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Używa wycofanych interfejsów API"}, "core/audits/deprecations.js | title": {"message": "Nie używa wycofanych interfejsów API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Wymagana jest deklaracja kodowania znaków. Możesz ją dodać za pomocą tagu `<meta>` w pierwszych 1024 bajtach kodu HTML lub w nagłówku odpowiedzi HTTP Content-Type. [Więcej informacji o deklarowaniu kodowania znaków](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Brakuje deklaracji zestawu znaków lub występuje ona zbyt późno w kodzie HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Prawidłowo definiuje zestaw znaków"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Podanie definicji doctype zapobiega przełączaniu przeglądarki w tryb osobliwości. [Więcej informacji o deklaracji doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Nazwa formatu doctype musi być ciągiem `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokument zawiera element `doctype` wywołujący `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument musi zaw<PERSON> deklarację doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Oczekiwano pustego ciągu w polu publicId"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Oczekiwano pustego ciągu w polu systemId"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokument zawiera element `doctype` wywołuj<PERSON>cy `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Strona nie zawiera elementu HTML doctype, przez co aktywuje tryb osobliwości"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Strona ma deklarację doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statystyki"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Duży DOM zwiększy wykorzystanie pamięci, w<PERSON><PERSON><PERSON><PERSON><PERSON> [obliczanie stylów](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) i spowoduje kosztowne [przeformatowania układu](https://developers.google.com/speed/articles/reflow). [Jak unikać nadmiernego rozmiaru DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}few{# elementy}many{# elementów}other{# elementu}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Unikaj zbyt dużego DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksymalna głębokość DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Łączna liczba elementów DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksymalna liczba elementów podrzędnych"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Unika zbyt dużego DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, które bez kontekstu pytają o zgodę na dostęp do lokalizacji, nie budzą zaufania użytkowników lub ich dezorientują. Sugerujemy powiązanie wyświetlenia tej prośby z działaniem użytkownika. [Więcej informacji o uprawnieniach do geolokalizacji](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Pyta o zgodę na geolokalizację podczas wczytywania strony"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Nie pyta o zgodę na geolokalizację podczas wczytywania strony"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON> problemu"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Informacje zarejestrowane w panelu `Issues` w Narzędziach deweloperskich Chrome wskazują na nierozwiązane problemy. Mogą być spowodowane nieudanymi żądaniami sieciowymi, niewystarczającymi zabezpieczeniami i innymi problemami w przeglądarce. Otwórz panel Issues (Problemy) w Narzędziach deweloperskich Chrome, aby dowiedzieć się więcej o poszczególnych problemach."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problemy zostały zarejestrowane w panelu `Issues` w Narzędziach deweloperskich Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Zablokowany przez zasadę dotyczącą zasobów z innych domen"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "<PERSON><PERSON>zo duże wykorzystanie zasobów przez reklamy"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Brak problemów w panelu `Issues` w Narzędziach deweloperskich Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Wszystkie biblioteki JavaScript interfejsu użytkownika wykryte na stronie. [Więcej informacji o tej kontroli diagnostycznej wykrywania biblioteki JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Wykryte biblioteki JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "W przypadku wolnego połączenia sieciowego skrypty zewnętrzne dodawane dynamicznie przy użyciu instrukcji `document.write()` mogą opóźnić wczytanie strony o kilkadziesiąt sekund. [Jak unikać instrukcji document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Unikaj `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Nie używa instrukcji `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, które bez kontekstu pytają o zgodę na wyświetlanie powiadomień, nie budzą zaufania użytkowników lub ich dezorientują. Sugerujemy powiązanie wyświetlenia tej prośby z gestami użytkownika. [Więcej informacji o odpowiedzialnym uzyskiwaniu uprawnień do wyświetlania powiadomień](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Pyta o zgodę na wyświetlanie powiadomień podczas wczytywania strony"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Nie pyta o zgodę na wyświetlanie powiadomień podczas wczytywania strony"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "W przypadku UX zapobieganie wklejaniu danych wejściowych to zła praktyka, która na dodatek zmniejsza bezpieczeństwo, blokując menedżery haseł. [Więcej informacji o łatwych w użyciu polach do wprowadzania danych](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Uniemożliwia użytkownikom wklejanie w polach do wprowadzania danych"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Zezwala użytkownikom na wklejanie w polach do wprowadzania danych"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokół"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ma wiele funkcji niedostępnych w HTTP/1.1, m.in. nagłówki binarne i multipleksowanie. [Więcej informacji o protokole HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 żądanie nieprzesłane przez HTTP/2}few{# żądania nieprzesłane przez HTTP/2}many{# żądań nieprzesłanych przez HTTP/2}other{# żądania nieprzesłanego przez HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Użyj HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Proponujemy oznaczenie detektorów zdarzeń dotyku i kółka myszy jako `passive`, aby pop<PERSON>ić działanie przewijania strony. [Więcej informacji o stosowaniu pasywnych detektorów zdarzeń](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nie używa pasywnych detektorów do poprawy działania przewijania"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Używa detektorów pasywnych do poprawy działania przewijania"}, "core/audits/errors-in-console.js | description": {"message": "Błędy zarejestrowane w konsoli wskazują na nierozwiązane problemy. Mogą być spowodowane nieudanymi żądaniami sieciowymi i innymi problemami w przeglądarce. [Więcej informacji o błędach podczas kontroli diagnostycznej konsoli](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Błędy przeglądarki zostały zarejestrowane w konsoli"}, "core/audits/errors-in-console.js | title": {"message": "W konsoli nie zostały zarejestrowane żadne błędy przeglądarki"}, "core/audits/font-display.js | description": {"message": "Użyj funkcji CSS `font-display`, aby <PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>ć tekstu dla użytkownika podczas ładowania czcionek internetowych. [Więcej informacji o funkcji `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "Zapewnij widoczność tekstu podczas ładowania czcionek internetowych"}, "core/audits/font-display.js | title": {"message": "Cały tekst pozostaje widoczny podczas ładowania czcionek internetowych"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Narzędziu Lighthouse nie udało się automatycznie sprawdzić wartości `font-display` dla strony źródłowej {fontOrigin}.}few{Narzędziu Lighthouse nie udało się automatycznie sprawdzić wartości `font-display` dla strony źródłowej {fontOrigin}.}many{Narzędziu Lighthouse nie udało się automatycznie sprawdzić wartości `font-display` dla strony źródłowej {fontOrigin}.}other{Narzędziu Lighthouse nie udało się automatycznie sprawdzić wartości `font-display` dla strony źródłowej {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Współczynnik proporcji (rzeczywisty)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Współczynnik proporcji (wyświetlany)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Wymiary wyświetlanego obrazu muszą odpowiadać naturalnemu współczynnikowi proporcji. [Więcej informacji o współczynniku proporcji obrazu](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Wyświetla obrazy o niepoprawnym współczynniku proporcji"}, "core/audits/image-aspect-ratio.js | title": {"message": "Wyświetla obrazy o poprawnym współczynniku proporcji"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Rozmiar rzeczywisty"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Rozmiar wyświetlany"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON>oz<PERSON><PERSON>"}, "core/audits/image-size-responsive.js | description": {"message": "Aby obraz był maksymalnie wyraźny, jego naturalne wymiary powinny być zgodne z rozmiarem i rozdzielczością ekranu. [Jak przesyłać obrazy elastyczne](https://web.dev/articles/serve-responsive-images)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Wyświetla obrazy w niskiej rozdzielczości"}, "core/audits/image-size-responsive.js | title": {"message": "Wyświetla obrazy w odpowiedniej rozdzielczości"}, "core/audits/installable-manifest.js | already-installed": {"message": "Ta aplikacja jest już z<PERSON>stalowana"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Nie udało się pobrać wymaganej ikony z pliku manifestu"}, "core/audits/installable-manifest.js | columnValue": {"message": "Przyczyna niepowodzenia"}, "core/audits/installable-manifest.js | description": {"message": "Skrypt service worker pozwala aplikacji na korzystanie z wielu funkcji progresywnych aplikacji internetowych – takich jak działanie offline, dodawanie do ekranu głównego czy powiadomienia push. Jeśli skrypt service worker i plik manifestu są odpowiednio zaimplementowane, przeglądarki mogą aktywnie prosić użytkowników o dodanie Twojej aplikacji do ekranu głównego, co może przekładać się na większe zaangażowanie. [Dowiedz się więcej o wymaganiach dotyczących możliwości zainstalowania plików manifestu](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 przyczyna}few{# przyczyny}many{# przyczyn}other{# przyczyny}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Plik manifestu aplikacji internetowej lub skrypt service worker nie spełnia wymagań instalowalności"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL aplikacji w Sklepie Play nie pasuje do identyfikatora w Sklepie Play"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Strona jest ładowana w oknie incognito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `display` w pliku manifestu musi mieć wartość `standalone`, `fullscreen` lub `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Plik manifestu zawiera pole „display_override”, a pierwszym obsługiwanym trybem wyświetlania musi być „standalone”, „fullscreen” lub „minimal-ui”"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Nie udało się pobrać pliku manifestu, jest on pusty albo nie można go przeanalizować"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL pliku manifestu zmienił się podczas pobierania pliku manifestu."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Plik manifestu nie zawiera pola `name` ani `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Plik manifestu nie zawiera odpowiedniej ikony. Ikona musi być w formacie PNG, SVG lub WebP, mieć co najmniej {value0} piks. i ustawiony atrybut rozmiarów. <PERSON><PERSON><PERSON> jest ustawiony atrybut przeznaczenia, to musi zaw<PERSON> wartość „any”."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Żadna z podanych ikon nie jest kwadratem z bokiem o długości co najmniej {value0} piks. w formacie PNG, SVG lub WebP, z atrybutem przeznaczenia o nieustawionej wartości lub wartości „any”"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Pobrana ikona była pusta lub uszkodzona"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "<PERSON>e podano identyfikatora w Sklepie Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Strona nie ma adresu URL pliku manifestu <link>"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "<PERSON><PERSON> można sprawdzić skryptu service worker bez pola „start_url” w pliku manifestu"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Nie rozpoznano identyfikatora błędu związanego z możliwością instalowania „{errorId}”"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Strona nie jest wysyłana z bezpiecznego źródła"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Strona nie jest ładowana w ramce głównej"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Strona nie działa offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Odinstalowano PWA. Trwa resetowanie sprawdzania możliwości zainstalowania."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Wskazana platforma aplikacji nie jest obsługiwana na Androidzie"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Plik manifestu zawiera prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Atrybut prefer_related_applications jest obsługiwany tylko w wersjach beta i stabilnej Chrome na Androida."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Narzędzie Lighthouse nie mogło <PERSON>, czy stronę można zainstalowa<PERSON>. Spróbuj użyć nowszej wersji Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "URL początkowy pliku manifestu jest nieprawidłowy"}, "core/audits/installable-manifest.js | title": {"message": "Plik manifestu aplikacji internetowej i skrypt service worker spełniają wymagania instalowalności"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL w pliku manifestu zawiera nazwę użytkownika, hasło lub port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Strona nie działa offline. Strona nie będzie uznawana za możliwą do zainstalowania, począwszy od wersji stabilnej Chrome 93, która zostanie opublikowana w sierpniu 2021 r."}, "core/audits/is-on-https.js | allowed": {"message": "Dozwolony"}, "core/audits/is-on-https.js | blocked": {"message": "Zablokowany"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Niezabezpieczony URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Obsługa żądań"}, "core/audits/is-on-https.js | description": {"message": "Wszystkie witryny powinny być zabezpieczone przy użyciu HTTPS – również te, które nie obsługują danych wrażliwych. Dotyczy to również unikania [treści mieszanej](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), jeśli część zasobów wczytuje się przez HTTP, mimo że wstępne żądanie używa HTTPS. HTTPS uniemożliwia intruzom modyfikowanie i podsłuchiwanie komunikacji między aplikacją a użytkownikami. HTTP/2 i liczne nowe interfejsy API platformy WWW wymagają używania HTTPS. [Więcej informacji o protokole HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Wykryto 1 niezabezpieczone żądanie}few{Wykryto # niezabezpieczone żądania}many{Wykryto # niezabezpieczonych żądań}other{Wykryto # niezabezpieczonego żądania}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Nie używa HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Używa HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatycznie uaktualniony do HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Dozwolony z ostrzeżeniem"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Faza"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Czas"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "To najwięks<PERSON> część treści wyrenderowana w widocznym obszarze. [Więcej informacji o renderowaniu największej części treści](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Opóźnienie wczytywania"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "<PERSON>zas wczytywania"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Opóźnienie renderowania"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Najwięszy wyrenderowany element"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Wpływ przesunięcia układu"}, "core/audits/layout-shift-elements.js | description": {"message": "Przesunięcia układu miały największy wpływ na te elementy DOM. Niektóre przesunięcia układu mogą nie być uwzględnione w wartości danych CLS z powodu [okienkowania](https://web.dev/articles/cls#what_is_cls). [<PERSON><PERSON><PERSON>, jak poprawić wartość CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Unikaj duż<PERSON> przesuni<PERSON>ć układu"}, "core/audits/layout-shifts.js | columnScore": {"message": "Wynik przesunięcia układu"}, "core/audits/layout-shifts.js | description": {"message": "To są największe przesunięcia układu zaobserwowane na stronie. Każdy element tabeli przedstawia jedno przesunięcie układu i pokazuje element, który przesunął się najbardziej. Pod każdym elementem znajdziesz listę możliwych głównych przyczyn, które doprowadziły do przesunięcia układu. Niektóre z tych przesunięć układu mogą nie być uwzględnione w wartości danych CLS z powodu [okienkowania](https://web.dev/articles/cls#what_is_cls). [<PERSON><PERSON><PERSON> się, jak poprawić wartość CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{Znaleziono 1 przesunięcie układu}few{Znaleziono # przesunięcia układu}many{Znaleziono # przesunięć układu}other{Znaleziono # przesunięcia układu}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Czcionka internetowa została wczytana"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Wstrzyknięty element iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Spóźnione żądanie sieciowe spowodowało zmianę układu strony"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Element multimedialny nie ma wyraźnego rozmiaru"}, "core/audits/layout-shifts.js | title": {"message": "Unikaj duż<PERSON> przesuni<PERSON>ć układu"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Obrazy w części strony widocznej na ekranie, które są leniwie ładowane, są renderowane później w cyklu życia strony, przez co może się opóźnić wyrenderowanie największej części treści. [Jak zoptymalizować leniwe ładowanie](https://web.dev/articles/lcp-lazy-loading)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Największy wyrenderowany obraz został leniwie załadowany"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Największy wyrenderowany obraz został załadowany bez użycia trybu leniwego ładowania"}, "core/audits/long-tasks.js | description": {"message": "Pokazuje najdłuższe zadania na liście w wątku głównym. Służy do rozpoznawania czynników, które mają największy wpływ na opóźnienia działania. [Jak unikać długich zadań w wątku głównym](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono # długie zadanie}few{Znaleziono # długie zadania}many{Znaleziono # długich zadań}other{Znaleziono # długiego zadania}}"}, "core/audits/long-tasks.js | title": {"message": "Unikaj długich zadań w wątku głównym"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategoria"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Pomyśl o skróceniu czasu poświęcanego na analizowanie, kompilowanie i wykonywanie kodu JS. Może w tym pomóc dostarczanie mniejszych ładunków JS. [Jak zminimalizować czas pracy z wątkiem głównym](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Zminimalizuj aktywność głównego wątku"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizuje aktywność głównego wątku"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Witryny powinny działać w każdej popularnej przeglądarce, aby mogło do nich dotrzeć jak najwięcej użytkowników. [Więcej informacji o zgodności stron z różnymi przeglądarkami](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Witryna działa w różnych przeglądarkach"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Upewnij się, że do poszczególnych stron można dotrzeć za pomocą precyzyjnych linków w postaci adresów URL i że adresy URL są unikalne na potrzeby udostępniania w mediach społecznościowych. [Więcej informacji o udostępnianiu precyzyjnych linków](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Każda strona ma swój URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "P<PERSON><PERSON><PERSON><PERSON> po kliknięciu powinny być płynne, nawet jeśli sieć jest wolna. Ma to kluczowe znaczenie dla postrzegania szybkości działania przeglądarki. [Więcej informacji o przejściach między stronami](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Przejścia między stronami nie sprawiają wrażenia, jakby zacinały się z powodu opóźnień w sieci"}, "core/audits/maskable-icon.js | description": {"message": "Ikona z maskowaniem da<PERSON>, że podczas instalowania aplikacji na urządzeniu obraz wypełni cały kształt, nie pozostawiając pustych przestrzeni. [Więcej informacji o ikonach manifestu z możliwością maskowania](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Plik manifestu nie ma ikony z możliwością maskowania"}, "core/audits/maskable-icon.js | title": {"message": "Plik manifestu ma ikonę z możliwością maskowania"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Zbiorcze przesunięcie układu to miara ruchu elementów w widocznym obszarze. [Więcej informacji o danych dotyczących zbiorczego przesunięcia układu](https://web.dev/articles/cls)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Pier<PERSON>ze wyrenderowanie treści oznacza czas wyrenderowania pierwszego tekstu lub obrazu. [Więcej informacji o danych pierwszego wyrenderowania treści](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Pierwsze wyrenderowanie elementu znaczącego oznacza czas pojawienia się na ekranie głównej zawartości strony. [Więcej informacji o pierwszym wyrenderowaniu elementu znaczącego](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Czas od interakcji do kolejnego wyrenderowania określa responsywność strony, c<PERSON><PERSON> czas potrzebny do widocznej reakcji na dane wprowadzone przez użytkownika. [Więcej informacji o czasie od interakcji do kolejnego wyrenderowania](https://web.dev/articles/inp)"}, "core/audits/metrics/interactive.js | description": {"message": "Czas do pełnej interaktywności to czas, po którym strona staje się w pełni interaktywna. [Więcej informacji o danych dotyczących czasu do pełnej interaktywności](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Wyrenderowanie największej części treści oznacza czas wyrenderowania największego tekstu lub obrazu. [Więcej informacji o wartości wyrenderowania największej części treści](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Maksymalne opóźnienie po pierwszej interakcji, którego mogą doświadczyć użytkownicy, to czas wykonywania najdłuższego zadania. [Więcej informacji o danych dotyczących maksymalnego potencjalnego opóźnienia po pierwszej interakcji](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "In<PERSON><PERSON> szy<PERSON> w<PERSON>, jak szybko strona zapełnia się widocznymi treściami. [Więcej informacji o danych indeksu szybko<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Wyrażona w milisekundach suma wszystkich okresów między pierwszym wyrenderowaniem treści a czasem do pełnej interaktywności, gdy długość zadania przekroczyła 50 ms. [Więcej informacji o łącznym czasie blokowania](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "<PERSON>zas błądzenia w sieci (RTT) ma duży wpływ na szybkość działania. Jeśli RTT do źródła jest duży, serwery znajdujące się bliżej użytkownika mogą przyśpieszyć działanie. [Więcej informacji o czasie błądzenia](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "Czasy błądzenia w sieci (Network Round Trip Times)"}, "core/audits/network-server-latency.js | description": {"message": "Opóźnienie serwera może wpływać na szybkość stron internetowych. Jeśli opóźnienie serwera źródłowego jest duże, serwer może być przeciążony lub mieć mało wydajny backend. [Więcej informacji o czasie reakcji serwera](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Opóźnienia backendu serwera"}, "core/audits/no-unload-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> `unload` nie zawsze jest uruchamiane prawidłowo, a wykrywanie go może uniemożliwić działanie mechanizmów optymalizujących pracę przeglądarki takich jak np. Back-Forward Cache. Zamiast tego użyj zdarzenia `pagehide` lub `visibilitychange`. [Więcej informacji o detektorach zdarzeń wyładowania](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Zarejestrowanie detektora zdarzeń `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Zarejestrowanie braku detektorów zdarzeń `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Nieskomponowane animacje mogą działać nieprawidłowo i zwiększać CLS. [Jak unikać nieskomponowanych animacji](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono # animowany element}few{Znaleziono # animowane elementy}many{Znaleziono # animowanych elementów}other{Znaleziono # animowanego elementu}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Właściwość związana z filtrowaniem może powodować przemieszczanie pikseli"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Obiekt docelowy zawiera inną, niezgodną animację"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efekt ma tryb komponowania inny niż „replace”"}, "core/audits/non-composited-animations.js | title": {"message": "Unikaj <PERSON>es<PERSON>mpo<PERSON>wanych animacji"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Właściwość związana z przekształceniem zależy od rozmiaru pola"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nieobsługiwana wła<PERSON><PERSON><PERSON>ść CSS: {properties}}few{Nieobsługiwane właściwości CSS: {properties}}many{Nieobsługiwane właściwości CSS: {properties}}other{Nieobsługiwane właściwości CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekt ma nieobsługiwane parametry czasowe"}, "core/audits/performance-budget.js | description": {"message": "Liczba ani rozmiar żądań sieciowych nie mogą przekraczać wartości określonych przez podany budżet wydajności. [Więcej informacji o budżetach wydajności](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 żądanie}few{# żądania}many{# żądań}other{# żądania}}"}, "core/audits/performance-budget.js | title": {"message": "Budżet wydajności"}, "core/audits/preload-fonts.js | description": {"message": "Włącz wstępne wczytywanie czcionek z atrybutem `optional`, aby mogli z nich korzystać nowi użytkownicy. [Więcej informacji o wstępnym wczytywaniu czcionek](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Czcionki z atrybutem `font-display: optional` nie są wstępnie wczytywane"}, "core/audits/preload-fonts.js | title": {"message": "Czcionki z atrybutem `font-display: optional` są wstępnie wczytywane"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Jeśli element LCP jest dynamicznie dodawany do strony, obraz należy wstępnie ładować, aby pop<PERSON> wartość LCP. [Więcej informacji o wstępnym ładowaniu elementów LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Wstępnie wczytuj największy wyrenderowany obraz"}, "core/audits/redirects.js | description": {"message": "Przekierowania wprowadzają dodatkowe opóźnienia przed załadowaniem strony. [Jak unikać przekierowań strony](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Unikaj wielokrotnych przekierowań"}, "core/audits/seo/canonical.js | description": {"message": "Linki kanoniczne sugerują URL, kt<PERSON>ry ma być poka<PERSON>wany w wynikach wyszukiwania. [Więcej informacji o linkach kanonicznych](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Niezgodne ze sobą adresy URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Nieprawidłowy URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Wskazuje inną lokalizację atrybutu `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "URL nie jest bezwzględny ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Wskazuje główny URL domeny (stronę główną) zamiast odpowiedniej strony z treścią"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nie zawiera prawidłowego atrybutu `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument ma prawidłowy atrybut `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Link niemożliwy do zindeksowania"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Wyszukiwarki mogą korzystać z atrybutów `href` w linkach, aby móc indeksować witryny. Upewnij się, że atrybut `href` zakotwiczonych elementów prowadzi do odpowiedniego miejsca docelowego, aby możliwe było odnalezienie więks<PERSON>j liczby stron w witrynie. [<PERSON><PERSON> um<PERSON><PERSON><PERSON><PERSON>ć indeksowanie linków](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Linków  nie  można zindeksować"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Linki  można  zindek<PERSON>"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Dodatkowy nieczytelny tekst"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Rozmiar <PERSON>ki"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% tekstu na stronie"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Na urządzeniach mobilnych czcionka o rozmiarze mniejszym niż 12 pikseli jest nieczytelna, dlatego użytkownicy muszą powiększać ekran, aby od<PERSON><PERSON><PERSON>ć tekst. Ponad 60% tekstu na stronie powinno mieć rozmiar co najmniej 12 pikseli. [Więcej informacji o czytelnych rozmiarach czcionek](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} czytelnego te<PERSON>tu"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst jest nieczytelny z powodu braku metatagu viewport zoptymalizowanego na potrzeby ekranów telefonów."}, "core/audits/seo/font-size.js | failureTitle": {"message": "W dokumencie nie są używane czytelne rozmiary czcionek"}, "core/audits/seo/font-size.js | legibleText": {"message": "Czytelny tekst"}, "core/audits/seo/font-size.js | title": {"message": "W dokumencie używane są czytelne rozmiary czcionek"}, "core/audits/seo/hreflang.js | description": {"message": "Linki hreflang informują w<PERSON>, kt<PERSON><PERSON><PERSON> wersję strony p<PERSON> w wynikach wyszukiwania dla danego języka lub regionu. [Więcej informacji o linkach `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nie ma prawidłowego atrybutu `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Względna wartość href"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument ma prawidłowy atrybut `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Nieoczekiwany kod języka"}, "core/audits/seo/http-status-code.js | description": {"message": "Strony z kodem stanu HTTP oznaczającym niepowodzenie mogą nie być indeksowane poprawnie. [Więcej informacji o kodach stanu HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Strona ma kod stanu HTTP oznaczający niepowodzenie"}, "core/audits/seo/http-status-code.js | title": {"message": "Strona ma kod stanu HTTP oznaczający powodzenie"}, "core/audits/seo/is-crawlable.js | description": {"message": "Wyszukiwarki nie mogą um<PERSON>ich stron w wynikach wyszukiwania, jeśli nie mają uprawnie<PERSON>, aby je indekso<PERSON>. [Więcej informacji o dyrektywach robota](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Zablokowano indeksowanie strony"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indeksowanie strony nie jest zablokowane"}, "core/audits/seo/link-text.js | description": {"message": "Opisowy tekst linków ułatwia wyszukiwarkom zrozumienie zawartości stron. [Jak ułatwić dostęp do linków](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 link}few{Znaleziono # linki}many{Znaleziono # linków}other{Znaleziono # linku}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linki nie mają opisowego tekstu"}, "core/audits/seo/link-text.js | title": {"message": "Linki mają tekst opisowy"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Uruchom [Narzędzie do testowania uporządkowanych danych](https://search.google.com/structured-data/testing-tool/) i narzędzie [Structured Data Linter](http://linter.structured-data.org/), aby sprawd<PERSON><PERSON> uporządkowane dane. [Więcej informacji o uporządkowanych danych](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Uporządkowane dane są prawidłowe"}, "core/audits/seo/meta-description.js | description": {"message": "Metaopis można um<PERSON> w wynikach wyszukiwania, aby k<PERSON><PERSON><PERSON><PERSON> pod<PERSON> zawartoś<PERSON> strony. [Więcej informacji o metaopisie](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Tekst opisu jest pusty."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nie ma metaopisu"}, "core/audits/seo/meta-description.js | title": {"message": "Dokument ma metaopis"}, "core/audits/seo/plugins.js | description": {"message": "Wyszukiwarki nie potrafią indeksować treści z wtyczek, a wiele urządzeń nie obsługuje wtyczek lub ogranicza ich działanie. [Więcej informacji o unikaniu wtyczek](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument używa wtyczek"}, "core/audits/seo/plugins.js | title": {"message": "Dokument nie wymaga wtyczek"}, "core/audits/seo/robots-txt.js | description": {"message": "Jeśli plik robots.txt ma nieprawidłowy format, roboty indeksujące mogą nie w<PERSON>, jak mają indeksować witrynę. [Więcej informacji o plikach robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Żądanie pliku robots.txt zwróciło stan HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Znaleziono 1 błąd}few{Znaleziono # błędy}many{Znaleziono # błędów}other{Znaleziono # błędu}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nie udało się pobrać pliku robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Plik robots.txt jest nieprawidłowy"}, "core/audits/seo/robots-txt.js | title": {"message": "Plik robots.txt jest prawidłowy"}, "core/audits/seo/tap-targets.js | description": {"message": "Elementy interaktywne, takie jak przyciski i linki, pow<PERSON><PERSON> być dostatecznie duże (48 x 48 pikseli) lub mieć wokół siebie odpowiednią ilość miejsca, aby można było ich łatwo dotknąć, nie zahac<PERSON>j<PERSON>c o inne elementy. [Więcej informacji o elementach docelowych kliknięcia](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} elementów dotykowych ma odpowiednią wielkość"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Elementy dotykowe są zbyt małe z powodu braku metatagu viewport zoptymalizowanego na potrzeby ekranów telefonów"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Elementy dotykowe nie mają odpowiedniej wielkości"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Pokrywające się elementy dotykowe"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Element dotykowy"}, "core/audits/seo/tap-targets.js | title": {"message": "Elementy dotykowe mają odpowiednią wielkość"}, "core/audits/server-response-time.js | description": {"message": "Zadbaj o to, aby czas reakcji serwera był jak najkrótszy dla głównego dokumentu na stronie, bo to od niego zależą wszystkie inne żądania. [Więcej informacji o czasie do pierwszego bajtu](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "<PERSON>zas odpowiedzi głównego dokumentu: {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Skr<PERSON>ć wstępny czas reakcji serwera"}, "core/audits/server-response-time.js | title": {"message": "Wstępny czas reakcji serwera był krótki"}, "core/audits/splash-screen.js | description": {"message": "Ekran powitalny z niestandardowym motywem zapewnia użytkownikom lepsze wrażenia podczas otwierania aplikacji z ekranu głównego. [Więcej informacji o ekranach powitalnych](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON> skonfigurowano niestandardowego ekranu powitalnego"}, "core/audits/splash-screen.js | title": {"message": "Skonfigurowano niestandardowy ekran powitalny"}, "core/audits/themed-omnibox.js | description": {"message": "Motyw paska adresu w przeglądarce możesz dopasować do swojej witryny. [Więcej informacji o tworzeniu motywu paska adresu](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON>e ustawia motywu kolorystycznego paska adresu."}, "core/audits/themed-omnibox.js | title": {"message": "Ustawia motyw kolorystyczny paska adresu."}, "core/audits/third-party-cookies.js | description": {"message": "W przys<PERSON>łej wersji Chrome wycofamy obsługę plików cookie innych firm. [Więcej informacji o wycofywaniu plików cookie innych firm](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 plik cookie}few{Znaleziono # pliki cookie}many{Znaleziono # plików cookie}other{Znaleziono # pliku cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Używa plików cookie innych firm"}, "core/audits/third-party-cookies.js | title": {"message": "Nie używa plików cookie innych firm"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (sukcesy klientów)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (społecznościowy)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (wideo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Usługa"}, "core/audits/third-party-facades.js | description": {"message": "Niektóre umieszczone na stronie elementy z innych witryn mogą być leniwie ładowane. Dopóki nie będą potrzebne, może je zastępować komponent fasadowy. [Jak opóźniać zewnętrzne strony za pomocą komponentu fasadowego](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Dostępny # komponent fasadowy}few{Dostępne # komponenty fasadowe}many{Dostępnych # komponentów fasadowych}other{Dostępne # komponentu fasadowego}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Niektóre zasoby z innych witryn mogą być leniwie ładowane z użyciem komponentu fasadowego"}, "core/audits/third-party-facades.js | title": {"message": "Leniwe ładowanie zasobów z innych witryn z użyciem komponentu fasadowego"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Dostawca zewnętrzny"}, "core/audits/third-party-summary.js | description": {"message": "Kod spoza witryny może znacznie spowalniać wczytywanie stron. Ogranicz liczbę zewnętrznych dostawców kodu i spróbuj wczytywać kod spoza witryny dopiero po zakończeniu wczytywania podstawowej strony. [<PERSON><PERSON> zminimal<PERSON>ć wpływ zewnętrznego kodu](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "Kod spoza witryny zablokował główny wątek na {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Ogranicz wpływ kodu spoza witryny"}, "core/audits/third-party-summary.js | title": {"message": "Minimalizacja wykorzystania kodu zewnętrznego"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Pomiary"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Ustaw budżet czasowy, aby ł<PERSON><PERSON><PERSON> wydajno<PERSON>ć witryny. Wydajne strony szybko się ładują i reagują na zdarzenia wejściowe użytkowników. [Więcej informacji o budżetach wydajności](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Budżet czasowy"}, "core/audits/unsized-images.js | description": {"message": "W elementach graficznych określ wyraźnie szerokość i wysokość, aby ograniczyć przesunięcia układu i ulepszyć CLS. [<PERSON><PERSON> ustawić wymiary obrazu](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Elementy graficzne nie mają bezpośrednio określonych atrybutów `width` ani `height`"}, "core/audits/unsized-images.js | title": {"message": "Elementy graficzne mają bezpośrednio określone atrybuty `width` i `height`"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Do aplikacji możesz dodać obsługę interfejsu User Timing API, aby mierzyć rzeczywist<PERSON> szybkość aplikacji z punktu widzenia użytkownika. [Więcej informacji o znacznikach czasu działań użytkownika](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 czas działań użytkownika}few{# czasy działań użytkownika}many{# czasów działań użytkownika}other{# czasu działań użytkownika}}"}, "core/audits/user-timings.js | title": {"message": "Znaczniki i odcinki Czasu działań użytkownika"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Znaleziono element `<link rel=preconnect>` dla „{securityOrigin}”, który nie jest używany przez przeglądarkę. Sprawdź, czy poprawnie używasz atrybutu `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Rozważ dodanie wskazówek `preconnect` lub `dns-prefetch`, aby wcze<PERSON><PERSON>j nawiązać połączenia z ważnymi źródłami w innych domenach. [Jak łączyć się z wymaganymi źródłami](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Wcześniej nawiąż połączenia z wymaganymi źródłami"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Znaleziono więcej niż 2 linki `<link rel=preconnect>`. Takich linków należy używać oszczędnie i tylko w przypadku najważniejszych źródeł."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Znaleziono element `<link rel=preconnect>` dla „{securityOrigin}”, który nie jest używany przez przeglądarkę. Elementy `preconnect` stosuj tylko do ważnych źródeł, o które strona na pewno poprosi."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Znaleziono element `<link>` typu preload dla „{preloadURL}”, kt<PERSON>ry nie jest używany przez przeglądarkę. Sprawdź, czy poprawnie używasz atrybutu `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Pomyśl o użyciu elementu `<link rel=preload>`, aby szybciej pobierały się zasoby, które są obecnie żądane na dalszym etapie ładowania strony. [Jak wstępnie wczytywać żądania kluczy](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Załaduj wstępnie kluczowe żądania"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL mapy"}, "core/audits/valid-source-maps.js | description": {"message": "Mapy źródeł tłumaczą zminifikowany kod na oryginalny kod źródłowy. Ułatwia to programistom debugowanie w środowisku produkcyjnym. Oprócz tego Lighthouse może udostępniać dodatkowe statystyki. Warto wdrożyć mapy źródeł, aby korzystać z tych możliwości. [Więcej informacji o mapach źródeł](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Brakuje map źródeł dla własnych dużych plików JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Duży plik JavaScript nie ma mapy źródeł"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Ostrzeżenie: brak 1 elementu w atrybucie `.sourcesContent`}few{Ostrzeżenie: brak # elementów w atrybucie `.sourcesContent`}many{Ostrzeżenie: brak # elementów w atrybucie `.sourcesContent`}other{Ostrzeżenie: brak # elementu w atrybucie `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Strona ma prawidłowe mapy źródeł"}, "core/audits/viewport.js | description": {"message": "Metatag `<meta name=\"viewport\">` nie tylko optymalizuje aplikację pod kątem rozmiarów ekranu urządzeń mobilnych, ale zapobiega też [300-milisekundowemu opóźnieniu danych wejściowych użytkownika](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Więcej informacji o korzystaniu z metatagu viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Nie znaleziono tagu `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Nie zawiera tagu `<meta name=\"viewport\">` z elementem `width` lub `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Zawiera tag `<meta name=\"viewport\">` z elementem `width` lub `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Jest to zadanie blokowania wątku, które miało miejsce podczas pomiaru czasu od interakcji do kolejnego wyrenderowania. [Więcej informacji o czasie od interakcji do kolejnego wyrenderowania](https://web.dev/articles/inp)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms na zdarzenie „{interactionType}”"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Cel zdarzenia"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimalizowanie pracy podczas kluczowej interakcji"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Opóźnienie wejściowe"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Opóźnienie prezentacji"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Czas przetwarzania"}, "core/audits/work-during-interaction.js | title": {"message": "Minimalizowanie pracy podczas kluczowej interakcji"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "To są możliwości lepszego wykorzystania atrybutów ARIA w Twojej aplikacji, by by<PERSON> ona wygodniejsza dla użytkowników technologii wspomagających, takich jak czytniki ekranu."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "To są możliwości dostarczenia alternatywnej treści dla audio i wideo. Dzięki temu treści mogą stać się bardziej przystępne dla osób niedowidzących i niedosłyszących."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Dźwięk i obraz"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Te pozycje wskazują typowe sprawdzone metody ułatwień dostępu."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Sprawdzone metody"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Te testy wskazują moż<PERSON>ci [ulepszenia ułatwień dostępu w Twojej aplikacji internetowej](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatycznie wykrywane są tylko niektóre problemy, co nie daje g<PERSON>, że w Twojej aplikacji internetowej będą działać ułatwienia dostępu, dlatego zalecam<PERSON> [testy ręczne](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Te pozycje dotyczą obszarów, których narzędzie do testów automatycznych nie może zbadać. Więcej informacji w naszym przewodniku po [prowadzeniu przeglądu ułatwień dostępu](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Ułatwienia dostępu"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "To są możliwości poprawy czytelności treści."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "To są możliwości ulepszenia interpretacji treści przez użytkowników mających różne ustawienia języka."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacjonalizacja i lokalizacja"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "To są możliwości ulepszenia semantyki kontrolek aplikacji. Dzięki temu treści mogą stać się bardziej przystępne dla użytkowników technologii wspomagających, takich jak czytniki ekranu."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nazwy i etykiety"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "To są możliwości ulepszenia nawigacji za pomocą klawiatury w Twojej aplikacji."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Tutaj znajdziesz informacje o tym, jak mo<PERSON><PERSON><PERSON>wi<PERSON> odczytywanie danych z tabel i list za pomocą technologii wspomagających osoby z niepełnosprawnością (np. czytników ekranu)."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele i listy"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Zgodność z przeglądarką"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Sprawdzone metody"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Ogólne"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Zaufanie i bezpieczeństwo"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Wygoda użytkowników"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Budżety wydajności są podstawą do określania standardów wydajności witryny."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budżety"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Więcej o wydajności aplikacji. Te liczby nie mają [bezpośredniego wpływu](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na wyniki w kategorii Wydajność."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostyka"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najważnie<PERSON><PERSON><PERSON> aspektem wydaj<PERSON>ści jest to, jak szybko piksele zostaną wyświetlone na ekranie. Kluczowe wskaźniki: <PERSON><PERSON><PERSON> wyrenderowanie treści, <PERSON><PERSON><PERSON> wyrenderowanie elementu znaczącego"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Ulepszenia pierwszego renderowania"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Te sugestie mogą pomóc przy<PERSON><PERSON>zyć wczytywanie strony. Nie mają one [bezpośredniego wpływu](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na wynik w kategorii Wydajność."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>ści"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Usprawnij całe ładowanie, by strona jak naj<PERSON><PERSON>b<PERSON>j była gotowa do używania i reagowała na działania użytkownika. Główne wskaźniki: Czas do pełnej interaktywności, <PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Ogólne usprawnienia"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Te testy służą do sprawdzenia różnych aspektów progresywnej aplikacji internetowej. [<PERSON><PERSON><PERSON>, co sprawia, że progresywna aplikacja internetowa jest prawidłowa](https://web.dev/articles/pwa-checklist)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Te testy są wymagane w ramach podstawowej [listy kontrolnej PWA](https://web.dev/articles/pwa-checklist), ale narzędzie Lighthouse nie przeprowadza ich automatycznie. Nie mają wpływu na wynik, ale należy pamiętać o wykonaniu ich ręcznie."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Możliwa do zainstalowania"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optymalizacja dla PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Te testy sprawdzają, czy strona została utworzona z zastosowaniem podstawowych zaleceń dotyczących optymalizacji witryn pod kątem wyszukiwarek (SEO). Na pozycję strony w wyszukiwarce mogą wpływać inne czynniki, których Lighthouse nie sprawdza, np. wydajność mierzona za pomocą [podstawowych wskaźników internetowych](https://web.dev/explore/vitals). [Dow<PERSON>z się więcej o podstawowych zasadach wyszukiwania w Google](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Uruchom te dodatkowe walidatory w swojej witrynie, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wi<PERSON><PERSON>j sprawdzonych metod SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Dostosuj kod HTML w taki sposób, by <PERSON><PERSON> le<PERSON>j rozumiały z<PERSON>ć aplik<PERSON>."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Sprawdzone metody publikowania treści"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Jeśli Twoja aplikacja ma pojawiać się w wynikach wyszukiwania, muszą mieć do niej dostęp roboty."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Skanowanie i indeksowanie"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Dostosuj strony do komórek, aby użytkownicy nie musieli pomniejszać ani powię<PERSON> ekranu, gdy będą chcieli coś przeczytać. [<PERSON><PERSON><PERSON>, jak dostosować strony do komórek](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Na komórki"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Testowane urządzenie wydaje się mieć procesor o szybkości mniejszej niż oczekiwana przez Lighthouse. Może to negatywnie wpływać na wynik w kategorii Wydajność. Dowiedz się więcej o [kalibrowaniu odpowiedniego mnożnika spowolnienia procesora](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Strona może nie wczytywać się prawidłowo, ponieważ testowy URL ({requested}) został przekierowany pod adres {final}. Przetestuj drugi adres URL bezpośrednio."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Wczytywanie strony trwało za długo i nie zostało ukończone w przewidzianym na to czasie. Wyniki mogą być niepełne."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Przekroczono limit czasu czyszczenia pamięci podręcznej przeglądarki. Ponownie sprawdź tę stronę, a jeśli problem nie ust<PERSON>pi, zg<PERSON><PERSON><PERSON> błąd."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{<PERSON><PERSON><PERSON><PERSON>, że przechowywane są dane, które mają wpływ na szybkość wczytywania w tej lokalizacji: {locations}. Aby uchronić się przed wpływem tych zasobów na wyniki, sprawdź tę stronę w oknie incognito.}few{Możliwe, że przechowywane są dane, które mają wpływ na szybkość wczytywania w tych lokalizacjach: {locations}. Aby uchronić się przed wpływem tych zasobów na wyniki, sprawdź tę stronę w oknie incognito.}many{Możliwe, że przechowywane są dane, które mają wpływ na szybkość wczytywania w tych lokalizacjach: {locations}. Aby uchronić się przed wpływem tych zasobów na wyniki, sprawdź tę stronę w oknie incognito.}other{<PERSON><PERSON><PERSON><PERSON>, że przechowywane są dane, które mają wpływ na szybkość wczytywania w tych lokalizacjach: {locations}. Aby uchronić się przed wpływem tych zasobów na wyniki, sprawdź tę stronę w oknie incognito.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Przekroczono limit czasu czyszczenia danych origin. Ponownie sprawdź tę stronę, a jeśli problem nie ust<PERSON>pi, z<PERSON><PERSON><PERSON><PERSON> błąd."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Podczas analizowania nagłówka `link` ({error}) wystąpił błąd: `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Podczas działania wykryto nawigację na stronie. Nie zalecamy używania trybu zakresu czasu do kontroli nawigacji na stronach. Aby kontrolować nawigację na stronach, używaj trybu nawigacji. Pozwoli to ulepszyć atrybucję firm zewnętrznych i wykrywanie wątków głównych."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Tylko strony wczytane za pomocą żądania GET kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "W pamięci mogą być przechowywane tylko strony z kodem stanu 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Przeglądarka Chrome wykryła próbę wykonania JavaScriptu podczas przechowywania strony w pamięci podręcznej."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON>, które poprosiły o AppBanner, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu żądania utrzymywania aktywności."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona przez flagi. Wejdź na chrome://flags/#back-forward-cache, aby włączyć ją lokalnie na tym urządzeniu."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona przez wiersz poleceń."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu niewystarczającej ilości pamięci."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej nie jest obsługiwana w ramach przekazywania."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona w przypadku renderowania wstępnego."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Strona nie może być przechowywana w pamięci podręcznej, bo zawiera wystąpienie BroadcastChannel z zarejestrowanymi detektorami."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Strony z nagłówkiem cache-control:no-store nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON><PERSON>ć podręczna została celowo wyczyszczona."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Strona została usunięta z pamięci podręcznej, aby umożliwić przechowywanie w niej innej strony."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "St<PERSON><PERSON>, które zawierają w<PERSON>ki, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Strony, które używają interfejsu FileChooser API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Strony, które używają interfejsu File System Access API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON>, które używają dyspozytora nośnika danych, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON>dt<PERSON><PERSON><PERSON>ł włą<PERSON>, gdy użytkownik zamknął stronę."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Strony, które używają interfejsu MediaSession API i określają stan odtwarzania, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Strony, które używają interfejsu MediaSession API i określają elementy działania, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu czytnika ekranu."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> uży<PERSON>ją <PERSON>, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Strony, które używają interfejsu Serial API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Strony, które używają interfejsu WebAuthetication API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Strony, które używają interfejsu WebBluetooth API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Strony, które używają interfejsu WebUSB API, nie kwalifikują się do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, bo na stronie, która korzysta z elementu `Cache-Control: no-store`, wyłączone są pliki cookie."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Strony, które używają dedykowanej instancji roboczej lub workletu, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokument nie został w pełni wczytany, zanim użytkownik go zamknął."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Podczas zamykania strony widoczny był baner aplikacji."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Podczas zamykania strony widoczny był Menedżer haseł Chrome."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Podczas zamykania strony aktywny był proces destylacji DOM."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Podczas zamykania strony widoczna była przeglądarka narzędzia DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona, bo rozszerzenia używają interfejsu Messaging API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Rozszerzenia od dłuższego czasu połączone powinny zakończyć połączenie przed uzyskaniem dostępu do pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Rozszerzenia od dłuższego czasu połączone próbowały wysłać wiadomości do ramek w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu rozszerzeń."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Podczas zamykania strony wyświetliło się okno modalne takie jak ponowne przesłanie formularza lub okno hasła http."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Podczas zamykania strony pokazywana była strona offline."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Podczas zamykania strony widoczny był pasek interwencji związanej z brakiem miejsca w pamięci."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Podczas zamykania strony pojawiły się prośby o uprawnienia."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Podczas zamykania strony widoczne było blokowanie wyskakujących okienek."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Podczas zamykania strony wyświetliły się szczegóły Bezpiecznego przeglądania."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezpieczne przeglądanie wykryło na tej stronie nadużycie i zablokowało wyskakujące okienko."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Skrypt service worker z<PERSON><PERSON><PERSON>, gdy strona znajdowała się w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu błędu w dokumencie."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Stron używających FencedFrames nie można przechowywać w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Strona została usunięta z pamięci podręcznej, aby umożliwić przechowywanie w niej innej strony."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Strony, kt<PERSON>re mają przyznany dostęp do strumienia multimediów, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "St<PERSON><PERSON>, które używają portali, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "St<PERSON><PERSON>, kt<PERSON>re używają <PERSON>a, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Strony, które mają otwarte połączenie IndexedDB, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu zdarzenia IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Użyto nieodpowiednich interfejsów API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Strony z interfejsem `JavaScript` wstrzykiwanym przez rozszerzenia nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Strony z interfejsem `StyleSheet` wstrzykiwanym przez rozszerzenia nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Błąd wewnętrzny."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna stanu strony internetowej jest wyłączona z powodu żądania utrzymywania aktywności."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> używają blokady klaw<PERSON>tury, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | loading": {"message": "Strona nie została w pełni wczytana, zanim użytkownik ją opuścił."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>ch zasoby główne mają nagłówek cache-control:no-cache, nie mogą korzysta<PERSON> z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Strony, kt<PERSON><PERSON>ch zasoby główne mają nagłówek cache-control:no-store, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Nawigacja została anulowana przed przywróceniem strony z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Strona została usunięta z pamięci podręcznej, ponieważ aktywne połączenie sieciowe otrzymało za dużo danych. Chrome ogranicza il<PERSON> danych, kt<PERSON>re strona moż<PERSON> o<PERSON><PERSON>, gdy jest przechowywana w pamięci podręcznej."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re mają przesyłane żądanie fetch() lub XHR, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Strona została usunięta z pamięci podręcznej stanu strony internetowej, ponieważ aktywne żądanie sieciowe wiązało się z przekierowaniem."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Strona została usunięta z pamięci podręcznej, ponieważ połączenie sieciowe było zbyt długo otwarte. Chrome ogranicza il<PERSON> c<PERSON>, przez który strona może otrzymywać dane podczas przechowywania w pamięci podręcznej."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON><PERSON>, które nie mają prawidłowego nagłówka odpowiedzi, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Nawigacja odbywała się w ramce innej niż ramka główna."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Strony z trwającymi transakcjami indeksowanej bazy danych nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Strony z przesyłanym żądaniem sieciowym nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Strony z przesyłanym żądaniem sieciowym fetch() nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Strony z przesyłanym żądaniem sieciowym nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Strony z przesyłanym żądaniem sieciowym XHR nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "St<PERSON>y, które używają PaymentManagera, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON><PERSON>, które używają obrazu w obrazie, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | portal": {"message": "St<PERSON><PERSON>, które używają portali, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | printing": {"message": "Strony, które wyświetlają interfejs drukowania, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Strona została otwarta za pomocą „`window.open()`” i inna karta się do niej odnosi lub strona otworzyła okno."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "W trakcie renderowania strony w pamięci podręcznej stanu strony internetowej wystąpił błąd."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Proces renderowania strony w pamięci podręcznej stanu strony internetowej został przerwany."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "St<PERSON>y, które poprosiły o przyznanie uprawnień do nagrywania dźwięku, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Strony, które poprosiły o przyznanie uprawnień dotyczących czujników, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do pobierania lub synchronizacji w tle, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "St<PERSON>y, które poprosiły o przyznanie uprawnień dotyczących MIDI, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do powiadomień, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "St<PERSON>y, które poprosiły o przyznanie dostępu do pamięci, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Strony, które poprosiły o przyznanie uprawnień do nagrywania filmów, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "W pamięci mogą być przechowywane tylko strony, kt<PERSON><PERSON>ch schemat URL to HTTP/HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Strona została wywołana przez skrypt service worker podczas korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Skrypt service worker p<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON> `MessageEvent` stronie w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Skrypt service worker by<PERSON>, gdy strona znajdowała się w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Strona została usunięta z pamięci podręcznej stanu strony internetowej z powodu aktywacji skryptu service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Przeglądarka Chrome uruchomiła się ponownie i wyczyściła wpisy w pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re używają <PERSON>d<PERSON>ker, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re używają SpeechRecognizer, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "St<PERSON><PERSON>, które używają SpeechSynthesis, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Element iframe na stronie rozpoczął nawigację, która się nie zakończyła."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>rych zasoby podrzędne mają nagłówek cache-control:no-cache, nie mogą korzysta<PERSON> z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Strony, kt<PERSON>rych zasoby podrzędne mają nagłówek cache-control:no-store, nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Strona przekroczyła limit czasu w pamięci podręcznej stanu strony internetowej i wygasła."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Strona przekroczyła limit czasu w trakcie zapisywania w pamięci podręcznej stanu strony internetowej (prawdopodobnie z powodu długotrwałych wyrażeń pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Strona ma wyrażenie unload w ramce głównej."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Strona ma wyrażenie unload w ramce podrzędnej."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Przeglądarka zmieniła nagłówek zastępowania klienta użytkownika."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON>, kt<PERSON>re mają przyznany dostęp do nagrywania filmu lub dźwięku, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Strony, które używają WebDatabase, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Strony, które używają WebHID, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Strony, które używają WebLocks, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "St<PERSON>y, które używają WebNfc, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Strony, które używają WebOTPService, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Strony z WebRTC nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Strony, które używają WebShare, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Strony z WebSocket nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Strony z WebTransport nie mogą korzystać z pamięci podręcznej stanu strony internetowej."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Strony, które używają WebXR, nie kwalifikują się obecnie do korzystania z pamięci podręcznej stanu strony internetowej."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Zastanów się, czy nie dodać schematów adresów URL https: i http: (ignorowanych przez przeglądarki obsługujące dyrektywę `'strict-dynamic'`), aby u<PERSON><PERSON><PERSON> zgodność wsteczną ze starszymi przeglądarkami."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Od momentu wprowadzenia CSP3 dyrektywa `disown-opener` jest wyco<PERSON>a. Zamiast niej użyj nagłówka Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Od momentu wprowadzenia CSP2 dyrektywa `referrer` jest wyco<PERSON>a. Zamiast niej użyj nagłówka Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Od momentu wprowadzenia CSP2 dyrektywa `reflected-xss` jest wycofana. Zamiast niej użyj nagłówka X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Brak dyrektywy `base-uri` umożliwia wstrzyknięcie tagów `<base>`. W ten sposób hakerzy mogą ustawić podstawowy adres URL dla wszystkich względnych adresów URL (np. skryptów) na domenę, którą kontrolują. Dyrektywę `base-uri` warto ustawić na `'none'` lub `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Brak dyrektywy `object-src` umożliwia wstrzykiwanie wtyczek, które wykonują niebezpieczne skrypty. <PERSON><PERSON><PERSON> to możliwe, ustaw dyrektywę `object-src` na `'none'`."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "<PERSON>rak dyrektywy `script-src`. <PERSON><PERSON><PERSON> to pozwolić na uruchamianie niebezpiecznych skryptów."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Prawdopodobnie brakuje średnika. Ciąg {keyword} wygląda jak dyrektywa, a nie jak słowo kluczowe."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "W liczbach jednorazowych należy używać zestawu znaków base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Liczby jednorazowe powinny składać się co najmniej z 8 znaków."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Unikaj używania w tej dyrektywie płaskich schematów URL ({keyword}). Płaskie schematy URL zezwalają na pobieranie skryptów z niebezpiecznej domeny."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Unikaj używania w tej dyrektywie płaskich symboli zastępczych ({keyword}). Płaskie symbole zastępcze zezwalają na pobieranie skryptów z niebezpiecznej domeny."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Miejsce docelowe raportowania zostało skonfigurowane tylko za pomocą dyrektywy report-to. Jest ona obsługiwana jedynie w przeglądarkach opartych na Chromium, dlatego zaleca się też używanie dyrektywy `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Dla CSP nie skonfigurowano żadnego miejsca docelowego raportowania. Będzie to powodować utrudnienia w późniejszym konserwowaniu CSP i monitorowaniu problemów."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Listy dozwolonych hostów mogą być często pomijane. Zastan<PERSON> się, czy nie uż<PERSON> liczb jednorazowych lub haszy CSP w połączeniu z dyrektywą `'strict-dynamic'`, je<PERSON><PERSON> to konieczne."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Nieznana dyrektywa CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} jest praw<PERSON><PERSON><PERSON><PERSON><PERSON> nieprawidłowym słowem kluczowym."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Dyrektywa `'unsafe-inline'` zezwala na wykonywanie niebezpiecznych skryptów na stronie i uruchamianie modułów obsługi zdarzeń. Zastanów się, czy nie uż<PERSON> liczb jednorazowych lub haszy CSP, aby zezwolić osobno na poszczególne skrypty."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "<PERSON><PERSON><PERSON><PERSON> się, czy nie dodać dyrektywy `'unsafe-inline'` (ignorowanej przez przeglądarki obsługujące liczby jednorazowe i hasze), aby u<PERSON><PERSON><PERSON> zgodność wsteczną ze starszymi przeglądarkami."}, "core/lib/deprecation-description.js | feature": {"message": "Więcej szczegółów znajdziesz na stronie z informacjami o stanie funkcji."}, "core/lib/deprecation-description.js | milestone": {"message": "Ta zmiana zostanie wprowadzona w wersji {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Użyto wyco<PERSON>ej <PERSON>"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "W przypadku obsługi nagłówka CORS `Access-Control-Allow-Headers` autoryzacja nie będzie przeprowadzana przy użyciu symboli wieloznacznych (*)."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "A<PERSON> w<PERSON><PERSON><PERSON> domyślną integrację przesyłania, nie używaj selektora `-internal-media-controls-overlay-cast-button`. Zamiast tego użyj atrybutu `disableRemotePlayback`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Żądania zasobów, których adresy URL zawierają znaki `(n|r|t)` z usuniętymi odstępami i znaki mniejszości (`<`), są blokowane. Usuń nowe wiersze i zakoduj znaki mniejszości z miejsc takich jak wartości atrybutów elementów, aby umożliwić wczytywanie tych zasobów."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Interfejs `chrome.loadTimes()` został wycofany. Użyj standaryzowanego interfejsu API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Interfejs `chrome.loadTimes()` został wycofany. Użyj standaryzowanego interfejsu API: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Interfejs `chrome.loadTimes()` został wycofany. Użyj standaryzowanego interfejsu API: `nextHopProtocol` w Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Pliki cookie zawierające znak `(0|r|n)` będ<PERSON> odrzucane (nie będą skracane)."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Złagodzenie zasady dotyczącej tej samej domeny przez ustawienie `document.domain` zostało wycofane i będzie domyślnie wyłączone. To ostrzeżenie o wycofaniu dotyczy dostępu z tej samej domeny, kt<PERSON>ry był włączony przez ustawienie `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Możliwość aktywowania funkcji window.alert z elementów iframe z innych domen została wycofana i w przyszłości zostanie usunięta."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Możliwość aktywowania funkcji window.confirm z elementów iframe z innych domen została wycofana i w przyszłości zostanie usunięta."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Zdarzenia mutacji DOM, w tym `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` i `DOMCharacterDataModified`, są wycofane (https://w3c.github.io/uievents/#legacy-event-types) i zostaną usunięte. Zamiast nich używaj elementu `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Obsługa danych: adresy URL w elemencie SVG <use> zostały wycofane i w przyszłości zostaną usunięte."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Złagodzenie zasady dotyczącej tej samej domeny przez ustawienie `document.domain` zostało wycofane i będzie domyślnie wyłączone. Aby nadal używać tej funkcji, przestań używać klastrów agentów ze źródłem jako kluczem, wysyłając nagłówek `Origin-Agent-Cluster: ?0` z odpowiedzią HTTP dla dokumentu i ramek. Więcej informacji znajdziesz na stronie https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Nagł<PERSON>ek `Expect-CT` został wycofany i zostanie usunięty. Chrome wymaga protokołu Certificate Transparency w przypadku wszystkich publicznie zaufanych certyfikatów wystawionych po 30 kwietnia 2018 roku."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Metody `getCurrentPosition()` i `watchPosition()` nie działają już w przypadku niezabezpieczonych źródeł. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "<PERSON>ycofano metody `getCurrentPosition()` i `watchPosition()` w niezabezpieczonych źródłach. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "Metoda `getUserMedia()` nie działa już w przypadku niezabezpieczonych źródeł. Aby używać tej funkcji, rozważ przełączenie aplikacji na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Pole `RTCPeerConnectionIceErrorEvent.hostCandidate` zostało wycofane. Użyj pola `RTCPeerConnectionIceErrorEvent.address` lub `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Dane pochodzenia sprzedawcy i arbitralne dane ze zdarzenia skryptu service worker `canmakepayment` nie są już używane i zostaną usunięte: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Witryna zażądała zasobu podrzędnego z sieci, do której miała dostęp wyłącznie z powodu uprawnień sieci użytkownika. Takie żądania ujawniają w internecie niepubliczne urządzenia i serwery, zwiększając ryzyko ataku polegającego na sfałszowaniu żądania z innej witryny (CSRF) lub wycieku informacji. Aby zapobiegać temu ryzyku, wycofujemy z Chrome możliwość przesyłania żądań do niepublicznych zasobów podrzędnych, gdy są wysyłane z niezabezpieczonych kontekstów. Chrome będzie je blokować."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Nazwa pola `dailyUpdateUrl` dla elementu `InterestGroups` przekazanego do `joinAdInterestGroup()` została zmieniona na `updateUrl`, aby trafniej odzwierciedlała jego działanie."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "CSS nie można ładować z adresów URL `file:`, chyba że są zakończone rozszerzeniem pliku `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Używanie metody `SourceBuffer.abort()` w celu przerwania procesu usuwania zakresu asynchronicznego przez metodę `remove()` zostało wycofane z powodu zmiany specyfikacji. Obsługa zostanie wycofana w przyszłości. Zamiast tego zacznij nasłuchiwać zdarzenia `updateend`. Metoda `abort()` służy tylko do przerywania procesu dołączania asynchronicznych multimediów lub resetowania stanu parsera."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Ustawianie `MediaSource.duration` poniżej najwyższej sygnatury czasowej prezentacji dowolnych buforowanych ramek kodu zostało wycofane z powodu zmiany specyfikacji. W przyszłości wycofamy możliwość usuwania przyciętych multimediów buforowanych. Zamiast tego użyj bezpośrednio metod `remove(newDuration, oldDuration)` w przypadku wszystkich elementów `sourceBuffers`, gdzie `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI poprosi o uprawnienia do użycia, nawet jeśli w interfejsie `MIDIOptions` nie określono komunikatów SysEx."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "<PERSON><PERSON>, niestandaryzowany atrybut `shadowroot` został wycofany i w wersji M119 *nie będzie już <PERSON>*. Użyj nowego, ustandaryzowanego atrybutu `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Nie można już używać interfejsu Notification API z niezabezpieczonych źródeł. Przełącz aplikację na zabezpieczone źródło, np. HTTPS. Więcej informacji znajdziesz na stronie https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Żądanie uprawnienia dla interfejsu Notification API nie może już być wysyłane z elementów iframe z innych domen. Rozważ wysłanie żądania o przyznanie uprawnienia z ramki najwyższego poziomu lub otwarcie nowego okna."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opcja `imageOrientation: 'none'` w metodzie createImageBitmap została wycofana. Użyj tej metody z opcją \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Partner negocjuje przestarzałą wersję (D)TLS. Poproś go o rozwiązanie problemu."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON><PERSON> okre<PERSON><PERSON> w<PERSON> `overflow: visible` w tagach img, video i canvas, mogą one wyświ<PERSON><PERSON>ć treści wizualne poza granicami elementu. Więcej informacji znajdziesz na https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "Interfejs `paymentManager.instruments` został wycofany. Zamiast niego w przypadku modułów obsługi płatności używaj instalacji „just-in-time”."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "<PERSON>je w<PERSON>nie `PaymentRequest` pomijało dyrektywę Content Security Policy (CSP) `connect-src`. To ominięcie zostało wycofane. Dodaj identyfikator formy płatności z interfejsu API `PaymentRequest` (w polu `supportedMethods`) do dyrektywy CSP `connect-src`."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Interfejs `StorageType.persistent` został wycofany. Zamiast niego użyj standaryzowanego typu `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Element `<source src>` z elementem nadrzędnym `<picture>` jest nieprawidłowy i dlatego jest ignorowany. Zamiast niego użyj elementu `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame jest specyficzna dla dostawcy. Użyj standardowej metody cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame jest specyficzna dla dostawcy. Użyj standardowej metody requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Interfejs HTMLVideoElement.webkitDisplayingFullscreen został wycofany. Użyj interfejsu Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Interfejs HTMLVideoElement.webkitEnterFullScreen() został wycofany. Użyj interfejsu Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Interfejs HTMLVideoElement.webkitEnterFullscreen() został wycofany. Użyj interfejsu Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Interfejs HTMLVideoElement.webkitExitFullScreen() został wycofany. Użyj interfejsu Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Interfejs HTMLVideoElement.webkitExitFullscreen() został wycofany. Użyj interfejsu Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Interfejs HTMLVideoElement.webkitSupportsFullscreen został wycofany. Użyj interfejsu Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Wycofujemy interfejs API `chrome.privacy.websites.privacySandboxEnabled`, ale pozostanie on aktywny, aby z<PERSON><PERSON><PERSON><PERSON> zgodn<PERSON> wsteczną do czasu opublikowania wersji M113. Zamiast niego używaj interfejsów `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` i `chrome.privacy.websites.adMeasurementEnabled`. Więcej informacji znajdziesz na stronie https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ogranic<PERSON>ie `DtlsSrtpKeyAgreement` zostało usunięte. W przypadku tego ograniczenia określono warto<PERSON> `false`, co jest interpretowane jako próba użycia usuniętej metody `SDES key negotiation`. Ta funkcja została usunięta. Użyj usługi, która obsługuje `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ograniczenie `DtlsSrtpKeyAgreement` zostało usunięte. W przypadku tego ograniczenia określono wartoś<PERSON> `true`, która nie ma zastosowania. W razie potrzeby moż<PERSON><PERSON> us<PERSON> to ograniczenie."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Metoda getStats() z wywołaniem zwrotnym nie jest już używana i zostanie usunięta. Użyj metody getStats() zgodnej ze specyfikacją."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Interfejs Range.expand() został wycofany. Użyj interfejsu Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Żądania zasobów podrzędnych, których adresy URL zawierają umieszczone dane logowania (np. `**********************/`), są blokowane."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Opcja `rtcpMuxPolicy` jest wycofana i zostanie usunięta."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` b<PERSON><PERSON><PERSON> wymagać witryny izolowanej od zasobów z innych domen. Więcej informacji znajdziesz na stronie https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Mo<PERSON><PERSON><PERSON>ść wywołania interfejsu `speechSynthesis.speak()` bez aktywacji użytkownika jest wycofana i zostanie usunięta."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Aby nadal u<PERSON><PERSON><PERSON> obiektu `SharedArrayBuffer`, rozszerzenia powinny korzystać z witryn izolowanych od zasobów z innych domen. Więcej informacji znajdziesz na stronie https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Usługa Web SQL została wycofana. Użyj bazy danych SQLite WebAssembly lub Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Deskryptor uprawnień `window-placement` został wycofany. Użyj w zamian zasady `window-management`. Więcej informacji znajdziesz na stronie https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Zasada dotycząca uprawnień `window-placement` została wycofana. Użyj w zamian zasady `window-management`. Więcej informacji znajdziesz na stronie https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "Kodowanie UTF-16 nie jest obsługiwane w kodzie JSON odpowiedzi na żądanie `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchroniczny interfejs `XMLHttpRequest` w głównym wątku został wycofany z powodu negatywnego wpływu na sposób obsługi. Więcej informacji znajdziesz na stronie https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Metoda `supportsSession()` została wycofana. Użyj metody `isSessionSupported()` i sprawdź znalezioną wartość logiczną."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON>zas blokowania głównego wątku"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Czas przechowywania danych w pamięci podręcznej"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Opis"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Czas trwania"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Nieprawidłowe elementy"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Lokalizacja"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nazwa"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Przekroczenie budżetu"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Żądania"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Rozmiar"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Źródło"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON> roz<PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Spędzony czas"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Rozmiar przesłanych danych"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potencjalne oszczędności"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potencjalne oszczędności"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potencjalna oszczędność: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Znaleziono 1 element}few{Znaleziono # elementy}many{Znaleziono # elementów}other{Znaleziono # elementu}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potencjalne przyspieszenie o {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Pierwsze wyrenderowanie elementu znaczącego"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Czcionka"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Od interakcji do następnego wyrenderowania"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Średni"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Wyrenderowanie największej części treści"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maks. potencjalne opóźnienie po pierwszej interakcji"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multimedia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Skrypt"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Arkusz stylów"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Zewnętrzne"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Razem"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Podczas rejestrowania śladu wczytywania strony wystąpił błąd. Uruchom Lighthouse ponownie. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Upłynął czas oczekiwania na wstępne połączenie z Protokołem debugującym."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Podczas wczytywania strony w Chrome nie zostały utworzone zrzuty ekranu. Zapewnij widoczność treści na stronie, a potem uruchom Lighthouse jeszcze raz. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Serwery DNS nie potrafiły ustalić adresu podanej domeny."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Wys<PERSON>ą<PERSON>ł błąd wymaganego elementu zbierającego {artifactName}: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Wystąpił wewnętrzny błąd Chrome. Uruchom ponownie Chrome, a następnie Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Wymagany element zbierający {artifactName} nie został uruchomiony."}, "core/lib/lh-error.js | noFcp": {"message": "Strona nie wyrenderowała żadnej treści. Upewnij się, że okno przeglądarki jest na pierwszym planie podczas ładowania, i spróbuj ponownie. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Strona nie wyświetlała treści powiązanych z renderowaniem największej części treści (LCP). <PERSON>ew<PERSON>j się, że strona zawiera prawidłowy element LCP, a następnie spróbuj ponownie. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Podana strona nie ma formatu HTML (jest wy<PERSON><PERSON><PERSON><PERSON> jako typ MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ta wersja Chrome jest przestarzała i nie obsługuje wartości „{featureName}”. <PERSON><PERSON> <PERSON><PERSON><PERSON> wszystkie wyniki, użyj nowszej wersji."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanego URL-a, ponieważ strona przestała odpowiadać."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Podany URL nie ma prawidłowego certyfikatu bezpieczeństwa. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Przeglądarka Chrome zablokowała wczytywanie strony i wyświetliła komunikat pełnoekranowy. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania. Szczegóły: {errorDetails}"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania. Kod stanu: {statusCode}"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Wczytywanie strony trwało zbyt długo. Skorzystaj z możliwości przyspieszenia wczytywania strony podanych w raporcie, a następnie ponownie uruchom Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Przekroczono przydzielony czas oczekiwania na odpowiedź protokołu DevTools. Metoda: {protocolMethod}"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Przekroczono czas przydzielony na pobranie zasobów"}, "core/lib/lh-error.js | urlInvalid": {"message": "Podany URL jest nieprawidłowy."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania. Kod stanu: {errorCode}"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Typ MIME strony to XHTML: Lighthouse nie obsługuje tego typu dokumentu"}, "core/user-flow.js | defaultFlowName": {"message": "Wzorzec przeglądania ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Raport dotyczący nawigacji ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Raport dotyczący określonego momentu ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Raport dotyczący okresu ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Wszystkie raporty"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Ułatwienia dostępu"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Sprawdzone metody"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresywna aplikacja internetowa"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON> kompute<PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Omówienie raportu dotyczącego procesu Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Omówienie procesów"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Używaj raportów dotyczących nawigacji do tych celów:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Używaj raportów dotyczących określonego momentu do tych celów:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Używaj raportów dotyczących okresu do tych celów:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "uzyskiwanie danych o wydajności Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "sprawdzanie parametrów szybkości ładowania strony, takich jak wyrenderowanie największej części treści czy indeks szyb<PERSON>ci"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "ocena możliwości progresywnej aplikacji internetowej"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "znajdowanie problemów dotyczących ułatwień dostępu w aplikacjach jednostronicowych lub złożonych formularzach"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "ocena sprawdzonych metod dotyczących elementów menu i interfejsu ukrytych za interakcją"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "pomiar czasu wykonywania przesunięć układu i JavaScriptu w serii interakcji"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "odkrywanie możliwości poprawy wydajności w celu usprawnienia działania istniejących od dawna stron i aplikacji jednostronicowych"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Najwięks<PERSON> wpływ"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} audyt informacyjny}few{{numInformative} audyty informacyjne}many{{numInformative} audytów informacyjnych}other{{numInformative} audytu informacyjnego}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Wersja mobilna"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Wczytywan<PERSON> strony"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Raporty dotyczące nawigacji analizują ładowanie pojedynczej strony – dokładnie tak jak oryginalne raporty Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Raport dotyczący nawigacji"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} raport dotyczący nawigacji}few{{numNavigation} raporty dotyczące nawigacji}many{{numNavigation} raportów dotyczących nawigacji}other{{numNavigation} raportu dotyczącego nawigacji}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} audyt zadowalający}few{{numPassableAudits} audyty zadowalające}many{{numPassableAudits} audyt<PERSON> zadowalających}other{{numPassableAudits} audytu zadowalającego}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} audyt zaliczony}few{{numPassed} audyty zaliczone}many{{numPassed} audytów zaliczonych}other{{numPassed} audytu zaliczone}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Średnia"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Błąd"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Dobra"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Zarejestrowany stan strony"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Raporty dotyczące określonego momentu analizują strony w konkretnym stanie, zwykle po interakcji z użytkownikiem."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Raport dotyczący określonego momentu"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} raport dotyczący określonego momentu}few{{numSnapshot} raporty dotyczące określonego momentu}many{{numSnapshot} raportów dotyczących określonego momentu}other{{numSnapshot} raportu dotyczącego określonego momentu}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Podsumowanie"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interakcje użytkownika"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Raporty dotyczące okresu analizują dowolne okresy, zwykle obejmujące interakcje z użytkownikiem."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Raport dotyczący okresu"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} raport dotyczący okresu}few{{numTimespan} raporty dotyczące okresu}many{{numTimespan} raportów dotyczących okresu}other{{numTimespan} raportu dotyczącego okresu}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Raport Lighthouse dotyczący przepływu użytkowników"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "W przypadku treści animowanych stosuj atrybut [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wykorzystanie procesora, gdy te treści znajdują się poza ekranem."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Sugerujemy wyświetlanie wszystkich komponentów [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) w formatach WebP i podanie odpowiednich zasobów zastępczych dla innych przeglądarek. [Więcej informacji](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Upew<PERSON>j się, że używasz elementu [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) dla obrazów, które są automatycznie ładowane w leniwy sposób. [Więcej informacji](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Korzystaj z narzędzi takich jak [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), aby [renderować układy AMP po stronie serwera](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Sprawdź w [dokumentacji AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), czy wszystkie style są obsługiwane."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Element [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ma atrybut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) w<PERSON><PERSON><PERSON><PERSON><PERSON>, których zasobów graficznych użyć w zależności od rozmiaru ekranu. [Więcej informacji](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "<PERSON><PERSON><PERSON>ne są bard<PERSON> du<PERSON> listy, proponujemy zastosowanie przewijania wirtualnego z pakietu CDK (Component Dev Kit). [Wię<PERSON>j informacji](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Zastos<PERSON>j [dzielenie kodu na poziomie przekierowań](https://web.dev/route-level-code-splitting-in-angular/), by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wiel<PERSON><PERSON><PERSON> pakietów JavaScript. Pomyśl też o wstępnym zapisywaniu zasobów w pamięci podręcznej za pomocą [skryptu service worker Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON><PERSON> używasz interfejsu wiersza <PERSON>ular, up<PERSON><PERSON><PERSON>, że kompilacje są generowane w trybie produkcyjnym. [Więcej informacji](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON> używasz interfejsu wiersza poleceń Angular, dołącz do kompilacji produkcyjnej mapy źródeł w celu inspekcji pakietów. [Więcej informacji](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON>, zała<PERSON>j wcześniej przekierowania. [Więcej informacji](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Do zarządzania punktami przerwania obrazów proponujemy używać narzędzia `BreakpointObserver` z pakietu CDK (Component Dev Kit). [Więcej informacji](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Możesz przesłać plik GIF do usługi, która umożliwi umieszczanie go jako pliku wideo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Gdy w swoim motywie definiujesz niestandardowe czcionki, określ `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>, czy nie skonfigurow<PERSON> w witrynie [formatów obrazu WebP za pomocą funkcji Konwertuj styl obrazu](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Zainstaluj [moduł Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), który umożliwia leniwe ładowanie obrazów. Takie moduły umożliwiają odłożenie ładowania obrazów niewyświetlanych na ekranie w celu poprawy wydajności."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Sugerujemy używanie modułu wbudowującego w stronę krytyczny kod CSS i JavaScript, a w przypadku mniej istotnego kodu CSS lub JavaScript stosowanie atrybutu opóźnienia."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Mo<PERSON>wy, moduły i specyfikacje serwera są nie bez znaczenia dla jego czasu reakcji. By<PERSON> może warto znaleźć lepiej zoptymalizowany motyw, star<PERSON><PERSON> wybrać moduł optymalizujący lub przejść na nowszą wersję serwera. Na serwerach hostujących należy używać pamięci podręcznej kodu operacji PHP oraz buforowania w pamięci operacyjnej (np. Redis lub Memcached), by s<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON> zapytań do bazy danych, a także zoptymalizować logikę aplikacji, by s<PERSON><PERSON><PERSON><PERSON> przygotowy<PERSON><PERSON> strony."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Sugerujemy używanie [stylów obrazów elastycznych](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) w celu zmniejszenia wielkości plików graficznych wczytywanych na stronie. Jeśli używasz Views do wyświetlania wielu elementów treści na stronie, rozważ zastosowanie podziału na strony, aby ograni<PERSON><PERSON><PERSON> liczbę elementów wyświetlanych na jednej stronie."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Musisz mieć włączoną opcję „Aggregate CSS files” (Agreguj pliki CSS) na stronie „Administration » Configuration » Development” (Administracja » Konfiguracja » Programowanie).  Aby Twoja witryna Drupal zapewniała lepszą obsługę agregacji zasobów, musi korzystać z oprogramowania Drupal w wersji 10.1 lub nowszej."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Musisz mieć włączoną opcję „Aggregate JavaScript files” (Agreguj pliki JavaScript) na stronie „Administration » Configuration » Development” (Administracja » Konfiguracja » Programowanie).  Aby Twoja witryna Drupal zapewniała lepszą obsługę agregacji zasobów, musi korzystać z oprogramowania Drupal w wersji 10.1 lub nowszej."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Sugerujemy usunięcie nieużywanych reguł CSS i dołączenie bibliotek Drupala tylko do tych stron lub komponentów na stronach, które ich potrzebują. Szczegółowe informacje znajdziesz w [dokumentacji Drupala](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Aby zidentyfikować dołączone biblioteki, które dodają nieistotny kod CSS, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Motyw/moduł, który dołącza te biblioteki, możesz zidentyfikować na podstawie adresu URL arkusza stylów, gdy witryna Drupala ma wyłączoną agregację kodu CSS. Szukaj motywów/modułów, które mają na liście wiele arkuszy stylów z dużą ilością czerwonego koloru w zasięgu kodu. Motyw/moduł powinien umieszczać arkusz stylów w kolejce tylko wtedy, gdy rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Sugerujemy usunięcie nieużywanych zasobów JavaSciptu i dołączenie bibliotek Drupala tylko do tych stron lub komponentów na stronach, które ich potrzebują. Szczegółowe informacje znajdziesz w [dokumentacji Drupala](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Aby zidentyfikować dołączone biblioteki, które dodają nieistotny kod JavaScript, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Motyw/moduł, który dołącza te biblioteki, możesz zidentyfikować na podstawie adresu URL skryptu, gdy witryna Drupala ma wyłączoną agregację kodu JavaScript. Szukaj motywów/modułów, które mają na liście wiele skryptów z dużą ilością czerwonego koloru w zasięgu kodu. Motyw/moduł powinien umieszczać skrypt w kolejce tylko wtedy, gdy rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Ustaw opcję „Browser and proxy cache maximum age” (Maksymalny wiek przeglądarki i pamięci podręcznej serwera proxy) na stronie „Administration » Configuration » Development” (Administracja » Konfiguracja » Programowanie). Przeczytaj artykuł o [pamięci podręcznej Drupala i optymalizacji w celu zwiększenia wydajności](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Sugerujemy używanie [modułu](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), który automatycznie optymalizuje i zmniejsza pliki graficzne przesłane do witryny, zachowując ich jakość. Należy też używać natywnych [stylów obrazów elastycznych](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) dostępnych w Drupalu (w wersji 8 i nowszych) dla wszystkich obrazów renderowanych w witrynie."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Wskazówki „preconnect” lub „dns-prefetch” można dodać, instalując i konfigurując [moduł](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) obsługujący wskazówki zasobów klienta użytkownika."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Należy używać natywnych [stylów obrazów elastycznych](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) dostępnych w Drupalu (w wersji 8 i nowszych). Używaj stylów obrazów elastycznych do renderowania pól obrazów za pomocą trybów widoku, widoków lub obrazów przesłanych w edytorze WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Optimize Fonts`, aby automatycznie wykorzystać funkcję CSS „`font-display`” do zapewnienia widoczności tekstu dla użytkownika podczas ładowania czcionek internetowych."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Uż<PERSON>j zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Next-Gen Formats`, aby konwertować obrazy do formatu WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Lazy <PERSON>ad Images`, aby odłożyć ładowanie obrazów niewyświetlanych na ekranie do momentu, aż będą potrzebne."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Uż<PERSON>j zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienia `Critical CSS` oraz `Script Delay`, aby opóźnić ładowanie niekrytycznych plików JS/CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Skorzystaj z [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), aby b<PERSON><PERSON><PERSON> swoje treści w naszej ogólnoświatowej sieci i skrócić czas reakcji serwera."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Minify CSS`, aby automatycznie zmniejszyć plik CSS i tym samym ładunki sieciowe."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Minify Javascript`, aby automatycznie zmniejszyć plik JS i tym samym ładunki sieciowe."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Remove Unused CSS`, aby pomóc rozwiązać ten problem. <PERSON><PERSON><PERSON><PERSON> to rozpoznać klasy CSS, które są rzeczywiście używane na poszczególnych stronach Twojej witryny, i usunąć pozostałe klasy, zachowując niewielki rozmiar pliku."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Efficient Static Cache Policy`, aby ustawić zalecane wartości w nagłówku buforowania zasobów statycznych."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Uż<PERSON>j zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Next-Gen Formats`, aby konwertować obrazy do formatu WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Pre-Connect Origins`, aby automatycznie dodać wskazówki zasobów „`preconnect`” w celu wcześniejszego nawiązywania połączeń z ważnymi źródłami w innych domenach."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienia `Preload Fonts` oraz `Preload Background Images`, aby dodać linki `preload` w celu szybszego pobierania zasobów, które są obecnie żądane na dalszym etapie ładowania strony."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Użyj zestawu narzędzi [Ezoic Leap](https://pubdash.ezoic.com/speed) i włącz ustawienie `Resize Images`, aby dostosować rozmiar obrazów odpowiednio do danego urządzenia, zmniejszając ładunki sieciowe."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Aby automatycznie zoptymalizować format obrazu, użyj komponentu `gatsby-plugin-image` zamiast `<img>`. [Więcej informacji](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Aby automatycznie była używana funkcja leniwego ładowania obrazów, użyj komponentu `gatsby-plugin-image` zamiast `<img>`. [Więcej informacji](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Użyj komponentu `gatsby-plugin-image` i ustaw właś<PERSON> `loading` na `eager`. [Więcej informacji](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Aby opóźnić wczytywanie niekrytycznych skryptów spoza witryny, użyj komponentu `Gatsby Script API`. [Więcej informacji](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "<PERSON><PERSON> usun<PERSON>ć nieużywane reguły z arkuszy stylów, uż<PERSON>j wtyczki `PurgeCSS` `Gatsby`. [Więcej informacji](https://purgecss.com/plugins/gatsby.html)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Użyj narzędzia `Webpack Bundle Analyzer`, aby w<PERSON><PERSON><PERSON> nieużywany kod JavaScript. [Więcej informacji](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Skonfiguruj buforowanie zasobów niezmiennych. [Więcej informacji](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "<PERSON><PERSON> <PERSON> j<PERSON>, u<PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image` zamiast `<img>`. [Więcej informacji](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> od<PERSON> war<PERSON> `sizes`, u<PERSON><PERSON>j komponentu `gatsby-plugin-image`. [Więcej informacji](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Możesz przesłać plik GIF do usługi, która umożliwi umieszczanie go jako pliku wideo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Mo<PERSON><PERSON><PERSON> skorzy<PERSON> z [wtyczki](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) lub usługi, która będzie automatycznie konwertować przesłane obrazy do optymalnych formatów."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Zainstaluj [wtyczkę Joomli do leniwego ładowania](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), która umożliwia odłożenie ładowania obrazów niewyświetlanych na ekranie, albo wy<PERSON>rz szablon, który ma tę funkcję. Począwszy od wersji Joomla 4.0 wszystkie nowe obrazy będą [automatycznie](https://github.com/joomla/joomla-cms/pull/30748) mieć atrybut `loading` ustawiany przez funkcje podstawowe."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Jest wiele wtyczek <PERSON>, które mogą pomóc Ci [um<PERSON><PERSON><PERSON><PERSON> w tekście najważniejsze zasoby](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) lub [op<PERSON><PERSON><PERSON><PERSON> wczyt<PERSON>wan<PERSON> tych, które są mniej ważne](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). <PERSON><PERSON><PERSON><PERSON><PERSON>, że optymalizacje dostarczane przez te wtyczki mogą uszkodzić funkcje Twoich szablonów lub innych wtyczek, więc konieczne jest przeprowadzenie dokładnych testów."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>y, rozszerzenia i specyfikacje serwera są nie bez znaczenia dla jego czasu reakcji. <PERSON><PERSON> może warto znaleźć lepiej zoptymalizowany szablon, starannie wybrać rozszerzenie optymalizujące lub przejść na nowszą wersję serwera."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Korzystne może być wyświetlanie fragmentów w poszczególnych kategoriach artykułów (np. przy użyciu linku Przeczytaj więcej), zmniejszenie liczby artykułów wyświetlanych na danej stronie, podział długich postów na kilka stron lub użycie wtyczki umożliwiającej leniwe ładowanie komentarzy."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) może przyspieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji stylów CSS. Istnieją też szablony z takimi funkcjami."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) może przyspieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji skryptów. Istnieją też szablony z takimi funkcjami."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) wczytujących na stronie nieużywany kod CSS albo zmiana tych wtyczek na inne. Aby zidentyfikować rozszerzenia, które dodają nieistotny kod CSS, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Możesz zidentyfikować taki motyw/wtyczkę w adresie URL arkusza stylów. Szukaj wtyczek, które mają na liście wiele arkuszy stylów z dużą ilością czerwonego koloru w zasięgu kodu. Wtyczka powinna umieszczać arkusz stylów w kolejce tylko wtedy, gdy r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) wczytujących na stronie nieużywany kod JavaScript albo zmiana tych wtyczek na inne. Aby zidenty<PERSON><PERSON> wtyczki, które dodają nieistotny kod JS, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Możesz zidentyfikować takie rozszerzenie na podstawie adresu URL skryptu. Szukaj rozszerzeń, które mają na liście wiele skryptów z dużą ilością czerwonego koloru w zasięgu kodu. Rozszerzenie powinno umieszczać skrypt w kolejce tylko wtedy, gdy rzecz<PERSON>wi<PERSON>cie jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Dowiedz się więcej o [pamięci podręcznej przeglądarki w Joomli](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Pomocna może być [wtyczka do optymalizacji obrazów](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), która kompresuje obrazy, zachowując ich jako<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Sugerujemy wykorzystanie [wtyczki obsługującej obrazy elastyczne](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Możesz włączyć kompresję tekstu za pomocą opcji Gzip Page Compression w konfiguracji Joomli (System > Global configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "<PERSON><PERSON><PERSON> nie łączysz zasobów JavaScript w pakiet, proponujemy użycie metody [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Proponujemy wyłączenie wbudowanych w Magento funkcji [tworzenia pakietów i minifikacji JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), a użycie zamiast nich metody [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[Defin<PERSON><PERSON><PERSON><PERSON> niestandardowe czcionki](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html), ustaw `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Proponujemy poszukać w [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) rozszerzeń innych firm umożliwiających wykorzystanie nowych formatów obrazów."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Sugerujemy zmodyfikowanie szablonów produktu i katalogu w celu wykorzystania funkcji platformy internetowej [leniwego ładowania](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Skorzystaj z [integracji Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) z Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> opcję „Minify CSS Files” (Minifikuj pliki CSS) w ustawieniach dewelopera swojego sklepu. [Więcej informacji](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Do minifikacji wszystkich plików JavaScript z wdrożenia treści statycznych używaj parsera [Terser](https://www.npmjs.com/package/terser) i wyłącz wbudowaną funkcję minifikacji."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Wyłącz wbudowaną w Magento funkcję [tworzenia pakietów JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Proponujemy poszukać w [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) rozszerzeń innych firm umożliwiających optymalizację obrazów."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Wskazów<PERSON> „preconnect” lub „dns-prefetch” mo<PERSON>na do<PERSON>, [mody<PERSON><PERSON><PERSON><PERSON><PERSON> układ motywu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Tagi `<link rel=preload>` m<PERSON><PERSON><PERSON>, [mody<PERSON><PERSON><PERSON><PERSON><PERSON> układ motywu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Aby automatycznie zoptymalizować format obrazu, użyj komponentu `next/image` zamiast `<img>`. [Więcej informacji](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Aby automatycznie była używana funkcja leniwego ładowania obrazów, użyj komponentu `next/image` zamiast `<img>`. [Więcej informacji](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Użyj komponentu `next/image` i ustaw wartość Prawda dla elementu „priority”, aby wstępnie wczytać obraz LCP. [Więcej informacji](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Uż<PERSON>j komponentu `next/script`, aby <PERSON><PERSON><PERSON><PERSON><PERSON> wczytywanie niekrytycznych skryptów spoza witryny. [Więcej informacji](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> był odpowiednio dostosowywany rozmiar obrazów, użyj komponentu `next/image`. [Więcej informacji](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON> skonfigurow<PERSON> wtyczkę `PurgeCSS` w konfiguracji `Next.js`, aby usun<PERSON>ć nieużywane reguły z arkuszy stylów. [Więcej informacji](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Użyj narzędzia `Webpack Bundle Analyzer`, aby w<PERSON><PERSON><PERSON> nieużywany kod JavaScript. [Więcej informacji](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON><PERSON><PERSON> użyć narzędzia `Next.js Analytics`, <PERSON><PERSON><PERSON> zmierzyć rzeczywistą szybkość aplikacji. [Więcej informacji](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Skonfiguruj buforowanie zasobów niezmiennych i stron `Server-side Rendered` (SSR). [Więcej informacji](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON><PERSON> <PERSON><PERSON> j<PERSON>, u<PERSON><PERSON>j komponentu `next/image` zamiast `<img>`. [Więcej informacji](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Użyj komponentu `next/image`, aby us<PERSON><PERSON><PERSON> od<PERSON> wartoś<PERSON> `sizes`. [Więcej informacji](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Włącz na swoim serwerze Next.js kompresję. [Wi<PERSON><PERSON>j informacji](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "<PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON> funk<PERSON>ę [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337), skontaktuj się z menedżerem konta. Po jej skonfigurowaniu renderowanie stron otrzyma odpowiedni priorytet i zostanie zoptymalizowane pod kątem wydajności."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> od<PERSON>wi<PERSON><PERSON> wartość reguły CSS font-display, u<PERSON><PERSON><PERSON> funk<PERSON> [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) w NitroPack."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Aby automatycznie przekonwertować obrazy na WebP, u<PERSON><PERSON><PERSON> funk<PERSON> [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Aby <PERSON><PERSON><PERSON><PERSON>ć wyświetlanie obrazów poza ekranem, wł<PERSON><PERSON>cję [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pier<PERSON> w<PERSON>, w<PERSON><PERSON><PERSON> [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) w NitroPack."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> czas odpowiedzi serwera i zoptymalizować wydaj<PERSON>, akty<PERSON><PERSON> [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Aby zmniejszyć rozmiar plików CSS, HTML i JavaScript i przyspieszyć ich wczytywanie, włącz w ustawieniach zapisywania w pamięci podręcznej funkcję [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Aby zmniejszyć rozmiar plików JS, HTML i CSS i przyspieszyć ich wczytywanie, włącz w ustawieniach zapisywania w pamięci podręcznej funkcję [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "<PERSON><PERSON> <PERSON> reguły CSS, które nie mają zastosowania do tej strony, wł<PERSON><PERSON> funk<PERSON>ję [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "A<PERSON> <PERSON> wczytywanie skryptów do momentu, gdy bę<PERSON><PERSON>, skonfigu<PERSON><PERSON> [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) w NitroPack."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Przejdź do funkcji [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) w menu `Caching` i dostosuj czas wygaśnięcia pamięci podręcznej strony, aby skr<PERSON><PERSON> czas wczytywania i poprawić wygodę użytkowników."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON>a funkcję [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack), aby automatycznie kompresować, optymalizować i konwertować obrazy do formatu WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Aby z wyprzedzeniem optymalizować obrazy i dostosowywać je do wymiarów kontenerów, w których są wyświetlane na wszystkich urządzeniach, włącz funkcję [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Aby zmniejszyć rozmiar plików wysyłanych do przeglądarki, u<PERSON><PERSON><PERSON> funk<PERSON> [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) w NitroPack."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Użyj komponentu `nuxt/image` i ustaw `format=\"webp\"`. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Użyj komponentu `nuxt/image` i ustaw `loading=\"lazy\"` dla obrazów poza ekranem. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Użyj komponentu `nuxt/image` i określ warto<PERSON> `preload` dla obrazu L<PERSON>. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON>ż<PERSON>j komponentu `nuxt/image` i jawnie określ `width` oraz `height`. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#width-height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Użyj komponentu `nuxt/image` i ustaw odpowiednią wartość `quality`. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Użyj komponentu `nuxt/image` i ustaw odpowiednią wartoś<PERSON> `sizes`. [Więcej informacji](https://image.nuxt.com/usage/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Zastąp animowane GIF-y filmami](https://web.dev/replace-gifs-with-videos/), aby strony ładowały się szybciej, i rozważ użycie nowoczesnych formatów plików, takich jak [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) lub [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), aby poprawić kompresję o ponad 30% w stosunku do kodeka wideo VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Moż<PERSON>z skorzy<PERSON>ć z [wtyczki](https://octobercms.com/plugins?search=image) lub us<PERSON><PERSON><PERSON>, która będzie automatycznie konwertować przesłane obrazy do optymalnych formatów. [Pliki graficzne z kompresją bezstratną WebP](https://developers.google.com/speed/webp) są o 26% mniejsze od plików PNG i o 25–34% mniejsze od porównywalnych plików JPEG przy równoważnym indeksie jakości SSIM. Inny nowoczesny format obrazu, którego użycie warto rozważyć, to [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON> [wtyczkę do leniwego ładowania obrazów](https://octobercms.com/plugins?search=lazy), która umożliwia odłożenie ładowania obrazów niewyświetlanych na ekranie, albo wybra<PERSON> motyw, który ma tę funkcję. Warto też skorzystać z [wtyczki AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Istnieje wiele wtyczek, które pomagają [umie<PERSON><PERSON><PERSON> w tekście najważniejsze zasoby](https://octobercms.com/plugins?search=css). Te wtyczki mogą zakłócać działanie innych wtyczek, więc należy przeprowadzić dokładne testy."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, wtyczki i specyfikacje serwera są nie bez znaczenia dla czasu odpowiedzi serwera. By<PERSON> może warto znaleźć lepiej zoptymalizowany motyw, star<PERSON>ie wybrać wtyczkę optymalizującą lub przejść na nowszą wersję serwera. October CMS pozwala programistom wykorzystać [`Queues`](https://octobercms.com/docs/services/queues) do odkładania na później czasochłonnych zadań, takich jak wysyłanie e-maili. Bar<PERSON>zo przyspiesza to obsługę żądań sieciowych."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Korzystne może być wyświetlanie fragmentów na liście postów (np. przy użyciu przycisku `show more`), zmniejszenie liczby postów wyświetlanych na danej stronie, podzielenie długich postów na kilka stron lub użycie wtyczki umożliwiającej leniwe ładowanie komentarzy."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Jest wiele [wty<PERSON><PERSON>](https://octobercms.com/plugins?search=css), które mogą przyspieszyć działanie witryny dzięki konkatenacji, minifikacji i kompresji stylów. Przeprowadzenie minifikacji już podczas kompilacji może przyspieszyć tworzenie witryny."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Jest wiele [wty<PERSON><PERSON>](https://octobercms.com/plugins?search=javascript), które mogą przyspieszyć działanie witryny dzięki konkatenacji, minifikacji i kompresji skryptów. Przeprowadzenie minifikacji już podczas kompilacji może przyspieszyć tworzenie witryny."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON> spraw<PERSON> [wty<PERSON><PERSON>](https://octobercms.com/plugins), które wczytują w witrynie nieużywany kod CSS. Aby zidentyfi<PERSON>wać wtyczki, które dodają niepotrzebny kod CSS, uruchom [pokrycie kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Narzędziach deweloperskich Chrome. Zidentyfikujesz taki motyw/wtyczkę na podstawie adresu URL arkusza stylów. Szukaj wtyczek, które mają wiele arkuszy stylów z dużą ilością czerwonego koloru w pokryciu kodu. Wtyczka powinna dodawać arkusz stylów tylko wtedy, gdy rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> spraw<PERSON> [wtyczki](https://octobercms.com/plugins?search=javascript), które wczytują na stronie nieużywany kod JavaScript. Aby zidentyfikować wtyczki, które dodają niepotrzebny kod JavaScript, uruchom [pokrycie kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Narzędziach deweloperskich Chrome. Zidentyfikujesz taki motyw/wtyczkę na podstawie adresu URL skryptu. Szukaj wtyczek, które mają wiele skryptów z dużą ilością czerwonego koloru w pokryciu kodu. Wtyczka powinna dodawać skrypt tylko wtedy, gdy rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Przeczytaj o [unikaniu zbędnych żądań sieciowych dzięki pamięci podręcznej HTTP](https://web.dev/http-cache/#caching-checklist). Jest wiele [wtyczek](https://octobercms.com/plugins?search=Caching), które pozwalają przyspieszyć zapisywanie w pamięci podręcznej."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Pomocna może być [wtyczka do optymalizacji obrazów](https://octobercms.com/plugins?search=image), która kompresuje obrazy, zachowując ich jako<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Przesy<PERSON><PERSON> obrazy bezpośrednio w menedżerze multimediów, aby mi<PERSON><PERSON>, że wymagane rozmiary obrazów są dostępne. Możesz użyć [filtra zmieniającego rozmiar](https://octobercms.com/docs/markup/filter-resize) lub [wtyczki do zmieniania rozmiaru obrazów](https://octobercms.com/plugins?search=image), aby zapewnić używanie optymalnych rozmiarów obrazów."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Włącz kompresję tekstu w konfiguracji serwera WWW."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Jeś<PERSON> renderujesz na stronie wiele powtarzających się elementów, proponujemy wykorzystanie biblioteki „okienkowej”, takiej jak `react-window`, aby z<PERSON><PERSON><PERSON><PERSON><PERSON> liczbę tworzonych węzłów DOM. [Więcej informacji](https://web.dev/virtualize-long-lists-react-window/) Aby przys<PERSON><PERSON> d<PERSON>łanie, zredukuj też liczbę ponownych renderowań za pomocą metod [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) lub [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo). Je<PERSON><PERSON> u<PERSON><PERSON><PERSON><PERSON> hooka `Effect`, [pomijaj efekty](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) tylko do momentu zmiany określonych zależności."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON><PERSON> używasz komponentów React Router, zminimalizuj wykorzystanie komponentu `<Redirect>` do [przekierowania nawigacji](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Jeś<PERSON> renderujesz komponenty React po stronie serwera, proponuje<PERSON> używanie metod `renderToPipeableStream()` lub `renderToStaticNodeStream()`, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klientowi odbieranie i rozwijanie poszczególnych części kodu zamiast całego naraz. [Więcej informacji](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Jeśli Twój system kompilacji automatycznie minifikuje pliki CSS, upewnij się, że wdrażasz kompilację produkcyjną aplikacji. Moż<PERSON>z to sprawdzić za pomocą rozszerzenia Narzędzia dla programistów React. [Więcej informacji](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Jeśli Twój system kompilacji automatycznie minifikuje pliki JS, upewnij się, że wdrażasz kompilację produkcyjną aplikacji. Moż<PERSON>z to sprawdzić za pomocą rozszerzenia Narzędzia dla programistów React. [Więcej informacji](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON> nie renderu<PERSON> po stronie serwera, [podziel pakiety JavaScript](https://web.dev/code-splitting-suspense/), uży<PERSON><PERSON><PERSON><PERSON> funk<PERSON> `React.lazy()`. W przeciwnym razie podziel kod za pomocą biblioteki innej firmy, takiej jak [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Do pomiaru wydajności renderowania komponentów używaj programu profilującego z narzędzi dla programistów React, który wykorzystuje interfejs Profiler API. [Więcej informacji](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Umieść filmy w elemencie `VideoBoxes`, dostosuj je za pomocą elementu `Video Masks` lub dodaj `Transparent Videos`. [Więcej informacji](https://support.wix.com/en/article/wix-video-about-wix-video)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> usługi `Wix Media Manager`, aby <PERSON><PERSON><PERSON>, ż<PERSON> będą one automatycznie wyświetlane w formacie WebP. Poznaj [inne sposoby optymalizacji](https://support.wix.com/en/article/site-performance-optimizing-your-media) multimediów w witrynie."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "[Do<PERSON><PERSON><PERSON>c kod spoza witryny](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) na karcie `Custom Code` w panelu witryny, upew<PERSON>j się, <PERSON>e jest on odroczony lub ładowany na końcu. G<PERSON> tylko jest to możliwe, umieszczaj narzędzia marketingowe w witrynie, korzystając z [integracji](https://support.wix.com/en/article/about-marketing-integrations) Wix. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix korzysta z sieci CDN i buforowania, aby jak najszybciej wyświetlać odpowiedzi większości użytkownikom. Zastanów się nad [ręcznym włączeniem buforowania](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) w witrynie – zwłaszcza jeśli używasz `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Sprawdź kod spoza witryny dodany na karcie `Custom Code` w panelu witryny i zachowaj tylko te usługi, które są niezbędne. [Więcej informacji](https://support.wix.com/en/article/site-performance-removing-unused-javascript)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Możesz przesłać plik GIF do usługi, która umożliwi umieszczanie go jako pliku wideo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Rozważ używanie wtyczki [Performance Lab](https://wordpress.org/plugins/performance-lab/) do automatycznego konwertowania przesyłanych plików JPEG na format WebP, jeśli jest on obsługiwany."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Zainstaluj [wtyczkę WordPressa do leniwego ładowania](https://wordpress.org/plugins/search/lazy+load/), która umożliwia odłożenie ładowania obrazów niewyświetlanych na ekranie, albo wybierz motyw, który ma tę funkcję. Warto też skorzystać z [wtyczki AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Jest wiele wtyczek WordPressa, które mogą pomóc Ci [umie<PERSON><PERSON><PERSON> w tekście najważniejsze zasoby](https://wordpress.org/plugins/search/critical+css/) lub [opóź<PERSON>ć wczytywanie mniej ważnych zasobów](https://wordpress.org/plugins/search/defer+css+javascript/). Pamiętaj, że optymalizacje dostarczane przez te wtyczki mogą uszkodzić funkcje Twojego motywu lub innych wtyczek i konieczne może być wprowadzenie zmian w kodzie."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, wtyczki i specyfikacje serwera są nie bez znaczenia dla jego czasu reakcji. <PERSON><PERSON> może warto znaleźć lepiej zoptymalizowany motyw, starannie wybierając wtyczkę optymalizującą, i przejść na nowszą wersję serwera."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Korzystne może być wyświetlanie fragmentów na liście postów (np. przy użyciu tagu more), zmniejszenie liczby postów wyświetlanych na danej stronie, podział długich postów na kilka stron lub użycie wtyczki umożliwiającej opóźnione wczytywanie komentarzy."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Kilka [wtyczek WordPressa](https://wordpress.org/plugins/search/minify+css/) może przyśpieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji stylów. Jeś<PERSON> masz taką mo<PERSON>, prz<PERSON><PERSON><PERSON>ż minifikację, zanim opublikujesz skrypty."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Kilka [wtyczek WordPressa](https://wordpress.org/plugins/search/minify+javascript/) może przyśpieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji skryptów. Jeś<PERSON> masz taką mo<PERSON>, prz<PERSON><PERSON><PERSON>ź minifikację, zanim opublikujesz skrypty."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [wtyczek WordPressa](https://wordpress.org/plugins/) wczytujących na stronie nieużywany kod CSS albo zmiana tych wtyczek na inne. Aby zidentyfi<PERSON>wać wtyczki, które dodają nieistotny kod CSS, uruchom [zasięg kodu](https://developer.chrome.com/docs/devtools/coverage/) w Chrome DevTools. Możesz zidentyfikować taki motyw/wtyczkę w adresie URL arkusza stylów. Szukaj wtyczek, które mają na liście wiele arkuszy stylów z dużą ilością czerwonego koloru w zasięgu kodu. Wtyczka powinna umieszczać arkusz stylów w kolejce tylko wtedy, gdy rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [wtyczek WordPressa](https://wordpress.org/plugins/) wczytujących na stronie nieużywany kod JavaScript albo zmiana tych wtyczek na inne. Aby zidentyfi<PERSON>wać wtyczki, które dodają nieistotny kod JS, uruchom [zasięg kodu](https://developer.chrome.com/docs/devtools/coverage/) w Chrome DevTools. Możesz zidentyfikować taki motyw/wtyczkę w adresie URL skryptu. Szukaj wtyczek, które mają na liście wiele skryptów z dużą ilością czerwonego koloru w zasięgu kodu. Wtyczka powinna umieszczać skrypt w kolejce, tylko jeśli rzeczywiście jest on używany na stronie."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Dowiedz się więcej o [pamięci podręcznej przeglądarki w WordPressie](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Pomocna może być [wtyczka WordPressa do optymalizacji obrazów](https://wordpress.org/plugins/search/optimize+images/), która kompresuje obrazy, zachow<PERSON><PERSON><PERSON><PERSON> ich jako<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Przes<PERSON><PERSON><PERSON> obrazy bezpośred<PERSON> przez [bibliotekę multimediów](https://wordpress.org/support/article/media-library-screen/), by <PERSON><PERSON><PERSON>, <PERSON><PERSON> wymagane rozmiary obrazów są dostępne, a następnie wstawiaj je z biblioteki multimediów lub używaj widżetu obrazów, by zapewnić użycie optymalnych rozmiarów obrazów (także w dynamicznych punktach przerwania). Unikaj używania obrazów `Full Size`, chyba że wymiary są adekwatne do danego zastosowania. [Więcej informacji](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Możesz włączyć kompresję tekstu w konfiguracji swojego serwera WWW."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Na platformie WP Rocket, na karcie Image Optimization (Optymalizacja obrazu) włącz opcję „Imagify”, aby przekonwertować obrazy do formatu WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Aby r<PERSON> ten problem, na platformie WP Rocket włącz [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) (Leniwe ładowanie). Ta funkcja opóźnia wczytywanie obrazów, dopóki użytkownik nie przewinie strony i rzeczywiście nie będzie musiał ich zobaczyć."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Aby r<PERSON><PERSON><PERSON> ten problem, na platformie WP Rocket włącz funkcję [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Usuwaj nieużywany kod CSS) i [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (Ładuj odroczony JavaScript). Te funkcje zoptymalizują pliki JavaScript i CSS, aby nie blokowały one renderowania strony."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Aby r<PERSON><PERSON><PERSON> ten problem, na platformie WP Rocket włącz funkcję [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Minifikuj pliki CSS). Wszelkie spacje i komentarze w plikach CSS Twojej witryny zostaną usunięte, aby zmniejszyć rozmiar pliku i tym samym przyspieszyć pobieranie."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Aby r<PERSON><PERSON><PERSON> ten problem, na platformie WP Rocket włącz funkcję [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Minifikuj pliki JavaScript). Puste miejsca i komentarze w plikach JavaScript zostaną usunięte, aby zmniejszyć ich rozmiar i tym samym przyspieszyć pobieranie."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Aby r<PERSON> ten problem, na platformie WP Rocket włącz funkcję [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Usuwaj nieużywany kod CSS). Zmniejsza to rozmiar strony przez usunięcie nieużywanych arkuszy stylów i kodu CSS, zachowując jednocześnie tylko używany kod CSS w przypadku każdej strony."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Aby r<PERSON> ten problem, na platformie WP Rocket włącz funkcję [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Opóźniaj wykonanie JavaScriptu). P<PERSON><PERSON><PERSON><PERSON> to wczytywanie strony, opóźniając wykonanie skryptów do momentu interakcji użytkownika. Jeśli Twoja witryna zawiera elementy iframe, moż<PERSON>z też używać dostępnych na platformie WP Rocket funkcji [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (Przeprowadzaj leniwe ładowanie elementów iframe i filmów) oraz [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Zastępuj element iframe YouTube obrazem podglądu)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Na platformie WP Rocket, na karcie Image Optimization (Optymalizacja obrazu) włącz opcję „Imagify” i uruchom optymalizację zbiorczą (ang. Bulk Optimization), aby s<PERSON>mp<PERSON> obrazy."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Na platformie WP Rocket używaj funkcji [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Pobieraj z wyprzedzeniem żądania DNS), aby dodać wskazówkę „dns-prefetch” i przyspieszyć łączenie z domenami zewnętrznymi. Poza tym WP Rocket automatycznie dodaje wskazówkę „preconnect” do [domeny Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) i rekordów CNAME dodanych za pomocą funkcji [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Włącz CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "<PERSON><PERSON> r<PERSON> ten problem w przy<PERSON><PERSON> czcionek, na platformie WP Rocket włącz funkcję [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Usuwaj nieużywany kod CSS). Najważniejsze czcionki witryny będą wstępnie wczytywane z priorytetem."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "Zwiń widok"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Początkowa nawigacja"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maksymalne opóźnienie ścieżki krytycznej:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Skopiuj JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Przełącz ciemny motyw"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Zap<PERSON>z jako <PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Zapisz jako HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Zapisz jako J<PERSON>"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Wyświetl niezmodyfikowany ślad"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Otwórz w przeglądarce"}, "report/renderer/report-utils.js | errorLabel": {"message": "Błąd"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Błąd raportu: brak informacji o audycie"}, "report/renderer/report-utils.js | expandView": {"message": "Rozwiń widok"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "Własna"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON>ł<PERSON>ś problem"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Dane <PERSON>jn<PERSON>"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Bieżąca strona została przeanalizowana przez narzędzie [Lighthouse](https://developers.google.com/web/tools/lighthouse/) wraz z emulacją sieci komórkowej. Wartości są szacunkowe i mogą się zmieniać."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Dodatkowe elementy do ręcznego sprawdzenia"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Otwórz w nowej karcie"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Szacowane oszczędności"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Zaliczone audyty"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Zgodnie ze [zaktualizowanymi kryteriami instalacyjnymi Chrome](https://developer.chrome.com/blog/update-install-criteria) w przysz<PERSON>ej wersji w Lighthouse wycofamy kategorię PWA. Aby dowiedzieć się, jak w przyszłości będzie wyglądać testowanie PWA, zapoznaj się ze [zaktualizowaną dokumentacją dotyczącą aplikacji PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/)."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Wstępne wczytanie strony"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Migawka z określonego momentu"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Okres interakcji użytkownika"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Ogranic<PERSON>ie <PERSON>rdo<PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulacja Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Wersja biblioteki Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Nieograniczona moc procesora/pamięci"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Ograniczanie wykorzystania procesora"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Urząd<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ograniczanie wykorzystania sieci"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>nu"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Klient użytkownika (sieć)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Sesja ograniczona do jednej strony"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Te dane pochodzą z sesji ograniczonej do jednej strony w odróżnieniu od wartości w polu zawierającym podsumowanie kilku sesji."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ograniczanie spowalniające do 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Po<PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Pokaż audyty dotyczące:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Zwiń fragment"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Rozwiń fragment"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Pokaż zasoby zewnętrzne"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Obsługiwane przez środowisko"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Podczas tego uruchomienia Lighthouse wystąpiły problemy:"}, "report/renderer/report-utils.js | unattributable": {"message": "Niepowiązane"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Wartości są szacunkowe i mogą się zmieniać. [Wynik wydajności jest obliczony](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) bezpośrednio na podstawie tych danych."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Wyświ<PERSON><PERSON> ślad"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON><PERSON> diagram"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Audyty zaliczone z ostrzeżeniami"}, "report/renderer/report-utils.js | warningHeader": {"message": "Ostrzeżenia "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Wszystkie skrypty"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Pokrycie"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Zduplikowane moduły"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> (w bajtach)"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nazwa"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Przełącz tabelę"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}}