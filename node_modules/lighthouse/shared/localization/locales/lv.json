{"core/audits/accessibility/accesskeys.js | description": {"message": "Piekļuves atslēgas ļauj lietotājiem ātri pievērsties lapas daļai. Lai navigācija būtu parei<PERSON>, katrai piekļuves atslēgai ir jābūt unikālai. [Uzziniet vairāk par piekļuves atslēgām](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[accesskey]` vē<PERSON><PERSON><PERSON> nav unikālas"}, "core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> “`[accesskey]`” v<PERSON><PERSON><PERSON><PERSON> ir un<PERSON>"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Katrs ARIA elements “`role`” atbalsta konkrētu atribūtu “`aria-*`” apakškopu. Ja tie netiek norād<PERSON>ti par<PERSON>, atrib<PERSON>ti “`aria-*`” nav derīgi. [<PERSON><PERSON><PERSON><PERSON>, kā pielāgot ARIA atribūtus to lomām](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` neatbilst savām lomām"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON> `[aria-*]` atbilst savām lomām"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "ARIA atribūti `role` <PERSON><PERSON>j atbalsta tehnoloģijām noteikt katra elementa lomu tīmekļa lapā. Ja `role` vērtības ir nepareizi <PERSON>z<PERSON>, nav esošas ARIA `role` vērtības vai ir abstraktas lomas, elementa mērķis netiks paziņots atbalsta tehnoloģiju lietotājiem. [Uzziniet vairāk par ARIA lomām](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Elementam `role=\"\"` piešķirtās vērtības nav derīgas <PERSON> lo<PERSON>."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Elementam `role=\"\"` piešķirtās vērt<PERSON>bas ir derīgas <PERSON>."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Ja elementam nav pieejama no<PERSON>, ek<PERSON><PERSON><PERSON> las<PERSON> nolasa to ar visp<PERSON><PERSON><PERSON><PERSON> nosaukumu, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt komandu elementus pieejamākus](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementiem “`button`”, “`link`” un “`menuitem`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementiem “`button`”, “`link`” un “`menuitem`” ir pieejami no<PERSON>mi"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Ja ARIA dialoglodziņu elementiem nav pieejamu no<PERSON>, ek<PERSON><PERSON><PERSON> las<PERSON>tā<PERSON> lietotāji var nesaprast šo elementu mērķi. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt ARIA dialoglodziņu elementus pieejamākus](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Elementiem ar <PERSON> “`role=\"dialog\"`” vai “`role=\"alertdialog\"`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Elementiem ar <PERSON> “`role=\"dialog\"`” vai “`role=\"alertdialog\"`” ir pieej<PERSON> no<PERSON>mi"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON><PERSON>, kad dokumenta elementam `<body>` ir iestatīts atribūts “`aria-hidden=\"true\"`”. [<PERSON><PERSON><PERSON><PERSON>, kā atribūts “`aria-hidden`” ietekmē dokumenta pamattekstu](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` ir dokumentā `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` nav dokumentā `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Fokusējami pēcteči elementā `[aria-hidden=\"true\"]` nov<PERSON><PERSON><PERSON>o interaktīvo elementu pieejamību atbalsta tehnoloģiju, pie<PERSON><PERSON><PERSON>, ekr<PERSON>a las<PERSON>, lietot<PERSON><PERSON><PERSON>. [<PERSON><PERSON><PERSON><PERSON>, kā “`aria-hidden`” ietekmē fokusējamos elementus](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` elementi satur fokusējamus pēct<PERSON>us"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` elementos nav fokusējamu pēcte<PERSON>u"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON>a ievades laukam nav pie<PERSON><PERSON> no<PERSON>, e<PERSON><PERSON><PERSON><PERSON> no<PERSON> to ar visp<PERSON><PERSON><PERSON><PERSON> no<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [Uzziniet vairāk par ievades lauku iezīmēm](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA ievades laukiem nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA ievades laukiem ir pieejami no<PERSON>mi"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Ja uzskaites elementam nav pieej<PERSON>, ek<PERSON><PERSON><PERSON> no<PERSON> to ar vispār<PERSON><PERSON> nosauku<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [<PERSON><PERSON><PERSON><PERSON>, kā piešķirt nosaukumu šiem elementiem (`meter`)](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA elementiem “`meter`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA elementiem “`meter`” ir pieejami no<PERSON>"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Ja elementam “`progressbar`” nav pie<PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON> no<PERSON> to ar vispār<PERSON><PERSON> no<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [<PERSON><PERSON><PERSON><PERSON>, kā pievienot iezīmes elementam “`progressbar`”](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA elementiem “`progressbar`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA elementiem “`progressbar`” ir pieejami no<PERSON>mi"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Dažām ARIA lomām ir obligāti atri<PERSON>ti, kas ekr<PERSON>a lasītājiem norāda elementa statusu. [Uzziniet vairāk par lomām un obligātajiem atribūtiem](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementiem `[role]` nav visu pieprasīto <PERSON> `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Visiem element<PERSON> `[role]` ir <PERSON><PERSON><PERSON><PERSON><PERSON> atribūti `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Dažām ARIA vecāklomām ir jāietver konkrētas pakārtotā<PERSON> lo<PERSON>, lai varētu nodrošināt pieejamības funkcijas. [Uzziniet vairāk par lomām un obligātajiem pakārtotajiem elementiem](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementos ar <PERSON> lomu `[role]`, kuru pakārtotajiem elementiem ir jāsatur konkrēts vienums `[role]`, trū<PERSON>t dažu vai visu šo obligāto pakārtoto elementu."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementos ar <PERSON> lomu `[role]`, kuru pakārtotajiem elementiem ir jāsatur konkrēts vienums `[role]`, ir visi obligātie pakārtotie elementi."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Dažām ARIA pakārtotajām lomām ir jābūt ietvertām konkrētās ve<PERSON>, lai varētu nodrošināt pieejamības funkcijas. [Uzziniet vairāk par ARIA lomām un obligāto galveno elementu](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` ne<PERSON>ver pie<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>[role]` ir iet<PERSON>s piepras<PERSON>tais vecākel<PERSON>s"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA lomām ir nepiecie<PERSON><PERSON> derī<PERSON> v<PERSON>, lai varētu nod<PERSON>t pieejamības funkcijas. [Uzziniet vairāk par derīgām ARIA lomām](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` v<PERSON><PERSON><PERSON><PERSON> nav derīgas"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` v<PERSON><PERSON><PERSON><PERSON> ir der<PERSON>gas"}, "core/audits/accessibility/aria-text.js | description": {"message": "<PERSON>a apkārt marķējuma sadalītam teksta mezglam pievienojat atribūtu “`role=text`”, funkcijā VoiceOver var apstrādāt šo teksta mezglu kā vienu frāzi, taču netiek paziņots par fokusējamiem elementa pēctečiem. [Uzziniet vairāk par atribūtu “`role=text`”](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Elementiem ar at<PERSON> “`role=text`” ir fokus<PERSON><PERSON> p<PERSON>."}, "core/audits/accessibility/aria-text.js | title": {"message": "Elementiem ar atri<PERSON> “`role=text`” nav fokusējamu pēcte<PERSON>u"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laukam nav pie<PERSON>, e<PERSON><PERSON><PERSON><PERSON> las<PERSON> no<PERSON> to ar visp<PERSON>r<PERSON><PERSON> nosauku<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [Uzziniet vairāk par pārslēg<PERSON><PERSON> laukiem](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA pārslēgšanas laukiem nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA pārslēgšanas laukiem ir pieejami nosaukumi"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "<PERSON>a rīka padoma elementam nav pie<PERSON>, e<PERSON><PERSON><PERSON><PERSON> no<PERSON> to ar vispā<PERSON><PERSON><PERSON> no<PERSON>uku<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [<PERSON><PERSON><PERSON><PERSON>, kā piešķirt nosaukumu šiem elementiem (`tooltip`)](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA elementiem “`tooltip`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA elementiem “`tooltip`” ir pieejami no<PERSON>"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Ja elementam “`treeitem`” nav pie<PERSON>, e<PERSON><PERSON><PERSON><PERSON> las<PERSON> no<PERSON> to ar vispā<PERSON><PERSON><PERSON> nosauku<PERSON>, līdz ar to elements kļūst nelietojams ekrāna lasītāju lietotājiem. [Uzziniet vairāk par iezīmju pievienošanu elementam “`treeitem`”](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA elementiem “`treeitem`” nav pieejamu nosaukumu"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA elementiem “`treeitem`” ir pieejami no<PERSON>mi"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>, nevar interpretēt ARIA atribūtus ar nederīgām vērtībām. [Uzziniet vairāk par derīgām ARIA atribūtu vērtībām](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atri<PERSON><PERSON><PERSON>m `[aria-*]` nav derīgu vērtību"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON>m `[aria-*]` ir der<PERSON><PERSON> vērt<PERSON>"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nevar interpretēt ARIA atribūtus ar nederīgiem nosaukumiem. [Uzziniet vairāk par derīgiem ARIA atribūtiem](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` nav derīgi vai ir k<PERSON>daini u<PERSON>ti"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` ir derīgi un pareizi u<PERSON>ti"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON>gi <PERSON>i"}, "core/audits/accessibility/button-name.js | description": {"message": "Ja pogai nav pieejama no<PERSON>, ko var nolas<PERSON>t ekrāna las<PERSON>, tad ekrāna lasītāji to atskaņo kā “Poga”. Tād<PERSON><PERSON><PERSON><PERSON> lietot<PERSON>, kuri i<PERSON><PERSON> ekr<PERSON>a las<PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON> tā<PERSON>. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt pogas pieejamākas](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Pogām nav piekļūstamības principiem atbilstošu nosaukumu"}, "core/audits/accessibility/button-name.js | title": {"message": "Pogām ir piekļūstamības principiem atbilstoši nosaukumi"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON>a <PERSON><PERSON>t iespēju apiet atkārtotu saturu, tastatūras lietotāji varēs labāk pārvietoties lapā. [Uzziniet vairāk par apiešanas blokiem](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Lapā nav v<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nas saites vai orientieru daļas"}, "core/audits/accessibility/bypass.js | title": {"message": "<PERSON><PERSON><PERSON> ir v<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> saite vai orientieru da<PERSON>a"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Daudziem lasītā<PERSON>em ir grūti vai pat <PERSON> i<PERSON>t tekstu ar zemu kontrastu. [<PERSON><PERSON><PERSON><PERSON>, k<PERSON> nodrošin<PERSON>t pietiekamu krāsu kontrastu](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Fona un priekšplāna krāsu kontrasta koeficients nav pietiekams."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Fona un priekšplāna krāsām ir pietiekams kontrasta koeficients"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON>a definīciju saraksti nav marķēti parei<PERSON>, e<PERSON><PERSON><PERSON><PERSON> atskaņotais saturs var būt mulsinošs vai neprecīzs. [<PERSON><PERSON><PERSON><PERSON>, kā pareizi strukturēt definīciju sarakstus](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Atribūtā `<dl>` nav ietvertas tikai pareizā secībā sakārtotas elementu `<dt>` un `<dd>` grupas, elements `<script>`, `<template>` vai `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Atribūtā `<dl>` ir iet<PERSON>as tikai pareizā secībā sakārtotas elementu`<dt>` un `<dd>` grupas, elements `<script>`, `<template>` vai `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Definīciju saraksta vienumiem (`<dt>` un `<dd>`) ir jābūt ietvertiem vecākelementā `<dl>`, lai ekr<PERSON>a las<PERSON>tāji tos varētu pareizi atskaņot. [<PERSON><PERSON><PERSON><PERSON>, kā pareizi strukturēt definīciju sarakstus](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definīciju saraksta vienumi netiek apvienoti elementos `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Definīciju saraksta vienumi tiek apvienoti elementos `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Nosaukums sniedz lapas kopsavilkumu ekrāna lasītāja lietotājiem, un meklētājprogrammas lietotāji ļoti paļaujas uz to, lai note<PERSON>, vai lapa ir at<PERSON> viņu meklē<PERSON>. [Uzziniet vairāk par dokumentu nosaukumiem](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentā nav elementa `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumentā ir ietverts elements `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Visiem fokusējamiem elementiem ir jābūt unikālam `id`, lai tie būtu redzami atbalsta tehnoloģijām. [<PERSON><PERSON><PERSON><PERSON>, kā novērst ar `id` dublikātiem saistītas problēmas](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]` at<PERSON><PERSON><PERSON><PERSON> a<PERSON>, fokusējamos elementos nav unikāli"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]` at<PERSON><PERSON><PERSON><PERSON> a<PERSON>, fokusējamos elementos ir unik<PERSON>li"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA ID vērtībai ir jā<PERSON><PERSON><PERSON> un<PERSON>, lai atbal<PERSON> tehnoloģijas neizlaistu citas instances. [<PERSON><PERSON><PERSON><PERSON>, kā novērst ARIA identifikatoru dublikātus](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA identifikatori nav unikāli"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA identifikatori ir unik<PERSON>li"}, "core/audits/accessibility/empty-heading.js | description": {"message": "<PERSON>a virsrakstā nav satura vai teksts nav pieejams, ek<PERSON><PERSON><PERSON> las<PERSON>tā<PERSON> lietotāji nevar piekļūt informācijai par lapas struktūru. [Uzziniet vairāk par virsrakstiem](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Virsraksta elementos nav satura"}, "core/audits/accessibility/empty-heading.js | title": {"message": "<PERSON>isos virs<PERSON> elementos ir saturs"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Veidlapas laukus ar vairākām iezīmēm var nepareizi nolas<PERSON>t atbalsta tehnoloģijas, <PERSON><PERSON><PERSON><PERSON>, ek<PERSON><PERSON><PERSON>, kuri izman<PERSON>ir<PERSON>, pēd<PERSON>jo vai visas iezīmes. [<PERSON><PERSON><PERSON><PERSON>, kā izmantot veidlapu iezīmes](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Veidlapas laukiem ir vair<PERSON> iez<PERSON>mes"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> laukā nav vairāku iezīmju"}, "core/audits/accessibility/frame-title.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ietvaru no<PERSON>, lai raksturotu ietvaru saturu. [Uzziniet vairāk par ietvaru nosaukumiem](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementiem `<frame>` vai `<iframe>` nav nosaukuma"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementiem `<frame>` vai `<iframe>` ir nosaukums"}, "core/audits/accessibility/heading-order.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas ir sakārtoti pareizi un neiz<PERSON> l<PERSON>, atveido lapas semantis<PERSON> struk<PERSON>, tā<PERSON><PERSON><PERSON><PERSON><PERSON> atvieglojot navigāciju un izpratni atbalsta tehnoloģiju lietotājiem. [Uzziniet vairāk par virsrakstu secību](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Virsrakstu elementi nav atveidoti secīgi dilstošā secībā"}, "core/audits/accessibility/heading-order.js | title": {"message": "Virsrakstu elementi ir atveidoti secīgi dilstošā secībā"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ja lapā nav norādīts atribūts “`lang`”, ekr<PERSON><PERSON> las<PERSON>tājā tiek pieņ<PERSON>, ka lapas saturs ir noklusējuma valodā, kuru lietotājs izvēlēj<PERSON>, iestatot ekrāna lasītā<PERSON>. Ja lapas saturs nav noklusējuma valodā, i<PERSON><PERSON><PERSON><PERSON><PERSON>, ekrāna lasītājs tekstu neatskaņos pareizi. [Uzziniet vairāk par atribūtu “`lang`”](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Tagam `<html>` nav derīga atribūta `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Tagam `<html>` ir atri<PERSON> `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> derīgu [BCP 47 valodu](https://www.w3.org/International/questions/qa-choosing-language-tags#question), ekr<PERSON>a las<PERSON> pareizi atskaņos tekstu. [<PERSON><PERSON><PERSON><PERSON>, k<PERSON> izmantot atribūtu “`lang`”.](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Tagam `<html>` nav derīgas vērtības tā atribūtam `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Elementam `<html>` ir derīga tā atribūta `[lang]` vē<PERSON><PERSON>ba"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Ja tīmekļa lapā netiek norādīta konsekventa valoda, i<PERSON><PERSON><PERSON><PERSON><PERSON>, ekr<PERSON>a las<PERSON>t<PERSON>ji pareizi neatskaņos lapas tekstu. [Uzziniet vairāk par atribūtu `lang`.](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Elementam `<html>` nav atribūta `[xml:lang]` ar tādu pašu pamata valodu kā atribūtam `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Elementam `<html>` ir atrib<PERSON> `[xml:lang]` ar tādu pašu pamata valodu kā atribūtam `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Ja saitēm ir viens galamērķis, ar<PERSON> saišu aprakstam jābūt vien<PERSON>, lai lietotāji varētu saprast katras saites mērķi un izlemt, vai sekot tai. [Uzziniet vairāk par identiskām saitēm](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identiskām saitēm ir atšķirīgi mērķi"}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Identisk<PERSON>m saitēm ir vienāds mērķis"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informatīvajiem elementiem ir nepieciešams īss, aprakstošs alternatīvais teksts. Dekoratīvajiem elementiem alternatīvo atribūtu var atstāt tukšu. [Uzziniet vairāk par atribūtu “`alt`”](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Attēlu elementiem nav atribūtu `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Attēlu elementiem ir atribūti `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Informatīvajiem elementiem ir nepieciešams īss, apraks<PERSON><PERSON><PERSON> alternatīvais teksts. Alternatīvs teksts, kas ir tieši tāds pats kā teksts blakus saitei vai attēlam, var mulsin<PERSON> lieto<PERSON>, kuri i<PERSON> e<PERSON><PERSON>, jo teksts tiks nolasīts divreiz. [Uzziniet vairāk par atribūtu `alt`.](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Attēlu elementiem ir atrib<PERSON>ti `[alt]`, kas ir lieks teksts."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Attēlu elementiem nav atribūtu `[alt]`, kas ir lieks teksts."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Pievienojot ievades pogām saprotamu un pieejamu tekstu, varat palīdzēt ekrāna lasītāju lietotājiem saprast, kam ievades pogas ir paredzētas. [Uzziniet vairāk par ievades pogām.](https://dequeuniversity.com/rules/axe/4.8/input-button-name)"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> pogām nav pievienots saprotams teksts."}, "core/audits/accessibility/input-button-name.js | title": {"message": "<PERSON><PERSON><PERSON> pogām ir pievienots saprotams teksts"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON>a attēls tiek izmantots kā poga `<input>`, alternatīvais teksts var sniegt informāciju par pogas nozīmi lietotājiem, kuri izmanto ekrāna lasītāju. [Uzziniet vairāk par ievades pogas attēla alternatīvo tekstu](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementiem `<input type=\"image\">` nav teksta `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` ietver tekstu `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>, kas neat<PERSON>st pieej<PERSON><PERSON><PERSON> no<PERSON>, var i<PERSON><PERSON><PERSON><PERSON><PERSON> mulsino<PERSON> pier<PERSON> lieto<PERSON>, kuri izmanto ekr<PERSON> las<PERSON>. [Uzziniet vairāk par pieejamajiem nosaukumiem](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Elementiem ar redzamām teksta iezīmēm nav at<PERSON>stošu pieejamu no<PERSON>ukumu."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Elementiem ar redzamām teksta iezīmēm ir atbilstoši pieejami no<PERSON>uku<PERSON>."}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> iez<PERSON>, at<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>, varēs pareizi atskaņot veidlapu vadīklas. [Uzziniet vairāk par veidlapas elementu iezīmēm](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Veidlapu elementiem nav sa<PERSON>"}, "core/audits/accessibility/label.js | title": {"message": "Veidlapu elementiem ir sa<PERSON> i<PERSON>"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Viens galvenais orientieris palīdz ekrāna lasītāja lietotājiem pārvietoties tīmekļa lapā. [Uzziniet vairāk par orientieriem](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokumentā nav galvenā orientiera"}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokumentam ir galvenais orientieris"}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Daudziem lietotājiem ir grūti vai pat ne<PERSON> i<PERSON>t tekstu ar zemu kontrastu. Atšķirams saišu teksts uzlabo pieredzi vājredzīgiem lietotājiem. [Uzzi<PERSON><PERSON>, kā padarīt saites atšķiramas](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Lai atšķirtu saites, j<PERSON><PERSON>ļaujas uz krāsu"}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Var atšķirt saites, nepaļaujoties uz krāsu"}, "core/audits/accessibility/link-name.js | description": {"message": "Atšķirams, unikāls un fokusējams saites teksts (un alternatīvais teksts attēliem, kas tiek izmantoti kā saites) nodrošina labākas navigācijas iespējas lietotājiem, kuri izmanto ekr<PERSON>a <PERSON>. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt saites pieejamas](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Saitēm nav atšķirama nosaukuma"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ir atšķirams nosaukums"}, "core/audits/accessibility/list.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a lasītāji nolasa sarakstus īpašā veidā. <PERSON>a saraksta struktūra ir pareiza, tiek atvieglota satura atskaņošana ekrāna lasītājā. [Uzziniet vairāk par pareizu sarakstu struktūru](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ne<PERSON>ver tikai tos elementus `<li>` un skriptus, kas atbalsta elementus (`<script>` un `<template>`)"}, "core/audits/accessibility/list.js | title": {"message": "<PERSON><PERSON><PERSON> ietver tikai elementus `<li>` un skriptus, kas atbalsta elementus (`<script>` un `<template>`)"}, "core/audits/accessibility/listitem.js | description": {"message": "<PERSON> ekr<PERSON>a las<PERSON>t<PERSON>ji varētu pareizi atskaņot saraksta vienumus (`<li>`), tiem ir jābūt ietvertiem vecākelementā `<ul>`, `<ol>` vai `<menu>`. [Uzziniet vairāk par pareizu sarakstu struktūru](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> vien<PERSON> (`<li>`) nav ietverti vecākelementā `<ul>`, `<ol>` vai `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> v<PERSON> (`<li>`) ir iet<PERSON>i vecā<PERSON> `<ul>`, `<ol>` vai `<menu>`."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ka lapa tiks automātiski atsvaidzināta un tādējādi atkal tiks pāriets uz lapas augšdaļu. Tas var būt kaitinoši vai mulsinoši. [Uzziniet vairāk par atsvaidzināšanas metatagu](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumentā tiek izmantots metatags `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumentā netiek izmantots metatags “`<meta http-equiv=\"refresh\">`”"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "<PERSON>a <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a ir at<PERSON><PERSON><PERSON><PERSON>, lieto<PERSON><PERSON><PERSON><PERSON> ar redzes tra<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri i<PERSON> e<PERSON>r<PERSON>, ir grūt<PERSON><PERSON> piekļūt tīmekļa lapas saturam. [Uzziniet vairāk par skatvietas metatagu](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Parametrs `[user-scalable=\"no\"]` tiek lietots elementā `<meta name=\"viewport\">`, vai atrib<PERSON>ts `[maximum-scale]` ir mazāks par 5"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Atribūts `[user-scalable=\"no\"]` netiek i<PERSON>ts elementā `<meta name=\"viewport\">`, un atribūts `[maximum-scale]` nav mazāks par 5"}, "core/audits/accessibility/object-alt.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a lasītāji nevar tulkot saturu, kas nav teksts. Ja elementiem `<object>` pievienosiet alternatīvo tekstu, ekr<PERSON>a lasītāji varēs lietotājiem paziņot teksta nozīmi. [Uzziniet vairāk par elementu “`object`” alternatīvo tekstu](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementiem `<object>` nav alternatīvā teksta"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` ietver alternatīvo tekstu"}, "core/audits/accessibility/select-name.js | description": {"message": "Veidlapu elementi bez efektīvām iezīmēm var radīt neapmierinošu pieredzi ekrāna lasītāju lietotājiem. [Uzziniet vairāk par elementu “`select`”](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Atlasītajiem elementiem nav saistītu iezīmju elementu"}, "core/audits/accessibility/select-name.js | title": {"message": "<PERSON>ī<PERSON><PERSON><PERSON> elementiem ir saistīti iezīmju elementi"}, "core/audits/accessibility/skip-link.js | description": {"message": "Ja iekļaus<PERSON> i<PERSON> saiti, lieto<PERSON><PERSON><PERSON> varēs pāriet uz galveno saturu un ietaupīt laiku. [Uzziniet vairāk par izlaišanas saitēm](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> nevar fokus<PERSON>t."}, "core/audits/accessibility/skip-link.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> var fokus<PERSON>t."}, "core/audits/accessibility/tabindex.js | description": {"message": "Ja vērtība ir lielāka par “0”, navig<PERSON>cijas secība ir noteikta. <PERSON> gan tehniski šis risinājums ir derīgs, bie<PERSON>i vien tas mulsina lietotāju<PERSON>, kuri i<PERSON>to at<PERSON> tehnoloģijas. [Uzziniet vairāk par atribūtu “`tabindex`”](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Dažiem elementiem atribūta “`[tabindex]`” vērt<PERSON><PERSON> ir lielā<PERSON> par 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nevienam elementam nav atribūta `[tabindex]` v<PERSON><PERSON><PERSON><PERSON>, kas augst<PERSON><PERSON> par 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "At<PERSON><PERSON><PERSON><PERSON> “summary” ir j<PERSON><PERSON><PERSON><PERSON> tabulas strukt<PERSON>ra, savukārt elementam `<caption>` ir j<PERSON><PERSON><PERSON><PERSON> ekrānā redzamais nosaukums. Precīza tabulas marķēšana palīdz lietot<PERSON>, kuri i<PERSON>to ekr<PERSON>a <PERSON>. [Uzziniet vairāk par atribūtu “summary” un elementu “caption”](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> “summary” un elementā `<caption>.` ir viens un tas pats saturs"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> “summary” un elementā `<caption>` ir atšķirīgs saturs"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a las<PERSON>t<PERSON> funkcijas atvieglo pārvietoša<PERSON> tabul<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai tabulās tiktu izmantots elements “caption”, nevis šūnas ar atribūtu `[colspan]`, varat uzlabot lieto<PERSON> pieredzi lieto<PERSON>, kuri izmanto ekr<PERSON>a las<PERSON>tā<PERSON>. [Uzziniet vairāk par subtitriem.](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "<PERSON> tabulās nor<PERSON><PERSON><PERSON>, tiek i<PERSON> nevis `<caption>`, bet <PERSON><PERSON>nas ar atribūtu `[colspan]`."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "<PERSON> tabulās nor<PERSON><PERSON><PERSON>, tiek i<PERSON> `<caption>`, nevis <PERSON> ar atribūtu `[colspan]`"}, "core/audits/accessibility/target-size.js | description": {"message": "Iestatot skāriena mērķiem pietiekamus izmērus un atstarpes, varat palīdzēt šos mērķus aktivizēt lietotājiem, kam ir grūtības atlasīt mazas vadīklas. [Uzziniet vairāk par skāriena mērķiem](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Skāriena mērķu izmēri un atstarpes nav pietiekamas"}, "core/audits/accessibility/target-size.js | title": {"message": "Skāriena mērķiem ir pietiekami izmēri un atstarpes"}, "core/audits/accessibility/td-has-header.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> las<PERSON>t<PERSON> funkcijas atvieglo pārvietošanos tabulās. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai lielā tabul<PERSON> (tā<PERSON><PERSON>, kurā ir vismaz trīs rindas un trīs kolonna<PERSON>) katram `<td>` elementam būtu saistīta tabulas galvene, varat u<PERSON>labot lieto<PERSON> piered<PERSON> lie<PERSON>, kuri i<PERSON>to ekrān<PERSON> las<PERSON>tā<PERSON>. [Uzziniet vairāk par tabulu galvenēm.](https://dequeuniversity.com/rules/axe/4.8/td-has-header)"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "`<td>` elementiem lielā `<table>` elementā nav tabulas galveņu."}, "core/audits/accessibility/td-has-header.js | title": {"message": "`<td>` elementiem lielā `<table>` elementā ir viena vai vairākas tabulas galvenes"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a lasītā<PERSON> funkcijas atvieglo pārvietoša<PERSON> tabul<PERSON>. Ja elementa `<td>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kas izmanto atribūtu `[headers]`, ir atsauces tikai uz citām šūnām tajā pašā tabulā, tiek nodro<PERSON>ta labāka pieredze lietot<PERSON>, kuri izmanto ekrāna lasītā<PERSON>. [Uzziniet vairāk par atribūtu “`headers`”](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Elementa `<table>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kuras i<PERSON> `[headers]`, ir atsauces uz elementu `id`, kas netika atrasts tajā pašā tabulā."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Elementa `<table>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kuras i<PERSON> `[headers]`, ir atsauces uz citām šūnām tajā pašā tabulā."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lasītā<PERSON> funkcijas atvieglo pārvietoša<PERSON> tabulā<PERSON>. Ja tabulu galvenēs vienmēr ir atsauces uz citām šūnām, tas var nodrošināt labāku pieredzi lie<PERSON>, kuri izmanto ekrāna lasītā<PERSON>. [Uzziniet vairāk par tabulu galvenēm](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` un elementi ar atribūtu `[role=\"columnheader\"/\"rowheader\"]` neietver to a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu <PERSON>"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` un elementi ar atribūtu `[role=\"columnheader\"/\"rowheader\"]` ietver to a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu <PERSON>"}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ja elementos norādīsiet derīgu [BCP 47 valodu](https://www.w3.org/International/questions/qa-choosing-language-tags#question), ekr<PERSON>a las<PERSON>tājs pareizi atskaņos tekstu. [<PERSON><PERSON><PERSON><PERSON>, k<PERSON> izmantot atribūtu “`lang`”.](https://dequeuniversity.com/rules/axe/4.8/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atri<PERSON><PERSON><PERSON>m `[lang]` nav derīgas vērt<PERSON>bas"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atrib<PERSON>tiem `[lang]` ir derīga vērt<PERSON>ba"}, "core/audits/accessibility/video-caption.js | description": {"message": "Ja video ir subtitri, ned<PERSON><PERSON><PERSON><PERSON> lietotāji un lietotāji ar dzirdes traucējumiem varēs vieglāk piekļūt informācijai. [Uzziniet vairāk par video subtitriem](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "<PERSON>ementi “`<video>`” neietver elementu “`<track>`” ar parametru “`[kind=\"captions\"]`”"}, "core/audits/accessibility/video-caption.js | title": {"message": "<PERSON>ementi “`<video>`” ietver elementu “`<track>`” ar parametru “`[kind=\"captions\"]`”"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Pašreizē<PERSON><PERSON> vērtība"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Ieteicamais marķieris"}, "core/audits/autocomplete.js | description": {"message": "Atri<PERSON><PERSON><PERSON> “`autocomplete`” palīdz lietotā<PERSON>em <PERSON> iesniegt veidlap<PERSON>. Lai sekmētu lietotāju darb<PERSON>, apsveriet iespēju iespējot automā<PERSON><PERSON>, atri<PERSON><PERSON><PERSON> “`autocomplete`” iestatot derīgu vērtību. [Uzziniet vairāk par atribūtu “`autocomplete`” veidlapās](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` elementiem nav pareizu `autocomplete` at<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Pārskatīt marķieru secību"}, "core/audits/autocomplete.js | title": {"message": "`<input>` elementos ir pareizi i<PERSON>ts atribūts `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Viens vai vairāki `autocomplete` marķieri “{token}”, kas atrodas fragmentā {snippet}, nav derīgi"}, "core/audits/autocomplete.js | warningOrder": {"message": "Pārskatiet secību marķieriem “{tokens}” fragmentā {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "<PERSON><PERSON> nov<PERSON><PERSON>"}, "core/audits/bf-cache.js | description": {"message": "<PERSON><PERSON>zas navigācijas darbības tiek veik<PERSON>, pā<PERSON><PERSON>t uz iepriekšējo lapu vai atkal atgriežoties sākotnējā lapā. Pilnīga saglabāšana kešatmiņā (bfcache) var paātrināt šo navigāciju. [Uzziniet vairāk par bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)."}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 kļūmes cēlonis}zero{# kļūmes cēloņu}one{# kļūmes cēlonis}other{# kļūmes cēloņi}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Atteices iemesls"}, "core/audits/bf-cache.js | failureTitle": {"message": "Lapa neļāva veikt atjaunošanu no kešatmiņas"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON><PERSON> veids"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pā<PERSON> ne<PERSON><PERSON>"}, "core/audits/bf-cache.js | title": {"message": "Lapa neliedza veikt atjaunošanu no kešatmiņas"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Pilnīgu saglab<PERSON><PERSON><PERSON> kešatmiņ<PERSON> nevar testēt iepriekšējā Chrome bez grafiskās lietotāja saskarnes versijā (`--chrome-flags=\"--headless=old\"`). <PERSON> skat<PERSON><PERSON> p<PERSON> rezult<PERSON>, izman<PERSON><PERSON>et jauno Chrome bez grafiskās lietotāja saskarnes versiju (`--chrome-flags=\"--headless=new\"`) vai standarta Chrome versiju."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome paplašinājumi negatīvi ietekmē šīs lapas ielādes veiktspēju. Mēģiniet lapas pārbaudi veikt inkognito režīmā vai no Chrome profila bez paplašinājumiem."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Skripta novē<PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>ā procesora laiks"}, "core/audits/bootup-time.js | description": {"message": "Ieteicams samazināt laiku, kas tiek izmantots JS parsēšanai, kompilēšanai un izpildei. Iespējams, kons<PERSON><PERSON><PERSON><PERSON>, ka ir noderīgi izmantot mazākas JS lietderīgās slodzes. [U<PERSON><PERSON><PERSON>, kā samazināt JavaScript izpildes laiku](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "JavaScript izpildes la<PERSON>"}, "core/audits/bootup-time.js | title": {"message": "JavaScript izpildes laiks"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, dub<PERSON><PERSON><PERSON> JavaScript moduļus no pakām, lai sama<PERSON>tu nevajad<PERSON><PERSON><PERSON> baitu a<PERSON>, ko patērē tīkla darb<PERSON>. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Noņemiet dublētos moduļus JavaScript pakās"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Lieli GIF attēli nav efektīvi animēta satura rādīšanai. Animācijām ieteicams izmantot MPEG4/WebM video, bet statiskiem attēliem — PNG/WebP, nevis GIF, lai samazinātu tīkla aktivitātes izmantoto baitu apjomu. [Uzziniet vairāk par efektīviem video formātiem](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)."}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Izmantojiet video failu formātus animētam saturam"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Funkcionalitāšu un pārveidošanas kodi ļauj mantotos pārlūkos izmantot jaunas JavaScript funkcijas. Tomēr modernos pārlūkos daudzi no tiem nav nepieciešami. JavaScript pakai pielāgojiet modernu skripta izvietošanas stratēģiju, i<PERSON><PERSON><PERSON><PERSON> moduļa/bez moduļa funkcijas note<PERSON>, lai samazinātu uz moderniem pārlūkiem nosūtīta koda apjomu, vienlaikus nodrošinot atbalstu mantotiem pārlūkiem. [Uzziniet, kā izmantot modernu JavaScript](https://web.dev/articles/publish-modern-javascript)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Novērsiet mantotā JavaScript koda izmantošanu modernos pārlūkos"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Tādi attēlu formāti kā WebP un AVIF bieži ir sekmīgāk saspiežami nekā PNG vai JPEG. Tas nozīmē ātrāku lejupielādi un mazāku datu patēriņu. [Uzziniet vairāk par modernajiem attēlu formātiem](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> attēlus nākamās paaudzes formātos"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON> laiku līdz interakti<PERSON><PERSON><PERSON>, ārp<PERSON> ekrāna attēlus un paslēptos attēlus ar lēnu ielādi ieteicams atlikt līdz visu svarīgo resursu ielādes pabeig<PERSON>nai. [<PERSON><PERSON><PERSON><PERSON>, kā atlikt ārpus ekrāna esošus attēlus](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Ā<PERSON>us ekrāna esošo attēlu atlikša<PERSON>"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursi bloķē jūsu lapas satura pirmo atveidojumu. Ieteicams rādīt svarīgo JS/CSS saturu iekļautā veidā un atteikties no nesvarīgā JS satura/stiliem. [<PERSON><PERSON><PERSON><PERSON>, kā izvairīties no resursiem, kas bloķē renderēšanu](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Samaziniet resursus, kas bloķē <PERSON>"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "<PERSON>las tīkla lietderīgās slodzes izmaksā lietotājiem īstu naudu un ir cieši saistītas ar ilgu ielādes laiku. [Uzzi<PERSON><PERSON>, kā samazināt lietderīgo slod<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lielums bija {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lielas tīkla lietderī<PERSON><PERSON><PERSON> s<PERSON>pieļau<PERSON>"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Nepieļauj pārāk lielu tīkla lietderīgo slodzi"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Samazinot CSS failus, var samazināties tīkla lietderī<PERSON> s<PERSON>. [<PERSON><PERSON><PERSON><PERSON>, kā samazināt CSS failus](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Samaziniet CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Samazinot JavaScript failus, var samazināties lietderīgā<PERSON> slodzes apjomi un skriptu parsēšanas ilgums. [<PERSON><PERSON><PERSON><PERSON>, kā samazināt JavaScript failus](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript·samazināšana"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Noņemiet neizmantotās kārtulas no stilu lapām un atlieciet pirmā ekrāna saturā neizmantotā CSS koda ielādi, lai samazinātu tīkla aktivitātes izmantoto baitu apjomu. [<PERSON><PERSON><PERSON><PERSON>, kā samazināt neizmantotu CSS kodu](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Noņemiet neizmantoto CSS kodu"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Noņemiet neizmantoto JavaScript kodu un atlieciet sk<PERSON><PERSON>, l<PERSON><PERSON><PERSON> tie bū<PERSON>, lai samazinātu tīkla aktivitātes izmantoto baitu apjomu. [<PERSON><PERSON><PERSON>t, kā samazināt neizmantotu JavaScript kodu](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Noņemiet neizmantoto JavaScript kodu"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Iestatot ilgu kešatmiņ<PERSON> mū<PERSON>, lapas atkārtoti apmeklējumi varētu paātrināties. [Uzziniet vairāk par efektīvām kešatmiņas politikām](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Atrasts 1 resurss}zero{Atrasti # resursi}one{Atrasts # resurss}other{Atrasti # resursi}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statisko elementu note<PERSON>na, izmantojot efektīvu kešatmiņas politiku"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Efektīvas kešatmiņas politikas izmantošana statiskiem elementiem"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizēti attēli tiek ielādēti ātrāk un izmanto mazāku mobilo datu apjomu. [<PERSON><PERSON><PERSON><PERSON>, kā efektīvi kodēt attēlus](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efektīva attēlu kodēšana"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Faktiskie izmēri"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Attēli bija lielāki par parādīta<PERSON>em i<PERSON>ēriem"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Attēli bija atbilstoši parādītajiem izmēriem"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> izmēra attēlus, lai tiktu izmantots mazāks mobilo datu apjoms un tiktu uzlabots ielādes laiks. [<PERSON><PERSON><PERSON><PERSON>, kā <PERSON>īt attēlu izmērus](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> lie<PERSON> att<PERSON>"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON> kop<PERSON> t<PERSON> (baitos), ieteica<PERSON> <PERSON><PERSON><PERSON><PERSON> (Gzip, Deflate vai Brotli). [Uzziniet vairāk par teksta sasp<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Iespējojiet te<PERSON>ta <PERSON>"}, "core/audits/content-width.js | description": {"message": "Ja lietotnes satura platums neatbilst skatvietas platumam, lietotne var nebūt optimizēta mobilo ierīču ekrāniem. [U<PERSON><PERSON><PERSON>, kā pielāgot skatvietas satura izmērus](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Skatvietas izmērs ({innerWidth} px) neatbilst loga izmēram ({outerWidth} px)."}, "core/audits/content-width.js | failureTitle": {"message": "Satura izmērs nav pareiz<PERSON>, sal<PERSON><PERSON><PERSON><PERSON> ar s<PERSON>tu"}, "core/audits/content-width.js | title": {"message": "Satura izmērs ir <PERSON>, sal<PERSON><PERSON><PERSON><PERSON> ar s<PERSON>"}, "core/audits/critical-request-chains.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Kritisko pieprasījumu ķēdes” t<PERSON><PERSON><PERSON><PERSON> parāda, kuri resursi ir ielādēti ar augstāko prioritāti. <PERSON> uzlabotu lapas ielādi, ieteicams samazināt ķēžu garumu, samazin<PERSON>t resursu lejupielādes apjomu vai atlikt nevajadzīgo resursu lejupielādi. [U<PERSON><PERSON><PERSON>, kā izvairīties no kritisku pieprasījumu ķēdēm](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Atrasta 1 ķēde}zero{Atrastas # ķēdes}one{Atrasta # ķēde}other{Atrastas # ķēdes}}"}, "core/audits/critical-request-chains.js | title": {"message": "Novērsiet kritisko pieprasījumu ķēdes"}, "core/audits/csp-xss.js | columnDirective": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "Stingra satura droš<PERSON> politika (SDP) ievērojami samazina starpvietņu skrip<PERSON> (cross-site scripting — XSS) uzbrukumu risku. [Uzziniet, k<PERSON> izmantot SDP, lai novērstu XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintakse"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Lapā ir SDP (CSP), kas definēta tagā “`<meta>`”. Ieteicams pārvietot SDP (CSP) uz HTTP galveni vai definēt citu stingru SDP (CSP) HTTP galvenē."}, "core/audits/csp-xss.js | noCsp": {"message": "Netika atrasta neviena SDP piemērošanas režīmā"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai SDP efektīvi novērstu XSS uzbrukumus"}, "core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/brīdin<PERSON><PERSON><PERSON>"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Novecojušas saskarnes API laika gaitā tiks noņemtas no pārlūka. [Uzziniet vairāk par saskarnēm API, kuru darbība ir pārt<PERSON>ta](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Atrasts 1 brīdinājums}zero{Atrasti # brīdinājumi}one{Atrasts # brīdinājums}other{Atrasti # brīdinājumi}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Tiek izmantotas novecojušas saskarnes API"}, "core/audits/deprecations.js | title": {"message": "Nav atļautas novecojušas saskarnes API"}, "core/audits/dobetterweb/charset.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> rakstzīmju kodējuma deklarācija. To var ietvert tagā `<meta>` HTML koda pirmajos 1024 baitos vai satura veida HTTP atbildes galvenē. [Uzziniet vairāk par rakstzīmju kodējuma dekla<PERSON>](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> raks<PERSON><PERSON><PERSON><PERSON> k<PERSON>, vai HTML kodā tā ir ietverta par tālu"}, "core/audits/dobetterweb/charset.js | title": {"message": "Pareizi definēta r<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>a"}, "core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> DOCTYP<PERSON>, pārl<PERSON><PERSON> nevar pārslēgties uz saderības režīmu. [Uzziniet vairāk par DOCTYPE deklarēšanu](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE nosaukumam ir jāb<PERSON>t v<PERSON> “`html`”."}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Do<PERSON><PERSON>ā ir `doctype` de<PERSON><PERSON><PERSON><PERSON><PERSON>, kas aktivizē režīmu `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumentam ir jāietver DOCTYPE."}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "<PERSON><PERSON><PERSON><PERSON> publiskais ID būs tuk<PERSON> v<PERSON>."}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "<PERSON><PERSON><PERSON><PERSON> sist<PERSON> būs tuk<PERSON> v<PERSON>."}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Do<PERSON><PERSON>ā ir `doctype` de<PERSON><PERSON><PERSON><PERSON><PERSON>, kas aktivizē režīmu `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Lapai trūkst HTML DOCTYPE, tāpēc tiek aktivizēts saderības režīms"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Lapā ir HTML DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statist<PERSON><PERSON> dati"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Liels DOM palielina atmi<PERSON> lie<PERSON>, pail<PERSON><PERSON><PERSON> [stila aprēķinus](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) un izraisa dārgu [izk<PERSON>rto<PERSON><PERSON> plūduma p<PERSON>](https://developers.google.com/speed/articles/reflow). [Uzziniet, kā izvairīties no pārmērīga DOM lieluma](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elements}zero{# elementu}one{# elements}other{# elementi}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Pārāk lielu DOM izmēru nepieļaušana"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DOM dziļums"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM elementu kopskaits"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Nepieļauj pārāk lielus DOM izmērus"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Lietotājus mulsina un viņiem nerada uzticību vietnes, kas pieprasa viņu atrašanās vietu bez konteksta. Tā vietā ieteicams saistīt pieprasījumu ar lietotāja darbību. [Uzziniet vairāk par ģeolokācijas atļauju](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Tiek pieprasīta ģeogrāfiskās atrašanās vietas note<PERSON> atļ<PERSON>ja lapas ielādei"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Netiek pieprasīta ģeogrāfiskās atrašanās vietas note<PERSON> atļau<PERSON> lapas ielādei"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> veids"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome izstrādātāja rīku panelī `Issues` ir reģistrētas neatrisinātas problēmas, kas jāatrisina. Tās var rasties no tīkla pieprasījuma kļūmēm, nepietiekamām drošības vadīklām un citām pārlūkprogrammas problēmām. <PERSON> skatītu sīkāku informāciju par katru problēmu, Chrome izstrādātāja rīkos atveriet paneli Issues (Problēmas)."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Chrome izstrādātāja rīku panelī `Issues` tika reģistrētas problēmas"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloķēts saskaņā ar politiku par citas izcelsmes resursu kopīgošanu"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pat<PERSON> daudz resursu"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome izstrādātāja rīku panelī `Issues` netika reģistrētas problēmas"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Visas lapā noteiktās JavaScript priekšgalsistēmas bibliotēkas. [Uzziniet vairāk par šo JavaScript bibliotēkas noteikšanas diagnostikas pārbaudi](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Noteiktās JavaScript bibliotēkas"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> ir lē<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kas ir din<PERSON>, i<PERSON><PERSON><PERSON><PERSON> metodi “`document.write()`”, var ievē<PERSON>ja<PERSON> a<PERSON>kav<PERSON>t lapas ielādi. [<PERSON><PERSON><PERSON><PERSON>, k<PERSON> izvairīties no metodes “document.write()”](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Izvairieties no: `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Netiek izmantots elements “`document.write()`”"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Lietotājus mulsina un viņiem nerada uzticību vietnes, kas pieprasa sūtīt paziņojumus bez konteksta. Tā vietā ieteicams saistīt pieprasījumu ar lietotāja žestiem. [Uzziniet vairāk par atbildīgu paziņojumu atļau<PERSON> saņem<PERSON>nu](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Tiek pieprasīta p<PERSON> atļauja lapas ielādei"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Netiek pieprasīta p<PERSON> atļauja lapas ielādei"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Nav ieteicams aizliegt satura <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jo <PERSON><PERSON><PERSON> ievades lauki pasliktina lietotāja pieredzi un mazina drošību, bloķējot paroļu pārvaldniekus. [Uzziniet vairāk par lietotājiem draudzīgiem ievades laukiem](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "<PERSON>eļauj <PERSON>to<PERSON>ā<PERSON>em i<PERSON>ī<PERSON>t saturu ievades laukos"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Ļauj lietotājiem ielīmēt saturu ievades laukos"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokols"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 piedāvā daudzas priekšrocības salīdzinājumā ar HTTP/1.1, tostarp binārās galvenes un multipleksēšanu. [Uzziniet vairāk par HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 pieprasījums nav parādīts, izmantojot HTTP/2}zero{# pieprasījumi nav parādīti, izmantojot HTTP/2}one{# pieprasījums nav parādīts, izmantojot HTTP/2}other{# pieprasījumi nav parādīti, izmantojot HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Izmantojiet HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Lai uzlabotu savas lapas ritin<PERSON><PERSON> veiktspēju, ieteicams atzīmēt pieskārienu un peles ritenīša notikumu uztvērējus kā “`passive`”. [Uzziniet vairāk par pasīvo notikumu uztvēr<PERSON><PERSON> i<PERSON>](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "<PERSON><PERSON> p<PERSON>, lai uzla<PERSON>u ritin<PERSON><PERSON> veiktspēju"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "<PERSON><PERSON> i<PERSON><PERSON> p<PERSON>, lai uzla<PERSON>u ritin<PERSON><PERSON> veiktspēju"}, "core/audits/errors-in-console.js | description": {"message": "Konsolē reģistrētās kļūdas norāda uz neatrisinātām problēmām. Tās var rasties no tīkla pieprasījuma kļūmēm un citām pārlūka problēmām. [Uzziniet vairāk par šīm kļūdām konsoles diagnostikas pārbaudē](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "Pārlūkprogrammas kļūdas tika reģistrētas konsolē"}, "core/audits/errors-in-console.js | title": {"message": "Neviena pārlūkprogrammas kļūda nav reģistrēta konsolē"}, "core/audits/font-display.js | description": {"message": "Izmantojiet CSS funkciju “`font-display`” lai teksts tīmekļa fontu ielādes laikā būtu redzams lietotājiem. [Uzziniet vairāk par “`font-display`”](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Visa teksta redzamības nodroš<PERSON>na tīmekļa fonta ielādes laikā"}, "core/audits/font-display.js | title": {"message": "Tīmekļa fonta ielādes laikā viss teksts paliek redzams"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse nevarēja automātiski pārbaudīt `font-display` vērtību no šī avota: {fontOrigin}.}zero{Lighthouse nevarēja automātiski pārbaudīt `font-display` vērtības no šī avota: {fontOrigin}.}one{Lighthouse nevarēja automātiski pārbaudīt `font-display` vērtības no šī avota: {fontOrigin}.}other{Lighthouse nevarēja automātiski pārbaudīt `font-display` vērtības no šī avota: {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON> (faktiskā)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (attēlotā)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Attēla parādī<PERSON> izmēriem jāatbilst dabiskajai malu attiecībai. [Uzziniet vairāk par attēlu malu attiecību](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Tiek rādīti attēli ar nepareizu malu attiec<PERSON>bu"}, "core/audits/image-aspect-ratio.js | title": {"message": "Tiek rādīti attēli ar pareizu malu attiec<PERSON>bu"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Faktiskie izmēri"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Attēlojuma izmēri"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON>red<PERSON><PERSON>ē<PERSON>"}, "core/audits/image-size-responsive.js | description": {"message": "Lai attēls būtu pēc iespē<PERSON> s<PERSON>, tā sākotnējiem izmēriem ir jābūt proporcionāliem attēlojuma lielumam un pikseļu attiecībai. [<PERSON><PERSON><PERSON><PERSON>, kā iesniegt adaptīvus attēlus](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Attēli tiek rādīti zemā izšķirtspējā"}, "core/audits/image-size-responsive.js | title": {"message": "Attēli tiek rādīti piemērotā izšķirtspējā"}, "core/audits/installable-manifest.js | already-installed": {"message": "Lietotne jau ir instalēta"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Nevar<PERSON>ja iel<PERSON>dēt obligātu ikonu no manifesta."}, "core/audits/installable-manifest.js | columnValue": {"message": "Atteices iemesls"}, "core/audits/installable-manifest.js | description": {"message": "Pakalpojumu skripts ir tehnoloģija, kas palīdz nodrošināt daudzas progresīvo tīmekļa lietotņu funkcijas, pie<PERSON><PERSON><PERSON>, lietotnes izmantošanu bezsaistē, pievie<PERSON>šanu sākuma ekrānam un informatīvos paziņojumus. Atbilstoši ieviešot pakalpojumu skriptu un manifestu, pārlūkprogrammās var aktīvi rādīt lietotājiem uzvednes ar aicinājumu pievienot jūsu lietotni sākuma ekrānam. Tādējādi var izdoties palielināt iesaisti. [Uzziniet vairāk par manifesta instalējamības prasībām](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{Viens iemesls}zero{# iemeslu}one{# iemesls}other{# iemesli}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Tīmekļa lieto<PERSON>nes manifests vai pakalpojumu skripts neatbilst instalējamības prasībām"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play veikala ID neatbilst Play veikala lietotnes vietrādim URL"}, "core/audits/installable-manifest.js | in-incognito": {"message": "<PERSON><PERSON> ir ielād<PERSON>ta inkognito logā"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Manifesta “`display`” rekvi<PERSON><PERSON>ta vērt<PERSON>i ir jā<PERSON> “`standalone`”, “`fullscreen`” vai “`minimal-ui`”."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifestā ir ietverts lauks “display_override”, un pirmajam atbalstītajam attēloša<PERSON> režīmam jābūt “standalone”, “fullscreen” vai “minimal-ui”"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestu nevarē<PERSON> i<PERSON>, tas ir tuk<PERSON>, vai to nevarēja pars<PERSON>t"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifesta ienešanas laikā mainījās manifesta URL."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestā nav lauka “`name`” vai “`short_name`”."}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestā nav piemērotas ikonas. Nepieciešams PNG, SVG vai WebP formāta fails, kura izmēriem jābūt vismaz {value0} piks. un atribūtam “sizes” jābūt iestatītam. Ja ir iestatīts atribūts “purpose”, tā vērtībai jāietver “any”."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nav nod<PERSON><PERSON><PERSON><PERSON><PERSON> neviena <PERSON>, SVG vai WebP formāta ikona, kura ir vismaz {value0} piks. liels kvadrāts un kuras atribūts “purpose” nav iestatīts vai tam iestatīta vērtība “any”."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Lejupielādētā ikona bija tukša vai bojāta."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nav norādīts Play veikala ID"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Lapā nav <link> taga ar manifesta vietrādi URL"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "<PERSON>eva<PERSON><PERSON><PERSON> p<PERSON> p<PERSON>, jo manifestā nav lauka “start_url”"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Instalējamības kļūdas ID {errorId} netika atpazīts"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Lapas rādī<PERSON><PERSON> vajadz<PERSON>gie dati netiek ienesti no droša s<PERSON>kotnējā servera"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "<PERSON>pa nav ielād<PERSON>ta galvenajā ietvarā"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "<PERSON><PERSON> nedarbojas bezsaistē"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PTL ir atinstalēta, un notiek instalējamības pārbaužu atiestatīšana."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Norādītā lietojumprogrammu platforma netiek atbalstīta operētājsistēmā Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “prefer_related_applications” manifestā ir norādīta vērtība “true”"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Rekvizīts “prefer_related_applications” tiek atbalstīts tikai pārlūkprogrammas Chrome beta un stabilās versijās operētājsistēmā Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse nevarēja note<PERSON>, vai lapa ir instalējama. Pamēģiniet izmantot jaunāku pārlūka Chrome versiju."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Manifesta sākuma URL nav derīgs"}, "core/audits/installable-manifest.js | title": {"message": "Tīmekļ<PERSON> manifests un pakalpojumu skripts atbilst instalējamības prasībām"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Manifestā ir <PERSON> URL, kas satur lietotājvārdu, paroli vai portu"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Lapa nedarbojas bezsaistē. Lapa vairs netiks uzskatīta par instalējamu, kad 2021. gada augustā būs izlaista Chrome 93 stabilā versija."}, "core/audits/is-on-https.js | allowed": {"message": "Atļauts"}, "core/audits/is-on-https.js | blocked": {"message": "Bloķēts"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nedrošs URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Visas vietnes ir jāaizsarg<PERSON> ar protokolu HTTPS, pat ja tajās netiek apstrādāti sensitīvi dati. Tostarp jāizvairās izmantot [jauktu saturu](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) — iel<PERSON><PERSON><PERSON><PERSON> noteiktus resursus, izman<PERSON>jot HTTP protokolu, kaut gan atbilde uz sākotnējo pieprasījumu nosūtīta, izmantojot HTTPS protokolu. HTTPS neļauj iebrucējiem manipulēt vai pasīvi uztvert sakarus starp jūsu lietotni un lietotājiem, un tas ir HTTP/2 un daudzu jaunu tīmekļa platformu saskarņu API priekšnoteikums. [Uzziniet vairāk par HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Noteikts 1 nedrošs pieprasījums}zero{Noteikti # nedroši pieprasījumi}one{Noteikts # nedrošs pieprasījums}other{Noteikti # nedroši pieprasījumi}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Netiek izmantots protokols HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Tiek izmantots protokols HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automātiski jaunināts uz HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "<PERSON><PERSON><PERSON><PERSON> ar <PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% no LSA"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Posms"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Laiks"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Tas ir lielākais skatvietā atveidotais satura elements. [Uzziniet vairāk par lielāko satura atveidojuma elementu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "<PERSON>elā<PERSON> a<PERSON>"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Atveidošanas aizkave"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "LLPB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Largest Contentful Paint elements"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Izkārtojuma nobī<PERSON> ietekme"}, "core/audits/layout-shift-elements.js | description": {"message": "Šos DOM elementus visvairāk ietekmēja izkārtojuma nobīdes. Dažas izkārtojuma nobīdes var nebūt iekļautas CLS metrikas vērtībā [logošanas](https://web.dev/articles/cls#what_is_cls) dēļ. [Uzziniet, kā uzlabot CLS](https://web.dev/articles/optimize-cls)."}, "core/audits/layout-shift-elements.js | title": {"message": "Centieties novē<PERSON> lielas iz<PERSON>rtoju<PERSON> nobīdes"}, "core/audits/layout-shifts.js | columnScore": {"message": "<PERSON>zk<PERSON><PERSON><PERSON><PERSON><PERSON> nob<PERSON> rād<PERSON>s"}, "core/audits/layout-shifts.js | description": {"message": "<PERSON><PERSON><PERSON> ir lielākās lapā novērotās izkārtojuma nobīdes. Katrs tabulas vienums apzīmē vienu izkārtojuma nobīdi, un tajā tiek rādīts elements, kurā bija vislielākā nobīde. Zem katra vienuma ir norādīti iespējamie galvenie cēloņi, kas izraisīja izkārtojuma nobīdi. Dažas no šīm izkārtojuma nobīdēm var nebūt iekļautas CLS metrikas vērtībā [logošanas](https://web.dev/articles/cls#what_is_cls) dēļ. [Uzziniet, kā uzlabot CLS](https://web.dev/articles/optimize-cls)."}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{Tika atrasta 1 izkārtojuma nobīde}zero{Tika atrastas # izkārtojuma nobīdes}one{Tika atrasta # izkārtojuma nobīde}other{Tika atrastas # izkārtojuma nobīdes}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Tika ielādēts tīmekļa fonts"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "<PERSON><PERSON> i<PERSON> if<PERSON>e"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Tika pielāgots lapas izkārtojums novēlota tīkla pieprasījuma dēļ"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Multivides elementam nav skaidra <PERSON>a"}, "core/audits/layout-shifts.js | title": {"message": "Centieties novē<PERSON> lielas iz<PERSON>rtoju<PERSON> nobīdes"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Attēli pir<PERSON>, kam tiek veikta atlikt<PERSON> iel<PERSON>, tiek atveidoti vēlāk lapas darbības ciklā. Tas var aizkavēt Largest Contentful Paint. [Uzziniet vairāk par optimālu atlikto ielādi](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> satura atveidojuma attēlam tika veikta atliktā ielāde"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Largest Contentful Paint attēlam netika veikta atliktā ielāde"}, "core/audits/long-tasks.js | description": {"message": "Šeit ir norādīti galvenā pavediena uzdevumi ar visilg<PERSON><PERSON> i<PERSON> laik<PERSON>, lai pal<PERSON><PERSON><PERSON><PERSON><PERSON> note<PERSON>, kuri uzdevumi visvair<PERSON>k palielina ievades aizkavi. [<PERSON><PERSON><PERSON><PERSON>, kā izvairīties no ilgiem galvenā pavediena uzdevumiem](https://web.dev/articles/long-tasks-devtools)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Tika atrasts # uzdevums ar ilgu izpildes laiku}zero{Tika atrasti # uzdevumi ar ilgu izpildes laiku}one{Tika atrasts # uzdevums ar ilgu izpildes laiku}other{Tika atrasti # uzdevumi ar ilgu izpildes laiku}}"}, "core/audits/long-tasks.js | title": {"message": "Izvairieties no galvenā pavediena uzdevumiem ar ilgu izpilde<PERSON> laiku"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Ieteicams samazināt laiku, kas tiek izmantots JS parsēšanai, kompilēšanai un izpildei. Iespējams, kons<PERSON><PERSON><PERSON><PERSON>, ka ir noderīgi izmantot mazākas JS lietderīgās slodzes. [<PERSON><PERSON><PERSON><PERSON>, kā minimizēt galvenā pavediena darbu](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Samaziniet galvenā pavediena darbu"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>a <PERSON>"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Lai sasniegtu pēc iespējas vairāk lietot<PERSON>, ieteicams izstrād<PERSON>t viet<PERSON>, kas darbojas visos lielā<PERSON> pārlūkos. [Uzziniet vairāk par saderību dažādos pārlūkos](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "<PERSON><PERSON><PERSON>rogram<PERSON>"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai lietotāji varētu veidot un atvērt dziļās saites uz atsevišķām lapām, izmantojot URL. Turklāt URL jābūt unikāliem, lai varētu kopīgot lapas sociālajos saziņas līdzekļos. [Uzziniet vairāk par dziļo saišu nod<PERSON>](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Katrai lapai ir URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON>eteicams, lai, pieskaroties lietotnes elementiem, pā<PERSON><PERSON> būtu ātras pat tad, ja tīkla darbība ir lēna. Šis ir galvenais faktors, kas nosaka to, kā lietotājs uztver veiktspēju. [Uzziniet vairāk par lapu pārejām](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> nerodas sajūta, ka lēna ielāde bloķētu pārejas starp lapām"}, "core/audits/maskable-icon.js | description": {"message": "Mask<PERSON><PERSON><PERSON> ikona <PERSON>, ka attēls aizpilda visu formu, ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to standarta platumam, kad lietotne tiek instalēta ierīcē. [Uzziniet par maskējamām manifesta ikonām](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifestam nav maskē<PERSON><PERSON> i<PERSON>as"}, "core/audits/maskable-icon.js | title": {"message": "Manifest<PERSON> ir mask<PERSON><PERSON> ikona"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative Layout Shift norāda redzamo elementu kustību skatvietā. [Uzziniet vairāk par rādītāju “Cumulative Layout Shift”](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “First Contentful Paint” at<PERSON>o laik<PERSON>, kad tiek atveidots pirmais teksts vai attēls. [Uzziniet vairāk par rādītāju “First Contentful Paint”](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “<PERSON>rmais nozīmīgais satura atveidojums” ataino, kad k<PERSON>ūst redzams lapas galvenais saturs. [Uzziniet vairāk par rādītāju “Pirmais nozīmīgais satura atveidojums”](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint nosaka lapas atbildi — la<PERSON><PERSON>, kas lapai <PERSON>, lai redzami reaģētu uz lietotāja ievadi. [Uzziniet vairāk par rādītāju “Interaction to Next Paint”](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> “Time to Interactive” ir la<PERSON>, ka<PERSON>, lai lapa k<PERSON>tu pilnībā interaktīva. [Uzziniet vairāk par rādītāju “Time to Interactive”](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint norāda laiku, kur<PERSON> tiek atveidots apjomīgākais teksts vai attēls. [Uzziniet vairāk par rādītāju “Largest Contentful Paint”](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Iespējamā maksimālā First Input Delay, ar ko jūsu lietot<PERSON> var saskarties, ir ilgākā uzdevuma ilgums. [Uzziniet vairāk par rādītāju “Iespējamā maksimālā First Input Delay”](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Ātruma rādīt<PERSON><PERSON><PERSON>, cik ātri tiek parādīts lapas saturs. [Uzziniet vairāk par ātruma rādītāju](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Visu laika periodu summa (no PSM līdz “Time to Interactive”), kad uzdevuma ilgums pārsniedz 50 ms (izteikts milisekundēs). [Uzziniet vairāk par rādītāju “Kopējais bloķēšanas laiks”](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Tīkla aprites laiks (RTT — round trip time) ievērojami ietekmē veiktspēju. Ja aprites laiks līdz avotam ir ilgs, tas nor<PERSON><PERSON>, ka var tikt uzla<PERSON>a to serveru veiktspēja, kas atrodas tuvāk lietotājam. [Uzziniet vairāk par aprites laiku](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Tīkla aprites laiks"}, "core/audits/network-server-latency.js | description": {"message": "Tīmekļa veiktspēju var ietekmēt servera latentums. Ja avota servera latentums ir augsts, tas norāda, ka serveris ir pārslogots vai arī tam ir vāja aizmugursistēmas veiktspēja. [Uzziniet vairāk par servera atbildes laiku](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "<PERSON>a aizmugursistēmas latentums"}, "core/audits/no-unload-listeners.js | description": {"message": "Notikums “`unload`” <PERSON><PERSON>drošināja uzticamu aktiv<PERSON><PERSON><PERSON>, un tā uztveršana var liegt pārlūka optimizāciju, pie<PERSON><PERSON><PERSON>, pilnī<PERSON> saglab<PERSON><PERSON><PERSON> kešatmiņ<PERSON>. Tā vietā izmantojiet notikumu “`pagehide`” vai “`visibilitychange`”. [Uzziniet vairāk par ielādes atcelšanas notikumu uztvērējiem](https://web.dev/articles/bfcache#never_use_the_unload_event)."}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Reģistrē notikuma “`unload`” uztvērēju"}, "core/audits/no-unload-listeners.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no notikuma “`unload`” uztvērējiem"}, "core/audits/non-composited-animations.js | description": {"message": "Nesaliktas animācijas var būt lēnas un palielināt kopējo izkārtojuma nobīdi. [<PERSON><PERSON><PERSON><PERSON>, kā izvairīties no nesaliktām animācijām](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Tika atrasts # animēts elements}zero{Tika atrasti # animēti elementi}one{Tika atrasts # animēts elements}other{Tika atrasti # animēti elementi}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "<PERSON>r <PERSON>lt<PERSON> sa<PERSON> var pārvietot p<PERSON>"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Mērķī ir cita, ne<PERSON><PERSON><PERSON><PERSON> anim<PERSON>"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efektam tiek lietots cits salikšanas režīms, nevis “replace”."}, "core/audits/non-composited-animations.js | title": {"message": "Nesaliktas animācijas nav ieteicams izmantot"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>pašuma darbība ir atkarīga no lodziņa lieluma"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Neatbalstīts CSS īpašums: {properties}}zero{Neatbalstīti CSS īpašumi: {properties}}one{Neatbalstīti CSS īpašumi: {properties}}other{Neatbalstīti CSS īpašumi: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "<PERSON><PERSON><PERSON><PERSON> ir nor<PERSON><PERSON><PERSON><PERSON> laika parametri"}, "core/audits/performance-budget.js | description": {"message": "Saglabājiet tīkla pieprasījumu daudzumu un lielumu zem mērķiem, kas noteikti sniegtajā izpildes budžetā. [Uzziniet vairāk par izpildes budžetiem](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 pieprasījums}zero{# pieprasījumi}one{# pieprasījums}other{# pieprasījumi}}"}, "core/audits/performance-budget.js | title": {"message": "<PERSON>z<PERSON><PERSON>s b<PERSON>"}, "core/audits/preload-fonts.js | description": {"message": "Lai jaunie apmeklētāji varētu izmantot fontus ar atribūtu “`optional`”, ielādējiet tos iepriekš. [Uzziniet vairāk par fontu iepriekšēju ielādi](https://web.dev/articles/preload-optional-fonts)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "Fonti ar atribūtu `font-display: optional` nav iepriekš i<PERSON>ti"}, "core/audits/preload-fonts.js | title": {"message": "Fonti ar atribūtu `font-display: optional` ir iel<PERSON><PERSON><PERSON><PERSON> i<PERSON>š"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ja LCP elements tiek dinamiski pievienots lapai, jums attēls ir j<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>, lai varētu uzlabot LCP. [Uzziniet vairāk par LCP elementu iepriekšēju ielādi](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Largest Contentful Paint attēla iepriekšēja ielāde"}, "core/audits/redirects.js | description": {"message": "Novirzīšana rada papildu aizkaves pirms lapas ielādes. [<PERSON><PERSON><PERSON><PERSON>, kā izvairīties no lapu novirzīšanas.](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Nepieļaujiet vairākas lapas novir<PERSON>"}, "core/audits/seo/canonical.js | description": {"message": "Kanoniskās saites iesaka, kurus URL rādīt meklēšanas rezultātos. [Uzziniet vairāk par kanoniskajām saitēm](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Vairāki konfliktējoši URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Nederīgs URL ({url})."}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Norāda uz citu atribūta “`hreflang`” atrašanās vietu ({url})."}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Nav absolūtais URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Ekvivalenta satura lapas vietā norāda uz domēna saknes piekļuves URL (sākumlapu)"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentā nav derīga atribūta “`rel=canonical`”"}, "core/audits/seo/canonical.js | title": {"message": "Dokumentam ir derīgs atribūts “`rel=canonical`”"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Nepārmeklējama saite"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "<PERSON><PERSON><PERSON> atrib<PERSON>ti “`href`” da<PERSON><PERSON><PERSON> tiek izmantoti meklētājprogrammās, lai pārmeklētu vietnes. Nodr<PERSON><PERSON><PERSON>et, lai enkurelementu atribūtos “`href`” būtu ietvertas saites uz piemērotiem galamērķiem, tād<PERSON><PERSON><PERSON><PERSON>t vietnē atklāt vairāk lapu. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt saites pārmeklējamas](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON><PERSON> nav pārmek<PERSON>"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON><PERSON> ir p<PERSON>"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Pa<PERSON><PERSON><PERSON> nesalasāms teksts"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON>ont<PERSON> lie<PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "<PERSON><PERSON><PERSON> pro<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "<PERSON><PERSON><PERSON>, kas ir mazāks par 12 pikseļ<PERSON>m, ir p<PERSON><PERSON><PERSON><PERSON> mazs, lai būtu <PERSON>, un mobilo ierīču lietotāji ir spiesti tuvināt tekstu, lai varētu to salas<PERSON>t. Centieties, lai vairāk nekā 60% lapas teksta būtu vismaz 12 pikseļu vai lielāka izmēra. [Uzziniet vairāk par salasāmiem fontu izmēriem](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} salasāms teksts"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Teksts nav salas<PERSON>ms, jo mobilo ierīču ekrāniem nav optimizēts skatvietas metatags."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentā netiek izmantoti sa<PERSON>āmi fonta izmēri"}, "core/audits/seo/font-size.js | legibleText": {"message": "Salasāms teksts"}, "core/audits/seo/font-size.js | title": {"message": "Dokumentā i<PERSON>ti sa<PERSON>mi fonta izmēri"}, "core/audits/seo/hreflang.js | description": {"message": "At<PERSON><PERSON><PERSON><PERSON> “hreflang” saites norāda meklētājprogrammām, kuru lapas versiju iek<PERSON><PERSON> meklēša<PERSON> rezultātu sarakstā konkrētai valodai vai reģionam. [Uzziniet vairāk par atribūtu “`hreflang`”](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentā nav derīga atribūta “`hreflang`”"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relatīva “href” vērt<PERSON><PERSON>"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumentam ir derīgs atribūts “`hreflang`”"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Neparedzēts valodas kods"}, "core/audits/seo/http-status-code.js | description": {"message": "Lapas ar nesekmīgu HTTP statusa kodu var tikt indeksētas nepareizi. [Uzziniet vairāk par HTTP statusa kodiem](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Lapai ir nesekmīgs HTTP statusa kods"}, "core/audits/seo/http-status-code.js | title": {"message": "Lapai ir sekmīgs HTTP statusa kods"}, "core/audits/seo/is-crawlable.js | description": {"message": "Meklētājprogrammas nevar i<PERSON><PERSON><PERSON> jūsu lapas mekl<PERSON><PERSON><PERSON> rezultā<PERSON>, ja tām nav atļaujas pārmeklēt lapas. [Uzziniet vairāk par rāpuļprogrammu direktīvām](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ir bloķēta indeksē<PERSON>na"}, "core/audits/seo/is-crawlable.js | title": {"message": "Lapa ir pieejama indek<PERSON>"}, "core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON> a<PERSON> teksts palīdz meklētājprogrammām saprast jūsu saturu. [<PERSON><PERSON><PERSON><PERSON>, kā padarīt saites pieejamākas](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Atrasta 1 saite}zero{Atrastas # saites}one{Atrasta # saite}other{Atrastas # saites}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Saitēm nav a<PERSON>ks<PERSON><PERSON> teksta"}, "core/audits/seo/link-text.js | title": {"message": "Sai<PERSON><PERSON><PERSON> ir a<PERSON><PERSON><PERSON> te<PERSON>"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Lai validētu strukturētos datus, palaidiet [strukturētu datu testēšanas rīku](https://search.google.com/structured-data/testing-tool/) un rīku [Structured Data Linter](http://linter.structured-data.org/). [Uzziniet vairāk par strukturētajiem datiem](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturētie dati ir derīgi"}, "core/audits/seo/meta-description.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rezultā<PERSON> var tikt iekļauti metaapraksti, lai sniegtu īsu kopsavilkumu par lapas saturu. [Uzziniet vairāk par metaaprakstu](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Apraksta teksts ir tuk<PERSON>."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentā nav metaapraksta"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumentā ir metaaprak<PERSON>"}, "core/audits/seo/plugins.js | description": {"message": "Meklētājprogram<PERSON> nevar indeks<PERSON>t spraudņu saturu, un daudzās ierīcēs spraudņi ir ierobežoti vai netiek atbalstīti. [Uzziniet vairāk par izvairīšanos no spraudņiem](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentā tiek i<PERSON> s<PERSON>"}, "core/audits/seo/plugins.js | title": {"message": "Dokumentā netiek pieļauti spraudņi"}, "core/audits/seo/robots-txt.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> fails “robots.txt” ir ne<PERSON><PERSON><PERSON> ve<PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, nevar<PERSON><PERSON>, k<PERSON> vajadz<PERSON>tu pārmeklēt vai indeksēt vietni atbilstoši jūsu vēlmēm. [Uzziniet vairāk par failu “robots.txt”](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Atbildē uz robots.txt pieprasījumu tika atgriezts HTTP statuss {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Tika atrasta 1 kļūda}zero{Tika atrastas # kļūdas}one{Tika atrasta # kļūda}other{Tika atrastas # kļūdas}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nevarēja ielādēt robots.txt failu"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt nav derīgs"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt ir derīgs"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktīvajiem elementiem, pie<PERSON><PERSON><PERSON>, pogām un saitēm, ir jāb<PERSON>t pietiekami lieliem (48 x 48 pikseļi) vai tiem apkārt ir jābūt pietiekami daudz brīvas vietas, lai tiem varētu viegli pieskarties, neai<PERSON><PERSON>ot citus elementus. [Uzziniet vairāk par pieskārienu mērķiem](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} pieskārienu mērķu izmērs ir at<PERSON>."}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Pieskārienu mērķu izmērs ir pārāk maz<PERSON>, jo mobilo ierīču ekrāniem nav optimizēts skatvietas metatags"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Pieskārienu mērķi nav at<PERSON>stoša izmēra"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Mērķis, kas pā<PERSON>"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Piesk<PERSON>rienu mērķis"}, "core/audits/seo/tap-targets.js | title": {"message": "Pieskārienu mērķi ir pietiekami liela i<PERSON>ra"}, "core/audits/server-response-time.js | description": {"message": "Galvenā dokumenta servera atbildes laikam jābūt īsam, jo no tā ir atkarīga pārējo pieprasījumu izpilde. [Uzziniet vairāk par rādītāju “Laiks līdz pirmajam baitam”](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Saknes dokumentam nepieciešamais laiks: {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Samaziniet servera sākotnējās atbildes laiku"}, "core/audits/server-response-time.js | title": {"message": "Sākotnējās servera atbildes laika bija īss"}, "core/audits/splash-screen.js | description": {"message": "Uzplaiksnījuma ekrāns ar piemērotu motīvu nodrošina labu pier<PERSON>, lietot<PERSON><PERSON>em palaižot lietotni no sākuma ekrāna. [Uzziniet vairāk par uzplaiksnījuma ekrāniem](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Nav konfigurēta ar pielāgotu uzplaiksnījuma ekr<PERSON>u"}, "core/audits/splash-screen.js | title": {"message": "Konfigurēta ar pie<PERSON>gotu uzplaiksnīju<PERSON> e<PERSON>u"}, "core/audits/themed-omnibox.js | description": {"message": "Pārlūka adreses joslu var noformēt atbilstoši jūsu vietnes motīvam. [Uzziniet vairāk par adreses joslas motīva piel<PERSON>](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> neiestata adreses joslas motī<PERSON> kr<PERSON>."}, "core/audits/themed-omnibox.js | title": {"message": "Lapa iestata adreses joslas motīva kr<PERSON>."}, "core/audits/third-party-cookies.js | description": {"message": "Chrome nākotnes versijā vairs netiks atbalstīti trešo pušu sīkfaili. [Uzziniet vairāk par trešo pušu sīkfailu lietoša<PERSON> p<PERSON>](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Atrasts 1 sīkfails}zero{Atrasti # sīkfaili}one{Atrasts # sīkfails}other{Atrasti # sīkfaili}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> tre<PERSON> pu<PERSON> s<PERSON>"}, "core/audits/third-party-cookies.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> tre<PERSON>o pušu s<PERSON>"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (klientu atbalsts)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (mārketings)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (<PERSON><PERSON><PERSON><PERSON>)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkts"}, "core/audits/third-party-facades.js | description": {"message": "<PERSON><PERSON>u trešo pušu iegulto resursu ielādi var atlikt. Apsveriet iespēju aizstāt tos ar fasādi, līdz tie būs vajadzīgi. [<PERSON><PERSON><PERSON><PERSON>, kā atlikt trešo pušu resursus ar fasādi](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Pieejama # fasāde, ar ko var aizstāt resursu}zero{Pieejamas # fasādes, ar ko var aizstāt resursus}one{Pieejama # fasāde, ar ko var aizstāt resursus}other{Pieejamas # fasādes, ar ko var aizstāt resursus}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> trešo pušu resursu ielādi var atlikt, izman<PERSON>jot fasādi"}, "core/audits/third-party-facades.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> pušu resursu iel<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> fas<PERSON>des"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> puse"}, "core/audits/third-party-summary.js | description": {"message": "Tre<PERSON><PERSON>s puses kods var ievērojami ietekmēt ielādes veiktspēju. Ierobežojiet lieko trešo pušu pakalpojumu sniedzēju skaitu un mēģiniet ielādēt trešās puses kodu pēc tam, kad jūsu lapa būs ielādēta. [<PERSON><PERSON><PERSON><PERSON>, kā samazināt trešo pušu ietekmi](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses kods bloķēja galveno pavedienu uz {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Samaziniet trešo pušu koda ietekmi"}, "core/audits/third-party-summary.js | title": {"message": "Samaziniet trešās puses koda lietojumu"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Metrisk<PERSON> sistēma"}, "core/audits/timing-budget.js | description": {"message": "Iestatiet laika budž<PERSON>u, lai uzraudz<PERSON>tu savas vietnes veiktspēju. Vietnes ar labu veiktspēju tiek ātri ielādētas un ātri reaģē uz lietotāju ievades notikumiem. [Uzziniet vairāk par izpildes budžetiem](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Laika budžets"}, "core/audits/unsized-images.js | description": {"message": "Iestatiet konkrētu platumu un augstumu attēlu elementiem, lai samazinātu izkārtojuma nobīdes un uzlabotu CLS. [Uzziniet, kā iestatīt attēlu izmērus](https://web.dev/articles/optimize-cls#images_without_dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Attēlu elementiem nav konkrēts `width` un `height`"}, "core/audits/unsized-images.js | title": {"message": "Attēlu elementiem ir konkrēts `width` un `height`"}, "core/audits/user-timings.js | columnType": {"message": "Veids"}, "core/audits/user-timings.js | description": {"message": "Ieteicams pievienot lietotnei Lietotāja laika API, lai noteiktu lietotnes aktuālo veiktspēju lietotāju pamata darbības laikā. [Uzziniet vairāk par lietotāja laika atzīmēm](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 lietotāja laiks}zero{# lietotāju laiks}one{# lietotāja laiks}other{# lietotāju laiks}}"}, "core/audits/user-timings.js | title": {"message": "Lietotāju laika atzī<PERSON> un mērījumi"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Adresei {security<PERSON><PERSON>in} tika at<PERSON><link rel=preconnect>` savie<PERSON><PERSON><PERSON>, ta<PERSON>u pārl<PERSON>kprogramma to neizmantoja. Pārbaudiet, vai pareizi izmanto<PERSON>t atribūtu `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Ieteicams pievienot “`preconnect`” vai “`dns-prefetch`” resursa nor<PERSON>, lai savlaicīgi izveidotu savienojumus ar svarīgiem trešo pušu avotiem. [<PERSON><PERSON><PERSON><PERSON>, kā iepriekš izveidot savienojumu ar obligātajiem avotiem](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Veiciet iepriekšēju pieslēgšanu obligātajiem sākumpunktiem"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Tika atrasti vairāk nekā divi `<link rel=preconnect>` savienojumi. Tie jāizmanto ierobežoti un tikai pašām svarīgākajām sākumadresēm."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Adresei {security<PERSON><PERSON>in} tika at<PERSON><link rel=preconnect>` savie<PERSON><PERSON><PERSON>, ta<PERSON>u pārl<PERSON>kprogramma to neizmantoja. <PERSON><PERSON><PERSON><PERSON>et `preconnect` savienojumus tikai svarīgām sā<PERSON>, ko lapa noteikti piepra<PERSON>."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Adresei {preloadURL} tika atrasts iepriekšējas ielādes parametrs `<link>`, ta<PERSON>u pārlūkprogramma to neizmantoja. Pārbaudiet, vai pareizi izmantojat atribūtu `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Ieteicams izmantot atribūtu `<link rel=preload>`, lai noteiktu prioritāti tādu resursu i<PERSON>, kas pašlaik lapas ielādē tiek pieprasīti vēlāk. [<PERSON><PERSON><PERSON><PERSON>, kā iepriekš ielādēt galvenos pieprasījumus](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Veiciet svarīgāko pie<PERSON>īju<PERSON> iepriekšē<PERSON> i<PERSON>"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Kartes URL"}, "core/audits/valid-source-maps.js | description": {"message": "Avota kartes pārveido saīsināto kodu sākotnējā pirmkodā. Tādējādi izstrādātāji var veikt atkļūdošanu produkcijas kanālā. Turklāt rīkā Lighthouse var gūt plašākus ieskatus. Lai varētu izmantot š<PERSON>, apsveriet iespēju izvietot avota kartes. [Uzziniet vairāk par avota kartēm](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "<PERSON><PERSON> pirm<PERSON> puses JavaScript failam trūkst avota kartes"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Lielā JavaScript failā trūkst avota kartes"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Brīdinājums! Atribūtam `.sourcesContent` trūkst viens vienums}zero{Brīdinājums! Atribūtam `.sourcesContent` trūkst # vienumu}one{Brīdinājums! Atribūtam `.sourcesContent` trūkst # vienums}other{Brīdinājums! Atribūtam `.sourcesContent` trūkst # vienumi}}"}, "core/audits/valid-source-maps.js | title": {"message": "Lapā ir derīgas avota kartes"}, "core/audits/viewport.js | description": {"message": "Parametrs `<meta name=\"viewport\">` ne tikai optimizē jūsu lietotni dažādiem mobilo ierīču ekrāna lielum<PERSON>, bet arī novērš [300 milisekunžu aizkavi lietotāja ievadei](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Uzziniet vairāk par skatvietas metataga i<PERSON>](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Netika atrasts tags `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Nav taga `<meta name=\"viewport\">` ar `width` vai `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Ir tags `<meta name=\"viewport\">` ar `width` vai `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "<PERSON><PERSON> ir <PERSON>a bloķēšanas darbs laik<PERSON>, kad notiek Interaction to Next Paint mērīšana. [Uzziniet vairāk par rādītāju “Interaction to Next Paint”](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms tika patēr<PERSON><PERSON> notikumam “{interactionType}”"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Notikuma mērķis"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "<PERSON><PERSON> sa<PERSON><PERSON> galven<PERSON> mi<PERSON><PERSON><PERSON><PERSON><PERSON> laik<PERSON>"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Ievades aiz<PERSON>"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "core/audits/work-during-interaction.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> darbu galvenās mijiedarbī<PERSON> laikā"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> iete<PERSON>j uzlabot ARIA lietojumu jūsu lietojumprogrammā. Tādēj<PERSON><PERSON> varat uzlabot piered<PERSON> lieto<PERSON>, kuri <PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON> ieteik<PERSON><PERSON>t papildu audio vai video saturu. Tādēj<PERSON><PERSON> var uzlabot pieredzi lietotājiem ar dzirdes vai redzes traucējumiem."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio un video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Šie vienumi parāda izplatītas pieejamības paraugprakses."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON><PERSON> i<PERSON> ies<PERSON>ē<PERSON> [uzlabot tīmekļa lietotnes pieejamību](https://developer.chrome.com/docs/lighthouse/accessibility/). Automātiski var noteikt tikai daļu no problēmām, un netiek garantēta jūsu tīmekļa lietotnes atbilstība pieejamības standartiem, tād<PERSON><PERSON> ieteicams veikt arī [manuālu testēšanu](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON>ie vienumi norāda uz vietām, kurām automātiskais testēšanas rīks nevar piekļūt. Uzziniet vairāk mūsu ceļvedī par [pieejam<PERSON><PERSON> pārskata veikša<PERSON>](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Pie<PERSON>amī<PERSON>"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> i<PERSON> u<PERSON>bot satura lasāmību."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON> i<PERSON>, k<PERSON>, <PERSON><PERSON><PERSON> lab<PERSON> i<PERSON>st j<PERSON><PERSON> saturu."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizācija un lokalizēšana"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> ieteikumi <PERSON>auj u<PERSON>bot lietojumprogrammas vadīklu semantiku. Tād<PERSON><PERSON><PERSON><PERSON> var nodrošin<PERSON>t labāku pieredzi lieto<PERSON>, kuri i<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ek<PERSON><PERSON><PERSON>."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "No<PERSON><PERSON>mi un iezīmes"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Šie ieteikumi <PERSON>j uzlabot jūsu lietotnes tastatūras navigāciju."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigā<PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Šeit ir norādītas iespējas uzlabot tabulu vai sarakstu datu las<PERSON> piered<PERSON> lie<PERSON>, kas i<PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabulas un saraksti"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Uzticamība un drošība"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Izpi<PERSON>s budžets nosaka jūsu vietnes veiktspējas standartus."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budžeti"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Plašāka informācija par jūsu lietojumprogrammas veiktspēju. <PERSON>ie skaitļi [tieši neietekmē](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) veiktspējas rezultātu"}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Vissvarīgākais veiktspējas aspekts ir pikseļu render<PERSON> ātrums ekrānā. Galvenās metrikas: “First Contentful Paint”, “First Meaningful Paint”"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Pirmā satura atveidojuma uzlabojumi"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON> ieteikumi var palīdzēt ātrāk ielādēt jūsu lapu. Tie [tieša veidā neietek<PERSON>ē](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) veiktspējas rezultātu."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON>es<PERSON>ējas"}, "core/config/default-config.js | metricGroupTitle": {"message": "Metrikas"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Uzlabojiet vispār<PERSON><PERSON> iel<PERSON> darb<PERSON>bu, lai lapa reaģētu un būtu gatava izmantošanai pēc iespējas ātrāk. Galvenās metrikas: “Laiks līdz interaktivitātei”, “Ātruma rādītājs”"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON>is<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Veiktspēja"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, varat validēt progresīvo tīmekļa lietotņu aspektus. [<PERSON><PERSON><PERSON><PERSON>, kas veido labu progresīvo tīmekļa lietotni](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> p<PERSON> ir vajadzīgas saskaņā ar standarta [PWA kontrolsarakstu](https://web.dev/articles/pwa-checklist), taču Lighthouse neveic tās automātiski. Tās neietekmē jū<PERSON> rezultātu, taču ir svarīgi pārbaudīt šos lietotnes aspektus manuāli."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PTL"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalēšana"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PTL optimizācija"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON><PERSON> p<PERSON>, ka jūsu lapa atbilst meklētājprogrammu optimizācijas pamatnostādnēm. Ir daudz papildu faktoru, kurus Lighthouse neņem vērā, taču kuri var ietekmēt jūsu meklēšanas rezultātu ran<PERSON>, tostarp [Core Web Vitals](https://web.dev/explore/vitals) veiktspēju. [Uzziniet vairāk par Google meklēšanas pamatiem](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Palaidiet savā vietnē šos papildu apstiprinā<PERSON><PERSON> r<PERSON>, lai aplūkotu papildu MPO paraugpraksi."}, "core/config/default-config.js | seoCategoryTitle": {"message": "MPO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatējiet savu HTML tā, lai rāpuļprogrammas varētu labāk saprast jūsu lietotnes saturu."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Satura paraugprakse"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Rāpuļprogrammā<PERSON> ir ne<PERSON><PERSON> pie<PERSON>ļ<PERSON> jū<PERSON> lie<PERSON>, lai nodro<PERSON><PERSON><PERSON> parād<PERSON><PERSON><PERSON> me<PERSON> rezultā<PERSON>."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un indek<PERSON>"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> lapām ir jābūt piemērotām mobilajām ierīcēm, lai lietotājiem nebūtu jāizmanto savilkšana vai tuvināšana lapu satura lasīšanai. [<PERSON><PERSON><PERSON><PERSON>, kā lapas padarīt piemērotas mobilajām ierīcēm](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mobilajām ierīc<PERSON>m"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Šķiet, testējam<PERSON><PERSON> ierīces centrālā procesora ātrums ir mazāks, nek<PERSON> paredzēts Lighthouse prasībās. Tas var negatīvi ietekmēt veiktspējas rezultātu. Uzziniet vairāk par to, [kā kalibrēt atbilstošu centrālā procesora palēninājuma reizinātāju](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lapa netiek <PERSON>, k<PERSON> par<PERSON>, jo jūsu testa URL ({requested}) tika novirzīts uz: {final}. Mēģiniet tieši testēt otro URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Lapa tika ielād<PERSON><PERSON> pār<PERSON> lē<PERSON>, lai ielāde tiktu pabe<PERSON>ta noteiktajā laika ierobežojumā. Rezultāti var būt nepiln<PERSON>."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. Pārbaudiet lapu vēlreiz un ziņojiet par kļūdu, ja problēmu neiz<PERSON> novērst."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{<PERSON><PERSON><PERSON>ēja<PERSON>, ielādes veiktspēju ietekmē dati, kas tiek glabāti šajā atrašanās vietā: {locations}. Lai šie resursi neietekmētu jūsu rezultātus, pārbaudiet šo lapu inkognito logā.}zero{Ies<PERSON>ējams, ielādes veiktspēju ietekmē dati, kas tiek glabāti šajās atrašanās vietās: {locations}. Lai šie resursi neietekmētu jūsu rezultātus, pārbaudiet šo lapu inkognito logā.}one{Iespējams, ielādes veiktspēju ietekmē dati, kas tiek glabāti šajās atrašanās vietās: {locations}. Lai šie resursi neietekmētu jūsu rezultātus, pārbaudiet šo lapu inkognito logā.}other{Iespējams, ielādes veiktspēju ietekmē dati, kas tiek glabāti šajās atrašanās vietās: {locations}. Lai šie resursi neietekmētu jūsu rezultātus, pārbaudiet šo lapu inkognito logā.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "<PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. Pārbaudiet lapu vēlreiz un ziņo<PERSON>et par k<PERSON>du, ja problēmu neiz<PERSON> novērst."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `link` gal<PERSON><PERSON> ({error}), r<PERSON><PERSON><PERSON>: `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "<PERSON>z<PERSON><PERSON>s laikā tika konstatēta navigācija lapās. Nav ieteicams izmantot laika posma režīmu, lai pārbaudītu navigāciju lapās. Izmantojiet navigācijas režīmu, lai pārbaudītu navigāciju lapās un nodrošinātu labāku trešās puses attiecinājumu un galvenā pavediena noteikšanu."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, saņemot GET <PERSON>, var pilnīgi saglabāt kešatmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Kešatmiņā var saglabāt tikai lapas ar statusa kodu 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Pārlūkā Chrome tiks konstatēts mēģinājums izpildīt JavaScript kodu kešatmiņā."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek <PERSON>pra<PERSON>ts AppBanner, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Pilnīga sagla<PERSON><PERSON> ke<PERSON> ir at<PERSON>, jo tika saņemts saites darbības pārbaudes pieprasījums."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Pilnīgu saglab<PERSON><PERSON><PERSON> kešatmiņā atspējoja karodziņu iestatījumi. <PERSON> šajā ierīcē to iesp<PERSON><PERSON><PERSON> lo<PERSON>, atveriet lapu chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Komandrinda atspējoja pilnīgu saglab<PERSON><PERSON><PERSON> ke<PERSON>tmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "<PERSON>ln<PERSON><PERSON> sagla<PERSON><PERSON> ke<PERSON> ir at<PERSON>, jo atmiņ<PERSON> nav pietiekami daudz vietas."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Pilnīgu saglab<PERSON><PERSON><PERSON> kešatmiņā neatbalsta deleģētais elements."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "<PERSON><PERSON><PERSON>ādes atveidotājs atspējoja pilnīgu saglab<PERSON><PERSON><PERSON> ke<PERSON>tmiņā."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON><PERSON> nevar saglab<PERSON>t ke<PERSON>, jo tajā ir BroadcastChannel instance ar reģistrētiem uztvērējiem."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> ir galvene cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "<PERSON><PERSON><PERSON><PERSON>ņ<PERSON> tika mērķtiecīgi notīrīta."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "<PERSON><PERSON><PERSON> lapas dati tika noņemti no kešatmiņas, lai kešatmiņā varētu saglabāt citu lapu."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Lapas ar spraudņ<PERSON>m pašlaik nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne FileChooser API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne File System Access API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON>ts multivides ierī<PERSON><PERSON>, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, darb<PERSON><PERSON><PERSON><PERSON> multivides atskaņotājs."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek izmantota saskarne MediaSession API un kurās ir iestatīts atskaņošanas st<PERSON>, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek izmantota saskarne MediaSession API un kurās ir iestatīti darbību ap<PERSON>, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir atspējota ekrāna lasītāja dēļ."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON> a<PERSON>dar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nevar piln<PERSON>gi saglab<PERSON>t ke<PERSON>tmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne Serial API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebAuthetication API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebBluetooth API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebUSB API, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Pilnīga saglabā<PERSON>na kešatmiņ<PERSON> ir atspējota, jo <PERSON>, kur<PERSON> tiek izmantota direktīva `Cache-Control: no-store`, ir atspējoti s<PERSON>."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON> atsevišķs strādnis vai uzdevumu kopa, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Do<PERSON><PERSON> tika a<PERSON>, vēl pirms bija pabeigta tā ielāde."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rādīts lietotnes reklāmkarogs."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs Chrome paroļu pārvaldnieks."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, notika DOM attīrīšana."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs DOM attīrītāja skatītājs."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir at<PERSON>, jo pap<PERSON>šināju<PERSON> izmantoja ziņojumapmaiņas API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Paplašinājumiem ar ilgstošu savienojumu jāpārtrauc savienojums pirms pilnīgas saglabāšanas kešatmiņā."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> ar il<PERSON><PERSON><PERSON>, tika mēģināts sūt<PERSON>t ziņojumus uz i<PERSON>, kas pilnīgi saglabāti kešatmiņā."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Pilnīga saglab<PERSON><PERSON> kešatmiņ<PERSON> ir atspējota paplaš<PERSON><PERSON><PERSON><PERSON> dē<PERSON>."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika parād<PERSON>ts modāls lapas dialoglodzi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, veidlapas atkārtotas iesniegšanas vai http paroļu dialoglodziņš."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rād<PERSON>ta be<PERSON> lapa."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika rā<PERSON><PERSON><PERSON> josla par atmiņas trūkumu."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija atļ<PERSON><PERSON> pieprasījumi."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, bija aktīvs uznirstošo elementu bloķētājs."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> prom, tika parā<PERSON><PERSON><PERSON> d<PERSON> p<PERSON><PERSON> informāci<PERSON>."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Funk<PERSON>j<PERSON> <PERSON><PERSON><PERSON><PERSON>” šī lapa tika identificēta kā ļaunprātīga un tika bloķēts uznirstošais logs."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON> norit<PERSON>ja lapas pilnīga saglabā<PERSON>na ke<PERSON>miņ<PERSON>, tika aktivizēts pakalpojumu skripts."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Pilnīga saglabā<PERSON>na kešatmiņā ir atspējota dokumenta kļūdas dēļ."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek i<PERSON>ts elements FencedFrames, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "<PERSON><PERSON><PERSON> lapas dati tika noņemti no kešatmiņas, lai kešatmiņā varētu saglabāt citu lapu."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir piešķirta piekļ<PERSON> multivides straumei, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek <PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek i<PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir atvērts IndexedDB savienojums, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Pilnīga saglabā<PERSON> kešatmiņ<PERSON> ir atspējota IndexedDB notikuma dēļ."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Tika izmantotas nepiemērotas API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmi<PERSON>, kur<PERSON><PERSON> pap<PERSON><PERSON><PERSON><PERSON><PERSON> iepludina `JavaScript` kodu."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmi<PERSON>, kur<PERSON><PERSON> pap<PERSON><PERSON><PERSON><PERSON><PERSON> iepludina `StyleSheet`."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Iekš<PERSON>ja <PERSON>."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Pilnīga sagla<PERSON><PERSON> ke<PERSON> ir at<PERSON>, jo tika saņemts saites darbības pārbaudes pieprasījums."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tiek i<PERSON>ta tastatūras bloķēšana, pa<PERSON><PERSON>k nevar pilnīgi saglabā kešatmiņā."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON>pa tika aizvērta, vēl pirms bija pabeigta tās i<PERSON>."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, kuru galvenā resursa direktīva ir cache-control:no-cache, nevar pilnīgi saglabāt kešatmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, kuru galvenā resursa direktīva ir cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika pārtraukta pirms kešatmiņā pilnīgi saglabātas lapas atjauno<PERSON>nas."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Lapas dati tika noņemti no ke<PERSON><PERSON><PERSON>, jo aktīvajā tīkla savienojumā tika saņemts pārmērīgs daudzums datu. Pārlūkā Chrome tiek ierobežots datu daudzums, ko lapa var saņemt, kad tiek saglabāta kešatmiņā."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvi fetch() vai XHR notikumi, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "<PERSON><PERSON><PERSON> lapas dati vairs nav pilnīgi saglabāti ke<PERSON>mi<PERSON>, jo aktīvs tīkla savienojuma pieprasījums ietvēra novir<PERSON>."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "<PERSON><PERSON><PERSON> lapas dati tika noņemti no ke<PERSON>tmiņ<PERSON>, jo tīkla savienojums bija atvērts pārāk ilgi. Pārlūkā Chrome tiek ierobežots laiks, k<PERSON><PERSON> lapa var saņemt datus, kam<PERSON>r norit tās saglab<PERSON><PERSON>na kešatmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON>, kurām nav derīgas atbildes galvenes, nevar pilnīgi saglabāt kešatmiņ<PERSON>."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON>na notika citā ietvarā, nevis galvenajā ietvarā."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> notiek IndexedDB operācijas, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla <PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla i<PERSON>, p<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs tīkla <PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> ir aktīvs XHR tīkla piepra<PERSON>, p<PERSON><PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izman<PERSON> saskar<PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izman<PERSON>ta funkcija “Attēls attēlā”, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek <PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek rā<PERSON><PERSON><PERSON> d<PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "<PERSON>pa tika atvērta, <PERSON><PERSON><PERSON><PERSON><PERSON> metodi `window.open()`, un citā cilnē ir atsauce uz šo lapu, vai arī lapa tika atvērta logā."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Kešatmiņā pilnīgi saglabātas lapas atveidotājs a<PERSON>."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Kešatmiņā pilnīgi saglabātas lapas atveidotāja darbība tika pārtraukta."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika pieprasītas audio ierakstīšanas atļaujas, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepras<PERSON> at<PERSON><PERSON><PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepra<PERSON> atļaujas sinhronizācijai fonā vai <PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika pieprasīta MIDI ierīču atļauja, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> tika piepras<PERSON> paziņ<PERSON> atļ<PERSON><PERSON>, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tika piepras<PERSON>ta pie<PERSON> kr<PERSON>vei, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tika piepra<PERSON> video iera<PERSON><PERSON><PERSON><PERSON><PERSON> atļaujas, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var saglabāt tikai tādas lapas, kuru URL shēma ietver HTTP vai HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON><PERSON> noritēja lapas pilnīga saglabā<PERSON>na ke<PERSON>miņ<PERSON>, lapu pieprasīja pakalpojuma skrip<PERSON>."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Pakalpojumu skripts mēģināja nosūtīt rekvizītu `MessageEvent` lapai, kas tiek pilnīgi saglabāta kešatmiņā."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorker reģistrācija tika atcelta, kamēr noritēja lapas pilnīga saglabāšana kešatmiņā."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON><PERSON> lapas dati vairs nav pilnīgi saglabāti ke<PERSON>mi<PERSON>, jo tika aktivizēts pakalpojumu skripts."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Pārlūks Chrome tika <PERSON>, un kešatmiņā pilnīgi saglabātie ieraksti tika notīrīti."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tiek izman<PERSON>ta saskarne <PERSON>ker, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izman<PERSON> klase SpeechRecognizer, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne SpeechSynthesis, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "<PERSON><PERSON> iframe ietvarā tika sākta p<PERSON>, bet tā netika pabe<PERSON>ta."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, kuru apakšresursa direktīva ir cache-control:no-cache, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, kuru apakšresursa direktīva ir cache-control:no-store, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON><PERSON> pā<PERSON>nie<PERSON><PERSON> maksim<PERSON>s laiks lapas pilnīgai saglab<PERSON><PERSON><PERSON> kešatmiņā, un lapas derīguma termi<PERSON>."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Sākot pilnīgu sa<PERSON><PERSON><PERSON><PERSON>, lap<PERSON> iest<PERSON> noil<PERSON> (iespējams, to izraisīja ieil<PERSON>i lapas paslēpšanas notikumu apdarin<PERSON><PERSON><PERSON> darb<PERSON>)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "La<PERSON> galvenajā ietvarā ir ielādes at<PERSON><PERSON> a<PERSON>."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Lapas apa<PERSON>š<PERSON> ir ielādes at<PERSON> a<PERSON>."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tika mainīta lietotāja aģenta ignorēšanas galvene."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON>, kur<PERSON><PERSON> ir piešķirta piekļuves video vai audio ierakstīšanai, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebDatabase, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebHID, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebLocks, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebNfc, pa<PERSON><PERSON><PERSON> nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebOTPService, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebRTC, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantots starpniekserveris WebShare, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantots protokols WebSocket, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebTransport, nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON>, k<PERSON><PERSON><PERSON> tiek izmantota saskarne WebXR, pa<PERSON><PERSON>k nevar pilnīgi saglabāt kešatmiņā."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "<PERSON> atpakaļsaderību ar vecākām pārlūkprogrammām, varat pievienot URL shēmas “https:” un “http:” (pārlūkprogrammās, kas atbalsta atslēgvārdu “`'strict-dynamic'`”, š<PERSON>s shēmas tiks ignorētas)."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Direk<PERSON><PERSON>va<PERSON> “`disown-opener`” ir pā<PERSON><PERSON><PERSON><PERSON> atbalsts kopš politikas versijas CSP3. Tās vietā izmantojiet galveni Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Direktīvai “`referrer`” ir pā<PERSON>ra<PERSON>ts atbalsts kopš politikas versijas CSP2. Tās vietā izmantojiet galveni Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Direktīva<PERSON> “`reflected-xss`” ir pārt<PERSON><PERSON>ts atbalsts kopš politikas versijas CSP2. Tās vietā izmantojiet galveni X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Direktīvai “`base-uri`” nav iestatīta vērtība, tā<PERSON><PERSON><PERSON> var iepludināt `<base>` tagus, lai visiem relatīvajiem vietrāžiem URL (piemēram, skriptiem) iestatītu pamata URL, kas atbilst uzbrucēju kontrolētam domēnam. Ieteicams iestatīt direktīvas “`base-uri`” vērtību “`'none'`” vai “`'self'`”."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Ja nav nor<PERSON><PERSON><PERSON><PERSON> direktīvas “`object-src`” v<PERSON><PERSON><PERSON><PERSON>, var tikt ieplu<PERSON><PERSON> spraud<PERSON>, kas izpilda nedro<PERSON> skriptus. <PERSON>a varat, ieteicams iestatīt direktīvas “`object-src`” vērtību “`'none'`”."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Trūkst direktīvas “`script-src`”. <PERSON><PERSON><PERSON><PERSON><PERSON> var būt iespējams izpildīt nedro<PERSON>us skriptus."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "<PERSON>ai a<PERSON> semi<PERSON>? Šķiet, {keyword} ir direktīva, nevis atslēgvārds."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Vienreizējiem kodiem jā<PERSON><PERSON><PERSON> r<PERSON><PERSON> kopa base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Vienreizējiem kodiem jābūt vismaz astoņas r<PERSON>tz<PERSON> gariem."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Neizmantojiet neformatētas URL shēmas ({keyword}) šajā direktīvā. Izmantojot neformatētas URL shēmas, skripti var tikt iegūti no nedroša domēna."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Neizmantojiet neformatētas aizstājējzīmes ({keyword}) šajā direktīvā. Izmantojot neformatētas aizstājējzīmes, skripti var tikt iegūti no nedroša domēna."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galamērķa konfigur<PERSON><PERSON><PERSON> ir izmantota tikai direktīva “report-to”. Šo direktīvu atbalsta tikai uz Chromium pamata izstrādātas pārlūkprogrammas, tāpēc ieteicams izmantot arī direktīvu “`report-uri`”."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nevienā SDP nav konfigurēts ziņošanas galamērķis. Tāpēc ir grūti ilgtermi<PERSON> uzturēt SDP un uzraudzīt, vai nerodas darbības traucējumi."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Saimniekdatoru atļaušanas sarakstus bieži var apiet. Ieteicams izmantot SDP (CSP) vienreizējos kodus (“nonces”) vai jaucējvērtības (“hashes”) kopā ar direktīvu “`'strict-dynamic'`”, ja ne<PERSON>."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Nezināma SDP direktīva."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} ir ne<PERSON><PERSON>gs atslēgvārds."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Atslēgvārds “`'unsafe-inline'`” <PERSON><PERSON><PERSON>t nedro<PERSON>us lapā ievietotus skriptus un notikumu apdarinātājus. Ieteicams atļaut skriptus pa vienam, izman<PERSON><PERSON>t <PERSON> (CSP) vienreizējos kodus (“nonces”) vai jaucējvērtības (“hashes”)."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "<PERSON> atpakaļsaderību ar vecākām pārlūkprogrammām, varat pievienot atslēgvārdu “`'unsafe-inline'`” (pārlūkprogrammās, kas atbalsta vienreizējos kodus (“nonces”) vai jaucējvērtības (“hashes”), šis atslēgvārds tiks ignorēts)."}, "core/lib/deprecation-description.js | feature": {"message": "Plašāku informāciju skatiet funkcijas statusa lapā."}, "core/lib/deprecation-description.js | milestone": {"message": "<PERSON><PERSON><PERSON> stā<PERSON> spēkā galvenajā versijā {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Tiek izmantota novecojusi funkcija"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Galvenei “authorization” nevar<PERSON><PERSON> i<PERSON> a<PERSON> (*), citas izcelsmes resursu kopīgošanas mehānismam apstrādājot “`Access-Control-Allow-Headers`”."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "<PERSON> integrāciju, ir j<PERSON><PERSON><PERSON><PERSON> “`disableRemotePlayback`”, nevis atlasītājs “`-internal-media-controls-overlay-cast-button`”."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Resu<PERSON>u <PERSON>, kuru vietr<PERSON>žos URL bija ietvertas gan noņemtas baltstarpas rakstz<PERSON> (`(n|r|t)`), gan rakstz<PERSON><PERSON> “mazāk nekā” (`<`), ir bloķēti. <PERSON> iel<PERSON>dētu šos resursus, <PERSON><PERSON><PERSON><PERSON>, noņemiet jaunas rindas rakstzīmes un kodējiet rakstzīmes “mazāk nekā” tādās vietās kā elementu atribūtu vērtības."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Metode “`chrome.loadTimes()`” vairs netiek i<PERSON>, tās vietā izmantojiet standartizētu API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Metode “`chrome.loadTimes()`” vairs netiek i<PERSON>, tās vietā izmantojiet standartizētu API: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Metode “`chrome.loadTimes()`” vairs netiek i<PERSON>, tās vietā izmantojiet standartizētu API: “`nextHopProtocol`” saskarnē Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> ir <PERSON>`(0|r|n)`”, t<PERSON><PERSON>, nevis <PERSON>."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Tās pašas izcelsmes politikas ierobežoju<PERSON>, iestatot “`document.domain`”, vairs netiek i<PERSON>, un šī iespēja tiks atspējota pēc noklusējuma. Šis darbības pārtraukšanas brīdinājums ir saistīts ar citas izcelsmes piekļuvi, kas tika iespējota, iestatot “`document.domain`”."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Funkcijas window.alert aktivizēšanai no citas izcelsmes iframe ietvariem ir pārtraukts atbalsts, un nākotnē šī iespēja tiks noņemta."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Funkcijas window.confirm aktiviz<PERSON>šanai no citas izcelsmes iframe ietvariem ir pārtraukts atbalsts, un nākotnē šī iespēja tiks noņemta."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "DOM mutācijas notikumi, tostarp `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` un `DOMCharacterDataModified`, ir noveco<PERSON><PERSON><PERSON> (https://w3c.github.io/uievents/#legacy-event-types) un tiks noņemti. To vietā izmantojiet `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Datu atbalsts: vietrāžiem URL ir pārtraukts atbalsts SVG elementā <use>, un nākotnē tie tiks noņemti."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Tā<PERSON> pašas izcelsmes politikas ierobežoju<PERSON> noņ<PERSON>, iestatot “`document.domain`”, vairs netiek i<PERSON>, un šī iespēja tiks atspējota pēc noklusējuma. Lai turpinātu izmantot š<PERSON>, <PERSON><PERSON><PERSON><PERSON>, atsakieties no aģentu grupēšanas pēc izcelsmes, nos<PERSON><PERSON><PERSON> galveni “`Origin-Agent-Cluster: ?0`” kopā ar HTTP atbildi dokumentam un ietvariem. Plašāku informāciju skatiet vietnē https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> “`Expect-CT`” vairs netiek atbalstīta un tiks noņemta. Pārlūkā Chrome visiem sertifikātiem, kas izdoti pēc 2018. gada 30. aprīļa un publiski atzīti par uzticamiem, ir jā<PERSON><PERSON>to sertifik<PERSON><PERSON> pā<PERSON>."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Metodes “`getCurrentPosition()`” un “`watchPosition()`” vairs nedarbojas nedrošos avotos. Lai izman<PERSON>tu š<PERSON>, apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Metodes “`getCurrentPosition()`” un “`watchPosition()`” vairs netiek izmantotas nedrošos avotos. Lai izmantotu šo <PERSON>, apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "Metode “`getUserMedia()`” vairs nedarbojas nedrošiem avotiem. <PERSON> i<PERSON> š<PERSON>, apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Objekts “`RTCPeerConnectionIceErrorEvent.hostCandidate`” vairs netiek atbalstīts. Tā vietā izmantojiet “`RTCPeerConnectionIceErrorEvent.address`” vai “`RTCPeerConnectionIceErrorEvent.port`”."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Tirgotāja izcelsme un patvaļīgi noteikti dati no pakalpojumu skripta notikuma “`canmakepayment`” ir novecojuši un tiks noņemti: `topOrigin`, `paymentRequestOrigin`, `methodData` un `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Vietne pieprasīja apakšresursu no tīkla, kam varēja piekļūt tikai lietotāju priviliģētā tīkla stāvokļa dēļ. Šie pieprasījumi saista publiski nepieejamas ierīces un serverus ar internetu, palielinot starpvietņu pieprasījuma viltošanas uzbrukumu un/vai informācijas noplūdes risku. <PERSON> riskus, Chrome pārtrauc atbalstīt pieprasījumus publiski nepieejamiem apakšresursiem, ja tie aktivizēti nedrošā vidē, un sāks tos bloķēt."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "`InterestGroups` lauks `dailyUpdateUrl`, kas tiek iestatīts kā vērtība metodei `joinAdInterestGroup()`, ir pārdēvēts par `updateUrl`, lai precīzāk atspoguļotu tā darbību."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "CSS nevar ielād<PERSON>t no “`file:`” vietrāžiem URL, ja tie nebeidzas ar faila pap<PERSON>jumu “`.css`”."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Specifikācijas izmaiņu dēļ metodes “`SourceBuffer.abort()`” <PERSON><PERSON><PERSON>, lai priekšlai<PERSON> pā<PERSON>ra<PERSON>tu “`remove()`” asinhrono diapazona no<PERSON>, ir novecojusi. Tiek plānots pārtraukt tās atbalstu. Tās vietā ieteicams izmantot notikuma “`updateend`” uztveršanu. Metode “`abort()`” ir paredzēta tikai asinhronas multivides līdzekļu pievienošanas priekšlaicīgai pārtraukšanai vai parsētāja statusa atiestatīšanai."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Specifikācijas izmaiņu dēļ vairs netiek atbalstīta “`MediaSource.duration`” iestatīšana zem lielākā parādīšanas laikspiedola buferī ievietotiem kodētiem ietvariem. <PERSON><PERSON><PERSON><PERSON><PERSON>, buferī ievietota multivides satura netiešas noņemšanas atbalsts tiks pārtraukts. Tās vietā izmantojiet tiešu metodi “`remove(newDuration, oldDuration)`” visiem objektiem “`sourceBuffers`”, ja tiek izpildīts nosacījums “`newDuration < oldDuration`”."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI vaicā<PERSON>ļauju, pat ja objekt<PERSON> “`MIDIOptions`” nav norād<PERSON>ta vērtība “sysex”."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "<PERSON><PERSON><PERSON><PERSON>, nestandart<PERSON><PERSON><PERSON><PERSON><PERSON> atribūtam `shadowroot` ir p<PERSON><PERSON><PERSON><PERSON> atbalsts, un tas *vairs nedarbosies* versijā M119. Tā vietā i<PERSON> jauno, standartizēto atribūtu `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Paziņojumu API vairs nevar izmantot no nedrošiem avotiem. Apsveriet iespēju lietojumprogrammā izmantot drošu avotu, piemēram, HTTPS. Plašāku informāciju skatiet vietnē https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "No citas izcelsmes iframe ietvara vairs nevar pieprasīt atļauju saskarnei Paziņojumu API. Apsveriet iespēju pieprasīt atļauju no augšējā līmeņa ietvara vai atvērt jaunu logu."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Metodes createImageBitmap opcijai `imageOrientation: 'none'` ir p<PERSON><PERSON><PERSON><PERSON>ts atbalsts. Tās vietā izmantojiet metodi createImageBitmap ar opciju \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> partneris sa<PERSON> (D)TLS versiju. <PERSON><PERSON><PERSON><PERSON>, sazin<PERSON>ies ar partneri, lai to nov<PERSON><PERSON><PERSON>."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Tagos “img”, “video” un “canvas” nor<PERSON><PERSON>t vērtību “`overflow: visible`”, vizu<PERSON>ls saturs var tikt rādīts ārpus elementa robežām. Skatiet vietni https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "Saskarne `paymentManager.instruments` vairs netiek atbalstīta. Tās vietā maksājumu apdarinātājiem izmantojiet instalēšanu tieši laikā."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Ar j<PERSON><PERSON> `PaymentRequest` izsaukumu tika apieta Content-Security-Policy (satura drošības politikas — SDP) direktīva “`connect-src`”. Šādai apiešanai ir pārtraukts atbalsts. SDP direktīvai “`connect-src`” pievienojiet maksājuma veida identifikatoru no `PaymentRequest` API (laukā `supportedMethods`)."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Saskarnei `StorageType.persistent` ir p<PERSON><PERSON><PERSON><PERSON><PERSON> atbalsts. Tās vietā izmantojiet standartizēto elementu “`navigator.storage`”."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Elements `<source src>` ar galveno elementu `<picture>` nav derīgs, tād<PERSON><PERSON> tiks ignorēts. T<PERSON> vietā, l<PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Metode webkitCancelAnimationFrame ir piesaistīta nod<PERSON>tājam. Tās vietā izmantojiet standarta metodi cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Metode webkitRequestAnimationFrame ir piesaistīta nod<PERSON>tāja<PERSON>. Tās vietā izmantojiet standarta metodi requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Saskarnei HTMLVideoElement.webkitDisplayingFullscreen ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Saskarnei HTMLVideoElement.webkitEnterFullScreen() ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Saskarnei HTMLVideoElement.webkitEnterFullscreen() ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Saskarnei HTMLVideoElement.webkitExitFullScreen() ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Saskarnei HTMLVideoElement.webkitExitFullscreen() ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Saskarnei HTMLVideoElement.webkitSupportsFullscreen ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Plānojam pārtraukt atbalstu saskarnei API `chrome.privacy.websites.privacySandboxEnabled`, kaut gan atpakaļsaderības nolūkiem tā būs aktīva līdz laidiena M113 publicēšanai. Tās vietā izmantojiet `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` un `chrome.privacy.websites.adMeasurementEnabled`. Skatiet vietni https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled"}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ierobežojums “`DtlsSrtpKeyAgreement`” ir noņemts. J<PERSON><PERSON> šim ierobe<PERSON>ojumam esat noteicis vērtību “`false`”, un tas tiek interpretēts kā mēģinājums izmantot noņemto metodi “`SDES key negotiation`”. Šī funkcionalitāte ir noņemta. T<PERSON>s viet<PERSON> izmantojiet pakalpo<PERSON>mu, kas atbalsta metodi “`DTLS key negotiation`”."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ierobežojums “`DtlsSrtpKeyAgreement`” ir noņemts. J<PERSON><PERSON> šim ierobežojumam esat noteicis vērtību “`true`”, taču tas nedarbojās. Skaidrības labad varat noņemt šo ierobežojumu."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Uz izsaukumu balstītajai metodei getStats() ir pārtraukts atbalsts, un tā tiks noņemta. Tās vietā izmantojiet specifikācijām atbilstošo metodi getStats()."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Saskarnei Range.expand() ir pārtraukts atbalsts. Tās vietā izmantojiet saskarni Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Apakš<PERSON><PERSON><PERSON>, kuru vietrāžos URL ir iegulti akreditācijas dati (piem., `**********************/`), ir bloķēti."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Opcija “`rtcpMuxPolicy`” vairs nav pieejama un tiks noņemta."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "<PERSON>b<PERSON><PERSON><PERSON> “`SharedArrayBuffer`” būs <PERSON> i<PERSON> no citas izcelsmes resursiem. Plašāku informāciju skatiet vietnē https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Metode “`speechSynthesis.speak()`” bez lietotāja aktivizācijas vairs netiek izmantota un tiks noņemta."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON> tur<PERSON> “`SharedArrayBuffer`”, pap<PERSON><PERSON><PERSON><PERSON><PERSON>mie<PERSON> ir jāizvēlas izolācija no citas izcelsmes resursiem. Plašāku informāciju skatiet vietnē https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Saskarne Web SQL vairs netiek izmantota. Izmantojiet SQLite WebAssembly vai Indexed Database."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Atļaujas deskriptoram `window-placement` ir p<PERSON><PERSON><PERSON><PERSON>ts atbalsts. Tā vietā izmantojiet deskriptoru `window-management`. Plašāku informāciju skatiet vietnē https://bit.ly/window-placement-rename"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Atļauju politikai `window-placement` ir pā<PERSON><PERSON><PERSON>ts atbalsts. Tās vietā izmantojiet politiku `window-management`. Plašāku informāciju skatiet vietnē https://bit.ly/window-placement-rename"}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "Saskarnē “`XMLHttpRequest`” atbildes json failos netiek atbalstīts UTF-16."}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhroniem saskar<PERSON> `XMLHttpRequest` pieprasījumiem galvenajā pavedienā ir pārtraukts atbalsts, jo tie negatīvi ietekmēja galalietotāju pieredzi. Plašāku informāciju skatiet vietnē https://xhr.spec.whatwg.org/"}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Metode “`supportsSession()`” vairs netiek atbalstīta. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> metodi “`isSessionSupported()`” un pārbaudiet saņemto Būla vērtību."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Galvenā pavediena bloķēšanas laiks"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Kešatmiņas TTL vērtība"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elements"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON>gi <PERSON>i"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nosa<PERSON>ms"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Resursa <PERSON>ē<PERSON>"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Resursu veids"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Izmērs"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Avots"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failu lie<PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenciālie <PERSON>"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenciālie <PERSON>"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenciālais ietaupījums: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Tika atrasts viens elements}zero{Tika atrasti # elementi}one{Tika atrasts # elements}other{Tika atrasti # elementi}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenciālais ietaupījums: {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokuments"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON><PERSON><PERSON>s satura atveidojums"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Fonts"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaction to Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Augsts"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Zems"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maks. potenci<PERSON>lā First Input Delay"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multivide"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Cits"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Citi resursi"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> lapa"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> puse"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Kopā"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Nevarēja reģistrēt lapas ielādes trasējumu. <PERSON><PERSON>, vēlreiz palaidiet Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kļūdotāja protokola savienojumu, r<PERSON><PERSON><PERSON>."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Lapas ielādes laikā pārlūkprogramma Chrome nav apkopojusi nevienu ekrānuzņēmumu. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ka saturs ir redzams lapā, un pēc tam mēģiniet atkārtoti palaist Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS serveri nevarēja atrast nor<PERSON><PERSON><PERSON>."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Nepieciešamajam parametra “{artifactName}” vācējam radā<PERSON> k<PERSON>: {errorMessage}."}, "core/lib/lh-error.js | internalChromeError": {"message": "<PERSON><PERSON><PERSON><PERSON>ja Chrome kļūda. <PERSON><PERSON><PERSON><PERSON>, restartējiet Chrome un mēģiniet atkārtoti palaist Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON>epie<PERSON><PERSON><PERSON><PERSON> parametra “{artifactName}” vā<PERSON><PERSON><PERSON><PERSON>."}, "core/lib/lh-error.js | noFcp": {"message": "Lapā netika atveidots saturs. Mēģiniet vē<PERSON><PERSON><PERSON>, ielādes laikā paturot pārlūkprogrammas logu priekšplānā. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Lapā netika rādīts saturs, kas tiek klasificēts kā lielākais satura atveidojums (LCP — Largest Contentful Paint). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai lapā būtu derīgs LCP elements, un pēc tam mēģiniet vēlreiz. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Norādītā lapa nav valodā HTML (tā tiek rādīta MIME veidā {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Šī Chrome versija ir pārāk veca, lai at<PERSON><PERSON><PERSON><PERSON> {featureName}. <PERSON> skatītu visus rezult<PERSON>, i<PERSON><PERSON><PERSON><PERSON> jaun<PERSON>ku versiju."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nevarēja droši ielādēt jūsu pieprasīto lapu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka testējat pareizo URL un serveris pareizi reaģē uz visiem pieprasījumiem."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nevarēja droši i<PERSON>ādēt jūsu pieprasīto URL, jo lapa pārstāja reaģēt."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "<PERSON><PERSON><PERSON>jam URL nav derīga drošības sertifikāta. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Pārlūkprogramma Chrome neļāva ielādēt lapu ar iespiestu reklāmu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nevarēja uzticami ielādēt jūsu pieprasīto lapu. <PERSON>dr<PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem. (Detalizēta informācija: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nevarēja uzticami ielādēt jūsu pieprasīto lapu. P<PERSON><PERSON><PERSON><PERSON>eties, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem. (Statusa kods: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Lapas ielādei bija nepieciešams pārāk ilgs laiks. <PERSON><PERSON><PERSON><PERSON>, izmantojiet pārskatā sniegtās iespējas, lai samazin<PERSON>tu lapas ielādes laiku, un pēc tam mēģiniet atkārtoti palaist Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Gaidot DevTools protokola atbildi, ir p<PERSON><PERSON><PERSON><PERSON><PERSON> atv<PERSON>l<PERSON><PERSON> laiks. (Veids: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Resursu satura izg<PERSON><PERSON> ir nepieciešams ilgāks laiks, nek<PERSON>"}, "core/lib/lh-error.js | urlInvalid": {"message": "Šķiet, ka jūsu norādītais URL nav derīgs."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Lighthouse nevarēja uzticami ielādēt jūsu pieprasīto lapu. P<PERSON><PERSON><PERSON>cinieties, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem. (Statusa kods: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Lapas MIME veids ir XHTML: rīkā Lighthouse šis dokumenta veids netiek atbalstīts."}, "core/user-flow.js | defaultFlowName": {"message": "Lie<PERSON><PERSON><PERSON><PERSON> p<PERSON> ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navig<PERSON><PERSON><PERSON> pā<PERSON> ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Momentuz<PERSON><PERSON><PERSON><PERSON> pā<PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Lai<PERSON> posma pārskats ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Visi pārskati"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Pie<PERSON>amī<PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Veiktspēja"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresīvā tīmekļa lietotne"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "MPO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Datoriem"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Par Lighthouse plūsmas p<PERSON>rskatu"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON>r pl<PERSON>m"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "<PERSON>zman<PERSON>jiet navigācijas pārskatus, lai…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>, lai…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laika posma pā<PERSON>, lai…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Iegūstiet Lighthouse veiktspējas rādītāju."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON><PERSON> tādus lapas ielādes veiktspējas rādītā<PERSON>s kā Largest Contentful Paint un ātruma indekss."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Izvērtējiet progresīvo tīmekļa lietotņu iespējas."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Atrodiet pieejamības problēmas vienas lapas lietojumprogrammās vai sarežģītās veidlapās."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Izvērtējiet paraugprakses principus izvēlnēm un lietotāja saskarnes elementiem, kas atkarīgi no mijiedarbības."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Izmēriet izkārtojuma nobīdes un JavaScript izpildes laiku vairāku mijiedarbību virknei."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Atklājiet veiktspējas iespējas, lai u<PERSON><PERSON><PERSON>u pieredzi ilgi atvērtās lapās un vienas lapas lietojumprogrammās."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informatīva pārbaude}zero{{numInformative} informatīvu pārbaužu}one{{numInformative} informatīva pārbaude}other{{numInformative} informatīvas pārbaudes}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON> i<PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Navigācijas pārskatos tiek analizēta vienas lapas iel<PERSON>de, tie<PERSON><PERSON> tāpat kā sākotnējos Lighthouse pārskatos."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigā<PERSON><PERSON> p<PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navig<PERSON><PERSON><PERSON> pārskats}zero{{numNavigation} navigācijas pārskatu}one{{numNavigation} navigā<PERSON>jas pārskats}other{{numNavigation} navig<PERSON><PERSON>jas pārskati}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} izpild<PERSON><PERSON> pārbaude}zero{{numPassableAudits} izpild<PERSON><PERSON> pārbauž<PERSON>}one{{numPassableAudits} izpild<PERSON><PERSON> pārbaude}other{{numPassableAudits} izpild<PERSON><PERSON> pārbaudes}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {numPassed} pārbaude}zero{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {numPassed} pārbaudes}one{<PERSON><PERSON>pi<PERSON><PERSON>ta {numPassed} pārbaude}other{<PERSON><PERSON>pi<PERSON><PERSON><PERSON> {numPassed} pārbaudes}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON> līmenis"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Lapas tvertais statuss"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Momentuzņēmumu pārskatos tiek analizēts konkrēts lapas stāvoklis (parasti pēc lietotāju veiktas mijiedarbības)."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Momentuzņē<PERSON><PERSON> p<PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} momentuzņē<PERSON>a pārskats}zero{{numSnapshot} momentuzņēmumu pārskatu}one{{numSnapshot} momentuzņēmumu pārskats}other{{numSnapshot} momentuzņēmumu pārskati}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Laika posma pārskatos tiek analizēti jebkādi laika periodi, kas parasti ietver lietotāja mi<PERSON>."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Laika posma pā<PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} laika posma pārskats}zero{{numTimespan} laika posmu pārskatu}one{{numTimespan} laika posmu pārskats}other{{numTimespan} laika posmu pārskati}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse lietotāju plū<PERSON> p<PERSON>"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Animētam saturam izmantojiet komponentu [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), lai samazinātu centrālā procesora lietojumu, kamēr saturs nav redzams ekrānā."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Apsveriet iespēju attēlot visus [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) komponentus WebP formātos un norādīt piemērotu atkāpšanās formātu citām pārlūkprogrammām. [Uzziniet vairāk](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai i<PERSON>t tagu [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites), lai attēlu ielāde tiktu automātiski atlikta. [Uzziniet vairāk](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Izmantojiet tādus rīkus kā [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), lai [servera pusē renderētu AMP izkārtojumus](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Skatiet [AMP dokumentāciju](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), lai p<PERSON><PERSON><PERSON>, ka visi stili tiek at<PERSON>ti."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponents [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) atbalsta atribūtu [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), lai varētu nor<PERSON>, kurus attēlu līdzekļus izmantot atkarībā no ekrāna izmēra. [Uzziniet vairāk](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "<PERSON>a tiek <PERSON>ti <PERSON>ot<PERSON>, apsveriet iespēju izmantot virtuālo r<PERSON> ar komponentu izstrādes komplektu (Component Dev Kit — CDK). [Uzziniet vairāk](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Lietojiet [koda dalīšanu marš<PERSON>ta līmenī](https://web.dev/route-level-code-splitting-in-angular/), lai samazinātu JavaScript komplektu lielumu. Apsveriet arī iespēju iepriekš saglabāt līd<PERSON> ke<PERSON>tmiņ<PERSON>, i<PERSON><PERSON><PERSON><PERSON> [Angular pakalpojumu skriptu](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> Angular CLI, būvējumi jāģenerē produkcijas režīmā. [Uzziniet vairāk](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ular CLI, iekļaujiet produkcijas būvējumā avota kartes, lai pā<PERSON> komplektus. [Uzziniet vairāk](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai pa<PERSON><PERSON><PERSON><PERSON> navigāciju. [Uzziniet vairāk](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "<PERSON> pārvaldītu attēlu <PERSON>, varat izmantot utilītprogrammu `BreakpointObserver`, kas ir pieejama komponentu izstrādes komplektā (Component Dev Kit — CDK). [Uzziniet vairāk](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Ieteicams augšupielādēt GIF failu pakalpojumā, kuru varē<PERSON>, lai iegultu GIF failu kā HTML5 videoklipu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Kad motī<PERSON> definējat pie<PERSON>us fontus, norā<PERSON>t fragmentu `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Mēģiniet savā vietnē konfigurēt [attēlu stila maiņu uz attēlu formātu WebP](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instalējiet [Drupal moduli](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), ar ko var veikt attēlu atlikto ielādi. Šādi moduļi sniedz iespēju atlikt jebkādu ārpus ekrāna esošu attēlu ielādi, lai uzlabotu veiktspēju."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Apsveriet iespēju izmantot moduli, lai i<PERSON><PERSON><PERSON><PERSON> būtisku CSS un JavaScript kodu, un izmantot atribūtu “defer” mazāk svarīgam CSS vai JavaScript kodam."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, moduļi un servera specifikācijas ietekmē servera atbildes laiku. Ieteicams atrast optimizētu motīvu, rūp<PERSON>gi izvēlēties optimizācijas moduli un/vai jaunināt serveri. Mitināšanas serveros ir j<PERSON><PERSON>vieš PHP operācijas koda saglabāšana kešatmiņā, atmiņas saglab<PERSON> ke<PERSON>miņ<PERSON>, lai samazinātu datu bāzes vaicājumu apstrādei nepiecie<PERSON> laiku, pie<PERSON><PERSON><PERSON>, <PERSON><PERSON> vai <PERSON>ched, kā arī optimizēta lietojumprogrammas loģika, lai varētu ātrāk sagatavot lapas."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Ieteicams izmantot [adaptīvo attēlu stilus](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), lai samazinātu lapā ielādēto attēlu lielumu. Ja saskarni Views izmantojat, lai lapā rādītu vairākus satura elementus, ieteicams ieviest lapdali, lai ierobežotu attiecīgajā lapā redzamo satura elementu skaitu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Lapā Administration (Administrācija) » Configuration (Konfigurācija) » Development (Izstrāde) jābūt iespējotai funkcijai “Aggregate CSS files” (Apkopot CSS failus).  <PERSON> tiktu at<PERSON> l<PERSON>, j<PERSON><PERSON> Drupal vietnē jāizmanto Drupal 10.1 vai jaunāka versija."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Lapā Administration (Administrācija) » Configuration (Konfigurācija) » Development (Izstrāde) jābūt iespējotai funkcijai “Aggregate JavaScript files” (Apkopot JavaScript failus).  <PERSON> tiktu at<PERSON><PERSON><PERSON> l<PERSON>, j<PERSON><PERSON> Drupal vietnē jāizmanto Drupal 10.1 vai jaunāka versija."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Ieteicams noņemt neizmantotās CSS kārtulas un pievienot tikai vajadzīgās Drupal bibliotēkas atbilstošajām lapām vai komponentiem lapā. Lai iegūtu detalizētu informāciju, skatiet [Drupal dokumentācijas saiti](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Lai identificētu pievienotās bibliotēkas, kas pievieno lieku CSS kodu, mēģiniet izpildīt [koda pārklājumu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome DevTools. Attiecīgo motīvu/moduli varat identificēt stilu lapas vietrādī URL, kad Drupal vietnē ir atspējota CSS apkopošana. Meklējiet motīvus/moduļus, kuriem sarakstā ir daudz stilu lapu ar daudz sarkanām atzīmēm koda pārklājumā. Motīvam/modulim ir jāievieto rindā stilu lapu tikai tad, ja tā faktiski tiek izmantota tīmekļa lapā."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Ieteicams noņemt neizmantotos JavaScript līdzekļus un pievienot tikai vajadzīgās Drupal bibliotēkas atbilstošajām lapām vai komponentiem lapā. Lai iegūtu detalizētu informāciju, skatiet [Drupal dokumentācijas saiti](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Lai identificētu pievienotās bibliotēkas, kas pievieno lieku JavaScript kodu, mēģiniet izpildīt [koda pārklājuma analīzi](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome izstrādātāju rīkus. Motīvu/moduli, kas rada problēmu, varat identificēt skripta vietrādī URL, kad Drupal vietnē ir atspējota JavaScript apkopošana. Meklējiet motīvus/moduļus, kuru sarakstā ir daudz skriptu ar daudzām sarkanām atzīmēm koda pārklājuma analīzē. Motīvam/modulim ir jāievieto skripts rindā tikai tad, ja tas patiešām tiek izmantots lapā."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "La<PERSON><PERSON> “Administrā<PERSON><PERSON> » Konfigurā<PERSON><PERSON> » Izstr<PERSON><PERSON>” norādiet iestatījumu “Pārlūka un starpniekservera kešatmiņas maksimālais vecums”. Uzziniet par [Drupal kešatmiņu un veiktspējas optimizēšanu](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Ieteicams izmantot [moduli](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), kas automātiski optimizē un samazina vietnē augšupielādēto attēlu lielumu, vienlaikus saglabājot kvalitāti. Kā arī jāizmanto iebūvētie [adaptīvo attēlu stili](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), kas ir sniegti programmatūrā Drupal (pieejami programmatūrā Drupal 8 un jaunākās versijās) visiem vietnē renderētajiem attēliem."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Varat pievienot norādes par iepriekšējas pieslēgšanas vai DNS priekšieneses resursiem, instalējot un konfigurējot [moduli](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), kas nodro<PERSON>ina lietotāja aģenta resursu norādes."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [adaptīvo attēlu stili](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), kas ir sniegti programmatūrā Drupal (pieejami programmatūrā Drupal 8 un jaunākās versijās). Lietojiet adaptīvo attēlu stilus, kad renderējat attēlu laukus skatīšanas režīmos, skatos vai attēlos, kas aug<PERSON>, izmantojot WYSIWYG redaktoru."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Izmantojiet [E<PERSON> Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Optimize Fonts`, lai automātiski izmantotu CSS funkciju `font-display` un nodrošinātu, ka tīmekļa fontu ielādes laikā teksts ir redzams lietotājiem."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Next-Gen Formats`, lai konvertētu attēlus uz WebP formātu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "<PERSON>zmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `<PERSON><PERSON>`, lai atliktu ekrānā pagaidām neredzamo attēlu ielādi, kamēr tie nav nepiecie<PERSON>mi."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opcijas `Critical CSS` un `Script Delay`, lai atliktu visu nesvarīgo JS/CSS kodu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "<PERSON>zmantojiet [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), lai savu saturu saglab<PERSON>tu kešatmiņā mūsu vispasaules tīklā, tā<PERSON><PERSON><PERSON><PERSON><PERSON> uzlabojot laiku līdz pirmajam baitam."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Minify CSS`, lai automātiski samazinātu CSS, tād<PERSON><PERSON><PERSON><PERSON> samazinot tīkla lietderī<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Minify Javascript`, lai automātiski samazinātu JS, tā<PERSON><PERSON><PERSON><PERSON><PERSON> samazinot tīkla lietderī<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Remove Unused CSS`, lai novērstu šo problēmu. Tiks identificētas CSS klases, kas faktiski tiek lietotas katrā vietnes lapā, un tiks noņemtas citas klases, lai samazinātu faila lielumu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Efficient Static Cache Policy`, lai kešatmiņas galvenē iestatītu statiskajiem līdzekļiem ieteicamās vērtības."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Izmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Next-Gen Formats`, lai konvertētu attēlus uz WebP formātu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "<PERSON>zmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opciju `Pre-Connect Origins`, lai automātiski pievienotu “`preconnect`” resursu nor<PERSON>, tādēj<PERSON><PERSON> agri izveidojot savienojumus ar svarīgiem trešās puses avotiem."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "<PERSON>zmantojiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet opcijas `Preload Fonts` un `Preload Background Images`, lai pievienotu “`preload`” saites un piešķirtu prioritāti to resursu <PERSON>, kas pašreiz lapas ielādes laikā tiek pieprasīti vēlāk."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "<PERSON>zman<PERSON>jiet [Ezoic Leap](https://pubdash.ezoic.com/speed) un iespējojiet `Resize Images`, lai mainītu attēlu izmērus atbilstoši ierīcei, tā<PERSON><PERSON><PERSON><PERSON><PERSON> sama<PERSON>ot tīkla lietderīgo <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Lai automātiski optimizētu attēlu formātu, i<PERSON><PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image`, nevis `<img>`. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "<PERSON> automātiski atlik<PERSON> att<PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image`, nevis `<img>`. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Izmantojiet komponentu `gatsby-plugin-image` un iestatiet elementa `loading` vērtību `eager`. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Izmantojiet `Gatsby Script API`, lai atliktu mazāk svarīgu trešās puses skriptu ielādi. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `PurgeCSS` `Gatsby` spra<PERSON><PERSON>, lai noņemtu neizmantotas stilu lapu kārtulas. [Uzziniet vairāk](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "<PERSON> note<PERSON>tu neiz<PERSON>tu JavaScript kodu, izmantojiet `Webpack Bundle Analyzer`. [Uzziniet vairāk](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Konfigurējiet nema<PERSON>īgu līdzek<PERSON> saglab<PERSON> ke<PERSON>tmi<PERSON>. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "<PERSON>u att<PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image`, nevis `<img>`. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Lai iestatītu atbilstošās elementa `sizes` v<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image`. [Uzziniet vairāk](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Ieteicams augšupielādēt GIF failu pakalpojumā, kuru varē<PERSON>, lai iegultu GIF failu kā HTML5 videoklipu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Ieteicams izmantot [spraudni](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) vai paka<PERSON>, kur<PERSON> jūsu augšupielādētie attēli tiks automātiski pārveidoti optimālos formātos."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instalējiet [atliktās ielā<PERSON> spraudni](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), kas sniedz iespēju atlikt ārpus ekrāna esošu attēlu ielādi, vai mainiet veidni uz tādu, kur<PERSON> šī funkcionalitāte tiek nodrošināta. Sākot ar versiju Joomla 4.0, visiem jaunajiem attēliem tiks [automātiski](https://github.com/joomla/joomla-cms/pull/30748) pievienots atribūts `loading` no kodola."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "<PERSON>r <PERSON><PERSON><PERSON><PERSON> spraud<PERSON>, kas var palīdz<PERSON>t [iekļ<PERSON> būtiskus līdzekļus](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) vai [atlikt mazāk svarīgus resursus](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Ņemiet vērā, ka šo spraudņu nodrošinātā optimizācija var traucēt funkciju darbībai jūsu veidnēs vai spraudņ<PERSON>, tādē<PERSON> tie būs rūpīgi jāpārbauda."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, paplašinājumu un servera specifikācijas ietekmē servera atbildes laiku. Ieteicams atrast optimizētu veidni, rūpīgi izvēlēties optimizācijas paplašinājumu un/vai jaunināt serveri."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Ieteicams rādīt fragmentus rakstu kategorijās (<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> saiti “<PERSON><PERSON><PERSON> vairāk”), sama<PERSON><PERSON>t attiec<PERSON>gaj<PERSON> lapā rād<PERSON><PERSON> rakstu skaitu, sadal<PERSON>t garas ziņas vairākās lapās vai izmantot spraudni, lai atliktu koment<PERSON> i<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> pap<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) var paātrināt jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot CSS stilus. Ir pieejamas ar<PERSON> ve<PERSON>, kas piedāvā <PERSON>o funkciju."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> pap<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) var paātrināt jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot skriptus. Ir pieejamas ar<PERSON> ve<PERSON>, kas piedāvā <PERSON> funk<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Ieteicams samazināt vai mainīt tādu [<PERSON><PERSON><PERSON> paplašinājumu](https://extensions.joomla.org/) skaitu, kuri i<PERSON><PERSON><PERSON> nelietotu CSS kodu jūsu lapā. Lai identificētu paplašin<PERSON>jumus, kuri pievieno lieku CSS kodu, mēģiniet izpildīt [koda pārklājumu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome DevTools. Attiecīgo motīvu/spraudni varat identificēt stilu lapas vietrādī URL. Meklē<PERSON><PERSON> spraudņ<PERSON>, kuriem sarakstā ir daudz stilu lapu ar daudz sarkanām atzīmēm koda pārklājumā. Spraudnim ir jāievieto rindā stilu lapa tikai tad, ja tā faktiski tiek izmantota lapā."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Ieteicams samazināt vai mainīt tādu [<PERSON><PERSON><PERSON> papla<PERSON>inājumu](https://extensions.joomla.org/) skaitu, kuri iel<PERSON><PERSON>ē neizmantotu JavaScript kodu jūsu lapā. Lai identificētu spraudņus, kuri pievieno lieku JS kodu, mēģiniet izpildīt [koda pārklājumu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome DevTools. Attiecīgo paplašinājumu varat identificēt skripta vietrādī URL. Meklējiet paplašinājumus, kuriem sarakstā ir daudz skriptu ar daudz sarkanām atzīmēm koda pārklājumā. Paplašinājumam ir jāievieto rindā skripts tikai tad, ja tas faktiski tiek izmantots lapā."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Uzziniet par [pārlūka datu saglab<PERSON><PERSON><PERSON> kešat<PERSON> Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Ieteicams izmantot [attēlu optimizācijas spraudni](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), ka<PERSON> sa<PERSON><PERSON><PERSON> att<PERSON>, vien<PERSON><PERSON> saglabā<PERSON> k<PERSON>it<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Ieteicams izmantot [adaptīvo attēlu spraudni](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), lai saturā varētu izmantot adaptīvos attēlus."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Varat iespē<PERSON>t te<PERSON>ta <PERSON>, iespē<PERSON><PERSON>t G<PERSON>u saspiešanu sistē<PERSON><PERSON> (Sistēma > Globāl<PERSON> > Serveris)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ja negrupējat JavaScript līdze<PERSON>, apsveriet iespēju izmantot programmu [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Atspējojiet Magento iebūvēto [JavaScript grupēšanu un samazināšanu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) un apsveriet iespēju izmantot programmu [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[Definējot pielāgotus fontus](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html), norādiet mainīgo `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Veikalā [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) varat atrast dažādus trešo pušu paplašinājumus jaunāku attēlu formātu <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Apsveriet iespēju modificēt produktu un katalogu veidnes, lai izmantotu tīmekļa platformas [lēnas ielādes](https://web.dev/native-lazy-loading) funkciju."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Izmantojiet platformas Magento [Varnish integrāciju](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Veikala izstrādātāja iestatījumos iespējojiet opciju Minify CSS Files (Samazināt CSS failus). [Uzziniet vairāk](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON>zmantoji<PERSON> r<PERSON> [Terser](https://www.npmjs.com/package/terser), lai samazinātu visus JavaScript līdzekļus no statiskas satura izvietošanas un atspējotu iebūvēto samazināšanas funkciju."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Atspējojiet Magento iebūvēto [JavaScript grupēšanu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Veikalā [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) varat atrast dažādus trešo pušu paplašinājumus attēlu optimizācijai."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Varat pievienot norādes par iepriekšēju pieslēgša<PERSON> vai DNS sākotnējo datu iegūšanas resursu, [modificējot motīva izkārtojumu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Varat pievienot `<link rel=preload>` tagus, [modificējot motīva izkārtojumu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Lai automātiski optimizētu attēlu formātu, i<PERSON><PERSON><PERSON><PERSON> komponentu `next/image`, nevis `<img>`. [Uzziniet vairāk](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Lai automātiski atlik<PERSON> att<PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `next/image`, nevis `<img>`. [Uzziniet vairāk](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON>zmantojiet komponentu `next/image` un iestatiet opcijai “priority” vērt<PERSON><PERSON> “true”, lai iepriekš ielādētu LCP attēlu. [Uzziniet vairāk](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "<PERSON>zman<PERSON><PERSON>et komponentu `next/script`, lai atliktu mazāk svarīgu trešās puses skriptu ielādi. [Uzziniet vairāk](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> komponentu `next/image`, lai nod<PERSON><PERSON><PERSON><PERSON> vienmēr pareizu lie<PERSON>u attēliem. [Uzziniet vairāk](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON> noņem<PERSON> neiz<PERSON> stilu lapu k<PERSON>, `Next.js` konfigur<PERSON><PERSON>j<PERSON> ieteicams iestatīt spraudni `PurgeCSS`. [Uzziniet vairāk](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "<PERSON> noteiktu neiz<PERSON>tu JavaScript kodu, izmantojiet `Webpack Bundle Analyzer`. [Uzziniet vairāk](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)."}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Lai novērtētu lietotnes faktisko veiktspēju ieteicams izmantot `Next.js Analytics`. [Uzziniet vairāk](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurējiet nemainīgu līdzekļu un `Server-side Rendered` (SSR) lapu saglabāšanu kešatmiņā. [Uzziniet vairāk](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON>u att<PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `next/image`, nevis `<img>`. [Uzziniet vairāk](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Lai iestatītu atbilstošās elementa `sizes` v<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> komponentu `next/image`. [Uzziniet vairāk](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Iespējojiet sa<PERSON> savā Next.js serverī. [Uzziniet vairāk](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Sazinieties ar savu kont<PERSON>, lai iespējo<PERSON> funk<PERSON> [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Konfigur<PERSON><PERSON>t to, prioritāte tiks piešķirta jūsu lapas renderēšanas veiktspējai un veiktspēja tiks optimizēta."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "<PERSON>zman<PERSON><PERSON><PERSON> [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) iet<PERSON><PERSON> NitroPack, lai iestatītu vēlamo CSS fonta attēlojuma kārtulas vērtību."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "<PERSON>zman<PERSON><PERSON><PERSON> funk<PERSON> [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513), lai automātiski pārveidotu attēlus WebP formātā."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Atlieciet ārpus ekrāna esošus attēlus, iespējojot funkciju [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Iespējo<PERSON><PERSON> [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) i<PERSON><PERSON><PERSON> NitroPack, lai pa<PERSON><PERSON><PERSON><PERSON><PERSON> sākotnējās ielādes laiku."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Uzlabojiet servera atbildes laiku un optimizējiet uztverto veiktspēju, aktivizējot funkciju [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Iespējojiet funk<PERSON> [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) kešatmiņas iestatījumos, lai sa<PERSON> CSS, HTML un JavaScript failu lielumu un paātrinātu ielādi."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Iespējojiet funk<PERSON> [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) kešatmiņas iestatījumos, lai sa<PERSON>zinātu JS, HTML un CSS failu lielumu un paātrinātu ielādi."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Iespējojiet funk<PERSON> [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS), lai noņemtu CSS kārtulas, kas nav piemēroja<PERSON> šai lapai."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Konfigur<PERSON><PERSON><PERSON> [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) iet<PERSON><PERSON> NitroPack, lai aizkav<PERSON>tu skrip<PERSON>, līd<PERSON> tie būs <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Pārejiet uz funkciju [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) izvēlnē `Caching` un pielāgojiet lapas kešatmiņas derīguma termiņu, lai uzlabotu ielādes laiku un lietotāja pieredzi."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "<PERSON>m<PERSON><PERSON><PERSON>, optimizējiet un pārveidojiet attēlus WebP formātā, iespējojot iestatījumu [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "<PERSON>espējo<PERSON><PERSON> funk<PERSON> [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site), lai jau iepriekš optimizētu savus attēlus un tie atbilstu to konte<PERSON>u izm<PERSON>riem, kuros tie tiek rādīti visās ierīc<PERSON>s."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Izmantojiet funkciju [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) iet<PERSON><PERSON> NitroPack, lai samazinātu pārlūkam nosūtīto failu lielumu."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "<PERSON>zmantojiet komponentu `nuxt/image` un iestatiet vērtību `format=\"webp\"`. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "<PERSON>zmantojiet komponentu `nuxt/image` un iestatiet vērtību `loading=\"lazy\"` ārpus ekrāna esošajiem attēliem. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "<PERSON>zmantojiet komponentu `nuxt/image` un norādiet elementa `preload` vērtību LCP attēlam. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON>zman<PERSON>ji<PERSON> komponentu `nuxt/image` un norādiet skaidras vērt<PERSON>bas `width` un `height`. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "<PERSON>zmantojiet komponentu `nuxt/image` un iestatiet atbilstošo elementa `quality` vērtību. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "<PERSON>zmantojiet komponentu `nuxt/image` un iestatiet atbilstošo elementa `sizes` vērtību. [Uzziniet vairāk](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Aizstā<PERSON>et animētus GIF attēlus ar videoklipiem](https://web.dev/replace-gifs-with-videos/), lai pa<PERSON><PERSON><PERSON><PERSON><PERSON> tīmekļa lapas ielādi, un apsveriet iespēju izmantot modernus failu formātus, piem<PERSON><PERSON>, [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) vai [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), lai uzlabotu saspiešanas efektivitāti par vairāk nekā 30%, salīdzinot ar pašreizējo nozarē vadošo video kodeku VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Ieteicams izmantot [spraudni](https://octobercms.com/plugins?search=image) vai pakalpojumu, lai automātiski pārveidotu augšupielādētos attēlus optimālos formātos. [WebP bezzudumu attēlu](https://developers.google.com/speed/webp) faili ir par 26% mazāki nekā PNG faili un par 25–34% mazāki nekā salīdzināmu JPEG attēlu faili ar līdzvērtīgu SSIM kvalitātes rādītāju. Varat izmantot arī citu nākamās paaudzes attēlu formātu — [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Apsveriet iespēju instalēt [attēlu atliktās ielādes spraudni](https://octobercms.com/plugins?search=lazy), kas sniedz iespēju atlikt ārpus ekrāna esošu attēlu ielādi, vai mainīt motīvu uz tādu, kur<PERSON> šī funkcionalitāte ir nodrošināta. Ieteicams izmantot arī [AMP spraudni](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, ka<PERSON> [i<PERSON><PERSON><PERSON> būtis<PERSON> līd<PERSON>](https://octobercms.com/plugins?search=css). <PERSON><PERSON> spraudņi var traucēt citu spraudņu darb<PERSON>, tāpēc ieteicams tos rūpīgi testēt."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, spraudņi un servera specifikācijas ietekmē servera atbildes laiku. Ieteicams atrast optimizētu motīvu, rūpīgi izvēlēties optimizācijas spraudni un/vai jaunināt serveri. Satura pārvaldības sistēmā October izstrādātāji var izmantot arī [`Queues`](https://octobercms.com/docs/services/queues), lai atliktu laikietilpīgu uzdevumu apstrādi, piem<PERSON>ram, e-pasta ziņojumu nosūtīšanu. Tas krietni paātrina tīmekļa pieprasījumus."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Ieteicams ziņu sarakstos rādīt fragmentus (<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> pogu `show more`), samazināt vienā tīmekļa lapā rādāmo ziņu skaitu, sadal<PERSON>t garas ziņas starp vairākām tīmekļa lapām vai izmantot spraudni, lai atliktu komentāru i<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [spraud<PERSON><PERSON>](https://octobercms.com/plugins?search=css) var paātrin<PERSON>t tīmekļa vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot stilus. <PERSON> pa<PERSON><PERSON><PERSON><PERSON><PERSON>, varat paveikt šo sa<PERSON> jau i<PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON> procesā."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> [spraud<PERSON><PERSON>](https://octobercms.com/plugins?search=javascript) var paātrin<PERSON>t tīmekļa vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot skriptus. Lai paātrin<PERSON><PERSON>, varat paveikt šo sama<PERSON> j<PERSON> i<PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON> procesā."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Ieteicams pārskatīt [spraudņ<PERSON>](https://octobercms.com/plugins), kuri tīme<PERSON> vietnē ielādē nelietotu CSS kodu. <PERSON> note<PERSON>, kuri spraudņi pievieno lieku CSS kodu, Chrome izstrādātāja rīkos izpildiet [koda lietojuma](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) komandu. Stilu lapas vietrādī URL atrodiet attiecīgo motīvu vai spraudni. Mek<PERSON><PERSON><PERSON><PERSON> spraudņ<PERSON>, kuriem ir daudz stilu lapu ar daudzām sarkanām atzīmēm koda lietojuma cilnē. Spraudnim vajadzētu pievienot tikai tādas stilu lapas, kas tīmekļa lapā tiek izmantotas."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Ieteicams pārskatīt [spraudņus](https://octobercms.com/plugins?search=javascript), kuri tīmekļa lapā ielādē neizmantotu JavaScript kodu. <PERSON> noteiktu, kuri spraudņi pievieno lieku JavaScript kodu, Chrome izstrādātāja rīkos izpildiet [koda lietojuma](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) komandu. Skripta vietrādī URL atrodiet attiecīgo motīvu vai spraudni. Meklējiet spraudņus, kuriem ir daudz skriptu ar daudzām sarkanām atzīmēm koda lietojuma cilnē. Spraudnim vajadzētu pievienot tikai tādus skriptus, kas tīmekļa lapā tiek izmantoti."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Lasiet par [lieku tīkla pieprasī<PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> HTTP kešatmiņu](https://web.dev/http-cache/#caching-checklist). Daudzus [spraud<PERSON><PERSON>](https://octobercms.com/plugins?search=Caching) var i<PERSON><PERSON><PERSON>, lai paātrinātu datu saglabāšanu kešatmiņā."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Ieteicams izmantot [attēlu optimizācijas spraudni](https://octobercms.com/plugins?search=image), lai saspiestu attēlus, vien<PERSON><PERSON> saglabā<PERSON> k<PERSON>it<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Lai vajadzīgie attēlu lielumi būtu <PERSON>, tie<PERSON><PERSON> aug<PERSON>dējiet attēlus multivides pārvaldniekā. Apsveriet iespēju izmantot [lieluma mainīšanas filtru](https://octobercms.com/docs/markup/filter-resize) vai [attēlu lieluma mainīšanas spraudni](https://octobercms.com/plugins?search=image), lai nodrošinātu pareizo attēlu lielumu lietošanu."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Iespējojiet teksta sasp<PERSON> tīmekļa servera konfigurācijā."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "<PERSON>a <PERSON><PERSON> atveidojat daudzus atkārtotus elementus, apsveriet iespēju izmantot logošanas bibliotēku, pie<PERSON><PERSON><PERSON>, `react-window`, lai samazinātu izveidoto DOM mezglu skaitu. [Uzziniet vairāk](https://web.dev/virtualize-long-lists-react-window/). Turklāt, ja izmanto<PERSON>t `Effect` aizķeri izpildlaika veiktspējas uzlabošanai, varat samazināt lieko atkārtoto atveidošanu, izmantojot [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) vai [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) un [izlai<PERSON>nas efektus](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) tikai līdz noteiktu atkar<PERSON>bu mai<PERSON> brīdi<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Ja izmantojat bibliotēku React Router, samaziniet komponenta `<Redirect>` lietojumu [mar<PERSON><PERSON><PERSON> navigā<PERSON>ja<PERSON>](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON>a renderē<PERSON>t React komponentus servera pusē, apsveriet iespēju izmantot metodi `renderToPipeableStream()` vai `renderToStaticNodeStream()`, lai klients varētu saņemt un aizpildīt dažādas marķējuma daļas, nevis visu marķējumu reizē. [Uzziniet vairāk](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "<PERSON>a būvējuma sistēmā CSS faili tiek automātiski <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir izvietots jūsu lietojumprogrammas produkcijas būvējums. To var pārbaud<PERSON>t, i<PERSON><PERSON><PERSON><PERSON> paplašinājumu React Developer Tools. [Uzziniet vairāk](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ja būvējuma sistēmā JavaScript faili tiek automātiski <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai ir izvietots jūsu lietojumprogrammas produkcijas būvējums. To var pārbaud<PERSON>t, i<PERSON><PERSON><PERSON><PERSON> paplašinājumu React Developer Tools. [Uzziniet vairāk](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON>a neveicat renderēšanu servera pusē, [sadaliet JavaScript pakas](https://web.dev/code-splitting-suspense/), izman<PERSON>jot `React.lazy()`. Pretējā gadījumā sadaliet kodu, izmantojot trešās puses bibliotēku, piem<PERSON><PERSON>, [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "<PERSON><PERSON>t mēr<PERSON>t komponentu renderēšanas veiktspēju ar spraudni React DevTools Profiler, kur<PERSON> tiek izmantots Profiler API. [Uzziniet vairāk.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Ievietojiet videoklipus `VideoBoxes` tagos, pie<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> `Video Masks`, vai pievienojiet `Transparent Videos`. [Uzziniet vairāk](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> rīku `Wix Media Manager`, lai tie automātiski tiktu rādīti formātā WebP. Uzziniet, [kā vēl var optimizēt](https://support.wix.com/en/article/site-performance-optimizing-your-media) vietnes multivides saturu."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Savas vietnes informācijas paneļa cilnē `Custom Code` [pievienojot trešās puses kodu](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site), g<PERSON><PERSON><PERSON><PERSON><PERSON>, lai šis kods tiktu ielādēts ar aizkavi vai koda kopas beigās. Lai iegultu vietnē mārketinga rīkus, ja iespē<PERSON>, izmantojiet [Wix integrācijas](https://support.wix.com/en/article/about-marketing-integrations). "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "<PERSON><PERSON> W<PERSON>, izmantojot satura nodro<PERSON><PERSON><PERSON><PERSON><PERSON> tīklus un saglab<PERSON><PERSON><PERSON> kešatmiņ<PERSON>, vairumam apmeklētāju atbildes tiek nosūtītas pēc iespējas ātrāk. Ieteicams vietnei [manuāli iespējot saglabā<PERSON>nu kešatmiņ<PERSON>](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed), <PERSON><PERSON><PERSON><PERSON> tad, ja i<PERSON><PERSON><PERSON><PERSON> `<PERSON><PERSON>`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Pārskatiet treš<PERSON> puses kodu, ko esat pievienojis vietnei, i<PERSON><PERSON>jot vietnes informācijas paneļa cilni `Custom Code`, un paturiet tikai vietnei nepieciešamos pakalpojumus. [Uzziniet vairāk](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Ieteicams augšupielādēt GIF failu pakalpojumā, kuru varē<PERSON>, lai iegultu GIF failu kā HTML5 videoklipu."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Ieteicams izmantot spraudni [Performance Lab](https://wordpress.org/plugins/performance-lab/), lai automātiski konvertētu augšupielādētos JPEG attēlus WebP formātā (kur tas tiek atbalstīts)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instalējiet [atliktās ielādes WordPress spraudni](https://wordpress.org/plugins/search/lazy+load/), kas sniedz iespēju atlikt jebkādus ārpus ekrāna esošus attēlus vai mainīt motīvu uz tādu, kur<PERSON> šī funkcija tiek nodrošināta. Ieteicams izmantot arī [AMP spraudni](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "<PERSON>r v<PERSON><PERSON>ki WordPress spraudņi, kas var palīdz<PERSON>t [iek<PERSON><PERSON> būtiskus līdzekļus](https://wordpress.org/plugins/search/critical+css/) vai [atlikt mazāk svarīgus resursus](https://wordpress.org/plugins/search/defer+css+javascript/). Ņemiet vērā, ka šo spraudņu nodrošinātā optimizācija var traucēt funkciju darbībai jūsu motīvā vai spraudņ<PERSON>, t<PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>, jums būs jāveic koda izmaiņas."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, spraudņi un servera specifikācijas ietekmē servera atbildes laiku. Apsveriet iespēju atrast optimizētāku motīvu, rūp<PERSON>gi izvēlēties optimizācijas spraudni un/vai jaunināt serveri."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Apsveriet iespēju rādīt fragmentus ziņu sarak<PERSON> (piemēram, izmantojot tagu “more”), sama<PERSON><PERSON>t attiec<PERSON>gajā lapā rādā<PERSON> ziņu skaitu, sadal<PERSON>t garas ziņas vairākās lapās vai izmanto<PERSON> spraudni, lai atliktu komentāru i<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPress spraudņi](https://wordpress.org/plugins/search/minify+css/) var pa<PERSON>trin<PERSON>t jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot stilus. <PERSON><PERSON> iespē<PERSON><PERSON>, varat arī veikt šo samazin<PERSON>šanu iepriekš izveides procesā."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPress spraudņi](https://wordpress.org/plugins/search/minify+javascript/) var paātrināt jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot skriptus. <PERSON>a iespē<PERSON>, varat veikt šo samazin<PERSON>šanu jau iepriekš izveides procesā."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Ieteicams samazināt vai mainīt tādu [WordPress spraudņu](https://wordpress.org/plugins/) skaitu, kuri iel<PERSON><PERSON>ē nelietotu CSS kodu jūsu lapā. Lai identificētu spraudņus, kuri pievieno lieku CSS kodu, mēģiniet izpildīt [koda pārklājumu](https://developer.chrome.com/docs/devtools/coverage/), izmantojot Chrome DevTools. Saistīto motīvu/spraudni varat identificēt stila lapas vietrādī URL. Mekl<PERSON><PERSON><PERSON> spraudņus, kuriem sarakstā ir daudz stila lapu ar daudz sarkanām atzīmēm koda pārklājumā. Spraudnim ir jāievieto rindā stilu lapa tikai tad, ja tā faktiski tiek izmantota lapā."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Ieteicams samazināt vai mainīt tādu [WordPress spraudņu](https://wordpress.org/plugins/) skaitu, kuri ielādē nelietotu JavaScript kodu jūsu lapā. Lai identificētu spraudņus, kuri pievieno lieku JS kodu, mēģiniet izpildīt [koda pārklājumu](https://developer.chrome.com/docs/devtools/coverage/), izmantojot Chrome DevTools. Saistīto motīvu/spraudni varat identificēt skripta vietrādī URL. Meklē<PERSON>et spraudņus, kuriem sarakstā ir daudz skriptu ar daudz sarkanām atzīmēm koda pārklājumā. Spraudnim ir jāievieto rindā skripts tikai tad, ja tas faktiski tiek izmantots lapā."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Uzziniet par [pārlūkprogrammas datu saglab<PERSON><PERSON><PERSON> kešatmiņā programmatūrā WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Ieteicams izmantot [attēlu optimizācijas WordPress spraudni](https://wordpress.org/plugins/search/optimize+images/), kas sa<PERSON><PERSON><PERSON> attē<PERSON>, vien<PERSON>kus saglabā<PERSON>t k<PERSON>it<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>iel<PERSON>dē<PERSON>et attēlus, i<PERSON><PERSON><PERSON><PERSON> [multivides bibliotēku](https://wordpress.org/support/article/media-library-screen/), lai nodrošinātu nepieciešamo attēlu lielumu pieejamību. Pēc tam ievietojiet attēlus no multivides bibliotēkas vai izmantojiet attēlu logrīku, lai tiktu izmantoti optimāli attēlu lielumi (tostarp reaģējošām robežvērtībām paredzētie). Neizmantojiet `Full Size` attēlus, ja to izmēri neatbilst to lietojumam. [Uzziniet vairāk](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Varat iespējot teksta saspieša<PERSON> tīmekļa servera konfigurācijā."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "<PERSON> konvertētu attēlus uz WebP formātu, sat<PERSON>a WP Rocket cilnē Image Optimization (Attēlu optimizācija) iespējojiet opciju Imagify."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "<PERSON>, <PERSON><PERSON><PERSON> WP Rocket iespējojiet opciju [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images). Šī funkcija aizkavē attēlu i<PERSON>, līdz apmeklētājs ritina lapu uz leju un faktiski tos skata."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "<PERSON> i<PERSON>tu šo i<PERSON>, <PERSON><PERSON><PERSON> WP Rocket iespējojiet opcijas [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Noņemt neizmantoto CSS kodu) un [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (Ielādēt JavaScript ar aizkavi). Šīs funkcijas attiecīgi optimizēs CSS un JavaScript failus, lai tie nebloķētu jūsu lapas atveidi."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Lai novērstu š<PERSON>, sat<PERSON><PERSON> WP Rocket iespējojiet opciju [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Samazināt CSS failus). Lai samazinātu failu lielumu un paātrin<PERSON><PERSON>, jūsu vietnes CSS failos tiks noņemta tukšā vieta un komentāri."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Lai novērstu šo <PERSON>, sat<PERSON><PERSON> WP Rocket iespējojiet opciju [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Samazināt JavaScript failus). Lai samazinātu failu lielumu un pa<PERSON><PERSON><PERSON><PERSON>, JavaScript failos tiks noņemta tukšā vieta un komentāri."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Lai novērstu šo pro<PERSON>, sat<PERSON><PERSON> WP Rocket iespējojiet opciju [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Noņemt neizmantoto CSS kodu). Tād<PERSON><PERSON><PERSON><PERSON> tiek samazināts lapas lielums, noņemot visu CSS kodu un stilu lapas, kas netiek izmantotas, un paturot tikai to CSS kodu, kas tiek izmantots katrā lapā."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Lai novērstu šo pro<PERSON>l<PERSON>, sat<PERSON><PERSON> WP Rocket iespējojiet opciju [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Aizkavēt JavaScript izpildi). T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiks uzlabota jūsu lapas ielāde, jo skriptu izpilde tiks aizkavēta līdz lietotāja mijiedarbībai. Ja jūsu vietnei ir iframe ietvari, varat izmantot arī WP Rocket opcijas [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (LazyLoad iframe ietvariem un video) un [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Aizstāt YouTube iframe ar priekšskatījuma attēlu)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Iespējojiet opciju Imagify satvara WP Rocket cilnē Image Optimization (Attēlu optimizācija) un izpildiet darbību Bulk Optimization (Lielapjoma optimizācija), lai saspiestu savus attēlus."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "<PERSON> pievie<PERSON>u “dns-prefetch” un paātrinātu savienojuma izveidi ar ārē<PERSON>em domēniem, sat<PERSON><PERSON> WP Rocket izmantojiet opciju [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Priekšienest DNS pieprasījumus). Turklāt WP Rocket automātiski pievieno “preconnect” [domēnam Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) un visiem iestatījumiem CNAME, kas pievienoti, izmantojot funkciju [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Iespējot CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Lai novērstu šo problēmu ar font<PERSON>, sat<PERSON><PERSON> WP Rocket iespējojiet opciju [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Noņemt neizmantoto CSS kodu). Jūsu vietnes kritiskie fonti tiks prioritāri ielādēti iepriekš."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Skatiet kalkulatoru."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON><PERSON><PERSON> skatu"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> navigā<PERSON>ja"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritiskais ceļa latentums:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopēt JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tumšo motīvu"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Drukājamā kopija izvērsta"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Saglabāt k<PERSON> G<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Saglabāt kā HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Saglabāt kā JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "<PERSON><PERSON><PERSON><PERSON> neierobežotu datplūsmu"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON><PERSON><PERSON> skat<PERSON>jā"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Pārskata kļūda: nav pārbaudes informācijas"}, "report/renderer/report-utils.js | expandView": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> skatu"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "<PERSON><PERSON><PERSON> puse"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON><PERSON><PERSON> probl<PERSON>mu"}, "report/renderer/report-utils.js | hide": {"message": "Paslēpt"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratorijas dati"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) pašreizēj<PERSON>s lapas analīze emulētajā mobilajā tīklā. Vērtības ir aptuvenas un var atšķirties."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> vienumi man<PERSON>"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Nav piemērojams"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON> jaunā cilnē"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Iespēja"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Aptuve<PERSON><PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Saskaņā ar [Chrome atjauninātajiem instalējamības kritērijiem](https://developer.chrome.com/blog/update-install-criteria) turpmākajā laidienā vairs netiks atbalstīta PTL kategorija rīkā Lighthouse. Lūdzu, skatiet [atjaunināto PTL dokumentāciju](https://developer.chrome.com/docs/devtools/progressive-web-apps/), lai turpmāk veiktu PTL testēšanu."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iel<PERSON>de"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Momentuzņēmums konkrētā brīdī"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laika posms"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Pielāgota ierobežošana"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON> dators"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Nav em<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe versija"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Neierobežota centrālā procesora/atmiņas jauda"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Centrālā procesora ierobež<PERSON>šana"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Tīkla ierobežošana"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Lietotāja aģents (tīkls)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Vienas lapas sesija"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "<PERSON>ie dati ir iegūti no vienas lapas sesijas atšķirībā no lauka datiem, kas tiek apkopoti par daudzām sesijām."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Lēna 4G savienojuma ierobežošana"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON>inā<PERSON>"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Sakļaut <PERSON>"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trešās puses resursus"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atbilstoši videi"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON><PERSON><PERSON>, kas ietekmēja š<PERSON> palaišanu:"}, "report/renderer/report-utils.js | unattributable": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Vērtības ir aptuvenas un var atšķirties. [Veiktspējas rezultāts tiek aprēķināts](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/), pamatojoties tieši uz šiem metrikas veidiem."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Skatīt koka struktūru"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar brī<PERSON>"}, "report/renderer/report-utils.js | warningHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "}, "treemap/app/src/util.js | allLabel": {"message": "Visi"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "V<PERSON> skripti"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Tvērums"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "<PERSON><PERSON>a lie<PERSON> baitos"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nosa<PERSON>ms"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>u"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Neizmantotais apjoms baitos"}}