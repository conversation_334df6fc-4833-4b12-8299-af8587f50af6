{"core/audits/accessibility/accesskeys.js | description": {"message": "Pristupne tipke omogućuju korisnicima da brzo fokusiraju određeni dio stranice. Za pravilno kretanje svaka pristupna tipka mora biti jedinstvena. [Saznajte više o pristupnim tipkama](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[accesskey]` nisu jedinstvene"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` vrijednosti su jedinstvene"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> ARIA `role` podr<PERSON><PERSON> određeni podskup `aria-*` atributa. Njihovo nepodudaranje poništava `aria-*` atribute. [Saznajte kako upariti atribute ARIA-e s n<PERSON><PERSON><PERSON> ul<PERSON>](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributi `[aria-*]` ne podudaraju se sa svojim ulogama"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributi `[aria-*]` podudaraju se sa svojim ulogama"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "`role` <PERSON> omogućuje asistivnim tehnologijama da znaju ulogu svakog elementa na web-stranici. Ako su vrijednosti `role` p<PERSON><PERSON><PERSON><PERSON>isan<PERSON>, ako se radi o nepostojećim vrijednostima `role` ARIA ili o apstraktnim ulogama, svrha elementa neće se prenijeti korisnicima asistivnih tehnologija. [Saznajte više o ulogama ARIA-e](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Vrijednosti dodijeljene polju `role=\"\"` nisu važeće uloge ARIA-e."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Vrijednosti dodijeljene polju `role=\"\"` va<PERSON><PERSON>će su uloge ARIA-e."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Kad element nema pristupačni naziv, č<PERSON><PERSON><PERSON> zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte kako naredbene elemente učiniti pristupačnijima](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementi `button`, `link` i `menuitem` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementi `button`, `link` i `menuitem` imaju pristupačne nazive"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "ARIA dijaloški elementi bez pristupačnih naziva mogu spriječiti korisnike čitača zaslona da prepoznaju svrhu tih elemenata. [Saznajte kako poboljšati pristupačnost ARIA dijaloških elemenata](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Elementi koji <PERSON> `role=\"dialog\"` ili `role=\"alertdialog\"` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Elementi koji <PERSON> `role=\"dialog\"` ili `role=\"alertdialog\"` imaju pristupačne nazive."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>ologi<PERSON>, pop<PERSON>, ne funkcioniraju dosljedno kad je oznaka `aria-hidden=\"true\"` postavljena na `<body>` dokumenta. [Saz<PERSON><PERSON><PERSON> kako `aria-hidden` utječe na tijelo dokumenta](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Element `<body>` dokumenta sadrži atribut `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Element `<body>` dokumenta ne sadrži atribut `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Podređeni elementi s mogućnošću fokusiranja unutar elementa `[aria-hidden=\"true\"]` onemogućuju dostupnost tih interaktivnih elemenata korisnicima asistivnih tehnologija kao što su čitači zaslona. [<PERSON>z<PERSON>j<PERSON> kako `aria-hidden` utječe na elemente s mogućnošću fokusiranja](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementi `[aria-hidden=\"true\"]` sadržavaju podređene elemente koji se mogu fokusirati"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elementi `[aria-hidden=\"true\"]` ne sadrže podređene elemente koji se mogu fokusirati"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Kad polje za unos nema pristupačni naziv, č<PERSON><PERSON><PERSON> zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte više o oznakama polja za unos](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA polja za unos nemaju pristupačne nazive"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA polja za unos imaju pristupačne nazive"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Kad element meter nema pristupačni naziv, čita<PERSON>i zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte kako dodijeliti naziv elementima `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA elementi `meter` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA elementi `meter` imaju pristupačne nazive"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Kad element `progressbar` nema pristupačni naziv, č<PERSON><PERSON><PERSON> zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte kako označiti element `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA elementi `progressbar` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA elementi `progressbar` imaju pristupačne nazive"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Neke uloge ARIA-e zahtijevaju atribute koji opisuju stanje elementa čitačima zaslona. [Saznajte više o ulogama i obveznim atributima](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementi `[role]` nemaju sve potrebne atribute `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` imaju sve obavezne atribute`[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Neke nadređene uloge ARIA-e moraju sadržavati posebne podređene uloge za izvršavanje svojih predviđenih funkcija pristupačnosti. [Saznajte više o ulogama i obveznim podređenim elementima](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementi s ARIA-om `[role]` koji zahtijeva<PERSON> da podređeni elementi sadrže određenu ulogu `[role]` ne sadrže neke ili sve te obavezne podređene elemente."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementi s ARIA-om `[role]` koji zahtije<PERSON> da podređeni elementi sadrže određenu ulogu `[role]` imaju sve obavezne podređene elemente."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Određene nadređene uloge moraju sadržavati neke podređene uloge ARIA-e kako bi mogle pravilno izvršavati predviđene funkcije pristupačnosti. [Saznajte više o ulogama ARIA-e i obveznom nadređenom elementu](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Potrebni nadređeni element ne sadrži `[role]`"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Potrebni nadređeni element sadr<PERSON>i `[role]`."}, "core/audits/accessibility/aria-roles.js | description": {"message": "Uloge ARIA-e moraju sadržavati valjane vrijednosti da bi se mogle izvršavati njihove predviđene funkcije pristupačnosti. [Saznajte više o va<PERSON>e<PERSON><PERSON> ulogama ARIA-e](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` nisu val<PERSON>e"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` su valjane"}, "core/audits/accessibility/aria-text.js | description": {"message": "<PERSON><PERSON> dodate `role=text` oko podjele tekstnog čvora <PERSON>, Voice<PERSON><PERSON> će ga moći tretirati kao jednu frazu, ali podređeni elementi koji se mogu fokusirati neće se najaviti. [Saznajte više o atributu `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Elementi s atributom `role=text` imaju podređene elemente koji se mogu fokusirati."}, "core/audits/accessibility/aria-text.js | title": {"message": "Elementi s atributom `role=text` nemaju podređene elemente koji se mogu fokusirati."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Kad polje prekidača nema pristupačni naziv, č<PERSON><PERSON><PERSON> zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte više o poljima prekidača](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA polja prekidača nemaju pristupačne nazive"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA polja prekidača imaju pristupačne nazive"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Kad element tooltip nema pristupačni naziv, č<PERSON><PERSON>i zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte kako dodijeliti naziv elementima `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA elementi `tooltip` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA elementi `tooltip` imaju pristupačne nazive"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Kad element `treeitem` nema pristupačni naziv, č<PERSON><PERSON>i zaslona najavljuju ga generičkim nazivom, što ga čini neupotrebljivim za korisnike koji se oslanjaju na čitače zaslona. [Saznajte više o označavanju elemenata `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA elementi `treeitem` nemaju pristupačne nazive."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA elementi `treeitem` imaju pristupačne nazive"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tehnologije, poput č<PERSON>, ne mogu tumačiti atribute ARIA-a s nevaže<PERSON>im vrijednostima. [Saznajte više o važećim vrijednostima za atribute ARIA-e](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributi `[aria-*]` ne sadrže valjane vrijednosti"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributi `[aria-*]` sad<PERSON><PERSON><PERSON> valjane vrijednosti"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>ologi<PERSON>, poput č<PERSON>, ne mogu tumačiti atribute ARIA-e s nevažećim nazivima. [Saznajte više o važećim atributima ARIA-e](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributi `[aria-*]` nisu valjani ili su pogrešno napisani"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributi `[aria-*]` su valjani i nisu pogreš<PERSON> napisani"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementi koji nisu pro<PERSON> provjeru"}, "core/audits/accessibility/button-name.js | description": {"message": "Kada gumb nema pristupačni naziv, <PERSON><PERSON><PERSON><PERSON> zaslona najavljuju ga kao \"gumb\" te je on neupotrebljiv za korisnike koji se oslanjaju na čitače zaslona. [Saznajte kako gumbe učiniti pristupačnijima](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "G<PERSON><PERSON> nemaju pristupačan naziv"}, "core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> imaju prist<PERSON> naziv"}, "core/audits/accessibility/bypass.js | description": {"message": "Dodavanje načina za zaobilaženje repetitivnog sadržaja omogućuje korisnicima tipkovnice učinkovitije kretanje po stranici. [Saznajte više o zaobilaženju blokada](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Stranica ne sadrži <PERSON>lov, vezu za preskakanje ili regiju kao orijentir"}, "core/audits/accessibility/bypass.js | title": {"message": "Stranica <PERSON>ž<PERSON>, vezu za preskakanje ili regiju kao orijentir"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Mnogim je korisnicima teško ili nemoguće čitati tekst niskog kontrasta. [Saznajte kako dodati dovoljan kontrast boja](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Boje u pozadini i prednjem planu nemaju zadovoljavajući omjer kontrasta."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Boje u pozadini i prednjem planu imaju zadovoljavajući omjer kontrasta"}, "core/audits/accessibility/definition-list.js | description": {"message": "Kada popisi definicija nisu val<PERSON>, <PERSON><PERSON><PERSON><PERSON> z<PERSON>lona mogu proizvesti zbunjujuć ili netočan izlaz. [Saznajte kako pravilno strukturirati popise definicija](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementi `<dl>` ne sadrže samo pravilno poredane grupe `<dt>` i `<dd>` i elemente `<script>`, `<template>` ili `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Elementi `<dl>` sad<PERSON><PERSON><PERSON> samo pravilno poredane grupe `<dt>` i `<dd>` i elemente `<script>`, `<template>` ili `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Stavke na popisu definicija (`<dt>` i `<dd>`) moraju biti sadržane u nadređenom elementu `<dl>` da bi ih čitači zaslona mogli pravilno najaviti. [Saznajte kako pravilno strukturirati popise definicija](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Stavke na popisu definicija nisu upakirane u elemente `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Stavke na popisu definicija upakirane su u elemente `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Naslov korisnicima čitača zaslona pruža pregled stranice, a korisnici tražilice značajno se na njega oslanjaju kako bi odredili je li neka stranica relevantna za njihovo pretraživanje. [Saznajte više o naslovima dokumenata](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument ne sadrži element `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument sadrži element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Svi elementi s mogućnošću fokusiranja moraju imati jedinstveni `id` kako bi bili vidljivi asistivnim tehnologijama. [Saz<PERSON>jte kako ispraviti duplicirani `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atributi `[id]` na aktivnim elementima koji se mogu fokusirati nisu jedinstveni"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atributi `[id]` na aktivnim elementima koji se mogu fokusirati jedinstveni su"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Vrijednost ARIA ID-ja mora biti jedinstvena kako bi se spriječilo da asistivne tehnologije previde druge instance. [Saznaj<PERSON> kako ispraviti duplikate ARIA ID-jeva](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ID-jevi nisu jed<PERSON><PERSON>i"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ID-jevi su jedinstveni"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Naslov bez sadržaja ili nepristupačan tekst korisnicima čitača zaslona onemogućuje pristup informacijama o strukturi stranice. [Saznajte više o naslovima](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Elementi naslova ne sadrže sadržaj."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Svi elementi naslova sadrže sadržaj."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Ako polja obrasca imaju vi<PERSON><PERSON>, asistivne tehnologije kao što su čitači zaslona koji koriste prvu, zadn<PERSON> ili sve oznake mogu ih najavljivati na zbunjujući način. [Saznajte kako upotrebljavati oznake obrasca](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Polja obrasca imaju više oznaka"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nijedno polje obrasca nema više oznaka"}, "core/audits/accessibility/frame-title.js | description": {"message": "Korisnici čitača zaslona oslanjaju se na naslove okvira za opisivanje sadržaja okvira. [Saznajte više o naslovima okvira](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementi `<frame>` ili `<iframe>` nema<PERSON> naslov"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementi `<frame>` ili `<iframe>` imaju naslov"}, "core/audits/accessibility/heading-order.js | description": {"message": "Pravilno poredani naslovi koji ne preskaču razine otkrivaju semantičku strukturu stranice, što olakšava navigaciju i razumijevanje prilikom upotrebe asistivnih tehnologija. [Saznajte više o redoslijedu naslova](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elementi naslova nisu u postupno silaznom redoslijedu"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elementi naslova pojavljuju se postupno silaznim redoslijedom"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ako za stranicu nije naveden atribut `lang`, čita<PERSON> zaslona pretpostavlja da je stranica na zadanom jeziku koji je korisnik odabrao prilikom postavljanja čitača zaslona. Ako stranica nije stvarno na zadanom jeziku, čitač zaslona možda neće pravilno najavljivati tekst sa stranice. [Saznajte više o atributu `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nema atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ima atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Na<PERSON>đ<PERSON><PERSON> valjanog [BCP 47 jezika](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaže čitačima zaslona u pravilnom najavljivanju teksta. [Saznajte kako upotrebljavati atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` ne sadrži valjanu vrijednost za atribut `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ima valjanu vrijednost za svoj atribut `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Ako web-stranica ne navodi dosljedan jez<PERSON>, <PERSON><PERSON><PERSON> zaslona možda neće pravilno najavljivati tekst sa stranice. [Saznajte više o atributu `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Element `<html>` nema atribut `[xml:lang]` s istim osnovnim jezikom kao atribut `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Element `<html>` ima atribut `[xml:lang]` s istim osnovnim jezikom kao atribut `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Veze s istim odredištem trebaju imati isti opis kako bi korisnicima pomogle da razumiju svrhu veze i odluče žele li je pratiti. [Saznajte više o identičnim vezama](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identične veze nemaju istu svrhu."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Identične veze imaju istu svrhu."}, "core/audits/accessibility/image-alt.js | description": {"message": "Informativni elementi trebali bi sadržavati kratak, opisni zamjenski tekst. Ukrasni elementi mogu se zanemariti praznim atributom alt. [Saznajte više o atributu `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementi slike nemaju atribute `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elementi slike imaju `[alt]` atribute"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Informativni elementi trebali bi sadržavati kratak, opisni zamjenski tekst. Zamjenski tekst koji je u potpunosti jednak tekstu pored veze ili slike potencijalno zbunjuje korisnike čitača zaslona jer će se tekst čitati dvaput. [Saznajte više o atributu `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Elementi slike imaju atribute `[alt]` koji su suvišan tekst."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Elementi slike nemaju atribute `[alt]` koji su suvišan tekst."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Dodavanje vidljivog i dostupnog teksta gumbima za unos može pomoći korisnicima čitača zaslona da shvate svrhu gumba za unos. [Saznajte više o gumbima za unos](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Gumbi za unos nemaju prepoznatljiv tekst."}, "core/audits/accessibility/input-button-name.js | title": {"message": "G<PERSON>bi za unos imaju prepoznatljiv tekst."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Kada se slika upotrebljava kao gumb `<input>`, navođenje zamjenskog teksta može pomoći korisnicima čitača zaslona da shvate svrhu gumba. [Saznajte više o zamjenskom tekstu slike za unos](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementi `<input type=\"image\">` nemaju `[alt]` tekst"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` sadr<PERSON><PERSON> `[alt]` tekst"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Vidljive tekstne oznake koje se ne podudaraju s pristupačnim nazivom mogu rezultirati zbunjujućim doživljajem korisnika čitača zaslona. [Saznajte više o pristupačnim nazivima](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Elementi s vidljivim tekstnim oznakama nemaju podudarne pristupačne nazive."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Elementi s vidljivim tekstualnim oznakama imaju podudarne pristupačne nazive."}, "core/audits/accessibility/label.js | description": {"message": "Oznake osiguravaju da asistivne tehnologije, pop<PERSON>, pravilno najavljuju kontrole obrazaca. [Saznajte više o oznakama elemenata obrasca](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Elementi oblika ne sadrže povezane oznake"}, "core/audits/accessibility/label.js | title": {"message": "Elementi oblika imaju povezane oznake"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "<PERSON>an glavni orijentir pomaže korisnicima čitača zaslona da se kreću web-stranicom. [Saznajte više o orijentirima](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokument nema glavni ori<PERSON>ntir."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokument ima glavni orijentir."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Mnogim je korisnicima teško ili nemoguće čitati tekst niskog kontrasta. Tekst veze koji se može raspoznati poboljšava doživljaj za slabovidne korisnike. [Saznajte kako postaviti raspoznatljive veze](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Veze se oslanjaju na boju kako bi bile raspoznatljive."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Veze su raspoznatljive bez oslanjanja na boju."}, "core/audits/accessibility/link-name.js | description": {"message": "Tekst veze (i zamjenski tekst za slike kada se upotrebljavaju kao veze) koji je prepoznatljiv, jedinstven i može se fokusirati omogućuje lakše kretanje korisnicima čitača zaslona. [Saznajte kako veze učiniti pristupačnima](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Veze nemaju naziv koji je moguće raspoznati"}, "core/audits/accessibility/link-name.js | title": {"message": "Veze imaju naziv koji je moguće raspoznati"}, "core/audits/accessibility/list.js | description": {"message": "Čitači zaslona imaju poseban način najavljivanja popisa. Osiguravanje pravilne strukture popisa pomaže izlazu čitača zaslona. [Saznajte više o pravilnoj strukturi popisa](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Popisi ne sadrže samo elemente `<li>` i elemente koji podupiru skriptu (`<script>` i`<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Popisi sadrže samo elemente `<li>` i elemente koji podupiru skriptu (`<script>` i`<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Za čitače zaslona stavke na popisu (`<li>`) moraju biti sadržane unutar nadređenog `<ul>`, `<ol>` ili `<menu>` kako bi ih se moglo pravilno najaviti. [Saznajte više o pravilnoj strukturi popisa](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> (`<li>`) nisu sadržane unutar nadređenih elemenata `<ul>`, `<ol>` ili `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> popisa (`<li>`) sadržane su unutar nadređenih elemenata `<ul>`, `<ol>` ili `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Korisnici ne očekuju automatsko osvježavanje stranice. Ako se to dogodi, fokus se vraća na vrh stranice. To može biti frustrirajuće i zbuniti korisnike. [Saznajte više o metaoznaci za osvježavanje](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument upotrebljava `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument ne upotrebljava `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Onemogućivanje zumiranja problematično je za slabovidne korisnike koji se oslanjaju na povećavanje zaslona kako bi dobro vidjeli sadržaj web-stranice. [Saznajte više o metaoznaci za vidljivi dio](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` se upotrebljava u elementu `<meta name=\"viewport\">` ili je atribut `[maximum-scale]` manji od pet."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` se ne upotrebljava u elementu `<meta name=\"viewport\">` i atribut `[maximum-scale]` nije manji od pet."}, "core/audits/accessibility/object-alt.js | description": {"message": "Čitači zaslona ne mogu prevesti netekstualni sadržaj. Dodavanje zamjenskog teksta elementima `<object>` pomaže čitačima zaslona u prenošenju značenja korisnicima. [Saznajte više o zamjenskom tekstu za element `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementi `<object>` nemaju zamjenski tekst"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` imaju zamjenski tekst"}, "core/audits/accessibility/select-name.js | description": {"message": "Elementi obrasca bez učinkovitih oznaka mogu frustrirati korisnike čitača zaslona. [Saznajte više o elementu `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Odabrani elementi nemaju povezane elemente oznaka."}, "core/audits/accessibility/select-name.js | title": {"message": "Odabrani elementi imaju povezane elemente oznake."}, "core/audits/accessibility/skip-link.js | description": {"message": "<PERSON>ko navedete vezu za preskakanje, korisnici će moći preskočiti na glavni sadržaj i tako uštedjeti vrijeme. [Saznajte više o vezama za preskakanje](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Preskočite veze koje se ne mogu fokusirati."}, "core/audits/accessibility/skip-link.js | title": {"message": "Veze za preskakanje mogu se fokusirati."}, "core/audits/accessibility/tabindex.js | description": {"message": "Vrijednost viša od 0 podrazumijeva eksplicitno naređivanje kretanja. Iak<PERSON> je tehnički valjano, to često frustrira korisnike koji se oslanjaju na asistivne tehnologije. [Saznajte više o atributu `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Neki elementi imaju vrijednost `[tabindex]` višu od 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nijedan element nema vrijednost `[tabindex]` veću od 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Atribut sažetka treba opisivati strukturu tablice, a `<caption>` treba imati naslov na zaslonu. Točno označavanje tablice pomaže korisnicima čitača zaslona. [Saznajte više o sažetku i titlovima](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Tablice imaju isti sadržaj u atributu sažetka i `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Tablice imaju različit sadržaj u atributu sažetka i `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Ako tablice upotrebljavaju stvarni element titlova umjesto ćelija s atributom `[colspan]`, to može poboljšati doživljaj za korisnike čitača zaslona. [Saznajte više o titlovima](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Tablice ne upotrebljavaju `<caption>` um<PERSON><PERSON> s atributom `[cols<PERSON>]` da bi nazna<PERSON><PERSON> naslov."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "<PERSON><PERSON><PERSON> upotrebljavaju `<caption>` um<PERSON><PERSON> s atributom `[colspan]` kako bi naznač<PERSON> naslov."}, "core/audits/accessibility/target-size.js | description": {"message": "Ako su ciljevi dodira dovoljno veliki i razmaknuti, korisnici koji imaju poteškoća s ciljanjem malih kontrola lakše će ih aktivirati. [Saznajte više o ciljevima dodira](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> dodira nisu dovoljno veliki ni razmaknuti."}, "core/audits/accessibility/target-size.js | title": {"message": "Ciljevi dodira dovoljno su veliki i razmaknuti."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Ako elementi `<td>` u velikoj tablici (tri ili više ćelija u širini i visini) imaju povezano zaglavlje tablice, to može poboljšati doživljaj za korisnike čitača zaslona. [Saznajte više o zaglavljima tablice](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Elementi `<td>` u velikom elementu `<table>` nemaju zaglavlja tablice."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Elementi `<td>` u velikom elementu `<table>` imaju jedno ili više zaglavlja tablice."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Potrebno je pripaziti da se ćelije `<td>` koje upotrebljavaju atribut `[headers]` odnose samo na druge ćelije u istoj tablici kako bi se korisnicima čitača zaslona omogućio bolji doživljaj. [Saznajte više o atributu `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ćelije u elementu `<table>` koje upotrebljavaju atribut `[headers]` odnose se na element `id` koji nije pronađen unutar iste tablice."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Ćelije u elementu `<table>` koje upotrebljavaju atribut `[headers]` odnose se na ćelije tablice unutar iste tablice."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Osiguravanje da se zaglavlja tablice uvijek odnose na neki skup ćelija može poboljšati doživljaj za korisnike čitača zaslona. [Saznajte više o zaglavljima tablice](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` i elementi s`[role=\"columnheader\"/\"rowheader\"]` nemaju podatkovne ćelije koje oni opisuju."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` i elementi s `[role=\"columnheader\"/\"rowheader\"]` imaju podatkovne <PERSON>elije koje opisuju."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Na<PERSON><PERSON><PERSON><PERSON> valjan<PERSON> [BCP 47 jezika](https://www.w3.org/International/questions/qa-choosing-language-tags#question) u elementima pomaže osigurati da čitač zaslona pravilno izgovara tekst. [Saznajte kako upotrebljavati atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributi `[lang]` ne sadrže valjanu vrijednost"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributi `[lang]` imaju valjanu vrijednost"}, "core/audits/accessibility/video-caption.js | description": {"message": "Kada videozapis sadr<PERSON>i titlove, glu<PERSON> i nagluhim osobama jednostavnije je pristupiti njegovim informacijama. [Saznajte više o titlovima za videozapise](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` ne sadrže element `<track>` s `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` sadržavaju element `<track>` s `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Trenutačna vrijednost"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Predložena oznaka"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` pomaže korisnicima da brže šalju obrasce. Kako biste pomogli korisnicima, savjetu<PERSON><PERSON> vam da to omogućite tako što ćete postaviti atribut `autocomplete` na važeću vrijednost. [Saznajte više o atributu `autocomplete` u obrascima](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Elementi `<input>` nemaju točne atribute za `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Zahtijeva ručni pregled"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Pregledajte narudžbu oznaka"}, "core/audits/autocomplete.js | title": {"message": "Elementi `<input>` pravilno upotrebljavaju `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "<PERSON><PERSON><PERSON> `autocomplete`: \"{token}\" nije važe<PERSON>e u {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Pregledajte narudžbu oznaka: \"{tokens}\" u {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Omogu<PERSON><PERSON><PERSON> pod<PERSON> radnje"}, "core/audits/bf-cache.js | description": {"message": "Mnoge se navigacije izvode vraćanjem na prethodnu stranicu ili ponovno na sljedeću stranicu. Predmemoriranje cijele stranice (bfcache) može ubrzati ta vraćanja. [Saz<PERSON>jte više o predmemoriranju cijele stranice](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 razlog neuspjeha}one{# razlog neuspjeha}few{# razloga neuspjeha}other{# razloga neuspjeha}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Razlog neuspjeha"}, "core/audits/bf-cache.js | failureTitle": {"message": "Stranica je spriječila vraćanje predmemoriranja cijele stranice"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Ne omogućuje poduzimanje radnje"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Na čekanju je podrška za preglednik"}, "core/audits/bf-cache.js | title": {"message": "Stranica nije spriječila vraćanje predmemoriranja cijele stranice"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Predmemoriranje cijele stranice ne može se testirati u starom Chromeu bez grafičkog korisničkog sučelja (`--chrome-flags=\"--headless=old\"`). Da biste vidjeli rezultate provjere, upotrijebite novi Chrome bez grafičkog korisničkog sučelja (`--chrome-flags=\"--headless=new\"`) ili standardni Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chromeova proširenja negativno su utjecala na izvedbu učitavanja ove stranice. Pokušajte pregledati stranicu anonimno ili putem Chromeovog profila bez proširenja."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Raščlamba skripte"}, "core/audits/bootup-time.js | columnTotal": {"message": "Ukupno vrijeme CPU-a"}, "core/audits/bootup-time.js | description": {"message": "Savjetujemo vam da skratite vrijeme potrebno za raščlambu, kompiliranje i izvršavanje JS-a. Isporuka manjih JS-ova mogla bi vam pomoći da to postignete. [Saznajte kako skratiti vrijeme izvršavanja JavaScripta](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Skratite vrijeme izvršavanja JavaScripta"}, "core/audits/bootup-time.js | title": {"message": "Vrijeme izvršavanja JavaScripta"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Uklonite velike i dvostruke JavaScript module iz skupova kako biste smanjili nepotrebne bajtove koje troši aktivnost na mreži. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Uklanjanje dvostrukih modula u JavaScript paketima"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veliki GIF-ovi nisu učinkoviti za prikaz animiranog sadržaja. Savjetujemo vam da umjesto GIF-a upotrebljavate MPEG4/WebM videozapise za animacije i PNG/WebP za statične slike da biste smanjili količinu mrežnih bajtova. [Saznajte više o učinkovitim formatima videozapisa](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Upotrebljavajte formate videozapisa za animirani sadržaj"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Kodovi polyfill i transform omogućuju starim preglednicima da upotrebljavaju nove značajke JavaScripta. Međutim, mnogi nisu potrebni za moderne preglednike. Za svoj skupni JavaScript usvojite modernu strategiju implementacije skripte upotrebom otkrivanja značajke modula/nemodula kako bi se smanjila količina koda koji se šalje modernim preglednicima, uz istodobno zadržavanje podrške za stare preglednike. [Saznajte kako upotrebljavati moderni JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Izbjegavajte posluživanje starog JavaScripta u modernim preglednicima"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formati slika kao što su WebP i AVIF često pružaju bolju kompresiju od PNG-a ili JPEG-a, što znači da će se preuzimati brže i uz manju potrošnju podataka. [Saznajte više o modernim formatima slika](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Poslužite slike u modernim formatima"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Za slike izvan zaslona i skrivene slike savjetujemo odgođeno učitavanje nakon što se učitaju svi kritični resursi da biste skratili vrijeme do interaktivnosti. [Saznajte kako odgoditi slike izvan zaslona](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odgodite slike izvan z<PERSON>a"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursi blokiraju prvo renderiranje vaše stranice. Savjetujemo vam da ključan JS/CSS isporučite u tekstu te da odgodite sve JS-ove/stilove koji nisu ključni. [Saznajte kako ukloniti resurse koji blokiraju renderiranje](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Uklonite resurse koji blokiraju generiranje"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Veliki mrežni resursi korisnicima uzrokuju stvarne novčane troškove i usko koreliraju s dugim vremenom učitavanja. [Saznajte kako smanjiti veličine resursa](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Ukupna veličina bila je {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Izbjegavajte ogromne mrežne resurse"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Izbjegava ogromne mrežne resurse"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Umanjenjem CSS datoteka mogu se smanjiti veličine mrežnih resursa. [Saznajte kako umanjiti CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Umanjite CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Umanjenjem JavaScript datoteka mogu se smanjiti veličine resursa i skratiti vrijeme raščlambe skripte. [Saznajte kako umanjiti JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Umanjite JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Smanjite nekorištena pravila iz listova stilova i odgodite CSS koji se ne koristi za sadržaj na vidljivom dijelu stranice kako biste smanjili potrošnju bajtova u mrežnoj aktivnosti. [Saznajte kako smanjiti nekorišteni CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Smanjite nekorišteni CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Smanjite nekorišteni JavaScript i odgodite učitavanje skripti dok ne budu potrebne kako biste smanjili potrošnju bajtova u mrežnoj aktivnosti. [Saznajte kako smanjiti nekorišteni JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Smanjite nekorišteni JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dugotrajno predmemoriranje može ubrzati ponovljene posjete vašoj stranici. [Saznajte više o pravilima učinkovitog predmemoriranja](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je jedan resurs}one{Pronađen je # resurs}few{Pronađena su # resursa}other{<PERSON>nađeno je # resursa}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Poslužite statične elemente s pravilima učinkovitog predmemoriranja"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Upotrebljava pravila učinkovitog predmemoriranja za statične elemente"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizirane slike učitavaju se brže i troše manje mobilnih podataka. [Saznajte kako učinkovito kodirati slike](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Kodirajte slike učinkovito"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Stvarne dimenzije"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Prikazane dimenzije"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Slike su veće od svoje prikazane veličine"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Slike su u skladu sa svojom prikazanom veličinom"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Poslužite slike prikladnih veličina da biste uštedjeli mobilne podatke i poboljšali vrijeme učitavanja. [Saznajte kako promijeniti veličinu slika](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Postavite slike u odgovarajućoj veličini"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstualni resursi trebaju se posluživati s kompresijom (gzip, deflate ili brotli) radi minimiziranja ukupne količine mrežnih bajtova. [Saznajte više o kompresiji teksta](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Omogućite sažimanje teksta"}, "core/audits/content-width.js | description": {"message": "Ako se širina sadržaja vaše aplikacije ne podudara sa širinom vidljivog dijela, vaša aplikacija možda neće biti optimizirana za mobilne zaslone. [Saznajte kako promijeniti veličinu sadržaja za vidljivi dio](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Veličina vidljivog dijela od {innerWidth} px ne podudara se s veličinom prozora od {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Sad<PERSON><PERSON><PERSON> nije ispravne veličine za vidljivi dio."}, "core/audits/content-width.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> je ispravne veličine za vidljivi dio"}, "core/audits/critical-request-chains.js | description": {"message": "Lanci kritičkih zahtjeva u nastavku prikazuju koji se resursi učitavaju s visokim prioritetom. Savjetujemo vam da skratite duljinu lanaca, smanjite veličinu resursa za preuzimanje ili odgodite preuzimanje resursa koji nisu nužni kako biste poboljšali učitavanje stranice. [Saznajte kako izbjeći lančano povezivanje kritičnih zahtjeva](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>nađen je jedan lanac}one{Pronađen je # lanac}few{<PERSON>nađena su # lanca}other{<PERSON>nađeno je # lanaca}}"}, "core/audits/critical-request-chains.js | title": {"message": "Izbjegavajte ulančavanje kritičnih zahtjeva"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Ozbiljnost"}, "core/audits/csp-xss.js | description": {"message": "Snažna pravila o sigurnosti <PERSON> (CSP) značajno smanjuju opasnost od napada skriptiranjem na više web-lokacija (XSS). [Saznajte kako upotrebljavati CSP za sprječavanje XSS-a](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sin<PERSON>ks<PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Stranica sadrži CSP definiran u oznaci `<meta>`. Razmislite o tome da premjestite CSP u HTTP zaglavlje ili da definirate drugi strogi CSP u HTTP zaglavlju."}, "core/audits/csp-xss.js | noCsp": {"message": "U načinu provedbe nije pronađen CSP"}, "core/audits/csp-xss.js | title": {"message": "Provjera učinkovitosti CSP-a protiv XSS napada"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Ukidanje / upozorenje"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Obustavljeni API-ji na kraju će se ukloniti iz preglednika. [Saznajte više o obustavljenim API-jima](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON><PERSON><PERSON> je jedno upozorenje}one{Broj pronađenih upozorenja: #}few{Broj pronađenih upozorenja: #}other{Broj pronađenih upozorenja: #}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Upotrebljava ukinute API-je"}, "core/audits/deprecations.js | title": {"message": "Izbjegava ukinute API-je"}, "core/audits/dobetterweb/charset.js | description": {"message": "Potrebna je izjava o kodiranju znakova. To je moguće napraviti uz oznaku `<meta>` u prva 1024 bajta HTML-a ili u HTTP zaglavlju odgovora vrste sadržaja. [Saznajte više o navođenju kodiranja znakova](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Izjava o skupu znakova nedostaje ili se javlja prekasno u HTML-u"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> def<PERSON><PERSON> sku<PERSON>"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Specificiranje vrste dokumenta (doctype) sprječava preglednik da prijeđe u način rada sa starijim značajkama. [Saznajte više o izjavi o vrsti dokumenta](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Naziv vrste dokumenta (doctype) mora biti niz `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Do<PERSON><PERSON> <PERSON><PERSON><PERSON>i `doctype` koji aktivira `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "URL mora sadržavati vrstu dokumenta."}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Očekivani javni ID bit će prazan niz"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Očekivani ID sustava bit će prazan niz"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Doku<PERSON> sad<PERSON><PERSON>i `doctype` koji aktivira `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stranici nedostaje vrsta dokumenta HTML, stoga pokreće način rada sa starijim značajkama"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Stranica ima HTML vrstu dokumenta"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vrijednost"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Veliki DOM zahtijeva više memorije, produlju<PERSON> [izra<PERSON>une stila](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) te dovodi do skupih [preoblikovanja izgleda](https://developers.google.com/speed/articles/reflow). [Saznajte kako izbjeći pretjeranu veličinu DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{Jedan element}one{# element}few{# elementa}other{# elemenata}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Izbjegavajte pretjeranu veličinu DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimalna dubina DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Ukupan broj elemenata DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksimalni broj podređenih elemenata"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Izbjegava pretjeranu veličinu DOM-a"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Korisnici ne vjeruju web-lokacijama koje žele znati njihovu lokaciju bez konteksta ili ih takve web-lokacije zbunjuju. Razmislite o tome da umjesto toga zahtjev povežete s radnjama korisnika. [Saznajte više o dopuštenju za geolociranje](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Zahtijeva dopuštenje za geolociranje pri učitavanju stranice"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Izbjegava traženje dopuštenja za geolociranje pri učitavanju stranice"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Vrsta poteškoće"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Poteškoće zabilježene na ploči `Issues` u Razvojnim alatima za Chrome ukazuju na neriješene probleme. Rezultat su neuspjelih mrežnih zahtjeva, nedovoljnih sigurnosnih kontrola i ostalih pitanja povezanih s preglednikom. Da biste saznali više pojedinosti o svakoj poteškoći, otvorite ploču Poteškoće u Razvojnim alatima za Chrome."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Poteškoće su zabilježene na ploči `Issues` u Razvojnim alatima za Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokirano zbog pravila o različitim izvorima"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Oglasi upotrebljavaju veliku količinu resursa"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Nema poteškoća na ploči `Issues` u Razvojnim alatima za Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verzija"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Sve pristupne JavaScript biblioteke otkrivene na stranici. [Saznajte više o ovoj dijagnostičkoj reviziji za otkrivanje JavaScript biblioteke](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Pronađene JavaScript biblioteke"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON>ko korisnici imaju spore veze, vanjske skripte koje se dinamički ubacuju pomoću `document.write()` mogu odgoditi učitavanje stranice za desetke sekundi. [Saznajte kako izbjeći document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Izbjegavajte `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Izbjegava `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Korisnici ne vjeruju web-lokacijama koje traže slanje obavijesti bez konteksta ili ih takve web-lokacije zbunjuju. Razmislite o tome da umjesto toga zahtjev povežete s pokretima korisnika. [Saznajte više o odgovornom dobivanju dopuštenja za obavijesti](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Zahtijeva dopuštenje za obavještavanje pri učitavanju stranice"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Izbjegava traženje dopuštenja za obavještavanje pri učitavanju stranice"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Sprječavanje lijepljenja unosa loša je praksa za UX i oslabljuje sigurnost blokiranjem upravitelja zaporki.[Saznajte više o poljima za unos prilagođenima korisnicima](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Onemogućuje korisnicima lijepljenje u polja za unos"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Omogućuje korisnicima lijepljenje u polja za unos"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ima brojne prednosti u odnosu na HTTP/1.1, uključujući binarna zaglavlja i multipleksiranje. [Saznajte više o HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON> zahtjev koji nije poslužen protokolom HTTP/2}one{Broj zahtjeva koji nisu posluženi protokolom HTTP/2: #}few{<PERSON><PERSON>j zahtjeva koji nisu posluženi protokolom HTTP/2: #}other{Broj zahtjeva koji nisu posluženi protokolom HTTP/2: #}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Koristi HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Razmislite o tome da svoje pasivne slušatelje događaja označite kao `passive` da biste poboljšali izvedbu pomicanja na stranici. [Saznajte više o usvajanju pasivnih slušatelja događaja](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ne upotrebljava pasivne osluškivače za unaprjeđenje rezultata pretraživanja"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Upotrebljava pasivne osluškivače za unaprjeđenje rezultata pretraživanja"}, "core/audits/errors-in-console.js | description": {"message": "Pogreške zabilježene u konzoli ukazuju na neriješene probleme. Rezultat su neuspjelih mrežnih zahtjeva i ostalih pitanja povezanih s preglednikom. [Saznajte više o tim pogreškama u dijagnostičkoj reviziji konzole](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Pogreške preglednika zabilježene su u konzoli"}, "core/audits/errors-in-console.js | title": {"message": "Na konzoli nema zabilježenih pogrešaka preglednika"}, "core/audits/font-display.js | description": {"message": "Iskoristite CSS značajku `font-display` da bi tekst bio vidljiv korisnicima dok se web-fontovi učitavaju. [Saznajte više o zna<PERSON> `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Neka tekst ostaje vidljiv tijekom učitavanja web-fontova"}, "core/audits/font-display.js | title": {"message": "Sav tekst ostaje vidljiv tijekom učitavanja web-fontova"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse nije mogao automatski provjeriti vrijednost `font-display` za izvor {fontOrigin}.}one{Lighthouse nije mogao automatski provjeriti vrijednost `font-display` za izvor {fontOrigin}.}few{Lighthouse nije mogao automatski provjeriti vrijednost `font-display` za izvor {fontOrigin}.}other{Lighthouse nije mogao automatski provjeriti vrijednost `font-display` za izvor {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON> (stvarni)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> (prikazane)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Dimenzije prikaza slike trebale bi odgovarati prirodnom omjeru slike. [Saznajte više o omjeru slike](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Prikazuje slike netočnog omjera"}, "core/audits/image-aspect-ratio.js | title": {"message": "Prikazuje slike točnog omjera"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Prikazan<PERSON> ve<PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Očekivana veličina"}, "core/audits/image-size-responsive.js | description": {"message": "Prirodne dimenzije slike trebale bi biti proporcionalne omjeru veličina zaslona i piksela radi dobivanja maksimalne jasnoće slike. [Saznajte kako pružiti responzivne slike](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Poslužuje slike s niskom razlučivošću"}, "core/audits/image-size-responsive.js | title": {"message": "Poslužuje slike s odgovarajućom razlučivošću"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikacija je već instalirana"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Preuzimanje obavezne ikone iz manifesta nije uspjelo"}, "core/audits/installable-manifest.js | columnValue": {"message": "Razlog neuspjeha"}, "core/audits/installable-manifest.js | description": {"message": "Us<PERSON>žni je alat tehnologija koja aplikaciji omogućuje korištenje brojnih značajki progresivnih web-aplikacija, kao što su izvanmrežni rad, dodavanje na početni zaslon i push obavijesti. Uz odgovarajuće implementacije uslužnog alata i manifesta preglednici mogu proaktivno zatražiti od korisnika da dodaju vašu aplikaciju na početni zaslon, što može dovesti do veće angažiranosti. [Saznajte više o zahtjevima za instalabilnost za manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 razlog}one{# razlog}few{# razloga}other{# razloga}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifest web-aplikacije ili uslužni alat ne udovoljavaju zahtjevima za instalaciju"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL aplikacije u Trgovini Play i ID u Trgovini Play ne podudaraju se"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Stranica je učitana u anonimnom prozoru"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Svojstvo manifesta `display` mora glasiti `standalone`, `fullscreen` ili `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest sad<PERSON><PERSON><PERSON> polje \"display_override\", pa prvi podr<PERSON>ani način prikaza mora biti \"standalone\", \"fullscreen\" ili \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest se ne može dohvatiti, prazan je ili se ne može raščlaniti"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL manifesta promijenio se tijekom dohvaćanja manifesta."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest ne sadr<PERSON> polje `name` ili `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest ne sadrži odgovaraj<PERSON>ću ikonu – potreban je PNG, SVG ili WebP format od najmanje {value0} px, mora biti postavljen atribut veličina, a atribut svrhe, ako je postavljen, mora sadr<PERSON><PERSON><PERSON> \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nijedna priložena ikona nije kvadrat od najmanje {value0} px u PNG, SVG ili WebP formatu te s atributom svrhe koji je poništen ili postavljen na \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Preuzeta ikona bila je prazna ili oštećena"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nije naveden ID u Trgovini Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Stranica nema URL manifesta <link>"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Uslužni alat ne može se provjeriti bez polja \"start_url\" u manifestu"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID pogreške instalacije \"{errorId}\" nije prepoznat"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Stranica se ne poslužuje iz sigurnog izvora"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Stranica se ne učitava u glavnom okviru"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Stranica ne radi izvanmrežno"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA je deinstaliran i provjere instalabilnosti vraćaju se na zadano."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Navedena platforma za aplikacije nije podržana na Androidu"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest navodi prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications podržan je samo na beta i stabilnom kanalu Chromea na Androidu."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse nije mogao odrediti može li se stranica instalirati. Pokušajte s novijom verzijom Chromea."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Početni URL manifesta nije važeći"}, "core/audits/installable-manifest.js | title": {"message": "Manifest web-aplikacije i uslužni alat udovoljavaju zahtjevima za instalaciju"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL u manifestu sadr<PERSON>i korisnič<PERSON> ime, zaporku ili priključak"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Stranica ne radi izvanmrežno. Nakon Chromea 93, stabilnog izdanja u kolovozu 2021., smatrat će se da se stranica ne može instalirati."}, "core/audits/is-on-https.js | allowed": {"message": "Dopušteno"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nesiguran URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Razr<PERSON>ša<PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Sve web-lokacije trebale bi biti zaštićene HTTPS-om, čak i one koje ne obrađuju osjetljive podatke. To uključuje izbjegavanje [mije<PERSON><PERSON><PERSON> sadržaja](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), odnosno učitavanja dijela resursa putem HTTP-a iako se početni zahtjev poslužuje putem HTTPS-a. HTTPS onemogućuje uljezima neovlašteno pristupanje komunikacijama između vaše aplikacije i vaših korisnika, kao i pasivno slušanje tih komunikacija. Preduvjet je za HTTP/2 i API-je brojnih novih web-platformi. [Saznajte više o HTTPS-u](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON><PERSON><PERSON> je jedan zahtjev koji nije siguran}one{Broj pronađenih zahtjeva koji nisu sigurni: #}few{Broj pronađenih zahtjeva koji nisu sigurni: #}other{Broj pronađenih zahtjeva koji nisu sigurni: #}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Ne upotrebljava HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Upotrebljava HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatski nadograđeno na HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Dopušteno uz upozorenje"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "Postotak vremena učitavanja punog sadržaja (LCP)"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Faza"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "<PERSON><PERSON>je<PERSON>"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Ovo je element s najvećim renderiranjem sadržaja unutar vidljivog dijela. [Saznajte više o elementu s najvećim renderiranjem sadržaja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Kašnjenje učitavanja"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Vrijeme učitavanja"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Kašnjenje <PERSON>"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element s naj<PERSON><PERSON><PERSON>"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Utjecaj pomaka izgleda"}, "core/audits/layout-shift-elements.js | description": {"message": "Pomaci izgleda najviše su utjecali na te DOM elemente. Neki pomaci izgleda možda neće biti uključeni u vrijednost mjernog podatka CLS-a zbog [prikaza u prozorima](https://web.dev/articles/cls#what_is_cls). [Saznajte kako poboljšati CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Izbjegavajte velike pomake izgleda"}, "core/audits/layout-shifts.js | columnScore": {"message": "Pokazatelj pomaka izgleda"}, "core/audits/layout-shifts.js | description": {"message": "To su najveći pomaci izgleda koji su primijećeni na stranici. Svaka stavka tablice predstavlja jedan pomak izgleda i prikazuje element koji se najviše pomaknuo. Ispod svake stavke navode se mogući glavni uzroci koji su doveli do pomaka izgleda. Neki pomaci izgleda možda neće biti uključeni u vrijednost mjernog podatka CLS-a zbog [prikaza u prozorima](https://web.dev/articles/cls#what_is_cls). [Saznajte kako poboljšati CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{<PERSON>nađen je jedan pomak izgleda}one{Pronađen je # pomak izgleda}few{Pronađena su # pomaka izgleda}other{Pronađeno je # pomaka izgleda}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Učitan web-font"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Ugra<PERSON><PERSON>"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "<PERSON><PERSON><PERSON> stranice promijenjen je zbog kasnog mrežnog zahtjeva"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Medijski element bez eksplicitne veličine"}, "core/audits/layout-shifts.js | title": {"message": "Izbjegavajte velike pomake izgleda"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Slike na vidljivom dijelu stranice koje se učitavaju s odgodom renderiraju se kasnije u životnom ciklusu stranice, što može odgoditi najveće renderiranje sadržaja. [Saznajte više o optimalnom odgođenom učitavanju](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Došlo je do odgođenog učitavanja slike s najvećim renderiranjem sadržaja"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Nije došlo do odgođenog učitavanja slike s najvećim renderiranjem sadržaja"}, "core/audits/long-tasks.js | description": {"message": "Navodi najduže zadatke na glavnoj niti, što je korisno za otkrivanje onih koji najviše doprinose kašnjenju unosa. [Saznajte kako izbjeći duge zadatke na glavnoj niti](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je # dugački zadatak}one{Pronađen je # dugački zadatak}few{Pronađena su # dugačka zadatka}other{Pronađeno je # dugačkih zadataka}}"}, "core/audits/long-tasks.js | title": {"message": "Izbjegavajte dugačke zadatke na glavnoj niti"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Savjetujemo vam da skratite vrijeme potrebno za raščlambu, kompiliranje i izvršavanje JS-a. Isporuka manjih JS-ova mogla bi vam pomoći da to postignete. [Saznajte kako smanjiti rad glavne niti](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizirajte rad glavne niti"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizira rad glavne niti"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Da bi dosegle najveći broj koris<PERSON>, web-lokacije bi trebale funkcionirati u svakom značajnijem pregledniku. [Saznajte više o kompatibilnosti s različitim preglednicima](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Web-lokacija funkcionira na različitim preglednicima"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Pobrinite se da se sve individualne stranice mogu dubinski povezivati putem URL-a i da su URL-ovi jedinstveni radi dijeljenja na društvenim mrežama. [Saznajte više o navođenju dubinskih veza](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Svaka stranica ima URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Pri dodirivanju stavki prijelazi bi trebali biti brzi, čak i na sporoj mreži. To je ključno za korisnikovu percepciju izvedbe. [Saznajte više o prijelazima stranica](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Ne čini se da se prijelazi stranica blokiraju na mreži"}, "core/audits/maskable-icon.js | description": {"message": "Ikona koja se može maskirati osigurava da slika ispunjava cjelokupan oblik, a da nije u letterbox formatu pri instaliranju aplikacije na uređaj. [Saznajte više o ikonama manifesta koje se mogu maskirati](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest nema ikonu koju je moguće maskirati"}, "core/audits/maskable-icon.js | title": {"message": "Manifest ima ikonu koju je moguće maskirati"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulativni pomak izgleda mjeri kretanje vidljivih elemenata u vidljivom dijelu. [Saznajte više o mjernom podatku Kumulativni pomak izgleda](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvo renderiranje sadržaja označava vrijeme renderiranja prvog teksta ili slike. [Saznajte više o mjernom podatku Prvo renderiranje sadržaja](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Prvo korisno renderiranje mjeri kada je vidljiv primarni sadržaj stranice. [Saznajte više o mjernom podatku Prvo korisno renderiranje](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Interakcija do sljedećeg renderiranja mjeri responzivnost stranice, koliko je vremena potrebno da stranica vidljivo reagira na korisnički unos. [Saznajte više o mjernom podatku Interakcija do sljedećeg renderiranja](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Vrijeme do interaktivnosti količina je vremena koje je potrebno da stranica postane potpuno interaktivna. [Saznajte više o mjernom podatku Vrijeme do interaktivnosti](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Najveće renderiranje sadržaja označava vrijeme renderiranja najvećeg teksta ili slike. [Saznajte više o mjernom podatku Najveće renderiranje sadržaja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Maksimalno potencijalno kašnjenje odgovora na prvi unos koje bi korisnik mogao doživjeti trajanje je najduljeg zadatka. [Saznajte više o mjernom podatku Maksimalno potencijalno kašnjenje odgovora na prvi unos](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Indeks brzine prikazuje koliko se brzo sadržaj stranice vidljivo popunjava. [Saznajte više o mjernom podatku Indeks brzine](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Zbroj svih razdoblja između PRS-a i vremena do interaktivnosti kad trajanje zadatka premašuje 50 ms, iskazan u milisekundama. [Saznajte više o mjernom podatku Ukupno vrijeme blokiranja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Vrijeme od slanja upita do primanja odgovora (RTT) za mrežu ima velik utjecaj na izvedbu. Ako je RTT do polazišta visok, to je znak da bi poslužitelji bliže korisniku mogli poboljšati izvedbu. [Saznajte više o vremenu od slanja upita do primanja odgovora](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Vrijeme od slanja upita do primanja odgovora za mrežu"}, "core/audits/network-server-latency.js | description": {"message": "Latencije poslužitelja mogu utjecati na izvedbu na webu. Ako je latencija poslužitelja polazišta visoka, to je znak da je poslužitelj preopterećen ili da ima lošu pozadinsku izvedbu. [Saznajte više o vremenu odgovora poslužitelja](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Pozadinske latencije poslužitelja"}, "core/audits/no-unload-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> `unload` ne aktivira se na pouzdan način i njegovo osluškivanje može spriječiti optimizacije preglednika kao što je predmemoriranje cijele stranice Umjesto toga upotrijebite događaj `pagehide` ili `visibilitychange`. [Saznajte više o uklanjanju slušatelja događaja](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registrira se kao slušatelj za događaj `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Izbjegava slušatelje događaja za `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animacije koje nisu kompozitne mogu biti lošije kvalitete i povisiti CLS. [Saznajte kako izbjeći nekompozitne animacije](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je # animirani element}one{Pronađen je # animirani element}few{Pronađena su # animirana elementa}other{Pronađeno je # animiranih elemenata}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Svojstvo koje se odnosi na filtar može pomicati piksele"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON>j ima neku drugu animaciju koja nije kompatibilna"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efekt sadrži kompozitni način koji nije \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Izbjegavajte animacije koje nisu kompozitne"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Svojstvo koje se odnosi na preobrazbu ovisi o veličini okvira"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepodržano svojstvo CSS-a: {properties}}one{Nepodržana svojstva CSS-a: {properties}}few{Nepodržana svojstva CSS-a: {properties}}other{Nepodržana svojstva CSS-a: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekt sadrži parametre tempiranja koji nisu podržani"}, "core/audits/performance-budget.js | description": {"message": "Količinu i veličinu mrežnih zahtjeva održavajte ispod ciljeva utvrđenih danim proračunom za izvedbu. [Saznajte više o proračunima za izvedbu](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{<PERSON><PERSON>}one{<PERSON><PERSON><PERSON> zahtje<PERSON>: #}few{<PERSON><PERSON><PERSON> zahtjeva: #}other{<PERSON><PERSON><PERSON> zahtjeva: #}}"}, "core/audits/performance-budget.js | title": {"message": "Proračun za izvedbu"}, "core/audits/preload-fonts.js | description": {"message": "Predučitajte fontove s opcijom `optional` kako bi ih novi posjetitelji mogli koristiti. [Saznajte više o predučitavanju fontova](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Fontovi s opcijom `font-display: optional` nisu predučitani"}, "core/audits/preload-fonts.js | title": {"message": "Predučitani su fontovi s opcijom `font-display: optional`"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ako se LCP element dinamično dodaje na stranicu, sliku trebate predučitati da biste poboljšali LCP. [Saznajte više o predučitavanju LCP elemenata](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Predučitavanje slike Najveće renderiranje sadržaja"}, "core/audits/redirects.js | description": {"message": "Preusmjeravanja uvode dodatna kašnjenja prije nego što se stranica može učitati. [Saznajte kako izbjeći preusmjeravanja stranica](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Izbjegavajte višestruka preusmjeravanja stranica"}, "core/audits/seo/canonical.js | description": {"message": "Kanonske veze ukazuju na to koji URL prikazati u rezultatima pretraživanja. [Saznajte više o kanonskim vezama](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Više URL-ova u sukobu ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Nevažeći URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Usmjerava na drugu `hreflang` lokaciju ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Nije apsolutni URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Upućuje na korijenski URL domene (početnu stranicu), a ne na ekvivalentnu stranicu sadržaja"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument ne sadrži valjanu vezu `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument ima valjani `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Veza koju nije moguće pretražiti i indeksirati"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Tražilice mogu koristiti atribute `href` na vezama da bi pretražile i indeksirale web-lokacije. Pobrinite se da atribut `href` sidrenih elemenata povezuje s odgovarajućim odredištem kako biste omogućili otkrivanje više stranica web-lokacije. [Saznajte kako omogućiti indeksiranje veza](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Veze se ne mogu pretražiti i indeksirati"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Veze se mogu pretražiti i indeksirati"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Dodatan nečitljiv tekst"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Postotak teksta na stranici"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Fontovi manji od 12 px premali su da bi bili čitljivi, pa posjetitelji na mobilnim uređajima moraju zumirati prstima. Neka više od 60% teksta na stranici ima veličinu od najmanje 12 px. [Saznajte više o čitljivim veličinama fontova](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} čitljivog teksta"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst nije čitljiv jer nijedna metaoznaka vidljivog dijela nije optimizirana za zaslone mobilnih uređaja."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokument ne sadrži čitljive veličine fontova"}, "core/audits/seo/font-size.js | legibleText": {"message": "Čitljiv tekst"}, "core/audits/seo/font-size.js | title": {"message": "Dokument upotrebljava čitljive veličine fontova"}, "core/audits/seo/hreflang.js | description": {"message": "Veze s atributom hreflang govore tražilicama koju verziju stranice trebaju navesti u rezultatima pretraživanja za određeni jezik ili regiju. [Saznajte više o `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument ne sadrži važe<PERSON>i `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativna href vrijednost"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument ima valjani `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Neočekivani jezični kôd"}, "core/audits/seo/http-status-code.js | description": {"message": "Stranice s neuspješnim HTTP kodom statusa možda se neće pravilno indeksirati. [Saznajte više o HTTP kodovima statusa](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Stranica ima neuspješan HTTP kôd statusa"}, "core/audits/seo/http-status-code.js | title": {"message": "Stranica ima uspješan HTTP kôd statusa"}, "core/audits/seo/is-crawlable.js | description": {"message": "Tražilice ne mogu uključiti vaše stranice u rezultate pretraživanja ako nemaju dopuštenje da ih pretraže i indeksiraju. [Saznajte više o smjernicama za alat za indeksiranje](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indeksiranje je stranice blokirano"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indeksiranje stranice nije blokirano"}, "core/audits/seo/link-text.js | description": {"message": "Opisni tekst veze pomaže tražilicama da razumiju vaš sadrž<PERSON>. [Saznajte kako veze učiniti pristupačnijima](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Pronađena je jedna veza}one{Pronađena je # veza}few{Pronađene su # veze}other{<PERSON>nađeno je # veza}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Veze nemaju opisni tekst"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON>eze sadr<PERSON><PERSON> deskriptivan tekst"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Pokrenite [alat za testiranje strukturiranih podataka](https://search.google.com/structured-data/testing-tool/) i [datoteku za provjeru koda (linter) strukturiranih podataka](http://linter.structured-data.org/) da biste potvrdili strukturirane podatke. [Saznajte više o strukturiranim podacima](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturirani su podaci važeći"}, "core/audits/seo/meta-description.js | description": {"message": "Metaopisi mogu se uključiti u rezultate pretraživanja kako bi se jezgrovito sažeo sadržaj stranice. [Saznajte više o metaopisu](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Tekst opisa nema sadržaj."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument ne sadrži metaopis"}, "core/audits/seo/meta-description.js | title": {"message": "Do<PERSON><PERSON> sadr<PERSON>"}, "core/audits/seo/plugins.js | description": {"message": "T<PERSON><PERSON><PERSON>ce ne mogu indeksirati sadr<PERSON><PERSON> dodata<PERSON>, pa mnogi uređaji ograničavaju dodatke ili ih ne podržavaju. [Saznajte više o izbjegavanju dodataka](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument upotrebljava dodatke"}, "core/audits/seo/plugins.js | title": {"message": "Dokument izbjegava dodatke"}, "core/audits/seo/robots-txt.js | description": {"message": "Ako je vaša robots.txt dato<PERSON><PERSON>, alati za indeksiranje možda neće moći razumjeti kako želite da se vaša web-lokacija pretraži ili indeksira. [Saznajte više o datoteci robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Zahtjevi za robots.txt vraćaju HTTP status: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Pronađena je jedna pogreška}one{Pronađena je # pogreška}few{Pronađene su # pogreške}other{Pronađeno je # pogrešaka}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nije uspio preuzeti robots.txt datoteku"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt nije valjan"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt je valjan"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktivni elementi kao što su gumbi i veze trebali bi biti dovoljno veliki (48 x 48 px) ili bi oko njih trebalo biti dovoljno prostora da ih se može lako dodirnuti bez preklapanja s drugim elementima. [Saznajte više o ciljevima dodira](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ciljeva dodira odgovarajuć<PERSON> velič<PERSON>"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Ciljevi dodira nisu dovoljno veliki jer nijedna metaoznaka vidljivog dijela nije optimizirana za zaslone mobilnih uređaja"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Ciljevi dodira nisu odgovaraj<PERSON>će veličine"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Preklapanje cilja"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "Ciljevi dodira odgovarajuće su veličine"}, "core/audits/server-response-time.js | description": {"message": "Neka vrijeme odgovora poslužitelja za glavni dokument bude kratko jer svi ostali zahtjevi ovise o njemu. [Saznajte više o mjernom podatku Vrijeme do prvog bajta](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Za korijenski dokument bilo je potrebno {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Skratite inicijalno vrijeme odgovora poslužitelja"}, "core/audits/server-response-time.js | title": {"message": "Inicijalno vrijeme odgovora poslužitelja bilo je kratko"}, "core/audits/splash-screen.js | description": {"message": "Tematski pozdravni zaslon pruža bolji doživljaj korisnicima koji pokreću vašu aplikaciju na početnom zaslonu. [Saznajte više o pozdravnim zaslonima](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "<PERSON>je konfigurirano za prilagođeni pozdravni zaslon"}, "core/audits/splash-screen.js | title": {"message": "Konfiguriran za prilagođeni pozdravni zaslon"}, "core/audits/themed-omnibox.js | description": {"message": "Adresna traka preglednika može se tematski podudarati s vašom web-lokacijom. [Saznajte više o tematskom povezivanju adresne trake](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Ne postavlja boju teme za adresnu traku."}, "core/audits/themed-omnibox.js | title": {"message": "Postavlja boju teme za adresnu traku."}, "core/audits/third-party-cookies.js | description": {"message": "Podrška za kolačiće trećih strana uklonit će se u budućoj verziji Chromea. [Saznajte više o ukidanju kolačića trećih strana](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>nađ<PERSON> je jedan kola<PERSON>}one{Pronađen je # kolačić}few{Pronađena su # kolačića}other{Pronađeno je # kolačića}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Upotrebljava kolačiće trećih strana"}, "core/audits/third-party-cookies.js | title": {"message": "Izbjegava kolačiće trećih strana"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (us<PERSON><PERSON><PERSON> kli<PERSON>)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (d<PERSON><PERSON>tvene mreže)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (videozapis)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Proizvod"}, "core/audits/third-party-facades.js | description": {"message": "Neka ugrađivanja trećih strana mogu se učitavati s odgodom. Savjetujemo da ih zamijenite vanjštinom dok ne budu potrebni. [Saznajte kako odgoditi treće strane s vanjštinom](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{dostupna je # alternativa s fasadom}one{dostupna je # alternativa s fasadom}few{dostupne su # alternative s fasadom}other{dostupno je # alternativa s fasadom}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Neki resursi trećih strana mogu se učitavati s odgodom pomoću fasade"}, "core/audits/third-party-facades.js | title": {"message": "Odgođeno učitavanje resursa trećih strana pomoću fasada"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "T<PERSON>ća strana"}, "core/audits/third-party-summary.js | description": {"message": "Kôd treće strane može znatno utjecati na izvedbu učitavanja. Ograničite broj suvišnih pružatelja trećih strana i pokušajte učitati kôd treće strane nakon završetka primarnog učitavanja vaše stranice. [Saznajte kako smanjiti utjecaj trećih strana](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON> treće strane blokirao je glavnu nit na {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Smanjite utjecaj koda trećih strana"}, "core/audits/third-party-summary.js | title": {"message": "Smanjenje upotrebe koda trećih strana"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Postavite proračun za tempiranje kako biste lakše pratili izvedbu na svojoj web-lokaciji. Web-lokacije koje funkcioniraju dobro brzo se učitavaju i reagiraju na korisničke unose. [Saznajte više o proračunima za izvedbu](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Proračun za tempiranje"}, "core/audits/unsized-images.js | description": {"message": "Postavite eksplicitnu širinu i visinu na elementima slika da bi se smanjili pomaci izgleda i poboljšao CLS. [Saznajte kako postaviti dimenzije slika](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Elementi slike nemaju eksplicitnu `width` i `height`"}, "core/audits/unsized-images.js | title": {"message": "Elementi slike imaju eksp<PERSON>nu `width` i `height`"}, "core/audits/user-timings.js | columnType": {"message": "Vrsta"}, "core/audits/user-timings.js | description": {"message": "Savjetujemo vam da na aplikaciju primijenite User Timing API radi mjerenja izvedbe aplikacije u stvarnom vremenu tijekom važnih korisničkih doživljaja. [Saznajte više o oznakama User Timinga](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{Jedno praćenje korisničkog vremena}one{# praćenje korisničkog vremena}few{# praćenja korisničkog vremena}other{# praćenja korisničkog vremena}}"}, "core/audits/user-timings.js | title": {"message": "Oznake i izmjere Praćenja korisničkog vremena"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Pronađen je element `<link rel=preconnect>` za \"{securityOrigin}\", ali ga preglednik nije upotrijebio. Provjerite upotrebljavate li atribut `crossorigin` pravilno."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Savjetuje<PERSON> vam da dodate `preconnect` ili `dns-prefetch` prilagodbe resursa radi uspostavljanja ranih veza s važnim izvorima trećih strana. [Saznajte kako se unaprijed povezati s traženim izvorima](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Rano se povežite s potrebnim izvorima"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Pronađeno je više od dviju veza `<link rel=preconnect>`. Te bi se veze trebale upotrebljavati štedljivo i samo za najvažnije izvore."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Pronađen je element `<link rel=preconnect>` za \"{securityOrigin}\", ali ga preglednik nije upotrijebio. Koristite `preconnect` samo za važne izvore koje će stranica sigurno zahtijevati."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Pronađen je element `<link>` za predučitavanje za \"{preloadURL}\", ali ga preglednik nije upotrijebio. Provjerite upotrebljavate li atribut `crossorigin` pravilno."}, "core/audits/uses-rel-preload.js | description": {"message": "Savjetuje<PERSON> vam da koristite `<link rel=preload>` da biste dali prednost dohvaćanju resursa koji se trenutačno traže kasnije u učitavanju stranice. [Saznajte kako predučitati ključne zahtjeve](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Unaprijed učitajte ključne zahtjeve"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL karte"}, "core/audits/valid-source-maps.js | description": {"message": "Karte izvora prevode umanjeni kôd u originalni izvorni kôd. To pomaže razvojnim programerima pri otklanjanju pogrešaka u produkciji. Osim toga, Lighthouse može pružiti daljnje uvide. Savjetujemo vam da primijenite karte izvora kako biste iskoristili te pogodnosti. [Saznajte više o kartama izvora](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Nedostaju karte izvora za veliki JavaScript prve strane"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "U velikoj datoteci JavaScripta nedostaje karta izvora"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Upozorenje: u atributu `.sourcesContent` nedostaje jedna stavka}one{Upozorenje: u atributu `.sourcesContent` nedostaje # stavka}few{Upozorenje: u atributu `.sourcesContent` nedostaju # stavke}other{Upozorenje: u atributu `.sourcesContent` nedostaje # stavki}}"}, "core/audits/valid-source-maps.js | title": {"message": "Stranica ima važeće karte izvora"}, "core/audits/viewport.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><meta name=\"viewport\">` ne samo da optimizira vašu aplikaciju za veličine zaslona na mobilnom uređaju već i sprječava [ kašnjenje povratnih informacija korisnika od 300 milisekundi](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Saznajte više o upotrebi metaoznake za vidljivi dio](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON>a oz<PERSON> `<meta name=\"viewport\">` nije pronađena"}, "core/audits/viewport.js | failureTitle": {"message": "Nema oznaku `<meta name=\"viewport\">` s `width` ili `initial-scale`"}, "core/audits/viewport.js | title": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s `width` ili `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "To je rad na blokiranju niti koji se događa tijekom mjerenja interakcije do sljedećeg renderiranja. [Saznajte više o mjernom podatku Interakcija do sljedećeg renderiranja](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms potrošeno na događaj {interactionType}"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimizirajte rad tijekom ključne interakcije"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Kašnjenje unosa"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Kašnjenje pre<PERSON>cije"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | title": {"message": "Minimizira rad tijekom ključne interakcije"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "To su prilike za poboljšanje uporabe sustava ARIA u vašoj aplikaciji, što može unaprijediti doživljaj za korisnike pomoćne tehnologije, primjerice čitača zaslona."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ovo su prilike za poboljšanje zamjenskog sadržaja za zvuk i videoprikaz. To može poboljšati doživljaj za korisnike s oštećenjem sluha ili vida."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk i videoprikaz"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ove stavke ističu uobičajene najbolje primjere iz prakse za pristupačnost."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Najbolji primjeri iz prakse"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ove provjere ističu prilike za [poboljšanje pristupačnosti vaše web-aplikacije](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatsko otkrivanje može otkriti samo podskup problema i ne jamči pristupačnost vaše web-aplikacije, stoga se preporučuje i [ručno testiranje](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Područja adresa za te stavke koja alat za automatizirano testiranje ne može pokriti. Saznajte više u našem vodiču o [provođenju pregleda pristupačnosti](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Pristupačnost"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "To su mogućnosti za poboljšanje čitljivosti vašeg sadržaja."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "To su prilike da unaprijedite tumačenje svojeg sadržaja za korisnike na različitim jezicima."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizacija i lokalizacija"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "To su prilike za poboljšanje semantike kontrola u vašoj aplikaciji. Na taj način možete poboljšati doživljaj za korisnike pomoćne tehnologije, primjerice čitača zaslona."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nazivi i oznake"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ovo su prilike za poboljšanje kretanja tipkovnicom u vašoj aplikaciji."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Kretanje"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ovo su prilike za poboljšanje doživljaja čitanja podataka u tablicama ili na popisima koristeći se asistivnom tehnologijom, primjerice čitačem zaslona."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tablice i popisi"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilnost preglednika"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Najbolji primjeri iz prakse"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Općenito"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Povjerenje i sigurnost"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Korisnički doživljaj"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Proračuni za izvedbu postavljaju standarde izvedbe vaše stranice."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Više informacija o izvedbi vaše aplikacije. Ovi brojevi ne [utječu izravno](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na rezultat izvedbe."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Dijagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najkritičniji je aspekt izvedbe brzina kojom se pikseli generiraju na zaslonu. Ključni mjerni podaci: <PERSON>rvo boje<PERSON><PERSON>, <PERSON><PERSON><PERSON> smisleno bojenje"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Poboljšanja prvog bojenja"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON>vi prijedlozi mogu vam pomoći da brže učitate stranicu. Oni ne [utječu izravno](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na rezultat izvedbe."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Poboljšajte općeniti doživljaj učitavanja tako da stranica bude responzivna i spremna za upotrebu što je prije moguće. Ključni mjerni podaci: Vrijeme do interaktivnosti, Indeks brzine"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Općenita poboljšanja"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Izvedba"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Ove provjere potvrđuju aspekte progresivne web-aplikacije. [Saznajte što čini dobru progresivnu web-aplikaciju](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Te su provjere potrebne za osnovni [PWA kontrolni popis](https://web.dev/articles/pwa-checklist), no Lighthouse ih ne izvodi automatski. One ne utječu na vaš rezultat, no važno je da ih ručno izvršite."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Može se instalirati"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA je optimiziran"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Ove provjere osiguravaju da vaša stranica slijedi osnovne preporuke u vezi s optimizacijom za tražilice. Mnogo je dodatnih čimbenika koje Lighthouse ne ocjenjuje, a koji mogu utjecati na položaj u pretraživanju, uključujući izvedbu povezanu s [ključnim pokazateljima web-stranice](https://web.dev/explore/vitals). [Saznajte više o smjernicama Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Pokrenite ove dodatne validatore na svojoj web-lokaciji da biste pregledali dodatne najbolje primjere iz prakse za SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatirajte HTML na način koji omogućuje alatima za indeksiranje da bolje razumiju sadržaj vaše aplikacije."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Najbolji primjeri sadržaja"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Da bi se vaša aplikacija pojavila u rezultatima pretraživanja, omogućite alatima za indeksiranje da joj pristupe."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Pretraživanje i indeksiranje"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Prilagodite svoje stranice mobilnim uređajima kako ih korisnici ne bi trebali sami povećavati da bi čitali sadržaj. [Saznajte kako prilagoditi stranice mobilnim uređajima](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Prilagođeno mobilnim uređajima"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Čini se da testirani uređaj ima sporiji procesor nego što Lighthouse očekuje. To može negativno utjecati na vaš rezultat izvedbe. Saznajte više o [kalibraciji odgovarajućeg množitelja usporavanja procesora](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Stranica se možda ne učitava kako je očekivano jer je vaš testni URL ({requested}) bio preusmjeren na {final}. Pokušajte izravno testirati drugi URL"}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Učitavanje stranice bilo je presporo da bi se dovr<PERSON><PERSON> unutar vremenskog ograničenja. Rezultati možda nisu potpuni."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Brisanje predmemorije preglednika je isteklo. Pokušajte ponovno pregledati ovu stranicu i prijaviti pogrešku ako se <PERSON> nastavi."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Na ovoj lokaciji možda ima pohranjenih podataka koji utječu na izvedbu učitavanja: {locations}. Provjerite stranicu u anonimnom prozoru da ti resursi ne bi utjecali na rezultate.}one{Na ovim lokacijama možda ima pohranjenih podataka koji utječu na izvedbu učitavanja: {locations}. Provjerite stranicu u anonimnom prozoru da ti resursi ne bi utjecali na rezultate.}few{Na ovim lokacijama možda ima pohranjenih podataka koji utječu na izvedbu učitavanja: {locations}. Provjerite stranicu u anonimnom prozoru da ti resursi ne bi utjecali na rezultate.}other{Na ovim lokacijama možda ima pohranjenih podataka koji utječu na izvedbu učitavanja: {locations}. Provjerite stranicu u anonimnom prozoru da ti resursi ne bi utjecali na rezultate.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Isteklo je vrijeme za brisanje izvornih podataka. Pokušajte ponovno pregledati ovu stranicu i prijaviti pogrešku ako se <PERSON> nastavi."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Pogreška prilikom raščlambe zaglavlja `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Tijekom pokretanja otkrivena je navigacija po stranici. Za reviziju navigacije po stranici ne preporučuje se upotreba načina vremenskog razdoblja. Navigacijski način rada upotrijebite da biste provjerili navigacije po stranicama radi bolje atribucije treće strane i otkrivanja glavne niti."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Samo stranice koje su učitane putem GET zahtjeva ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Jedino se stranice sa šifrom statusa 2XX mogu predmemorirati."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome je otkrio pokušaj izvršavanja JavaScripta dok je u predmemoriji."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Stranice koje su zatražile AppBanner trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog keepalive zahtjeva."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Oznake onemogućuju predmemoriranje cijele stranice. Otvorite chrome://flags/#back-forward-cache da biste ga lokalno omogućili na ovom uređaju."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Naredbeni redak onemogućio je predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog nedovoljno memorije."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Ovlaštenje ne podržava predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Predučitavač je onemogućio predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Stranica se ne može predmemorirati jer ima instancu BroadcastChannel s registriranim slušateljima."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Stranice sa zaglavljem cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Predmemorija je namjerno izbrisana."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Stranica je izbačena iz predmemorije kako bi se omogućilo predmemoriranje druge stranice."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Stranice koje sadrže dodatke trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Stranice koje koriste FileChooser API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Stranice koje koriste File System Access API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Stranice koje koriste otpremnik za medijski uređaj ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Tijekom napuštanja stranice u tijeku je bila reprodukcija Media Playera."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Stranice koje koriste MediaSession API i postave stanje reprodukcije ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Stranice koje koriste MediaSession API i postave rukovatelje radnjom ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog čitača zaslona."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Stranice koje koriste SecurityHandler ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Stranice koje koriste Serial API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Stranice koje koriste WebAuthetication API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Stranice koje koriste WebBluetooth API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Stranice koje koriste WebUSB API ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Predmemoriranje cijele stranice onemogućeno je jer su kolačići onemogućeni na stranici koja upotrebljava `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Stranice koje koriste predviđeni alat ili radni zadatak trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokument nije završio s učitavanjem prije napuštanja stranice."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Tijekom napuštanja stranice bio je prisutan natpis aplikacije."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Tijekom napuštanja stranice bio je prisutan Chromeov Upravitelj zaporki."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Tijekom napuštanja stranice u tijeku je bilo sažimanje DOM-a."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "Tijekom napuštanja stranice bio je prisutan preglednik za sažimanje DOM-a."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog proširenja koja koriste API za slanje poruka."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Proširenja s dugotrajnijom vezom trebaju zatvoriti vezu prije pristupanja predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Proširenja s dugotrajnijom vezom pokušala su poslati poruke okvirima u predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog proširenja."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Modalni dijalog, kao <PERSON>to je ponovno slanje obrasca ili dijalog sa zaporkom za http prikazao se za stranicu tijekom napuštanja stranice."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Tijekom napuštanja stranice prikazala se offline stranica."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Tijekom napuštanja stranice bila je prisutna traka za intervenciju zbog nedovoljno memorije."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Tijekom napuštanja stranice postojali su zahtjevi za dopuštenja."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Tijekom napuštanja stranice bio je prisutan alat za blokiranje skočnih prozora."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Pojedinosti o sigurnom pregledavanju prikazale su se tijekom napuštanja stranice."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON><PERSON>rno pregledavanje ovu je stranicu smatralo zlonamjernom i blokiralo je skočne prozore."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Uslužni alat aktiviran je dok je u tijeku bilo predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog pogreške dokumenta."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Stranice koje upotrebljavaju FencedFrames ne mogu se pohraniti u predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Stranica je izbačena iz predmemorije kako bi se omogućilo predmemoriranje druge stranice."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Stranice koje su odobrile pristup streamanju medijskih sadržaja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Stranice koje koriste portale trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Stranice koje koriste IdleManager trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Stranice koje imaju otvorenu IndexedDB vezu trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog IndexedDB događaja."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Ko<PERSON>šteni su API-ji koji ne ispunjavaju kriterije."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Stranice u koje je `JavaScript` ugrađen putem proširenja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Stranice u koje je `StyleSheet` ugrađen putem proširenja trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Interna pogreška."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Predmemoriranje cijele stranice onemogućeno je zbog keepalive zahtjeva."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Stranice koje koriste zaključavanje tipkovnice trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | loading": {"message": "Stranica nije završila s učitavanjem prije napuštanja stranice."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> glavni resurs ima cache-control:no-cache ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji glavni izvor ima cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Kretanje je otkazano prije nego što se stranica mogla vratiti iz predmemoriranja cijele stranice."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Stranica je izbačena iz predmemorije jer je aktivna mrežna veza primila previše podataka. Chrome ograničava količinu podataka koje stranica može primiti tijekom predmemoriranja."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stranice koje imaju dohvaćanje ili XHR u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Stranica je izbačena iz predmemoriranja cijele stranice jer je aktivni zahtjev za mrežu uključivao preusmjeravanje."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Stranica je izbačena iz predmemorije jer je mrežna veza bila predugo otvorena. Chrome ograničava količinu vremena tijekom kojeg stranica može primiti podatke za vrijeme predmemoriranja."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Stranice koje nemaju važeći odgovor ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Kretanje se dogodilo u okviru koji nije glavni."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Stranice s indeksiranim DB transakcijama trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Stranice sa zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Stranice sa zahtjevom za dohvaćanje mreže u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Stranice sa zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Stranice s XHR zahtjevom za mrežu u tijeku trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Stranice koje koriste PaymentManager trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Stranice koje koriste sliku u slici trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | portal": {"message": "Stranice koje koriste portale trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | printing": {"message": "Stranice koje prikazuju korisničko sučelje za ispisivanje trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Stranica je otvorena pomoću metode `window.open()`, a druga kartica navodi referencu na nju ili je stranica otvorila prozor."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Proces generiranja za stranicu u predmemoriranju cijele stranice se srušio."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Postupak generiranja za stranicu u predmemoriranju cijele stranice je prekinut."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Stranice koje su zatražile dopuštenja za snimanje zvučnih zapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Stranice koje su zatražile dopuštenja za senzor trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Stranice koje su zatražile pozadinsku sinkronizaciju ili dopuštenja za dohvaćanje trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Stranice koje su zatražile dopuštenja za MIDI trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Stranice koje su zatražile dopuštenja za obavijesti trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Stranice koje su zatražite pristup pohrani trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Stranice koje su zatražile dopuštenja za snimanje videozapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "<PERSON>ino se stranice čija je shema URL-a HTTP/HTTPS mogu predmemorirati."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Stranicu je preuzeo uslužni alat dok je u predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Uslužni alat pokušao je stranici kod koje je u tijeku predmemoriranje cijele stranice poslati `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Otkazana je registracija za ServiceWorker dok je u tijeku bilo predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Stranica je izbačena iz predmemoriranja cijele stranice zbog aktivacije uslužnog alata."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome se ponovno pokrenuo i izbrisao unose predmemoriranja cijele stranice."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Stranice koje koriste SharedWorker trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Stranice koje koriste SpeechRecognizer trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Stranice koje koriste SpeechSynthesis trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Iframe na stranici pokrenuo je kretanje koje nije dovršeno."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-cache ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON> čiji podresurs ima cache-control:no-store ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Stranica je premašila maksimalno vrijeme u predmemoriranju cijele stranice i istekla je."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Isteklo je pristupanje predmemoriranju cijele stranice (vjerojatno zbog rukovatelja sakrivanjem stranice koji su bili dugo pokrenuti)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Stranica ima unload rukovatelj u glavnom okviru."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Stranica ima unload rukovatelj u podokviru."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Preglednik je promijenio zaglavlje nadjačavanja korisničkog agenta."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Stranice koje su odobrile pristup snimanju videozapisa ili audiozapisa trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Stranice koje koriste WebDatabase trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Stranice koje koriste WebHID trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Stranice koje koriste WebLocks trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Stranice koje koriste WebNfc trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Stranice koje koriste WebOTPService trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Stranice s WebRTC-om ne mogu pristupiti predmemoriranju cijele stranice"}, "core/lib/bf-cache-strings.js | webShare": {"message": "Stranice koje koriste WebShare trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Stranice s WebSocketom ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Stranice s WebTransportom ne mogu pristupiti predmemoriranju cijele stranice."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Stranice koje koriste WebXR trenutačno ne ispunjavaju kriterije za predmemoriranje cijele stranice."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Savjetujemo vam da dodate https: i http: URL sheme (koje ignoriraju preglednici koji podržavaju `'strict-dynamic'`) radi kompatibilnosti s prijašnjim verzijama preglednika."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "`disown-opener` je obustavljen od pravila CSP3. Upotrijebite zaglavlje Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "`referrer` je obustavljen od pravila CSP2. Upotrijebite zaglavlje Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "`reflected-xss` je obustavljen od pravila CSP2. Upotrijebite zaglavlje X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Smjernica `base-uri` koja nedostaje dopušta ugrađenim oz<PERSON> `<base>` da postave osnovni URL za sve relativne URL-ove (npr. skripte) na domenu kojom upravljaju napadači. Savjetujemo vam da `base-uri` postavite na `'none'` ili `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Smjernica `object-src` koja nedostaje omogućuje ugradnju dodataka koji izvršavaju nesigurne skripte. Savjetujemo vam da `object-src` postavite na `'none'` ako mo<PERSON>."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Ned<PERSON><PERSON><PERSON> s<PERSON>jer<PERSON> `script-src`. To može omogućiti izvršavanje nesigurnih skripti."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Jeste li zabora<PERSON> točku sa zarezom? <PERSON><PERSON> se da je {keyword} direktiva, a ne ključna riječ."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonceovi trebaju upotrebljavati base64 skup znakova."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonceovi trebaju imati najmanje osam znakova."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Izbjegavajte upotrebu URL shema ({keyword}) u direktivi. Sheme običnih URL-ova omogućuju skripte s nesigurne domene."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Izbjegavajte upotrebu običnih zamjenskih znakova ({keyword}) u direktivi. Obični zamjenski znakovi omogućuju skripte s nesigurne domene."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Odredište izvješća konfigurira se samo putem smjernice report-to. Ta je smjernica podržana samo u preglednicima koji se temelje na Chromiumu, pa se preporučuje da se upotrijebi i smjernica `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nijedan CSP ne konfigurira odredište izvješća. To otežava održavanje CSP-a tijekom vremena i nadzor mogućih prekida."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Popis dopuštenih hostova često se može zaobići. Savjetujemo vam da umjesto toga upotrijebite CSP jednokratne ključeve ili hasheve, kao i `'strict-dynamic'` ako je potre<PERSON>no."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Nepoznata direktiva CSP-a."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON> se da {keyword} nije važeća ključna riječ."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` omogućuje izvršavanje nesigurnih skripti i rukovatelja događajima unutar stranice. Savjetujemo vam da koristite CSP jednokratne ključeve ili hasheve kako biste omogućivali skripte pojedinačno."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Savjetuje<PERSON> vam da dodate `'unsafe-inline'` (koji ignoriraju preglednici koji podržavaju jednokratne ključeve/hashove) radi kompatibilnosti s prijašnjim verzijama preglednika."}, "core/lib/deprecation-description.js | feature": {"message": "Više pojedinosti potražite na stranici statusa značajki."}, "core/lib/deprecation-description.js | milestone": {"message": "Promjena će stupiti na snagu uz prekretnicu {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Koristi se obustavljena značajka"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Autorizacija neće biti pokrivena zamjenskim znakom (*) u rukovanju CORS zaglavljem `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Atribut `disableRemotePlayback` trebao bi se koristiti kako bi se onemogućila zadana integracija Casta umjesto upotrebe selektora `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Blokirani su zahtjevi za resurse čiji su URL-ovi sadržavali uklonjene znakove razmaka `(n|r|t)` i znakove \"manje od\" (`<`). Uklonite nove retke i kodirajte znakove \"manje od\" s mjesta kao što su vrijednosti atributa elemenata kako bi se ti resursi učitali."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` je obustavljen, upotrijebite standardizirani API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` je obustavl<PERSON>n, upotrijebite standardizirani API: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` je obustavljen, upotrijebite standardizirani API: `nextHopProtocol` u Navigation Timingu 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Kolačići koji sadrže znak `(0|r|n)` neće se skratiti već će se odbiti."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Popuštanje pravila istog porijekla postavljanjem `document.domain` obustavlja se i onemogućit će se prema zadanim postavkama. Ovo upozorenje o obustavljanju odnosi se na pristupanje iz više izvora koje je bilo omogućeno postavkom `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Pokretanje window.alert iz iframea različitih izvora obustavlja se i ubuduće će biti uklonjeno."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Pokretanje API-ja window.confirm iz iframesa različitih izvora obustavlja se i ubuduće će biti uklonjeno."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Obustavljeni su događaji mutacije DOM-a, ukl<PERSON><PERSON><PERSON><PERSON><PERSON>i `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` i `DOMCharacterDataModified` (https://w3c.github.io/uievents/#legacy-event-types) i uklonit će se. Umjesto toga koristite `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Podrška za podatke: URL-ovi u SVG elementu <use> obustavljaju se i uklanjaju."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Popuštanje pravila istog porijekla postavljanjem `document.domain` obustavlja se i onemogućit će se prema zadanim postavkama. Da biste nastavili koristiti tu značajku, isključite grupe agenata s ključem porijekla slanjem zaglavlja `Origin-Agent-Cluster: ?0` uz HTTP odgovor za dokument i okvire. Više pojedinosti pročitajte na vezi https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Zaglavlje `Expect-CT` obustavlja se i uklonit će se. Chrome zahtijeva Transparentnost certifikata za sve javno pouzdane certifikate izdane nakon 30. travnja 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "`getCurrentPosition()` i `watchPosition()` više ne rade na nesigurnim izvorima. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` i `watchPosition()` obustavljaju se na nesigurnim izvorima. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` više ne funkcionira na nesigurnim izvorima. Da biste upotrebljavali tu zna<PERSON>, trebali biste razmisliti o prebacivanju svoje aplikacije na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` je obustavljen. Upotrijebite `RTCPeerConnectionIceErrorEvent.address` ili `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Podaci o izvoru trgovca i proizvoljni podaci o događaju uslužnog alata `canmakepayment` obustavljeni su te će se ukloniti: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Web-lokacija je zatražila podresurs s mreže kojem može pristupiti samo zbog privilegiranog položaja svojih korisnika u mreži. Ti zahtjevi internetu izlažu uređaje i poslužitelje koji nisu javni, povećavajući opasnost od napada krivotvorenjem zahtjeva s druge web-lokacije (CSRF) i/ili curenja informacija. Da bi smanjio te opasnosti, Chrome obustavlja zahtjeve podresursima koji nisu javni kad se pokreću iz nesigurnih konteksta i počet će ih blokirati."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Polje `dailyUpdateUrl` u strukturi `InterestGroups` koje je proslijeđeno na `joinAdInterestGroup()` preimenovano je u `updateUrl` kako bi preciznije odražavalo njegovo ponašanje."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "CSS se ne može učitati s `file:` URL-ova osim ako završavaju na datotečni nastavak `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Upotreba `SourceBuffer.abort()` za prekid uklanjanja asinkronog raspona za `remove()` obustavljena je zbog promjene specifikacija. Podrška će se ukloniti u budućnosti. Umjesto toga trebali biste osluškivati događaj `updateend`. `abort()` može prekinuti samo stanje dodavanja asinkronog medija ili raščlanjivača vraćanja na zadano."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Postavljanje atributa `MediaSource.duration` ispod najviše vremenske oznake prezentacije bilo kojeg kodiranog okvira spremljenog u međuspremnik obustavljeno je zbog promjene specifikacija. Podrška za implicitno uklanjanje skraćenog medija spremljenog u međuspremnik uklonit će se. Umjesto toga trebali biste izvesti eksplicitni `remove(newDuration, oldDuration)` na svim `sourceBuffers`, gdje je `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI tražit će dopuštenje za upotrebu čak i ako sysex nije naveden u `MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Obustavljen je stariji nestandardizirani atribut `shadowroot` vi<PERSON>e *neće funkcionirati* u verziji M119. Umjesto njega koristite novi, standardizirani atribut `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Notification API možda se više neće upotrebljavati iz nesigurnih izvora. Savjetujemo vam da svoju aplikaciju prebacite na siguran izvor kao što je HTTPS. Više pojedinosti pročitajte na vezi https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Možda se više neće tražiti dopuštenje za Notification API iz iframea različitih izvora. Trebali biste razmisliti o traženju dopuštenja okvira najviše razine ili o otvaranju novog prozora."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Opcija `imageOrientation: 'none'` u stavci CreateImageBitmapa je obustavljena. Umjesto nje upotrijebite oznaku createImageBitmap s opcijom \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON>š partner <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (D)TLS-a. <PERSON><PERSON> s partner<PERSON> kako biste to is<PERSON><PERSON>."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON> nave<PERSON> `overflow: visible` za oznake img, video i canvas, te oznake mogu proizvesti vizualni sadržaj izvan granica elemenata. Pogledajte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "`paymentManager.instruments` je obustavljen. Umjesto toga koristite just-in-time instalaciju za rukovatelje plaćanjem."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Vaš je poziv `PaymentRequest` zaobilazio direktivu `connect-src` Pravila o sigurnosti sadržaja (CSP). To je zaobilaženje obustavljeno. Dodajte identifikator načina plaćanja iz `PaymentRequest` API-ja (u polju `supportedMethods`) direktivi `connect-src` CSP-a."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "`StorageType.persistent` je obustavl<PERSON>. Umjesto njega upotrijebite standardizirani `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "`<source src>` s nadre<PERSON><PERSON><PERSON> `<picture>` nevaž<PERSON>ći je i stoga se zanemaruje. Umjesto toga koristite `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame odnosi se na dobavljača. Upotrijebite standardni cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame odnosi se na dobavljača. Umjesto njega upotrijebite standardni requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingfullscreen je obustavljen. Umjesto njega koristite Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() je obustavljen. Umjesto njega koristite Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() je obustavljen. Umjesto njega koristite Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() je obustavljen. Umjesto njega koristite Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() je obustavljen. Umjesto njega koristite Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen je obustavljen. Umjesto njega koristite Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Obustavljamo API `chrome.privacy.websites.privacySandboxEnabled`, ali <PERSON>e ostati aktivan za kompatibilnost s prijašnjim verzijama do izdanja M113. Umjesto toga upotrijebite `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` i `chrome.privacy.websites.adMeasurementEnabled`. Posjetite https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Uklonjeno je ograničenje `DtlsSrtpKeyAgreement`. Naveli ste vrijednost `false` za ograni<PERSON>, što se tumači kao pokušaj korištenja uklonjene metode `SDES key negotiation`. Ta je funkcija uklonjena. Umjesto nje koristite uslugu koja podržava `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Uklonjeno je ograničenje `DtlsSrtpKeyAgreement`. Naveli ste vrijednost `true` za to ograničenje, što nije im<PERSON>, ali mož<PERSON> uklon<PERSON> to ograničenje radi jas<PERSON>će."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "GetStats() na temelju poziva obustavljen je i uklonit će se. Umjesto toga upotrijebite getStats() usklađen sa specifikacijama."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Range.expand() je obustavljen. Umjesto njega upotrijebite Select.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Blokirani su zahtjevi podresursa čiji URL-ovi sadrže ugrađene vjerodajnice (npr. `**********************/`)."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Opcija `rtcpMuxPolicy` obustavlja se i uklonit će se."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` će zahtijevati izoliranje unakrsnih izvora. Više pojedinosti pročitajte na vezi https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` bez aktivacije korisnika obustavlja se i uklonit će se."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Proširenja se trebaju uključiti u izoliranje unakrsnih izvora da bi se nastavio upotrebljavati `SharedArrayBuffer`. Posjetite https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Web SQL je obustavljen. Upotrijebite SQLite WebAssembly ili indeksiranu bazu podataka"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Obustavljen je opisnik dopuštenja `window-placement`. Umjesto toga upotrijebite pravilo `window-management`. Za dodatnu pomoć posjetite https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Obustavljeno je pravilo o dopuštenjima `window-placement`. Umjesto toga upotrijebite pravilo `window-management`. Za dodatnu pomoć posjetite https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "JSON odgovor ne podržava UTF-16 u `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinkroni `XMLHttpRequest` na glavnoj niti obustavlja se zbog negativnog utjecaja na doživljaj krajnjeg korisnika. Za dodatnu pomoć posjetite https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Metoda `supportsSession()` je obustavljena. Umjesto toga koristite `isSessionSupported()` i označite riješenu Booleovu vrijednost."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Vrijeme blokiranja glavne niti"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL predmemoriranja"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Opis"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementi koji nisu pro<PERSON> provjeru"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Lokacija"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Više od proračuna"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Veličina resursa"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Vrsta resursa"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Veličina"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Vrijeme početka"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Utrošeno vrijeme"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Veličina prijenosa"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potencijalna ušteda"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potencijalna ušteda"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potencijalna ušteda {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Pronađen je jedan element}one{Pronađen je # element}few{Pronađena su # elementa}other{Pronađeno je # elemenata}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potencijalna ušteda {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON>r<PERSON> s<PERSON>o bo<PERSON>"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Slika"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interakcija do sljedećeg renderiranja"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Visok"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Srednje"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maks. potencijalno kašnjenje odgovora na prvi unos"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Drugo"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON> resursi"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Skripta"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "List stila"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "T<PERSON>ća strana"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Ukupno"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Nešto nije u redu sa snimanjem traga preko učitavanja stranice. Ponovno pokrenite Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Isteklo je vrijeme čekanja za inicijalnu vezu za protokol za otklanjanje pogrešaka."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome nije prikupio nikakve snimke zaslona tijekom učitavanja stranice. Provjerite je li sadržaj vidljiv na stranici, a zatim pokušajte ponovno pokrenuti Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS poslužitelji nisu mogli razriješiti navedenu domenu."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Potrebni prikupljač {artifactName} naišao je na pogrešku: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Došlo je do interne pogreške u Chromeu. Ponovo pokrenite Chrome i pokušajte ponovo pokrenuti Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON>je se pokrenuo potrebni prikupljač {artifactName}."}, "core/lib/lh-error.js | noFcp": {"message": "Stranica nije prikazala nikakav sadržaj. Pobrinite se da prozor preglednika ostane u prednjem planu tijekom učitavanja i pokušajte ponovo. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Na stranici se nije prikazivao sadržaj koji ispunjava uvjete za najveće renderiranje sadržaja (LCP). Provjerite ima li stranica važeći LCP element i pokušajte ponovno. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Navedena stranica nije HTML (poslužuje se kao vrsta MIME-a {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ova je verzija Chromea prestara da bi pod<PERSON>žavala \"{featureName}\". Upotrijebite noviju verziju da biste vidjeli potpune rezultate."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nije mogao pouzdano učitati URL koji ste zatražili jer je stranica prestala reagirati."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL koji ste naveli nema valjani sigurnosni certifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome je spriječio međuprostorno učitavanje stranice. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve. (Pojedinosti: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve. (Šifra statusa: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Učitavanje stranice trajalo je predugo. Slijedite mogućnosti u izvješću da biste smanjili vrijeme učitavanja stranice, a zatim pokušajte ponovno pokrenuti Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Čekanje odgovora protokola DevTools premašilo je dodijeljeno vrijeme. (Način: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Dohvaćanje sadržaja resursa premašilo je dodijeljeno vrijeme"}, "core/lib/lh-error.js | urlInvalid": {"message": "Čini se da URL koji ste naveli nije važeći."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve. (Šifra statusa: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Vrsta MIME-a stranice je XHTML: Lighthouse ne podržava izričito tu vrstu dokumenta"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON><PERSON><PERSON> koris<PERSON> ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Izvješće o kretanju ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Izvješće o snimci ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Izvješće o razdoblju ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Sva izvješća"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kategorije"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Pristupačnost"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Najbolji primjeri iz prakse"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Izvedba"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresivna web-aplikacija"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Razumijevanje Lighthouseovog izvješća o putovima"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Upotrijebite izvješća o kretanju za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Upotrijebite izvješća o snimkama za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Upotrijebite izvješća o razdoblju za..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "dobivanje rezultata izvedbe za Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "mjerenje pokazatelja izvedbe učitavanja stranica kao što su najveće renderiranje sadržaja i indeks brzine"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "procjenu mogućnosti progresivne web-aplikacije"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "pronalaženje problema s pristupačnošću u jednostraničnim aplikacijama ili složenim obrascima"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "procjenu najboljih primjera iz prakse za izbornike i elemente korisničkog sučelja skrivene iza interakcije"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "mjerenje pomaka izgleda i vremena izvršavanja JavaScripta u nizu interakcija"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "otkrivanje prilika za izvedbu radi poboljšanja doživljaja za dugotrajne stranice i jednostranične aplikacije"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Najviši utjecaj"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativna revizija}one{{numInformative} informativna revizija}few{{numInformative} informativne revizije}other{{numInformative} informativnih revizija}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Učitavanje stranice"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Izvješća o kretanju analiziraju učitavanje jedne stranice, jednako kao i izvorna Lighthouseova izvješća."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Izvješće o kretanju"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} izv<PERSON><PERSON>će o kretanju}one{{numNavigation} izv<PERSON><PERSON>će o kretanju}few{{numNavigation} izv<PERSON><PERSON><PERSON><PERSON> o kretanju}other{{numNavigation} izv<PERSON><PERSON>ća o kretanju}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} prolazna revizija}one{{numPassableAudits} prolazna revizija}few{{numPassableAudits} prolazne revizije}other{{numPassableAudits} prolaz<PERSON>h revizija}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} usp<PERSON><PERSON>na revizija}one{{numPassed} uspje<PERSON>na revizija}few{{numPassed} usp<PERSON><PERSON>ne revizije}other{{numPassed} usp<PERSON><PERSON><PERSON>h revizija}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Prosječno"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Pogreška"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Dobro"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Sp<PERSON>i"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Snimljeno stanje stranice"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Izvješća o snimkama analiziraju stranicu u određenom stanju, obično nakon interakcija korisnika."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Izvješće o snimci"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} izv<PERSON><PERSON><PERSON><PERSON> o snimci}one{{numSnapshot} izv<PERSON><PERSON><PERSON><PERSON> o snimci}few{{numSnapshot} izv<PERSON><PERSON><PERSON><PERSON> o snimci}other{{numSnapshot} izv<PERSON><PERSON><PERSON><PERSON> o snimci}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Sažetak"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Korisničke interakcije"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Izvješća o razdoblju analiziraju proizvoljno razdoblje, koje obično obuhvaća korisničke interakcije."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Izvješće o razdoblju"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} izvješće o razdoblju}one{{numTimespan} izvješće o razdoblju}few{{numTimespan} izv<PERSON><PERSON>ća o razdoblju}other{{numTimespan} izvješća o razdoblju}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouseovo izvješće o putovima korisnika"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Za animirani sadržaj upotrijebite [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) da biste smanjili upotrebu procesora kad sadržaj nije na zaslonu."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Razmislite o prikazivanju svih komponenti [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) u WebP formatima uz određivanje odgovarajuće zamjene za druge preglednike. [Saznaj<PERSON> više](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Pobrinite se da upotrebljavate [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) kako bi se slike automatski učitavale s odgodom. [Saznajte više](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Upotrebljavajte alate kao <PERSON> je [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) za [generiranje AMP izgleda na poslužitelju](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "U [AMP dokumentaciji](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) provjerite jesu li svi stilovi pod<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponenta [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) podržava atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) za određivanje slikovnih elemenata koji će se upotrebljavati ovisno o veličini zaslona. [Saznajte više](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "U slučaju generiranja vrlo velikih popisa savjetujemo vam virtualno pomicanje pomoću paketa Component Dev Kit (CDK). [Saznajte više](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Primijenite [razdvajanje koda na razini rute](https://web.dev/route-level-code-splitting-in-angular/) da biste smanjili veličinu JavaScript paketa. Također razmislite o predmemoriranju elemenata pomoću [uslu<PERSON><PERSON>g alata Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON> up<PERSON>l<PERSON>vate Angular CLI, međuverzije se trebaju generirati u produkcijskom načinu rada. [Saznajte više](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "<PERSON><PERSON> up<PERSON>ljavate Angular CLI, uključite karte izvora u produkcijsku međuverziju kako biste provjerili pakete. [Saznajte više](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Unaprijed predučitajte rute da biste ubrzali navigaciju. [Saznajte više](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Za upravljanje prijelomnim točkama slike savjetujemo vam upotrebu uslužnog programa `BreakpointObserver` u paketu Component Dev Kit (CDK). [Saznajte više](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Savjetujemo vam da prenesete GIF na uslugu na kojoj će se kodirati za ugrađivanje kao HTML5 videozapis."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Navedite `@font-display` prilikom definiranja prilagođenih fontova u svojoj temi."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Uzmite u obzir konfiguriranje [WebP formata slika stilom Pretvaranje slike](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) na web-lokaciji."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instalirajte [Drupalov modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) koji može učitavati slike s odgodom. Takvi moduli omogućuju odgađanje svih slika koje nisu na zaslonu kako bi se poboljšala izvedba."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Razmislite o modulu kojim bi se ugradili ključni CSS i JavaScript te upotrijebite atribut odgode za CSS ili JavaScript koji nisu ključni."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Teme, moduli i specifikacije poslužitelja produljuju vrijeme odgovora poslužitelja. Savjetujemo vam da pronađete optimiziraniju temu, pažljivo odaberete modul za optimizaciju i/ili nadogradite poslužitelja. Hosting poslužitelji trebali bi koristiti predmemoriranje za PHP opkôd, predmemoriranje radi smanjenja vremena odgovaranja na upit iz baze podataka kao što su Redis ili Memcached, kao i optimiziranje logike aplikacije kako bi se stranice brže pripremile."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Savjetujemo vam da upotrebljavate [responzivne stilove slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) kako biste smanjili veličinu slika učitanih na vašu stranicu. Ako koristite prikaze za prikazivanje više stavki sadržaja na stranici, savjetujemo vam da numeriranjem stranica ograničite broj stavki sadržaja koje se prikazuju na toj stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Provjerite jeste li aktivirali opciju Agregiraj CSS datoteke na stranici Administracija » Konfiguracija » Razvoj.  Osigurajte da vaša Drupal web-lokacija radi barem na platformi Drupal 10.1 radi poboljšane podrške za agregaciju elemenata."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Provjerite jeste li aktivirali opciju Agregiraj JavaScript datoteke na stranici Administracija » Konfiguracija » Razvoj.  Osigurajte da vaša Drupal web-lokacija radi barem na platformi Drupal 10.1 radi poboljšane podrške za agregaciju elemenata."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Razmislite o uklanjanju nekorištenih pravila CSS-a te relevantnoj stranici ili komponenti stranice priložite samo potrebne Drupalove zbirke. Pojedinosti pročitajte na [vezi na Drupalove dokumente](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Da biste pronašli priložene zbirke koje dodaju suvišan CSS, pokušajte pokrenuti [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovornu temu ili modul možete pronaći u URL-u list stilova kad je na web-lokaciji Drupala onemogućena agregacija CSS-a. Obratite pažnju na teme ili module koji na popisu imaju mnogo stilskih tablica s mnogo crvenog u pokrivenosti koda. Tema ili modul trebali bi postaviti list stilova u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Razmislite o uklanjanju nekorištenih elemenata JavaScripta te relevantnoj stranici ili komponenti stranice priložite samo potrebne Drupalove biblioteke. Pojedinosti pročitajte na [vezi na Drupalovu dokumentaciju](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Da biste pronašli priložene biblioteke koje dodaju suvišan JavaScript, pokušajte pokrenuti [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovornu temu ili modul možete pronaći u URL-u skripte kad je na Drupalovoj web-lokaciji onemogućena JavaScriptova agregacija. Obratite pažnju na teme ili module koji na popisu imaju mnogo skripti s mnogo crvenog u pokrivenosti koda. Tema ili modul trebali bi postaviti skriptu u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "<PERSON>avi<PERSON> \"Maksimalno trajanje predmemorije preglednika i proxyja\" na stranici \"Administracija » Konfiguracija » Razvoj\". Pročitajte o [Drupalovoj predmemoriji i optimiziranju za rad](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Savjetujemo vam da upotrebljavate [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) koji automatski optimizira i smanjuje veličinu slika prenesenih preko web-lokacije te istodobno zadržava kvalitetu. Također provjerite koristite li izvorne [responzivne stilove slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) koje pruža Drupal (dostupni u Drupalu 8 i novijim verzijama) za sve slike generirane na web-lokaciji."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Nagovještaji resursa za rano povezivanje ili prethodno dohvaćanje DNS-a mogu se dodati instaliranjem i konfiguriranjem [modula](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) koji pruža infrastrukturu za nagovještaje resursa korisničkog agenta."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Provjerite koristite li izvorne [responzivne stilove slika](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) koje pruža Drupal (dostupni u Drupalu 8 i novijim verzijama). Responzivne stilove slika koristite kada generirate slikovna polja kroz načine prikaza, prikaze ili slike prenesene WYSIWYG uređivačem."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Optimize Fonts` da biste automatski iskoristili `font-display` CSS značajku kako bi tekst bio vidljiv korisnicima dok se web-fontovi učitavaju."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Next-Gen Formats` radi konvertiranja slika u WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `<PERSON><PERSON> Images` da bi se odgodilo učitavanje slika izvan zaslona dok ne budu potrebne."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Critical CSS` i `Script Delay` da bi se odgodio nekritični JS/CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Koristite [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) kako biste predmemorirali svoj sadržaj na našoj svjetskoj mreži i poboljšali vrijeme do prvog bajta."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Minify CSS` da biste automatski umanjili CSS kako bi se smanjile veličine mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Minify Javascript` da biste automatski umanjili JS kako bi se smanjile veličine mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Remove Unused CSS` radi lak<PERSON> rješavanja tog problema. Identificirat će CSS klase koje se doista koriste na svakoj stranici vaše web-lokacije i ukloniti sve druge kako bi datoteka ostala male veličine."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Efficient Static Cache Policy` da biste postavili preporučene vrijednosti u zaglavlju predmemoriranja za statičke elemente."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Next-Gen Formats` radi konvertiranja slika u WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Pre-Connect Origins` da biste automatski dodali nagovještaje resursa `preconnect` radi uspostavljanja ranih veza s važnim izvorima trećih strana."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Preload Fonts` i `Preload Background Images` da biste dodali veze za `preload` kako bi se prednost dala dohvaćanju resursa koji se trenutačno traže kasnije tijekom učitavanja stranice."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Koristite [Ezoic Leap](https://pubdash.ezoic.com/speed) i omogućite `Resize Images` da bi se veličina slika promijenila ovisno o uređaju i time smanjila veličina mrežnih resursa."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Upotrijebite komponentu `gatsby-plugin-image` umjesto komponente `<img>` da biste automatski optimizirali format slike. [Saznajte više](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Upotrijebite komponentu `gatsby-plugin-image` umje<PERSON> komponente `<img>` da bi se slike automatski učitale s odgodom. [Saznajte više](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Upotrijebite komponentu `gatsby-plugin-image` i postavite objekt `loading` na `eager`. [Saznajte više](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Pomoću komponente `Gatsby Script API` odgodite učitavanje nekritičnih skripti trećih strana. [Saznajte više](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON> `PurgeCSS` `Gatsby` uklonite nekorištena pravila s listova stilova. [Saznajte više](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Koristite `Webpack Bundle Analyzer` za otkrivanje nekorištenog JavaScript koda. [Saznajte više](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Konfigurirajte predmemoriranje za nepromjenjive elemente. [Saznajte više](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Upotrijebite komponentu `gatsby-plugin-image` umje<PERSON> komponente `<img>` da biste prilagodili kvalitetu slike. [Saznajte više](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponentu `gatsby-plugin-image` da biste postavili od<PERSON> `sizes`. [Saznajte više](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Savjetujemo vam da prenesete GIF na uslugu na kojoj će se kodirati za ugrađivanje kao HTML5 videozapis."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Savjet<PERSON>je<PERSON> vam upotrebu [dodatka](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ili usluge koji će automatski konvertirati prenesene slike u optimalne formate."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instalirajte [Jo<PERSON><PERSON> dodatak za učitavanje s odgodom](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) koji pruža mogućnost odgode slika koje nisu na zaslonu ili prijeđite na predložak koji pruža tu funkciju. Počevši od Joomle 4.0, sve nove slike [automatski](https://github.com/joomla/joomla-cms/pull/30748) će dobiti atribut `loading` od jezgre."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON> dodaci omogućuju vam da [ugradite kritične elemente](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ili [odgodite nevažnije resurse](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Upozoravamo da optimizacije koje pružaju ti dodaci mogu oštetiti značajke vaših predložaka ili dodataka, pa ćete ih trebati temeljito testirati."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, proširenja i specifikacije poslužitelja produljuju vrijeme odgovora poslužitelja. Savjetujemo vam da pronađete optimiziraniji predložak, pažljivo odaberete proširenje za optimizaciju i/ili nadogradite poslužitelj."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Savjetujemo vam da prikažete odlomke u kategorijama članaka (na primjer pomoću veze \"pročitajte više\"), smanjite broj članaka koji se prikazuju na određenoj stranici, razlomite dugačke postove na više stranica ili koristite dodatak za lijeno učitavanje komentara."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> pro<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem css stilova. Postoje i predlošci koji nude tu funkcionalnost."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> pro<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem skripti. Postoje i predlošci koji nude tu funkcionalnost."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Savjetujemo vam da smanjite ili isključite broj [<PERSON><PERSON><PERSON><PERSON> proširenja](https://extensions.joomla.org/) koja učitavaju CSS koji se ne koristi na vašoj stranici. Da biste pronašli proširenja koja dodaju suvišan CSS, pokrenite [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovornu temu ili dodatak možete pronaći u URL-u stilske tablice. Obratite pažnju na dodatke koji na popisu imaju mnogo stilskih tablica s mnogo crvenog u pokrivenosti koda. Dodatak bi trebao postaviti stilsku tablicu u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Savjetujemo vam da smanjite ili isključite broj [<PERSON><PERSON><PERSON><PERSON> proširenja](https://extensions.joomla.org/) koja učitavaju JavaScript koji se ne koristi na vašoj stranici. Da biste pronašli dodatke koji dodaju suvišan JS, pokrenite [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovorno proširenje možete pronaći u URL-u skripte. Obratite pažnju na proširenja koja na popisu imaju mnogo skripti s mnogo crvenog u pokrivenosti koda. Proširenje bi trebalo postaviti skriptu u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Pročitajte više o [predmemoriranju koje preglednici obavljaju u Joomli](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Savjetuje<PERSON> vam upotrebu [ dodatka za optimizaciju slika](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) koji komprimira slike bez gubitka kvalitete."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Savjetujemo vam upotrebu [dodatka za responzivne slike](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) da biste u svojem sadržaju koristili responzivne slike."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Možete omogućiti kompresiju teksta omogućavanjem Gzip kompresije stranice u Joomli (Sustav > Globalna konfiguracija > Poslužitelj)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ako ne grupirate JavaScript elemente, savjetujemo vam upotrebu [balera](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Onemogućite ugrađeno [JavaScript grupiranje i umanjivanje](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) na platformi Magento i umjesto toga razmotrite upotrebu [balera](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Navedite `@font-display` kada [definirate prilagođ<PERSON> fontove](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Savjetujemo vam da pretražite [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) kako biste pronašli razna proširenja trećih strana za korištenje novijih formata slike."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Savjetujemo vam da izmijenite predloške proizvoda i kataloga da biste iskoristili značajku [odgođenog učitavanja](https://web.dev/native-lazy-loading) web-platforme."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Upotrijebite [integraci<PERSON>](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) na platformi Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Omogućite opciju \"Umanji CSS datoteke\" u postavkama razvojnog programera svoje trgovine. [Saznajte više](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Upotrijebite [Terser](https://www.npmjs.com/package/terser) da biste umanjili implementaciju statičkog sadržaja u svim JavaScript elementima i onemogućite ugrađenu značajku umanjivanja."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Onemogućite ugrađeno [JavaScript grupiranje](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) na platformi Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Savjetujemo vam da pretražite [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) kako biste pronašli razna proširenja trećih strana za optimizaciju slika."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Nagovještaji resursa za rano povezivanje ili prethodno dohvaćanje DNS-a mogu se dodati [promjenom izgleda tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` oznake mogu se dodati [promjenom izgleda tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Upotrijebite komponentu `next/image` umjesto komponente `<img>` da biste automatski optimizirali format slike. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Upotrijebite komponentu `next/image` umjesto komponente `<img>` da bi se slike automatski učitale s odgodom. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image` i postavite \"priority\" na \"true\" kako bi se LCP slika predučitala. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON> komponente `next/script` odgodite učitavanje nekritičnih skripti trećih strana. [Saznajte više](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Upotrijebite komponentu `next/image` kako bi slike uvijek bile odgovarajuće veličine. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Savjetuje<PERSON> vam da postavite `PurgeCSS` u `Next.js` konfiguraciji da biste uklonili nekorištena pravila s listova stilova. [Saznajte više](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Koristite `Webpack Bundle Analyzer` za otkrivanje nekorištenog JavaScript koda. [Saznajte više](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Savjetuje<PERSON> vam da koristite `Next.js Analytics` radi mjerenja izvedbe aplikacije u stvarnom svijetu. [Saznajte više](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurirajte predmemoriranje za nepromjenjive elemente i stranice `Server-side Rendered` (SSR). [Saznajte više](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Upotrijebite komponentu `next/image` umjesto komponente `<img>` da biste prilagodili kvalitetu slike. [Saznajte više](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> komponentu `next/image` da biste postavili od<PERSON> `sizes`. [Saznajte više](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Omogućite kompresiju na Next.js poslužitelju. [Saznajte više](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Obratite se upravitelju računa da biste omogućili [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Konfiguriranjem će se prioritizirati i optimizirati uspješnost generiranja stranice."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Upotrijebite opciju [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) u NitroPacku da biste postavili željenu vrijednost za pravilo CSS-a za prikaz fontova."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Upotrijebite [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) za automatsko pretvaranje slika u WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Odgodite slike izvan z<PERSON>a tako da omo<PERSON>ćite [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Omogućite [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) u NitroPacku da biste ubrzali početno učitavanje."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Poboljšajte vrijeme odgovora poslužitelja i optimizirajte percipiranu izvedbu aktiviranjem [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Omogućite [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) u postavkama spremanja u predmemoriju kako biste smanjili veličinu CSS, HTML i JavaScript datoteka i ubrzali učitavanje."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Omogućite [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) u postavkama spremanja u predmemoriju da biste smanjili veličinu JS, HTML i CSS datoteka i ubrzali učitavanje."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Omogućite [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) da biste uklonili pravila CSS-a koja se ne primjenjuju na ovu stranicu."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Konfigurirajte [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) u NitroPacku da biste odgodili učitavanje skripti dok ne budu potrebne."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Otvorite značajku [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) u izborniku `Caching` i prilagodite vrijeme isteka predmemorije svoje stranice da biste poboljšali vremena učitavanja i korisnički doživljaj."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Automatski komprimirajte, optimizirajte i pretvorite svoje slike u WebP tako da omogućite postavku [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Omogućite [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) da biste unaprijed optimizirali slike i prilagodili ih dimenzijama spremnika u kojima se prikazuju na svim uređajima."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Upotrijebite [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) u NitroPacku da biste smanjili veličinu datoteka koje se šalju pregledniku."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Upotrijebite komponentu `nuxt/image` i postavite `format=\"webp\"`. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Upotrijebite komponentu `nuxt/image` i postavite `loading=\"lazy\"` za slike koje nisu na zaslonu. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Upotrijebite komponentu `nuxt/image` i navedite komponentu `preload` za LCP sliku. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Upotrijebite komponentu `nuxt/image` i navedite eksplicitne komponente `width` i `height`. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Upotrijebite komponentu `nuxt/image` i postavite odgovarajuću komponentu `quality`. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Upotrijebite komponentu `nuxt/image` i postavite odgovarajuću komponentu `sizes`. [Saznajte više](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Zamijenite animirane GIF-ove videozapisima](https://web.dev/replace-gifs-with-videos/) za brže učitavanje web-stranice i razmislite o upotrebi modernih formata datoteka kao što su [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ili [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) kako biste poboljšali učinkovitost kompresije za više od 30% u odnosu na trenutačno najnapredniji videokodek, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Savjetujemo vam upotrebu [dodatka](https://octobercms.com/plugins?search=image) ili usluge koji će prenesene slike automatski konvertirati u optimalne formate. [WebP slike bez gubitaka](https://developers.google.com/speed/webp) 26% su manje u odnosu na slike u formatu PNG i 25 – 34% manje od usporedivih slika u formatu JPEG pri ekvivalentnom indeksu kvalitete SSIM. Još jedan moderan format slike koji vam savjetujemo je [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Savjetujemo vam instalaciju [dodatka za učitavanje slika s odgodom](https://octobercms.com/plugins?search=lazy) koji pruža mogućnost odgode slika koje nisu na zaslonu ili prelazak na temu koja pruža tu funkciju. Savjetujemo vam i upotrebu [dodatka za AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Post<PERSON>je brojni dodaci koji pomažu [ugraditi kritične elemente](https://octobercms.com/plugins?search=css). Ti dodaci mogu oštetiti druge dodatke, pa biste ih trebali temeljito testirati."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Specifikacije za teme, dodatke i poslužitelj produljuju vrijeme odgovora poslužitelja. Savjetujemo vam da pronađete optimiziraniju temu, pažljivo odaberete dodatak za optimizaciju i/ili nadogradite poslužitelj. Sustav upravljanja sadržajem October razvojnim programerima omogućuje i upotrebu [`Queues`](https://octobercms.com/docs/services/queues) za odgađanje obrade dugotrajnog zadatka poput slanja e-poruke. Time se značajno ubrzavaju web-zahtjevi."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Savjetujemo vam da prikažete odlomke na popisu postova (npr. pomoću gumba `show more`), smanjite broj postova koji se prikazuju na određenoj web-stranici, razlomite dugačke postove na više web-stranica ili koristite dodatak za učitavanje komentara s odgodom."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> brojni [dodaci](https://octobercms.com/plugins?search=css) koji mogu ubrzati web-lokaciju nizanjem, umanjivanjem i komprimiranjem stilova. Izvršavanje umanjivanja unaprijed, tijekom postupka izrade, može ubrzati razvoj."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON> brojni [doda<PERSON>](https://octobercms.com/plugins?search=javascript) koji mogu ubrzati web-lokaciju nizanjem, umanjivanjem i komprimiranjem skripti. Izvršavanje umanjivanja unaprijed, tijekom postupka izrade, može ubrzati razvoj."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Savjetuje<PERSON> vam da pregledate [dodatke](https://octobercms.com/plugins) koji učitavaju CSS koji se ne koristi na web-lokaciji. Da biste pronašli dodatke koji dodaju nepotreban CSS, pokrenite [iskorištenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u Razvojnim alatima za Chrome. Pronađite odgovornu temu ili dodatak u URL-u lista stilova. Obratite pažnju na dodatke koji imaju mnogo listova stilova s mnogo crvenog u iskorištenosti koda. Dodatak bi trebao dodati list stilova samo ako se on doista upotrebljava na web-stranici."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Savjetuje<PERSON> vam da pregledate [dodatke](https://octobercms.com/plugins?search=javascript) koji učitavaju JavaScript koji se ne koristi na web-stranici. Da biste pronašli dodatke koji dodaju nepotreban JavaScript, pokrenite [iskorištenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u Razvojnim alatima za Chrome. Pronađite odgovornu temu ili dodatak u URL-u skripte. Obratite pozornost na dodatke koji imaju mnogo skripti s mnogo crvenog u iskorištenosti koda. Dodatak bi trebao dodati skriptu samo ako se ona doista upotrebljava na web-stranici."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Pročitajte više o [sprječavanju nepotrebnih mrežnih zahtjeva s HTTP predmemorijom](https://web.dev/http-cache/#caching-checklist). <PERSON><PERSON><PERSON> brojn<PERSON> [dodaci](https://octobercms.com/plugins?search=Caching) koji se mogu upotrijebiti za ubrzavanje predmemoriranja."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Savjetujemo vam upotrebu [dodatka za optimizaciju slika](https://octobercms.com/plugins?search=image) kako biste komprimirali slike bez gubitka kvalitete."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Prenesite slike izravno u upravitelj medija kako biste bili sigurni da će biti dostupne potrebne veličine slike. Savjetujemo vam upotrebu [filtra za promjenu veličine](https://octobercms.com/docs/markup/filter-resize) ili [dodatka za promjenu veličine slike](https://octobercms.com/plugins?search=image) kako biste se pobrinuli da se upotrebljavaju optimalne veličine slike."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Omogućite kompresiju teksta u konfiguraciji web-poslužitelja."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Ako na stranici generirate mnogo elemenata koji se ponavljaju, savjetujemo vam upotrebu zbirke \"u prozorima\" kao što je `react-window` radi smanjivanja broja izrađenih čvorova DOM-a. [Sazna<PERSON><PERSON> više](https://web.dev/virtualize-long-lists-react-window/). Osim toga, smanjite broj nepotrebnih ponovnih generiranja tako da upotrijebite [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ili [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) i [preskočite efekte](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) samo dok se određene zavisnosti ne promijene ako za poboljšanje vremena izvođenja upotrebljavate kuku `Effect`."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON> upotrebljavate React Router, smanjite upotrebu komponente `<Redirect>` za [kretanja rutom](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ako generirate komponente React na poslužitelju, savjetujemo vam upotrebu `renderToPipeableStream()` ili `renderToStaticNodeStream()` da biste klijentu omogućili primanje različitih dijelova označavanja i njihovo popunjavanje podacima umjesto da se to učini odjednom. [Saznajte više](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ako vaš sustav za izradu automatski umanjuje CSS datoteke, trebate implementirati produkcijsku međuverziju aplikacije. To možete provjeriti pomoću proširenja React Developer Tools. [Saznajte više](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ako vaš sustav za izradu automatski umanjuje JS datoteke, trebate implementirati produkcijsku međuverziju aplikacije. To možete provjeriti pomoću proširenja React Developer Tools. [Saznajte više](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Ako ne generirate na poslužitelju, [podijelite svoje JavaScript pakete](https://web.dev/code-splitting-suspense/) pomoću `React.lazy()`. U suprotnom razdvojite kôd pomoću zbirke treće strane, primjerice [komponenti koje se mogu učitati](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Upotrebljavajte React DevTools Profiler, koji se koristi Profiler API-jem, za mjerenje uspješnosti generiranja komponenti. [Saznajte više.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Postavite videozapise unutar `VideoBoxes`, prilagodite ih uz pomoć `Video Masks` ili dodajte `Transparent Videos`. [Saznajte više](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Prenesite slike pomoću usluge `Wix Media Manager` kako bi se automatski posluživale kao WebP. Pronađite [više načina za optimizaciju](https://support.wix.com/en/article/site-performance-optimizing-your-media) medija na svojoj web-lokaciji."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Prilikom [dodavanja koda treće strane](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) na karticu `Custom Code` na nadzornoj ploči web-lokacije pazite da bude odgođen ili da se učitava na kraju tijela koda. Ako je moguće, upotrijebite Wixove [integracije](https://support.wix.com/en/article/about-marketing-integrations) da biste na svoju web-lokaciju ugradili marketinške alate. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix koristi CDN-ove i predmemoriranje radi što bržeg posluživanja odgovora za većinu posjetitelja. Savjetujemo vam da [ručno omogućite spremanje u predmemoriju](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) za svoju web-lokaciju, osobito ako koristite `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Na kartici `Custom Code` na nadzornoj ploči web-lokacije pregledajte sav kôd treće strane koji ste dodali na web-lokaciju i zadržite samo usluge koje su neophodne za vašu web-lokaciju. [Saznajte više](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Savjetujemo vam da prenesete GIF na uslugu na kojoj će se kodirati za ugrađivanje kao HTML5 videozapis."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Savjetujemo vam upotrebu dodatka [Performance Lab](https://wordpress.org/plugins/performance-lab/) radi automatskog konvertiranja prenesenih JPEG slika u WebP, ako je to podržano."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instalirajte [WordPressov dodatak za lijeno učitavanje](https://wordpress.org/plugins/search/lazy+load/) koji pruža mogućnost odgode slika koje nisu na zaslonu ili prijeđite na temu koja pruža tu funkciju. Savjetujemo vam i upotrebu [dodatka za AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Brojni WordPressovi dodaci omogućuju vam da [ugradite kritične elemente](https://wordpress.org/plugins/search/critical+css/) ili [odgodite nevažnije resurse](https://wordpress.org/plugins/search/defer+css+javascript/). Upozoravamo da optimizacije koje pružaju ti dodaci mogu oštetiti značajke vaše teme ili dodataka, pa ćete vjerojatno trebati unijeti promjene u kôd."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Specifikacije za teme, dodatke i poslužitelj produljuju vrijeme odgovora poslužitelja. Savjetujemo vam da pronađete optimiziraniju temu, pažljivo odaberete dodatak za optimizaciju i/ili nadogradite poslužitelj."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Savjetujemo vam da prikažete odlomke na popisu postova (na primjer pomoću više oznaka), smanjite broj postova koji se prikazuju na određenoj stranici, raz<PERSON>mite dugačke postove na više stranica ili koristite dodatak za lijeno učitavanje komentara."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPressov<PERSON> dodaci](https://wordpress.org/plugins/search/minify+css/) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem stilova. Umanjivanje je dobro izvršiti i unaprijed, tijekom postupka razvoja."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPressov<PERSON> dodaci](https://wordpress.org/plugins/search/minify+javascript/) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem skripti. Umanjivanje je dobro izvršiti i unaprijed, tijekom postupka razvoja."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Savjetujemo vam da smanjite broj [WordPressovih dodataka](https://wordpress.org/plugins/) koji učitavaju CSS koji se ne koristi na vašoj stranici ili da ih isključite. Da biste pronašli dodatke koji dodaju suvišan CSS, pokrenite [pokrivenost koda](https://developer.chrome.com/docs/devtools/coverage/) u alatu Chrome DevTools. Odgovornu temu ili dodatak možete pronaći u URL-u stilske tablice. Obratite pažnju na dodatke koji na popisu imaju mnogo stilskih tablica s mnogo crvenog u pokrivenosti koda. Dodatak bi trebao postaviti stilsku tablicu u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Savjetujemo vam da smanjite broj [WordPressovih dodataka](https://wordpress.org/plugins/) koji učitavaju JavaScript koji se ne koristi na vašoj stranici ili da ih isključite. Da biste pronašli dodatke koji dodaju suvišan JS, pokrenite [pokrivenost koda](https://developer.chrome.com/docs/devtools/coverage/) u alatu Chrome DevTools. Odgovornu temu ili dodatak možete pronaći u URL-u skripte. Obratite pažnju na dodatke koji na popisu imaju mnogo skripti s mnogo crvenog u pokrivenosti koda. Dodatak bi trebao postaviti skriptu u red samo ako se ona doista upotrebljava na stranici."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Pročitajte više o [predmemoriranju koje preglednici obavljaju u WordPressu](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Savjetujemo vam upotrebu [WordPressovog dodatka za optimizaciju slika](https://wordpress.org/plugins/search/optimize+images/) koji komprimira slike bez gubitka kvalitete."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Prenesite slike izravno pomoću  [medijske biblioteke](https://wordpress.org/support/article/media-library-screen/) kako bi bile dostupne potrebne veličine slika, a zatim ih umetnite iz medijske biblioteke ili upotrijebite widget za slike da bi se koristile optimalne veličine slika (uključujući one za responzivne prijelomne točke). Izbjegavajte upotrebu slika `Full Size` osim ako dimenzije odgovaraju njihovoj upotrebi. [Saznajte više](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Možete omogućiti kompresiju teksta u konfiguraciji web-poslužitelja."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Omogućite opciju Imagify na kartici Optimizacija slike u stavci WP Rocket da biste svoje slike konvertirali u WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Omogućite [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) u WP Rocketu da biste ispravili tu preporuku. Ta značajka odgađa učitavanje slika dok se posjetitelj ne pomakne prema dolje i zapravo ih mora vidjeti."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Omogućite značajke [Ukloni CSS koji se ne koristi](https://docs.wp-rocket.me/article/1529-remove-unused-css) i [Učitaj JavaScript odgođeno](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) u opciji WP Rocket da biste riješili problem s tom preporukom. Te će značajke optimizirati CSS i JavaScript datoteke kako ne bi blokirale generiranje stranice."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Omogućite [Minify CSS datoteke](https://docs.wp-rocket.me/article/1350-css-minify-combine) u aplikaciji WP Rocket da biste riješili taj problem. Svi prostori i komentari u CSS datotekama vaše web-lokacije uklonit će se kako bi se datoteka smanjila i ubrzala za preuzimanje."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Omogućite [Minimiziranje JavaScript datoteka](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) u aplikaciji WP Rocket da biste riješili taj problem. Prazni prostori i komentari uklonit će se iz JavaScript datoteka da bi se njihova veličina smanjila i ubrzala za preuzimanje."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Omogućite opciju [Ukloni CSS koji se ne koristi](https://docs.wp-rocket.me/article/1529-remove-unused-css) u grupi WP Rocket da biste riješili taj problem. Smanjuje veličinu stranice uklanjanjem svih CSS-ova i listova stilova koji se ne koriste, a istovremeno zadržava samo korišten CSS za svaku stranicu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "U odjeljku WP Rocket omogućite [odgodu izvršavanja JavaScripta](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) u alatu WP Rocket da biste riješili taj problem. Poboljšat će se učitavanje vaše stranice odgađanjem izvršavanja skripti do interakcije korisnika. Ako vaša web-lokacija ima iframeove, možete upotrijebiti i [LazyLoad za iframes i videozapise](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) za WP Rocket te [zamijeniti iframe s YouTubeom na slici pregleda](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Omogućite opciju Imagify na kartici Optimizacija slike u stavci WP Rocket i pokrenite skupnu optimizaciju da biste komprimirali svoje slike."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Upotrijebite [Preddohvaćanje DNS zahtjeva](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) u odjeljku WP Rocket da biste dodali dns-prefetch i ubrzali vezu s vanjskim domenama. <PERSON><PERSON><PERSON><PERSON>, WP Rocket automatski dodaje predpovezivanje na [domenu Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) i sve CNAME-ove koji su dodani putem značajke [Omogući CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Da biste riješili taj problem s fontovima, omogućite opciju [Ukloni CSS koji se ne koristi](https://docs.wp-rocket.me/article/1529-remove-unused-css) u verziji WP Rocket. Kritični fontovi vaše web-lokacije unaprijed će se učitati s prioritetom."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Pogledaj<PERSON> kalkulator."}, "report/renderer/report-utils.js | collapseView": {"message": "Sažmi prikaz"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Početna navigacija"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maksimalna latencija kritičkog puta:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopiranje JSON-a"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Prebacivanje na Tamnu temu"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Is<PERSON>š<PERSON>"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Spremanje kao G<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Spremanje kao HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "<PERSON><PERSON><PERSON><PERSON> kao JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Prikaži izvorni trag"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Otvori u pregledniku"}, "report/renderer/report-utils.js | errorLabel": {"message": "Pogreška!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Pogreška izvješća: nema podataka o pregledu"}, "report/renderer/report-utils.js | expandView": {"message": "Proširi prikaz"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "Prva strana"}, "report/renderer/report-utils.js | footerIssue": {"message": "Prijavljivanje poteškoće"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratorijski podaci"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analiza trenutačne stranice na emuliranoj mobilnoj mreži. Vrijednosti se procjenjuju i mogu se razlikovati."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Dodatne stavke za ručnu provjeru"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Otvorite u novoj kartici"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Procijenjena <PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "U skladu s [<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON>m kriterijima prikladnosti za instalaciju](https://developer.chrome.com/blog/update-install-criteria), Lighthouse će u budućem izdanju obustaviti PWA kategoriju. Pogledajte [ažuriranu dokumentaciju o PWA-u](https://developer.chrome.com/docs/devtools/progressive-web-apps/) za buduće testiranje PWA-a."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Početno učitavanje stranice"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Pregled u trenutku"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Razdoblje interakcija korisnika"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Prilagođeno potiskivanje"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON>rana radna p<PERSON>"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "<PERSON><PERSON><PERSON> G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON> em<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "<PERSON><PERSON><PERSON> verzija"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Neograničena snaga procesora / memorije"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Potiskivanje procesora"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ograničavanje podatkovnog prometa mreže"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulacija zaslona"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Korisnički agent (mreža)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Sesija s napuštanjem početne stranice"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ovi su podaci preuzeti iz sesije s napuštanjem početne stranice, za razliku od podataka polja koji sažimaju mnoge sesije."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Sporo 4G potiskivanje"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Nepoznato"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Prikaži revizije relevantne za sljedeće:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Po<PERSON><PERSON><PERSON> resurse treće strane"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Omogućuje <PERSON>"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Na ovo izvođenje Lighthousea utjecale su neke poteškoće:"}, "report/renderer/report-utils.js | unattributable": {"message": "Ne može se dodijeliti"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Vrijednosti se procjenjuju i mogu se razlikovati. [Rezultat izvedbe računa se](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) izravno pomoću tih mjernih podataka."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Prikaži trag"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Pregledajte Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Uspješni pregledi no s upozorenjima"}, "report/renderer/report-utils.js | warningHeader": {"message": "Upozorenja: "}, "treemap/app/src/util.js | allLabel": {"message": "Sve"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Sve skripte"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Pokrivenost"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Duplic<PERSON>ni moduli"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bajtovi resursa"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>/sakrij tablicu"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Neiskorišteni bajtovi"}}