{"core/audits/accessibility/accesskeys.js | description": {"message": "Клавиши доступа позволяют быстро перейти к нужной части страницы. Для удобства навигации на каждую клавишу должно быть назначено только одно действие. Подробнее [о клавишах доступа](https://dequeuniversity.com/rules/axe/4.8/accesskeys)…"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Значения атрибута `[accesskey]` не уникальны"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`: значения уникальны"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Каждая `role` ARIA поддерживает определенный набор атрибутов `aria-*`. Неверно присвоенные атрибуты `aria-*` будут недействительны. Подробнее о том, [как сопоставлять атрибуты ARIA с их ролями](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)…"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Атрибуты `[aria-*]` не соответствуют своим ролям"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибуты `[aria-*]` соответствуют своим ролям"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "Благодаря параметрам ARIA (`role`) технологии специальных возможностей определяют роль каждого элемента на веб-странице. Если значения `role` написаны с ошибкой, содержат несуществующие роли ARIA (`role`) или абстрактные роли, то назначение элемента не будет сообщено пользователям технологий специальных возможностей. Подробнее [о ролях ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)…"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Значения, указанные для атрибута `role=\"\"`, не входят в список допустимых ролей ARIA"}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Значения, указанные для атрибута `role=\"\"`, входят в список допустимых ролей ARIA"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Если у элемента нет названия, доступного программам чтения с экрана, пользователи услышат его общее название и не поймут, для чего он нужен. Подробнее о том, [как сделать элементы команд более доступными](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)…"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "У элементов `button`, `link` и `menuitem` нет названий, доступных программам чтения с экрана"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "У элементов `button`, `link` и `menuitem` есть названия, доступные программам чтения с экрана"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Если у элементов диалогового окна ARIA нет доступных имен, пользователи программ чтения с экрана могут не понимать назначение этих элементов. Подробнее о том, [как сделать элементы диалогового окна ARIA более доступными](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)…"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "У элементов с атрибутом `role=\"dialog\"` или `role=\"alertdialog\"` нет доступных названий"}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "У элементов с атрибутом `role=\"dialog\"` или `role=\"alertdialog\"` есть доступные названия"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Программы чтения с экрана и другие технологии специальных возможностей могут работать некорректно, если для `<body>` задан атрибут `aria-hidden=\"true\"`. Подробнее о том, [как `aria-hidden` влияет на элемент body](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)…"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Элемент `<body>` в документе содержит атрибут `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Элемент `<body>` в документе не содержит атрибут `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Если для родительского элемента задан атрибут `[aria-hidden=\"true\"]`, то все его фокусируемые потомки становятся недоступны для программ чтения с экрана и других технологий специальных возможностей. Подробнее о том, [какое влияние `aria-hidden` оказывает на фокусируемые элементы](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)…"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Элементы, к которым применен атрибут `[aria-hidden=\"true\"]`, содержат активные дочерние элементы"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Элементы, к которым применен атрибут `[aria-hidden=\"true\"]`, не содержат активных дочерних элементов"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Если у поля ввода нет названия, доступного программам чтения с экрана, пользователи услышат его общее название и не поймут, для чего оно нужно. Подробнее [о ярлыках для полей ввода](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)…"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "У полей ввода ARIA нет доступных названий"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "У полей ввода ARIA есть доступные названия"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Если у элемента счетчика нет названия, доступного программам чтения с экрана, пользователи услышат только общее название и не поймут, для чего он нужен. Подробнее о том, [как присваивать названия элементам \"`meter`\"](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)…"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "У элементов `meter` ARIA нет названий, доступных программам чтения с экрана"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "У элементов `meter` ARIA есть названия, доступные программам чтения с экрана"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Если у элемента `progressbar` нет названия, доступного программам чтения с экрана, пользователи услышат только его общее название и не поймут, для чего он нужен. Подробнее о том, [как присваивать ярлыки элементам `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)…"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "У элементов `progressbar` ARIA нет названий, доступных программам чтения с экрана"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "У элементов `progressbar` ARIA есть названия, доступные программам чтения с экрана"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "К некоторым ролям ARIA необходимо добавлять атрибуты, описывающие состояние элемента для программ чтения с экрана. Подробнее [о ролях и необходимых атрибутах](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)…"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Для элементов с атрибутом `[role]` заданы не все необходимые атрибуты `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "У элементов `[role]` есть все необходимые атрибуты `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Указав роль ARIA для родительского элемента, в некоторых случаях вы должны также задать определенные роли его дочерним элементам. Если этого не сделать, связанные с ARIA функции специальных возможностей будут работать неправильно. Подробнее [о ролях и необходимых дочерних элементах](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)…"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "В элементах с ролью ARIA `[role]` отсутствуют некоторые или все обязательные дочерние элементы, которые должны содержать определенный элемент `[role]`."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "В элементах с ролью ARIA `[role]` присутствуют все обязательные дочерние элементы, которые должны содержать определенный элемент `[role]`."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Некоторые дочерние элементы с ролями ARIA должны содержаться внутри определенных родительских элементов, иначе связанные с ними функции специальных возможностей будут работать неправильно. Подробнее [о ролях ARIA и необходимых родительских элементах](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)…"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Элементы с атрибутом `[role]` не содержатся в своих родительских элементах"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Элементы с атрибутом `[role]` содержатся в своих родительских элементах"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Значения ролей ARIA должны быть допустимыми, иначе связанные с ними функции специальных возможностей будут работать неправильно. Подробнее о том, [какие роли ARIA можно указывать](https://dequeuniversity.com/rules/axe/4.8/aria-roles)…"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Присутствуют недействительные значения атрибутов `[role]`"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Недействительные значения атрибутов `[role]` отсутствуют"}, "core/audits/accessibility/aria-text.js | description": {"message": "Если добавить отделенный разметкой атрибут `role=text` перед текстовым узлом и после него, VoiceOver будет определять текст как одну фразу, но фокусируемые потомки не будут озвучиваться. Подробнее [об атрибуте `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)…"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "У элементов с атрибутом `role=text` есть фокусируемые потомки"}, "core/audits/accessibility/aria-text.js | title": {"message": "У элементов атрибута `role=text` нет фокусируемых потомков"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Если у переключателя нет названия, доступного программам чтения с экрана, пользователи услышат его общее название и не поймут, для чего он нужен. Подробнее [о переключателях](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)…"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "У переключателей ARIA нет доступных названий"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "У переключателей ARIA есть доступные названия"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Если у элемента подсказки нет названия, доступного программам чтения с экрана, пользователи услышат только его общее название и не поймут, для чего он нужен. Подробнее о том, [как присваивать названия элементам \"`tooltip`\"](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)…"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "У элементов `tooltip` ARIA нет названий, доступных программам чтения с экрана"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "У элементов `tooltip` ARIA есть названия, доступные программам чтения с экрана"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Если у элемента `treeitem` нет названия, доступного программам чтения с экрана, пользователи услышат только его общее название и не поймут, для чего он нужен. Подробнее [о присваивании ярлыков элементам `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)…"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "У элементов `treeitem` ARIA нет названий, доступных программам чтения с экрана"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "У элементов `treeitem` ARIA есть названия, доступные программам чтения с экрана"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Технологии специальных возможностей, например программы чтения с экрана, не могут распознавать атрибуты ARIA с недопустимыми значениями. Подробнее [о существующих значениях для атрибутов ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)…"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "У атрибутов `[aria-*]` недействительные значения"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Недействительные значения атрибутов `[aria-*]` отсутствуют"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Технологии специальных возможностей, например программы чтения с экрана, не могут распознавать атрибуты ARIA с недопустимыми названиями. Подробнее [о существующих атрибутах ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)…"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Атрибуты `[aria-*]` недействительны или указаны с ошибками"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибуты `[aria-*]` действительны и написаны без ошибок"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Элементы, не прошедшие проверку"}, "core/audits/accessibility/button-name.js | description": {"message": "Если у кнопки нет названия, доступного программам чтения с экрана, пользователи услышат слово \"кнопка\", но не поймут, для чего она нужна. Подробнее о том, [как сделать кнопки более доступными](https://dequeuniversity.com/rules/axe/4.8/button-name)…"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Названия кнопок недоступны программам чтения с экрана"}, "core/audits/accessibility/button-name.js | title": {"message": "Названия кнопок доступны программам чтения с экрана"}, "core/audits/accessibility/bypass.js | description": {"message": "Чтобы пользователям было проще перемещаться по странице с помощью клавиатуры, добавьте возможность пропускать повторяющийся контент. Подробнее [о пропускаемых блоках данных](https://dequeuniversity.com/rules/axe/4.8/bypass)…"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "На странице отсутствует заголовок, ссылка для пропуска или указание региона"}, "core/audits/accessibility/bypass.js | title": {"message": "Страница содержит заголовок, ссылку для пропуска контента или указание региона"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Многие пользователи не видят текст с низкой контрастностью, или им сложно его воспринимать. Подробнее о том, [как обеспечить достаточный цветовой контраст](https://dequeuniversity.com/rules/axe/4.8/color-contrast)…"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Цвета фона и переднего плана недостаточно контрастны"}, "core/audits/accessibility/color-contrast.js | title": {"message": "Цвета фона и переднего плана достаточно контрастны"}, "core/audits/accessibility/definition-list.js | description": {"message": "Если списки определений размечены с ошибками, программы чтения с экрана могут озвучивать их некорректно. Подробнее о том, [как правильно создавать списки определений](https://dequeuniversity.com/rules/axe/4.8/definition-list)…"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Ошибка в элементах `<dl>`: они могут содержать только правильно размещенные группы `<dt>` и `<dd>` и элементы `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Элементы `<dl>` содержат только правильно размещенные группы `<dt>` и `<dd>` и элементы `<script>`, `<template>` или `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Чтобы программы чтения с экрана правильно озвучивали элементы списков определений `<dt>` и `<dd>`, они должны располагаться внутри родительского элемента `<dl>`. Подробнее о том, [как правильно создавать списки определений](https://dequeuniversity.com/rules/axe/4.8/dlitem)…"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Элементы списков определений не расположены внутри элементов `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Элементы списков определений расположены внутри элементов `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Название (элемент title) нужно для того, чтобы программы чтения с экрана могли озвучивать название страницы. Также оно появляется в результатах поиска и позволяет определять, соответствует ли сайт запросу. Подробнее [о названиях документов](https://dequeuniversity.com/rules/axe/4.8/document-title)…"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "В документе нет элемента `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Документ содержит элемент `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Чтобы технологии специальных возможностей могли считывать все фокусируемые элементы, их атрибуты `id` должны быть уникальными. Подробнее о том, [как убрать копии атрибутов `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)…"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "У активных элементов есть неуникальные атрибуты `[id]`"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Атрибуты `[id]` у активных элементов уникальны"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Значение идентификатора ARIA должно быть уникальным, поскольку технологии специальных возможностей могут игнорировать повторяющиеся идентификаторы. Подробнее о том, [как убрать копии идентификаторов ARIA](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)…"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Идентификаторы ARIA не уникальны"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Идентификаторы ARIA уникальны"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Если у заголовка нет содержимого или текст недоступен, пользователи программы чтения с экрана не могут получить информацию о структуре страницы. Подробнее [о заголовках](https://dequeuniversity.com/rules/axe/4.8/empty-heading)…"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "В элементах заголовка нет содержимого"}, "core/audits/accessibility/empty-heading.js | title": {"message": "Во всех элементах заголовка есть содержимое"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Когда программы чтения с экрана или другие технологии специальных возможностей обнаруживают поля формы с несколькими ярлыками, они озвучивают только первый, последний или все ярлыки. Это может запутать пользователей. Подробнее о том, [как использовать ярлыки формы](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)…"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "В форме есть поля с несколькими ярлыками"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "В форме нет полей с несколькими ярлыками"}, "core/audits/accessibility/frame-title.js | description": {"message": "Чтобы программы чтения с экрана могли описывать содержимое фреймов, у каждого из них должно быть название (атрибут title). Подробнее [о названиях фреймов](https://dequeuniversity.com/rules/axe/4.8/frame-title)…"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Для элементов `<frame>` или `<iframe>` не указан атрибут title"}, "core/audits/accessibility/frame-title.js | title": {"message": "У элементов `<frame>` и `<iframe>` есть атрибут title"}, "core/audits/accessibility/heading-order.js | description": {"message": "Когда заголовки расположены в правильном порядке и между их уровнями нет пропусков, они образуют семантическую структуру страницы. Благодаря этому навигация с помощью технологий специальных возможностей становится проще и понятнее. Подробнее [о расположении заголовков](https://dequeuniversity.com/rules/axe/4.8/heading-order)…"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Элементы заголовков не расположены последовательно в порядке убывания"}, "core/audits/accessibility/heading-order.js | title": {"message": "Элементы заголовков расположены последовательно в порядке убывания"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Если для страницы не указан атрибут `lang`, программа чтения с экрана предполагает, что текст приведен на языке по умолчанию, выбранном пользователем при установке программы. Если текст написан на другом языке, он может озвучиваться некорректно. Подробнее [об атрибуте `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)…"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Для элемента `<html>` не задан атрибут `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Элемент `<html>` содержит атрибут `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Чтобы программы чтения с экрана правильно озвучивали текст, укажите допустимый [языковой тег BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question). Подробнее о том, [как использовать атрибут `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)…"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "В элементе `<html>` нет действительного значения для атрибута `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Для элемента `<html>` указано действительное значение атрибута `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Если для веб-страницы не указан подходящий язык, программа чтения с экрана может озвучивать текст некорректно. Подробнее [об атрибуте `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)…"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "У элемента `<html>` нет атрибута `[xml:lang]`, основной язык которого совпадает с языком в атрибуте `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "У элемента `<html>` есть атрибут `[xml:lang]`, основной язык которого совпадает с языком в атрибуте `[lang]`"}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "У ссылок с одним назначением должно быть одинаковое описание. Так пользователь поймет, куда они ведут, и решит, следует ли по ним переходить. Подробнее [об одинаковых ссылках](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)…"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "У одинаковых ссылок разное назначение"}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "У одинаковых ссылок одно назначение"}, "core/audits/accessibility/image-alt.js | description": {"message": "В информационных элементах должен содержаться короткий и ясный альтернативный текст. Если элемент декоративный, то атрибут alt для него можно оставить пустым. Подробнее [об атрибуте `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)…"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Для элементов изображений не заданы атрибуты `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "У элементов изображений есть атрибут `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "В информационных элементах должен содержаться короткий и ясный альтернативный текст. Если он будет повторять текст рядом со ссылкой или изображением, то одна и та же фраза может прозвучать дважды, что собьет с толку пользователей программы чтения с экрана. Подробнее [об атрибуте `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)…"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "У изображений есть атрибуты `[alt]`, представляющие собой избыточный текст"}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "У изображений нет атрибутов `[alt]`, представляющих собой избыточный текст"}, "core/audits/accessibility/input-button-name.js | description": {"message": "Добавьте к кнопкам ввода легко различимый текст, чтобы пользователи программы чтения с экрана могли понимать их назначение. Подробнее [о кнопках ввода](https://dequeuniversity.com/rules/axe/4.8/input-button-name)…"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Текст кнопок ввода плохо различим"}, "core/audits/accessibility/input-button-name.js | title": {"message": "Текст кнопок ввода легко различим"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Если в элементе `<input>` в качестве кнопки используется изображение, добавьте альтернативный текст, описывающий назначение этой кнопки для программ чтения с экрана. Подробнее [об альтернативном тексте для изображения в элементе ввода](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)…"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Атрибут `[alt]` задан не для всех элементов `<input type=\"image\">`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Элементы `<input type=\"image\">` содержат атрибут `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Видимые текстовые метки, которые не совпадают с доступными названиями, могут запутать пользователей программ чтения с экрана. Подробнее [о доступных названиях](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)…"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "У элементов с видимыми текстовыми метками нет подходящих доступных названий"}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "У элементов с видимыми текстовыми метками есть подходящие доступные названия"}, "core/audits/accessibility/label.js | description": {"message": "Ярлыки нужны для того, чтобы программы чтения с экрана и другие технологии специальных возможностей могли правильно озвучивать элементы управления формой. Подробнее [о ярлыках для элементов формы](https://dequeuniversity.com/rules/axe/4.8/label)…"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Элементам формы не присвоены соответствующие ярлыки"}, "core/audits/accessibility/label.js | title": {"message": "Элементам формы присвоены соответствующие ярлыки"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Если добавить ориентир main, пользователям программы чтения с экрана будет удобнее перемещаться по веб-странице. Подробнее [об ориентирах](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)…"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "В документе нет ориентира main"}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "В документе есть ориентир main"}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Многие пользователи не видят текст с низкой контрастностью, или им сложно его воспринимать. Хорошо различимый текст ссылки облегчает работу людям со слабым зрением. Подробнее о том, [как сделать ссылку хорошо различимой](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)…"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Ссылки нельзя различить, не опираясь на цвет"}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Ссылки можно различить, не опираясь на цвет"}, "core/audits/accessibility/link-name.js | description": {"message": "Текст ссылок (как и альтернативный текст для изображений, используемых в качестве ссылок) должен быть уникальным, фокусируемым и доступным для программ чтения с экрана. Подробнее о том, [как сделать ссылки доступными для программ с технологиями специальных возможностей](https://dequeuniversity.com/rules/axe/4.8/link-name)…"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Текст ссылок неразличим для программ чтения с экрана"}, "core/audits/accessibility/link-name.js | title": {"message": "Текст ссылок различим для программ чтения с экрана"}, "core/audits/accessibility/list.js | description": {"message": "Используйте правильную структуру кода при верстке списков, иначе программы чтения с экрана будут некорректно их озвучивать. Подробнее [о правильной структуре списков](https://dequeuniversity.com/rules/axe/4.8/list)…"}, "core/audits/accessibility/list.js | failureTitle": {"message": "В списках содержатся другие элементы, помимо элементов `<li>` и элементов поддержки скрипта (`<script>` и `<template>`)"}, "core/audits/accessibility/list.js | title": {"message": "В списках содержатся только элементы `<li>` и элементы поддержки скрипта (`<script>` и `<template>`)"}, "core/audits/accessibility/listitem.js | description": {"message": "Чтобы программы чтения с экрана правильно озвучивали списки, элементы `<li>` должны располагаться внутри родительских элементов `<ul>`, `<ol>` или `<menu>`. Подробнее [о правильной структуре списков](https://dequeuniversity.com/rules/axe/4.8/listitem)…"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Элементы списка (`<li>`) не содержатся в родительских элементах `<ul>`, `<ol>` или `<menu>`"}, "core/audits/accessibility/listitem.js | title": {"message": "Элементы списка (`<li>`) расположены внутри родительских элементов `<ul>`, `<ol>` или `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Когда страница обновляется автоматически, фокус, используемый программами для чтения с экрана, перемещается в верхнюю часть. Это может мешать работе пользователей и вызывать у них негативные эмоции. Подробнее [о метатеге refresh](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)…"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "В документе используется метатег `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "В документе не используется метатег `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Не отключайте масштабирование. Эта функция помогает слабовидящим пользователям читать информацию на веб-страницах. Подробнее [о метатеге viewport](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)…"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Атрибут `[user-scalable=\"no\"]` используется в элементе `<meta name=\"viewport\">` или значение атрибута `[maximum-scale]` меньше 5"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Атрибут `[user-scalable=\"no\"]` не используется в элементе `<meta name=\"viewport\">`, и значение атрибута `[maximum-scale]` больше или равно 5"}, "core/audits/accessibility/object-alt.js | description": {"message": "Чтобы программы чтения с экрана могли зачитывать содержимое элементов `<object>`, добавьте к ним альтернативный текст. Подробнее [об альтернативном тексте для элементов `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)…"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "У элементов `<object>` нет альтернативного текста"}, "core/audits/accessibility/object-alt.js | title": {"message": "У элементов `<object>` есть альтернативный текст"}, "core/audits/accessibility/select-name.js | description": {"message": "Элементы формы без эффективных меток могут доставить неудобства пользователям программы чтения с экрана. Подробнее [об элементе `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)…"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "У элементов select нет связанных элементов label"}, "core/audits/accessibility/select-name.js | title": {"message": "У элементов select есть связанные элементы label"}, "core/audits/accessibility/skip-link.js | description": {"message": "Добавьте ссылку для пропуска контента, чтобы пользователи могли сразу перейти к основному контенту. [Подробнее…](https://dequeuniversity.com/rules/axe/4.8/skip-link)"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Ссылки для пропуска контента нефокусируемые"}, "core/audits/accessibility/skip-link.js | title": {"message": "Ссылки для пропуска контента фокусируемые"}, "core/audits/accessibility/tabindex.js | description": {"message": "Значение больше 0 подразумевает явное указание порядка навигации. Это может создавать трудности для пользователей с ограниченными возможностями. Подробнее [об атрибуте `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)…"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Для некоторых элементов значение `[tabindex]` больше 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Нет элементов со значением атрибута `[tabindex]` выше 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Атрибут summary должен описывать структуру таблицы, а в элементе `<caption>` требуется указать экранный заголовок. Точная разметка таблиц упрощает работу пользователей программ чтения с экрана. Подробнее [об атрибуте summary и элементе caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)…"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "В таблицах для атрибута summary и элемента`<caption>.` указаны одинаковые значения"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "В таблицах указаны разные значения для атрибута summary и элемента `<caption>`"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Чтобы пользователям было проще перемещаться по таблицам с помощью программ чтения с экрана, не используйте в таблицах ячейки с атрибутом `[colspan]` для обозначения подписей. Подробнее [о подписях](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)…"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Для обозначения подписей в таблицах не используется элемент `<caption>` вместо ячеек с атрибутом `[colspan]`"}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Для обозначения подписей в таблицах используется элемент `<caption>` вместо ячеек с атрибутом `[colspan]`"}, "core/audits/accessibility/target-size.js | description": {"message": "Области прикосновения должны быть достаточно большими, как и расстояние между ними. Так пользователям, у которых могут возникнуть трудности с нажатием на небольшие элементы управления, будет проще выбрать нужный. Подробнее [об областях прикосновения](https://dequeuniversity.com/rules/axe/4.8/target-size)…"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Области прикосновения и расстояние между ними недостаточно большие"}, "core/audits/accessibility/target-size.js | title": {"message": "Области прикосновения и расстояние между ними достаточно большие"}, "core/audits/accessibility/td-has-header.js | description": {"message": "Чтобы пользователям было проще перемещаться по таблицам с помощью программ чтения с экрана, рекомендуем добавлять заголовки для элементов `<td>` в больших таблицах (состоящих из трех или более ячеек в высоту и ширину). Подробнее [о заголовках таблиц](https://dequeuniversity.com/rules/axe/4.8/td-has-header)…"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "У элементов `<td>` нет заголовков в большой таблице `<table>`"}, "core/audits/accessibility/td-has-header.js | title": {"message": "У элементов `<td>` есть один или несколько заголовков в большой таблице `<table>`"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Чтобы пользователям было проще перемещаться по таблицам с помощью программ чтения с экрана, убедитесь, что ячейки (элементы `<td>`) с атрибутом `[headers]` ссылаются только на другие ячейки в той же таблице. Подробнее [об атрибуте `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)…"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ячейки внутри элемента `<table>`, в которых используется атрибут `[headers]`, ссылаются на элемент `id`, не найденный внутри той же таблицы."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Ячейки внутри элемента `<table>`, в которых используется атрибут `[headers]`, ссылаются на ячейки той же таблицы."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Чтобы пользователям было проще перемещаться по таблицам с помощью программ чтения с экрана, убедитесь, что все заголовки в таблицах ссылаются на тот или иной набор ячеек. Подробнее [о заголовках таблиц](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)…"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "В элементах `<th>` и элементах с атрибутом `[role=\"columnheader\"/\"rowheader\"]` нет описываемых ими ячеек с данными"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "В элементах `<th>` и элементах с атрибутом `[role=\"columnheader\"/\"rowheader\"]` есть описываемые ими ячейки с данными"}, "core/audits/accessibility/valid-lang.js | description": {"message": "Чтобы программы чтения с экрана правильно озвучивали текст, укажите для элементов допустимый [языковой тег BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question). Подробнее о том, [как использовать атрибут `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)…"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Присутствуют недействительные значения атрибутов `[lang]`"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Недействительные значения атрибутов `[lang]` отсутствуют"}, "core/audits/accessibility/video-caption.js | description": {"message": "Чтобы информация, озвучиваемая в видео, была доступна людям с нарушениями слуха, добавьте субтитры. Подробнее [о субтитрах в видео](https://dequeuniversity.com/rules/axe/4.8/video-caption)…"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Элементы `<video>` не содержат элемент `<track>` с атрибутом `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Элементы `<video>` содержат элемент `<track>` с атрибутом `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Текущее значение"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Предлагаемый токен"}, "core/audits/autocomplete.js | description": {"message": "Атрибут `autocomplete` помогает пользователям отправлять формы быстрее. Чтобы упростить процесс, включите автозаполнение, задав атрибуту `autocomplete` допустимое значение. Подробнее [об атрибуте `autocomplete` в формах](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)…"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` – для элементов не заданы верные атрибуты `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Требуется проверка вручную"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Проверьте порядок токенов"}, "core/audits/autocomplete.js | title": {"message": "Элементы `<input>` верно используют атрибут `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Недопустимый токен `autocomplete` \"{token}\" в выражении {snippet}."}, "core/audits/autocomplete.js | warningOrder": {"message": "Проверьте порядок токенов: \"{tokens}\" в выражении {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Есть доступные действия"}, "core/audits/bf-cache.js | description": {"message": "Пользователи часто возвращаются на предыдущую страницу и снова открывают исходную. Применение возвратного кеша помогает ускорить эти переходы. Подробнее [о возвратном кеше](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)…"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 причина ошибки}one{# причина ошибки}few{# причины ошибки}many{# причин ошибки}other{# причины ошибки}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Причина ошибки"}, "core/audits/bf-cache.js | failureTitle": {"message": "На странице предотвращено восстановление из возвратного кеша"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Тип оши<PERSON>ки"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Нет доступных действий"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Поддержка браузера планируется"}, "core/audits/bf-cache.js | title": {"message": "На странице разрешено восстановление из возвратного кеша"}, "core/audits/bf-cache.js | warningHeadless": {"message": "В старой версии Headless Chrome (`--chrome-flags=\"--headless=old\"`) невозможно проверить возвратный кеш. Чтобы посмотреть результаты, используйте новую версию Headless Chrome (`--chrome-flags=\"--headless=new\"`) или стандартный браузер Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Расширения Chrome замедляют загрузку этой страницы. Попробуйте использовать режим инкогнито или профиль Chrome без расширений."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Время оценки скриптов"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Время анализа скриптов"}, "core/audits/bootup-time.js | columnTotal": {"message": "Общее процессорное время"}, "core/audits/bootup-time.js | description": {"message": "Рекомендуем сократить время на обработку, компиляцию и выполнение скриптов JS. Для этого вы можете разбить код JS на небольшие фрагменты. Подробнее о том, [как ускорить выполнение скриптов JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)…"}, "core/audits/bootup-time.js | failureTitle": {"message": "Сократите время выполнения кода JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Время выполнения кода JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Чтобы сократить расход трафика, удалите из пакетов большие повторяющиеся модули JavaScript. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Удалите повторяющиеся модули из пакетов JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Анимированный контент неэффективно загружать в виде больших GIF-файлов. Чтобы экономить сетевой трафик для пользователей, используйте видеоформаты MPEG4 и WebM для анимированного контента и графические форматы PNG и WebP – для статического. Подробнее [об эффективных форматах видео](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)…"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Используйте видеоформаты для анимированного контента"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Полифилы и преобразования позволяют работать с новыми возможностями JavaScript в устаревших браузерах. Однако для современных браузеров большинство из них не требуется. Используйте новую стратегию развертывания скриптов в пакетах JavaScript. Обнаружение модульных и немодульных функций сократит объем кода в современных браузерах и обеспечит поддержку устаревших браузеров. Подробнее о том, [как использовать новую стратегию работы с JavaScript](https://web.dev/articles/publish-modern-javascript)…"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Не отправляйте устаревший код JavaScript в современные браузеры"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Форматы WebP и AVIF обеспечивают более эффективное сжатие по сравнению с PNG или JPEG, поэтому такие изображения загружаются быстрее и потребляют меньше трафика. Подробнее [о современных графических форматах](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)…"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Используйте современные форматы изображений"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Чтобы уменьшить время загрузки для взаимодействия, рекомендуем настроить отложенную загрузку скрытых изображений. Тогда основные ресурсы сайта будут загружаться в первую очередь. Подробнее о том, [как отложить загрузку скрытых изображений](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)…"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Отложите загрузку скрытых изображений"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Некоторые ресурсы блокируют первую отрисовку страницы. Рекомендуем встроить критическую часть данных JS и CSS в код страницы и отложить загрузку всех второстепенных ресурсов. Подробнее о том, [как устранить ресурсы, блокирующие отрисовку](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)…"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Устраните ресурсы, блокирующие отображение"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Чрезмерная полезная нагрузка на сеть приводит к финансовым затратам пользователей и может замедлять время загрузки контента. Подробнее о том, [как сократить объем полезной нагрузки](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)…"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Общий размер достиг {totalBytes, number, bytes} КиБ"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Предотвратите чрезмерную нагрузку на сеть"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Предотвращение чрезмерной нагрузки на сеть"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Уменьшив файлы CSS, вы можете сократить объем полезной сетевой нагрузки. Подробнее о том, [как уменьшить файл CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)…"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Уменьшите размер кода CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Уменьшив файлы JavaScript, вы можете сократить объем полезной нагрузки и время обработки скриптов. Подробнее о том, [как уменьшить файл JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)…"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Уменьшите размер кода JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Чтобы сократить расход трафика, удалите ненужные правила из таблиц стилей и используйте отложенную загрузку кода CSS, который не используется в видимой части страницы. Подробнее о том, [как уменьшить объем неиспользуемого кода CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)…"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Удалите неиспользуемый код CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Чтобы сократить расход трафика, удалите неиспользуемый код JavaScript и отложите загрузку скриптов до тех пор, пока они не понадобятся. Подробнее о том, [как удалить неиспользуемый код JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)…"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Удалите неиспользуемый код JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Благодаря долгому времени хранения кеша страница может быстрее загружаться при повторных посещениях. Подробнее [об эффективных правилах в отношении кеша](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)…"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Найден 1 ресурс}one{Найден # ресурс}few{Найдено # ресурса}many{Найдено # ресурсов}other{Найдено # ресурса}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Задайте правила эффективного использования кеша для статических объектов"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Настройка правил эффективного использования кеша для статических объектов"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимизированные изображения загружаются быстрее и расходуют меньше мобильного трафика. Подробнее [об эффективном кодировании изображений](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)…"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Настройте эффективную кодировку изображений"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Фактические размеры"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Отображаемые размеры"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Размеры изображений больше отображаемых"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Размеры изображений соответствует отображаемым"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Чтобы экономить мобильный трафик пользователей и ускорять загрузку страниц, следите за тем, чтобы размеры изображений соответствовали требованиям. Подробнее о том, [как правильно выбирать размер изображений](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)…"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Настройте подходящий размер изображений"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Чтобы уменьшить расход сетевого трафика, рекомендуем сжимать текстовые ресурсы (используйте gzip, deflate ил<PERSON> brotli). Подробнее [о сжатии текста](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)…"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Включите сжатие текста"}, "core/audits/content-width.js | description": {"message": "Приложение не оптимизировано под экраны мобильных устройств, если ширина контента приложения не совпадает с шириной области просмотра. Подробнее о том, [как изменять размеры контента для области просмотра](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)…"}, "core/audits/content-width.js | explanation": {"message": "Область просмотра ({innerWidth} пикселей) не совпадает с размером окна ({outerWidth} пикселей)."}, "core/audits/content-width.js | failureTitle": {"message": "Размер контента не соответствует области просмотра"}, "core/audits/content-width.js | title": {"message": "Размер контента соответствует области просмотра"}, "core/audits/critical-request-chains.js | description": {"message": "Приведенные ниже цепочки критических запросов показывают, какие ресурсы загружаются с высоким приоритетом. Чтобы ускорить загрузку страниц, рекомендуем сократить длину цепочек, уменьшить размер скачиваемых ресурсов или отложить скачивание ненужных ресурсов. Подробнее о том, [как предотвращать образование цепочек критических запросов](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)…"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Найдена 1 цепочка}one{Найдена # цепочка}few{Найдено # цепочки}many{Найдено # цепочек}other{Найдено # цепочки}}"}, "core/audits/critical-request-chains.js | title": {"message": "Старайтесь не допускать создания цепочек критических запросов"}, "core/audits/csp-xss.js | columnDirective": {"message": "Директива"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Уровень серьезности"}, "core/audits/csp-xss.js | description": {"message": "Надежная политика безопасности контента (Content Security Policy, CSP) существенно снижает риск атак XSS (межсайтовый скриптинг). Подробнее о том, [как использовать CSP для предотвращения атак XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)…"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "На странице есть политика CSP, определенная в теге `<meta>`. Переместите CSP в HTTP-заголовок или укажите в нем другую строгую политику CSP."}, "core/audits/csp-xss.js | noCsp": {"message": "Не найдены политики CSP в режиме принудительного применения."}, "core/audits/csp-xss.js | title": {"message": "Убедитесь, что политика CSP эффективна против атак XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Прекращение поддержки / предупреждение"}, "core/audits/deprecations.js | columnLine": {"message": "Строка"}, "core/audits/deprecations.js | description": {"message": "В будущем устаревшие API будут удалены из браузера. Подробнее [об устаревших API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)…"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Обнаружено 1 предупреждение}one{Обнаружено # предупреждение}few{Обнаружено # предупреждения}many{Обнаружено # предупреждений}other{Обнаружено # предупреждения}}"}, "core/audits/deprecations.js | failureTitle": {"message": "На странице используются устаревшие API"}, "core/audits/deprecations.js | title": {"message": "Устаревшие API не используются"}, "core/audits/dobetterweb/charset.js | description": {"message": "Требуется задать кодировку символов. Это можно сделать с помощью тега `<meta>` в первых 1024 байтах HTML-страницы или в заголовке ответа HTTP Content-Type. Подробнее о том, [как задать кодировку символов](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)…"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Объявление набора символов отсутствует или указано в неправильном месте на HTML-странице"}, "core/audits/dobetterweb/charset.js | title": {"message": "Правильно заданный набор символов"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Если для страницы указан параметр DOCTYPE, браузер не будет переключаться в режим совместимости. Подробнее [об указании DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)…"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "В качестве названия элемента DOCTYPE нужно указать значение `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "В документе с помощью тега `doctype` включен режим `limited-quirks-mode`."}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Необходимо добавить элемент DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Поле publicId содержит данные"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Поле systemId содержит данные"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "В документе с помощью тега `doctype` включен режим `quirks-mode`."}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Активирован режим совместимости, так как на странице отсутствует элемент DOCTYPE"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Тип страницы (DOCTYPE): HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистический показатель"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Значение"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Из-за сложной структуры DOM используется больше памяти, замедляется [вычисление стилей](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) и возникают дополнительные [компоновки макета](https://developers.google.com/speed/articles/reflow). Подробнее о том, [как сократить размер структуры DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)…"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 элемент}one{# элемент}few{# элемента}many{# элементов}other{# элемента}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Сократите размер структуры DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимальная глубина вложенности DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Общее количество элементов DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Максимальное число дочерних элементов"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Сокращение размера структуры DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Пользователи с подозрением относятся к сайтам, которые беспричинно запрашивают доступ к их местоположению. Рекомендуем связать этот запрос с определенными действиями пользователя. Подробнее [о разрешении на определение местоположения](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)…"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Разрешение на определение местоположения запрашивается при загрузке страницы"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Разрешение на определение местоположения не запрашивается при загрузке страницы"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Тип нарушения"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "О<PERSON><PERSON><PERSON><PERSON><PERSON>, записанные на панели`Issues` в Инструментах разработчика Chrome, указывают на нерешенные проблемы. Это могут быть невыполненные сетевые запросы, низкий уровень защиты и другие сбои в работе браузера. Чтобы получить подробную информацию о каждой неполадке, откройте панель."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Ошибки были записаны на панели `Issues` в Инструментах разработчика Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Заблокировано в соответствии с правилами использования междоменных запросов."}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Объявления используют слишком много ресурсов"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Нет записей ошибок на панели `Issues` в Инструментах разработчика Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Версия"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Все клиентские библиотеки JavaScript, обнаруженные на странице. Подробнее [о результатах диагностики этой библиотеки JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)…"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Обнаруженные библиотеки JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Использование метода `document.write()` для динамического внедрения внешних скриптов может значительно (на десятки секунд) замедлять загрузку страницы для пользователей с низкой скоростью подключения. Подробнее о том, [как отказаться от использования метода document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)…"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Не используйте `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Метод `document.write()` не используется"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Пользователи с подозрением относятся к сайтам, которые беспричинно запрашивают разрешение на отправку уведомлений. Рекомендуем связать этот запрос с определенными действиями пользователя. Подробнее о том, [как запрашивать разрешение на уведомления должным образом](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)…"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Разрешение на отправку уведомлений запрашивается при загрузке страницы"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Разрешение на отправку уведомлений не запрашивается при загрузке страницы"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Запрет на вставку информации в поле ввода негативно влияет на удобство использования и снижает безопасность, блокируя работу менеджеров паролей. Подробнее [об удобных полях ввода](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)…"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Вставка контента в поля ввода запрещена"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Вставка контента в поля ввода разрешена"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Протокол HTTP/2 отличается от HTTP/1.1 массой преимуществ, включая мультиплексирование запросов и бинарность. Подробнее [об HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)…"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 запрос не передан через HTTP/2}one{# запрос не передан через HTTP/2}few{# запроса не передано через HTTP/2}many{# запросов не передано через HTTP/2}other{# запроса не передано через HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Перейдите на протокол HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Чтобы повысить производительность при прокрутке страницы, используйте флаг `passive` для прослушивателей событий прикосновения и колеса мыши. Подробнее [о применении пассивных прослушивателей событий](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)…"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Пассивные прослушиватели событий не используются для улучшения производительности при прокрутке"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Пассивные прослушиватели событий используются для улучшения производительности при прокрутке"}, "core/audits/errors-in-console.js | description": {"message": "О<PERSON><PERSON><PERSON><PERSON><PERSON>, которые появляются в консоли, указывают на нерешенные проблемы. Это могут быть невыполненные сетевые запросы и другие сбои в работе браузера. Подробнее [об этих ошибках в результатах диагностики консоли](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)…"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Ошибки браузера занесены в журнал консоли"}, "core/audits/errors-in-console.js | title": {"message": "В журнале консоли нет ошибок браузера"}, "core/audits/font-display.js | description": {"message": "Используйте CSS-дескриптор `font-display`, чтобы пользователи могли видеть текст, пока загружаются веб-шрифты. Подробнее [о `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)…"}, "core/audits/font-display.js | failureTitle": {"message": "Настройте показ всего текста во время загрузки веб-шрифтов"}, "core/audits/font-display.js | title": {"message": "Показ всего текста во время загрузки веб-шрифтов"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Сервису Lighthouse не удалось автоматически проверить значение `font-display` для следующего URL: {fontOrigin}.}one{Сервису Lighthouse не удалось автоматически проверить значения `font-display` для следующего URL: {fontOrigin}.}few{Сервису Lighthouse не удалось автоматически проверить значения `font-display` для следующего URL: {fontOrigin}.}many{Сервису Lighthouse не удалось автоматически проверить значения `font-display` для следующего URL: {fontOrigin}.}other{Сервису Lighthouse не удалось автоматически проверить значения `font-display` для следующего URL: {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Соотношение сторон (фактическое)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Соотношение сторон (отображаемое)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Размеры отображаемого изображения должны соответствовать нормальному соотношению сторон. Подробнее [о соотношении сторон изображения](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)…"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Присутствуют изображения с некорректным соотношением сторон"}, "core/audits/image-aspect-ratio.js | title": {"message": "Отсутствуют изображения с некорректным соотношением сторон"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Фактический размер"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Отображаемый размер"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Ожидаемый размер"}, "core/audits/image-size-responsive.js | description": {"message": "Чтобы обеспечить максимальное качество, размеры исходного изображения должны быть пропорциональны размерам при отображении на экране с учетом соотношения пикселей. Подробнее о том, [как добавлять адаптивные изображения](https://web.dev/articles/serve-responsive-images)…"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Изображения показываются в низком разрешении"}, "core/audits/image-size-responsive.js | title": {"message": "Изображения показываются в нужном разрешении"}, "core/audits/installable-manifest.js | already-installed": {"message": "Приложение уже установлено."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Не удалось скачать нужный значок из манифеста."}, "core/audits/installable-manifest.js | columnValue": {"message": "Причина ошибки"}, "core/audits/installable-manifest.js | description": {"message": "Service worker — это технология, которая позволяет использовать преимущества современных веб-приложений, такие как поддержка офлайн-режима, push-уведомлений и добавления на главный экран. Браузеры могут предлагать пользователям добавлять приложение на главный экран, за счет чего они наверняка будут чаще его открывать. Подробнее [о требованиях установки, предъявляемых к манифесту](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)…"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 причина}one{# причина}few{# причины}many{# причин}other{# причины}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Манифест веб-приложения или файл service worker не соответствует условиям, необходимым для установки"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL приложения и его идентификатор в Google Play не соответствуют друг другу."}, "core/audits/installable-manifest.js | in-incognito": {"message": "Эта страница открыта в режиме инкогнито."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "У свойства `display` манифеста должно быть значение `standalone`, `fullscreen` или `minimal-ui`."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Файл манифеста содержит поле display_override, поэтому первый поддерживаемый режим отображения должен быть следующим: standalone (стандартный), fullscreen (полноэкранный) или minimal-ui (минимальный)."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Файл манифеста нельзя загрузить или обработать, либо он пуст."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Во время загрузки манифеста его URL был изменен."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "В манифесте нет поля `name` или `short_name`."}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "В файле манифеста отсутствует допустимый значок. Добавьте значок в формате PNG, SVG или WebP с разрешением не менее {value0} пкс и атрибутом sizes. Если есть атрибут purpose, у него должно быть значение any."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "В файле манифеста нет значка в формате PNG, SVG или WebP в форме квадрата с разрешением не менее {value0} пкс, у которого атрибут purpose не задан или задан со значением any."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Скачанный значок пуст или поврежден."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Укажите идентификатор Google Play."}, "core/audits/installable-manifest.js | no-manifest": {"message": "Страница не содержит URL манифеста <link>."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Для проверки файла service worker в манифесте должно быть поле start_url."}, "core/audits/installable-manifest.js | noErrorId": {"message": "Идентификатор ошибки установки {errorId} не распознан."}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Страница загружена из незащищенного источника."}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Страница не загружается в основном фрейме."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Эта страница не поддерживается в офлайн-режиме."}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Условия установки сброшены после удаления PWA."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Указанная платформа приложения не поддерживается на устройствах Android."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Значение атрибута prefer_related_applications в манифесте: true."}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Атрибут prefer_related_applications поддерживается только в бета-версии Chrome и стабильной версии на устройствах Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse не удалось проверить, можно ли установить страницу. Повторите попытку в более новой версии Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Недействительный URL стартовой страницы манифеста."}, "core/audits/installable-manifest.js | title": {"message": "Манифест веб-приложения и файл service worker соответствуют условиям, необходимым для установки"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL в манифесте содержит имя пользователя, пароль или порт."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Эта страница не работает в офлайн-режиме. Она не будет считаться доступной для установки после выхода Chrome 93 в августе 2021 года."}, "core/audits/is-on-https.js | allowed": {"message": "Разрешено"}, "core/audits/is-on-https.js | blocked": {"message": "Заблокировано"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Небезопасный URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Обработка запросов"}, "core/audits/is-on-https.js | description": {"message": "Все сайты (даже если они не обрабатывают конфиденциальные данные) должны быть защищены протоколом HTTPS. Это в том числе означает, что не следует использовать [смешанный контент](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content): не должно быть ситуаций, когда некоторые ресурсы загружаются по протоколу HTTP, хотя первоначальный запрос передается с применением HTTPS. HTTPS обеспечивает защиту от взлома и не позволяет посторонним узнавать, как пользователи взаимодействуют с приложением. Кроме того, использование этого протокола обязательно при работе с версией HTTP/2 и многими новыми API для веб-платформ. Подробнее [об HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)…"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Обнаружен 1 небезопасный запрос}one{Обнаружен # небезопасный запрос}few{Обнаружено # небезопасных запроса}many{Обнаружено # небезопасных запросов}other{Обнаружено # небезопасного запроса}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Протокол HTTPS не используется"}, "core/audits/is-on-https.js | title": {"message": "Используется протокол HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Автоматически изменено на HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Разрешено (есть предупреждение)"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% от LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Фаза"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Время"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Это самый большой элемент контента, отрисованный в области просмотра. Подробнее [об отрисовке самого крупного контента](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)…"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Задержка загрузки"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Время загрузки"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Задержка отрисовки"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Элемент \"Отрисовка самого крупного контента\""}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Влияние смещения макета"}, "core/audits/layout-shift-elements.js | description": {"message": "Смещения макета больше всего повлияли на эти элементы DOM. Некоторые смещения макета, возможно, не были учтены в показателе CLS, так как использовалась [обработка методом окна](https://web.dev/articles/cls#what_is_cls). Узнайте, [как уменьшить CLS](https://web.dev/articles/optimize-cls)."}, "core/audits/layout-shift-elements.js | title": {"message": "Устраните большие смещения макета"}, "core/audits/layout-shifts.js | columnScore": {"message": "Показатель смещения макета"}, "core/audits/layout-shifts.js | description": {"message": "Это самые значительные смещения макета страницы. Каждый пункт таблицы относится к одному сдвигу и показывает, какой элемент затронут сильнее всего. Под пунктами таблицы приведены возможные причины смещения макета. Некоторые из приведенных случаев могли быть упущены при расчете показателя CLS, так как использовалась [обработка методом окна](https://web.dev/articles/cls#what_is_cls). Узнайте, [как уменьшить CLS](https://web.dev/articles/optimize-cls)."}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{1 смещение макета}one{# смещение макета}few{# смещения макета}many{# смещений макета}other{# смещения макета}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Загрузка веб-шрифта"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Внедрение окна iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Изменение макета страницы при последнем сетевом запросе"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "У медиаэлемента нет заданного явным образом размера"}, "core/audits/layout-shifts.js | title": {"message": "Устраните большие смещения макета"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Отложенная загрузка изображений в видимой части страницы приводит к тому, что они позже показываются. Это может замедлить отрисовку самого крупного контента. Подробнее [об оптимальной отложенной загрузке](https://web.dev/articles/lcp-lazy-loading)…"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Изображение, которое отображается при отрисовке самого крупного контента, загружено с задержкой"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Изображение, которое отображается при отрисовке самого крупного контента, загружено без задержки"}, "core/audits/long-tasks.js | description": {"message": "Для основного потока создается список самых длительных задач. Это позволяет выявлять главные факторы, которые приводят к задержкам после ввода. Подробнее о том, [как предотвратить появление длительных задач в основном потоке](https://web.dev/articles/long-tasks-devtools)…"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Обнаружена # длительная задача}one{Обнаружена # длительная задача}few{Обнаружено # длительные задачи}many{Обнаружено # длительных задач}other{Обнаружено # длительной задачи}}"}, "core/audits/long-tasks.js | title": {"message": "Избегайте длительных задач в основном потоке"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категория"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Рекомендуем сократить время на обработку, компиляцию и выполнение скриптов JS. Для этого вы можете разбить код JS на небольшие фрагменты. Подробнее о том, [как минимизировать работу в основном потоке](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)…"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Минимизируйте работу в основном потоке"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Минимизация работы в основном потоке"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Для максимального охвата аудитории сайт должен поддерживать все основные браузеры. Подробнее [о совместимости с разными браузерами](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)…"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Сайт работает в разных браузерах"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Убедитесь, что у каждой страницы есть уникальный URL, чтобы контент, к которому они ведут, было удобно распространять в социальных сетях. Подробнее [об указании ссылок на контент](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)…"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "У каждой страницы есть URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Переходы должны создавать впечатление мгновенного отклика даже при медленной работе сети. Это имеет решающее значение для удобства работы с приложением. Подробнее [о переходах между страницами](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)…"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Во время перехода между страницами нет ощущения, что они ожидают ответа от сети"}, "core/audits/maskable-icon.js | description": {"message": "Создайте маскируемый значок. Тогда к значку вашего установленного приложения не будет добавляться белый фон. Подробнее [о маскируемых значках из файла манифеста](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)…"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Манифест не содержит маскируемый значок"}, "core/audits/maskable-icon.js | title": {"message": "Манифест содержит маскируемый значок"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Совокупное смещение макета – это величина, на которую смещаются видимые элементы области просмотра при загрузке. Подробнее о [совокупном смещении макета](https://web.dev/articles/cls)…"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Первая отрисовка контента – показатель, который отражает время между началом загрузки страницы и появлением первого изображения или блока текста. Подробнее [о первой отрисовке контента](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)…"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Первая значимая отрисовка – показатель, определяющий интервал времени между началом загрузки страницы и появлением основного контента. Подробнее [о первой значимой отрисовке](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)…"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Взаимодействие до следующей отрисовки – показатель скорости отклика страницы. Он отражает, через какое время становится виден ответ страницы на ввод данных пользователем. Подробнее [о взаимодействии до следующей отрисовки](https://web.dev/articles/inp)…"}, "core/audits/metrics/interactive.js | description": {"message": "Время загрузки для взаимодействия – показатель, который отражает время, за которое страница полностью подготавливается к взаимодействию с пользователем. Подробнее [о времени загрузки для взаимодействия](https://developer.chrome.com/docs/lighthouse/performance/interactive/)…"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Отрисовка самого крупного контента – показатель, который отражает время, требуемое на полную отрисовку самого крупного изображения или текстового блока. Подробнее [об отрисовке самого крупного контента](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)…"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Максимальная потенциальная задержка после первого ввода показывает время выполнения самой длительной задачи. Подробнее [о максимальной потенциальной задержке после первого ввода](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)…"}, "core/audits/metrics/speed-index.js | description": {"message": "Индекс скорости загрузки показывает, как быстро на странице появляется контент. Подробнее [об индексе скорости загрузки](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)…"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Сумма (в миллисекундах) всех периодов от первой отрисовки контента до загрузки для взаимодействия, когда скорость выполнения задач превышала 50 мс. Подробнее [об общем времени блокировки](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)…"}, "core/audits/network-rtt.js | description": {"message": "Время прохождения сигнала сети (RTT) напрямую влияет на производительность сайта. Высокое значение этого показателя может означать, что необходимо использовать серверы, которые находятся ближе к пользователю. Подробнее [о времени прохождения сигнала](https://hpbn.co/primer-on-latency-and-bandwidth/)…"}, "core/audits/network-rtt.js | title": {"message": "Время прохождения сигнала сети"}, "core/audits/network-server-latency.js | description": {"message": "Задержки со стороны сервера могут влиять на скорость загрузки страниц. Высокое время реакции сервера говорит о его перегруженности или недостаточной производительности. Подробнее [о времени ответа сервера](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)…"}, "core/audits/network-server-latency.js | title": {"message": "Задержка со стороны сервера"}, "core/audits/no-unload-listeners.js | description": {"message": "Событие `unload` активируется не всегда. Если для него создан прослушиватель, это может привести к ошибкам средств оптимизации браузера, например функции возвратного кеша. Рекомендуем использовать события `pagehide` или `visibilitychange`. Подробнее [о прослушивателе событий unload](https://web.dev/articles/bfcache#never_use_the_unload_event)…"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Регистрируется прослушиватель для события `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Не используются прослушиватели события `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Некомбинированные анимации могут пропускать кадры и усиливать совокупное смещение макета. Подробнее о том, [как убрать некомбинированные анимации](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)…"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Обнаружен # анимированный элемент}one{Обнаружен # анимированный элемент}few{Обнаружено # анимированных элемента}many{Обнаружено # анимированных элементов}other{Обнаружено # анимированного элемента}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Свойство, связанное с фильтром, может передвигать пиксели."}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Несовместимая анимация."}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "В эффекте вместо режима replace используется другой режим."}, "core/audits/non-composited-animations.js | title": {"message": "Избегайте некомбинированных анимаций"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Свойство, связанное с преобразованием, зависит от размера элемента."}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Неподдерживаемое свойство CSS: {properties}}one{Неподдерживаемые свойства CSS: {properties}}few{Неподдерживаемые свойства CSS: {properties}}many{Неподдерживаемые свойства CSS: {properties}}other{Неподдерживаемые свойства CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Параметры времени в настройках эффекта не поддерживаются"}, "core/audits/performance-budget.js | description": {"message": "Следите за тем, чтобы количество и размер сетевых запросов соответствовали целям, установленным в бюджете производительности. Подробнее [о бюджетах производительности](https://developers.google.com/web/tools/lighthouse/audits/budgets)…"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 запрос}one{# запрос}few{# запроса}many{# запросов}other{# запроса}}"}, "core/audits/performance-budget.js | title": {"message": "Бюджет производительности"}, "core/audits/preload-fonts.js | description": {"message": "Выполняйте предварительную загрузку шрифтов, задав дескриптору font-display значение `optional`, чтобы новые посетители могли воспользоваться ими. Подробнее [о предзагрузке шрифтов](https://web.dev/articles/preload-optional-fonts)…"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Шрифты со свойством `font-display: optional` не загружены"}, "core/audits/preload-fonts.js | title": {"message": "Шрифты со свойством `font-display: optional` предварительно загружены"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Если вы динамически добавляете элемент LCP на страницу, настройте его предзагрузку, чтобы оптимизировать время отрисовки самого крупного контента. Подробнее [о предзагрузке элементов LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)…"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Предзагрузите изображение для элемента \"Отрисовка самого крупного контента\""}, "core/audits/redirects.js | description": {"message": "Переадресации могут стать причиной дополнительных задержек при загрузке страницы. Подробнее о том, [как избежать переадресаций страниц](https://developer.chrome.com/docs/lighthouse/performance/redirects/)…"}, "core/audits/redirects.js | title": {"message": "Избегайте большого количества переадресаций"}, "core/audits/seo/canonical.js | description": {"message": "Канонические ссылки помогают определить, какой URL будет показан в результатах поиска. Подробнее [о канонических ссылках](https://developer.chrome.com/docs/lighthouse/seo/canonical/)…"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Несколько конфликтующих URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Недопустимый URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Указывает на другое расположение атрибута `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Указан не абсолютный URL ({url})."}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Каноническая ссылка ведет на корневой URL домена, а не на соответствующую страницу с контентом."}, "core/audits/seo/canonical.js | failureTitle": {"message": "В документе нет действительного атрибута `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Для документа указан действительный атрибут `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Ссылки, которые невозможно просканировать"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Поисковые системы при сканировании сайтов учитывают содержащиеся в ссылках атрибуты `href`. Чтобы на вашем сайте могло быть проиндексировано максимально возможное количество страниц, атрибуты `href` в анкерах должны корректно ссылаться на целевые страницы. Подробнее о том, как [сделать ссылки доступными для сканирования](https://support.google.com/webmasters/answer/9112205)…"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Ссылки невозможно просканировать"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Ссылки можно просканировать"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Дополнительный нечитабельный текст"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Размер шрифта"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% текста на странице"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Селектор"}, "core/audits/seo/font-size.js | description": {"message": "Если вы хотите, чтобы текст легко читался, размер шрифта должен составлять не менее 12 пикселей. В противном случае пользователям мобильных устройств придется увеличивать масштаб страницы для чтения. Желательно, чтобы более 60 % текста на странице было написано шрифтом размером не менее 12 пикселей. Подробнее [об оптимальных размерах шрифтов](https://developer.chrome.com/docs/lighthouse/seo/font-size/)…"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} текста можно легко прочитать"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Слишком мелкий текст. Настройте область просмотра для экранов мобильных устройств с помощью метатега viewport."}, "core/audits/seo/font-size.js | failureTitle": {"message": "В документе используются шрифты слишком маленького размера"}, "core/audits/seo/font-size.js | legibleText": {"message": "Читабельный текст"}, "core/audits/seo/font-size.js | title": {"message": "В документе используются шрифты оптимального размера"}, "core/audits/seo/hreflang.js | description": {"message": "Добавьте на страницу элементы link с атрибутом hreflang. Тогда в результатах поиска будут представлены те версии ваших страниц, которые лучше всего подходят для языка и региона пользователя. Подробнее [об атрибуте `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)…"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "В документе нет действительного атрибута `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Недопустимое значение атрибута href"}, "core/audits/seo/hreflang.js | title": {"message": "Для документа указан действительный атрибут `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Недопустимый код языка"}, "core/audits/seo/http-status-code.js | description": {"message": "Если страница возвращает код статуса HTTP, который говорит об ошибках, она может не индексироваться правильно. Подробнее [о кодах статусов HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)…"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Код статуса HTTP недействителен"}, "core/audits/seo/http-status-code.js | title": {"message": "Код статуса HTTP действителен"}, "core/audits/seo/is-crawlable.js | description": {"message": "Поисковые системы не смогут включать ваши страницы в результаты поиска, если вы не предоставите разрешение на сканирование. Подробнее [о директивах для поисковых роботов](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)…"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Страница недоступна для индексации"}, "core/audits/seo/is-crawlable.js | title": {"message": "Страница доступна для индексации"}, "core/audits/seo/link-text.js | description": {"message": "Сделайте текст ссылок содержательным, чтобы поисковые системы лучше распознавали ваш контент. Подробнее о том, [как повысить доступность ссылок](https://developer.chrome.com/docs/lighthouse/seo/link-text/)…"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Найдена 1 ссылка}one{Найдена # ссылка}few{Найдено # ссылки}many{Найдено # ссылок}other{Найдено # ссылки}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "У ссылок нет описаний"}, "core/audits/seo/link-text.js | title": {"message": "У ссылок есть описания"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Чтобы протестировать структурированные данные, воспользуйтесь инструментами для их [проверки](https://search.google.com/structured-data/testing-tool/) и [статического анализа](http://linter.structured-data.org/). Подробнее [о структурированных данных](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)…"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Структурированные данные действительны"}, "core/audits/seo/meta-description.js | description": {"message": "Метаописания содержат общие сведения о контенте страницы и могут быть показаны в результатах поиска. Подробнее [о метаописании](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)…"}, "core/audits/seo/meta-description.js | explanation": {"message": "Отсутствует описание."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "В документе нет метаописания"}, "core/audits/seo/meta-description.js | title": {"message": "В документе есть метаописание"}, "core/audits/seo/plugins.js | description": {"message": "Поисковые системы не могут индексировать содержимое плагинов. К тому же на многих устройствах использование плагинов ограничено или не поддерживается. Подробнее о том, [как отказаться от использования плагинов](https://developer.chrome.com/docs/lighthouse/seo/plugins/)…"}, "core/audits/seo/plugins.js | failureTitle": {"message": "В документе используются плагины"}, "core/audits/seo/plugins.js | title": {"message": "В документе нет плагинов"}, "core/audits/seo/robots-txt.js | description": {"message": "Если файл robots.txt поврежден, поисковые роботы могут не распознать ваши инструкции по сканированию или индексации сайта. Подробнее [о файле robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)…"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Код статуса HTTP, полученный в ответ на запрос файла robots.txt: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Обнаружена 1 ошибка}one{Обнаружена # ошибка}few{Обнаружено # ошибки}many{Обнаружено # ошибок}other{Обнаружено # ошибки}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Не удалось скачать файл robots.txt."}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Файл robots.txt недействителен"}, "core/audits/seo/robots-txt.js | title": {"message": "Файл robots.txt действителен"}, "core/audits/seo/tap-targets.js | description": {"message": "Интерактивные элементы, такие как кнопки и ссылки, должны быть достаточно крупными (48 x 48 пкс) или располагаться на достаточном расстоянии друг от друга. Тогда при нажатии пользователи не будут задевать соседние объекты. Подробнее [об оптимальных размерах интерактивных элементов](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)…"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "Размеры {decimalProportion, number, percent} интерактивных элементов соответствуют требованиям."}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Слишком маленькие интерактивные элементы. Настройте область просмотра для экранов мобильных устройств с помощью метатега viewport."}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Размер интерактивных элементов не оптимален"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Частичное совпадение элементов"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Интерактивный элемент"}, "core/audits/seo/tap-targets.js | title": {"message": "Размер интерактивных элементов оптимален"}, "core/audits/server-response-time.js | description": {"message": "Время ответа сервера для основного документа должно быть небольшим, так как все прочие запросы зависят от этого показателя. Подробнее [о времени до получения первого байта](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)…"}, "core/audits/server-response-time.js | displayValue": {"message": "Загрузка корневого документа заняла {timeInMs, number, milliseconds} мс"}, "core/audits/server-response-time.js | failureTitle": {"message": "Сократите время до получения первого байта от сервера"}, "core/audits/server-response-time.js | title": {"message": "Время до получения первого байта от сервера допустимое"}, "core/audits/splash-screen.js | description": {"message": "Приложение оставляет у пользователей более приятное впечатление, когда оно встречает их тематической заставкой. Подробнее [о заставках](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)…"}, "core/audits/splash-screen.js | failureTitle": {"message": "Собственная заставка не настроена"}, "core/audits/splash-screen.js | title": {"message": "Настроена собственная заставка"}, "core/audits/themed-omnibox.js | description": {"message": "Цвет адресной строки браузера можно изменить под цветовую тему сайта. Подробнее [об оформлении адресной строки](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)…"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Не изменяет цвет адресной строки в соответствии с темой"}, "core/audits/themed-omnibox.js | title": {"message": "Изменяет цвет адресной строки в соответствии с темой"}, "core/audits/third-party-cookies.js | description": {"message": "В будущей версии Chrome поддержка сторонних файлов cookie будет прекращена. Подробнее [об отказе от сторонних файлов cookie](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)…"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Обнаружен 1 файл cookie}one{Обнаружен # файл cookie}few{Обнаружено # файла cookie}many{Обнаружено # файлов cookie}other{Обнаружено # файла cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Используются сторонние файлы cookie"}, "core/audits/third-party-cookies.js | title": {"message": "Сторонние файлы cookie не используются"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (истории успеха)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (маркетинг)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (социальные сети)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (видео)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Продукт"}, "core/audits/third-party-facades.js | description": {"message": "Некоторые из сторонних встроенных объектов поддерживают отложенную загрузку. Пока они не понадобятся, используйте вместо них фасады. Подробнее о том, [как отложить загрузку сторонних объектов с помощью фасадов](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)…"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Доступен # альтернативный фасадный объект}one{Доступен # альтернативный фасадный объект}few{Доступно # альтернативных фасадных объекта}many{Доступно # альтернативных фасадных объектов}other{Доступно # альтернативного фасадного объекта}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Для отложенной загрузки с фасадными объектами доступно несколько сторонних ресурсов"}, "core/audits/third-party-facades.js | title": {"message": "Фасадные объекты сторонних ресурсов для отложенной загрузки"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Сторонний поставщик"}, "core/audits/third-party-summary.js | description": {"message": "Сторонний код может сильно замедлить загрузку страниц сайта. Рекомендуем использовать только самые необходимые сторонние ресурсы и сделать так, чтобы они загружались после основных элементов документа. Подробнее о том, [как минимизировать влияние стороннего кода](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)…"}, "core/audits/third-party-summary.js | displayValue": {"message": "Сторонний код заблокировал основной поток на {timeInMs, number, milliseconds} мс"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Уменьшите влияние стороннего кода"}, "core/audits/third-party-summary.js | title": {"message": "Уменьшение использования стороннего кода"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Единица измерения"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Показатель"}, "core/audits/timing-budget.js | description": {"message": "Указав предельное время загрузки страниц сайта, вы сможете отслеживать его производительность. Показателями хорошей производительности являются быстрая загрузка страниц и обработка событий ввода данных пользователем. Подробнее [о бюджетах производительности](https://developers.google.com/web/tools/lighthouse/audits/budgets)…"}, "core/audits/timing-budget.js | title": {"message": "Время на загрузку"}, "core/audits/unsized-images.js | description": {"message": "Чтобы уменьшить совокупное смещение макета и избежать проблем, связанных со смещением элементов, рекомендуем всегда явным образом задавать ширину и высоту для изображений. Подробнее о том, [как задавать размеры изображений](https://web.dev/articles/optimize-cls#images_without_dimensions)…"}, "core/audits/unsized-images.js | failureTitle": {"message": "Для изображений не заданы явным образом атрибуты `width` и `height`."}, "core/audits/unsized-images.js | title": {"message": "Для изображений явным образом заданы атрибуты `width` и `height`."}, "core/audits/user-timings.js | columnType": {"message": "Тип"}, "core/audits/user-timings.js | description": {"message": "Используйте User Timing API, чтобы измерить реальную производительность своего приложения во время ключевых моментов взаимодействия с пользователями. Подробнее [о метках пользовательского времени](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)…"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 временная метка}one{# временная метка}few{# временные метки}many{# временных меток}other{# временной метки}}"}, "core/audits/user-timings.js | title": {"message": "Метки и промежутки пользовательского времени"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Для страницы {securityOrigin} был найден компонент `<link rel=preconnect>`, не использованный браузером. Проверьте значение атрибута `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Чтобы быстро устанавливать соединение с необходимыми сторонними источниками, добавьте ресурсную подсказку `preconnect` или `dns-prefetch`. Подробнее о том, [как предварительно подключаться к необходимым источникам](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)…"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Используйте предварительное подключение к необходимым доменам"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Обнаружено больше двух предварительно подключенных ссылок `<link rel=preconnect>`. Их необходимо использовать в умеренном количестве и только для наиболее значимых источников."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Для страницы {securityOrigin} был найден компонент `<link rel=preconnect>`, не использованный браузером. Используйте только `preconnect` для значимых источников, которым страница будет точно отправлять запрос."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Для страницы {preloadURL} был найден предварительно загруженный компонент `<link>`, не использованный браузером. Проверьте значение атрибута `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Чтобы основные ресурсы загружались на странице в первую очередь, используйте для них элемент `<link rel=preload>`. Подробнее о том, [как выполнять предзагрузку ключевых запросов](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)…"}, "core/audits/uses-rel-preload.js | title": {"message": "Настройте предварительную загрузку ключевых запросов"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL карт"}, "core/audits/valid-source-maps.js | description": {"message": "Карты исходных кодов переводят минифицированный код в исходный. Благодаря этому разработчики могут выполнять отладку на действующем ресурсе. Кроме того, Lighthouse может предоставлять дополнительную информацию. Чтобы воспользоваться этими преимуществами, внедрите карты исходных кодов. Подробнее [о картах исходных кодов](https://developer.chrome.com/docs/devtools/javascript/source-maps/)…"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Отсутствуют карты исходного кода для собственных больших скриптов JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "В большом файле JavaScript отсутствует карта исходного кода."}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Внимание! В атрибуте `.sourcesContent` отсутствует 1 элемент.}one{Внимание! В атрибуте `.sourcesContent` отсутствует # элемент.}few{Внимание! В атрибуте `.sourcesContent` отсутствуют # элемента.}many{Внимание! В атрибуте `.sourcesContent` отсутствуют # элементов.}other{Внимание! В атрибуте `.sourcesContent` отсутствует # элемента.}}"}, "core/audits/valid-source-maps.js | title": {"message": "У страницы правильные карты исходного кода"}, "core/audits/viewport.js | description": {"message": "С помощью метатега `<meta name=\"viewport\">` можно не только оптимизировать приложение под экраны мобильных устройств, но и предотвратить [задержку длительностью 300 мс при вводе данных пользователем](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). Подробнее [об использовании метатега viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)…"}, "core/audits/viewport.js | explanationNoTag": {"message": "Метатег `<meta name=\"viewport\">` не найден."}, "core/audits/viewport.js | failureTitle": {"message": "Отсутствует метатег `<meta name=\"viewport\">` со свойством `width` или `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Присутствует метатег `<meta name=\"viewport\">` со свойством `width` или `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Это работа, блокирующая поток, которая происходит после взаимодействия со страницей и до следующей отрисовки. Подробнее [о взаимодействии до следующей отрисовки](https://web.dev/articles/inp)…"}, "core/audits/work-during-interaction.js | displayValue": {"message": "На обработку события {interactionType} ушло {timeInMs, number, milliseconds} мс"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Цель события"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Минимизируйте работу во время взаимодействия пользователя с клавишами"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Задержка ввода"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Задержка вывода ответа на экран"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Время обработки"}, "core/audits/work-during-interaction.js | title": {"message": "Минимизация работы во время взаимодействия пользователя с клавишами"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Проверьте, правильно ли заданы атрибуты ARIA. Они облегчают работу с вашим приложением пользователям с ограниченными возможностями."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Проверьте, доступны ли на вашем сайте описания для аудио- и видеоконтента. Это сделает сайт удобнее для пользователей с нарушениями зрения и слуха."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Аудио и видео"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Проверьте, соответствует ли ваш сайт рекомендациям по оптимизации для поисковых систем."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Рекомендации"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Узнайте, какие трудности могут возникнуть у людей с ограниченными возможностями при использовании вашего веб-приложения, и [сделайте его доступнее](https://developer.chrome.com/docs/lighthouse/accessibility/). Автоматические проверки не гарантируют доступность приложения, поэтому мы рекомендуем выполнять [тестирование вручную](https://web.dev/articles/how-to-review). Оно поможет выявить оставшиеся проблемы."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ручная проверка позволяет охватить области, которые невозможно протестировать автоматически. Подробнее [о проверке специальных возможностей](https://web.dev/articles/how-to-review)…"}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Специальные возможности"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Проверьте, хорошо ли виден ваш текст."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контрастность"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Проверьте, правильно ли заданы атрибуты языков для программ чтения с экрана."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Интернационализация и локализация"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Проверьте, насколько элементы управления в вашем приложении различимы для программ чтения с экрана."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Названия и ярлыки"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Проверьте, удобно ли пользователям перемещаться по вашему приложению с помощью клавиатуры."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навигация"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Проверьте, насколько эффективно программы чтения с экрана распознают данные в таблицах и списках на вашем сайте."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Таблицы и списки"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Совместимость с браузерами"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Рекомендации"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Общие рекомендации"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Надежность и безопасность"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Удобство для пользователей"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "В бюджете производительности устанавливаются нормы для производительности вашего сайта."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Подробная информация о производительности вашего приложения. Эти цифры не влияют на показатель производительности [напрямую](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Диагностика"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Один из самых важных параметров производительности – насколько быстро пиксели отображаются на экране. Ключевые показатели: \"Время загрузки первого контента\" и \"Время загрузки достаточной части контента\"."}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Уменьшение времени загрузки контента"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Эти рекомендации могут помочь вам ускорить загрузку страницы. Они не влияют на показатель производительности [напрямую](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Оптимизация"}, "core/config/default-config.js | metricGroupTitle": {"message": "Показатели"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Улучшите параметры загрузки, чтобы страница была готова для работы как можно скорее. Ключевые показатели: \"Время загрузки для взаимодействия\" и \"Индекс скорости загрузки\"."}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Общие улучшения"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Производительность"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Здесь проверяется соответствие нормам современных веб-приложений. Подробнее о том, [как создать качественное современное веб-приложение](https://web.dev/articles/pwa-checklist)…"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Lighthouse не проверяет эти пункты автоматически, но они необходимы для соответствия базовому [контрольному списку современных веб-приложений](https://web.dev/articles/pwa-checklist). Они не влияют на показатель приложения, однако их следует проверить вручную."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Возможность установки"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Соответствие рекомендациям для PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Эти проверки позволяют узнать, соответствует ли страница основным рекомендациям к поисковой оптимизации. Lighthouse оценивает не все факторы, которые могут повлиять на позицию сайта в результатах поиска (например, производительность по [основным интернет-показателям](https://web.dev/explore/vitals)). Подробнее [о факторах, важных для Google Поиска](https://support.google.com/webmasters/answer/35769)…"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Проверьте, соответствует ли ваш сайт рекомендациям по поисковой оптимизации (SEO), с помощью этих дополнительных сервисов."}, "core/config/default-config.js | seoCategoryTitle": {"message": "Поисковая оптимизация"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Оптимизируйте HTML-код, чтобы поисковые роботы могли лучше проанализировать контент приложения."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Рекомендации в отношении контента"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Чтобы ваше приложение появлялось в результатах поиска, предоставьте доступ к нему поисковым роботам."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Сканирование и индексирование"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Убедитесь, что ваши страницы оптимизированы для мобильных устройств, чтобы пользователям не приходилось менять масштаб страниц или подстраивать их под размер экрана. Подробнее о том, [как оптимизировать страницы для мобильных устройств](https://developers.google.com/search/mobile-sites/)…"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Оптимизация для мобильных устройств"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "У тестируемого устройства недостаточно быстрый процессор. Это может негативно повлиять на показатель производительности. Подробнее [о калибровке множителя замедления процессора](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)…"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Эта страница может загружаться неправильно, потому что тестовый URL ({requested}) был перенаправлен на {final}. Попробуйте напрямую проверить второй URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Страница загружалась слишком медленно и не загрузилась в отведенное время. Результаты могут быть неполными."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Превышено время ожидания для очистки кеша браузера. Ещё раз проверьте эту страницу. Если проблема не исчезла, сообщите об ошибке."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{На скорость загрузки могут влиять данные из этого хранилища: {locations}. Попробуйте открыть страницу в режиме инкогнито.}one{На скорость загрузки могут влиять данные из этих хранилищ: {locations}. Попробуйте открыть страницу в режиме инкогнито.}few{На скорость загрузки могут влиять данные из этих хранилищ: {locations}. Попробуйте открыть страницу в режиме инкогнито.}many{На скорость загрузки могут влиять данные из этих хранилищ: {locations}. Попробуйте открыть страницу в режиме инкогнито.}other{На скорость загрузки могут влиять данные из этих хранилищ: {locations}. Попробуйте открыть страницу в режиме инкогнито.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Превышено время ожидания для удаления исходных данных. Ещё раз проверьте эту страницу. Если проблема не исчезла, сообщите об ошибке."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Ошибка обработки заголовка `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Во время запуска обнаружена навигация по страницам. Использовать режим анализа временного диапазона для проверки навигации по страницам не рекомендуется. Для этого подходит режим навигации. Он обеспечивает более эффективную атрибуцию и обнаружение основных потоков."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "В возвратный кеш можно добавить только страницы, загруженные с помощью GET-запроса."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Можно кешировать только страницы с кодом статуса 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome обнаружил попытку выполнить код JavaScript в кеше."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Страницы, которые запросили AppBanner, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Возвратный кеш отключен из-за запроса keepalive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Возвратный кеш отключен в разделе экспериментальных параметров Chrome. Введите chrome://flags/#back-forward-cache в адресной строке, чтобы включить кеш на этом устройстве."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Возвратный кеш отключен с помощью командной строки."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Возвратный кеш отключен из-за недостаточного объема памяти."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Возвратный кеш не поддерживается представителем."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Возвратный кеш отключен для системы предварительной отрисовки."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Страницу нельзя кешировать, так как у нее есть экземпляр BroadcastChannel с зарегистрированными прослушивателями."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Страницы с заголовком cache-control:no-store нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Кеш был намеренно очищен."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Страница удалена из кеша, чтобы освободить место для другой страницы."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Страницы с плагинами в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Страницы, которые используют FileChooser API, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Страницы, которые используют File System Access API, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Страницы, которые используют диспетчер медиаустройств, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "При переходе на другую страницу медиапроигрыватель воспроизводил контент."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Страницы, которые используют MediaSession API и установили состояние воспроизведения, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Страницы, которые используют MediaSession API и для которых настроены обработчики действий, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Возвратный кеш отключен, так как работает программа чтения с экрана."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Страницы, которые используют SecurityHandler, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Страницы, которые используют Serial API, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Страницы, которые используют WebAuthetication API, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Страницы, которые используют WebBluetooth API, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Страницы, которые используют WebUSB API, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Возвратный кеш отключен, так как на странице, использующей `Cache-Control: no-store`, отключены файлы cookie."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Страницы, которые используют Dedicated Worker или Worklet, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Переход со страницы выполнен до завершения загрузки документа."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "При переходе на другую страницу был показан баннер приложения."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "При переходе на другую страницу работал Менеджер паролей Chrome."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "При переходе на другую страницу выполнялся процесс DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "При переходе на другую страницу работало средство просмотра DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Возвратный кеш отключен, так как расширения используют API для обмена сообщениями."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Расширения должны закрывать долговременные подключения перед записью возвратного кеша."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Расширения с долговременными подключениями попытались отправлять сообщения во фреймы в возвратном кеше."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Возвратный кеш отключен из-за работы расширений."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "При переходе на другую страницу было показано модальное диалоговое окно (например, связанное с повторной отправкой формы или паролем в протоколе HTTP)."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "При переходе на другую страницу была показана ее офлайн-версия."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "При переходе на другую страницу была показана строка с сообщением о нехватке памяти."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "При переходе на другую страницу обнаружены запросы разрешений."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "При переходе на другую страницу работал блокировщик всплывающих окон."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "При переходе на другую страницу были показаны данные Безопасного просмотра."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Сервис \"Безопасный просмотр\" заблокировал всплывающее окно, так как страница содержит недопустимый контент."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Скрипт Service Worker был активирован, когда страница находилась в возвратном кеше."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Возвратный кеш отключен из-за ошибки документа."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Страницы, на которых используется элемент FencedFrames, нельзя сохранить в возвратный кеш."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Страница удалена из кеша, чтобы освободить место для другой страницы."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Страницы, которые предоставили доступ к трансляции мультимедиа, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Страницы, которые используют порталы, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Страницы, которые используют IdleManager, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Страницы с открытым подключением IndexedDB в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Возвратный кеш отключен из-за события IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Использовались недопустимые API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Страницы, в которые с помощью расширений внедряется `JavaScript`, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Страницы, в которые с помощью расширений внедряется `StyleSheet`, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Внутренняя ошибка."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Возвратный кеш отключен из-за запроса keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Страницы, которые используют блокировку клавиатуры, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | loading": {"message": "Переход со страницы выполнен до завершения ее загрузки."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Страницы, в основном ресурсе которых есть заголовок cache-control:no-cache, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Страницы, в основном ресурсе которых есть заголовок cache-control:no-store, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Страницу не удалось восстановить из возвратного кеша до отмены перехода."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Страница удалена из кеша, так как через активное сетевое подключение получено слишком много данных. Chrome ограничивает объем данных, передаваемых на кешируемую страницу."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Страницы, которые используют событие inflight fetch() или объект XHR, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Страница удалена из возвратного кеша, так как активный сетевой запрос был выполнен с переадресацией."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Страница удалена из кеша, так как подключение к сети было открыто слишком долго. Chrome ограничивает время получения данных кешируемой страницей."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Страницы без действительного заголовка ответа нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Переход выполнен во фрейме, отличном от основного."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Страни<PERSON><PERSON>, на которой выполняются транзакции индексированной базы данных, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Страницы с активным сетевым запросом в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Страницы с активным сетевым запросом на извлечение в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Страницы с активным сетевым запросом в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Страницы с активным сетевым запросом XHR в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Страницы, которые используют PaymentManager, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Страницы, которые используют режим \"картинка в картинке\", в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | portal": {"message": "Страницы, которые используют порталы, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | printing": {"message": "Страницы, на которых показан интерфейс печати, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Страница открыта с помощью `window.open()`, и на нее ссылается другая вкладка, или страница открыта в другом окне."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "При отрисовке страницы, находящейся в возвратном кеше, произошла ошибка."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Отрисовка страницы, находящейся в возвратном кеше, прервана."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Страницы, которые запросили разрешения на запись аудио, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Страницы, запросившие разрешения на доступ к датчикам, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Страницы, которые запросили разрешения на фоновую синхронизацию или извлечение, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Страницы, запросившие разрешения на доступ к MIDI-устройствам, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Страницы, запросившие разрешения на уведомления, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Страницы, запросившие доступ к хранилищу, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Страницы, которые запросили разрешения на запись видео, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Можно кешировать только страницы со схемой URL HTTP или HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Страница была запрошена скриптом Service Worker, когда находилась в возвратном кеше."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Скрипт Service Worker пытался отправить свойство `MessageEvent` на страницу в возвратном кеше."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Регистрация Service Worker была отменена, когда страница находилась в возвратном кеше."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Страница удалена из возвратного кеша из-за активации Service Worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Браузер Chrome перезапущен. Все записи возвратного кеша удалены."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Страницы, которые используют SharedWorker, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Страницы, которые используют SpeechRecognizer, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Страницы, которые используют SpeechSynthesis, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Окно iframe на странице запустило переход, который не завершился."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Страницы, в подресурсе которых есть заголовок cache-control:no-cache, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Страницы, в подресурсе которых есть заголовок cache-control:no-store, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Время нахождения страницы в возвратном кеше истекло, так как превысило максимально допустимое."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Время добавления страницы в возвратный кеш истекло (возможно, обработчики событий pagehide выполняются слишком долго)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "В основном фрейме страницы есть обработчик выгрузки."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "В субфрейме страницы есть обработчик выгрузки."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Браузер изменил заголовок переопределения агента пользователя."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Страницы, которые разрешили запись видео или аудио, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Страницы, которые используют WebDatabase, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Страницы, которые используют WebHID, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Страницы, которые используют WebLocks, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Страницы, которые используют WebNFC, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Страницы, которые используют WebOTPService, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Страницы, которые используют WebRTC, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Страницы, которые используют WebShare, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Страницы, которые используют WebSocket, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Страницы, которые используют WebTransport, нельзя добавить в возвратный кеш."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Страницы, которые используют WebXR, в настоящее время нельзя добавить в возвратный кеш."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Чтобы обеспечить обратную совместимость с предыдущими версиями браузеров, добавьте в URL протоколы HTTPS и HTTP (игнорируются браузерами с поддержкой `'strict-dynamic'`)."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Директива `disown-opener` не поддерживается, начиная с CSP3. Используйте вместо нее заголовок Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Директива `referrer` не поддерживается, начиная с CSP2. Используйте вместо нее заголовок Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Директива `reflected-xss` не поддерживается, начиная с CSP2. Используйте вместо нее заголовок X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Отсутствие директивы `base-uri` позволяет внедренным тегам `<base>` устанавливать контролируемый злоумышленником домен в качестве базового URL для всех относительных (например, скриптов). Присвойте `base-uri` значение `'none'` или `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Отсутствие директивы `object-src` позволяет внедрять плагины, исполняющие небезопасные скрипты. Если возможно, присвойте `object-src` значение `'none'`."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Отсутствует директива `script-src`. Это позволяет выполнять небезопасные скрипты."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Возможно, отсутствует точка с запятой. {keyword} имеет синтаксис директивы, а не ключевого слова."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Для nonce нужно использовать набор символов base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "<PERSON><PERSON><PERSON><PERSON> nonce должна составлять не менее 8 символов."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Не используйте простые схемы URL ({keyword}) в этой директиве. Они допускают получение скриптов от небезопасного домена."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Не используйте простые подстановочные знаки ({keyword}) в этой директиве. Они допускают получение скриптов от небезопасного домена."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Место назначения для отчетов задается только директивой report-to. Она поддерживается исключительно в браузерах на основе Chromium, поэтому рекомендуется также использовать директиву `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Ни в одной политике CSP не задано место назначения для отчетов. Это усложняет управление политикой CSP и мониторинг сбоев."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Злоумышленники часто обходят белые списки хостов. Используйте вместо них nonce-значения или хеши политики CSP и, при необходимости, директиву `'strict-dynamic'`."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Неизвестная директива CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} — недопустимое ключевое слово."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Директива `'unsafe-inline'` позволяет выполнять небезопасные скрипты и обработчики событий на странице. Чтобы разрешать отдельные скрипты, используйте nonce-значения или хеши в политике CSP."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Чтобы обеспечить обратную совместимость с предыдущими версиями браузеров, добавьте директиву `'unsafe-inline'` (игнорируется браузерами с поддержкой nonce-значений и хешей)."}, "core/lib/deprecation-description.js | feature": {"message": "Дополнительную информацию можно найти на странице состояния функции."}, "core/lib/deprecation-description.js | milestone": {"message": "Это изменение вступит в силу в версии {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Используется устаревшая функция"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Авторизация не будет выполняться, если в заголовке CORS `Access-Control-Allow-Headers` вместо необходимых данных указан подстановочный знак (*)."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Чтобы отключить интеграцию Google Cast по умолчанию, вместо селектора `-internal-media-controls-overlay-cast-button` используйте атрибут `disableRemotePlayback`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Запросы ресурсов, в URL которых содержатся и удаленные пробельные символы (`(n|r|t)`), и знаки \"меньше\" (`<`), блокируются. Чтобы загружать такие ресурсы, удалите символы новой строки и используйте коды для знаков \"меньше\" в таких местах URL, где, например, указываются значения атрибутов элементов."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Метод `chrome.loadTimes()` больше не поддерживается. Вместо него используйте стандартный API Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Метод `chrome.loadTimes()` больше не поддерживается. Вместо него используйте стандартный API Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Метод `chrome.loadTimes()` больше не поддерживается. Вместо него используйте `nextHopProtocol` в стандартном API Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Файлы cookie с символом `(0|r|n)` не обрезаются, а отклоняются."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Ослаблять требования правила ограничения источника за счет изменения `document.domain` не рекомендуется. По умолчанию эта возможность будет отключена. Это предупреждение о прекращении поддержки относится к доступу из другого источника, который включен с помощью `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Вызов функции window.alert из окон iframe в других источниках больше не поддерживается. В дальнейшем эта возможность будет удалена."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Вызов функции window.confirm из окон iframe в других источниках больше не поддерживается. В дальнейшем эта возможность будет удалена."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "События изменения DOM, в том числе `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` и `DOMCharacterDataModified`, больше не поддерживаются (https://w3c.github.io/uievents/#legacy-event-types) и будут удалены. Используйте вместо них `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "URL из элемента <use> в SVG-файле больше не поддерживаются и в дальнейшем будут удалены."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Ослаблять требования правила ограничения источника за счет изменения `document.domain` не рекомендуется. Эта возможность больше не поддерживается и будет отключена по умолчанию. Теперь, чтобы использовать эту функцию, нужно отказаться от применения кластеров агента с ключом источника, отправив заголовок `Origin-Agent-Cluster: ?0` в ответе HTTP для документа и фреймов. Подробнее: https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Заголовок `Expect-CT` больше не поддерживается и будет удален. В Chrome проверка требуется для всех общедоступных доверенных сертификатов, выпущенных после 30 апреля 2018 года."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Методы `getCurrentPosition()` и `watchPosition()` больше не работают с небезопасными источниками. Чтобы использовать его, задайте для приложения безопасный источник, например работающий по протоколу HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Методы `getCurrentPosition()` и `watchPosition()` в небезопасных источниках больше не поддерживаются. Чтобы использовать его, задайте для приложения безопасный источник, например работающий по протоколу HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "Метод `getUserMedia()` больше не работает с небезопасными источниками. Чтобы использовать его, задайте для приложения безопасный источник, например работающий по протоколу HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` больше не поддерживается. Используйте `RTCPeerConnectionIceErrorEvent.address` или `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Следующие свойства, связанные с источником продавца и произвольными данными из события service worker `canmakepayment`, больше не поддерживаются и будут удалены: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Сайт запросил подресурс из сети, доступ к которому можно получить только благодаря расширенным сетевым разрешениям пользователей. Такие запросы делают внутренние устройства и серверы доступными через интернет, что повышает риск подделки межсайтовых запросов (CSRF) и утечки информации. Чтобы предотвратить такие ситуации, в Chrome прекращается поддержка запросов к внутренним подресурсам, которые инициируются в небезопасных контекстах. В будущем такие запросы будут блокироваться."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Поле `dailyUpdateUrl` в структуре `InterestGroups`, переданное в `joinAdInterestGroup()`, было переименовано в `updateUrl`, чтобы лучше отражать его поведение."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Загрузить CSS с помощью URL в формате `file:` можно, только если имя файла имеет расширение `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Из-за изменений в стандарте не рекомендуется использовать метод `SourceBuffer.abort()` для отмены асинхронного удаления диапазона, выполняемого в `remove()`. В будущем поддержка этого метода будет прекращена. Руководствуйтесь событием `updateend`. Метод `abort()` предназначен только для отмены асинхронного добавления медиаданных или сброса состояния синтаксического анализатора."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Устанавливать для `MediaSource.duration` значение меньше максимальной временной метки в воспроизводимых медиаданных для любых закодированных фреймов в буфере не рекомендуется из-за изменений стандарта. В будущем поддержка неявного удаления обрезанных медиаданных в буфере будет прекращена. Вместо этого придется явно выполнять метод `remove(newDuration, oldDuration)` для всех объектов `sourceBuffers`, для которых `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI запрашивает разрешение на использование, даже если в объекте `MIDIOptions` не указан параметр sysex."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Устаревший нестандартный атрибут `shadowroot` больше не поддерживается и *перестанет работать* в версии M119. Используйте вместо него новый стандартный атрибут `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Использовать Notification API из небезопасных источников больше нельзя. Укажите для своего приложения безопасный источник, например HTTPS. Подробнее: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Из окон iframe в других источниках больше нельзя запрашивать разрешение на использование Notification API. Запросите разрешение у фрейма верхнего уровня или откройте новое окно."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Для метода createImageBitmap параметр \\{`imageOrientation: 'none'`\\} больше не поддерживается. Используйте этот метод с параметром \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Ваш партнер обменивается данными с использованием устаревшей версии (D)TLS. Сообщите ему о необходимости это исправить."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Если назначить свойство `overflow: visible` тегам img, video или canvas, визуальный контент указанного элемента может выйти за пределы границ. Подробности: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "`paymentManager.instruments` больше не поддерживается. Используйте актуальный API для обработки платежей."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Вызов `PaymentRequest` API нарушает директиву Content-Security-Policy (CSP) `connect-src`. Этот метод обхода директивы больше не поддерживается. Добавьте идентификатор способа оплаты из `PaymentRequest` API (поле `supportedMethods`) в вашу директиву CSP `connect-src`."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Тип `StorageType.persistent` больше не поддерживается. Используйте стандартный тип `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Элемент `<source src>` с родительским объектом `<picture>` недействителен, поэтому игнорируется. Используйте `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Метод webkitCancelAnimationFrame связан с определенным поставщиком. Используйте вместо него стандартный метод cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Метод webkitRequestAnimationFrame связан с определенным поставщиком. Используйте вместо него стандартный метод requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen больше не поддерживается. Используйте Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() больше не поддерживается. Используйте Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() больше не поддерживается. Используйте Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() больше не поддерживается. Используйте Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() больше не поддерживается. Используйте Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen больше не поддерживается. Используйте Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Мы прекращаем поддержку `chrome.privacy.websites.privacySandboxEnabled` API. Он будет работать в рамках обратной совместимости до версии M113. Вместо него используйте `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` и `chrome.privacy.websites.adMeasurementEnabled`. Подробнее: https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ограничение `DtlsSrtpKeyAgreement` удалено. Вы указали для него значение `false`, которое интерпретируется как попытка использовать неподдерживаемый метод `SDES key negotiation`. Эта функция удалена. Используйте сервис, поддерживающий `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Ограничение `DtlsSrtpKeyAgreement` удалено. Вы указали для него значение `true`. Оно ни на что не влияет, поэтому ограничение можно удалить."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Функция обратного вызова getStats() больше не поддерживается и будет удалена. Используйте вместо нее соответствующую спецификации функцию getStats()."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Range.expand() больше не поддерживается. Используйте Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Запросы подресурсов, в URL которых содержатся встроенные учетные данные (например, `**********************/`), блокируются."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Параметр `rtcpMuxPolicy` больше не поддерживается и будет удален."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "Для `SharedArrayBuffer` требуется изоляция от междоменных источников. Подробнее: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Вызов метода `speechSynthesis.speak()` без активации пользователем не поддерживается. В дальнейшем эта возможность будет удалена."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Теперь, чтобы использовать объект `SharedArrayBuffer`, в расширениях необходимо включить изоляцию от междоменных источников. Подробнее: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Web SQL больше не поддерживается. Используйте SQLite WebAssembly или индексированную базу данных."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Дескриптор разрешения `window-placement` больше не поддерживается. Используйте вместо него `window-management`. Подробнее: https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Правило `window-placement` больше не поддерживается. Используйте вместо него `window-management`. Подробнее: https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "В объекте JSON ответа `XMLHttpRequest` не поддерживается UTF-16."}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Синхронные вызовы метода `XMLHttpRequest` в основном потоке больше не поддерживаются, так как они отрицательно влияют на удобство работы пользователей. Подробнее: https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Метод `supportsSession()` больше не поддерживается. Вместо него используйте метод `isSessionSupported()` и получайте результат в виде логического значения."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Время блокировки основного потока"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Время жизни кеша"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Описание"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Длительность"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Элемент"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Неподходящие элементы"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Расположение"}, "core/lib/i18n/i18n.js | columnName": {"message": "Название"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Сверх бюджета"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Запросы"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Размер файла"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурса"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Размер"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Источник"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Время начала"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Потраченное время"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Объем переданных данных"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенциальная экономия"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенциальная экономия"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенциальная экономия – {wastedBytes, number, bytes} КиБ"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Обнаружен 1 элемент}one{Обнаружен # элемент}few{Обнаружено # элемента}many{Обнаружено # элементов}other{Обнаружено # элемента}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенциальная экономия – {wastedMs, number, milliseconds} мс"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Время загрузки достаточной части контента"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON>ри<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Изображение"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Взаимодействие до следующей отрисовки"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Высокая"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Низкая"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Средняя"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Макс. потенц. задержка после первого ввода"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Медиа"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} мс"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Друг<PERSON>й"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Другие ресурсы"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Скрипт"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} сек."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Таблица стилей"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Сторонний"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Всего"}, "core/lib/lh-error.js | badTraceRecording": {"message": "При записи трассировки для вашей страницы произошла ошибка. Перезапустите Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Превышено время ожидания для первичного соединения с протоколом отладчика."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "При загрузке страницы в Chrome не были сделаны скриншоты. Проверьте, есть ли на странице видимый контент, и перезапустите Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-серверам не удалось определить IP-адрес по указанному домену."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "При сборе данных ресурса {artifactName} произошла ошибка ({errorMessage})."}, "core/lib/lh-error.js | internalChromeError": {"message": "Произошла внутренняя ошибка. Перезапустите Chrome и Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Не удалось собрать данные ресурса {artifactName}."}, "core/lib/lh-error.js | noFcp": {"message": "Контент на этой странице не был отрисован. Убедитесь, что окно браузера находится в активном режиме во время загрузки, и повторите попытку. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "На странице не отображается контент, связанный с показателем \"Отрисовка самого крупного контента\" (LCP). Удостоверьтесь, что страница содержит действительный элемент LCP, и повторите попытку. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Указана страница не в формате HTML (MIME-тип {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Текущая версия Chrome не поддерживает функцию \"{featureName}\". Чтобы посмотреть результаты полностью, обновите версию Chrome."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Не удалось загрузить страницу. Убедитесь, что URL введен правильно и сервер отвечает на все запросы."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Не удалось загрузить URL, так как страница не отвечает."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Сертификат безопасности для указанного URL недействителен ({securityMessages})."}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Браузер Chrome остановил загрузку страницы с межстраничным объявлением. Убедитесь, что URL введен правильно и сервер отвечает на все запросы."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Не удалось загрузить страницу. Убедитесь, что URL введен правильно и сервер отвечает на все запросы. Подробнее: {errorDetails}."}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Не удалось загрузить страницу. Убедитесь, что URL введен правильно и сервер корректно отвечает на все запросы. Код статуса: {statusCode}."}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Страница загружалась слишком долго. Уменьшите время загрузки, выполнив рекомендации из отчета, а затем перезапустите Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Истекло время ожидания ответа от протокола DevTools. Метод: {protocolMethod}."}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Контент загружался слишком долго. Время ожидания истекло."}, "core/lib/lh-error.js | urlInvalid": {"message": "Недействительный URL."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Не удалось загрузить страницу. Убедитесь, что URL введен правильно и сервер корректно отвечает на все запросы. Код статуса: {errorCode}."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Эта страница имеет MIME-тип XHTML. Lighthouse не поддерживает этот тип документов."}, "core/user-flow.js | defaultFlowName": {"message": "Путь пользователя ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Отчет о переходе на страницу ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Отчет о состоянии страницы на определенный момент времени ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Отчет об анализе временного диапазона ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Все отчеты"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Категории"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Специальные возможности"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Рекомендации"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Производительность"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Современное веб-приложение"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "Поисковая оптимизация"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Версия для компьютера"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Интерпретация отчета Lighthouse о пути пользователя"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Узнать о путях"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Использовать отчеты о навигации, чтобы…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Использовать отчеты о состоянии страницы на определенный момент времени, чтобы…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Использовать отчеты об анализе временного диапазона, чтобы…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Получить показатель производительности Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Измерить показатели загрузки страницы, например время отрисовки самого крупного контента и индекс скорости загрузки."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Оценить возможности современного веб-приложения."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Обнаружить проблемы доступности в одностраничных приложениях и сложных формах."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Оценить рекомендации в отношении меню и элементов интерфейса, участвующих во взаимодействии."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Измерить смещения макета и время выполнения JavaScript в ходе серии взаимодействий."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Узнать возможности для улучшения взаимодействия со страницами, которые используются в течение длительного времени, и одностраничными приложениями."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "С наибольшим влиянием"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} информационная проверка}one{{numInformative} информационная проверка}few{{numInformative} информационные проверки}many{{numInformative} информационных проверок}other{{numInformative} информационной проверки}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Мобильная версия"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Загрузка страницы"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "В отчетах о навигации представлен анализ загрузки одной страницы, в точности как в исходных отчетах Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Отчет о навигации"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} отчет о навигации}one{{numNavigation} отчет о навигации}few{{numNavigation} отчета о навигации}many{{numNavigation} отчетов о навигации}other{{numNavigation} отчета о навигации}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{Можно пройти {numPassableAudits} проверку}one{Можно пройти {numPassableAudits} проверку}few{Можно пройти {numPassableAudits} проверки}many{Можно пройти {numPassableAudits} проверок}other{Можно пройти {numPassableAudits} проверки}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{Пройдена {numPassed} проверка}one{Пройдена {numPassed} проверка}few{Пройдено {numPassed} проверки}many{Пройдено {numPassed} проверок}other{Пройдено {numPassed} проверки}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Средне"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Ошибка"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Плохо"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Сохранить"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Зарегистрированное состояние страницы"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "В отчетах о состоянии страницы на определенный момент времени представлен анализ конкретного состояния страницы (обычно после взаимодействия с пользователем)."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Отчет о состоянии страницы на определенный момент времени"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} отчет о состоянии страницы на определенный момент времени}one{{numSnapshot} отчет о состоянии страницы на определенный момент времени}few{{numSnapshot} отчета о состоянии страницы на определенный момент времени}many{{numSnapshot} отчетов о состоянии страницы на определенный момент времени}other{{numSnapshot} отчета о состоянии страницы на определенный момент времени}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Сводка"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Взаимодействие пользователя"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "В отчетах об анализе временного диапазона приводятся данные за произвольный период, чаще всего о взаимодействии пользователя со страницей."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Отчет об анализе временного диапазона"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} отчет об анализе временного диапазона}one{{numTimespan} отчет об анализе временного диапазона}few{{numTimespan} отчета об анализе временного диапазона}many{{numTimespan} отчетов об анализе временного диапазона}other{{numTimespan} отчета об анализе временного диапазона}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Отчет Lighthouse о пути пользователя"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Если на странице есть анимированный контент, используйте [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), чтобы уменьшить нагрузку на процессор, когда контент не отображается на экране."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Рекомендуем показывать все компоненты [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) в форматах WebP при указании соответствующего резервного объявления для других браузеров. [Подробнее…](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Используйте [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) для автоматической отложенной загрузки изображений. [Подробнее…](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Используйте инструменты, например [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer), для [серверного рендеринга страниц AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Проверьте в [документации по AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), все ли стили поддерживаются."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Атрибут [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) компонента [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) определяет, какие изображения использовать исходя из размера экрана. [Подробнее…](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Для рендеринга очень длинных списков рекомендуем использовать виртуальную прокрутку с помощью набора инструментов Component Dev Kit (CDK). [Подробнее…](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Примените [разделение кода на уровне маршрута](https://web.dev/route-level-code-splitting-in-angular/), чтобы уменьшить размер пакетов JavaScript. Рекомендуем также выполнять предварительное кеширование файлов с помощью скрипта [Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Если вы используете Angular CLI, создавайте сборки в рабочем режиме. [Подробнее…](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Если вы используете Angular CLI, включите карты исходного кода в рабочую версию сборки, чтобы проверять создаваемые пакеты. [Подробнее…](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Чтобы ускорить переход, выполняйте предзагрузку маршрутов. [Подробнее…](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Для управления контрольными точками изображений мы также рекомендуем использовать утилиту `BreakpointObserver` из набора инструментов Component Dev Kit (CDK). [Подробнее…](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Используйте сервисы, которые позволяют встраивать GIF-файлы на страницы сайта в формате видео HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Укажите свойство `@font-display` при выборе специальных шрифтов для темы."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Попробуйте изменить [стиль изображений в формате WebP](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) на своем сайте."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Чтобы применять отложенную загрузку изображений, установите [модуль Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search). Разрешив загружать скрытые изображения позже, вы ускорите отрисовку сайта."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Рекомендуем встраивать критически важный код CSS и JavaScript с помощью модуля, а менее важный код – с использованием атрибута."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "На время ответа влияют как используемые на сайте темы и модули, так и настройки сервера. Советуем найти более оптимизированную тему, тщательно подобрать модуль для оптимизации и/или обновить версию сервера. Серверы вашего хостинга должны поддерживать кеширование операционного кода PHP, кеширование в памяти для ускорения ответа баз данных (например, Redis или <PERSON>), а также оптимизированную логику приложения, чтобы быстрее подготавливать страницы к загрузке."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Чтобы уменьшить размер изображений на странице, используйте [адаптивные стили изображений](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8). Если вы используете Views для показа нескольких материалов на одной странице, рекомендуем применить разбивку на страницы, чтобы ограничить количество показываемых элементов."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Убедитесь, что включен параметр \"Объединить файлы CSS\" на странице \"Администрирование > Конфигурация > Разработка\".  Чтобы улучшить поддержку агрегирования ресурсов, используйте для сайта версию Drupal не ниже 10.1."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Убедитесь, что включен параметр \"Объединить файлы JavaScript\" на странице \"Администрирование > Конфигурация > Разработка\".  Чтобы улучшить поддержку агрегирования ресурсов, используйте для сайта версию Drupal не ниже 10.1."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Рекомендуем удалять ненужные правила CSS и подключать к страницам и их компонентам только те библиотек<PERSON>, которые требуются для их загрузки. Подробные сведения можно прочитать в [документации Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Чтобы найти подключенные библиотеки, которые загружают неиспользуемый код JavaScript, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Инструментах разработчика Chrome. Если на вашем сайте Drupal выключена агрегация CSS, определить проблемный модуль или тему можно по URL-адресу таблицы стилей. Обращайте внимание на модули и темы с большим количеством таблиц стилей, где при анализе покрытия кода преобладает красный цвет. Таблица стилей должна попадать в очередь, только если она используется на странице."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Рекомендуем удалять ненужные файлы JavaScript и подключать к страницам и их компонентам только те библиотеки <PERSON>, которые необходимы для их загрузки. Подробные сведения можно прочитать в [документации Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Чтобы найти подключенные библиотеки, которые загружают неиспользуемый код JavaScript, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в инструментах разработчика Chrome. Если на вашем сайте Drupal выключена агрегация JavaScript, определить проблемный модуль или тему можно по URL-адресу скрипта. Обращайте внимание на модули и темы с большим количеством скриптов, где при анализе покрытия кода преобладает красный цвет. Скрипт должен попадать в очередь, только если он используется на странице."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Перейдите на страницу \"Администрирование > Конфигурация > Разработка\" и укажите максимальный срок хранения кеша браузера и прокси-сервера. Подробнее о [кеше в Drupal и оптимизации производительности сайта](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)…"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Чтобы автоматически оптимизировать изображения, загруженные на сайт, и уменьшить их размер, сохранив при этом качество, используйте [специальный модуль](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search). Кроме того, убедитесь, что применяете для всех изображений на сайте нативные [адаптивные стили](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) Drupal (доступны в Drupal 8 и более поздних версий)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Чтобы добавить ресурсные подсказки preconnect и dns-prefetch, установите и настройте [модуль](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), который обеспечивает работу ресурсных подсказок для агента пользователя."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Убедитесь, что используете нативные [адаптивные стили изображений](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) Drupal (доступны в Drupal 8 и более поздних версий). Используйте адаптивные стили при отрисовке полей изображений в режимах просмотра и представлениях, а также при отрисовке изображений, загруженных через редактор WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Optimize Fonts`, чтобы автоматически применять функцию CSS `font-display`. Так пользователи смогут видеть текст, пока загружаются веб-шрифты."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Next-Gen Formats`, чтобы конвертировать изображения в формат WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Lazy Load Images`, чтобы откладывать загрузку изображений вне экрана."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройки `Critical CSS` и `Script Delay`, чтобы откладывать загрузку второстепенного кода JavaScript и CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Воспользуйтесь [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching), чтобы кешировать контент в нашей глобальной сети и уменьшать время до получения первого байта."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Minify CSS`, чтобы автоматически уменьшать размер кода CSS и снижать нагрузку на сеть."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Minify Javascript`, чтобы автоматически уменьшать размер кода JavaScript и снижать нагрузку на сеть."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Remove Unused CSS`, чтобы устранить эту проблему. Фреймворк будет искать и удалять CSS-классы, которые не применяются на страницах сайта, чтобы уменьшить размер файла."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Efficient Static Cache Policy`, чтобы задавать рекомендуемые значения в заголовке кеширования для статических ресурсов."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Next-Gen Formats`, чтобы конвертировать изображения в формат WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Pre-Connect Origins`, чтобы автоматически добавлять ресурсные подсказки `preconnect` и быстро устанавливать соединение с необходимыми сторонними источниками."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройки `Preload Fonts` и `Preload Background Images`, чтобы добавить ссылки `preload` в код вашего сайта. Так вы сможете откладывать загрузку запрошенных ресурсов."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Воспользуйтесь [Ezoic Leap](https://pubdash.ezoic.com/speed) и включите настройку `Resize Images`, чтобы настраивать размер изображений в зависимости от устройств пользователей. Так вы снизите нагрузку на сеть."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Чтобы автоматически оптимизировать формат изображений, используйте вместо `<img>` компонент `gatsby-plugin-image`. [Подробнее…](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Для автоматической отложенной загрузки изображений используйте вместо `<img>` компонент `gatsby-plugin-image`. [Подробнее…](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Используйте компонент `gatsby-plugin-image` и задавайте для свойств `loading` значение `eager`. [Подробнее…](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Чтобы откладывать загрузку второстепенных сторонних скриптов, используйте `Gatsby Script API`. [Подробнее…](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Чтобы удалять неиспользуемые правила из таблиц стилей, настройте плагин `PurgeCSS` в конфигурации `Gatsby`. [Подробнее…](https://purgecss.com/plugins/gatsby.html)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Чтобы обнаруживать неиспользуемый код JavaScript, установите `Webpack Bundle Analyzer`. [Подробнее…](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Настройте кеширование для неизменяемых объектов. [Подробнее…](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Чтобы настраивать качество изображений, используйте вместо `<img>` компонент `gatsby-plugin-image`. [Подробнее…](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Чтобы задавать подходящие размеры `sizes`, используйте компонент `gatsby-plugin-image`. [Подробнее…](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Используйте сервисы, которые позволяют встраивать GIF-файлы на страницы сайта в формате видео HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Чтобы загружаемые изображения автоматически конвертировались в оптимальный формат, используйте специальный [плагин](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) или сервис."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Чтобы отложить загрузку скрытых изображений, установите [плагин <PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) или подберите шаблон, который поддерживает такую возможность. С версии Joomla 4.0 все новые изображения [автоматически](https://github.com/joomla/joomla-cms/pull/30748) получают атрибут `loading`."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Используйте один из плагинов Joom<PERSON>, которые позволяют [встраивать критически важный код](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) или [откладывать загрузку менее важных ресурсов](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Обратите внимание, что такие плагины могут привести к сбоям в работе других шаблонов и плагинов на сайте, поэтому их понадобится тщательно протестировать."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "На время ответа влияют как используемые на сайте шаблоны и расширения, так и настройки сервера. Советуем найти более оптимизированный шаблон, тщательно подобрать расширение для оптимизации и/или обновить версию сервера."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Рекомендуем показывать на страницах категорий только начальные фрагменты материалов (например, со ссылкой \"Подробнее\"). Советуем также сократить количество материалов на одной странице, разбить длинные записи на несколько страниц или использовать плагин для отложенной загрузки комментариев."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Чтобы ускорить загрузку сайта, используйте [расширения Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) или специальные шаблоны, которые позволяют объединять, уменьшать и сжимать таблицы стилей CSS."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Чтобы ускорить загрузку сайта, используйте [расширения Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) или специальные шаблоны, которые позволяют объединять, уменьшать и сжимать таблицы стилей CSS."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Рекомендуем заменить или удалить [расширения Joomla](https://extensions.joomla.org/), которые загружают неиспользуемый код CSS. Чтобы найти такие расширения, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Инструментах разработчика Chrome. Определить проблемный плагин или тему можно по URL-адресу таблицы стилей. Обращайте внимание на плагины с большим количеством таблиц стилей, где при анализе покрытия кода преобладает красный цвет. Таблица стилей должна попадать в очередь, только если она используется на странице."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Рекомендуем заменить или удалить [расширения Joomla](https://extensions.joomla.org/), которые загружают неиспользуемый код JavaScript. Чтобы найти такие плагины, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Инструментах разработчика Chrome. Определить проблемное расширение можно по URL-адресу скрипта. Обращайте внимание на расширения с большим количеством скриптов, в которых при анализе покрытия кода преобладает красный цвет. Скрипт должен попадать в очередь, только если он используется на странице."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Подробнее о [кешировании в браузере на платформе Joomla](https://docs.joomla.org/Cache)…"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Рекомендуем использовать [плагин для сжатия изображений без потери качества](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Рекомендуем использовать [плагин для адаптивных изображений](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Чтобы включить сжатие текста, разрешите GZIP-сжатие страниц в настройках Joomla (\"Система > Общие настройки > Сервер\")."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Если вы не группируете файлы JavaScript, рекомендуем использовать [модуль пакетирования и предварительной загрузки](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Отключите встроенную функцию [группировки и минификации файлов JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) в Magento и используйте вместо нее [модуль пакетирования и предварительной загрузки](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Укажите свойство `@font-display` при [выборе специальных шрифтов](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "В [магази<PERSON><PERSON>](https://marketplace.magento.com/catalogsearch/result/?q=webp) можно найти множество сторонних расширений для использования новых графических форматов."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Рекомендуем изменить шаблоны продукции и каталогов для использования функции [отложенной загрузки](https://web.dev/native-lazy-loading) на веб-платформе."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Используйте [модуль интеграции с Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) в Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "В настройках для разработчиков своего магазина включите параметр Minify CSS Files (Уменьшать размер файлов CSS). [Подробнее…](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Используйте [Terser](https://www.npmjs.com/package/terser), чтобы уменьшить размер всех файлов JavaScript, загружаемых при развертывании статического контента, и отключите встроенную функцию минификации."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Отключите встроенную функцию [группировки файлов JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) в Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "В [магази<PERSON><PERSON>o](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) можно найти множество сторонних расширений для оптимизации изображений."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Ресурсные подсказки preconnect или dns-prefetch можно добавить, [изменив разметку темы](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "Чтобы добавить теги `<link rel=preload>`, [измените разметку темы](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Чтобы автоматически оптимизировать формат изображений, используйте вместо `<img>` компонент `next/image`. [Подробнее…](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Для автоматической отложенной загрузки изображений используйте вместо `<img>` компонент `next/image`. [Подробнее…](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Используйте компонент `next/image` и установите для параметра priority значение True, чтобы выполнять предзагрузку изображения LCP. [Подробнее…](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Используйте компонент `next/script`, чтобы отложить загрузку несущественных сторонних скриптов. [Подробнее…](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Используйте компонент `next/image`, чтобы обеспечить правильный выбор размеров изображений. [Подробнее…](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Настройте плагин `PurgeCSS` в конфигурации `Next.js`, чтобы удалить неиспользуемые правила из таблицы стилей. [Подробнее…](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Чтобы обнаруживать неиспользуемый код JavaScript, установите `Webpack Bundle Analyzer`. [Подробнее…](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Используйте `Next.js Analytics`, чтобы измерять фактическую эффективность приложения. [Подробнее…](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Включите кеширование для неизменяемых объектов и страниц `Server-side Rendered` (SSR). [Подробнее…](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Чтобы настраивать качество изображений, используйте вместо `<img>` компонент `next/image`. [Подробнее…](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Используйте компонент `next/image`, чтобы задавать подходящие размеры `sizes`. [Подробнее…](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Включите сжатие на сервере Next.js. [Подробнее…](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Свяжитесь с менеджером аккаунта, чтобы включить функцию [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Настроив ее, вы сможете приоритизировать и оптимизировать отрисовку страницы."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Установите желаемое значение для правила показа шрифта CSS с помощью функции [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) в настройках NitroPack."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Воспользуйтесь функцией [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513), чтобы автоматически преобразовывать изображения в формат WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Включите функцию [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images), чтобы отложить загрузку скрытых изображений."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Включите функцию [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) в настройках NitroPack, чтобы сократить время первоначальной загрузки."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Включите функцию [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361), чтобы уменьшить время отклика сервера и оптимизировать производительность."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Включите функцию [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) в настройках кеширования. Это позволит уменьшить размер файлов CSS, HTML и JavaScript, чтобы сократить время первоначальной загрузки."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Включите функцию [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) в настройках кеширования. Это позволит уменьшить размер файлов CSS, HTML и JavaScript, чтобы сократить время первоначальной загрузки."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Включите функцию [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS), чтобы удалить правила CSS, неприменимые к этой странице."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Настройте функцию [`<PERSON>ay<PERSON> Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) в NitroPack, чтобы откладывать загрузку скриптов до тех пор, пока они не потребуются."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Выберите функцию [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) в меню `Caching` и настройте срок действия кеша страниц, чтобы уменьшить время загрузки и повысить удобство использования."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Включите функцию [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack), чтобы автоматически сжимать и оптимизировать изображения, а также преобразовывать их в формат WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Включите функцию [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site), чтобы заранее оптимизировать изображения и привести их в соответствие с размерами контейнеров, в которых они показываются, для всех устройств."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Воспользуйтесь функцией [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) в настройках NitroPack, чтобы уменьшить размер файлов, отправляемых в браузер."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Используйте компонент `nuxt/image`, чтобы установить значение `format=\"webp\"`. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Используйте компонент `nuxt/image`, чтобы установить значение `loading=\"lazy\"` для закадровых изображений. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Используйте компонент `nuxt/image`, чтобы установить значение `preload` для изображения LCP. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Используйте компонент `nuxt/image`, чтобы явно задать значения для параметров `width` и `height`. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#width-height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Используйте компонент `nuxt/image`, чтобы задать подходящее значение для параметра `quality`. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Используйте компонент `nuxt/image`, чтобы задать подходящее значение для параметра `sizes`. [Подробнее…](https://image.nuxt.com/usage/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Смените анимированные GIF-файлы на видео](https://web.dev/replace-gifs-with-videos/), чтобы ускорить загрузку страниц. Также рекомендуем использовать современные форматы файлов, такие как [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) и [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder). Это позволит повысить эффективность сжатия более чем на 30 % по сравнению с показателями новейшего видеокодека VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Чтобы загружаемые изображения автоматически конвертировались в оптимальный формат, используйте специальный [плагин](https://octobercms.com/plugins?search=image) или сервис. [Изображения без потери качества в формате WebP](https://developers.google.com/speed/webp) на 26 % меньше по размеру изображений в формате PNG и на 25–34 % меньше изображений в формате JPEG при одинаковом значении индекса структурного сходства. Мы также рекомендуем использовать такой современный формат изображения, как [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Чтобы отложить загрузку скрытых изображений, установите [плагин отложенной загрузки](https://octobercms.com/plugins?search=lazy) или подберите тему, которая поддерживает такую возможность. Мы также рекомендуем [плагин AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Используйте один из плагинов, которые позволяют [встраивать критически важный код](https://octobercms.com/plugins?search=css). Такие плагины могут привести к сбоям в работе других плагинов на сайте, поэтому перед внедрением тщательно протестируйте их."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Темы, плагины и спецификации сервера – все это влияет на время ответа сервера. Советуем найти более оптимизированную тему, тщательно подобрать плагин для оптимизации и/или обновить сервер. October CMS также позволяет разработчикам использовать [`Queues`](https://octobercms.com/docs/services/queues), чтобы отложить обработку ресурсозатратной задачи, например отправку электронного письма. Это значительно ускоряет веб-запросы."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Рекомендуем показывать в списках записей только начальные фрагменты материалов (например, с кнопкой `show more`). Советуем также сократить количество материалов на одной странице, разбить длинные записи на несколько страниц или использовать плагин для отложенной загрузки комментариев."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Существует множество [плагинов](https://octobercms.com/plugins?search=css), которые ускоряют загрузку сайтов, объединяя, уменьшая или сжимая стили. Рекомендуем использовать такие плагины на этапе сборки."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Существует множество [плагинов](https://octobercms.com/plugins?search=javascript), которые ускоряют загрузку сайтов, объединяя, уменьшая или сжимая скрипты. Рекомендуем использовать такие плагины на этапе сборки."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Рекомендуем проверить [плагины](https://octobercms.com/plugins), которые загружают неиспользуемый код CSS на сайт. Чтобы найти такие плагины, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Инструментах разработчика Chrome. Определить проблемный плагин или тему можно по URL-адресу таблицы стилей. Обращайте внимание на плагины с большим количеством таблиц стилей, где при анализе покрытия кода преобладает красный цвет. Таблица стилей должна добавляться, только если она используется на странице."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Проверьте [плагины](https://octobercms.com/plugins?search=javascript), которые загружают неиспользуемый код JavaScript. Чтобы найти такие плагины, запустите [анализ покрытия кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Инструментах разработчика Chrome. Определить проблемный плагин или тему можно по URL-адресу скрипта. Обращайте внимание на плагины с большим количеством скриптов, в которых при анализе покрытия кода преобладает красный цвет. Плагин должен добавлять скрипт, только если он используется на странице."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Ознакомьтесь с информацией о том, [как предотвратить ненужные сетевые запросы с помощью кеширования HTTP](https://web.dev/http-cache/#caching-checklist). Вы можете использовать [плагины для ускорения скорости кеширования](https://octobercms.com/plugins?search=Caching)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Рекомендуем использовать [плагин для сжатия изображений без потери качества](https://octobercms.com/plugins?search=image)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Загружайте изображения напрямую из менеджера медиафайлов, чтобы убедиться, что их размеры соответствуют требованиям. Чтобы обеспечить оптимальный размер изображений, используйте [фильтр](https://octobercms.com/docs/markup/filter-resize) или [плагин](https://octobercms.com/plugins?search=image) для изменения размера."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Включите в настройках веб-сервера сжатие текста."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Рекомендуем использовать библиотеку для виртуализации списков, например `react-window`, чтобы уменьшить количество создаваемых узлов DOM, если на одной странице выполняется отрисовка множества повторяющихся элементов. [Подробнее…](https://web.dev/virtualize-long-lists-react-window/) Кроме того, если для повышения скорости загрузки применяется обработчик `Effect`, используйте [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) или [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo), чтобы исключить повторные отрисовки, и [пропускайте эффекты](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), пока не изменятся определенные зависимости."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Если вы используете React Router, максимально ограничьте применение компонента `<Redirect>` для [перехода по маршрутам](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Если вы применяете серверный рендеринг компонентов React, мы рекомендуем использовать методы `renderToPipeableStream()` или `renderToStaticNodeStream()`. Это позволит отправлять разметку частями, а не целиком и воссоздавать ее на устройстве клиента. [Подробнее…](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Если ваша система сборки автоматически уменьшает размер файлов CSS, перед развертыванием приложения убедитесь, что вы используете рабочую версию сборки. Для этого установите расширение \"Инструменты разработчика React\". [Подробнее…](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Если ваша система сборки автоматически уменьшает размер файлов JS, перед развертыванием приложения убедитесь, что вы используете рабочую версию сборки. Для этого установите расширение \"Инструменты разработчика React\". [Подробнее…](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Если вы не применяете серверный рендеринг, используйте метод `React.lazy()` для [разделения пакетов JavaScript](https://web.dev/code-splitting-suspense/). Разделить код можно также с помощью сторонней библиотеки, например [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Используйте подключаемый модуль React DevTools, который с помощью API профилировщика может оценить скорость визуализации ваших компонентов. [Подробнее…](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Поместите видео в `VideoBoxes`, настройте их с помощью `Video Masks` или добавьте `Transparent Videos`. [Подробнее…](https://support.wix.com/en/article/wix-video-about-wix-video)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Загрузите изображения, используя `Wix Media Manager`, чтобы они автоматически преобразовывались в формат WebP. Узнайте [о других способах оптимизации медиаконтента на сайте](https://support.wix.com/en/article/site-performance-optimizing-your-media)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Если вы [добавляете сторонний код](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) на вкладке `Custom Code` панели управления сайта, вызывайте или загружайте его в конце кода тела страницы. По возможности встраивайте маркетинговые инструменты в сайт с помощью [интеграций](https://support.wix.com/en/article/about-marketing-integrations) Wix. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix использует CDN и кеширование, чтобы как можно быстрее обрабатывать ответы для большинства посетителей. Рекомендуем [вручную включить кеширование](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) для сайта, особенно если вы используете код `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Просмотрите весь сторонний код, который вы добавили на сайт на вкладке `Custom Code` панели управления, и оставьте только необходимые сервисы. [Подробнее…](https://support.wix.com/en/article/site-performance-removing-unused-javascript)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Используйте сервисы, которые позволяют встраивать GIF-файлы на страницы сайта в формате видео HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Чтобы автоматически конвертировать загруженные изображения из формата JPEG в WebP (если это поддерживается), используйте плагин [Performance Lab](https://wordpress.org/plugins/performance-lab/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Чтобы отложить загрузку скрытых страниц, установите [плагин WordPress](https://wordpress.org/plugins/search/lazy+load/) или подберите тему, которая поддерживает такую возможность. Мы также рекомендуем [плагин AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Используйте плагины WordPress, которые позволяют [встроить критическую часть данных](https://wordpress.org/plugins/search/critical+css/) или [отложить загрузку менее важных ресурсов](https://wordpress.org/plugins/search/defer+css+javascript/). Обратите внимание, что такие плагины могут привести к сбоям в работе других используемых вами тем или плагинов. Не исключено, что вам потребуется внести изменения в код."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Темы, плагины и спецификации сервера – все это влияет на время ответа сервера. Советуем найти более подходящую тему, тщательно подобрать плагин для оптимизации и/или обновить сервер."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Мы рекомендуем включить показ только начального фрагмента записей (например, с помощью тега More). Вы также можете сократить количество записей на одной странице, разбить длинные записи на несколько страниц или использовать отложенную загрузку комментариев."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Ускорьте загрузку сайта с помощью [плагинов WordPress](https://wordpress.org/plugins/search/minify+css/), которые позволяют объединять, уменьшать и сжимать стили. Рекомендуем использовать такие плагины на этапе сборки."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Ускорьте загрузку сайта с помощью [плагинов WordPress](https://wordpress.org/plugins/search/minify+javascript/), которые позволяют объединять, уменьшать и сжимать скрипты. Рекомендуем использовать такие плагины на этапе сборки."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Рекомендуем заменить или удалить [плагины WordPress](https://wordpress.org/plugins/), которые загружают неиспользуемый код CSS на вашей странице. Чтобы найти такие плагины, запустите [анализ покрытия кода](https://developer.chrome.com/docs/devtools/coverage/) в инструментах разработчика Chrome. Определить нужный плагин или тему можно по URL таблицы стилей. Обращайте внимание на плагины с большим количеством таблиц стилей, в которых при анализе покрытия кода преобладает красный цвет. Таблица стилей должна попадать в очередь, только если она используется на странице."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Рекомендуем заменить или удалить [плагины WordPress](https://wordpress.org/plugins/), которые загружают неиспользуемый код JavaScript на вашей странице. Чтобы найти такие плагины, запустите [анализ покрытия кода](https://developer.chrome.com/docs/devtools/coverage/) в инструментах разработчика Chrome. Определить нужный плагин или тему можно по URL скрипта. Обращайте внимание на плагины с большим количеством скриптов, в которых при анализе покрытия кода преобладает красный цвет. Скрипт должен попадать в очередь, только если он используется на странице."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Подробнее [о кешировании в браузере на платформе WordPress](https://wordpress.org/support/article/optimization/#browser-caching)…"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Чтобы сжимать изображения без потери качества, используйте [специальный плагин WordPress](https://wordpress.org/plugins/search/optimize+images/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Чтобы привести размеры ваших изображений в соответствие с требованиями, загрузите их в [библиотеку файлов](https://wordpress.org/support/article/media-library-screen/). Затем вставьте их на сайт прямо из библиотеки или используйте виджет изображений. Таким образом вы сможете обеспечить оптимальный размер изображений (в том числе для контрольных точек адаптивного дизайна). Старайтесь не использовать для изображения значение `Full Size` без особой необходимости. [Подробнее…](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Включить сжатие текста можно в настройках веб-сервера."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Чтобы изменить формат изображений на WebP, перейдите на вкладку Image Optimization (Оптимизация изображений) в WP Rocket и включите плагин Imagify."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Чтобы выполнить эту рекомендацию, включите функцию [LazyLoad (Отложенная загрузка)](https://docs.wp-rocket.me/article/1141-lazyload-for-images) в WP Rocket. Изображения начнут загружаться только в тот момент, когда посетитель прокрутит до них страницу."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Чтобы применить эту рекомендацию, включите в WP Rocket следующие функции: [Remove Unused CSS (Удалить неиспользуемый код CSS)](https://docs.wp-rocket.me/article/1529-remove-unused-css) и [Load JavaScript deferred (Загрузить отложенный скрипт JavaScript)](https://docs.wp-rocket.me/article/1265-load-javascript-deferred). Эти функции оптимизируют файлы CSS и JavaScript таким образом, что они не будут блокировать отрисовку страницы."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Чтобы устранить эту проблему, включите функцию [Minify CSS files (Уменьшить файлы CSS)](https://docs.wp-rocket.me/article/1350-css-minify-combine) в WP Rocket. Пробелы и комментарии в файлах CSS сайта будут удалены. Это позволяет увеличить скорость скачивания файлов, так как их размеры становятся меньше."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Чтобы устранить эту проблему, включите функцию [Minify JavaScript files (Уменьшить файлы JavaScript)](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) в WP Rocket. Пробелы и комментарии в файлах JavaScript будут удалены. Это позволяет увеличить скорость скачивания файлов, так как их размеры становятся меньше."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Чтобы устранить эту проблему, включите функцию [Remove Unused CSS (Удалить неиспользуемый код CSS)](https://docs.wp-rocket.me/article/1529-remove-unused-css) в WP Rocket. Она удаляет все неиспользуемые таблицы стилей и фрагменты кода CSS, оставляя для каждой страницы только задействованный код CSS. Благодаря этому размер страницы уменьшается."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Чтобы устранить эту проблему, включите функцию [Delay JavaScript (Отложить выполнение JavaScript)](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) в WP Rocket. Скрипты будут выполняться только после взаимодействия с пользователем, благодаря чему страница станет загружаться быстрее. Если в сайт встроены окна iframe, используйте следующие функции WP Rocket: [LazyLoad for iframes and videos (Отложенная загрузка для окон iframe и видео)](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) и [Replace YouTube iframe with preview image (Замена окна iframe YouTube изображением для предварительного просмотра)](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Чтобы сжать изображения, перейдите на вкладку Image Optimization (Оптимизация изображений) в WP Rocket, включите плагин Imagify и запустите процесс Bulk Optimization (Массовая оптимизация)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Чтобы добавить параметр dns-prefetch и ускорить взаимодействие с внешними доменами, используйте в WP Rocket функцию [Prefetch DNS Requests (Предварительно загружать DNS-запросы)](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests). Кроме того, WP Rocket автоматически добавляет параметр preconnect к [домену Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) и настройкам CNAME, заданным с помощью функции [Enable CDN (Включить CDN)](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Чтобы устранить проблему со шрифтами, включите функцию [Remove Unused CSS (Удалить неиспользуемый код CSS)](https://docs.wp-rocket.me/article/1529-remove-unused-css) в WP Rocket. Шрифты, важные для сайта, будут предзагружены в первую очередь."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Показать калькулятор"}, "report/renderer/report-utils.js | collapseView": {"message": "Свернуть"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Начальная навигация"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Максимальная задержка критического пути:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Копировать JSON-файл"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Включить/выключить тёмную тему"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Печать полного отчета"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Печать сводных данных"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Сохранить как Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Сохранить как HTML-файл"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Сохранить как JSON-файл"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Просмотр исходной трассировки"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Открыть в Viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "Ошибка"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Ошибка отчета: информация аудита отсутствует"}, "report/renderer/report-utils.js | expandView": {"message": "Развернуть"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "Собственные"}, "report/renderer/report-utils.js | footerIssue": {"message": "Сообщить о проблеме"}, "report/renderer/report-utils.js | hide": {"message": "Скрыть"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Имитация загрузки страницы"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Результаты анализа [Lighthouse](https://developers.google.com/web/tools/lighthouse/), проведенного для текущей страницы в эмулированной мобильной сети. Значения приблизительные и могут изменяться."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Дополнительные объекты для проверки вручную"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Неприменимо"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Открыть в новой вкладке"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Возможности"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Приблизительная экономия"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Успешные аудиты"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "В соответствии с [обновленными условиями установки Chrome](https://developer.chrome.com/blog/update-install-criteria) поддержка категории PWA в Lighthouse скоро будет прекращена. Информацию о том, как тестировать современные веб-приложения в дальнейшем, можно найти в [обновленной документации по PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/)."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Начальная загрузка страницы"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Сводка по состоянию на определенный момент"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Период, в течение которого пользователь взаимодействовал со страницей"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Настраиваемое ограничение"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Эмуляция компьютера"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Эмуляция устройства Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Без эмуляции"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Версия Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Производительность памяти/ЦП без пропуска тактов"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Ограничение пропускной способности ЦП"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Устройство"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Ограничение пропускной способности сети"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Эмуляция экрана"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Агент пользователя (сеть)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Сеанс с просмотром одной страницы"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Это данные загрузки одной страницы, а не сводка множества сеансов."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ограничение для сети 4G с низкой скоростью"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Неизвестно"}, "report/renderer/report-utils.js | show": {"message": "Показать"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Показать аудиты"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Свернуть фрагмент"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Развернуть фрагмент"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Показывать сторонние ресурсы"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Предоставлено средой"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Во время работы Lighthouse возникли следующие проблемы:"}, "report/renderer/report-utils.js | unattributable": {"message": "Несвязанные"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Значения приблизительные и могут изменяться. [Уровень производительности рассчитывается](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) непосредственно на основании этих показателей."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Посмотреть трассировку"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Открыть карту эффективности"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Пройденные проверки с предупреждениями"}, "report/renderer/report-utils.js | warningHeader": {"message": "Предупреждения: "}, "treemap/app/src/util.js | allLabel": {"message": "Все"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Все скрипты"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Доля использования"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Повторяющиеся модули"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Размер в байтах"}, "treemap/app/src/util.js | tableColumnName": {"message": "Название"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Показать/скрыть таблицу"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Неиспользуемые байты"}}