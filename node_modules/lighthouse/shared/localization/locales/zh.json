{"core/audits/accessibility/accesskeys.js | description": {"message": "快捷键可让用户快速转到页面的某个部分。为确保正确进行导航，每个快捷键都必须是独一无二的。[详细了解快捷键](https://dequeuniversity.com/rules/axe/4.8/accesskeys)。"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` 的值重复"}, "core/audits/accessibility/accesskeys.js | title": {"message": "“`[accesskey]`”值是独一无二的"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "每个 ARIA“`role`”都支持一部分特定的“`aria-*`”属性。如果这些项不匹配，会导致“`aria-*`”属性无效。[了解如何将 ARIA 属性与其角色进行匹配](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)。"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 属性与其角色不匹配"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 属性与其角色匹配"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "ARIA `role` 让辅助技术能够了解网页上每个元素的角色。如果 `role` 值拼写有误、不是现有的 ARIA `role` 值或是抽象角色，系统便不会将相应元素的用途传达给使用辅助技术的用户。[详细了解 ARIA 角色](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)。"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "向 `role=\"\"` 分配的值不是有效的 ARIA 角色。"}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "向 `role=\"\"` 分配的值是有效的 ARIA 角色。"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "如果某个元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何让命令元素更便于使用](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)。"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`、`link` 和 `menuitem` 元素缺少可供访问的名称。"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`、`link` 和 `menuitem` 元素都有可供访问的名称"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "如果 ARIA 对话框元素缺少无障碍名称，可能会妨碍屏幕阅读器用户理解这些元素的用途。[了解如何让 ARIA 对话框元素更符合无障碍需求](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)。"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "具有 `role=\"dialog\"` 或 `role=\"alertdialog\"` 的元素缺少无障碍名称。"}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "具有 `role=\"dialog\"` 或 `role=\"alertdialog\"` 的元素均有无障碍名称。"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "在文档 `<body>` 上设置 `aria-hidden=\"true\"` 后，辅助技术（如屏幕阅读器）的工作方式不一致。[了解 `aria-hidden` 对文档正文的影响](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)。"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "文档 `<body>` 中有 `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "文档 `<body>` 中没有 `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "有一个 `[aria-hidden=\"true\"]` 元素包含可聚焦的下级元素，导致这些交互式元素都无法被辅助技术（如屏幕阅读器）用户使用。[了解 `aria-hidden` 如何影响可聚焦的元素](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)。"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` 元素包含可聚焦的下级元素"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` 元素都不含可聚焦的下级元素"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "如果某个输入字段没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解输入字段标签](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)。"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA 输入字段缺少可供访问的名称"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA 输入字段都有可供访问的名称"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "如果某个 meter 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `meter` 元素命名](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)。"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA `meter` 元素缺少可供访问的名称。"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA `meter` 元素都有可供访问的名称"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "如果某个 `progressbar` 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `progressbar` 元素添加标签](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)。"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA `progressbar` 元素缺少可供访问的名称。"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA `progressbar` 元素都有可供访问的名称"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "一些 ARIA 角色有必需属性，这些属性向屏幕阅读器描述了相应元素的状态。[详细了解角色和必需属性](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)。"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` 缺少部分必需的 `[aria-*]` 属性"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` 具备所有必需的 `[aria-*]` 属性"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "一些 ARIA 父角色必须包含特定子角色，才能执行它们的预期无障碍功能。[详细了解角色和必需的子元素](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)。"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "具有 ARIA `[role]`且要求子元素必须包含特定`[role]`的元素缺少部分或全部必需子元素。"}, "core/audits/accessibility/aria-required-children.js | title": {"message": "具有 ARIA `[role]`且要求子元素必须包含特定`[role]`的元素包含全部必需子元素。"}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "一些 ARIA 子角色必须包含在特定的父角色中，才能正确执行它们的预期无障碍功能。[详细了解 ARIA 角色和必需的父元素](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)。"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` 未包含在其必需的父元素中"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` 包含在其必需的父元素中"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA 角色必须具备有效值，才能执行它们的预期无障碍功能。[详细了解有效的 ARIA 角色](https://dequeuniversity.com/rules/axe/4.8/aria-roles)。"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` 值无效"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` 值有效"}, "core/audits/accessibility/aria-text.js | description": {"message": "在一个被标记分隔的文本节点周围添加 `role=text` 后，VoiceOver 会将该文本元素视为一个短语，但不会读出它的可聚焦后代元素。[详细了解 `role=text` 属性](https://dequeuniversity.com/rules/axe/4.8/aria-text)。"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "具有 `role=text` 属性的元素包含可聚焦后代元素。"}, "core/audits/accessibility/aria-text.js | title": {"message": "具有 `role=text` 属性的元素缺少可聚焦后代元素。"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "如果某个切换字段没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解切换字段](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)。"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIA 切换字段缺少可供访问的名称"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIA 切换字段都有可供访问的名称"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "如果某个 tooltip 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何为 `tooltip` 元素命名](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)。"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA `tooltip` 元素缺少可供访问的名称。"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA `tooltip` 元素都有可供访问的名称"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "如果某个 `treeitem` 元素没有无障碍名称，屏幕阅读器会将它读为通用名称，这会导致依赖屏幕阅读器的用户无法使用它。[详细了解如何为 `treeitem` 元素添加标签](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)。"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA `treeitem` 元素缺少可供访问的名称。"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA `treeitem` 元素都有可供访问的名称"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "辅助技术（如屏幕阅读器）无法解读值无效的 ARIA 属性。[详细了解 ARIA 属性的有效值](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)。"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 属性缺少有效值"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 属性具备有效值"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "辅助技术（如屏幕阅读器）无法解读名称无效的 ARIA 属性。[详细了解有效的 ARIA 属性](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)。"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 属性无效或拼写有误"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 属性有效且拼写正确"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "未通过审核的元素"}, "core/audits/accessibility/button-name.js | description": {"message": "当某个按钮没有无障碍名称时，屏幕阅读器会将它读为“按钮”，这会导致依赖屏幕阅读器的用户无法使用它。[了解如何让按钮更便于使用](https://dequeuniversity.com/rules/axe/4.8/button-name)。"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "按钮缺少可供访问的名称"}, "core/audits/accessibility/button-name.js | title": {"message": "按钮有可供访问的名称"}, "core/audits/accessibility/bypass.js | description": {"message": "添加用于绕过重复内容的方式有助于键盘用户更高效地浏览页面。[详细了解如何绕过内容块](https://dequeuniversity.com/rules/axe/4.8/bypass)。"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "该页面不含任何标题、跳过链接或地标区域"}, "core/audits/accessibility/bypass.js | title": {"message": "该页面包含一个标题、跳过链接或地标区域"}, "core/audits/accessibility/color-contrast.js | description": {"message": "对于许多用户而言，对比度低的文字都是难以阅读或无法阅读的。[了解如何提供足够高的色彩对比度](https://dequeuniversity.com/rules/axe/4.8/color-contrast)。"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "背景色和前景色没有足够高的对比度。"}, "core/audits/accessibility/color-contrast.js | title": {"message": "背景色和前景色具有足够高的对比度"}, "core/audits/accessibility/definition-list.js | description": {"message": "如果未正确标记定义列表，屏幕阅读器读出的内容可能会令人困惑或不准确。[了解如何正确构建定义列表](https://dequeuniversity.com/rules/axe/4.8/definition-list)。"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` 并非只包含经过适当排序的 `<dt>` 和 `<dd>` 组及 `<script>`、`<template>` 或 `<div>` 元素。"}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` 只包含经过适当排序的 `<dt>` 和 `<dd>` 组及 `<script>`、`<template>` 或 `<div>` 元素。"}, "core/audits/accessibility/dlitem.js | description": {"message": "定义列表项（`<dt>` 和 `<dd>`）必须封装在父 `<dl>` 元素中，以确保屏幕阅读器可以正确地读出它们。[了解如何正确构建定义列表](https://dequeuniversity.com/rules/axe/4.8/dlitem)。"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "定义列表项已封装在 `<dl>` 元素中"}, "core/audits/accessibility/dlitem.js | title": {"message": "定义列表项已封装在 `<dl>` 元素中"}, "core/audits/accessibility/document-title.js | description": {"message": "屏幕阅读器用户可借助标题来大致了解某个页面的内容，搜索引擎用户则非常依赖标题来确定某个页面是否与其搜索相关。[详细了解文档标题](https://dequeuniversity.com/rules/axe/4.8/document-title)。"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "文档不含 `<title>` 元素"}, "core/audits/accessibility/document-title.js | title": {"message": "文档包含 `<title>` 元素"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "所有可聚焦的元素都必须具有独一无二的 `id`，以确保能被辅助技术识别。[了解如何修正重复的 `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)。"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "处于活动状态的可聚焦元素的 `[id]` 属性不是独一无二的"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "处于活动状态的可聚焦元素的 `[id]` 属性都是独一无二的"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA ID 的值必须独一无二，才能防止其他实例被辅助技术忽略。[了解如何修正重复的 ARIA ID](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)。"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ID 不是独一无二的"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ID 都是独一无二的"}, "core/audits/accessibility/empty-heading.js | description": {"message": "如果标题元素未包含任何内容或包含无法读取的文本，屏幕阅读器用户将难以获取网页的结构信息。[详细了解标题](https://dequeuniversity.com/rules/axe/4.8/empty-heading)。"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "标题元素未包含任何内容。"}, "core/audits/accessibility/empty-heading.js | title": {"message": "所有标题元素均包含内容。"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "有多个标签的表单字段可能会被辅助技术（如屏幕阅读器）以令人困惑的方式播报出来，因为这些辅助技术可能会使用第一个标签或最后一个标签，也可能会使用所有标签。[了解如何使用表单标签](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)。"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "表单字段有多个标签"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "表单字段都没有多个标签"}, "core/audits/accessibility/frame-title.js | description": {"message": "屏幕阅读器用户依靠框架标题来描述框架的内容。[详细了解框架标题](https://dequeuniversity.com/rules/axe/4.8/frame-title)。"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` 或 `<iframe>` 元素缺少标题"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` 或 `<iframe>` 元素有标题"}, "core/audits/accessibility/heading-order.js | description": {"message": "未跳过任何等级且排序正确的标题会准确传达页面的语义结构，从而使得相应页面在使用辅助技术时更易于浏览和理解。[详细了解标题顺序](https://dequeuniversity.com/rules/axe/4.8/heading-order)。"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "标题元素未按降序显示"}, "core/audits/accessibility/heading-order.js | title": {"message": "标题元素按降序显示"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "如果页面未指定 `lang` 属性，屏幕阅读器便会假定该页面采用的是用户在设置屏幕阅读器时选择的默认语言。如果该页面实际上并未采用默认语言，屏幕阅读器可能无法正确读出该页面中的文字。[详细了解 `lang` 属性](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)。"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 元素缺少 `[lang]` 属性"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 元素包含 `[lang]` 属性"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "指定有效的 [BCP 47 语言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)有助于屏幕阅读器正确读出文字。[了解如何使用 `lang` 属性](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)。"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 元素的 `[lang]` 属性缺少有效值。"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 元素的 `[lang]` 属性具备有效值"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "如果网页未指定一致的语言，屏幕阅读器可能无法正确读出相应网页上的文本。[详细了解 `lang` 属性](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)。"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "`<html>` 元素的 `[xml:lang]` 属性与 `[lang]` 属性使用了不同的基本语言。"}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "`<html>` 元素的 `[xml:lang]` 属性与 `[lang]` 属性使用了相同的基本语言。"}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "指向相同目标位置的链接应具有相同的说明，这有助于用户了解链接的用途并决定是否要访问它。[详细了解相同链接](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)。"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "相同链接的用途不一致。"}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "相同链接的用途一致。"}, "core/audits/accessibility/image-alt.js | description": {"message": "说明性元素应力求使用简短的描述性替代文字。未指定 alt 属性的装饰性元素可被忽略。[详细了解 `alt` 属性](https://dequeuniversity.com/rules/axe/4.8/image-alt)。"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "图片元素缺少 `[alt]` 属性"}, "core/audits/accessibility/image-alt.js | title": {"message": "图片元素具备 `[alt]` 属性"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "说明性元素应力求使用简短的描述性替代文本。如果替代文本与链接或图片旁边的文本完全相同，可能会让屏幕阅读器用户感到困惑，因为他们会听到两次同样的内容。[详细了解 `alt` 属性](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)。"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "图片元素具有属于多余文本的 `[alt]` 属性。"}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "图片元素没有属于多余文本的 `[alt]` 属性。"}, "core/audits/accessibility/input-button-name.js | description": {"message": "向输入按钮添加可识别的无障碍文本可帮助屏幕阅读器用户了解输入按钮的用途。[详细了解输入按钮](https://dequeuniversity.com/rules/axe/4.8/input-button-name)。"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "输入按钮没有可识别的文本。"}, "core/audits/accessibility/input-button-name.js | title": {"message": "输入按钮具有可识别的文本。"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "将图片用作 `<input>` 按钮时，提供替代文字有助于屏幕阅读器用户了解该按钮的用途。[了解输入图片替代文字](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)。"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 元素无对应的 `[alt]` 文字"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 元素有对应的 `[alt]` 文字"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "如果可见文本标签与无障碍名称不匹配，可能会让屏幕阅读器用户感到困惑。[详细了解无障碍名称](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)。"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "带有可见文本标签的元素没有匹配的无障碍名称。"}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "带有可见文本标签的元素具有匹配的无障碍名称。"}, "core/audits/accessibility/label.js | description": {"message": "标签可确保辅助技术（如屏幕阅读器）正确读出表单控件。[详细了解表单元素标签](https://dequeuniversity.com/rules/axe/4.8/label)。"}, "core/audits/accessibility/label.js | failureTitle": {"message": "表单元素不具备关联的标签"}, "core/audits/accessibility/label.js | title": {"message": "表单元素具有关联的标签"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "主要位置标记有助于屏幕阅读器用户浏览网页。[详细了解位置标记](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)。"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "文档缺少主要位置标记。"}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "文档有一个主要位置标记。"}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "对许多用户而言，对比度低的文本都是难以阅读或无法阅读的。清晰可辨的链接文本可改善低视力用户的体验。[了解如何让链接容易辨识](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)。"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "必须依赖颜色才能辨识链接。"}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "无需依赖颜色即可辨识链接。"}, "core/audits/accessibility/link-name.js | description": {"message": "请确保链接文字（以及用作链接的图片替代文字）可识别、独一无二且可聚焦，这样做会提升屏幕阅读器用户的导航体验。[了解如何使链接可供访问](https://dequeuniversity.com/rules/axe/4.8/link-name)。"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "链接缺少可识别的名称"}, "core/audits/accessibility/link-name.js | title": {"message": "链接具备可识别的名称"}, "core/audits/accessibility/list.js | description": {"message": "屏幕阅读器会采用特定的方法来读出列表内容。确保列表结构正确有助于屏幕阅读器顺利读出相应内容。[详细了解适当的列表结构](https://dequeuniversity.com/rules/axe/4.8/list)。"}, "core/audits/accessibility/list.js | failureTitle": {"message": "列表并非只包含 `<li>` 元素和脚本支持元素（`<script>` 和 `<template>`）。"}, "core/audits/accessibility/list.js | title": {"message": "列表只包含 `<li>` 元素和脚本支持元素（`<script>` 和 `<template>`）。"}, "core/audits/accessibility/listitem.js | description": {"message": "屏幕阅读器要求列表项 (`<li>`) 必须包含在父 `<ul>`、`<ol>` 或 `<menu>` 中，这样才能正确读出它们。[详细了解适当的列表结构](https://dequeuniversity.com/rules/axe/4.8/listitem)。"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "列表项 (`<li>`) 未包含在 `<ul>`、`<ol>` 或 `<menu>` 父元素中。"}, "core/audits/accessibility/listitem.js | title": {"message": "列表项 (`<li>`) 包含在 `<ul>`、`<ol>` 或 `<menu>` 父元素中"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "用户并不希望网页自动刷新，因为自动刷新会不断地将焦点移回到页面顶部。这可能会让用户感到沮丧或困惑。[详细了解刷新元标记](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)。"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "文档使用了 `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "文档未使用 `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "对于必须依靠放大屏幕才能清晰看到网页内容的低视力用户而言，停用缩放功能会给他们带来问题。[详细了解视口元标记](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)。"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`<meta name=\"viewport\">` 元素中使用了 `[user-scalable=\"no\"]`，或者 `[maximum-scale]` 属性小于 5。"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` 未用在 `<meta name=\"viewport\">` 元素中，并且 `[maximum-scale]` 属性不小于 5。"}, "core/audits/accessibility/object-alt.js | description": {"message": "屏幕阅读器无法转换非文字内容。通过向 `<object>` 元素添加替代文字，可帮助屏幕阅读器将含义传达给用户。[详细了解 `object` 元素的替代文字](https://dequeuniversity.com/rules/axe/4.8/object-alt)。"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 元素无对应的替代文字"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 元素有对应的替代文字"}, "core/audits/accessibility/select-name.js | description": {"message": "如果表单元素缺少有效标签，可能会给屏幕阅读器用户带来糟糕的体验。[详细了解 `select` 元素](https://dequeuniversity.com/rules/axe/4.8/select-name)。"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "select 元素缺少关联的标签元素。"}, "core/audits/accessibility/select-name.js | title": {"message": "select 元素具有关联的标签元素。"}, "core/audits/accessibility/skip-link.js | description": {"message": "添加跳转链接可帮助用户跳至主要内容以节省时间。[详细了解跳转链接](https://dequeuniversity.com/rules/axe/4.8/skip-link)。"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "跳转链接无法聚焦。"}, "core/audits/accessibility/skip-link.js | title": {"message": "跳转链接可聚焦。"}, "core/audits/accessibility/tabindex.js | description": {"message": "值大于 0 意味着明确的导航顺序。尽管这在技术上可行，但往往会让依赖辅助技术的用户感到沮丧。[详细了解 `tabindex` 属性](https://dequeuniversity.com/rules/axe/4.8/tabindex)。"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "一些元素的 `[tabindex]` 值大于 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "所有元素的 `[tabindex]` 值都不大于 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "summary 属性应描述表格结构，而 `<caption>` 应含有屏幕上显示的标题。准确的表格标记对屏幕阅读器用户有帮助。[详细了解 summary 和 caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)。"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "表格的 summary 属性和 `<caption>.` 具有相同的内容"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "相关表格的 summary 属性和 `<caption>` 具有不同的内容。"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请务必确保表格实际使用了 <caption> 元素（而非带有 `[colspan]` 属性的单元格），这可以提升屏幕阅读器用户的体验。[详细了解表格标题](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)。"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "表格未使用 `<caption>`（而是使用了带有 `[colspan]` 属性的单元格）来表示表格标题。"}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "表格使用了 `<caption>`（而非带有 `[colspan]` 属性的单元格）来表示表格标题。"}, "core/audits/accessibility/target-size.js | description": {"message": "如果触摸目标的尺寸和间距足够大，就能帮助难以精准触按小控件的用户激活相应目标。[详细了解触摸目标](https://dequeuniversity.com/rules/axe/4.8/target-size)。"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "触摸目标的尺寸或间距不足。"}, "core/audits/accessibility/target-size.js | title": {"message": "触摸目标的尺寸和间距足够大。"}, "core/audits/accessibility/td-has-header.js | description": {"message": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请务必确保大型表格（宽度和高度至少为 3 个单元格）中的 `<td>` 元素具有关联的表格标头，这可以提升屏幕阅读器用户的体验。[详细了解表格标头](https://dequeuniversity.com/rules/axe/4.8/td-has-header)。"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "大型 `<table>` 中的 `<td>` 元素没有表格标头。"}, "core/audits/accessibility/td-has-header.js | title": {"message": "大型 `<table>` 中的 `<td>` 元素具有一个或多个表格标头。"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "屏幕阅读器提供了更便于用户浏览表格内容的功能。请确保那些使用 `[headers]` 属性的 `<td>` 单元格仅引用同一个表格中的其他单元格，这样做可提升屏幕阅读器用户的体验。[详细了解 `headers` 属性](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)。"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` 元素中使用 `[headers]` 属性的单元格引用了在同一表格中找不到的元素 `id`。"}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` 元素中使用 `[headers]` 属性的单元格引用的是同一表格中的单元格。"}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "屏幕阅读器提供了更便于用户浏览表格内容的功能。确保表格标头始终引用特定一组单元格可以提升屏幕阅读器用户的体验。[详细了解表格标头](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)。"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 元素和 `[role=\"columnheader\"/\"rowheader\"]` 的元素缺少它们所描述的数据单元格。"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 元素和 `[role=\"columnheader\"/\"rowheader\"]` 的元素具备它们所描述的数据单元格。"}, "core/audits/accessibility/valid-lang.js | description": {"message": "为元素指定有效的 [BCP 47 语言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)有助于确保屏幕阅读器正确读出文字。[了解如何使用 `lang` 属性](https://dequeuniversity.com/rules/axe/4.8/valid-lang)。"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 属性缺少有效值"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 属性的值有效"}, "core/audits/accessibility/video-caption.js | description": {"message": "如果视频提供了字幕，失聪用户和听障用户便能更轻松地获取此视频中的信息。[详细了解视频字幕](https://dequeuniversity.com/rules/axe/4.8/video-caption)。"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 元素缺少带 `[kind=\"captions\"]` 的 `<track>` 元素。"}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 元素包含带 `[kind=\"captions\"]` 的 `<track>` 元素"}, "core/audits/autocomplete.js | columnCurrent": {"message": "当前值"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "建议的令牌"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` 可帮助用户更快地提交表单。如果想减少用户操作，建议启用自动补全功能，只需将 `autocomplete` 属性设为一个有效值即可。[详细了解在表单中使用 `autocomplete`](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` 元素没有正确的 `autocomplete` 属性"}, "core/audits/autocomplete.js | manualReview": {"message": "需要人工审核"}, "core/audits/autocomplete.js | reviewOrder": {"message": "请检查令牌的顺序"}, "core/audits/autocomplete.js | title": {"message": "`<input>` 元素正确地使用了 `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` 令牌：{snippet} 中的“{token}”无效"}, "core/audits/autocomplete.js | warningOrder": {"message": "检查令牌的顺序：{snippet} 中的“{tokens}”"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "有待解决"}, "core/audits/bf-cache.js | description": {"message": "许多导航操作都是网页间来回切换，往返缓存 (bfcache) 可以加快这些返回导航的速度。[详细了解 bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 个失败原因}other{# 个失败原因}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "失败原因"}, "core/audits/bf-cache.js | failureTitle": {"message": "网页已阻止恢复往返缓存"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "失败类型"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "无法解决"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "尚不支持浏览器"}, "core/audits/bf-cache.js | title": {"message": "网页并未阻止恢复往返缓存"}, "core/audits/bf-cache.js | warningHeadless": {"message": "在旧版无头 Chrome (`--chrome-flags=\"--headless=old\"`) 中无法测试往返缓存。要查看审核结果，请使用新版无头 Chrome (`--chrome-flags=\"--headless=new\"`) 或标准 Chrome。"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 扩展程序对此网页的加载性能产生了负面影响。请尝试在无痕模式下或使用未安装这些扩展程序的 Chrome 个人资料审核此网页。"}, "core/audits/bootup-time.js | columnScriptEval": {"message": "脚本评估"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "脚本解析"}, "core/audits/bootup-time.js | columnTotal": {"message": "总 CPU 时间"}, "core/audits/bootup-time.js | description": {"message": "建议您减少为解析、编译和执行 JS 而花费的时间。您可能会发现，提供较小的 JS 载荷有助于实现此目标。[了解如何缩短 JavaScript 执行时间](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)。"}, "core/audits/bootup-time.js | failureTitle": {"message": "缩短 JavaScript 执行用时"}, "core/audits/bootup-time.js | title": {"message": "JavaScript 执行用时"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "从软件包中移除重复的大型 JavaScript 模块可减少网络传输时不必要的流量消耗。 "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "请移除 JavaScript 软件包中的重复模块"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "使用大型 GIF 提供动画内容会导致效率低下。建议您改用 MPEG4/WebM 视频（来提供动画）和 PNG/WebP（来提供静态图片）以减少网络活动消耗的字节数。[详细了解高效视频格式](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "使用视频格式提供动画内容"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill 和 transform 让旧版浏览器能够使用新的 JavaScript 功能。不过，其中的很多函数对现代浏览器而言并不是必需的。对于捆绑的 JavaScript，请采用现代脚本部署策略，以便利用 module/nomodule 功能检测机制来减少传送到现代浏览器的代码量，同时保留对旧版浏览器的支持。[了解如何使用现代 JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "应避免向新型浏览器提供旧版 JavaScript"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP 和 AVIF 等图片格式的压缩效果通常优于 PNG 或 JPEG，因而下载速度更快，消耗的数据流量更少。[详细了解现代图片格式](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)。"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "采用新一代格式提供图片"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "建议您在所有关键资源加载完毕后再加载屏幕外图片和处于隐藏状态的图片，从而缩短 Time to Interactive。[了解如何推迟加载屏幕外图片](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)。"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "推迟加载屏幕外图片"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "资源阻止了系统对您网页的首次绘制。建议以内嵌方式提供关键的 JS/CSS，并推迟提供所有非关键的 JS/样式。[了解如何移除阻塞渲染的资源](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)。"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "移除阻塞渲染的资源"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "网络载荷过大不仅会让用户付出真金白银，还极有可能会延长加载用时。[了解如何缩减载荷大小](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)。"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "总大小为 {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "避免网络负载过大"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "避免网络负载过大"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "缩减 CSS 文件大小可缩减网络载荷规模。[了解如何缩减 CSS 的大小](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)。"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "缩减 CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "缩减 JavaScript 文件大小不仅可以缩减载荷规模，还能缩短脚本解析用时。[了解如何缩减 JavaScript 的大小](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)。"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "缩减 JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "请从样式表中减少未使用的规则，并延迟加载首屏内容未用到的 CSS，以减少网络活动耗用的字节数。[了解如何减少未使用的 CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)。"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "减少未使用的 CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "请减少未使用的 JavaScript，并等到需要使用时再加载脚本，以减少网络活动耗用的字节数。[了解如何减少未使用的 JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)。"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "减少未使用的 JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "延长缓存期限可加快重访您网页的速度。[详细了解高效缓存政策](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)。"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{找到了 1 项资源}other{找到了 # 项资源}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "采用高效的缓存策略提供静态资源"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "针对静态资源使用高效的缓存策略"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "经过优化的图片有助于提升加载速度，并减少消耗的移动数据网络流量。[了解如何高效地对图片进行编码](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)。"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "对图片进行高效编码"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "实际尺寸"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "显示时的尺寸"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "图片大于其显示时的尺寸"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "图片适合其显示时的尺寸"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "提供大小合适的图片可节省移动数据网络流量并缩短加载用时。[了解如何调整图片大小](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)。"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "适当调整图片大小"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "对于文本资源，应先压缩（gzip、deflate 或 brotli），然后再提供，以最大限度地减少网络活动消耗的字节总数。[详细了解文本压缩](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)。"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "启用文本压缩"}, "core/audits/content-width.js | description": {"message": "如果应用内容的宽度与视口的宽度不一致，该应用可能不会针对移动设备屏幕进行优化。[了解如何根据视口调整内容大小](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)。"}, "core/audits/content-width.js | explanation": {"message": "视口大小（{innerWidth} 像素）与窗口大小（{outerWidth} 像素）不一致。"}, "core/audits/content-width.js | failureTitle": {"message": "未根据视口正确设置内容尺寸"}, "core/audits/content-width.js | title": {"message": "已根据视口正确设置内容尺寸"}, "core/audits/critical-request-chains.js | description": {"message": "下面的关键请求链显示了以高优先级加载的资源。建议缩短链长、缩减资源的下载文件大小，或者推迟下载不必要的资源，从而提高网页加载速度。[了解如何避免链接关键请求](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)。"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{找到了 1 条请求链}other{找到了 # 条请求链}}"}, "core/audits/critical-request-chains.js | title": {"message": "避免链接关键请求"}, "core/audits/csp-xss.js | columnDirective": {"message": "指令"}, "core/audits/csp-xss.js | columnSeverity": {"message": "严重程度"}, "core/audits/csp-xss.js | description": {"message": "强有力的内容安全政策 (CSP) 可显著降低遭遇跨站脚本攻击 (XSS) 的风险。[了解如何使用 CSP 来防止 XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "语法"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "该网页包含一个在 `<meta>` 标记中定义的 CSP。因此，我们建议您将 CSP 移至 HTTP 标头，或在 HTTP 标头中定义其他严格 CSP。"}, "core/audits/csp-xss.js | noCsp": {"message": "找不到任何处于强制模式的 CSP"}, "core/audits/csp-xss.js | title": {"message": "请确保 CSP 能够有效地抵御 XSS 攻击"}, "core/audits/deprecations.js | columnDeprecate": {"message": "弃用/警告"}, "core/audits/deprecations.js | columnLine": {"message": "Line"}, "core/audits/deprecations.js | description": {"message": "已弃用的 API 最终将从浏览器中移除。[详细了解已弃用的 API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)。"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{发现了 1 条警告}other{发现了 # 条警告}}"}, "core/audits/deprecations.js | failureTitle": {"message": "使用了已弃用的 API"}, "core/audits/deprecations.js | title": {"message": "请勿使用已弃用的 API"}, "core/audits/dobetterweb/charset.js | description": {"message": "必须声明字符编码。您可以在 HTML 的前 1024 个字节中使用 `<meta>` 标记来声明，也可以在 HTTP 响应标头 Content-Type 中进行声明。[详细了解如何声明字符编码](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)。"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "字符集声明缺失，或者在 HTML 中出现得太晚"}, "core/audits/dobetterweb/charset.js | title": {"message": "正确地设定了字符集"}, "core/audits/dobetterweb/doctype.js | description": {"message": "指定 DOCTYPE 可阻止浏览器切换到 Quirks 模式。[详细了解 DOCTYPE 声明](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)。"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE 名称必须为字符串 `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "该文档包含会触发 `limited-quirks-mode` 的 `doctype`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "文档必需包含 DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId 应为空字符串"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId 应为空字符串"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "该文档包含会触发 `quirks-mode` 的 `doctype`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "页面缺少 HTML DOCTYPE，从而触发了 Quirks 模式"}, "core/audits/dobetterweb/doctype.js | title": {"message": "页面包含 HTML DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "统计信息"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "值"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "大型 DOM 会增加内存用量、导致[样式计算](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)用时延长，并产生高昂的[布局自动重排](https://developers.google.com/speed/articles/reflow)成本。[了解如何避免 DOM 规模过大](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)。"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 个元素}other{# 个元素}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "避免 DOM 规模过大"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "最大 DOM 深度"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM 元素总数"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "子元素数量最大值"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "避免 DOM 规模过大"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "如果网站在缺少上下文的情况下请求位置信息，会导致用户不信任网站或感到困惑。建议将请求与用户操作进行绑定。[详细了解地理定位权限](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)。"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "在网页加载时请求地理定位权限"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "避免在网页加载时请求地理定位权限"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "问题类型"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome Devtools 的“`Issues`”面板中记录的问题表明有未解决的问题。这些问题的起因可能是网络请求失败、安全控件不足和其他浏览器问题。如需详细了解每个问题，请打开 Chrome Devtools 的“Issues”面板。"}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "问题记录在 Chrome Devtools 的“`Issues`”面板中"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "已被跨源政策屏蔽"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "广告占用了大量资源"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Chrome Devtools 的“`Issues`”面板中无任何问题"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "版本"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "页面上检测到的所有前端 JavaScript 库。[详细了解此 JavaScript 库检测诊断审核](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)。"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "已检测到的 JavaScript 库"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "对于连接速度较慢的用户，通过 `document.write()` 动态注入的外部脚本可将网页加载延迟数十秒。[了解如何避免使用 document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)。"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "避免使用 `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "请勿使用 `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "如果网站在缺少上下文的情况下请求发送通知，会导致用户不信任网站或感到困惑。建议将请求与用户手势进行绑定。[详细了解如何以负责任的方式获取通知权限](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)。"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "在网页加载时请求通知权限"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "避免在网页加载时请求通知权限"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "禁用粘贴式输入功能会对用户体验产生不良影响，停用密码管理工具则会降低安全性。[详细了解方便用户使用的输入字段](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)。"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "阻止用户将内容粘贴到输入字段中"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "允许用户将内容粘贴到输入字段中"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "协议"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 提供了许多优于 HTTP/1.1 的益处，包括二进制标头和多路复用。[详细了解 HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)。"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 条请求未通过 HTTP/2 传送}other{# 条请求未通过 HTTP/2 传送}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "使用 HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "建议您将轻触和滚轮事件监听器标记为 `passive`，以提高页面的滚动性能。[详细了解如何采用被动事件监听器](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)。"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "未使用被动式监听器来提高滚动性能"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "使用被动式监听器来提高滚动性能"}, "core/audits/errors-in-console.js | description": {"message": "控制台中记录的错误表明有未解决的问题。这些问题的起因可能是网络请求失败和其他浏览器问题。[在控制台诊断审核中详细了解此错误](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "控制台日志中已记录浏览器错误"}, "core/audits/errors-in-console.js | title": {"message": "控制台日志中未记录浏览器错误"}, "core/audits/font-display.js | description": {"message": "利用 `font-display` CSS 功能来确保用户在网页字体加载期间能看到文字。[详细了解 `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)。"}, "core/audits/font-display.js | failureTitle": {"message": "确保文本在网页字体加载期间保持可见状态"}, "core/audits/font-display.js | title": {"message": "在网页字体加载期间，所有文本都保持可见状态"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse 无法自动检查来源 {fontOrigin} 的 `font-display` 值。}other{Lighthouse 无法自动检查来源 {fontOrigin} 的 `font-display` 值。}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "宽高比（实际）"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "宽高比（显示）"}, "core/audits/image-aspect-ratio.js | description": {"message": "图像显示尺寸应与自然宽高比相匹配。[详细了解图片宽高比](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)。"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "显示的图像宽高比不正确"}, "core/audits/image-aspect-ratio.js | title": {"message": "显示的图像宽高比正确"}, "core/audits/image-size-responsive.js | columnActual": {"message": "实际大小"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "显示时大小"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "预期大小"}, "core/audits/image-size-responsive.js | description": {"message": "图片的自然尺寸应与显示屏大小及像素比成正比，这样才能令图片达到最清晰的显示效果。[了解如何提供自适应图片](https://web.dev/articles/serve-responsive-images)。"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "所提供的部分图片采用了较低的分辨率"}, "core/audits/image-size-responsive.js | title": {"message": "所提供的图片都采用了合适的分辨率"}, "core/audits/installable-manifest.js | already-installed": {"message": "该应用已安装"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "无法从清单下载必需的图标"}, "core/audits/installable-manifest.js | columnValue": {"message": "失败原因"}, "core/audits/installable-manifest.js | description": {"message": "Service Worker 是一项技术，可让您的应用使用很多渐进式 Web 应用功能，例如离线、添加到主屏幕和推送通知。如果 Service Worker 和清单的实现均正确无误，浏览器可主动提示用户向其主屏幕中添加您的应用，这有助于提高互动度。[详细了解清单可安装性要求](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)。"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 个原因}other{# 个原因}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Web 应用清单或 Service Worker 不符合可安装性要求"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play 商店应用网址和 Play 商店 ID 不相符"}, "core/audits/installable-manifest.js | in-incognito": {"message": "该网页是在无痕式窗口中加载的"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "清单 `display` 属性必须是 `standalone`、`fullscreen` 或 `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "清单包含“display_override”字段，因此第一个受支持的显示模式必须是“standalone”、“fullscreen”或“minimal-ui”之一"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "清单无法提取、为空或无法解析"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "在提取清单期间，清单网址发生了更改。"}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "清单未包含 `name` 或 `short_name` 字段"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "清单未包含合适的图标 - 必须提供不小于 {value0} 像素的 PNG、SVG 或 WebP 格式图标；必须设置 sizes 属性；如果设置了 purpose 属性，其中必须包含“any”。"}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "清单未提供不小于 {value0} 正方形像素的 PNG、SVG 或 WebP 格式图标，而且 purpose 属性设为“any”或未设置"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "下载的图标为空或已损坏"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "未提供 Play 商店 ID"}, "core/audits/installable-manifest.js | no-manifest": {"message": "该网页没有清单 <link> 网址"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "如果清单不含“start_url”字段，则无法检查 Service Worker"}, "core/audits/installable-manifest.js | noErrorId": {"message": "系统无法识别可安装性错误 ID“{errorId}”"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "该网页不是从安全来源提供的"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "该网页不是在主框架中加载的"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "该网页无法离线使用"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA 已卸载完毕，正在重置可安装性检查。"}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "指定的应用平台在 Android 设备上不受支持"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "清单指定了 prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications 仅适用于 Android 设备上的 Chrome Beta 版和稳定版。"}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse 无法确定该页面是否可安装。请在较新版本的 Chrome 中重试。"}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "清单起始网址无效"}, "core/audits/installable-manifest.js | title": {"message": "Web 应用清单和 Service Worker 符合可安装性要求"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "清单中的某个网址包含用户名、密码或端口"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "该网页无法离线使用。Chrome 于 2021 年 8 月推出 93 稳定版之后，不会将该网页视为可安装。"}, "core/audits/is-on-https.js | allowed": {"message": "允许"}, "core/audits/is-on-https.js | blocked": {"message": "已屏蔽"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "不安全的网址"}, "core/audits/is-on-https.js | columnResolution": {"message": "对请求的处理方式"}, "core/audits/is-on-https.js | description": {"message": "所有网站都应该通过 HTTPS 来保护，即使网站不处理敏感数据，也应如此。这包括要避免使用[混合内容](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)（即：通过 HTTPS 实现初始请求，但却通过 HTTP 加载某些资源）。HTTPS 可防止入侵程序篡改或被动地监听您的应用与用户之间的通信，它是 HTTP/2 和许多新的网络平台 API 的先决条件。[详细了解 HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)。"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{发现 1 条不安全的请求}other{发现 # 条不安全的请求}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "未使用 HTTPS"}, "core/audits/is-on-https.js | title": {"message": "使用 HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "自动升级至 HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "允许（附带警告）"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "LCP 占比 (%)"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "阶段"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "用时"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "这是此视口内绘制的最大内容元素。[详细了解 Largest Contentful Paint 元素](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "加载延迟"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "加载时间"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "渲染延迟"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "最大内容渲染时间元素"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "布局偏移的影响"}, "core/audits/layout-shift-elements.js | description": {"message": "这些 DOM 元素受布局偏移的影响最大。由于[窗口化](https://web.dev/articles/cls#what_is_cls)的原因，某些布局偏移可能未包含在 CLS 指标值中。[了解如何改善 CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "请避免出现大幅度的布局偏移"}, "core/audits/layout-shifts.js | columnScore": {"message": "布局偏移分数"}, "core/audits/layout-shifts.js | description": {"message": "这些是在网页上观察到的最大幅的布局偏移。表格中的每个项均表示一次布局偏移，并显示了发生最大幅偏移的元素。每个项的下方列出了哪些可能的根本原因导致了相应的布局偏移。由于[窗口化](https://web.dev/articles/cls#what_is_cls)的原因，所显示的某些布局偏移可能未包含在 CLS 指标值中。[了解如何改善 CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{发现了 1 次布局偏移}other{发现了 # 次布局偏移}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "加载了网络字体"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "注入的 iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "延迟的网络请求调整了页面布局"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "媒体元素缺少明确的大小"}, "core/audits/layout-shifts.js | title": {"message": "请避免出现大幅度的布局偏移"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "被延迟加载的首屏图片会在页面生命周期内的较晚时间渲染，这可能会致使系统延迟 Largest Contentful Paint。[详细了解最佳延迟加载](https://web.dev/articles/lcp-lazy-loading)。"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Largest Contentful Paint 所对应的图片被延迟加载了"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Largest Contentful Paint 所对应的图片未被延迟加载"}, "core/audits/long-tasks.js | description": {"message": "列出了主线程中运行时间最长的任务，有助于识别出导致输入延迟的最主要原因。[了解如何避免出现长时间运行的主线程任务](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{发现了 # 项长时间运行的任务}other{发现了 # 项长时间运行的任务}}"}, "core/audits/long-tasks.js | title": {"message": "应避免出现长时间运行的主线程任务"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "类别"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "建议您减少为解析、编译和执行 JS 而花费的时间。您可能会发现，提供较小的 JS 载荷有助于实现此目标。[了解如何尽可能减少主线程工作](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "最大限度地减少主线程工作"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "最大限度地减少主线程工作"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "网站应该能在所有主流浏览器中正常显示，以便覆盖尽可能多的用户。[了解跨浏览器兼容性](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)。"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "网站能在各种浏览器中正常显示"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "确保各个网页可通过网址建立深层链接，并且这些网址是独一无二的，可以在社交媒体上共享。[详细了解如何提供深层链接](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)。"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "每个网页都有一个网址"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "无论点按哪个链接，都应能快速跳转到相应页面，即使网速缓慢也应如此。若想让用户感知到出色的网页加载速度，这种体验至关重要。[详细了解网页转换](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)。"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "在不同网页之间跳转时，用户感觉不到网页加载缓慢"}, "core/audits/maskable-icon.js | description": {"message": "利用可遮盖式图标，可确保应用安装到设备上后，图片可填满整个形状轮廓，而不会出现黑边。[了解可遮盖式清单图标](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)。"}, "core/audits/maskable-icon.js | failureTitle": {"message": "清单不含可遮罩的图标"}, "core/audits/maskable-icon.js | title": {"message": "清单含有可遮罩的图标"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Cumulative Layout Shift 旨在衡量可见元素在视口内的移动情况。[详细了解 Cumulative Layout Shift 指标](https://web.dev/articles/cls)。"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint 标记了绘制出首个文本或首张图片的时间。[详细了解 First Contentful Paint 指标](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)。"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "“首次有效绘制时间”测量的是网页主要内容开始对用户显示的时间。[详细了解“首次有效绘制时间”指标](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)。"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Interaction to Next Paint 用于衡量网页响应速度，即网页需要多久才会明显响应用户输入。[详细了解 Interaction to Next Paint 指标](https://web.dev/articles/inp)。"}, "core/audits/metrics/interactive.js | description": {"message": "“Time to Interactive”是指网页需要多长时间才能提供完整的交互功能。[详细了解“Time to Interactive”指标](https://developer.chrome.com/docs/lighthouse/performance/interactive/)。"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint 标记了绘制出最大文本或图片的时间。[详细了解 Largest Contentful Paint 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "您的用户可能会遇到的最长 First Input Delay 是用时最长的任务的耗时。[详细了解 Maximum Potential First Input Delay 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)。"}, "core/audits/metrics/speed-index.js | description": {"message": "Speed Index 表明了网页内容的可见填充速度。[详细了解 Speed Index 指标](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)。"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "当任务用时超过 50 毫秒时计算 FCP 和 Time to Interactive 之间的所有时间段的总和，以毫秒表示。[详细了解 Total Blocking Time 指标](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)。"}, "core/audits/network-rtt.js | description": {"message": "网络往返时间 (RTT) 对性能有很大的影响。如果与来源之间的 RTT 较长，则表明缩短服务器与用户之间的距离可能会提高性能。[详细了解往返时间](https://hpbn.co/primer-on-latency-and-bandwidth/)。"}, "core/audits/network-rtt.js | title": {"message": "网络往返时间"}, "core/audits/network-server-latency.js | description": {"message": "服务器延迟时间可能会对网站性能产生不良影响。如果源服务器的延迟时间较长，则表明服务器过载或后端性能较差。[详细了解服务器响应时间](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)。"}, "core/audits/network-server-latency.js | title": {"message": "服务器后端延迟"}, "core/audits/no-unload-listeners.js | description": {"message": "“`unload`”事件不会可靠地触发，而且监听该事件可能会妨碍系统实施“往返缓存”之类的浏览器优化策略。请改用“`pagehide`”或“`visibilitychange`”事件。[详细了解如何卸载事件监听器](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "注册“`unload`”事件监听器"}, "core/audits/no-unload-listeners.js | title": {"message": "避免使用“`unload`”事件监听器"}, "core/audits/non-composited-animations.js | description": {"message": "未合成的动画可能会卡顿并增加 CLS。[了解如何避免使用未合成的动画](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{发现了 # 个动画元素}other{发现了 # 个动画元素}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "某个过滤相关属性可能会移动像素"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "目标中另有一个不兼容的动画"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "效果中含有除了“replace”以外的合成模式"}, "core/audits/non-composited-animations.js | title": {"message": "避免使用未合成的动画"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "某个转换相关属性取决于元素边界框的大小"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{不受支持的 CSS 属性：{properties}}other{不受支持的 CSS 属性：{properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "效果含有不受支持的时间参数"}, "core/audits/performance-budget.js | description": {"message": "请将网络请求的数量和数据大小保持在提供的性能预算所设定的目标之内。[详细了解性能预算](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 条请求}other{# 条请求}}"}, "core/audits/performance-budget.js | title": {"message": "性能预算"}, "core/audits/preload-fonts.js | description": {"message": "请预加载 `optional` 字体以方便初访者使用。[详细了解如何预加载字体](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "未预加载使用 `font-display: optional` 的字体"}, "core/audits/preload-fonts.js | title": {"message": "已预加载使用 `font-display: optional` 的字体"}, "core/audits/prioritize-lcp-image.js | description": {"message": "如果向网页中动态添加 LCP 元素，您应预加载图片以缩短 LCP 用时。[详细了解如何预加载 LCP 元素](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)。"}, "core/audits/prioritize-lcp-image.js | title": {"message": "预加载 LCP 元素所用图片"}, "core/audits/redirects.js | description": {"message": "重定向会在网页可加载前引入更多延迟。[了解如何避免网页重定向](https://developer.chrome.com/docs/lighthouse/performance/redirects/)。"}, "core/audits/redirects.js | title": {"message": "避免多次网页重定向"}, "core/audits/seo/canonical.js | description": {"message": "规范链接用于建议应在搜索结果中显示哪个网址。[详细了解规范链接](https://developer.chrome.com/docs/lighthouse/seo/canonical/)。"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "存在多个相互冲突的网址（{urlList}）"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "网址无效 ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "指向了其他 `hreflang` 位置 ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "不是一个绝对网址 ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "指向了网域的根网址（首页），而非等效内容页面"}, "core/audits/seo/canonical.js | failureTitle": {"message": "文档缺少有效的 `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "文档的 `rel=canonical` 有效"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "无法抓取的链接"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "搜索引擎可能会使用链接中的 `href` 属性来抓取网站。请确保锚元素的 `href` 属性链接到合适的目标网站，以便搜索引擎发现该网站上的更多网页。[了解如何使链接可供抓取](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "有无法抓取的链接"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "链接都是可抓取的"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "其他无法辨认的文字"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "字号"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "网页文本百分比"}, "core/audits/seo/font-size.js | columnSelector": {"message": "选择器"}, "core/audits/seo/font-size.js | description": {"message": "12px 以下的字体过小，会导致用户无法辨认；此外，这样的字体需要移动设备访问者“张合双指进行缩放”才能阅读。请尽量让 60% 以上的页面文字不小于 12px。[详细了解清晰可辨的字体大小](https://developer.chrome.com/docs/lighthouse/seo/font-size/)。"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} 清晰可辨的文字"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "无法辨认文字，因为缺少已针对移动设备屏幕进行优化的 viewport meta 标记。"}, "core/audits/seo/font-size.js | failureTitle": {"message": "文档未使用清晰可辨的字体大小"}, "core/audits/seo/font-size.js | legibleText": {"message": "清晰可辨的文字"}, "core/audits/seo/font-size.js | title": {"message": "文档所用的字体大小清晰可辨"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang 链接用于告知搜索引擎应在特定语言或地区的搜索结果中显示哪种版本的网页。[详细了解 `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)。"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "文档的 `hreflang` 无效"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "相对 href 值"}, "core/audits/seo/hreflang.js | title": {"message": "文档的 `hreflang` 有效"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "未预料到的语言代码"}, "core/audits/seo/http-status-code.js | description": {"message": "返回无效 HTTP 状态代码的页面可能无法被正确编入索引。[详细了解 HTTP 状态代码](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)。"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "页面返回了无效的 HTTP 状态代码"}, "core/audits/seo/http-status-code.js | title": {"message": "页面返回了有效的 HTTP 状态代码"}, "core/audits/seo/is-crawlable.js | description": {"message": "如果搜索引擎无权抓取您的网页，则无法将它们添加到搜索结果中。[详细了解抓取工具指令](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)。"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "页面已被屏蔽，无法编入索引"}, "core/audits/seo/is-crawlable.js | title": {"message": "页面未被屏蔽，可编入索引"}, "core/audits/seo/link-text.js | description": {"message": "描述性链接文字有助于搜索引擎理解您的内容。[了解如何让链接更便于使用](https://developer.chrome.com/docs/lighthouse/seo/link-text/)。"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{找到了 1 个链接}other{找到了 # 个链接}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "链接缺少描述性文字"}, "core/audits/seo/link-text.js | title": {"message": "链接有描述性文字"}, "core/audits/seo/manual/structured-data.js | description": {"message": "运行[结构化数据测试工具](https://search.google.com/structured-data/testing-tool/)和 [Structured Data Linter](http://linter.structured-data.org/) 可验证结构化数据。[详细了解结构化数据](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)。"}, "core/audits/seo/manual/structured-data.js | title": {"message": "结构化数据有效"}, "core/audits/seo/meta-description.js | description": {"message": "元描述可能会被包含在搜索结果中，以简要概括网页内容。[详细了解元描述](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)。"}, "core/audits/seo/meta-description.js | explanation": {"message": "描述性文字为空。"}, "core/audits/seo/meta-description.js | failureTitle": {"message": "文档缺少 meta 描述"}, "core/audits/seo/meta-description.js | title": {"message": "文档有 meta 描述"}, "core/audits/seo/plugins.js | description": {"message": "搜索引擎无法将插件内容编入索引，而且许多设备都限制或不支持使用插件。[详细了解如何避免使用插件](https://developer.chrome.com/docs/lighthouse/seo/plugins/)。"}, "core/audits/seo/plugins.js | failureTitle": {"message": "文档使用了插件"}, "core/audits/seo/plugins.js | title": {"message": "文档中没有插件"}, "core/audits/seo/robots-txt.js | description": {"message": "如果 robots.txt 文件的格式不正确，抓取工具可能无法理解您希望以何种方式抓取网站内容或将其编入索引。[详细了解 robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)。"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "对 robots.txt 的请求返回了 HTTP 状态代码：{statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{发现了 1 处错误}other{发现了 # 处错误}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse 无法下载 robots.txt 文件"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt 无效"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt 有效"}, "core/audits/seo/tap-targets.js | description": {"message": "交互式元素（例如按钮和链接）应足够大 (48x48px)，或者周围有足够的空间以便用户轻松点按，且不遮挡其他元素。[详细了解点按目标](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)。"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} 的点按目标大小合适"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "点按目标过小，因为缺少已针对移动设备屏幕进行优化的 viewport meta 标记"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "点按目标的大小不合适"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "点按目标重叠"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "点按目标"}, "core/audits/seo/tap-targets.js | title": {"message": "点按目标的大小合适"}, "core/audits/server-response-time.js | description": {"message": "请确保服务器响应主文档的用时较短，因为这会影响到所有其他请求的响应时长。[详细了解 Time to First Byte 指标](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)。"}, "core/audits/server-response-time.js | displayValue": {"message": "根文档花费了 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/server-response-time.js | failureTitle": {"message": "请缩短初始服务器响应用时"}, "core/audits/server-response-time.js | title": {"message": "初始服务器响应用时较短"}, "core/audits/splash-screen.js | description": {"message": "选定主题的启动画面可确保用户在从主屏幕中启动您的应用时获得优质体验。[详细了解启动画面](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)。"}, "core/audits/splash-screen.js | failureTitle": {"message": "未针对自定义启动画面进行配置"}, "core/audits/splash-screen.js | title": {"message": "已针对自定义启动画面进行配置"}, "core/audits/themed-omnibox.js | description": {"message": "可以为浏览器地址栏设置与您的网站相契合的主题。[详细了解如何为地址栏设置主题](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)。"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "没有为地址栏设置主题背景颜色。"}, "core/audits/themed-omnibox.js | title": {"message": "为地址栏设置主题背景颜色。"}, "core/audits/third-party-cookies.js | description": {"message": "在 Chrome 的某个未来版本中，对第三方 Cookie 的支持将被彻底取消。[详细了解逐步停用第三方 Cookie](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)。"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{找到 1 个 Cookie}other{找到 # 个 Cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "使用第三方 <PERSON>ie"}, "core/audits/third-party-cookies.js | title": {"message": "避免使用第三方 Cookie"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName}（客户成功案例）"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName}（营销）"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName}（社交）"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName}（视频）"}, "core/audits/third-party-facades.js | columnProduct": {"message": "产品"}, "core/audits/third-party-facades.js | description": {"message": "您可以延迟加载某些第三方嵌入代码。建议使用 Facade 替换这些代码，直到您需要使用它们为止。[了解如何使用 Facade 推迟加载第三方代码](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)。"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{有 # 个备用 Facade}other{有 # 个备用 Facade}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "可以使用 Facade 延迟加载某些第三方资源"}, "core/audits/third-party-facades.js | title": {"message": "使用 Facade 延迟加载第三方资源"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "第三方"}, "core/audits/third-party-summary.js | description": {"message": "第三方代码可能会显著影响加载性能。请限制冗余第三方提供商的数量，并尝试在页面完成主要加载后再加载第三方代码。[了解如何将第三方的影响降至最低](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)。"}, "core/audits/third-party-summary.js | displayValue": {"message": "第三方代码将主线程阻止了 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/third-party-summary.js | failureTitle": {"message": "降低第三方代码的影响"}, "core/audits/third-party-summary.js | title": {"message": "尽量减少第三方使用"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "实测值"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "指标"}, "core/audits/timing-budget.js | description": {"message": "设置时间预算有助于您密切关注网站性能。性能出色的网站不仅加载迅速，还能快速响应用户输入事件。[详细了解性能预算](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "core/audits/timing-budget.js | title": {"message": "时间预算"}, "core/audits/unsized-images.js | description": {"message": "请为图片元素设置明确的宽度值和高度值，以减少布局偏移并改善 CLS。[了解如何设置图片尺寸](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "图片元素没有明确的`width`和`height`"}, "core/audits/unsized-images.js | title": {"message": "图片元素都有明确的`width`和`height`"}, "core/audits/user-timings.js | columnType": {"message": "类型"}, "core/audits/user-timings.js | description": {"message": "建议使用 User Timing API 对您的应用进行插桩，从而衡量应用在关键用户体验中的实际性能。[详细了解 User Timing 标记](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)。"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 项 User Timing 结果}other{# 项 User Timing 结果}}"}, "core/audits/user-timings.js | title": {"message": "User Timing 标记和测量结果"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "“{security<PERSON><PERSON>in}”已有 `<link rel=preconnect>`，但浏览器未使用该链接。请检查并确保您正确地使用了 `crossorigin` 属性。"}, "core/audits/uses-rel-preconnect.js | description": {"message": "建议添加 `preconnect` 或 `dns-prefetch` 资源提示，以尽早连接到重要的第三方源。[了解如何预先连接到必需的源](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)。"}, "core/audits/uses-rel-preconnect.js | title": {"message": "预先连接到必要的来源"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "发现了不止 2 个 `<link rel=preconnect>` 连接。此类连接应尽量少用，且应仅用于指向最重要的来源。"}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "“{security<PERSON><PERSON>in}”已有 `<link rel=preconnect>`，但浏览器未使用该链接。请仅针对网页肯定会请求的重要来源使用 `preconnect`。"}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "“{preloadURL}”已有 preload `<link>`，但浏览器未使用该链接。请检查并确保您正确地使用了 `crossorigin` 属性。"}, "core/audits/uses-rel-preload.js | description": {"message": "建议使用 `<link rel=preload>` 来优先提取目前在网页加载过程中较晚请求加载的资源。[了解如何预加载关键请求](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)。"}, "core/audits/uses-rel-preload.js | title": {"message": "预加载关键请求"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "映射网址"}, "core/audits/valid-source-maps.js | description": {"message": "源代码映射会将缩减了大小的代码转换成原始源代码。这有助于开发者在正式版中调试。此外，Lighthouse 还能提供进一步的数据分析。建议部署源代码映射以充分利用这些好处。[详细了解源代码映射](https://developer.chrome.com/docs/devtools/javascript/source-maps/)。"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "大型第一方 JavaScript 缺少源代码映射"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "大型 JavaScript 文件缺少源代码映射"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{警告：`.sourcesContent` 中缺少 1 项内容}other{警告：`.sourcesContent` 中缺少 # 项内容}}"}, "core/audits/valid-source-maps.js | title": {"message": "页面包含有效的源代码映射"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` 不仅会针对移动设备屏幕尺寸优化您的应用，还会阻止[系统在响应用户输入前出现 300 毫秒的延迟](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)。[详细了解如何使用视口元标记](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)。"}, "core/audits/viewport.js | explanationNoTag": {"message": "未找到任何 `<meta name=\"viewport\">` 标记"}, "core/audits/viewport.js | failureTitle": {"message": "没有包含 `width` 或 `initial-scale` 的 `<meta name=\"viewport\">` 标记"}, "core/audits/viewport.js | title": {"message": "具有包含 `width` 或 `initial-scale` 的 `<meta name=\"viewport\">` 标记"}, "core/audits/work-during-interaction.js | description": {"message": "这是在 Interaction to Next Paint 测量期间发生的线程阻塞工作。[详细了解 Interaction to Next Paint 指标](https://web.dev/articles/inp)。"}, "core/audits/work-during-interaction.js | displayValue": {"message": "事件“{interactionType}”花费了 {timeInMs, number, milliseconds} 毫秒"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "事件目标"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "尽量减少关键互动期间的工作量"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "输入延迟"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "展示延迟时间"}, "core/audits/work-during-interaction.js | processingTime": {"message": "处理时间"}, "core/audits/work-during-interaction.js | title": {"message": "尽量减少关键互动期间的工作量"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "这些提示旨在帮助改进 ARIA 在您的应用内的使用情况，从而改善辅助技术（例如屏幕阅读器）用户的体验。"}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "这提示旨在为音频和视频提供替代内容。这或许能改善听障用户或视障用户的体验。"}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "音频和视频"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "这些条目突出显示了常见的无障碍功能最佳做法。"}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "最佳做法"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "这些检查会突出显示可[改进您 Web 应用的无障碍功能](https://developer.chrome.com/docs/lighthouse/accessibility/)的提示。自动检测功能只能检测到一部分问题，无法保证您 Web 应用的无障碍程度，因此您不妨再[手动测试](https://web.dev/articles/how-to-review)一下。"}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "这些条目旨在检查自动化测试工具未涵盖的方面。如需了解详情，请参阅有关如何[执行无障碍功能审查](https://web.dev/articles/how-to-review)的指南。"}, "core/config/default-config.js | a11yCategoryTitle": {"message": "无障碍"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "这些提示旨在帮助改进您的内容的易读性。"}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "对比度"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "这些提示旨在让不同语言区域中的用户能够更好地解读您的内容。"}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "国际化和本地化"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "这些提示旨在帮助改进您的应用内控件的语义。这可以改善辅助技术（例如屏幕阅读器）用户的体验。"}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "名称和标签"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "这些提示旨在改进您应用中的键盘导航。"}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "导航"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "这些提示旨在改善使用辅助技术（例如屏幕阅读器）查看表格数据或列表数据的体验。"}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "表格和列表"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "浏览器兼容性"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "最佳做法"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "常规"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "信任与安全"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "用户体验"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "性能预算为您的网站的性能设置了标准。"}, "core/config/default-config.js | budgetsGroupTitle": {"message": "预算"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "详细了解您的应用的性能。这些数字不会[直接影响](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)性能得分。"}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "诊断结果"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "像素在屏幕上的呈现速度是性能的最重要方面。关键指标：First Contentful Paint、First Meaningful Paint"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "改进首次绘制"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "这些建议可以帮助您提高网页加载速度。它们不会[直接影响](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)性能得分。"}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "优化建议"}, "core/config/default-config.js | metricGroupTitle": {"message": "指标"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "改善整体的加载体验，使该网页响应迅速且可尽快投入使用。关键指标：可交互前的耗时、速度指数"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "整体改进"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "性能"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "此类检查会验证渐进式 Web 应用的各个方面。[了解如何构建优秀的渐进式 Web 应用](https://web.dev/articles/pwa-checklist)。"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "基准 [PWA 核对清单](https://web.dev/articles/pwa-checklist)要求必须进行此类检查，但 Lighthouse 不会自动进行此类检查。它们不会影响您的得分，但您必须手动对其进行验证。"}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "可安装"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "优化 PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "此类检查可确保您的网页遵循了基本的搜索引擎优化建议。还有很多其他因素可能会影响您的网页在搜索引擎结果中的排名，但未被 Lighthouse 纳入此处的评估范围，其中包括[核心网页指标](https://web.dev/explore/vitals)衡量结果。[详细了解 Google Search Essentials](https://support.google.com/webmasters/answer/35769)。"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "请在您的网站上运行这些额外的验证程序，以检查其他 SEO 最佳做法。"}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "请确保您的 HTML 格式正确，以便抓取工具更好地了解您的应用的内容。"}, "core/config/default-config.js | seoContentGroupTitle": {"message": "内容最佳做法"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "若想让您的应用显示在搜索结果中，您需要先授权抓取工具访问该应用。"}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "抓取和编入索引"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "请确保您的网页适合移动设备，以便用户无需缩放即可轻松阅读内容页面。[了解如何制作适合移动设备的网页](https://developers.google.com/search/mobile-sites/)。"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "适合移动设备"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "所测试设备的 CPU 速度似乎未达到 Lighthouse 的预期。这可能会对您的性能得分产生负面影响。详细了解如何[校准相应的 CPU 速度减慢倍数](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)。"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "相应网页可能无法按预期加载，因为您所测试的网址 ({requested}) 重定向到了 {final}。请尝试直接测试第二个网址。"}, "core/gather/driver/navigation.js | warningTimeout": {"message": "此页面的加载速度太慢，无法在时限内完成。结果可能会不完整。"}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "浏览器缓存清除操作超时。请尝试再次审核此网页。如果问题仍然存在，请报告错误。"}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{下列位置可能存储了会影响加载性能的数据：{locations}。请在无痕式窗口中审核此页面，以防止这些资源影响您的得分。}other{下列位置可能存储了会影响加载性能的数据：{locations}。请在无痕式窗口中审核此页面，以防止这些资源影响您的得分。}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "原始数据清除操作超时。请尝试再次审核此网页。如果问题仍然存在，请报告错误。"}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "解析 `link` 标头 ({error}) 时出错：`{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "在运行期间检测到网页导航。不建议使用时间跨度模式审核网页导航。使用导航模式审核网页导航可以实现更好的第三方归因和主线程检测。"}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "只有通过 GET 请求进行加载的网页才能储存至往返缓存。"}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "只有状态代码为 2XX 的网页才能被缓存。"}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome 检测到一项在储存于缓存期间执行 JavaScript 的意图。"}, "core/lib/bf-cache-strings.js | appBanner": {"message": "已请求 AppBanner 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "往返缓存已被停用，因为这是一项 keepalive 请求。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "往返缓存被相关 flag 停用了。请在此设备上访问 chrome://flags/#back-forward-cache 以从本地启用该功能。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "往返缓存已被命令行停用。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "因为内存不足，往返缓存已被停用。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "委托行为不支持往返缓存。"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "已针对预渲染程序停用往返缓存。"}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "该网页无法缓存，因为它包含的 BroadcastChannel 实例具有已注册的监听器。"}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "含 cache-control:no-store 标头的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "缓存被刻意清除了。"}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "包含插件的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "使用 FileChooser API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "使用 File System Access API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "使用媒体设备调度程序的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "媒体播放器正在播放内容时用户就离开了网页。"}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "使用 MediaSession API 并设置了播放状态的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "使用 MediaSession API 并设置了操作处理程序的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "往返缓存已被停用，因为受屏幕阅读器影响。"}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "使用 SecurityHandler 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "使用 Serial API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "使用 WebAuthetication API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "使用 WebBluetooth API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "使用 WebUSB API 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "往返缓存已被停用，因为在使用 `Cache-Control: no-store` 的网页上 Cookie 处于停用状态。"}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "使用专用 Worker 或 Worklet 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "该文档还未加载完毕时用户就离开了。"}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "用户离开网页时，系统显示了应用横幅。"}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "用户离开网页时，系统显示了 Chrome 密码管理器。"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "用户离开网页时，DOM 提取正在进行。"}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "用户离开网页时，系统显示了 DOM Distiller Viewer。"}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "往返缓存已被停用，因为扩展程序使用了 Messaging API。"}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "在进入往返缓存之前，采用长期有效连接的扩展程序应断开连接。"}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "采用长期有效连接的扩展程序试图将消息发送到往返缓存中的框架。"}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "往返缓存已被停用，因为受扩展程序影响。"}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "用户离开网页时，该网页上显示了模态对话框（例如表单重新提交）或 HTTP 密码对话框。"}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "用户离开网页时，系统显示了离线版网页。"}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "用户离开网页时，系统显示了 Out-Of-Memory 栏。"}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "用户离开网页时，系统显示了权限请求。"}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "用户离开网页时，系统显示了弹出式内容拦截器。"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "用户离开网页时，系统显示了安全浏览详情。"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "“安全浏览”功能认定该网页有滥用性质，因此拦截了弹出式窗口。"}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "在该网页储存于往返缓存期间，有一个 Service Worker 被启用了。"}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "往返缓存已被停用，因为文档出错了。"}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "采用 FencedFrame 的网页无法存储在 bfcache 中。"}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "该网页被逐出了缓存，以使另一个网页能够缓存。"}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "已被授予媒体流访问权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "使用门户的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | idleManager": {"message": "使用 IdleManager 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "具备开放的 IndexedDB 连接的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "往返缓存已被停用，因为发生了 IndexedDB 事件。"}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "使用了不符合条件的 API。"}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "已被扩展程序注入 `JavaScript` 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "已被扩展程序注入 `StyleSheet` 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | internalError": {"message": "内部错误。"}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "往返缓存已被停用，因为这是一项 keepalive 请求。"}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "使用“键盘锁定”功能的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | loading": {"message": "该网页还未加载完毕时用户就离开了。"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "主资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "主资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "该网页还没从往返缓存中恢复时导航就被取消了。"}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "该网页被逐出了缓存，因为有一项使用中的网络连接收到了太多数据。Chrome 会限制网页在缓存期间可接收的数据量。"}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "包含传输中的 fetch() 或 XHR 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "该网页被逐出了往返缓存，因为有一项使用中的网络请求涉及了重定向。"}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "该网页被逐出了缓存，因为有一项网络连接处于开放状态的时间太长。Chrome 会限制网页在缓存期间可接收数据的时长。"}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "不含有效响应标头的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "导航是在主框架之外的某个框架中发生的。"}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "正在针对已建立索引的数据库处理事务的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "包含传输中的 fetch() 网络请求的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "包含传输中的网络请求的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "包含传输中的 XHR 网络请求的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "使用 PaymentManager 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "使用“画中画”功能的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | portal": {"message": "使用门户的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | printing": {"message": "显示打印界面的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "该网页是使用“`window.open()`”打开的，而另一个标签页引用了该网页；或者，该网页打开了一个窗口。"}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "储存在往返缓存中的网页的渲染程序进程崩溃了。"}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "储存于往返缓存中的网页的渲染程序进程被终止了。"}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "已请求音频截取权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "已请求传感器使用权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "已请求后台同步或提取权限的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "已请求 MIDI 权限的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "已请求通知权限的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "已请求存储空间使用权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "已请求视频拍摄权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "只有网址架构为 HTTP/HTTPS 的网页才能被缓存。"}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "在储存于往返缓存期间，该网页被一个 Service Worker 认领了。"}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "有一个 Service Worker 尝试向储存于往返缓存中的网页发送 `MessageEvent`。"}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "在网页储存于往返缓存期间，ServiceWorker 被取消注册了。"}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "该网页被逐出了往返缓存，因为有一个 Service Worker 被启用了。"}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome 重启了，因而清除了往返缓存条目。"}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "使用 SharedWorker 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "使用 SpeechRecognizer 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "使用 SpeechSynthesis 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "该网页上某个 iframe 发起的导航并未完成。"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "子资源包含 ache-control:no-cache 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "子资源包含 cache-control:no-store 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | timeout": {"message": "该网页超出了往返缓存中的储存时长上限，因而已过期。"}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "该网页在储存至往返缓存时超时了（可能是因为 pagehide 处理程序长时间运行）。"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "该网页的主框架中含有一款卸载处理程序。"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "该网页的子框架中含有一款卸载处理程序。"}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "浏览器更改了用户代理替换标头。"}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "已被授予视频/音频录制权的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "使用 WebDatabase 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webHID": {"message": "使用 WebHID 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webLocks": {"message": "使用 WebLocks 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webNfc": {"message": "使用 WebNfc 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "使用 WebOTPService 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webRTC": {"message": "使用 WebRTC 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webShare": {"message": "使用 Webshare 的网页目前无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webSocket": {"message": "使用 WebSocket 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webTransport": {"message": "使用 WebTransport 的网页无法储存至往返缓存。"}, "core/lib/bf-cache-strings.js | webXR": {"message": "使用 WebXR 的网页目前无法储存至往返缓存。"}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "建议您添加 https: 和 http: 网址架构（会被支持 `'strict-dynamic'` 的浏览器忽略），以便向后兼容旧版浏览器。"}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "从 CSP3 开始，`disown-opener` 已被弃用。因此，请改用 Cross-Origin-Opener-Policy 标头。"}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "从 CSP2 开始，`referrer` 已被弃用。因此，请改用 Referrer-Policy 标头。"}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "从 CSP2 开始，`reflected-xss` 已被弃用。因此，请改用 X-XSS-Protection 标头。"}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "如果缺少 `base-uri`，将为注入的 `<base>` 标记提供可乘之机，让它们能够将所有相对网址（例如脚本）的基础网址设置为受攻击者控制的网域。因此，我们建议您将 `base-uri` 设置为 `'none'` 或 `'self'`。"}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "如果缺少 `object-src`，将为注入可执行不安全脚本的插件提供可乘之机。因此，我们建议您将 `object-src` 设置为 `'none'`（如果可以的话）。"}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "缺少 `script-src` 指令。这可为执行不安全脚本提供可乘之机。"}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "您忘了要使用英文分号吗？{keyword}似乎是一个指令，而非关键字。"}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces 应该使用 base64 字符集。"}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces 应包含至少 8 个字符。"}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "请避免在这条指令中仅仅使用网址架构 ({keyword})。如果仅仅使用网址架构，便无法防止脚本来自不安全的网域。"}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "请避免在这条指令中仅仅使用通配符 ({keyword})。如果仅仅使用通配符，便无法防止脚本来自不安全的网域。"}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "报告目的地只能通过 report-to 指令进行配置。此指令仅适用于基于 Chromium 的浏览器，因此我们建议您一并使用 `report-uri` 指令。"}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "任何 CSP 都未配置报告目的地。这会导致系统很难持续维护 CSP 以及监控故障。"}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "主机许可名单经常可被绕过。因此，我们建议您改用 CSP nonces 或 hashes（以及 `'strict-dynamic'`，若有必要的话）。"}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "CSP 指令不明。"}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword}似乎是无效关键字。"}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "如果使用 `'unsafe-inline'`，将为执行不安全的页内脚本和事件处理脚本提供可乘之机。因此，我们建议您使用 CSP nonces 或 hashes 来逐一允许各个脚本。"}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "建议您添加 `'unsafe-inline'`（会被支持 nonces/hashes 的浏览器忽略），以便向后兼容旧版浏览器。"}, "core/lib/deprecation-description.js | feature": {"message": "如需了解详情，请参阅功能状态页面。"}, "core/lib/deprecation-description.js | milestone": {"message": "此变更将从里程碑 {milestone} 生效。"}, "core/lib/deprecation-description.js | title": {"message": "使用了已弃用的功能"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "处理 CORS `Access-Control-Allow-Headers` 时，授权将不在通配符 (*) 的涵盖范围内。"}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "若要停用默认 Cast 集成，应使用 `disableRemotePlayback` 属性，而非 `-internal-media-controls-overlay-cast-button` 选择器。"}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "如果资源请求的网址同时包含已移除的空白字符 `(n|r|t)` 和小于字符 (`<`)，相应资源请求会被屏蔽。请从元素属性值等位置移除换行符并编码小于字符，以便加载这些资源。"}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` 已被弃用，请改用标准化 API：Navigation Timing 2。"}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` 已被弃用，请改用标准化 API：Paint Timing。"}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` 已被弃用，请改用标准化 API：Navigation Timing 2 中的 `nextHopProtocol`。"}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "包含 `(0|r|n)` 字符的 Cookie 将被拒，而不是被截断。"}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "通过设置 `document.domain` 放宽同源政策的功能已被弃用，并将默认处于停用状态。此弃用警告针对的是通过设置 `document.domain` 启用的跨源访问。"}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "从跨源 iframe 触发 window.alert 的功能已被弃用，日后将被移除。"}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "从跨源 iframe 触发 window.confirm 的功能已被弃用，日后将被移除。"}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "DOM 变更事件（包括 `DOMSubtreeModified`、`DOMNodeInserted`、`DOMNodeRemoved`、`DOMNodeRemovedFromDocument`、`DOMNodeInsertedIntoDocument` 和 `DOMCharacterDataModified`，详见 https://w3c.github.io/uievents/#legacy-event-types）已被弃用，并将被移除。请改用 `MutationObserver`。"}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "对 SVG <use> 元素中 data: URL 的支持已被弃用，日后将被移除。"}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "通过设置 `document.domain` 放宽同源政策的功能已被弃用，并将默认处于停用状态。若要继续使用此功能，请通过发送 `Origin-Agent-Cluster: ?0` 标头以及文档和框架的 HTTP 响应来选择停用以源为键的代理集群。如需了解详情，请访问 https://developer.chrome.com/blog/immutable-document-domain/。"}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "`Expect-CT` 标头已被弃用，并将被移除。Chrome 要求，在 2018 年 4 月 30 日之后颁发的所有受大众信任的证书均须遵守证书透明度政策。"}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "`getCurrentPosition()` 和 `watchPosition()` 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` 和 `watchPosition()` 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` 不再适用于不安全的源。若要使用此功能，您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` 已被弃用。请改用 `RTCPeerConnectionIceErrorEvent.address` 或 `RTCPeerConnectionIceErrorEvent.port`。"}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "`canmakepayment` Service Worker 事件中的商家源和任意数据已被弃用，并将被移除：`topOrigin`、`paymentRequestOrigin`、`methodData`、`modifiers`。"}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "该网站向网络请求了一项子资源，而且完全是因为其用户的特权网络位置才能够访问此项资源。此类请求会向互联网公开非公用设备和服务器，这会增加跨站请求伪造 (CSRF) 攻击和/或信息泄露的风险。为降低这类风险，Chrome 不再支持从非安全上下文发起针对非公用子资源的请求，并将开始阻止此类请求。"}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "向 `joinAdInterestGroup()` 传递的 `InterestGroups` 所含 `dailyUpdateUrl` 字段已被重命名为 `updateUrl`，以便更准确地反映其行为。"}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "无法从 `file:` 网址加载 CSS，除非它们以 `.css` 文件扩展名结尾。"}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "由于规范变更，使用 `SourceBuffer.abort()` 中止 `remove()` 移除异步范围的功能已被弃用。日后我们会移除相应支持。您应改为监听 `updateend` 事件。`abort()` 只应当用于中止异步媒体附加或重置解析状态。"}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "由于规范变更，我们不再支持将 `MediaSource.duration` 设为低于任何缓冲编码帧的最高呈现时间戳。日后我们将不再支持隐式移除被截断的缓冲媒体。您应改为对 `newDuration < oldDuration` 的所有 `sourceBuffers` 执行显式 `remove(newDuration, oldDuration)`。"}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "即使 `MIDIOptions` 中未指定 sysex，Web MIDI 也会请求获得使用许可。"}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "较旧的非标准化 `shadowroot` 属性已被弃用，自 M119 起将*不再起作用*。请改用新的标准化 `shadowrootmode` 属性。"}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "无法再从不安全的源使用 Notification API。您应考虑将您的应用转移到安全的源，例如 HTTPS。如需了解详情，请访问 https://goo.gle/chrome-insecure-origins。"}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "无法再从跨源 iframe 中请求 Notification API 权限。您应考虑改为从顶级框架中请求权限，或者打开一个新窗口。"}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "createImageBitmap 中的 `imageOrientation: 'none'` 选项已被弃用。请改用带有 \\{imageOrientation: 'from-image'\\} 选项的 createImageBitmap。"}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "您的合作伙伴正在协商某个已过时的 (D)TLS 版本。请与您的合作伙伴联系，以解决此问题。"}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "为 img、video 和 canvas 标记指定 `overflow: visible` 可能会导致这些标记在元素边界之外生成视觉内容。请参阅 https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md。"}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "`paymentManager.instruments` 已被弃用。请改用即时安装方式安装付款处理程序。"}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "您的 `PaymentRequest` 调用已绕过内容安全政策 (CSP) `connect-src` 指令。此绕过方式已被弃用。请将 `PaymentRequest` API 中的付款方式标识符（在 `supportedMethods` 字段中）添加到 CSP `connect-src` 指令中。"}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "`StorageType.persistent` 已被弃用。请改用标准化 `navigator.storage`。"}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "带有 `<picture>` 父级的 `<source src>` 无效，因此会被忽略。请改用 `<source srcset>`。"}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame 因供应商而异。请改用标准 cancelAnimationFrame。"}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame 因供应商而异。请改用标准 requestAnimationFrame。"}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "HTMLVideoElement.webkitDisplayingFullscreen 已被弃用。请改用 Document.fullscreenElement。"}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "HTMLVideoElement.webkitEnterFullScreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "HTMLVideoElement.webkitEnterFullscreen() 已被弃用。请改用 Element.requestFullscreen()。"}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "HTMLVideoElement.webkitExitFullScreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "HTMLVideoElement.webkitExitFullscreen() 已被弃用。请改用 Document.exitFullscreen()。"}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "HTMLVideoElement.webkitSupportsFullscreen 已被弃用。请改用 Document.fullscreenEnabled。"}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "我们即将弃用 API `chrome.privacy.websites.privacySandboxEnabled`，但为了保持向后兼容性，该 API 可持续使用到 M113 版。届时，请改用 `chrome.privacy.websites.topicsEnabled`、`chrome.privacy.websites.fledgeEnabled` 和 `chrome.privacy.websites.adMeasurementEnabled`。请参阅 https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled。"}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "约束条件 `DtlsSrtpKeyAgreement` 已被移除。您已为此约束条件指定 `false` 值，系统会将这种情况解读为尝试使用已被移除的 `SDES key negotiation` 方法。此功能已被移除；请改用支持 `DTLS key negotiation`的服务。"}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "约束条件 `DtlsSrtpKeyAgreement` 已被移除。您已为此约束条件指定 `true` 值，这没有任何作用，但为整洁起见，您可以移除此约束条件。"}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "基于回调的 getStats() 已被弃用，并将被移除。请改用符合规范的 getStats()。"}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Range.expand() 已被弃用。请改用 Selection.modify()。"}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "如果子资源请求的网址包含嵌入式凭据（例如 `**********************/`），相应子资源请求会被屏蔽。"}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` 选项已被弃用，并将被移除。"}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` 将要求进行跨域隔离。如需了解详情，请访问 https://developer.chrome.com/blog/enabling-shared-array-buffer/。"}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "无需用户激活的 `speechSynthesis.speak()` 已被弃用，并将被移除。"}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "扩展程序应选择启用跨域隔离，以便继续使用 `SharedArrayBuffer`。请参阅 https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/。"}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Web SQL 已被弃用。请使用 SQLite WebAssembly 或 Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "权限描述符 `window-placement` 已被弃用。请改用 `window-management`。如需更多帮助，请访问 https://bit.ly/window-placement-rename。"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "权限政策 `window-placement` 已被弃用。请改用 `window-management`。如需更多帮助，请访问 https://bit.ly/window-placement-rename。"}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "`XMLHttpRequest` 中的响应 JSON 不支持 UTF-16"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "主线程上的同步 `XMLHttpRequest` 已被弃用，因为它会对最终用户的体验产生不利影响。如需更多帮助，请访问 https://xhr.spec.whatwg.org/。"}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "`supportsSession()` 已被弃用。请改用 `isSessionSupported()` 并查看已解析的布尔值。"}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "主线程拦截时间"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "缓存 TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "说明"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "时长"}, "core/lib/i18n/i18n.js | columnElement": {"message": "元素"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "与建议不符的元素"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "位置"}, "core/lib/i18n/i18n.js | columnName": {"message": "名称"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "超出预算"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "请求"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "资源大小"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "资源类型"}, "core/lib/i18n/i18n.js | columnSize": {"message": "大小"}, "core/lib/i18n/i18n.js | columnSource": {"message": "来源"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "开始时间"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "花费的时间"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "传输文件大小"}, "core/lib/i18n/i18n.js | columnURL": {"message": "网址"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "可能达到的节省程度"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "可能达到的节省程度"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "有望节省 {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{发现了 1 个元素}other{发现了 # 个元素}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "有望节省 {wastedMs, number, milliseconds} 毫秒"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "文档"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "首次有效绘制时间"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "字体"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "图片"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "从互动到下一次绘制"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "高"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "低"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "中"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "First Input Delay 最长预估值"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "媒体"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} 毫秒"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "其他"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "其他资源"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "脚本"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 秒"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "样式表"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "第三方"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "总计"}, "core/lib/lh-error.js | badTraceRecording": {"message": "在您加载的网页上录制跟踪记录时发生了错误。请重新运行 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "等待调试程序协议初次连接时超时。"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome 在网页加载期间未收集任何屏幕截图。请确保网页上有可见的内容，然后尝试重新运行 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS 服务器无法解析所提供的网域。"}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "必需的 {artifactName} 收集器出现错误：{errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "发生了内部 Chrome 错误。请重新启动 Chrome，然后尝试重新运行 Lighthouse。"}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "必需的 {artifactName} 收集器未运行。"}, "core/lib/lh-error.js | noFcp": {"message": "该网页未渲染任何内容。请确保在网页加载过程中让浏览器窗口始终位于前台，然后重试。({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "该网页显示的内容不符合 Largest Contentful Paint (LCP) 的要求。请确保该网页具有有效的 LCP 元素，然后重试。({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "所提供的网页不是 HTML（以 MIME 类型 {mimeType} 提供）。"}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "这个 Chrome 版本太旧，不支持“{featureName}”。请使用较新版本以查看完整结果。"}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse 无法可靠地加载您请求的页面。请确保您测试的网址正确无误并且服务器可正确响应所有请求。"}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse 无法可靠地加载您请求的网址，因为页面已停止响应。"}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "您提供的网址缺少有效的安全证书。{securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome 阻止了带有插页式广告的网页加载。请确保您测试的网址正确无误并且服务器可正确响应所有请求。"}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse 无法可靠地加载您请求的页面。请确保您测试的网址正确无误并且服务器可正确响应所有请求。（详细信息：{errorDetails}）"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse 无法可靠地加载您请求的页面。请确保您测试的网址正确无误并且服务器可正确响应所有请求。（状态代码：{statusCode}）"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "您的网页加载时间过长。请按照报告中给出的提示缩短网页加载时间，然后尝试重新运行 Lighthouse。({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "等待 DevTools 协议响应的用时超出了分配的时间。(方法：{protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "提取资源内容的用时超出了分配的时间"}, "core/lib/lh-error.js | urlInvalid": {"message": "您提供的网址似乎无效。"}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Lighthouse 无法可靠地加载您请求的页面。请确保您测试的网址正确无误并且服务器可正确响应所有请求。（状态代码：{errorCode}）"}, "core/lib/navigation-error.js | warningXhtml": {"message": "页面 MIME 类型为 XHTML：Lighthouse 未明确支持此文档类型"}, "core/user-flow.js | defaultFlowName": {"message": "用户体验流程 ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "导航报告 ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "快照报告 ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "时间跨度报告 ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "所有报告"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "类别"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "无障碍"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "最佳做法"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "性能"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "渐进式 Web 应用"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "桌面版"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "了解 Lighthouse 流程报告"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "了解流程"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "使用导航报告可以…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "使用快照报告可以…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "使用时间跨度报告可以…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "获取 Lighthouse 给出的性能得分。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "衡量网页加载性能指标，例如 Largest Contentful Paint 和 Speed Index。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "评估渐进式 Web 应用的功能。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "查找单页应用或复杂表单中的无障碍功能方面的问题。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "评估互动背后隐藏的菜单和界面元素的最佳做法。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "衡量一系列互动的布局偏移和 JavaScript 执行用时。"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "发掘性能提升机会，以便改进长期网页和单页应用的用户体验。"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "影响力最大"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} 项参考性评估}other{{numInformative} 项参考性评估}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "移动版"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "网页加载"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "导航报告旨在分析单个网页的加载情况，与最初的 Lighthouse 报告完全一样。"}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "导航报告"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} 份导航报告}other{{numNavigation} 份导航报告}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} 项有望通过的评估}other{{numPassableAudits} 项有望通过的评估}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{通过了 {numPassed} 项评估}other{通过了 {numPassed} 项评估}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "一般"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "出错了"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "较差"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "良好"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "保存"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "捕获到的网页状态"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "快照报告旨在分析处于特定状态的网页（通常是在用户互动之后）。"}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "快照报告"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} 份快照报告}other{{numSnapshot} 份快照报告}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "摘要"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "用户互动"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "时间跨度报告旨在分析任意时间段（通常包含用户互动）。"}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "时间跨度报告"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} 份时间跨度报告}other{{numTimespan} 份时间跨度报告}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse 用户流报告"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "对于动画内容，请使用 [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) 尽量减少当内容在屏幕外时的 CPU 使用量。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "您不妨既以 WebP 格式显示所有 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 组件，也为其他浏览器指定适当的后备图片。[了解详情](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "确保您对图片使用了 [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 以便自动延迟加载。[了解详情](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "使用 [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) 之类的工具[在服务器端呈现 AMP 布局](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "请参阅 [AMP 文档](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)以确保所有样式均受支持。"}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) 组件支持 [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) 属性，以便系统根据屏幕尺寸指定要使用的图片素材资源。[了解详情](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "如果要呈现的列表非常大，您不妨使用 Component Dev Kit (CDK) 进行虚拟滚动。[了解详情](https://web.dev/virtualize-lists-with-angular-cdk/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "采用[路径级代码拆分](https://web.dev/route-level-code-splitting-in-angular/)尽量压缩 JavaScript 软件包的大小。您也可考虑使用 [Angular Service Worker](https://web.dev/precaching-with-the-angular-service-worker/) 预缓存资源。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "如果您使用的是 Angular CLI，请确保在正式版模式下生成版本。[了解详情](https://angular.io/guide/deployment#enable-runtime-production-mode)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "如果您使用了 Angular CLI，请在正式版中添加源代码映射以检查您的软件包。[了解详情](https://angular.io/guide/deployment#inspect-the-bundles)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "提前预加载路径以加快导航速度。[了解详情](https://web.dev/route-preloading-in-angular/)。"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "您不妨使用 Component Dev Kit (CDK) 中的 `BreakpointObserver` 实用程序管理映像断点。[了解详情](https://material.angular.io/cdk/layout/overview)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "建议您将 GIF 上传到可让其作为 HTML5 视频嵌入的服务。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "在主题背景中定义自定义字体时，您可以指定 `@font-display`。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "不妨考虑在您的网站上配置[含 Convert 图片样式的 WebP 图片格式](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "安装可延迟加载图片的 [Drupal 模块](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search)。此类模块能够推迟加载所有的屏幕外图片，进而提高性能。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "不妨考虑使用模块来内嵌关键的 CSS 和 JavaScript，并为非关键的 CSS 或 JavaScript 使用 defer 属性。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "主题背景、模块和服务器规格都会影响服务器响应用时。建议您查找更优化的主题背景、仔细选择优化模块并/或升级您的服务器。您的托管服务器应该使用 PHP 运算码缓存、内存缓存（例如 Redis 或 Memcached）来缩短数据库查询时间，并优化应用逻辑以加快网页准备速度。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "建议您使用[自适应图片样式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)减小在您网页上加载的图片的大小。如果您使用 Views 在网页上显示多个内容项，请考虑实现分页，以限制给定网页上显示的内容项的数量。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "确保您已在“管理 » 配置 » 开发”页面中启用了“聚合 CSS 文件”。为获得更好的资源聚合支持，请确保您的 Drupal 网站至少在 Drupal 10.1 以上版本运行。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "确保您已在“管理 » 配置 » 开发”页面中启用了“聚合 JavaScript 文件”。为获得更好的资源聚合支持，请确保您的 Drupal 网站至少在 Drupal 10.1 以上版本运行。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "建议您移除未使用的 CSS 规则，仅将所需的 Drupal 库附加到相关网页或网页中的组件。如需了解详情，请访问 [Drupal 文档链接](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)。若想找出会添加无关 CSS 的附加库，请尝试在 Chrome 开发者工具中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。在 Drupal 网站中停用 CSS 聚合后，您可根据样式表网址找出导致问题的主题背景/模块。请留意列表中哪些主题背景/模块有大量样式表的代码覆盖率偏红。主题背景/模块应该只将网页中确实用到的样式表加入队列。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "建议您移除未使用的 JavaScipt 资源，仅将所需的 Drupal 库附加到相关网页或网页中的组件。如需了解详情，请访问 [Drupal 文档链接](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library)。若要找出会添加无关 JavaScript 的附加库，请尝试在 Chrome 开发者工具中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。在 Drupal 网站中停用 JavaScript 聚合后，您可根据脚本网址找出导致问题的主题背景/模块。请留意列表中哪些主题背景/模块有大量脚本的代码覆盖率偏红。主题背景/模块应该只将网页中确实用到的脚本加入队列。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "在“管理 » 配置 » 开发”页面中设置“浏览器和代理缓存最长时限”。了解 [Drupal 缓存和性能优化](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "建议您使用[模块](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)自动优化通过网站上传的图片，并在不影响图片质量的前提下缩减这些图片的大小。此外，还请确保为网站上呈现的所有图片使用 Drupal 的原生[自适应图片样式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)（在 Drupal 8 及更高版本中提供）。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "您可以通过安装和配置[一个能协助创建用户代理资源提示的模块](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)，来添加 preconnect 或 dns-prefetch 资源提示。"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "确保您使用的是 Drupal 提供的原生[自适应图片样式](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)（在 Drupal 8 及更高版本中提供）。通过视图模式、视图或借助 WYSIWYG 编辑器上传的图片呈现图片字段时，请使用自适应图片样式。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Optimize Fonts`”设置，即可自动利用 `font-display` CSS 功能来确保用户在网页字体加载期间能看到文字。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Next-Gen Formats`”设置，即可将图片转换成 WebP 格式。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Lazy Load Images`”设置，即可将屏幕外图片的加载时间延迟到需要它们时。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Critical CSS`”和“`Script Delay`”设置，即可延迟加载非关键 JS/CSS。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "您只需使用 [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching)，即可将内容缓存到我们的全球网络，从而缩短第一个字节呈现前的加载用时。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Minify CSS`”设置，即可自动压缩 CSS 以减少网络载荷量。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Minify Javascript`”设置，即可自动压缩 JS 以减少网络载荷量。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Remove Unused CSS`”设置，即可助力解决此问题。此设置会识别您网站的每个网页上实际使用的 CSS 类并移除其他所有类，以确保文件始终不大。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Efficient Static Cache Policy`”设置，即可在缓存标头中为静态资源设定建议值。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Next-Gen Formats`”设置，即可将图片转换成 WebP 格式。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Pre-Connect Origins`”设置，即可自动添加 `preconnect` 资源提示以尽早连接到重要的第三方源。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Preload Fonts`”和“`Preload Background Images`”设置，即可添加 `preload` 链接来优先提取目前在网页加载过程中较晚请求加载的资源。"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "您只需使用 [Ezoic Leap](https://pubdash.ezoic.com/speed) 并启用“`Resize Images`”设置，即可调整图片大小以适合设备的大小，从而减少网络载荷量。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "请使用 `gatsby-plugin-image` 组件（而非 `<img>`）自动优化图片格式。[了解详情](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "请使用 `gatsby-plugin-image` 组件（而非 `<img>`）自动延迟加载图片。[了解详情](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "请使用 `gatsby-plugin-image` 组件并将 `loading` 属性设为 `eager`。[了解详情](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "请使用 `Gatsby Script API` 推迟加载非关键第三方脚本。[了解详情](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "请使用 `PurgeCSS` `Gatsby` 插件从样式表中移除未使用的规则。[了解详情](https://purgecss.com/plugins/gatsby.html)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "请使用 `Webpack Bundle Analyzer` 检测未使用的 JavaScript 代码。[了解详情](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "请为不可变资源配置缓存。[了解详情](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "请使用 `gatsby-plugin-image` 组件（而非 `<img>`）调整图片质量。[了解详情](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)。"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "请使用 `gatsby-plugin-image` 组件设置适当的 `sizes`。[了解详情](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "建议您将 GIF 上传到可让其作为 HTML5 视频嵌入的服务。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "建议您使用可自动将您上传的图片转换为最佳格式的[插件](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp)或服务。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "安装能够延迟加载所有屏幕外图片的[延迟加载 Joomla 插件](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading)，或者改用可提供该功能的模板。从 Joomla 4.0 开始，所有新图片都会[自动](https://github.com/joomla/joomla-cms/pull/30748)从核心获取 `loading` 属性。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "有很多 Joomla 插件可帮助您[内嵌重要资源](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)或[推迟加载不太重要的资源](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)。请注意，这些插件执行的优化可能会导致您的模板或插件功能无法正常运行，因此您需要全面测试这些插件。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "模板、扩展程序和服务器规格都会影响服务器响应用时。建议您查找更优化的模板、仔细选择优化扩展程序并/或升级您的服务器。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "建议您在文章类别中显示摘录（例如，通过“了解详情”链接）、减少给定页面上显示的文章的数量、将长博文拆分成多个页面，或者使用插件延迟加载评论。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "很多 [Joomla 扩展程序](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)可通过串联、缩减和压缩您的 CSS 样式来加快您网站的加载速度。部分模板也提供此功能。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "很多 [Joomla 扩展程序](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)都可通过串联、缩减和压缩您的脚本来加快您网站的加载速度。部分模板也提供此功能。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "某些 [Joomla 扩展程序](https://extensions.joomla.org/)会在您的网页中加载未使用的 CSS，建议您减少这类扩展程序的数量，或改用其他扩展程序。若想找出会添加无关 CSS 的扩展程序，请尝试在 Chrome 开发者工具中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。您可根据样式表网址找出导致问题的主题背景/插件。请留意列表中哪些插件有大量样式表的代码覆盖率偏红。插件应该只将网页中确实用到的样式表加入队列。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "某些 [Joomla 扩展程序](https://extensions.joomla.org/)会在您的网页中加载未使用的 JavaScript，建议您减少这类扩展程序的数量，或改用其他扩展程序。若想找出会添加无关 JS 的插件，请尝试在 Chrome 开发者工具中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。您可根据脚本网址找出导致问题的扩展程序。请留意列表中哪些扩展程序有大量脚本的代码覆盖率偏红。扩展程序应该只将网页中确实用到的脚本加入队列。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "了解 [<PERSON><PERSON><PERSON> 中的浏览器缓存功能](https://docs.joomla.org/Cache)。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "建议您使用[图片优化插件](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)，以在不影响图片质量的前提下压缩图片。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "建议您使用[自适应图片插件](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)，以在您的内容中使用自适应图片。"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "您可以通过在 Joomla 中启用 Gzip 页面压缩（“系统”>“全局配置”>“服务器”）来启用文本压缩功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "如果您没有捆绑 JavaScript 资源，不妨使用 [baler](https://github.com/magento/baler)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "停用 Magento 内置的 [JavaScript 捆绑和缩减](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)功能，并考虑改用 [baler](https://github.com/magento/baler/)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "在[定义自定义字体](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)时指定 `@font-display`。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "您不妨在 [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) 中搜索各种第三方扩展程序，以利用更新的图片格式。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "您不妨修改产品和目录模板，以利用这一网络平台的[延迟加载](https://web.dev/native-lazy-loading)功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "使用 Magento 的 [Varnish 集成](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "在您商店的开发者设置中启用“Minify CSS Files”选项。[了解详情](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "使用 [Terser](https://www.npmjs.com/package/terser) 缩减静态内容部署中的所有 JavaScript 资源，并停用内置的缩减功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "停用 Magento 内置的 [JavaScript 捆绑](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)功能。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "您不妨在 [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) 中搜索各种第三方扩展程序，以便优化图片。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "您可以通过[修改主题背景的布局](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)来添加 preconnect 或 dns-prefetch 资源提示。"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "您可以通过[修改主题背景的布局](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)来添加 `<link rel=preload>` 标记。"}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "请使用 `next/image` 组件（而非 `<img>`）自动优化图片格式。[了解详情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "请使用 `next/image` 组件（而非 `<img>`）自动延迟加载图片。[了解详情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "您只需使用 `next/image` 组件并将“priority”设为 true，即可预加载 LCP 图片。[了解详情](https://nextjs.org/docs/api-reference/next/image#priority)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "请使用 `next/script` 组件推迟加载非关键第三方脚本。[了解详情](https://nextjs.org/docs/basic-features/script)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "请使用 `next/image` 组件，以确保始终指定恰当的图片尺寸。[了解详情](https://nextjs.org/docs/api-reference/next/image#width)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "建议您在 `Next.js` 配置中设置 `PurgeCSS` 以从样式表中移除未使用的规则。[了解详情](https://purgecss.com/guides/next.html)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "请使用 `Webpack Bundle Analyzer` 检测未使用的 JavaScript 代码。[了解详情](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "建议您使用 `Next.js Analytics` 衡量应用的实际性能。[了解详情](https://nextjs.org/docs/advanced-features/measuring-performance)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "请为不可变资源和`Server-side Rendered` (SSR) 网页配置缓存。[了解详情](https://nextjs.org/docs/going-to-production#caching)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "请使用 `next/image` 组件（而非 `<img>`）调整图片质量。[了解详情](https://nextjs.org/docs/basic-features/image-optimization)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "请使用 `next/image` 组件设置适当的 `sizes`。[了解详情](https://nextjs.org/docs/api-reference/next/image#sizes)。"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "请在您的 Next.js 服务器上启用压缩功能。[了解详情](https://nextjs.org/docs/api-reference/next.config.js/compression)。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "如需启用 [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337)，请与您的客户经理联系。配置该功能后，系统会将页面渲染性能设为优先事项，并优化此性能。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "使用 NitroPack 中的 [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) 选项为 CSS font-display 规则设置所需的值。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "使用 [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) 可自动将图片转换为 WebP 格式。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "启用 [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images) 可推迟加载屏幕外图片。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "在 NitroPack 中启用 [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) 可缩短初始加载时间。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "启用 [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361) 可缩短服务器响应用时，并提升用户感知到的性能。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "在缓存设置中启用 [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) 可缩减 CSS、HTML 和 JavaScript 文件的大小，从而缩短加载时间。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "在缓存设置中启用 [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) 可缩减 JS、HTML 和 CSS 文件的大小，从而缩短加载时间。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "启用 [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) 可移除对该页面不适用的 CSS 规则。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "在 NitroPack 中配置 [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) 可延迟脚本的加载时间，到有需要时再加载。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "前往 `Caching` 菜单中的 [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) 功能，调整页面缓存失效时间，即可缩短加载时间并改善用户体验。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "启用 [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack) 设置后，系统会自动压缩、优化图片并将其转换为 WebP 格式。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "启用 [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) 可提前优化图片，使其与在所有设备上显示时所用容器的尺寸相匹配。"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "在 NitroPack 中使用 [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) 可缩减向浏览器发送的文件的大小。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "请使用 `nuxt/image` 组件并采用如下设置：`format=\"webp\"`。[了解详情](https://image.nuxt.com/usage/nuxt-img#format)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "对于屏幕外图片，请使用 `nuxt/image` 组件并采用如下设置：`loading=\"lazy\"`。[了解详情](https://image.nuxt.com/usage/nuxt-img#loading)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "对于 LCP 图片，请使用 `nuxt/image` 组件并指定 `preload`。[了解详情](https://image.nuxt.com/usage/nuxt-img#preload)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "请使用 `nuxt/image` 组件并指定明确的 `width` 和 `height`。[了解详情](https://image.nuxt.com/usage/nuxt-img#width-height)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "请使用 `nuxt/image` 组件并设置适当的 `quality`。[了解详情](https://image.nuxt.com/usage/nuxt-img#quality)。"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "请使用 `nuxt/image` 组件并设置适当的 `sizes`。[了解详情](https://image.nuxt.com/usage/nuxt-img#sizes)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[将 GIF 动画替换为视频](https://web.dev/replace-gifs-with-videos/)以提高网页加载速度，并考虑使用现代文件格式（例如 [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) 或 [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)），通过目前最先进的 VP9 视频编解码器将压缩效率提高 30% 以上。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "建议您使用可自动将所上传的图片转换为最佳格式的[插件](https://octobercms.com/plugins?search=image)或服务。[WebP 无损图片](https://developers.google.com/speed/webp)比 PNG 图片小 26%，比采用等效 SSIM 质量指标的同等 JPEG 图片小 25-34%。还可以考虑使用 [AVIF](https://jakearchibald.com/2020/avif-has-landed/) 这种新一代图片格式。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "建议您安装能延迟加载所有屏幕外图片的[图片延迟加载插件](https://octobercms.com/plugins?search=lazy)，或者改用可提供该功能的主题。另外，建议您使用 [AMP 插件](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "有很多插件可帮助您[内嵌重要资源](https://octobercms.com/plugins?search=css)。这些插件可能会导致其他插件无法正常运行，因此您需要全面测试。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "主题、插件和服务器规范都会影响服务器响应用时。建议您查找更优化的主题、仔细选择优化插件并/或升级服务器。借助 October CMS，开发者还可以使用[`Queues`](https://octobercms.com/docs/services/queues)推迟耗时任务（例如发送电子邮件）的处理时间。这可以大大加快 Web 请求的处理速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "建议您在博文列表中显示摘录（例如，使用“`show more`”按钮）、减少给定网页上显示的博文的数量、将长博文拆分成多个网页，或者使用插件延迟加载评论。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "有很多[插件](https://octobercms.com/plugins?search=css)可通过串联、缩减和压缩样式来加快网站的加载速度。使用构建流程预先执行此缩减操作可加快开发速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "有很多[插件](https://octobercms.com/plugins?search=javascript)可通过串联、缩减和压缩脚本来加快网站的加载速度。使用构建流程预先执行此缩减操作可加快开发速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "建议您检查会在网站上加载未使用的 CSS 的[插件](https://octobercms.com/plugins)。若想找出会添加不必要 CSS 的插件，请在 Chrome Devtools 中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。根据样式表网址找出引发问题的主题/插件。检查哪些插件有大量样式表的代码覆盖率偏红。插件应该只添加网页中确实用到的样式表。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "建议您检查会在网页中加载未使用的 JavaScript 的[插件](https://octobercms.com/plugins?search=javascript)。若想找出会添加不必要 JavaScript 的插件，请在 Chrome DevTools 中运行[代码覆盖率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)测试。根据脚本网址找出引发问题的主题/插件。检查哪些插件有大量脚本的代码覆盖率偏红。插件应该只添加网页中确实用到的脚本。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "了解如何[使用 HTTP 缓存避免不必要的网络请求](https://web.dev/http-cache/#caching-checklist)。有很多[插件](https://octobercms.com/plugins?search=Caching)可用于加快缓存速度。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "建议您使用[图片优化插件](https://octobercms.com/plugins?search=image)，以在不影响图片质量的前提下压缩图片。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "直接在媒体管理器中上传图片，以确保能按照所要求的尺寸提供图片。建议您使用[大小调整过滤器](https://octobercms.com/docs/markup/filter-resize)或[图片大小调整插件](https://octobercms.com/plugins?search=image)来确保采用最佳的图片大小。"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "在网络服务器配置中启用文本压缩。"}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "如果您要在页面上呈现很多重复元素，不妨使用“窗口类”库（如 `react-window`）尽量减少所创建的 DOM 节点的数量。[了解详情](https://web.dev/virtualize-long-lists-react-window/)。此外，如果您使用 `Effect` 钩子提高运行时性能，则应该仅在某些依赖项发生更改之前使用 [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action)、[`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) 或 [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) 及[跳过效果](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects)，以尽量减少不必要的重复呈现。"}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "如果您使用了 React Router，请尽量避免使用 `<Redirect>` 组件进行[路径导航](https://reacttraining.com/react-router/web/api/Redirect)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "如果您在服务器端呈现任何 React 组件，不妨使用 `renderToPipeableStream()` 或 `renderToStaticNodeStream()` 来允许客户端接收并合成标记的各个不同部分，而不是一次性接收整个标记。[了解详情](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "如果您的构建系统可自动压缩 CSS 文件大小，请确保您部署的是正式版应用。您可以通过 React Developer Tools 扩展程序执行此项检查。[了解详情](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "如果您的构建系统可自动压缩 JS 文件大小，请确保您部署的是正式版应用。您可以通过 React Developer Tools 扩展程序执行此项检查。[了解详情](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)。"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "如果您不使用服务器端呈现，请使用 `React.lazy()` [拆分 JavaScript 软件包](https://web.dev/code-splitting-suspense/)。否则，请使用第三方库（如 [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)）拆分代码。"}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "使用 React DevTools Profiler，从而利用 Profiler API 来衡量组件的呈现性能。[了解详情](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)。"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "请将视频放置在 `VideoBoxes` 内，使用`Video Masks`自定义视频，或者添加`Transparent Videos`。[了解详情](https://support.wix.com/en/article/wix-video-about-wix-video)。"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "请使用 `Wix Media Manager` 上传图片，以确保网站能自动以 WebP 格式加载图片。了解网站媒体资源的[更多优化方法](https://support.wix.com/en/article/site-performance-optimizing-your-media)。"}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "在网站信息中心的“`Custom Code`”标签页中[添加第三方代码](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site)时，请确保推迟加载或在代码正文的末尾加载该第三方代码。请尽可能使用 Wix 的[集成](https://support.wix.com/en/article/about-marketing-integrations)功能将营销工具嵌入到您的网站中。 "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix 利用 CDN 和缓存，为大多数访问者提供尽可能快速的响应。请考虑为网站[手动启用缓存](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed)，尤其是在使用 `Velo` 的情况下。"}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "请在网站信息中心的“`Custom Code`”标签页中查看您已向网站添加的所有第三方代码，并且仅保留对网站必要的服务。[了解详情](https://support.wix.com/en/article/site-performance-removing-unused-javascript)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "建议您将 GIF 上传到可让其作为 HTML5 视频嵌入的服务。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "建议您使用 [Performance Lab](https://wordpress.org/plugins/performance-lab/) 插件自动将您上传的 JPEG 图片转换为 WebP 格式（如果支持的话）。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "安装[延迟加载 WordPress 插件](https://wordpress.org/plugins/search/lazy+load/)以便能够推迟加载所有的屏幕外图片，或者改用可提供该功能的主题背景。另外，建议您使用 [AMP 插件](https://wordpress.org/plugins/amp/)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "有很多 WordPress 插件可帮助您[内嵌重要资源](https://wordpress.org/plugins/search/critical+css/)或[推迟加载不太重要的资源](https://wordpress.org/plugins/search/defer+css+javascript/)。请注意，这些插件提供的优化可能会导致您的主题背景或插件的功能中断，因此您可能需要更改代码。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "主题背景、插件和服务器规范都会影响服务器响应用时。建议您查找更优化的主题背景、仔细选择所需的优化插件并/或升级您的服务器。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "建议您在博文列表中显示摘录（例如，通过“更多”标签）、减少给定页面上显示的博文的数量、将您的长博文拆分成多个页面或者使用插件延迟加载评论。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "很多 [WordPress 插件](https://wordpress.org/plugins/search/minify+css/)都可通过连接、削减和压缩您的样式来加快您网站的加载速度。另外，您最好使用编译流程预先执行此缩减操作（如果可能的话）。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "很多 [WordPress 插件](https://wordpress.org/plugins/search/minify+javascript/)都可通过连接、削减和压缩您的脚本来加快您网站的加载速度。另外，您最好使用编译流程预先执行此缩减操作（如果可能的话）。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "建议您减少或改变会在您网页中加载未使用的 CSS 的 [WordPress 插件](https://wordpress.org/plugins/)的数量。若想找出会添加无关 CSS 的插件，请尝试在 Chrome DevTools 中运行[代码覆盖率](https://developer.chrome.com/docs/devtools/coverage/)测试。您可根据样式表网址找出导致问题的主题背景/插件。请留意在列表中包含多个以大量红色显示代码覆盖率的样式表的插件。插件应该只将网页中确实用到的样式表加入队列。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "建议您减少或改变会在您网页中加载未使用的 JavaScript 的 [WordPress 插件](https://wordpress.org/plugins/)的数量。若想找出会添加无关 JS 的插件，请尝试在 Chrome DevTools 中运行[代码覆盖率](https://developer.chrome.com/docs/devtools/coverage/)测试。您可根据脚本网址找出导致问题的主题背景/插件。请留意在列表中包含多个以大量红色显示代码覆盖率的脚本的插件。插件应只将网页中确实用到的脚本加入队列。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "了解 [WordPress 中的浏览器缓存](https://wordpress.org/support/article/optimization/#browser-caching)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "建议您使用[图片优化 WordPress 插件](https://wordpress.org/plugins/search/optimize+images/)，以在不影响图片质量的前提下压缩图片大小。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "直接通过[媒体库](https://wordpress.org/support/article/media-library-screen/)上传图片，以确保能够按要求的尺寸提供图片，然后从媒体库插入图片，或使用图片微件来确保采用最佳的图片尺寸（包括适用于自适应断点的尺寸）。避免使用 `Full Size` 图片，除非有足够的可用空间。[了解详情](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)。"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "您可以在网络服务器配置中启用文本压缩。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "若要将图片转换为 WebP 格式，请在“WP Rocket”中的“Image Optimization”（图片优化）标签页中启用“Imagify”。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "若要修正此建议，请在 WP Rocket 中启用[“LazyLoad”（延迟加载）](https://docs.wp-rocket.me/article/1141-lazyload-for-images)功能。此功能会延迟加载图片，直到访问者向下滚动网页并真正看到图片为止。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "若要解决此问题，请在“WP Rocket”中启用[“Remove Unused CSS”（移除未使用的 CSS）](https://docs.wp-rocket.me/article/1529-remove-unused-css)和[“Load JavaScript deferred”（加载延迟的 JavaScript）](https://docs.wp-rocket.me/article/1265-load-javascript-deferred)功能。这两项功能会分别优化 CSS 和 JavaScript 文件，使其不会阻止网页呈现。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "若要解决此问题，请在“WP Rocket”中启用[“Minify CSS files”（缩减 CSS 文件大小）](https://docs.wp-rocket.me/article/1350-css-minify-combine)功能，让系统移除网站 CSS 文件中的所有空格和注释，从而缩小文件并加快下载速度。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "若要解决此问题，请在“WP Rocket”中启用[“Minify JavaScript files”（缩减 JavaScript 文件大小）](https://docs.wp-rocket.me/article/1351-javascript-minify-combine)功能，让系统从 JavaScript 文件中移除空白区域和注释，从而缩小文件并加快下载速度。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "若要解决此问题，请在“WP Rocket”中启用[“Remove Unused CSS”（移除未使用的 CSS）](https://docs.wp-rocket.me/article/1529-remove-unused-css)功能，让系统移除所有未使用的 CSS 和样式表，同时仅保留每个网页的已使用 CSS，从而缩减网页大小。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "若要解决此问题，请在“WP Rocket”中启用[“Delay JavaScript execution”（延迟执行 JavaScript）](https://docs.wp-rocket.me/article/1349-delay-javascript-execution)功能，让系统等到用户开始互动时才执行脚本，从而提高网页的加载速度。如果网站包含 iframe，您也可以使用 WP Rocket 的[“LazyLoad for iframes and videos”（延迟加载 iframe 和视频）](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)和[“Replace YouTube iframe with preview image”（将 YouTube iframe 替换为预览图片）](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)功能。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "若要压缩图片，请在“WP Rocket”中的“Image Optimization”（图片优化）标签页中启用“Imagify”，然后运行“Bulk Optimization”（批量优化）。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "若要添加“dns-prefetch”并加快与外部网域的连接速度，请使用“WP Rocket”中的[“Prefetch DNS Requests”（预提取 DNS 请求）](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)功能。此外，“WP Rocket”还会自动将“preconnect”添加到 [Google Fonts 网域](https://docs.wp-rocket.me/article/1312-optimize-google-fonts)以及通过[“Enable CDN”（启用 CDN）](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)功能添加的所有 CNAME。"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "若要解决此字体问题，请在“WP Rocket”中启用[“Remove Unused CSS”（移除未使用的 CSS）](https://docs.wp-rocket.me/article/1529-remove-unused-css)，让系统优先加载您网站的重要字体。"}, "report/renderer/report-utils.js | calculatorLink": {"message": "查看计算器。"}, "report/renderer/report-utils.js | collapseView": {"message": "收起视图"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "初始导航"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "关键路径延迟时间上限："}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "复制 JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "开启/关闭深色主题"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "展开打印对话框"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "打印摘要"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "另存为 Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "另存为 HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "另存为 JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "查看原始跟踪记录"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "在查看器中打开"}, "report/renderer/report-utils.js | errorLabel": {"message": "出错了！"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "报告错误：没有任何审核信息"}, "report/renderer/report-utils.js | expandView": {"message": "展开视图"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "第一方"}, "report/renderer/report-utils.js | footerIssue": {"message": "提交问题"}, "report/renderer/report-utils.js | hide": {"message": "隐藏"}, "report/renderer/report-utils.js | labDataTitle": {"message": "实验室数据"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) 使用模拟的移动网络对当前页面进行的分析。这些值都是估算值，且可能会因时而异。"}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "待手动检查的其他项"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "不适用"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "在新标签页中打开"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "优化建议"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "有望节省的总时间（估算值）"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "已通过的审核"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "根据 [Chrome 更新后的可安装性标准](https://developer.chrome.com/blog/update-install-criteria)，Lighthouse 将在未来的版本中弃用 PWA 类别。请参阅[更新后的 PWA 文档](https://developer.chrome.com/docs/devtools/progressive-web-apps/)，了解今后的 PWA 测试。"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "初始网页加载"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "时间点快照"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "用户互动时间跨度"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "自定义节流"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "模拟桌面设备"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "模拟了 Moto G 电源"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "无模拟"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe 版本"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "未节流限制的 CPU/内存功耗"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU 节流"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "设备"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "网络节流"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "屏幕模拟"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "用户代理（网络）"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "单页会话"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "此数据是取自单页会话，现场数据则是汇总了许多次会话。"}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "低速 4G 节流"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "不明"}, "report/renderer/report-utils.js | show": {"message": "显示"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "显示与所选指标相关的评估结果："}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "收起代码段"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "展开代码段"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "显示第三方资源"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "由环境提供"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "此次 Lighthouse 运行并不顺利，原因如下："}, "report/renderer/report-utils.js | unattributable": {"message": "无法归因"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "这些都是估算值，且可能会因时而异。系统会直接基于这些指标来[计算性能得分](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)。"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "查看跟踪记录"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "查看树状图"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "已顺利通过审核，但有警告消息"}, "report/renderer/report-utils.js | warningHeader": {"message": "警告： "}, "treemap/app/src/util.js | allLabel": {"message": "全部"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "所有脚本"}, "treemap/app/src/util.js | coverageColumnName": {"message": "覆盖率"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "重复的模块"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "资源字节数"}, "treemap/app/src/util.js | tableColumnName": {"message": "名称"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "显示/隐藏表格"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "未使用的字节数"}}