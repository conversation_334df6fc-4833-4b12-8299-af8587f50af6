{"core/audits/accessibility/accesskeys.js | description": {"message": "Âćĉéŝś k̂éŷś l̂ét̂ úŝér̂ś q̂úîćk̂ĺŷ f́ôćûś â ṕâŕt̂ óf̂ t́ĥé p̂áĝé. F̂ór̂ ṕr̂óp̂ér̂ ńâv́îǵât́îón̂, éâćĥ áĉćêśŝ ḱêý m̂úŝt́ b̂é ûńîq́ûé. [L̂éâŕn̂ ḿôŕê áb̂óût́ âćĉéŝś k̂éŷś](https://dequeuniversity.com/rules/axe/4.9/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` v̂ál̂úêś âŕê ńôt́ ûńîq́ûé"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` v̂ál̂úêś âŕê ún̂íq̂úê"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Êáĉh́ ÂŔÎÁ `role` ŝúp̂ṕôŕt̂ś â śp̂éĉíf̂íĉ śûb́ŝét̂ óf̂ `aria-*` át̂t́r̂íb̂út̂éŝ. Ḿîśm̂át̂ćĥín̂ǵ t̂h́êśê ín̂v́âĺîd́ât́êś t̂h́ê `aria-*` át̂t́r̂íb̂út̂éŝ. [Ĺêár̂ń ĥóŵ t́ô ḿât́ĉh́ ÂŔÎÁ ât́t̂ŕîb́ût́êś t̂ó t̂h́êír̂ ŕôĺêś](https://dequeuniversity.com/rules/axe/4.9/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ ḿât́ĉh́ t̂h́êír̂ ŕôĺêś"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś m̂át̂ćĥ t́ĥéîŕ r̂ól̂éŝ"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "ÂŔÎÁ `role`ŝ én̂áb̂ĺê áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś t̂ó k̂ńôẃ t̂h́ê ŕôĺê óf̂ éâćĥ él̂ém̂én̂t́ ôń t̂h́ê ẃêb́ p̂áĝé. Îf́ t̂h́ê `role` v́âĺûéŝ ár̂é m̂íŝśp̂él̂ĺêd́, n̂ót̂ éx̂íŝt́îńĝ ÁR̂ÍÂ `role` v́âĺûéŝ, ór̂ áb̂śt̂ŕâćt̂ ŕôĺêś, t̂h́êń t̂h́ê ṕûŕp̂óŝé ôf́ t̂h́ê él̂ém̂én̂t́ ŵíl̂ĺ n̂ót̂ b́ê ćôḿm̂ún̂íĉát̂éd̂ t́ô úŝér̂ś ôf́ âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ÁR̂ÍÂ ŕôĺêś](https://dequeuniversity.com/rules/axe/4.9/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "V̂ál̂úêś âśŝíĝńêd́ t̂ó `role=\"\"` âŕê ńôt́ v̂ál̂íd̂ ÁR̂ÍÂ ŕôĺêś."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "V̂ál̂úêś âśŝíĝńêd́ t̂ó `role=\"\"` âŕê v́âĺîd́ ÂŔÎÁ r̂ól̂éŝ."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Ŵh́êń âń êĺêḿêńt̂ d́ôéŝń't̂ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê, śĉŕêén̂ ŕêád̂ér̂ś âńn̂óûńĉé ît́ ŵít̂h́ â ǵêńêŕîć n̂ám̂é, m̂ák̂ín̂ǵ ît́ ûńûśâb́l̂é f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ h́ôẃ t̂ó m̂ák̂é ĉóm̂ḿâńd̂ él̂ém̂én̂t́ŝ ḿôŕê áĉćêśŝíb̂ĺê](https://dequeuniversity.com/rules/axe/4.9/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "`button`, `link`, âńd̂ `menuitem` él̂ém̂én̂t́ŝ d́ô ńôt́ ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "`button`, `link`, âńd̂ `menuitem` él̂ém̂én̂t́ŝ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "ÂŔÎÁ d̂íâĺôǵ êĺêḿêńt̂ś ŵít̂h́ôút̂ áĉćêśŝíb̂ĺê ńâḿêś m̂áŷ ṕr̂év̂én̂t́ ŝćr̂éêń r̂éâd́êŕŝ úŝér̂ś f̂ŕôḿ d̂íŝćêŕn̂ín̂ǵ t̂h́ê ṕûŕp̂óŝé ôf́ t̂h́êśê él̂ém̂én̂t́ŝ. [Ĺêár̂ń ĥóŵ t́ô ḿâḱê ÁR̂ÍÂ d́îál̂óĝ él̂ém̂én̂t́ŝ ḿôŕê áĉćêśŝíb̂ĺê](https://dequeuniversity.com/rules/axe/4.9/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Êĺêḿêńt̂ś ŵít̂h́ `role=\"dialog\"` ôŕ `role=\"alertdialog\"` d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Êĺêḿêńt̂ś ŵít̂h́ `role=\"dialog\"` ôŕ `role=\"alertdialog\"` ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś, ŵór̂ḱ îńĉón̂śîśt̂én̂t́l̂ý ŵh́êń `aria-hidden=\"true\"` îś ŝét̂ ón̂ t́ĥé d̂óĉúm̂én̂t́ `<body>`. [L̂éâŕn̂ h́ôẃ `aria-hidden` âf́f̂éĉt́ŝ t́ĥé d̂óĉúm̂én̂t́ b̂ód̂ý](https://dequeuniversity.com/rules/axe/4.9/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` îś p̂ŕêśêńt̂ ón̂ t́ĥé d̂óĉúm̂én̂t́ `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` îś n̂ót̂ ṕr̂éŝén̂t́ ôń t̂h́ê d́ôćûḿêńt̂ `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "F̂óĉúŝáb̂ĺê d́êśĉén̂d́êńt̂ś ŵít̂h́îń âń `[aria-hidden=\"true\"]` êĺêḿêńt̂ ṕr̂év̂én̂t́ t̂h́ôśê ín̂t́êŕâćt̂ív̂é êĺêḿêńt̂ś f̂ŕôḿ b̂éîńĝ áv̂áîĺâb́l̂é t̂ó ûśêŕŝ óf̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś l̂ík̂é ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń ĥóŵ `aria-hidden` áf̂f́êćt̂ś f̂óĉúŝáb̂ĺê él̂ém̂én̂t́ŝ](https://dequeuniversity.com/rules/axe/4.9/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` êĺêḿêńt̂ś ĉón̂t́âín̂ f́ôćûśâb́l̂é d̂éŝćêńd̂én̂t́ŝ"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]` êĺêḿêńt̂ś d̂ó n̂ót̂ ćôńt̂áîń f̂óĉúŝáb̂ĺê d́êśĉén̂d́êńt̂ś"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Ŵh́êń âń îńp̂út̂ f́îél̂d́ d̂óêśn̂'t́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é, ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê ít̂ ẃît́ĥ á ĝén̂ér̂íĉ ńâḿê, ḿâḱîńĝ ít̂ ún̂úŝáb̂ĺê f́ôŕ ûśêŕŝ ẃĥó r̂él̂ý ôń ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ín̂ṕût́ f̂íêĺd̂ ĺâb́êĺŝ](https://dequeuniversity.com/rules/axe/4.9/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ÂŔÎÁ îńp̂út̂ f́îél̂d́ŝ d́ô ńôt́ ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ÂŔÎÁ îńp̂út̂ f́îél̂d́ŝ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Ŵh́êń â ḿêt́êŕ êĺêḿêńt̂ d́ôéŝń't̂ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê, śĉŕêén̂ ŕêád̂ér̂ś âńn̂óûńĉé ît́ ŵít̂h́ â ǵêńêŕîć n̂ám̂é, m̂ák̂ín̂ǵ ît́ ûńûśâb́l̂é f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ h́ôẃ t̂ó n̂ám̂é `meter` êĺêḿêńt̂ś](https://dequeuniversity.com/rules/axe/4.9/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ÂŔÎÁ `meter` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ÂŔÎÁ `meter` êĺêḿêńt̂ś ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Ŵh́êń â `progressbar` él̂ém̂én̂t́ d̂óêśn̂'t́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é, ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê ít̂ ẃît́ĥ á ĝén̂ér̂íĉ ńâḿê, ḿâḱîńĝ ít̂ ún̂úŝáb̂ĺê f́ôŕ ûśêŕŝ ẃĥó r̂él̂ý ôń ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń ĥóŵ t́ô ĺâb́êĺ `progressbar` êĺêḿêńt̂ś](https://dequeuniversity.com/rules/axe/4.9/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ÂŔÎÁ `progressbar` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ÂŔÎÁ `progressbar` êĺêḿêńt̂ś ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Ŝóm̂é ÂŔÎÁ r̂ól̂éŝ h́âv́ê ŕêq́ûír̂éd̂ át̂t́r̂íb̂út̂éŝ t́ĥát̂ d́êśĉŕîb́ê t́ĥé ŝt́ât́ê óf̂ t́ĥé êĺêḿêńt̂ t́ô śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ r̂ól̂éŝ án̂d́ r̂éq̂úîŕêd́ ât́t̂ŕîb́ût́êś](https://dequeuniversity.com/rules/axe/4.9/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`ŝ d́ô ńôt́ ĥáv̂é âĺl̂ ŕêq́ûír̂éd̂ `[aria-*]` át̂t́r̂íb̂út̂éŝ"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`ŝ h́âv́ê ál̂ĺ r̂éq̂úîŕêd́ `[aria-*]` ât́t̂ŕîb́ût́êś"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Ŝóm̂é ÂŔÎÁ p̂ár̂én̂t́ r̂ól̂éŝ ḿûśt̂ ćôńt̂áîń ŝṕêćîf́îć ĉh́îĺd̂ ŕôĺêś t̂ó p̂ér̂f́ôŕm̂ t́ĥéîŕ îńt̂én̂d́êd́ âćĉéŝśîb́îĺît́ŷ f́ûńĉt́îón̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ r̂ól̂éŝ án̂d́ r̂éq̂úîŕêd́ ĉh́îĺd̂ŕêń êĺêḿêńt̂ś](https://dequeuniversity.com/rules/axe/4.9/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Êĺêḿêńt̂ś ŵít̂h́ âń ÂŔÎÁ `[role]` t̂h́ât́ r̂éq̂úîŕê ćĥíl̂d́r̂én̂ t́ô ćôńt̂áîń â śp̂éĉíf̂íĉ `[role]` ár̂é m̂íŝśîńĝ śôḿê ór̂ ál̂ĺ ôf́ t̂h́ôśê ŕêq́ûír̂éd̂ ćĥíl̂d́r̂én̂."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Êĺêḿêńt̂ś ŵít̂h́ âń ÂŔÎÁ `[role]` t̂h́ât́ r̂éq̂úîŕê ćĥíl̂d́r̂én̂ t́ô ćôńt̂áîń â śp̂éĉíf̂íĉ `[role]` h́âv́ê ál̂ĺ r̂éq̂úîŕêd́ ĉh́îĺd̂ŕêń."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Ŝóm̂é ÂŔÎÁ ĉh́îĺd̂ ŕôĺêś m̂úŝt́ b̂é ĉón̂t́âín̂éd̂ b́ŷ śp̂éĉíf̂íĉ ṕâŕêńt̂ ŕôĺêś t̂ó p̂ŕôṕêŕl̂ý p̂ér̂f́ôŕm̂ t́ĥéîŕ îńt̂én̂d́êd́ âćĉéŝśîb́îĺît́ŷ f́ûńĉt́îón̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ÂŔÎÁ r̂ól̂éŝ án̂d́ r̂éq̂úîŕêd́ p̂ár̂én̂t́ êĺêḿêńt̂](https://dequeuniversity.com/rules/axe/4.9/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`ŝ ár̂é n̂ót̂ ćôńt̂áîńêd́ b̂ý t̂h́êír̂ ŕêq́ûír̂éd̂ ṕâŕêńt̂ él̂ém̂én̂t́"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`ŝ ár̂é ĉón̂t́âín̂éd̂ b́ŷ t́ĥéîŕ r̂éq̂úîŕêd́ p̂ár̂én̂t́ êĺêḿêńt̂"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ÂŔÎÁ r̂ól̂éŝ ḿûśt̂ h́âv́ê v́âĺîd́ v̂ál̂úêś îń ôŕd̂ér̂ t́ô ṕêŕf̂ór̂ḿ t̂h́êír̂ ín̂t́êńd̂éd̂ áĉćêśŝíb̂íl̂ít̂ý f̂ún̂ćt̂íôńŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ v́âĺîd́ ÂŔÎÁ r̂ól̂éŝ](https://dequeuniversity.com/rules/axe/4.9/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` v̂ál̂úêś âŕê ńôt́ v̂ál̂íd̂"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` v̂ál̂úêś âŕê v́âĺîd́"}, "core/audits/accessibility/aria-text.js | description": {"message": "Âd́d̂ín̂ǵ `role=text` âŕôún̂d́ â t́êx́t̂ ńôd́ê śp̂ĺît́ b̂ý m̂ár̂ḱûṕ êńâb́l̂éŝ V́ôíĉéÔv́êŕ t̂ó t̂ŕêát̂ ít̂ áŝ ón̂é p̂h́r̂áŝé, b̂út̂ t́ĥé êĺêḿêńt̂'ś f̂óĉúŝáb̂ĺê d́êśĉén̂d́êńt̂ś ŵíl̂ĺ n̂ót̂ b́ê án̂ńôún̂ćêd́. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê `role=text` át̂t́r̂íb̂út̂é](https://dequeuniversity.com/rules/axe/4.9/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Êĺêḿêńt̂ś ŵít̂h́ t̂h́ê `role=text` át̂t́r̂íb̂út̂é d̂ó ĥáv̂é f̂óĉúŝáb̂ĺê d́êśĉén̂d́êńt̂ś."}, "core/audits/accessibility/aria-text.js | title": {"message": "Êĺêḿêńt̂ś ŵít̂h́ t̂h́ê `role=text` át̂t́r̂íb̂út̂é d̂ó n̂ót̂ h́âv́ê f́ôćûśâb́l̂é d̂éŝćêńd̂én̂t́ŝ."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Ŵh́êń â t́ôǵĝĺê f́îél̂d́ d̂óêśn̂'t́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é, ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê ít̂ ẃît́ĥ á ĝén̂ér̂íĉ ńâḿê, ḿâḱîńĝ ít̂ ún̂úŝáb̂ĺê f́ôŕ ûśêŕŝ ẃĥó r̂él̂ý ôń ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ôǵĝĺê f́îél̂d́ŝ](https://dequeuniversity.com/rules/axe/4.9/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ÂŔÎÁ t̂óĝǵl̂é f̂íêĺd̂ś d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ÂŔÎÁ t̂óĝǵl̂é f̂íêĺd̂ś ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Ŵh́êń â t́ôól̂t́îṕ êĺêḿêńt̂ d́ôéŝń't̂ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê, śĉŕêén̂ ŕêád̂ér̂ś âńn̂óûńĉé ît́ ŵít̂h́ â ǵêńêŕîć n̂ám̂é, m̂ák̂ín̂ǵ ît́ ûńûśâb́l̂é f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ h́ôẃ t̂ó n̂ám̂é `tooltip` êĺêḿêńt̂ś](https://dequeuniversity.com/rules/axe/4.9/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ÂŔÎÁ `tooltip` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ÂŔÎÁ `tooltip` êĺêḿêńt̂ś ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Ŵh́êń â `treeitem` él̂ém̂én̂t́ d̂óêśn̂'t́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é, ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê ít̂ ẃît́ĥ á ĝén̂ér̂íĉ ńâḿê, ḿâḱîńĝ ít̂ ún̂úŝáb̂ĺê f́ôŕ ûśêŕŝ ẃĥó r̂él̂ý ôń ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ĺâb́êĺîńĝ `treeitem` él̂ém̂én̂t́ŝ](https://dequeuniversity.com/rules/axe/4.9/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ÂŔÎÁ `treeitem` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ÂŔÎÁ `treeitem` êĺêḿêńt̂ś ĥáv̂é âćĉéŝśîb́l̂é n̂ám̂éŝ"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś, ĉán̂'t́ îńt̂ér̂ṕr̂ét̂ ÁR̂ÍÂ át̂t́r̂íb̂út̂éŝ ẃît́ĥ ín̂v́âĺîd́ v̂ál̂úêś. [L̂éâŕn̂ ḿôŕê áb̂óût́ v̂ál̂íd̂ v́âĺûéŝ f́ôŕ ÂŔÎÁ ât́t̂ŕîb́ût́êś](https://dequeuniversity.com/rules/axe/4.9/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ h́âv́ê v́âĺîd́ v̂ál̂úêś"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś ĥáv̂é v̂ál̂íd̂ v́âĺûéŝ"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś, ĉán̂'t́ îńt̂ér̂ṕr̂ét̂ ÁR̂ÍÂ át̂t́r̂íb̂út̂éŝ ẃît́ĥ ín̂v́âĺîd́ n̂ám̂éŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ v́âĺîd́ ÂŔÎÁ ât́t̂ŕîb́ût́êś](https://dequeuniversity.com/rules/axe/4.9/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś âŕê ńôt́ v̂ál̂íd̂ ór̂ ḿîśŝṕêĺl̂éd̂"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś âŕê v́âĺîd́ âńd̂ ńôt́ m̂íŝśp̂él̂ĺêd́"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "F̂áîĺîńĝ Él̂ém̂én̂t́ŝ"}, "core/audits/accessibility/button-name.js | description": {"message": "Ŵh́êń â b́ût́t̂ón̂ d́ôéŝń't̂ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê, śĉŕêén̂ ŕêád̂ér̂ś âńn̂óûńĉé ît́ âś \"b̂út̂t́ôń\", m̂ák̂ín̂ǵ ît́ ûńûśâb́l̂é f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ h́ôẃ t̂ó m̂ák̂é b̂út̂t́ôńŝ ḿôŕê áĉćêśŝíb̂ĺê](https://dequeuniversity.com/rules/axe/4.9/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "B̂út̂t́ôńŝ d́ô ńôt́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é"}, "core/audits/accessibility/button-name.js | title": {"message": "B̂út̂t́ôńŝ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê"}, "core/audits/accessibility/bypass.js | description": {"message": "Âd́d̂ín̂ǵ ŵáŷś t̂ó b̂ýp̂áŝś r̂ép̂ét̂ít̂ív̂é ĉón̂t́êńt̂ ĺêt́ŝ ḱêýb̂óâŕd̂ úŝér̂ś n̂áv̂íĝát̂é t̂h́ê ṕâǵê ḿôŕê éf̂f́îćîén̂t́l̂ý. [L̂éâŕn̂ ḿôŕê áb̂óût́ b̂ýp̂áŝś b̂ĺôćk̂ś](https://dequeuniversity.com/rules/axe/4.9/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "T̂h́ê ṕâǵê d́ôéŝ ńôt́ ĉón̂t́âín̂ á ĥéâd́îńĝ, śk̂íp̂ ĺîńk̂, ór̂ ĺâńd̂ḿâŕk̂ ŕêǵîón̂"}, "core/audits/accessibility/bypass.js | title": {"message": "T̂h́ê ṕâǵê ćôńt̂áîńŝ á ĥéâd́îńĝ, śk̂íp̂ ĺîńk̂, ór̂ ĺâńd̂ḿâŕk̂ ŕêǵîón̂"}, "core/audits/accessibility/color-contrast.js | description": {"message": "L̂óŵ-ćôńt̂ŕâśt̂ t́êx́t̂ íŝ d́îf́f̂íĉúl̂t́ ôŕ îḿp̂óŝśîb́l̂é f̂ór̂ ḿâńŷ úŝér̂ś t̂ó r̂éâd́. [L̂éâŕn̂ h́ôẃ t̂ó p̂ŕôv́îd́ê śûf́f̂íĉíêńt̂ ćôĺôŕ ĉón̂t́r̂áŝt́](https://dequeuniversity.com/rules/axe/4.9/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "B̂áĉḱĝŕôún̂d́ âńd̂ f́ôŕêǵr̂óûńd̂ ćôĺôŕŝ d́ô ńôt́ ĥáv̂é â śûf́f̂íĉíêńt̂ ćôńt̂ŕâśt̂ ŕât́îó."}, "core/audits/accessibility/color-contrast.js | title": {"message": "B̂áĉḱĝŕôún̂d́ âńd̂ f́ôŕêǵr̂óûńd̂ ćôĺôŕŝ h́âv́ê á ŝúf̂f́îćîén̂t́ ĉón̂t́r̂áŝt́ r̂át̂íô"}, "core/audits/accessibility/definition-list.js | description": {"message": "Ŵh́êń d̂éf̂ín̂ít̂íôń l̂íŝt́ŝ ár̂é n̂ót̂ ṕr̂óp̂ér̂ĺŷ ḿâŕk̂éd̂ úp̂, śĉŕêén̂ ŕêád̂ér̂ś m̂áŷ ṕr̂ód̂úĉé ĉón̂f́ûśîńĝ ór̂ ín̂áĉćûŕât́ê óût́p̂út̂. [Ĺêár̂ń ĥóŵ t́ô śt̂ŕûćt̂úr̂é d̂éf̂ín̂ít̂íôń l̂íŝt́ŝ ćôŕr̂éĉt́l̂ý](https://dequeuniversity.com/rules/axe/4.9/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`'ŝ d́ô ńôt́ ĉón̂t́âín̂ ón̂ĺŷ ṕr̂óp̂ér̂ĺŷ-ór̂d́êŕêd́ `<dt>` âńd̂ `<dd>` ǵr̂óûṕŝ, `<script>`, `<template>` ór̂ `<div>` él̂ém̂én̂t́ŝ."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`'ŝ ćôńt̂áîń ôńl̂ý p̂ŕôṕêŕl̂ý-ôŕd̂ér̂éd̂ `<dt>` án̂d́ `<dd>` ĝŕôúp̂ś, `<script>`, `<template>` ôŕ `<div>` êĺêḿêńt̂ś."}, "core/audits/accessibility/dlitem.js | description": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ (`<dt>` án̂d́ `<dd>`) m̂úŝt́ b̂é ŵŕâṕp̂éd̂ ín̂ á p̂ár̂én̂t́ `<dl>` êĺêḿêńt̂ t́ô én̂śûŕê t́ĥát̂ śĉŕêén̂ ŕêád̂ér̂ś ĉán̂ ṕr̂óp̂ér̂ĺŷ án̂ńôún̂ćê t́ĥém̂. [Ĺêár̂ń ĥóŵ t́ô śt̂ŕûćt̂úr̂é d̂éf̂ín̂ít̂íôń l̂íŝt́ŝ ćôŕr̂éĉt́l̂ý](https://dequeuniversity.com/rules/axe/4.9/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ ár̂é n̂ót̂ ẃr̂áp̂ṕêd́ îń `<dl>` êĺêḿêńt̂ś"}, "core/audits/accessibility/dlitem.js | title": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ ár̂é ŵŕâṕp̂éd̂ ín̂ `<dl>` él̂ém̂én̂t́ŝ"}, "core/audits/accessibility/document-title.js | description": {"message": "T̂h́ê t́ît́l̂é ĝív̂éŝ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś âń ôv́êŕv̂íêẃ ôf́ t̂h́ê ṕâǵê, án̂d́ ŝéâŕĉh́ êńĝín̂é ûśêŕŝ ŕêĺŷ ón̂ ít̂ h́êáv̂íl̂ý t̂ó d̂ét̂ér̂ḿîńê íf̂ á p̂áĝé îś r̂él̂év̂án̂t́ t̂ó t̂h́êír̂ śêár̂ćĥ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ d́ôćûḿêńt̂ t́ît́l̂éŝ](https://dequeuniversity.com/rules/axe/4.9/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ĥáv̂é â `<title>` él̂ém̂én̂t́"}, "core/audits/accessibility/document-title.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á `<title>` êĺêḿêńt̂"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Âĺl̂ f́ôćûśâb́l̂é êĺêḿêńt̂ś m̂úŝt́ ĥáv̂é â ún̂íq̂úê `id` t́ô én̂śûŕê t́ĥát̂ t́ĥéŷ'ŕê v́îśîb́l̂é t̂ó âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ. [Ĺêár̂ń ĥóŵ t́ô f́îx́ d̂úp̂ĺîćât́ê `id`ś](https://dequeuniversity.com/rules/axe/4.9/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]` ât́t̂ŕîb́ût́êś ôń âćt̂ív̂é, f̂óĉúŝáb̂ĺê él̂ém̂én̂t́ŝ ár̂é n̂ót̂ ún̂íq̂úê"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]` ât́t̂ŕîb́ût́êś ôń âćt̂ív̂é, f̂óĉúŝáb̂ĺê él̂ém̂én̂t́ŝ ár̂é ûńîq́ûé"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "T̂h́ê v́âĺûé ôf́ âń ÂŔÎÁ ÎD́ m̂úŝt́ b̂é ûńîq́ûé t̂ó p̂ŕêv́êńt̂ ót̂h́êŕ îńŝt́âńĉéŝ f́r̂óm̂ b́êín̂ǵ ôv́êŕl̂óôḱêd́ b̂ý âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ. [Ĺêár̂ń ĥóŵ t́ô f́îx́ d̂úp̂ĺîćât́ê ÁR̂ÍÂ ÍD̂ś](https://dequeuniversity.com/rules/axe/4.9/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ÂŔÎÁ ÎD́ŝ ár̂é n̂ót̂ ún̂íq̂úê"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ÂŔÎÁ ÎD́ŝ ár̂é ûńîq́ûé"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Â h́êád̂ín̂ǵ ŵít̂h́ n̂ó ĉón̂t́êńt̂ ór̂ ín̂áĉćêśŝíb̂ĺê t́êx́t̂ ṕr̂év̂én̂t́ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ f́r̂óm̂ áĉćêśŝín̂ǵ îńf̂ór̂ḿât́îón̂ ón̂ t́ĥé p̂áĝé'ŝ śt̂ŕûćt̂úr̂é. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĥéâd́îńĝś](https://dequeuniversity.com/rules/axe/4.9/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Ĥéâd́îńĝ él̂ém̂én̂t́ŝ d́ô ńôt́ ĉón̂t́âín̂ ćôńt̂én̂t́."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Âĺl̂ h́êád̂ín̂ǵ êĺêḿêńt̂ś ĉón̂t́âín̂ ćôńt̂én̂t́."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "F̂ór̂ḿ f̂íêĺd̂ś ŵít̂h́ m̂úl̂t́îṕl̂é l̂áb̂él̂ś ĉán̂ b́ê ćôńf̂úŝín̂ǵl̂ý âńn̂óûńĉéd̂ b́ŷ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś l̂ík̂é ŝćr̂éêń r̂éâd́êŕŝ ẃĥíĉh́ ûśê éît́ĥér̂ t́ĥé f̂ír̂śt̂, t́ĥé l̂áŝt́, ôŕ âĺl̂ óf̂ t́ĥé l̂áb̂él̂ś. [L̂éâŕn̂ h́ôẃ t̂ó ûśê f́ôŕm̂ ĺâb́êĺŝ](https://dequeuniversity.com/rules/axe/4.9/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "F̂ór̂ḿ f̂íêĺd̂ś ĥáv̂é m̂úl̂t́îṕl̂é l̂áb̂él̂ś"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "N̂ó f̂ór̂ḿ f̂íêĺd̂ś ĥáv̂é m̂úl̂t́îṕl̂é l̂áb̂él̂ś"}, "core/audits/accessibility/frame-title.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ ŕêĺŷ ón̂ f́r̂ám̂é t̂ít̂ĺêś t̂ó d̂éŝćr̂íb̂é t̂h́ê ćôńt̂én̂t́ŝ óf̂ f́r̂ám̂éŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ f́r̂ám̂é t̂ít̂ĺêś](https://dequeuniversity.com/rules/axe/4.9/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` ôŕ `<iframe>` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê á t̂ít̂ĺê"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` ôŕ `<iframe>` êĺêḿêńt̂ś ĥáv̂é â t́ît́l̂é"}, "core/audits/accessibility/heading-order.js | description": {"message": "P̂ŕôṕêŕl̂ý ôŕd̂ér̂éd̂ h́êád̂ín̂ǵŝ t́ĥát̂ d́ô ńôt́ ŝḱîṕ l̂év̂él̂ś ĉón̂v́êý t̂h́ê śêḿâńt̂íĉ śt̂ŕûćt̂úr̂é ôf́ t̂h́ê ṕâǵê, ḿâḱîńĝ ít̂ éâśîér̂ t́ô ńâv́îǵât́ê án̂d́ ûńd̂ér̂śt̂án̂d́ ŵh́êń ûśîńĝ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĥéâd́îńĝ ór̂d́êŕ](https://dequeuniversity.com/rules/axe/4.9/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Ĥéâd́îńĝ él̂ém̂én̂t́ŝ ár̂é n̂ót̂ ín̂ á ŝéq̂úêńt̂íâĺl̂ý-d̂éŝćêńd̂ín̂ǵ ôŕd̂ér̂"}, "core/audits/accessibility/heading-order.js | title": {"message": "Ĥéâd́îńĝ él̂ém̂én̂t́ŝ áp̂ṕêár̂ ín̂ á ŝéq̂úêńt̂íâĺl̂ý-d̂éŝćêńd̂ín̂ǵ ôŕd̂ér̂"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Îf́ â ṕâǵê d́ôéŝń't̂ śp̂éĉíf̂ý â `lang` át̂t́r̂íb̂út̂é, â śĉŕêén̂ ŕêád̂ér̂ áŝśûḿêś t̂h́ât́ t̂h́ê ṕâǵê íŝ ín̂ t́ĥé d̂éf̂áûĺt̂ ĺâńĝúâǵê t́ĥát̂ t́ĥé ûśêŕ ĉh́ôśê ẃĥén̂ śêt́t̂ín̂ǵ ûṕ t̂h́ê śĉŕêén̂ ŕêád̂ér̂. Íf̂ t́ĥé p̂áĝé îśn̂'t́ âćt̂úâĺl̂ý îń t̂h́ê d́êf́âúl̂t́ l̂án̂ǵûáĝé, t̂h́êń t̂h́ê śĉŕêén̂ ŕêád̂ér̂ ḿîǵĥt́ n̂ót̂ án̂ńôún̂ćê t́ĥé p̂áĝé'ŝ t́êx́t̂ ćôŕr̂éĉt́l̂ý. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê `lang` át̂t́r̂íb̂út̂é](https://dequeuniversity.com/rules/axe/4.9/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` êĺêḿêńt̂ d́ôéŝ ńôt́ ĥáv̂é â `[lang]` át̂t́r̂íb̂út̂é"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` êĺêḿêńt̂ h́âś â `[lang]` át̂t́r̂íb̂út̂é"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â v́âĺîd́ [B̂ĆP̂ 47 ĺâńĝúâǵê](https://www.w3.org/International/questions/qa-choosing-language-tags#question) h́êĺp̂ś ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê t́êx́t̂ ṕr̂óp̂ér̂ĺŷ. [Ĺêár̂ń ĥóŵ t́ô úŝé t̂h́ê `lang` át̂t́r̂íb̂út̂é](https://dequeuniversity.com/rules/axe/4.9/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` êĺêḿêńt̂ d́ôéŝ ńôt́ ĥáv̂é â v́âĺîd́ v̂ál̂úê f́ôŕ ît́ŝ `[lang]` át̂t́r̂íb̂út̂é."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` êĺêḿêńt̂ h́âś â v́âĺîd́ v̂ál̂úê f́ôŕ ît́ŝ `[lang]` át̂t́r̂íb̂út̂é"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Îf́ t̂h́ê ẃêb́p̂áĝé d̂óêś n̂ót̂ śp̂éĉíf̂ý â ćôńŝíŝt́êńt̂ ĺâńĝúâǵê, t́ĥén̂ t́ĥé ŝćr̂éêń r̂éâd́êŕ m̂íĝh́t̂ ńôt́ âńn̂óûńĉé t̂h́ê ṕâǵê'ś t̂éx̂t́ ĉór̂ŕêćt̂ĺŷ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé `lang` ât́t̂ŕîb́ût́ê](https://dequeuniversity.com/rules/axe/4.9/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "`<html>` êĺêḿêńt̂ d́ôéŝ ńôt́ ĥáv̂é âń `[xml:lang]` ât́t̂ŕîb́ût́ê ẃît́ĥ t́ĥé ŝám̂é b̂áŝé l̂án̂ǵûáĝé âś t̂h́ê `[lang]` át̂t́r̂íb̂út̂é."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "`<html>` êĺêḿêńt̂ h́âś âń `[xml:lang]` ât́t̂ŕîb́ût́ê ẃît́ĥ t́ĥé ŝám̂é b̂áŝé l̂án̂ǵûáĝé âś t̂h́ê `[lang]` át̂t́r̂íb̂út̂é."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "L̂ín̂ḱŝ ẃît́ĥ t́ĥé ŝám̂é d̂éŝt́îńât́îón̂ śĥóûĺd̂ h́âv́ê t́ĥé ŝám̂é d̂éŝćr̂íp̂t́îón̂, t́ô h́êĺp̂ úŝér̂ś ûńd̂ér̂śt̂án̂d́ t̂h́ê ĺîńk̂'ś p̂úr̂ṕôśê án̂d́ d̂éĉíd̂é ŵh́êt́ĥér̂ t́ô f́ôĺl̂óŵ ít̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ íd̂én̂t́îćâĺ l̂ín̂ḱŝ](https://dequeuniversity.com/rules/axe/4.9/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Îd́êńt̂íĉál̂ ĺîńk̂ś d̂ó n̂ót̂ h́âv́ê t́ĥé ŝám̂é p̂úr̂ṕôśê."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Îd́êńt̂íĉál̂ ĺîńk̂ś ĥáv̂é t̂h́ê śâḿê ṕûŕp̂óŝé."}, "core/audits/accessibility/image-alt.js | description": {"message": "Îńf̂ór̂ḿât́îv́ê él̂ém̂én̂t́ŝ śĥóûĺd̂ áîḿ f̂ór̂ śĥór̂t́, d̂éŝćr̂íp̂t́îv́ê ál̂t́êŕn̂át̂é t̂éx̂t́. D̂éĉór̂át̂ív̂é êĺêḿêńt̂ś ĉán̂ b́ê íĝńôŕêd́ ŵít̂h́ âń êḿp̂t́ŷ ál̂t́ ât́t̂ŕîb́ût́ê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé `alt` ât́t̂ŕîb́ût́ê](https://dequeuniversity.com/rules/axe/4.9/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ d́ô ńôt́ ĥáv̂é `[alt]` ât́t̂ŕîb́ût́êś"}, "core/audits/accessibility/image-alt.js | title": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ h́âv́ê `[alt]` át̂t́r̂íb̂út̂éŝ"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Îńf̂ór̂ḿât́îv́ê él̂ém̂én̂t́ŝ śĥóûĺd̂ áîḿ f̂ór̂ śĥór̂t́, d̂éŝćr̂íp̂t́îv́ê ál̂t́êŕn̂át̂ív̂é t̂éx̂t́. Âĺt̂ér̂ńât́îv́ê t́êx́t̂ t́ĥát̂ íŝ éx̂áĉt́l̂ý t̂h́ê śâḿê áŝ t́ĥé t̂éx̂t́ âd́ĵáĉén̂t́ t̂ó t̂h́ê ĺîńk̂ ór̂ ím̂áĝé îś p̂ót̂én̂t́îál̂ĺŷ ćôńf̂úŝín̂ǵ f̂ór̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś, b̂éĉáûśê t́ĥé t̂éx̂t́ ŵíl̂ĺ b̂é r̂éâd́ t̂ẃîćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé `alt` ât́t̂ŕîb́ût́ê](https://dequeuniversity.com/rules/axe/4.9/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ h́âv́ê `[alt]` át̂t́r̂íb̂út̂éŝ t́ĥát̂ ár̂é r̂éd̂ún̂d́âńt̂ t́êx́t̂."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ d́ô ńôt́ ĥáv̂é `[alt]` ât́t̂ŕîb́ût́êś t̂h́ât́ âŕê ŕêd́ûńd̂án̂t́ t̂éx̂t́."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Âd́d̂ín̂ǵ d̂íŝćêŕn̂áb̂ĺê án̂d́ âćĉéŝśîb́l̂é t̂éx̂t́ t̂ó îńp̂út̂ b́ût́t̂ón̂ś m̂áŷ h́êĺp̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś ûńd̂ér̂śt̂án̂d́ t̂h́ê ṕûŕp̂óŝé ôf́ t̂h́ê ín̂ṕût́ b̂út̂t́ôń. [L̂éâŕn̂ ḿôŕê áb̂óût́ îńp̂út̂ b́ût́t̂ón̂ś](https://dequeuniversity.com/rules/axe/4.9/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Îńp̂út̂ b́ût́t̂ón̂ś d̂ó n̂ót̂ h́âv́ê d́îśĉér̂ńîb́l̂é t̂éx̂t́."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Îńp̂út̂ b́ût́t̂ón̂ś ĥáv̂é d̂íŝćêŕn̂íb̂ĺê t́êx́t̂."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Ŵh́êń âń îḿâǵê íŝ b́êín̂ǵ ûśêd́ âś âń `<input>` b̂út̂t́ôń, p̂ŕôv́îd́îńĝ ál̂t́êŕn̂át̂ív̂é t̂éx̂t́ ĉán̂ h́êĺp̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś ûńd̂ér̂śt̂án̂d́ t̂h́ê ṕûŕp̂óŝé ôf́ t̂h́ê b́ût́t̂ón̂. [Ĺêár̂ń âb́ôút̂ ín̂ṕût́ îḿâǵê ál̂t́ t̂éx̂t́](https://dequeuniversity.com/rules/axe/4.9/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê `[alt]` t́êx́t̂"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` êĺêḿêńt̂ś ĥáv̂é `[alt]` t̂éx̂t́"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "V̂íŝíb̂ĺê t́êx́t̂ ĺâb́êĺŝ t́ĥát̂ d́ô ńôt́ m̂át̂ćĥ t́ĥé âćĉéŝśîb́l̂é n̂ám̂é ĉán̂ ŕêśûĺt̂ ín̂ á ĉón̂f́ûśîńĝ éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ áĉćêśŝíb̂ĺê ńâḿêś](https://dequeuniversity.com/rules/axe/4.9/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Êĺêḿêńt̂ś ŵít̂h́ v̂íŝíb̂ĺê t́êx́t̂ ĺâb́êĺŝ d́ô ńôt́ ĥáv̂é m̂át̂ćĥín̂ǵ âćĉéŝśîb́l̂é n̂ám̂éŝ."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Êĺêḿêńt̂ś ŵít̂h́ v̂íŝíb̂ĺê t́êx́t̂ ĺâb́êĺŝ h́âv́ê ḿât́ĉh́îńĝ áĉćêśŝíb̂ĺê ńâḿêś."}, "core/audits/accessibility/label.js | description": {"message": "L̂áb̂él̂ś êńŝúr̂é t̂h́ât́ f̂ór̂ḿ ĉón̂t́r̂ól̂ś âŕê án̂ńôún̂ćêd́ p̂ŕôṕêŕl̂ý b̂ý âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ f̂ór̂ḿ êĺêḿêńt̂ ĺâb́êĺŝ](https://dequeuniversity.com/rules/axe/4.9/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "F̂ór̂ḿ êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áŝśôćîát̂éd̂ ĺâb́êĺŝ"}, "core/audits/accessibility/label.js | title": {"message": "F̂ór̂ḿ êĺêḿêńt̂ś ĥáv̂é âśŝóĉíât́êd́ l̂áb̂él̂ś"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Ôńê ḿâín̂ ĺâńd̂ḿâŕk̂ h́êĺp̂ś ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ ńâv́îǵât́ê á ŵéb̂ ṕâǵê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ĺâńd̂ḿâŕk̂ś](https://dequeuniversity.com/rules/axe/4.9/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêś n̂ót̂ h́âv́ê á m̂áîń l̂án̂d́m̂ár̂ḱ."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á m̂áîń l̂án̂d́m̂ár̂ḱ."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "L̂óŵ-ćôńt̂ŕâśt̂ t́êx́t̂ íŝ d́îf́f̂íĉúl̂t́ ôŕ îḿp̂óŝśîb́l̂é f̂ór̂ ḿâńŷ úŝér̂ś t̂ó r̂éâd́. L̂ín̂ḱ t̂éx̂t́ t̂h́ât́ îś d̂íŝćêŕn̂íb̂ĺê ím̂ṕr̂óv̂éŝ t́ĥé êx́p̂ér̂íêńĉé f̂ór̂ úŝér̂ś ŵít̂h́ l̂óŵ v́îśîón̂. [Ĺêár̂ń ĥóŵ t́ô ḿâḱê ĺîńk̂ś d̂íŝt́îńĝúîśĥáb̂ĺê](https://dequeuniversity.com/rules/axe/4.9/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "L̂ín̂ḱŝ ŕêĺŷ ón̂ ćôĺôŕ t̂ó b̂é d̂íŝt́îńĝúîśĥáb̂ĺê."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "L̂ín̂ḱŝ ár̂é d̂íŝt́îńĝúîśĥáb̂ĺê ẃît́ĥóût́ r̂él̂ýîńĝ ón̂ ćôĺôŕ."}, "core/audits/accessibility/link-name.js | description": {"message": "L̂ín̂ḱ t̂éx̂t́ (âńd̂ ál̂t́êŕn̂át̂é t̂éx̂t́ f̂ór̂ ím̂áĝéŝ, ẃĥén̂ úŝéd̂ áŝ ĺîńk̂ś) t̂h́ât́ îś d̂íŝćêŕn̂íb̂ĺê, ún̂íq̂úê, án̂d́ f̂óĉúŝáb̂ĺê ím̂ṕr̂óv̂éŝ t́ĥé n̂áv̂íĝát̂íôń êx́p̂ér̂íêńĉé f̂ór̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś. [L̂éâŕn̂ h́ôẃ t̂ó m̂ák̂é l̂ín̂ḱŝ áĉćêśŝíb̂ĺê](https://dequeuniversity.com/rules/axe/4.9/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "L̂ín̂ḱŝ d́ô ńôt́ ĥáv̂é â d́îśĉér̂ńîb́l̂é n̂ám̂é"}, "core/audits/accessibility/link-name.js | title": {"message": "L̂ín̂ḱŝ h́âv́ê á d̂íŝćêŕn̂íb̂ĺê ńâḿê"}, "core/audits/accessibility/list.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê á ŝṕêćîf́îć ŵáŷ óf̂ án̂ńôún̂ćîńĝ ĺîśt̂ś. Êńŝúr̂ín̂ǵ p̂ŕôṕêŕ l̂íŝt́ ŝt́r̂úĉt́ûŕê áîd́ŝ śĉŕêén̂ ŕêád̂ér̂ óût́p̂út̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕr̂óp̂ér̂ ĺîśt̂ śt̂ŕûćt̂úr̂é](https://dequeuniversity.com/rules/axe/4.9/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "L̂íŝt́ŝ d́ô ńôt́ ĉón̂t́âín̂ ón̂ĺŷ `<li>` él̂ém̂én̂t́ŝ án̂d́ ŝćr̂íp̂t́ ŝúp̂ṕôŕt̂ín̂ǵ êĺêḿêńt̂ś (`<script>` âńd̂ `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "L̂íŝt́ŝ ćôńt̂áîń ôńl̂ý `<li>` êĺêḿêńt̂ś âńd̂ śĉŕîṕt̂ śûṕp̂ór̂t́îńĝ él̂ém̂én̂t́ŝ (`<script>` án̂d́ `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ ŕêq́ûír̂é l̂íŝt́ ît́êḿŝ (`<li>`) t́ô b́ê ćôńt̂áîńêd́ ŵít̂h́îń â ṕâŕêńt̂ `<ul>`, `<ol>` ór̂ `<menu>` t́ô b́ê án̂ńôún̂ćêd́ p̂ŕôṕêŕl̂ý. [L̂éâŕn̂ ḿôŕê áb̂óût́ p̂ŕôṕêŕ l̂íŝt́ ŝt́r̂úĉt́ûŕê](https://dequeuniversity.com/rules/axe/4.9/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "L̂íŝt́ ît́êḿŝ (`<li>`) ár̂é n̂ót̂ ćôńt̂áîńêd́ ŵít̂h́îń `<ul>`, `<ol>` ôŕ `<menu>` p̂ár̂én̂t́ êĺêḿêńt̂ś."}, "core/audits/accessibility/listitem.js | title": {"message": "L̂íŝt́ ît́êḿŝ (`<li>`) ár̂é ĉón̂t́âín̂éd̂ ẃît́ĥín̂ `<ul>`, `<ol>` ór̂ `<menu>` ṕâŕêńt̂ él̂ém̂én̂t́ŝ"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Ûśêŕŝ d́ô ńôt́ êx́p̂éĉt́ â ṕâǵê t́ô ŕêf́r̂éŝh́ âút̂óm̂át̂íĉál̂ĺŷ, án̂d́ d̂óîńĝ śô ẃîĺl̂ ḿôv́ê f́ôćûś b̂áĉḱ t̂ó t̂h́ê t́ôṕ ôf́ t̂h́ê ṕâǵê. T́ĥíŝ ḿâý ĉŕêát̂é â f́r̂úŝt́r̂át̂ín̂ǵ ôŕ ĉón̂f́ûśîńĝ éx̂ṕêŕîén̂ćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé r̂éf̂ŕêśĥ ḿêt́â t́âǵ](https://dequeuniversity.com/rules/axe/4.9/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "T̂h́ê d́ôćûḿêńt̂ úŝéŝ `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "T̂h́ê d́ôćûḿêńt̂ d́ôéŝ ńôt́ ûśê `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "D̂íŝáb̂ĺîńĝ źôóm̂ín̂ǵ îś p̂ŕôb́l̂ém̂át̂íĉ f́ôŕ ûśêŕŝ ẃît́ĥ ĺôẃ v̂íŝíôń ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ḿâǵn̂íf̂íĉát̂íôń t̂ó p̂ŕôṕêŕl̂ý ŝéê t́ĥé ĉón̂t́êńt̂ś ôf́ â ẃêb́ p̂áĝé. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê v́îéŵṕôŕt̂ ḿêt́â t́âǵ](https://dequeuniversity.com/rules/axe/4.9/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` îś ûśêd́ îń t̂h́ê `<meta name=\"viewport\">` él̂ém̂én̂t́ ôŕ t̂h́ê `[maximum-scale]` át̂t́r̂íb̂út̂é îś l̂éŝś t̂h́âń 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` îś n̂ót̂ úŝéd̂ ín̂ t́ĥé `<meta name=\"viewport\">` êĺêḿêńt̂ án̂d́ t̂h́ê `[maximum-scale]` át̂t́r̂íb̂út̂é îś n̂ót̂ ĺêśŝ t́ĥán̂ 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ ćâńn̂ót̂ t́r̂án̂śl̂át̂é n̂ón̂-t́êx́t̂ ćôńt̂én̂t́. Âd́d̂ín̂ǵ âĺt̂ér̂ńât́ê t́êx́t̂ t́ô `<object>` él̂ém̂én̂t́ŝ h́êĺp̂ś ŝćr̂éêń r̂éâd́êŕŝ ćôńv̂éŷ ḿêán̂ín̂ǵ t̂ó ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ál̂t́ t̂éx̂t́ f̂ór̂ `object` él̂ém̂én̂t́ŝ](https://dequeuniversity.com/rules/axe/4.9/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê ál̂t́êŕn̂át̂é t̂éx̂t́"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` êĺêḿêńt̂ś ĥáv̂é âĺt̂ér̂ńât́ê t́êx́t̂"}, "core/audits/accessibility/select-name.js | description": {"message": "F̂ór̂ḿ êĺêḿêńt̂ś ŵít̂h́ôút̂ éf̂f́êćt̂ív̂é l̂áb̂él̂ś ĉán̂ ćr̂éât́ê f́r̂úŝt́r̂át̂ín̂ǵ êx́p̂ér̂íêńĉéŝ f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé `select` êĺêḿêńt̂](https://dequeuniversity.com/rules/axe/4.9/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Ŝél̂éĉt́ êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áŝśôćîát̂éd̂ ĺâb́êĺ êĺêḿêńt̂ś."}, "core/audits/accessibility/select-name.js | title": {"message": "Ŝél̂éĉt́ êĺêḿêńt̂ś ĥáv̂é âśŝóĉíât́êd́ l̂áb̂él̂ él̂ém̂én̂t́ŝ."}, "core/audits/accessibility/skip-link.js | description": {"message": "Îńĉĺûd́îńĝ á ŝḱîṕ l̂ín̂ḱ ĉán̂ h́êĺp̂ úŝér̂ś ŝḱîṕ t̂ó t̂h́ê ḿâín̂ ćôńt̂én̂t́ t̂ó ŝáv̂é t̂ím̂é. [L̂éâŕn̂ ḿôŕê áb̂óût́ ŝḱîṕ l̂ín̂ḱŝ](https://dequeuniversity.com/rules/axe/4.9/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Ŝḱîṕ l̂ín̂ḱŝ ár̂é n̂ót̂ f́ôćûśâb́l̂é."}, "core/audits/accessibility/skip-link.js | title": {"message": "Ŝḱîṕ l̂ín̂ḱŝ ár̂é f̂óĉúŝáb̂ĺê."}, "core/audits/accessibility/tabindex.js | description": {"message": "Â v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0 ím̂ṕl̂íêś âń êx́p̂ĺîćît́ n̂áv̂íĝát̂íôń ôŕd̂ér̂ín̂ǵ. Âĺt̂h́ôúĝh́ t̂éĉh́n̂íĉál̂ĺŷ v́âĺîd́, t̂h́îś ôf́t̂én̂ ćr̂éât́êś f̂ŕûśt̂ŕât́îńĝ éx̂ṕêŕîén̂ćêś f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê `tabindex` át̂t́r̂íb̂út̂é](https://dequeuniversity.com/rules/axe/4.9/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Ŝóm̂é êĺêḿêńt̂ś ĥáv̂é â `[tabindex]` v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "N̂ó êĺêḿêńt̂ h́âś â `[tabindex]` v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "T̂h́ê śûḿm̂ár̂ý ât́t̂ŕîb́ût́ê śĥóûĺd̂ d́êśĉŕîb́ê t́ĥé t̂áb̂ĺê śt̂ŕûćt̂úr̂é, ŵh́îĺê `<caption>` śĥóûĺd̂ h́âv́ê t́ĥé ôńŝćr̂éêń t̂ít̂ĺê. Áĉćûŕât́ê t́âb́l̂é m̂ár̂ḱ-ûṕ ĥél̂ṕŝ úŝér̂ś ôf́ ŝćr̂éêń r̂éâd́êŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ śûḿm̂ár̂ý âńd̂ ćâṕt̂íôń](https://dequeuniversity.com/rules/axe/4.9/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "T̂áb̂ĺêś ĥáv̂é t̂h́ê śâḿê ćôńt̂én̂t́ îń t̂h́ê śûḿm̂ár̂ý ât́t̂ŕîb́ût́ê án̂d́ `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "T̂áb̂ĺêś ĥáv̂é d̂íf̂f́êŕêńt̂ ćôńt̂én̂t́ îń t̂h́ê śûḿm̂ár̂ý ât́t̂ŕîb́ût́ê án̂d́ `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ t́ĥát̂ t́âb́l̂éŝ úŝé t̂h́ê áĉt́ûál̂ ćâṕt̂íôń êĺêḿêńt̂ ín̂śt̂éâd́ ôf́ ĉél̂ĺŝ ẃît́ĥ t́ĥé `[colspan]` ât́t̂ŕîb́ût́ê ḿâý îḿp̂ŕôv́ê t́ĥé êx́p̂ér̂íêńĉé f̂ór̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĉáp̂t́îón̂ś](https://dequeuniversity.com/rules/axe/4.9/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "T̂áb̂ĺêś d̂ó n̂ót̂ úŝé `<caption>` îńŝt́êád̂ óf̂ ćêĺl̂ś ŵít̂h́ t̂h́ê `[colspan]` át̂t́r̂íb̂út̂é t̂ó îńd̂íĉát̂é â ćâṕt̂íôń."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "T̂áb̂ĺêś ûśê `<caption>` ín̂śt̂éâd́ ôf́ ĉél̂ĺŝ ẃît́ĥ t́ĥé `[colspan]` ât́t̂ŕîb́ût́ê t́ô ín̂d́îćât́ê á ĉáp̂t́îón̂."}, "core/audits/accessibility/target-size.js | description": {"message": "T̂óûćĥ t́âŕĝét̂ś ŵít̂h́ ŝúf̂f́îćîén̂t́ ŝíẑé âńd̂ śp̂áĉín̂ǵ ĥél̂ṕ ûśêŕŝ ẃĥó m̂áŷ h́âv́ê d́îf́f̂íĉúl̂t́ŷ t́âŕĝét̂ín̂ǵ ŝḿâĺl̂ ćôńt̂ŕôĺŝ t́ô áĉt́îv́ât́ê t́ĥé t̂ár̂ǵêt́ŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ôúĉh́ t̂ár̂ǵêt́ŝ](https://dequeuniversity.com/rules/axe/4.9/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "T̂óûćĥ t́âŕĝét̂ś d̂ó n̂ót̂ h́âv́ê śûf́f̂íĉíêńt̂ śîźê ór̂ śp̂áĉín̂ǵ."}, "core/audits/accessibility/target-size.js | title": {"message": "T̂óûćĥ t́âŕĝét̂ś ĥáv̂é ŝúf̂f́îćîén̂t́ ŝíẑé âńd̂ śp̂áĉín̂ǵ."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ t́ĥát̂ `<td>` él̂ém̂én̂t́ŝ ín̂ á l̂ár̂ǵê t́âb́l̂é (3 ôŕ m̂ór̂é ĉél̂ĺŝ ín̂ ẃîd́t̂h́ âńd̂ h́êíĝh́t̂) h́âv́ê án̂ áŝśôćîát̂éd̂ t́âb́l̂é ĥéâd́êŕ m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́âb́l̂é ĥéâd́êŕŝ](https://dequeuniversity.com/rules/axe/4.9/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "`<td>` êĺêḿêńt̂ś îń â ĺâŕĝé `<table>` d̂ó n̂ót̂ h́âv́ê t́âb́l̂é ĥéâd́êŕŝ."}, "core/audits/accessibility/td-has-header.js | title": {"message": "`<td>` êĺêḿêńt̂ś îń â ĺâŕĝé `<table>` ĥáv̂é ôńê ór̂ ḿôŕê t́âb́l̂é ĥéâd́êŕŝ."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ `<td>` ćêĺl̂ś ûśîńĝ t́ĥé `[headers]` ât́t̂ŕîb́ût́ê ón̂ĺŷ ŕêf́êŕ t̂ó ôt́ĥér̂ ćêĺl̂ś îń t̂h́ê śâḿê t́âb́l̂é m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé `headers` ât́t̂ŕîb́ût́ê](https://dequeuniversity.com/rules/axe/4.9/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ĉél̂ĺŝ ín̂ á `<table>` êĺêḿêńt̂ t́ĥát̂ úŝé t̂h́ê `[headers]` át̂t́r̂íb̂út̂é r̂éf̂ér̂ t́ô án̂ él̂ém̂én̂t́ `id` n̂ót̂ f́ôún̂d́ ŵít̂h́îń t̂h́ê śâḿê t́âb́l̂é."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Ĉél̂ĺŝ ín̂ á `<table>` êĺêḿêńt̂ t́ĥát̂ úŝé t̂h́ê `[headers]` át̂t́r̂íb̂út̂é r̂éf̂ér̂ t́ô t́âb́l̂é ĉél̂ĺŝ ẃît́ĥín̂ t́ĥé ŝám̂é t̂áb̂ĺê."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ t́âb́l̂é ĥéâd́êŕŝ ál̂ẃâýŝ ŕêf́êŕ t̂ó ŝóm̂é ŝét̂ óf̂ ćêĺl̂ś m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́âb́l̂é ĥéâd́êŕŝ](https://dequeuniversity.com/rules/axe/4.9/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` êĺêḿêńt̂ś âńd̂ él̂ém̂én̂t́ŝ ẃît́ĥ `[role=\"columnheader\"/\"rowheader\"]` d́ô ńôt́ ĥáv̂é d̂át̂á ĉél̂ĺŝ t́ĥéŷ d́êśĉŕîb́ê."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` êĺêḿêńt̂ś âńd̂ él̂ém̂én̂t́ŝ ẃît́ĥ `[role=\"columnheader\"/\"rowheader\"]` h́âv́ê d́ât́â ćêĺl̂ś t̂h́êý d̂éŝćr̂íb̂é."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â v́âĺîd́ [B̂ĆP̂ 47 ĺâńĝúâǵê](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ón̂ él̂ém̂én̂t́ŝ h́êĺp̂ś êńŝúr̂é t̂h́ât́ t̂éx̂t́ îś p̂ŕôńôún̂ćêd́ ĉór̂ŕêćt̂ĺŷ b́ŷ á ŝćr̂éêń r̂éâd́êŕ. [L̂éâŕn̂ h́ôẃ t̂ó ûśê t́ĥé `lang` ât́t̂ŕîb́ût́ê](https://dequeuniversity.com/rules/axe/4.9/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ h́âv́ê á v̂ál̂íd̂ v́âĺûé"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` ât́t̂ŕîb́ût́êś ĥáv̂é â v́âĺîd́ v̂ál̂úê"}, "core/audits/accessibility/video-caption.js | description": {"message": "Ŵh́êń â v́îd́êó p̂ŕôv́îd́êś â ćâṕt̂íôń ît́ îś êáŝíêŕ f̂ór̂ d́êáf̂ án̂d́ ĥéâŕîńĝ ím̂ṕâír̂éd̂ úŝér̂ś t̂ó âćĉéŝś ît́ŝ ín̂f́ôŕm̂át̂íôń. [L̂éâŕn̂ ḿôŕê áb̂óût́ v̂íd̂éô ćâṕt̂íôńŝ](https://dequeuniversity.com/rules/axe/4.9/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` êĺêḿêńt̂ś d̂ó n̂ót̂ ćôńt̂áîń â `<track>` él̂ém̂én̂t́ ŵít̂h́ `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "`<video>` êĺêḿêńt̂ś ĉón̂t́âín̂ á `<track>` êĺêḿêńt̂ ẃît́ĥ `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Ĉúr̂ŕêńt̂ V́âĺûé"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Ŝúĝǵêśt̂éd̂ T́ôḱêń"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` ĥél̂ṕŝ úŝér̂ś ŝúb̂ḿît́ f̂ór̂ḿŝ q́ûíĉḱêŕ. T̂ó r̂éd̂úĉé ûśêŕ êf́f̂ór̂t́, ĉón̂śîd́êŕ êńâb́l̂ín̂ǵ b̂ý ŝét̂t́îńĝ t́ĥé `autocomplete` ât́t̂ŕîb́ût́ê t́ô á v̂ál̂íd̂ v́âĺûé. [L̂éâŕn̂ ḿôŕê áb̂óût́ `autocomplete` îń f̂ór̂ḿŝ](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê ćôŕr̂éĉt́ `autocomplete` ât́t̂ŕîb́ût́êś"}, "core/audits/autocomplete.js | manualReview": {"message": "R̂éq̂úîŕêś m̂án̂úâĺ r̂év̂íêẃ"}, "core/audits/autocomplete.js | reviewOrder": {"message": "R̂év̂íêẃ ôŕd̂ér̂ óf̂ t́ôḱêńŝ"}, "core/audits/autocomplete.js | title": {"message": "`<input>` êĺêḿêńt̂ś ĉór̂ŕêćt̂ĺŷ úŝé `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` t̂ók̂én̂(ś): \"{token}\" îś îńv̂ál̂íd̂ ín̂ {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "R̂év̂íêẃ ôŕd̂ér̂ óf̂ t́ôḱêńŝ: \"{tokens}\" ín̂ {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Âćt̂íôńâb́l̂é"}, "core/audits/bf-cache.js | description": {"message": "M̂án̂ý n̂áv̂íĝát̂íôńŝ ár̂é p̂ér̂f́ôŕm̂éd̂ b́ŷ ǵôín̂ǵ b̂áĉḱ t̂ó â ṕr̂év̂íôúŝ ṕâǵê, ór̂ f́ôŕŵár̂d́ŝ áĝáîń. T̂h́ê b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê (b́f̂ćâćĥé) ĉán̂ śp̂éêd́ ûṕ t̂h́êśê ŕêt́ûŕn̂ ńâv́îǵât́îón̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê b́f̂ćâćĥé](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 f̂áîĺûŕê ŕêáŝón̂}\n    other {# f́âíl̂úr̂é r̂éâśôńŝ}\n    }"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "F̂áîĺûŕê ŕêáŝón̂"}, "core/audits/bf-cache.js | failureTitle": {"message": "P̂áĝé p̂ŕêv́êńt̂éd̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê ŕêśt̂ór̂át̂íôń"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "F̂áîĺûŕê t́ŷṕê"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "N̂ót̂ áĉt́îón̂áb̂ĺê"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "P̂én̂d́îńĝ b́r̂óŵśêŕ ŝúp̂ṕôŕt̂"}, "core/audits/bf-cache.js | title": {"message": "P̂áĝé d̂íd̂ń't̂ ṕr̂év̂én̂t́ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé r̂éŝt́ôŕât́îón̂"}, "core/audits/bf-cache.js | warningHeadless": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé ĉán̂ńôt́ b̂é t̂éŝt́êd́ îń ôĺd̂ H́êád̂ĺêśŝ Ćĥŕôḿê (`--chrome-flags=\"--headless=old\"`). T́ô śêé âúd̂ít̂ ŕêśûĺt̂ś, ûśê t́ĥé n̂éŵ H́êád̂ĺêśŝ Ćĥŕôḿê (`--chrome-flags=\"--headless=new\"`) ór̂ śt̂án̂d́âŕd̂ Ćĥŕôḿê."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Ĉh́r̂óm̂é êx́t̂én̂śîón̂ś n̂éĝát̂ív̂él̂ý âf́f̂éĉt́êd́ t̂h́îś p̂áĝé'ŝ ĺôád̂ ṕêŕf̂ór̂ḿâńĉé. T̂ŕŷ áûd́ît́îńĝ t́ĥé p̂áĝé îń îńĉóĝńît́ô ḿôd́ê ór̂ f́r̂óm̂ á Ĉh́r̂óm̂é p̂ŕôf́îĺê ẃît́ĥóût́ êx́t̂én̂śîón̂ś."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Ŝćr̂íp̂t́ Êv́âĺûát̂íôń"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Ŝćr̂íp̂t́ P̂ár̂śê"}, "core/audits/bootup-time.js | columnTotal": {"message": "T̂ót̂ál̂ ĆP̂Ú T̂ím̂é"}, "core/audits/bootup-time.js | description": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ t̂h́ê t́îḿê śp̂én̂t́ p̂ár̂śîńĝ, ćôḿp̂íl̂ín̂ǵ, âńd̂ éx̂éĉút̂ín̂ǵ ĴŚ. Ŷóû ḿâý f̂ín̂d́ d̂él̂ív̂ér̂ín̂ǵ ŝḿâĺl̂ér̂ J́Ŝ ṕâýl̂óâd́ŝ h́êĺp̂ś ŵít̂h́ t̂h́îś. [L̂éâŕn̂ h́ôẃ t̂ó r̂éd̂úĉé Ĵáv̂áŝćr̂íp̂t́ êx́êćût́îón̂ t́îḿê](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "R̂éd̂úĉé Ĵáv̂áŜćr̂íp̂t́ êx́êćût́îón̂ t́îḿê"}, "core/audits/bootup-time.js | title": {"message": "Ĵáv̂áŜćr̂íp̂t́ êx́êćût́îón̂ t́îḿê"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "R̂ém̂óv̂é l̂ár̂ǵê, d́ûṕl̂íĉát̂é Ĵáv̂áŜćr̂íp̂t́ m̂ód̂úl̂éŝ f́r̂óm̂ b́ûńd̂ĺêś t̂ó r̂éd̂úĉé ûńn̂éĉéŝśâŕŷ b́ŷt́êś ĉón̂śûḿêd́ b̂ý n̂ét̂ẃôŕk̂ áĉt́îv́ît́ŷ. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "R̂ém̂óv̂é d̂úp̂ĺîćât́ê ḿôd́ûĺêś îń Ĵáv̂áŜćr̂íp̂t́ b̂ún̂d́l̂éŝ"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "L̂ár̂ǵê ǴÎF́ŝ ár̂é îńêf́f̂íĉíêńt̂ f́ôŕ d̂él̂ív̂ér̂ín̂ǵ âńîḿât́êd́ ĉón̂t́êńt̂. Ćôńŝíd̂ér̂ úŝín̂ǵ M̂ṔÊǴ4/Ŵéb̂Ḿ v̂íd̂éôś f̂ór̂ án̂ím̂át̂íôńŝ án̂d́ P̂ŃĜ/Ẃêb́P̂ f́ôŕ ŝt́ât́îć îḿâǵêś îńŝt́êád̂ óf̂ ǴÎF́ t̂ó ŝáv̂é n̂ét̂ẃôŕk̂ b́ŷt́êś. [L̂éâŕn̂ ḿôŕê áb̂óût́ êf́f̂íĉíêńt̂ v́îd́êó f̂ór̂ḿât́ŝ](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Ûśê v́îd́êó f̂ór̂ḿât́ŝ f́ôŕ âńîḿât́êd́ ĉón̂t́êńt̂"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "P̂ól̂ýf̂íl̂ĺŝ án̂d́ t̂ŕâńŝf́ôŕm̂ś êńâb́l̂é l̂éĝáĉý b̂ŕôẃŝér̂ś t̂ó ûśê ńêẃ Ĵáv̂áŜćr̂íp̂t́ f̂éât́ûŕêś. Ĥóŵév̂ér̂, ḿâńŷ ár̂én̂'t́ n̂éĉéŝśâŕŷ f́ôŕ m̂ód̂ér̂ń b̂ŕôẃŝér̂ś. F̂ór̂ ýôúr̂ b́ûńd̂ĺêd́ Ĵáv̂áŜćr̂íp̂t́, âd́ôṕt̂ á m̂ód̂ér̂ń ŝćr̂íp̂t́ d̂ép̂ĺôým̂én̂t́ ŝt́r̂át̂éĝý ûśîńĝ ḿôd́ûĺê/ńôḿôd́ûĺê f́êát̂úr̂é d̂ét̂éĉt́îón̂ t́ô ŕêd́ûćê t́ĥé âḿôún̂t́ ôf́ ĉód̂é ŝh́îṕp̂éd̂ t́ô ḿôd́êŕn̂ b́r̂óŵśêŕŝ, ẃĥíl̂é r̂ét̂áîńîńĝ śûṕp̂ór̂t́ f̂ór̂ ĺêǵâćŷ b́r̂óŵśêŕŝ. [Ĺêár̂ń ĥóŵ t́ô úŝé m̂ód̂ér̂ń Ĵáv̂áŜćr̂íp̂t́](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Âv́ôíd̂ śêŕv̂ín̂ǵ l̂éĝáĉý Ĵáv̂áŜćr̂íp̂t́ t̂ó m̂ód̂ér̂ń b̂ŕôẃŝér̂ś"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Îḿâǵê f́ôŕm̂át̂ś l̂ík̂é Ŵéb̂Ṕ âńd̂ ÁV̂ÍF̂ óf̂t́êń p̂ŕôv́îd́ê b́êt́t̂ér̂ ćôḿp̂ŕêśŝíôń t̂h́âń P̂ŃĜ ór̂ J́P̂ÉĜ, ẃĥíĉh́ m̂éâńŝ f́âśt̂ér̂ d́ôẃn̂ĺôád̂ś âńd̂ ĺêśŝ d́ât́â ćôńŝúm̂ṕt̂íôń. [L̂éâŕn̂ ḿôŕê áb̂óût́ m̂ód̂ér̂ń îḿâǵê f́ôŕm̂át̂ś](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Ŝér̂v́ê ím̂áĝéŝ ín̂ ńêx́t̂-ǵêń f̂ór̂ḿât́ŝ"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Ĉón̂śîd́êŕ l̂áẑý-l̂óâd́îńĝ óf̂f́ŝćr̂éêń âńd̂ h́îd́d̂én̂ ím̂áĝéŝ áf̂t́êŕ âĺl̂ ćr̂ít̂íĉál̂ ŕêśôúr̂ćêś ĥáv̂é f̂ín̂íŝh́êd́ l̂óâd́îńĝ t́ô ĺôẃêŕ t̂ím̂é t̂ó îńt̂ér̂áĉt́îv́ê. [Ĺêár̂ń ĥóŵ t́ô d́êf́êŕ ôf́f̂śĉŕêén̂ ím̂áĝéŝ](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "D̂éf̂ér̂ óf̂f́ŝćr̂éêń îḿâǵêś"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "R̂éŝóûŕĉéŝ ár̂é b̂ĺôćk̂ín̂ǵ t̂h́ê f́îŕŝt́ p̂áîńt̂ óf̂ ýôúr̂ ṕâǵê. Ćôńŝíd̂ér̂ d́êĺîv́êŕîńĝ ćr̂ít̂íĉál̂ J́Ŝ/ĆŜŚ îńl̂ín̂é âńd̂ d́êf́êŕr̂ín̂ǵ âĺl̂ ńôń-ĉŕît́îćâĺ ĴŚ/ŝt́ŷĺêś. [L̂éâŕn̂ h́ôẃ t̂ó êĺîḿîńât́ê ŕêńd̂ér̂-b́l̂óĉḱîńĝ ŕêśôúr̂ćêś](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Êĺîḿîńât́ê ŕêńd̂ér̂-b́l̂óĉḱîńĝ ŕêśôúr̂ćêś"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "L̂ár̂ǵê ńêt́ŵór̂ḱ p̂áŷĺôád̂ś ĉóŝt́ ûśêŕŝ ŕêál̂ ḿôńêý âńd̂ ár̂é ĥíĝh́l̂ý ĉór̂ŕêĺât́êd́ ŵít̂h́ l̂ón̂ǵ l̂óâd́ t̂ím̂éŝ. [Ĺêár̂ń ĥóŵ t́ô ŕêd́ûćê ṕâýl̂óâd́ ŝíẑéŝ](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "T̂ót̂ál̂ śîźê ẃâś {totalBytes, number, bytes} K̂íB̂"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Âv́ôíd̂ én̂ór̂ḿôúŝ ńêt́ŵór̂ḱ p̂áŷĺôád̂ś"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Âv́ôíd̂ś êńôŕm̂óûś n̂ét̂ẃôŕk̂ ṕâýl̂óâd́ŝ"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "M̂ín̂íf̂ýîńĝ ĆŜŚ f̂íl̂éŝ ćâń r̂éd̂úĉé n̂ét̂ẃôŕk̂ ṕâýl̂óâd́ ŝíẑéŝ. [Ĺêár̂ń ĥóŵ t́ô ḿîńîf́ŷ ĆŜŚ](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "M̂ín̂íf̂ý ĈŚŜ"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "M̂ín̂íf̂ýîńĝ J́âv́âŚĉŕîṕt̂ f́îĺêś ĉán̂ ŕêd́ûćê ṕâýl̂óâd́ ŝíẑéŝ án̂d́ ŝćr̂íp̂t́ p̂ár̂śê t́îḿê. [Ĺêár̂ń ĥóŵ t́ô ḿîńîf́ŷ J́âv́âŚĉŕîṕt̂](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "M̂ín̂íf̂ý Ĵáv̂áŜćr̂íp̂t́"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "R̂éd̂úĉé ûńûśêd́ r̂úl̂éŝ f́r̂óm̂ śt̂ýl̂éŝh́êét̂ś âńd̂ d́êf́êŕ ĈŚŜ ńôt́ ûśêd́ f̂ór̂ áb̂óv̂é-t̂h́ê-f́ôĺd̂ ćôńt̂én̂t́ t̂ó d̂éĉŕêáŝé b̂ýt̂éŝ ćôńŝúm̂éd̂ b́ŷ ńêt́ŵór̂ḱ âćt̂ív̂ít̂ý. [L̂éâŕn̂ h́ôẃ t̂ó r̂éd̂úĉé ûńûśêd́ ĈŚŜ](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "R̂éd̂úĉé ûńûśêd́ ĈŚŜ"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "R̂éd̂úĉé ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́ âńd̂ d́êf́êŕ l̂óâd́îńĝ śĉŕîṕt̂ś ûńt̂íl̂ t́ĥéŷ ár̂é r̂éq̂úîŕêd́ t̂ó d̂éĉŕêáŝé b̂ýt̂éŝ ćôńŝúm̂éd̂ b́ŷ ńêt́ŵór̂ḱ âćt̂ív̂ít̂ý. [L̂éâŕn̂ h́ôẃ t̂ó r̂éd̂úĉé ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "R̂éd̂úĉé ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Â ĺôńĝ ćâćĥé l̂íf̂ét̂ím̂é ĉán̂ śp̂éêd́ ûṕ r̂ép̂éât́ v̂íŝít̂ś t̂ó ŷóûŕ p̂áĝé. [L̂éâŕn̂ ḿôŕê áb̂óût́ êf́f̂íĉíêńt̂ ćâćĥé p̂ól̂íĉíêś](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 r̂éŝóûŕĉé f̂óûńd̂}\n    other {# ŕêśôúr̂ćêś f̂óûńd̂}\n    }"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Ŝér̂v́ê śt̂át̂íĉ áŝśêt́ŝ ẃît́ĥ án̂ éf̂f́îćîén̂t́ ĉáĉh́ê ṕôĺîćŷ"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Ûśêś êf́f̂íĉíêńt̂ ćâćĥé p̂ól̂íĉý ôń ŝt́ât́îć âśŝét̂ś"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Ôṕt̂ím̂íẑéd̂ ím̂áĝéŝ ĺôád̂ f́âśt̂ér̂ án̂d́ ĉón̂śûḿê ĺêśŝ ćêĺl̂úl̂ár̂ d́ât́â. [Ĺêár̂ń ĥóŵ t́ô éf̂f́îćîén̂t́l̂ý êńĉód̂é îḿâǵêś](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Êf́f̂íĉíêńt̂ĺŷ én̂ćôd́ê ím̂áĝéŝ"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Âćt̂úâĺ d̂ím̂én̂śîón̂ś"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "D̂íŝṕl̂áŷéd̂ d́îḿêńŝíôńŝ"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Îḿâǵêś ŵér̂é l̂ár̂ǵêŕ t̂h́âń t̂h́êír̂ d́îśp̂ĺâýêd́ ŝíẑé"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Îḿâǵêś ŵér̂é âṕp̂ŕôṕr̂íât́ê f́ôŕ t̂h́êír̂ d́îśp̂ĺâýêd́ ŝíẑé"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Ŝér̂v́ê ím̂áĝéŝ t́ĥát̂ ár̂é âṕp̂ŕôṕr̂íât́êĺŷ-śîźêd́ t̂ó ŝáv̂é ĉél̂ĺûĺâŕ d̂át̂á âńd̂ ím̂ṕr̂óv̂é l̂óâd́ t̂ím̂é. [L̂éâŕn̂ h́ôẃ t̂ó ŝíẑé îḿâǵêś](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "P̂ŕôṕêŕl̂ý ŝíẑé îḿâǵêś"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "T̂éx̂t́-b̂áŝéd̂ ŕêśôúr̂ćêś ŝh́ôúl̂d́ b̂é ŝér̂v́êd́ ŵít̂h́ ĉóm̂ṕr̂éŝśîón̂ (ǵẑíp̂, d́êf́l̂át̂é ôŕ b̂ŕôt́l̂í) t̂ó m̂ín̂ím̂íẑé t̂ót̂ál̂ ńêt́ŵór̂ḱ b̂ýt̂éŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́êx́t̂ ćôḿp̂ŕêśŝíôń](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂"}, "core/audits/content-width.js | description": {"message": "Îf́ t̂h́ê ẃîd́t̂h́ ôf́ ŷóûŕ âṕp̂'ś ĉón̂t́êńt̂ d́ôéŝń't̂ ḿât́ĉh́ t̂h́ê ẃîd́t̂h́ ôf́ t̂h́ê v́îéŵṕôŕt̂, ýôúr̂ áp̂ṕ m̂íĝh́t̂ ńôt́ b̂é ôṕt̂ím̂íẑéd̂ f́ôŕ m̂ób̂íl̂é ŝćr̂éêńŝ. [Ĺêár̂ń ĥóŵ t́ô śîźê ćôńt̂én̂t́ f̂ór̂ t́ĥé v̂íêẃp̂ór̂t́](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "T̂h́ê v́îéŵṕôŕt̂ śîźê óf̂ {innerWidth}ṕx̂ d́ôéŝ ńôt́ m̂át̂ćĥ t́ĥé ŵín̂d́ôẃ ŝíẑé ôf́ {outerWidth}p̂x́."}, "core/audits/content-width.js | failureTitle": {"message": "Ĉón̂t́êńt̂ íŝ ńôt́ ŝíẑéd̂ ćôŕr̂éĉt́l̂ý f̂ór̂ t́ĥé v̂íêẃp̂ór̂t́"}, "core/audits/content-width.js | title": {"message": "Ĉón̂t́êńt̂ íŝ śîźêd́ ĉór̂ŕêćt̂ĺŷ f́ôŕ t̂h́ê v́îéŵṕôŕt̂"}, "core/audits/critical-request-chains.js | description": {"message": "T̂h́ê Ćr̂ít̂íĉál̂ Ŕêq́ûéŝt́ Ĉh́âín̂ś b̂él̂óŵ śĥóŵ ýôú ŵh́ât́ r̂éŝóûŕĉéŝ ár̂é l̂óâd́êd́ ŵít̂h́ â h́îǵĥ ṕr̂íôŕît́ŷ. Ćôńŝíd̂ér̂ ŕêd́ûćîńĝ t́ĥé l̂én̂ǵt̂h́ ôf́ ĉh́âín̂ś, r̂éd̂úĉín̂ǵ t̂h́ê d́ôẃn̂ĺôád̂ śîźê óf̂ ŕêśôúr̂ćêś, ôŕ d̂éf̂ér̂ŕîńĝ t́ĥé d̂óŵńl̂óâd́ ôf́ ûńn̂éĉéŝśâŕŷ ŕêśôúr̂ćêś t̂ó îḿp̂ŕôv́ê ṕâǵê ĺôád̂. [Ĺêár̂ń ĥóŵ t́ô áv̂óîd́ ĉh́âín̂ín̂ǵ ĉŕît́îćâĺ r̂éq̂úêśt̂ś](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ĉh́âín̂ f́ôún̂d́}\n    other {# ĉh́âín̂ś f̂óûńd̂}\n    }"}, "core/audits/critical-request-chains.js | title": {"message": "Âv́ôíd̂ ćĥáîńîńĝ ćr̂ít̂íĉál̂ ŕêq́ûéŝt́ŝ"}, "core/audits/csp-xss.js | columnDirective": {"message": "D̂ír̂éĉt́îv́ê"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Ŝév̂ér̂ít̂ý"}, "core/audits/csp-xss.js | description": {"message": "Â śt̂ŕôńĝ Ćôńt̂én̂t́ Ŝéĉúr̂ít̂ý P̂ól̂íĉý (ĈŚP̂) śîǵn̂íf̂íĉán̂t́l̂ý r̂éd̂úĉéŝ t́ĥé r̂íŝḱ ôf́ ĉŕôśŝ-śît́ê śĉŕîṕt̂ín̂ǵ (X̂ŚŜ) át̂t́âćk̂ś. [L̂éâŕn̂ h́ôẃ t̂ó ûśê á ĈŚP̂ t́ô ṕr̂év̂én̂t́ X̂ŚŜ](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Ŝýn̂t́âx́"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "T̂h́ê ṕâǵê ćôńt̂áîńŝ á ĈŚP̂ d́êf́îńêd́ îń â `<meta>` t́âǵ. Ĉón̂śîd́êŕ m̂óv̂ín̂ǵ t̂h́ê ĆŜṔ t̂ó âń ĤT́T̂Ṕ ĥéâd́êŕ ôŕ d̂éf̂ín̂ín̂ǵ âńôt́ĥér̂ śt̂ŕîćt̂ ĆŜṔ îń âń ĤT́T̂Ṕ ĥéâd́êŕ."}, "core/audits/csp-xss.js | noCsp": {"message": "N̂ó ĈŚP̂ f́ôún̂d́ îń êńf̂ór̂ćêḿêńt̂ ḿôd́ê"}, "core/audits/csp-xss.js | title": {"message": "Êńŝúr̂é ĈŚP̂ íŝ éf̂f́êćt̂ív̂é âǵâín̂śt̂ X́ŜŚ ât́t̂áĉḱŝ"}, "core/audits/deprecations.js | columnDeprecate": {"message": "D̂ép̂ŕêćât́îón̂ / Ẃâŕn̂ín̂ǵ"}, "core/audits/deprecations.js | columnLine": {"message": "L̂ín̂é"}, "core/audits/deprecations.js | description": {"message": "D̂ép̂ŕêćât́êd́ ÂṔÎś ŵíl̂ĺ êv́êńt̂úâĺl̂ý b̂é r̂ém̂óv̂éd̂ f́r̂óm̂ t́ĥé b̂ŕôẃŝér̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ d́êṕr̂éĉát̂éd̂ ÁP̂Íŝ](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ŵár̂ńîńĝ f́ôún̂d́}\n    other {# ŵár̂ńîńĝś f̂óûńd̂}\n    }"}, "core/audits/deprecations.js | failureTitle": {"message": "Ûśêś d̂ép̂ŕêćât́êd́ ÂṔÎś"}, "core/audits/deprecations.js | title": {"message": "Âv́ôíd̂ś d̂ép̂ŕêćât́êd́ ÂṔÎś"}, "core/audits/dobetterweb/charset.js | description": {"message": "Â ćĥár̂áĉt́êŕ êńĉód̂ín̂ǵ d̂éĉĺâŕât́îón̂ íŝ ŕêq́ûír̂éd̂. Ít̂ ćâń b̂é d̂ón̂é ŵít̂h́ â `<meta>` t́âǵ îń t̂h́ê f́îŕŝt́ 1024 b̂ýt̂éŝ óf̂ t́ĥé ĤT́M̂Ĺ ôŕ îń t̂h́ê Ćôńt̂én̂t́-T̂ýp̂é ĤT́T̂Ṕ r̂éŝṕôńŝé ĥéâd́êŕ. [L̂éâŕn̂ ḿôŕê áb̂óût́ d̂éĉĺâŕîńĝ t́ĥé ĉh́âŕâćt̂ér̂ én̂ćôd́îńĝ](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Ĉh́âŕŝét̂ d́êćl̂ár̂át̂íôń îś m̂íŝśîńĝ ór̂ óĉćûŕŝ t́ôó l̂át̂é îń t̂h́ê H́T̂ḾL̂"}, "core/audits/dobetterweb/charset.js | title": {"message": "P̂ŕôṕêŕl̂ý d̂éf̂ín̂éŝ ćĥár̂śêt́"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â d́ôćt̂ýp̂é p̂ŕêv́êńt̂ś t̂h́ê b́r̂óŵśêŕ f̂ŕôḿ ŝẃît́ĉh́îńĝ t́ô q́ûír̂ḱŝ-ḿôd́ê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé d̂óĉt́ŷṕê d́êćl̂ár̂át̂íôń](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "D̂óĉt́ŷṕê ńâḿê ḿûśt̂ b́ê t́ĥé ŝt́r̂ín̂ǵ `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "D̂óĉúm̂én̂t́ ĉón̂t́âín̂ś â `doctype` t́ĥát̂ t́r̂íĝǵêŕŝ `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "D̂óĉúm̂én̂t́ m̂úŝt́ ĉón̂t́âín̂ á d̂óĉt́ŷṕê"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Êx́p̂éĉt́êd́ p̂úb̂ĺîćÎd́ t̂ó b̂é âń êḿp̂t́ŷ śt̂ŕîńĝ"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Êx́p̂éĉt́êd́ ŝýŝt́êḿÎd́ t̂ó b̂é âń êḿp̂t́ŷ śt̂ŕîńĝ"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "D̂óĉúm̂én̂t́ ĉón̂t́âín̂ś â `doctype` t́ĥát̂ t́r̂íĝǵêŕŝ `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "P̂áĝé l̂áĉḱŝ t́ĥé ĤT́M̂Ĺ d̂óĉt́ŷṕê, t́ĥúŝ t́r̂íĝǵêŕîńĝ q́ûír̂ḱŝ-ḿôd́ê"}, "core/audits/dobetterweb/doctype.js | title": {"message": "P̂áĝé ĥáŝ t́ĥé ĤT́M̂Ĺ d̂óĉt́ŷṕê"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Ŝt́ât́îśt̂íĉ"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "V̂ál̂úê"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Â ĺâŕĝé D̂ÓM̂ ẃîĺl̂ ín̂ćr̂éâśê ḿêḿôŕŷ úŝáĝé, ĉáûśê ĺôńĝér̂ [śt̂ýl̂é ĉál̂ćûĺât́îón̂ś](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), âńd̂ ṕr̂ód̂úĉé ĉóŝt́l̂ý [l̂áŷóût́ r̂éf̂ĺôẃŝ](https://developers.google.com/speed/articles/reflow). [Ĺêár̂ń ĥóŵ t́ô áv̂óîd́ âń êx́ĉéŝśîv́ê D́ÔḾ ŝíẑé](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 êĺêḿêńt̂}\n    other {# él̂ém̂én̂t́ŝ}\n    }"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Âv́ôíd̂ án̂ éx̂ćêśŝív̂é D̂ÓM̂ śîźê"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "M̂áx̂ím̂úm̂ D́ÔḾ D̂ép̂t́ĥ"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "T̂ót̂ál̂ D́ÔḾ Êĺêḿêńt̂ś"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "M̂áx̂ím̂úm̂ Ćĥíl̂d́ Êĺêḿêńt̂ś"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Âv́ôíd̂ś âń êx́ĉéŝśîv́ê D́ÔḾ ŝíẑé"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Ûśêŕŝ ár̂é m̂íŝt́r̂úŝt́f̂úl̂ óf̂ ór̂ ćôńf̂úŝéd̂ b́ŷ śît́êś t̂h́ât́ r̂éq̂úêśt̂ t́ĥéîŕ l̂óĉát̂íôń ŵít̂h́ôút̂ ćôńt̂éx̂t́. Ĉón̂śîd́êŕ t̂ýîńĝ t́ĥé r̂éq̂úêśt̂ t́ô á ûśêŕ âćt̂íôń îńŝt́êád̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé ĝéôĺôćât́îón̂ ṕêŕm̂íŝśîón̂](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "R̂éq̂úêśt̂ś t̂h́ê ǵêól̂óĉát̂íôń p̂ér̂ḿîśŝíôń ôń p̂áĝé l̂óâd́"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Âv́ôíd̂ś r̂éq̂úêśt̂ín̂ǵ t̂h́ê ǵêól̂óĉát̂íôń p̂ér̂ḿîśŝíôń ôń p̂áĝé l̂óâd́"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Îśŝúê t́ŷṕê"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Îśŝúêś l̂óĝǵêd́ t̂ó t̂h́ê `Issues` ṕâńêĺ îń Ĉh́r̂óm̂é D̂év̂t́ôól̂ś îńd̂íĉát̂é ûńr̂éŝól̂v́êd́ p̂ŕôb́l̂ém̂ś. T̂h́êý ĉán̂ ćôḿê f́r̂óm̂ ńêt́ŵór̂ḱ r̂éq̂úêśt̂ f́âíl̂úr̂éŝ, ín̂śûf́f̂íĉíêńt̂ śêćûŕît́ŷ ćôńt̂ŕôĺŝ, án̂d́ ôt́ĥér̂ b́r̂óŵśêŕ ĉón̂ćêŕn̂ś. Ôṕêń ûṕ t̂h́ê Íŝśûéŝ ṕâńêĺ îń Ĉh́r̂óm̂é D̂év̂T́ôól̂ś f̂ór̂ ḿôŕê d́êt́âíl̂ś ôń êáĉh́ îśŝúê."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Îśŝúêś ŵér̂é l̂óĝǵêd́ îń t̂h́ê `Issues` ṕâńêĺ îń Ĉh́r̂óm̂é D̂év̂t́ôól̂ś"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "B̂ĺôćk̂éd̂ b́ŷ ćr̂óŝś-ôŕîǵîń p̂ól̂íĉý"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Ĥéâv́ŷ ŕêśôúr̂ćê úŝáĝé b̂ý âd́ŝ"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "N̂ó îśŝúêś îń t̂h́ê `Issues` ṕâńêĺ îń Ĉh́r̂óm̂é D̂év̂t́ôól̂ś"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "V̂ér̂śîón̂"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Âĺl̂ f́r̂ón̂t́-êńd̂ J́âv́âŚĉŕîṕt̂ ĺîb́r̂ár̂íêś d̂ét̂éĉt́êd́ ôń t̂h́ê ṕâǵê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥíŝ J́âv́âŚĉŕîṕt̂ ĺîb́r̂ár̂ý d̂ét̂éĉt́îón̂ d́îáĝńôśt̂íĉ áûd́ît́](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "D̂ét̂éĉt́êd́ Ĵáv̂áŜćr̂íp̂t́ l̂íb̂ŕâŕîéŝ"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "F̂ór̂ úŝér̂ś ôń ŝĺôẃ ĉón̂ńêćt̂íôńŝ, éx̂t́êŕn̂ál̂ śĉŕîṕt̂ś d̂ýn̂ám̂íĉál̂ĺŷ ín̂j́êćt̂éd̂ v́îá `document.write()` ĉán̂ d́êĺâý p̂áĝé l̂óâd́ b̂ý t̂én̂ś ôf́ ŝéĉón̂d́ŝ. [Ĺêár̂ń ĥóŵ t́ô áv̂óîd́ d̂óĉúm̂én̂t́.ŵŕît́ê()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Âv́ôíd̂ `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Âv́ôíd̂ś `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Ûśêŕŝ ár̂é m̂íŝt́r̂úŝt́f̂úl̂ óf̂ ór̂ ćôńf̂úŝéd̂ b́ŷ śît́êś t̂h́ât́ r̂éq̂úêśt̂ t́ô śêńd̂ ńôt́îf́îćât́îón̂ś ŵít̂h́ôút̂ ćôńt̂éx̂t́. Ĉón̂śîd́êŕ t̂ýîńĝ t́ĥé r̂éq̂úêśt̂ t́ô úŝér̂ ǵêśt̂úr̂éŝ ín̂śt̂éâd́. [L̂éâŕn̂ ḿôŕê áb̂óût́ r̂éŝṕôńŝíb̂ĺŷ ǵêt́t̂ín̂ǵ p̂ér̂ḿîśŝíôń f̂ór̂ ńôt́îf́îćât́îón̂ś](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "R̂éq̂úêśt̂ś t̂h́ê ńôt́îf́îćât́îón̂ ṕêŕm̂íŝśîón̂ ón̂ ṕâǵê ĺôád̂"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Âv́ôíd̂ś r̂éq̂úêśt̂ín̂ǵ t̂h́ê ńôt́îf́îćât́îón̂ ṕêŕm̂íŝśîón̂ ón̂ ṕâǵê ĺôád̂"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "P̂ŕêv́êńt̂ín̂ǵ îńp̂út̂ ṕâśt̂ín̂ǵ îś â b́âd́ p̂ŕâćt̂íĉé f̂ór̂ t́ĥé ÛX́, âńd̂ ẃêák̂én̂ś ŝéĉúr̂ít̂ý b̂ý b̂ĺôćk̂ín̂ǵ p̂áŝśŵór̂d́ m̂án̂áĝér̂ś.[L̂éâŕn̂ ḿôŕê áb̂óût́ ûśêŕ-f̂ŕîén̂d́l̂ý îńp̂út̂ f́îél̂d́ŝ](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "P̂ŕêv́êńt̂ś ûśêŕŝ f́r̂óm̂ ṕâśt̂ín̂ǵ îńt̂ó îńp̂út̂ f́îél̂d́ŝ"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Âĺl̂óŵś ûśêŕŝ t́ô ṕâśt̂é îńt̂ó îńp̂út̂ f́îél̂d́ŝ"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "P̂ŕôt́ôćôĺ"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "ĤT́T̂Ṕ/2 ôf́f̂ér̂ś m̂án̂ý b̂én̂éf̂ít̂ś ôv́êŕ ĤT́T̂Ṕ/1.1, îńĉĺûd́îńĝ b́îńâŕŷ h́êád̂ér̂ś âńd̂ ḿûĺt̂íp̂ĺêx́îńĝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ H́T̂T́P̂/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 r̂éq̂úêśt̂ ńôt́ ŝér̂v́êd́ v̂íâ H́T̂T́P̂/2}\n    other {# ŕêq́ûéŝt́ŝ ńôt́ ŝér̂v́êd́ v̂íâ H́T̂T́P̂/2}\n    }"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Ûśê H́T̂T́P̂/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Ĉón̂śîd́êŕ m̂ár̂ḱîńĝ ýôúr̂ t́ôúĉh́ âńd̂ ẃĥéêĺ êv́êńt̂ ĺîśt̂én̂ér̂ś âś `passive` t̂ó îḿp̂ŕôv́ê ýôúr̂ ṕâǵê'ś ŝćr̂ól̂ĺ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ád̂óp̂t́îńĝ ṕâśŝív̂é êv́êńt̂ ĺîśt̂én̂ér̂ś](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "D̂óêś n̂ót̂ úŝé p̂áŝśîv́ê ĺîśt̂én̂ér̂ś t̂ó îḿp̂ŕôv́ê śĉŕôĺl̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Ûśêś p̂áŝśîv́ê ĺîśt̂én̂ér̂ś t̂ó îḿp̂ŕôv́ê śĉŕôĺl̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê"}, "core/audits/errors-in-console.js | description": {"message": "Êŕr̂ór̂ś l̂óĝǵêd́ t̂ó t̂h́ê ćôńŝól̂é îńd̂íĉát̂é ûńr̂éŝól̂v́êd́ p̂ŕôb́l̂ém̂ś. T̂h́êý ĉán̂ ćôḿê f́r̂óm̂ ńêt́ŵór̂ḱ r̂éq̂úêśt̂ f́âíl̂úr̂éŝ án̂d́ ôt́ĥér̂ b́r̂óŵśêŕ ĉón̂ćêŕn̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́îś êŕr̂ór̂ś îń ĉón̂śôĺê d́îáĝńôśt̂íĉ áûd́ît́](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "B̂ŕôẃŝér̂ ér̂ŕôŕŝ ẃêŕê ĺôǵĝéd̂ t́ô t́ĥé ĉón̂śôĺê"}, "core/audits/errors-in-console.js | title": {"message": "N̂ó b̂ŕôẃŝér̂ ér̂ŕôŕŝ ĺôǵĝéd̂ t́ô t́ĥé ĉón̂śôĺê"}, "core/audits/font-display.js | description": {"message": "L̂év̂ér̂áĝé t̂h́ê `font-display` ĆŜŚ f̂éât́ûŕê t́ô én̂śûŕê t́êx́t̂ íŝ úŝér̂-v́îśîb́l̂é ŵh́îĺê ẃêb́f̂ón̂t́ŝ ár̂é l̂óâd́îńĝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Êńŝúr̂é t̂éx̂t́ r̂ém̂áîńŝ v́îśîb́l̂é d̂úr̂ín̂ǵ ŵéb̂f́ôńt̂ ĺôád̂"}, "core/audits/font-display.js | title": {"message": "Âĺl̂ t́êx́t̂ ŕêḿâín̂ś v̂íŝíb̂ĺê d́ûŕîńĝ ẃêb́f̂ón̂t́ l̂óâd́ŝ"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin, plural, =1 {L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô áût́ôḿât́îćâĺl̂ý ĉh́êćk̂ t́ĥé `font-display` v̂ál̂úê f́ôŕ t̂h́ê ór̂íĝín̂ {fontOrigin}.} other {Ĺîǵĥt́ĥóûśê ẃâś ûńâb́l̂é t̂ó âút̂óm̂át̂íĉál̂ĺŷ ćĥéĉḱ t̂h́ê `font-display` v́âĺûéŝ f́ôŕ t̂h́ê ór̂íĝín̂ {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Âśp̂éĉt́ R̂át̂íô (Áĉt́ûál̂)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Âśp̂éĉt́ R̂át̂íô (D́îśp̂ĺâýêd́)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Îḿâǵê d́îśp̂ĺâý d̂ím̂én̂śîón̂ś ŝh́ôúl̂d́ m̂át̂ćĥ ńât́ûŕâĺ âśp̂éĉt́ r̂át̂íô. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ím̂áĝé âśp̂éĉt́ r̂át̂íô](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "D̂íŝṕl̂áŷś îḿâǵêś ŵít̂h́ îńĉór̂ŕêćt̂ áŝṕêćt̂ ŕât́îó"}, "core/audits/image-aspect-ratio.js | title": {"message": "D̂íŝṕl̂áŷś îḿâǵêś ŵít̂h́ ĉór̂ŕêćt̂ áŝṕêćt̂ ŕât́îó"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Âćt̂úâĺ ŝíẑé"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "D̂íŝṕl̂áŷéd̂ śîźê"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Êx́p̂éĉt́êd́ ŝíẑé"}, "core/audits/image-size-responsive.js | description": {"message": "Îḿâǵê ńât́ûŕâĺ d̂ím̂én̂śîón̂ś ŝh́ôúl̂d́ b̂é p̂ŕôṕôŕt̂íôńâĺ t̂ó t̂h́ê d́îśp̂ĺâý ŝíẑé âńd̂ t́ĥé p̂íx̂él̂ ŕât́îó t̂ó m̂áx̂ím̂íẑé îḿâǵê ćl̂ár̂ít̂ý. [L̂éâŕn̂ h́ôẃ t̂ó p̂ŕôv́îd́ê ŕêśp̂ón̂śîv́ê ím̂áĝéŝ](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Ŝér̂v́êś îḿâǵêś ŵít̂h́ l̂óŵ ŕêśôĺût́îón̂"}, "core/audits/image-size-responsive.js | title": {"message": "Ŝér̂v́êś îḿâǵêś ŵít̂h́ âṕp̂ŕôṕr̂íât́ê ŕêśôĺût́îón̂"}, "core/audits/installable-manifest.js | already-installed": {"message": "T̂h́ê áp̂ṕ îś âĺr̂éâd́ŷ ín̂śt̂ál̂ĺêd́"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Ĉóûĺd̂ ńôt́ d̂óŵńl̂óâd́ â ŕêq́ûír̂éd̂ íĉón̂ f́r̂óm̂ t́ĥé m̂án̂íf̂éŝt́"}, "core/audits/installable-manifest.js | columnValue": {"message": "F̂áîĺûŕê ŕêáŝón̂"}, "core/audits/installable-manifest.js | description": {"message": "Ŝér̂v́îćê ẃôŕk̂ér̂ íŝ t́ĥé t̂éĉh́n̂ól̂óĝý t̂h́ât́ êńâb́l̂éŝ ýôúr̂ áp̂ṕ t̂ó ûśê ḿâńŷ Ṕr̂óĝŕêśŝív̂é Ŵéb̂ Áp̂ṕ f̂éât́ûŕêś, ŝúĉh́ âś ôf́f̂ĺîńê, ád̂d́ t̂ó ĥóm̂éŝćr̂éêń, âńd̂ ṕûśĥ ńôt́îf́îćât́îón̂ś. Ŵít̂h́ p̂ŕôṕêŕ ŝér̂v́îćê ẃôŕk̂ér̂ án̂d́ m̂án̂íf̂éŝt́ îḿp̂ĺêḿêńt̂át̂íôńŝ, b́r̂óŵśêŕŝ ćâń p̂ŕôáĉt́îv́êĺŷ ṕr̂óm̂ṕt̂ úŝér̂ś t̂ó âd́d̂ ýôúr̂ áp̂ṕ t̂ó t̂h́êír̂ h́ôḿêśĉŕêén̂, ẃĥíĉh́ ĉán̂ ĺêád̂ t́ô h́îǵĥér̂ én̂ǵâǵêḿêńt̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ḿâńîf́êśt̂ ín̂śt̂ál̂ĺâb́îĺît́ŷ ŕêq́ûír̂ém̂én̂t́ŝ](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 r̂éâśôń}\n    other {# r̂éâśôńŝ}\n    }"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Ŵéb̂ áp̂ṕ m̂án̂íf̂éŝt́ ôŕ ŝér̂v́îćê ẃôŕk̂ér̂ d́ô ńôt́ m̂éêt́ t̂h́ê ín̂śt̂ál̂ĺâb́îĺît́ŷ ŕêq́ûír̂ém̂én̂t́ŝ"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "T̂h́ê Ṕl̂áŷ Śt̂ór̂é âṕp̂ ÚR̂Ĺ âńd̂ Ṕl̂áŷ Śt̂ór̂é ÎD́ d̂ó n̂ót̂ ḿât́ĉh́"}, "core/audits/installable-manifest.js | in-incognito": {"message": "P̂áĝé îś l̂óâd́êd́ îń âń îńĉóĝńît́ô ẃîńd̂óŵ"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "M̂án̂íf̂éŝt́ `display` p̂ŕôṕêŕt̂ý m̂úŝt́ b̂é ôńê óf̂ `standalone`, `fullscreen`, ór̂ `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "M̂án̂íf̂éŝt́ ĉón̂t́âín̂ś 'd̂íŝṕl̂áŷ_óv̂ér̂ŕîd́ê' f́îél̂d́, âńd̂ t́ĥé f̂ír̂śt̂ śûṕp̂ór̂t́êd́ d̂íŝṕl̂áŷ ḿôd́ê ḿûśt̂ b́ê ón̂é ôf́ 'ŝt́âńd̂ál̂ón̂é', 'f̂úl̂ĺŝćr̂éêń', ôŕ 'm̂ín̂ím̂ál̂-úî'"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "M̂án̂íf̂éŝt́ ĉóûĺd̂ ńôt́ b̂é f̂ét̂ćĥéd̂, íŝ ém̂ṕt̂ý, ôŕ ĉóûĺd̂ ńôt́ b̂é p̂ár̂śêd́"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "M̂án̂íf̂éŝt́ ÛŔL̂ ćĥán̂ǵêd́ ŵh́îĺê t́ĥé m̂án̂íf̂éŝt́ ŵáŝ b́êín̂ǵ f̂ét̂ćĥéd̂."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "M̂án̂íf̂éŝt́ d̂óêś n̂ót̂ ćôńt̂áîń â `name` ór̂ `short_name` f́îél̂d́"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "M̂án̂íf̂éŝt́ d̂óêś n̂ót̂ ćôńt̂áîń â śûít̂áb̂ĺê íĉón̂ - ṔN̂Ǵ, ŜV́Ĝ ór̂ Ẃêb́P̂ f́ôŕm̂át̂ óf̂ át̂ ĺêáŝt́ {value0} p̂x́ îś r̂éq̂úîŕêd́, t̂h́ê śîźêś ât́t̂ŕîb́ût́ê ḿûśt̂ b́ê śêt́, âńd̂ t́ĥé p̂úr̂ṕôśê át̂t́r̂íb̂út̂é, îf́ ŝét̂, ḿûśt̂ ín̂ćl̂úd̂é \"âńŷ\"."}, "core/audits/installable-manifest.js | manifest-parsing-or-network-error": {"message": "M̂án̂íf̂éŝt́ ĉóûĺd̂ ńôt́ b̂é f̂ét̂ćĥéd̂, íŝ ém̂ṕt̂ý, ôŕ ĉóûĺd̂ ńôt́ b̂é p̂ár̂śêd́"}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "N̂ó ŝúp̂ṕl̂íêd́ îćôń îś ât́ l̂éâśt̂ {value0} ṕx̂ śq̂úâŕê ín̂ ṔN̂Ǵ, ŜV́Ĝ ór̂ Ẃêb́P̂ f́ôŕm̂át̂, ẃît́ĥ t́ĥé p̂úr̂ṕôśê át̂t́r̂íb̂út̂é ûńŝét̂ ór̂ śêt́ t̂ó \"âńŷ\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "D̂óŵńl̂óâd́êd́ îćôń ŵáŝ ém̂ṕt̂ý ôŕ ĉór̂ŕûṕt̂éd̂"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "N̂ó P̂ĺâý ŝt́ôŕê ÍD̂ ṕr̂óv̂íd̂éd̂"}, "core/audits/installable-manifest.js | no-manifest": {"message": "P̂áĝé ĥáŝ ńô ḿâńîf́êśt̂ <ĺîńk̂> ÚR̂Ĺ"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Ĉóûĺd̂ ńôt́ ĉh́êćk̂ śêŕv̂íĉé ŵór̂ḱêŕ ŵít̂h́ôút̂ á 'ŝt́âŕt̂_úr̂ĺ' f̂íêĺd̂ ín̂ t́ĥé m̂án̂íf̂éŝt́"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Îńŝt́âĺl̂áb̂íl̂ít̂ý êŕr̂ór̂ íd̂ '{errorId}' íŝ ńôt́ r̂éĉóĝńîźêd́"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "P̂áĝé îś n̂ót̂ śêŕv̂éd̂ f́r̂óm̂ á ŝéĉúr̂é ôŕîǵîń"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "P̂áĝé îś n̂ót̂ ĺôád̂éd̂ ín̂ t́ĥé m̂áîń f̂ŕâḿê"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "P̂áĝé d̂óêś n̂ót̂ ẃôŕk̂ óf̂f́l̂ín̂é"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "P̂ẂÂ h́âś b̂éêń ûńîńŝt́âĺl̂éd̂ án̂d́ îńŝt́âĺl̂áb̂íl̂ít̂ý ĉh́êćk̂ś r̂éŝét̂t́îńĝ."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "T̂h́ê śp̂éĉíf̂íêd́ âṕp̂ĺîćât́îón̂ ṕl̂át̂f́ôŕm̂ íŝ ńôt́ ŝúp̂ṕôŕt̂éd̂ ón̂ Án̂d́r̂óîd́"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "M̂án̂íf̂éŝt́ ŝṕêćîf́îéŝ ṕr̂éf̂ér̂_ŕêĺât́êd́_âṕp̂ĺîćât́îón̂ś: t̂ŕûé"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "p̂ŕêf́êŕ_r̂él̂át̂éd̂_áp̂ṕl̂íĉát̂íôńŝ íŝ ón̂ĺŷ śûṕp̂ór̂t́êd́ ôń Ĉh́r̂óm̂é B̂ét̂á âńd̂ Śt̂áb̂ĺê ćĥán̂ńêĺŝ ón̂ Án̂d́r̂óîd́."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "L̂íĝh́t̂h́ôúŝé ĉóûĺd̂ ńôt́ d̂ét̂ér̂ḿîńê íf̂ t́ĥé p̂áĝé îś îńŝt́âĺl̂áb̂ĺê. Ṕl̂éâśê t́r̂ý ŵít̂h́ â ńêẃêŕ v̂ér̂śîón̂ óf̂ Ćĥŕôḿê."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "M̂án̂íf̂éŝt́ ŝt́âŕt̂ ÚR̂Ĺ îś n̂ót̂ v́âĺîd́"}, "core/audits/installable-manifest.js | title": {"message": "Ŵéb̂ áp̂ṕ m̂án̂íf̂éŝt́ âńd̂ śêŕv̂íĉé ŵór̂ḱêŕ m̂éêt́ t̂h́ê ín̂śt̂ál̂ĺâb́îĺît́ŷ ŕêq́ûír̂ém̂én̂t́ŝ"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Â ÚR̂Ĺ îń t̂h́ê ḿâńîf́êśt̂ ćôńt̂áîńŝ á ûśêŕn̂ám̂é, p̂áŝśŵór̂d́, ôŕ p̂ór̂t́"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "P̂áĝé d̂óêś n̂ót̂ ẃôŕk̂ óf̂f́l̂ín̂é. T̂h́ê ṕâǵê ẃîĺl̂ ńôt́ b̂é r̂éĝár̂d́êd́ âś îńŝt́âĺl̂áb̂ĺê áf̂t́êŕ Ĉh́r̂óm̂é 93, ŝt́âb́l̂é r̂él̂éâśê Áûǵûśt̂ 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Âĺl̂óŵéd̂"}, "core/audits/is-on-https.js | blocked": {"message": "B̂ĺôćk̂éd̂"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Îńŝéĉúr̂é ÛŔL̂"}, "core/audits/is-on-https.js | columnResolution": {"message": "R̂éq̂úêśt̂ Ŕêśôĺût́îón̂"}, "core/audits/is-on-https.js | description": {"message": "Âĺl̂ śît́êś ŝh́ôúl̂d́ b̂é p̂ŕôt́êćt̂éd̂ ẃît́ĥ H́T̂T́P̂Ś, êv́êń ôńêś t̂h́ât́ d̂ón̂'t́ ĥán̂d́l̂é ŝén̂śît́îv́ê d́ât́â. T́ĥíŝ ín̂ćl̂úd̂éŝ áv̂óîd́îńĝ [ḿîx́êd́ ĉón̂t́êńt̂](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), ẃĥér̂é ŝóm̂é r̂éŝóûŕĉéŝ ár̂é l̂óâd́êd́ ôv́êŕ ĤT́T̂Ṕ d̂éŝṕît́ê t́ĥé îńît́îál̂ ŕêq́ûéŝt́ b̂éîńĝ śêŕv̂éd̂ óv̂ér̂ H́T̂T́P̂Ś. ĤT́T̂ṔŜ ṕr̂év̂én̂t́ŝ ín̂t́r̂úd̂ér̂ś f̂ŕôḿ t̂ám̂ṕêŕîńĝ ẃît́ĥ ór̂ ṕâśŝív̂él̂ý l̂íŝt́êńîńĝ ín̂ ón̂ t́ĥé ĉóm̂ḿûńîćât́îón̂ś b̂ét̂ẃêén̂ ýôúr̂ áp̂ṕ âńd̂ ýôúr̂ úŝér̂ś, âńd̂ íŝ á p̂ŕêŕêq́ûíŝít̂é f̂ór̂ H́T̂T́P̂/2 án̂d́ m̂án̂ý n̂éŵ ẃêb́ p̂ĺât́f̂ór̂ḿ ÂṔÎś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĤT́T̂ṔŜ](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 îńŝéĉúr̂é r̂éq̂úêśt̂ f́ôún̂d́}\n    other {# îńŝéĉúr̂é r̂éq̂úêśt̂ś f̂óûńd̂}\n    }"}, "core/audits/is-on-https.js | failureTitle": {"message": "D̂óêś n̂ót̂ úŝé ĤT́T̂ṔŜ"}, "core/audits/is-on-https.js | title": {"message": "Ûśêś ĤT́T̂ṔŜ"}, "core/audits/is-on-https.js | upgraded": {"message": "Âút̂óm̂át̂íĉál̂ĺŷ úp̂ǵr̂ád̂éd̂ t́ô H́T̂T́P̂Ś"}, "core/audits/is-on-https.js | warning": {"message": "Âĺl̂óŵéd̂ ẃît́ĥ ẃâŕn̂ín̂ǵ"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% ôf́ L̂ĆP̂"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "P̂h́âśê"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "T̂ím̂ín̂ǵ"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "T̂h́îś îś t̂h́ê ĺâŕĝéŝt́ ĉón̂t́êńt̂f́ûĺ êĺêḿêńt̂ ṕâín̂t́êd́ ŵít̂h́îń t̂h́ê v́îéŵṕôŕt̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ êĺêḿêńt̂](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "L̂óâd́ D̂él̂áŷ"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "L̂óâd́ T̂ím̂é"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "R̂én̂d́êŕ D̂él̂áŷ"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "T̂T́F̂B́"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ êĺêḿêńt̂"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "L̂áŷóût́ ŝh́îf́t̂ ím̂ṕâćt̂"}, "core/audits/layout-shift-elements.js | description": {"message": "T̂h́êśê D́ÔḾ êĺêḿêńt̂ś ŵér̂é m̂óŝt́ âf́f̂éĉt́êd́ b̂ý l̂áŷóût́ ŝh́îf́t̂ś. Ŝóm̂é l̂áŷóût́ ŝh́îf́t̂ś m̂áŷ ńôt́ b̂é îńĉĺûd́êd́ îń t̂h́ê ĆL̂Ś m̂ét̂ŕîć v̂ál̂úê d́ûé t̂ó [ŵín̂d́ôẃîńĝ](https://web.dev/articles/cls#what_is_cls). [Ĺêár̂ń ĥóŵ t́ô ím̂ṕr̂óv̂é ĈĹŜ](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Âv́ôíd̂ ĺâŕĝé l̂áŷóût́ ŝh́îf́t̂ś"}, "core/audits/layout-shifts.js | columnScore": {"message": "L̂áŷóût́ ŝh́îf́t̂ śĉór̂é"}, "core/audits/layout-shifts.js | description": {"message": "T̂h́êśê ár̂é t̂h́ê ĺâŕĝéŝt́ l̂áŷóût́ ŝh́îf́t̂ś ôb́ŝér̂v́êd́ ôń t̂h́ê ṕâǵê. Éâćĥ t́âb́l̂é ît́êḿ r̂ép̂ŕêśêńt̂ś â śîńĝĺê ĺâýôút̂ śĥíf̂t́, âńd̂ śĥóŵś t̂h́ê él̂ém̂én̂t́ t̂h́ât́ ŝh́îf́t̂éd̂ t́ĥé m̂óŝt́. B̂él̂óŵ éâćĥ ít̂ém̂ ár̂é p̂óŝśîb́l̂é r̂óôt́ ĉáûśêś t̂h́ât́ l̂éd̂ t́ô t́ĥé l̂áŷóût́ ŝh́îf́t̂. Śôḿê óf̂ t́ĥéŝé l̂áŷóût́ ŝh́îf́t̂ś m̂áŷ ńôt́ b̂é îńĉĺûd́êd́ îń t̂h́ê ĆL̂Ś m̂ét̂ŕîć v̂ál̂úê d́ûé t̂ó [ŵín̂d́ôẃîńĝ](https://web.dev/articles/cls#what_is_cls). [Ĺêár̂ń ĥóŵ t́ô ím̂ṕr̂óv̂é ĈĹŜ](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount, plural, =1 {1 l̂áŷóût́ ŝh́îf́t̂ f́ôún̂d́} other {# l̂áŷóût́ ŝh́îf́t̂ś f̂óûńd̂}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Ŵéb̂ f́ôńt̂ ĺôád̂éd̂"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Îńĵéĉt́êd́ îf́r̂ám̂é"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Â ĺât́ê ńêt́ŵór̂ḱ r̂éq̂úêśt̂ ád̂j́ûśt̂éd̂ t́ĥé p̂áĝé l̂áŷóût́"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "M̂éd̂íâ él̂ém̂én̂t́ l̂áĉḱîńĝ án̂ éx̂ṕl̂íĉít̂ śîźê"}, "core/audits/layout-shifts.js | title": {"message": "Âv́ôíd̂ ĺâŕĝé l̂áŷóût́ ŝh́îf́t̂ś"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Âb́ôv́ê-t́ĥé-f̂ól̂d́ îḿâǵêś t̂h́ât́ âŕê ĺâźîĺŷ ĺôád̂éd̂ ŕêńd̂ér̂ ĺât́êŕ îń t̂h́ê ṕâǵê ĺîf́êćŷćl̂é, ŵh́îćĥ ćâń d̂él̂áŷ t́ĥé l̂ár̂ǵêśt̂ ćôńt̂én̂t́f̂úl̂ ṕâín̂t́. [L̂éâŕn̂ ḿôŕê áb̂óût́ ôṕt̂ím̂ál̂ ĺâźŷ ĺôád̂ín̂ǵ](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ îḿâǵê ẃâś l̂áẑíl̂ý l̂óâd́êd́"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ îḿâǵê ẃâś n̂ót̂ ĺâźîĺŷ ĺôád̂éd̂"}, "core/audits/long-tasks.js | description": {"message": "L̂íŝt́ŝ t́ĥé l̂ón̂ǵêśt̂ t́âśk̂ś ôń t̂h́ê ḿâín̂ t́ĥŕêád̂, úŝéf̂úl̂ f́ôŕ îd́êńt̂íf̂ýîńĝ ẃôŕŝt́ ĉón̂t́r̂íb̂út̂ór̂ś t̂ó îńp̂út̂ d́êĺâý. [L̂éâŕn̂ h́ôẃ t̂ó âv́ôíd̂ ĺôńĝ ḿâín̂-t́ĥŕêád̂ t́âśk̂ś](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount, plural,\n  =1 {# l̂ón̂ǵ t̂áŝḱ f̂óûńd̂}\n  other {# ĺôńĝ t́âśk̂ś f̂óûńd̂}\n  }"}, "core/audits/long-tasks.js | title": {"message": "Âv́ôíd̂ ĺôńĝ ḿâín̂-t́ĥŕêád̂ t́âśk̂ś"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Ĉát̂éĝór̂ý"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ t̂h́ê t́îḿê śp̂én̂t́ p̂ár̂śîńĝ, ćôḿp̂íl̂ín̂ǵ âńd̂ éx̂éĉút̂ín̂ǵ ĴŚ. Ŷóû ḿâý f̂ín̂d́ d̂él̂ív̂ér̂ín̂ǵ ŝḿâĺl̂ér̂ J́Ŝ ṕâýl̂óâd́ŝ h́êĺp̂ś ŵít̂h́ t̂h́îś. [L̂éâŕn̂ h́ôẃ t̂ó m̂ín̂ím̂íẑé m̂áîń-t̂h́r̂éâd́ ŵór̂ḱ](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "M̂ín̂ím̂íẑé m̂áîń-t̂h́r̂éâd́ ŵór̂ḱ"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "M̂ín̂ím̂íẑéŝ ḿâín̂-t́ĥŕêád̂ ẃôŕk̂"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "T̂ó r̂éâćĥ t́ĥé m̂óŝt́ n̂úm̂b́êŕ ôf́ ûśêŕŝ, śît́êś ŝh́ôúl̂d́ ŵór̂ḱ âćr̂óŝś êv́êŕŷ ḿâj́ôŕ b̂ŕôẃŝér̂. [Ĺêár̂ń âb́ôút̂ ćr̂óŝś-b̂ŕôẃŝér̂ ćôḿp̂át̂íb̂íl̂ít̂ý](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Ŝít̂é ŵór̂ḱŝ ćr̂óŝś-b̂ŕôẃŝér̂"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Êńŝúr̂é îńd̂ív̂íd̂úâĺ p̂áĝéŝ ár̂é d̂éêṕ l̂ín̂ḱâb́l̂é v̂íâ ÚR̂Ĺ âńd̂ t́ĥát̂ ÚR̂Ĺŝ ár̂é ûńîq́ûé f̂ór̂ t́ĥé p̂úr̂ṕôśê óf̂ śĥár̂éâb́îĺît́ŷ ón̂ śôćîál̂ ḿêd́îá. [L̂éâŕn̂ ḿôŕê áb̂óût́ p̂ŕôv́îd́îńĝ d́êép̂ ĺîńk̂ś](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Êáĉh́ p̂áĝé ĥáŝ á ÛŔL̂"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "T̂ŕâńŝít̂íôńŝ śĥóûĺd̂ f́êél̂ śn̂áp̂ṕŷ áŝ ýôú t̂áp̂ ár̂óûńd̂, év̂én̂ ón̂ á ŝĺôẃ n̂ét̂ẃôŕk̂. T́ĥíŝ éx̂ṕêŕîén̂ćê íŝ ḱêý t̂ó â úŝér̂'ś p̂ér̂ćêṕt̂íôń ôf́ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕâǵê t́r̂án̂śît́îón̂ś](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "P̂áĝé t̂ŕâńŝít̂íôńŝ d́ôń't̂ f́êél̂ ĺîḱê t́ĥéŷ b́l̂óĉḱ ôń t̂h́ê ńêt́ŵór̂ḱ"}, "core/audits/maskable-icon.js | description": {"message": "Â ḿâśk̂áb̂ĺê íĉón̂ én̂śûŕêś t̂h́ât́ t̂h́ê ím̂áĝé f̂íl̂ĺŝ t́ĥé êńt̂ír̂é ŝh́âṕê ẃît́ĥóût́ b̂éîńĝ ĺêt́t̂ér̂b́ôx́êd́ ŵh́êń îńŝt́âĺl̂ín̂ǵ t̂h́ê áp̂ṕ ôń â d́êv́îćê. [Ĺêár̂ń âb́ôút̂ ḿâśk̂áb̂ĺê ḿâńîf́êśt̂ íĉón̂ś](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "M̂án̂íf̂éŝt́ d̂óêśn̂'t́ ĥáv̂é â ḿâśk̂áb̂ĺê íĉón̂"}, "core/audits/maskable-icon.js | title": {"message": "M̂án̂íf̂éŝt́ ĥáŝ á m̂áŝḱâb́l̂é îćôń"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Ĉúm̂úl̂át̂ív̂é L̂áŷóût́ Ŝh́îf́t̂ ḿêáŝúr̂éŝ t́ĥé m̂óv̂ém̂én̂t́ ôf́ v̂íŝíb̂ĺê él̂ém̂én̂t́ŝ ẃît́ĥín̂ t́ĥé v̂íêẃp̂ór̂t́. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê Ćûḿûĺât́îv́ê Ĺâýôút̂ Śĥíf̂t́ m̂ét̂ŕîć](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "F̂ír̂śt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ m̂ár̂ḱŝ t́ĥé t̂ím̂é ât́ ŵh́îćĥ t́ĥé f̂ír̂śt̂ t́êx́t̂ ór̂ ím̂áĝé îś p̂áîńt̂éd̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé F̂ír̂śt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ m̂ét̂ŕîć](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "F̂ír̂śt̂ Ḿêán̂ín̂ǵf̂úl̂ Ṕâín̂t́ m̂éâśûŕêś ŵh́êń t̂h́ê ṕr̂ím̂ár̂ý ĉón̂t́êńt̂ óf̂ á p̂áĝé îś v̂íŝíb̂ĺê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé F̂ír̂śt̂ Ḿêán̂ín̂ǵf̂úl̂ Ṕâín̂t́ m̂ét̂ŕîć](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Îńt̂ér̂áĉt́îón̂ t́ô Ńêx́t̂ Ṕâín̂t́ m̂éâśûŕêś p̂áĝé r̂éŝṕôńŝív̂én̂éŝś, ĥóŵ ĺôńĝ ít̂ t́âḱêś t̂h́ê ṕâǵê t́ô v́îśîb́l̂ý r̂éŝṕôńd̂ t́ô úŝér̂ ín̂ṕût́. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê Ín̂t́êŕâćt̂íôń t̂ó N̂éx̂t́ P̂áîńt̂ ḿêt́r̂íĉ](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê íŝ t́ĥé âḿôún̂t́ ôf́ t̂ím̂é ît́ t̂ák̂éŝ f́ôŕ t̂h́ê ṕâǵê t́ô b́êćôḿê f́ûĺl̂ý îńt̂ér̂áĉt́îv́ê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê ḿêt́r̂íĉ](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ m̂ár̂ḱŝ t́ĥé t̂ím̂é ât́ ŵh́îćĥ t́ĥé l̂ár̂ǵêśt̂ t́êx́t̂ ór̂ ím̂áĝé îś p̂áîńt̂éd̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ m̂ét̂ŕîć](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "T̂h́ê ḿâx́îḿûḿ p̂ót̂én̂t́îál̂ F́îŕŝt́ Îńp̂út̂ D́êĺâý t̂h́ât́ ŷóûŕ ûśêŕŝ ćôúl̂d́ êx́p̂ér̂íêńĉé îś t̂h́ê d́ûŕât́îón̂ óf̂ t́ĥé l̂ón̂ǵêśt̂ t́âśk̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé M̂áx̂ím̂úm̂ Ṕôt́êńt̂íâĺ F̂ír̂śt̂ Ín̂ṕût́ D̂él̂áŷ ḿêt́r̂íĉ](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Ŝṕêéd̂ Ín̂d́êx́ ŝh́ôẃŝ h́ôẃ q̂úîćk̂ĺŷ t́ĥé ĉón̂t́êńt̂ś ôf́ â ṕâǵê ár̂é v̂íŝíb̂ĺŷ ṕôṕûĺât́êd́. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê Śp̂éêd́ Îńd̂éx̂ ḿêt́r̂íĉ](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Ŝúm̂ óf̂ ál̂ĺ t̂ím̂é p̂ér̂íôd́ŝ b́êt́ŵéêń F̂ĆP̂ án̂d́ T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê, ẃĥén̂ t́âśk̂ ĺêńĝt́ĥ éx̂ćêéd̂éd̂ 50ḿŝ, éx̂ṕr̂éŝśêd́ îń m̂íl̂ĺîśêćôńd̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê T́ôt́âĺ B̂ĺôćk̂ín̂ǵ T̂ím̂é m̂ét̂ŕîć](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "N̂ét̂ẃôŕk̂ ŕôún̂d́ t̂ŕîṕ t̂ím̂éŝ (ŔT̂T́) ĥáv̂é â ĺâŕĝé îḿp̂áĉt́ ôń p̂ér̂f́ôŕm̂án̂ćê. Íf̂ t́ĥé R̂T́T̂ t́ô án̂ ór̂íĝín̂ íŝ h́îǵĥ, ít̂'ś âń îńd̂íĉát̂íôń t̂h́ât́ ŝér̂v́êŕŝ ćl̂óŝér̂ t́ô t́ĥé ûśêŕ ĉóûĺd̂ ím̂ṕr̂óv̂é p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé R̂óûńd̂ T́r̂íp̂ T́îḿê](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "N̂ét̂ẃôŕk̂ Ŕôún̂d́ T̂ŕîṕ T̂ím̂éŝ"}, "core/audits/network-server-latency.js | description": {"message": "Ŝér̂v́êŕ l̂át̂én̂ćîéŝ ćâń îḿp̂áĉt́ ŵéb̂ ṕêŕf̂ór̂ḿâńĉé. Îf́ t̂h́ê śêŕv̂ér̂ ĺât́êńĉý ôf́ âń ôŕîǵîń îś ĥíĝh́, ît́'ŝ án̂ ín̂d́îćât́îón̂ t́ĥé ŝér̂v́êŕ îś ôv́êŕl̂óâd́êd́ ôŕ ĥáŝ ṕôór̂ b́âćk̂én̂d́ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é âb́ôút̂ śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Ŝér̂v́êŕ B̂áĉḱêńd̂ Ĺât́êńĉíêś"}, "core/audits/no-unload-listeners.js | description": {"message": "T̂h́ê `unload` év̂én̂t́ d̂óêś n̂ót̂ f́îŕê ŕêĺîáb̂ĺŷ án̂d́ l̂íŝt́êńîńĝ f́ôŕ ît́ ĉán̂ ṕr̂év̂én̂t́ b̂ŕôẃŝér̂ óp̂t́îḿîźât́îón̂ś l̂ík̂é t̂h́ê B́âćk̂-F́ôŕŵár̂d́ Ĉáĉh́ê. Úŝé `pagehide` ôŕ `visibilitychange` êv́êńt̂ś îńŝt́êád̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ún̂ĺôád̂ év̂én̂t́ l̂íŝt́êńêŕŝ](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "R̂éĝíŝt́êŕŝ án̂ `unload` ĺîśt̂én̂ér̂"}, "core/audits/no-unload-listeners.js | title": {"message": "Âv́ôíd̂ś `unload` êv́êńt̂ ĺîśt̂én̂ér̂ś"}, "core/audits/non-composited-animations.js | description": {"message": "Âńîḿât́îón̂ś ŵh́îćĥ ár̂é n̂ót̂ ćôḿp̂óŝít̂éd̂ ćâń b̂é ĵán̂ḱŷ án̂d́ îńĉŕêáŝé ĈĹŜ. [Ĺêár̂ń ĥóŵ t́ô áv̂óîd́ n̂ón̂-ćôḿp̂óŝít̂éd̂ án̂ím̂át̂íôńŝ](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount, plural,\n  =1 {# âńîḿât́êd́ êĺêḿêńt̂ f́ôún̂d́}\n  other {# âńîḿât́êd́ êĺêḿêńt̂ś f̂óûńd̂}\n  }"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "F̂íl̂t́êŕ-r̂él̂át̂éd̂ ṕr̂óp̂ér̂t́ŷ ḿâý m̂óv̂é p̂íx̂él̂ś"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "T̂ár̂ǵêt́ ĥáŝ án̂ót̂h́êŕ âńîḿât́îón̂ ẃĥíĉh́ îś îńĉóm̂ṕât́îb́l̂é"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Êf́f̂éĉt́ ĥáŝ ćôḿp̂óŝít̂é m̂ód̂é ôt́ĥér̂ t́ĥán̂ \"ŕêṕl̂áĉé\""}, "core/audits/non-composited-animations.js | title": {"message": "Âv́ôíd̂ ńôń-ĉóm̂ṕôśît́êd́ âńîḿât́îón̂ś"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "T̂ŕâńŝf́ôŕm̂-ŕêĺât́êd́ p̂ŕôṕêŕt̂ý d̂ép̂én̂d́ŝ ón̂ b́ôx́ ŝíẑé"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount, plural,\n    =1 {Ûńŝúp̂ṕôŕt̂éd̂ ĆŜŚ P̂ŕôṕêŕt̂ý: {properties}}\n    other {Ûńŝúp̂ṕôŕt̂éd̂ ĆŜŚ P̂ŕôṕêŕt̂íêś: {properties}}\n  }"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Êf́f̂éĉt́ ĥáŝ ún̂śûṕp̂ór̂t́êd́ t̂ím̂ín̂ǵ p̂ár̂ám̂ét̂ér̂ś"}, "core/audits/performance-budget.js | description": {"message": "K̂éêṕ t̂h́ê q́ûán̂t́ît́ŷ án̂d́ ŝíẑé ôf́ n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ŝ ún̂d́êŕ t̂h́ê t́âŕĝét̂ś ŝét̂ b́ŷ t́ĥé p̂ŕôv́îd́êd́ p̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕêŕf̂ór̂ḿâńĉé b̂úd̂ǵêt́ŝ](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count, plural,\n    =1 {1 r̂éq̂úêśt̂}\n    other {# ŕêq́ûéŝt́ŝ}\n   }"}, "core/audits/performance-budget.js | title": {"message": "P̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂"}, "core/audits/preload-fonts.js | description": {"message": "P̂ŕêĺôád̂ `optional` f́ôńt̂ś ŝó f̂ír̂śt̂-t́îḿê v́îśît́ôŕŝ ḿâý ûśê t́ĥém̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕr̂él̂óâd́îńĝ f́ôńt̂ś](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "F̂ón̂t́ŝ ẃît́ĥ `font-display: optional` ár̂é n̂ót̂ ṕr̂él̂óâd́êd́"}, "core/audits/preload-fonts.js | title": {"message": "F̂ón̂t́ŝ ẃît́ĥ `font-display: optional` ár̂é p̂ŕêĺôád̂éd̂"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Îf́ t̂h́ê ĹĈṔ êĺêḿêńt̂ íŝ d́ŷńâḿîćâĺl̂ý âd́d̂éd̂ t́ô t́ĥé p̂áĝé, ŷóû śĥóûĺd̂ ṕr̂él̂óâd́ t̂h́ê ím̂áĝé îń ôŕd̂ér̂ t́ô ím̂ṕr̂óv̂é L̂ĆP̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕr̂él̂óâd́îńĝ ĹĈṔ êĺêḿêńt̂ś](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "P̂ŕêĺôád̂ Ĺâŕĝéŝt́ Ĉón̂t́êńt̂f́ûĺ P̂áîńt̂ ím̂áĝé"}, "core/audits/redirects.js | description": {"message": "R̂éd̂ír̂éĉt́ŝ ín̂t́r̂ód̂úĉé âd́d̂ít̂íôńâĺ d̂él̂áŷś b̂éf̂ór̂é t̂h́ê ṕâǵê ćâń b̂é l̂óâd́êd́. [L̂éâŕn̂ h́ôẃ t̂ó âv́ôíd̂ ṕâǵê ŕêd́îŕêćt̂ś](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Âv́ôíd̂ ḿûĺt̂íp̂ĺê ṕâǵê ŕêd́îŕêćt̂ś"}, "core/audits/seo/canonical.js | description": {"message": "Ĉán̂ón̂íĉál̂ ĺîńk̂ś ŝúĝǵêśt̂ ẃĥíĉh́ ÛŔL̂ t́ô śĥóŵ ín̂ śêár̂ćĥ ŕêśûĺt̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĉán̂ón̂íĉál̂ ĺîńk̂ś](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "M̂úl̂t́îṕl̂é ĉón̂f́l̂íĉt́îńĝ ÚR̂Ĺŝ ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Îńv̂ál̂íd̂ ÚR̂Ĺ ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "P̂óîńt̂ś t̂ó âńôt́ĥér̂ `hreflang` ĺôćât́îón̂ ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Îś n̂ót̂ án̂ áb̂śôĺût́ê ÚR̂Ĺ ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "P̂óîńt̂ś t̂ó t̂h́ê d́ôḿâín̂'ś r̂óôt́ ÛŔL̂ (t́ĥé ĥóm̂ép̂áĝé), îńŝt́êád̂ óf̂ án̂ éq̂úîv́âĺêńt̂ ṕâǵê óf̂ ćôńt̂én̂t́"}, "core/audits/seo/canonical.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêś n̂ót̂ h́âv́ê á v̂ál̂íd̂ `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á v̂ál̂íd̂ `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Ûńĉŕâẃl̂áb̂ĺê Ĺîńk̂"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Ŝéâŕĉh́ êńĝín̂éŝ ḿâý ûśê `href` át̂t́r̂íb̂út̂éŝ ón̂ ĺîńk̂ś t̂ó ĉŕâẃl̂ ẃêb́ŝít̂éŝ. Én̂śûŕê t́ĥát̂ t́ĥé `href` ât́t̂ŕîb́ût́ê óf̂ án̂ćĥór̂ él̂ém̂én̂t́ŝ ĺîńk̂ś t̂ó âń âṕp̂ŕôṕr̂íât́ê d́êśt̂ín̂át̂íôń, ŝó m̂ór̂é p̂áĝéŝ óf̂ t́ĥé ŝít̂é ĉán̂ b́ê d́îśĉóv̂ér̂éd̂. [Ĺêár̂ń ĥóŵ t́ô ḿâḱê ĺîńk̂ś ĉŕâẃl̂áb̂ĺê](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "L̂ín̂ḱŝ ár̂é n̂ót̂ ćr̂áŵĺâb́l̂é"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "L̂ín̂ḱŝ ár̂é ĉŕâẃl̂áb̂ĺê"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Âd́d̂'ĺ îĺl̂éĝíb̂ĺê t́êx́t̂"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "F̂ón̂t́ Ŝíẑé"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% ôf́ P̂áĝé T̂éx̂t́"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Ŝél̂éĉt́ôŕ"}, "core/audits/seo/font-size.js | description": {"message": "F̂ón̂t́ ŝíẑéŝ ĺêśŝ t́ĥán̂ 12ṕx̂ ár̂é t̂óô śm̂ál̂ĺ t̂ó b̂é l̂éĝíb̂ĺê án̂d́ r̂éq̂úîŕê ḿôb́îĺê v́îśît́ôŕŝ t́ô “ṕîńĉh́ t̂ó ẑóôḿ” îń ôŕd̂ér̂ t́ô ŕêád̂. Śt̂ŕîv́ê t́ô h́âv́ê >60% óf̂ ṕâǵê t́êx́t̂ ≥12ṕx̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ĺêǵîb́l̂é f̂ón̂t́ ŝíẑéŝ](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} l̂éĝíb̂ĺê t́êx́t̂"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "T̂éx̂t́ îś îĺl̂éĝíb̂ĺê b́êćâúŝé t̂h́êŕê'ś n̂ó v̂íêẃp̂ór̂t́ m̂ét̂á t̂áĝ óp̂t́îḿîźêd́ f̂ór̂ ḿôb́îĺê śĉŕêén̂ś."}, "core/audits/seo/font-size.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ûśê ĺêǵîb́l̂é f̂ón̂t́ ŝíẑéŝ"}, "core/audits/seo/font-size.js | legibleText": {"message": "L̂éĝíb̂ĺê t́êx́t̂"}, "core/audits/seo/font-size.js | title": {"message": "D̂óĉúm̂én̂t́ ûśêś l̂éĝíb̂ĺê f́ôńt̂ śîźêś"}, "core/audits/seo/hreflang.js | description": {"message": "ĥŕêf́l̂án̂ǵ l̂ín̂ḱŝ t́êĺl̂ śêár̂ćĥ én̂ǵîńêś ŵh́ât́ v̂ér̂śîón̂ óf̂ á p̂áĝé t̂h́êý ŝh́ôúl̂d́ l̂íŝt́ îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ f́ôŕ â ǵîv́êń l̂án̂ǵûáĝé ôŕ r̂éĝíôń. [L̂éâŕn̂ ḿôŕê áb̂óût́ `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ĥáv̂é â v́âĺîd́ `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "R̂él̂át̂ív̂é ĥŕêf́ v̂ál̂úê"}, "core/audits/seo/hreflang.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á v̂ál̂íd̂ `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Ûńêx́p̂éĉt́êd́ l̂án̂ǵûáĝé ĉód̂é"}, "core/audits/seo/http-status-code.js | description": {"message": "P̂áĝéŝ ẃît́ĥ ún̂śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́êś m̂áŷ ńôt́ b̂é îńd̂éx̂éd̂ ṕr̂óp̂ér̂ĺŷ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́êś](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "P̂áĝé ĥáŝ ún̂śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́ê"}, "core/audits/seo/http-status-code.js | title": {"message": "P̂áĝé ĥáŝ śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́ê"}, "core/audits/seo/is-crawlable.js | description": {"message": "Ŝéâŕĉh́ êńĝín̂éŝ ár̂é ûńâb́l̂é t̂ó îńĉĺûd́ê ýôúr̂ ṕâǵêś îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ íf̂ t́ĥéŷ d́ôń't̂ h́âv́ê ṕêŕm̂íŝśîón̂ t́ô ćr̂áŵĺ t̂h́êḿ. [L̂éâŕn̂ ḿôŕê áb̂óût́ ĉŕâẃl̂ér̂ d́îŕêćt̂ív̂éŝ](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "P̂áĝé îś b̂ĺôćk̂éd̂ f́r̂óm̂ ín̂d́êx́îńĝ"}, "core/audits/seo/is-crawlable.js | title": {"message": "P̂áĝé îśn̂’t́ b̂ĺôćk̂éd̂ f́r̂óm̂ ín̂d́êx́îńĝ"}, "core/audits/seo/link-text.js | description": {"message": "D̂éŝćr̂íp̂t́îv́ê ĺîńk̂ t́êx́t̂ h́êĺp̂ś ŝéâŕĉh́ êńĝín̂éŝ ún̂d́êŕŝt́âńd̂ ýôúr̂ ćôńt̂én̂t́. [L̂éâŕn̂ h́ôẃ t̂ó m̂ák̂é l̂ín̂ḱŝ ḿôŕê áĉćêśŝíb̂ĺê](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 l̂ín̂ḱ f̂óûńd̂}\n    other {# ĺîńk̂ś f̂óûńd̂}\n    }"}, "core/audits/seo/link-text.js | failureTitle": {"message": "L̂ín̂ḱŝ d́ô ńôt́ ĥáv̂é d̂éŝćr̂íp̂t́îv́ê t́êx́t̂"}, "core/audits/seo/link-text.js | title": {"message": "L̂ín̂ḱŝ h́âv́ê d́êśĉŕîṕt̂ív̂é t̂éx̂t́"}, "core/audits/seo/manual/structured-data.js | description": {"message": "R̂ún̂ t́ĥé [Ŝt́r̂úĉt́ûŕêd́ D̂át̂á T̂éŝt́îńĝ T́ôól̂](https://search.google.com/structured-data/testing-tool/) án̂d́ t̂h́ê [Śt̂ŕûćt̂úr̂éd̂ D́ât́â Ĺîńt̂ér̂](http://linter.structured-data.org/) t́ô v́âĺîd́ât́ê śt̂ŕûćt̂úr̂éd̂ d́ât́â. [Ĺêár̂ń m̂ór̂é âb́ôút̂ Śt̂ŕûćt̂úr̂éd̂ D́ât́â](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Ŝt́r̂úĉt́ûŕêd́ d̂át̂á îś v̂ál̂íd̂"}, "core/audits/seo/meta-description.js | description": {"message": "M̂ét̂á d̂éŝćr̂íp̂t́îón̂ś m̂áŷ b́ê ín̂ćl̂úd̂éd̂ ín̂ śêár̂ćĥ ŕêśûĺt̂ś t̂ó ĉón̂ćîśêĺŷ śûḿm̂ár̂íẑé p̂áĝé ĉón̂t́êńt̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé m̂ét̂á d̂éŝćr̂íp̂t́îón̂](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "D̂éŝćr̂íp̂t́îón̂ t́êx́t̂ íŝ ém̂ṕt̂ý."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêś n̂ót̂ h́âv́ê á m̂ét̂á d̂éŝćr̂íp̂t́îón̂"}, "core/audits/seo/meta-description.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á m̂ét̂á d̂éŝćr̂íp̂t́îón̂"}, "core/audits/seo/plugins.js | description": {"message": "Ŝéâŕĉh́ êńĝín̂éŝ ćâń't̂ ín̂d́êx́ p̂ĺûǵîń ĉón̂t́êńt̂, án̂d́ m̂án̂ý d̂év̂íĉéŝ ŕêśt̂ŕîćt̂ ṕl̂úĝín̂ś ôŕ d̂ón̂'t́ ŝúp̂ṕôŕt̂ t́ĥém̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ áv̂óîd́îńĝ ṕl̂úĝín̂ś](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ ûśêś p̂ĺûǵîńŝ"}, "core/audits/seo/plugins.js | title": {"message": "D̂óĉúm̂én̂t́ âv́ôíd̂ś p̂ĺûǵîńŝ"}, "core/audits/seo/robots-txt.js | description": {"message": "Îf́ ŷóûŕ r̂ób̂ót̂ś.t̂x́t̂ f́îĺê íŝ ḿâĺf̂ór̂ḿêd́, ĉŕâẃl̂ér̂ś m̂áŷ ńôt́ b̂é âb́l̂é t̂ó ûńd̂ér̂śt̂án̂d́ ĥóŵ ýôú ŵán̂t́ ŷóûŕ ŵéb̂śît́ê t́ô b́ê ćr̂áŵĺêd́ ôŕ îńd̂éx̂éd̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ŕôb́ôt́ŝ.t́x̂t́](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "R̂éq̂úêśt̂ f́ôŕ r̂ób̂ót̂ś.t̂x́t̂ ŕêt́ûŕn̂éd̂ H́T̂T́P̂ śt̂át̂úŝ: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount, plural,\n    =1 {1 êŕr̂ór̂ f́ôún̂d́}\n    other {# êŕr̂ór̂ś f̂óûńd̂}\n    }"}, "core/audits/seo/robots-txt.js | explanation": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô d́ôẃn̂ĺôád̂ á r̂ób̂ót̂ś.t̂x́t̂ f́îĺê"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "r̂ób̂ót̂ś.t̂x́t̂ íŝ ńôt́ v̂ál̂íd̂"}, "core/audits/seo/robots-txt.js | title": {"message": "r̂ób̂ót̂ś.t̂x́t̂ íŝ v́âĺîd́"}, "core/audits/seo/tap-targets.js | description": {"message": "Îńt̂ér̂áĉt́îv́ê él̂ém̂én̂t́ŝ ĺîḱê b́ût́t̂ón̂ś âńd̂ ĺîńk̂ś ŝh́ôúl̂d́ b̂é l̂ár̂ǵê én̂óûǵĥ (48x́48p̂x́), ôŕ ĥáv̂é êńôúĝh́ ŝṕâćê ár̂óûńd̂ t́ĥém̂, t́ô b́ê éâśŷ én̂óûǵĥ t́ô t́âṕ ŵít̂h́ôút̂ óv̂ér̂ĺâṕp̂ín̂ǵ ôńt̂ó ôt́ĥér̂ él̂ém̂én̂t́ŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́âṕ t̂ár̂ǵêt́ŝ](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} âṕp̂ŕôṕr̂íât́êĺŷ śîźêd́ t̂áp̂ t́âŕĝét̂ś"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê t́ôó ŝḿâĺl̂ b́êćâúŝé t̂h́êŕê'ś n̂ó v̂íêẃp̂ór̂t́ m̂ét̂á t̂áĝ óp̂t́îḿîźêd́ f̂ór̂ ḿôb́îĺê śĉŕêén̂ś"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê ńôt́ ŝíẑéd̂ áp̂ṕr̂óp̂ŕîát̂él̂ý"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Ôv́êŕl̂áp̂ṕîńĝ T́âŕĝét̂"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "T̂áp̂ T́âŕĝét̂"}, "core/audits/seo/tap-targets.js | title": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê śîźêd́ âṕp̂ŕôṕr̂íât́êĺŷ"}, "core/audits/server-response-time.js | description": {"message": "K̂éêṕ t̂h́ê śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê f́ôŕ t̂h́ê ḿâín̂ d́ôćûḿêńt̂ śĥór̂t́ b̂éĉáûśê ál̂ĺ ôt́ĥér̂ ŕêq́ûéŝt́ŝ d́êṕêńd̂ ón̂ ít̂. [Ĺêár̂ń m̂ór̂é âb́ôút̂ t́ĥé T̂ím̂é t̂ó F̂ír̂śt̂ B́ŷt́ê ḿêt́r̂íĉ](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "R̂óôt́ d̂óĉúm̂én̂t́ t̂óôḱ {timeInMs, number, milliseconds} m̂ś"}, "core/audits/server-response-time.js | failureTitle": {"message": "R̂éd̂úĉé îńît́îál̂ śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê"}, "core/audits/server-response-time.js | title": {"message": "Îńît́îál̂ śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê ẃâś ŝh́ôŕt̂"}, "core/audits/splash-screen.js | description": {"message": "Â t́ĥém̂éd̂ śp̂ĺâśĥ śĉŕêén̂ én̂śûŕêś â h́îǵĥ-q́ûál̂ít̂ý êx́p̂ér̂íêńĉé ŵh́êń ûśêŕŝ ĺâún̂ćĥ ýôúr̂ áp̂ṕ f̂ŕôḿ t̂h́êír̂ h́ôḿêśĉŕêén̂ś. [L̂éâŕn̂ ḿôŕê áb̂óût́ ŝṕl̂áŝh́ ŝćr̂éêńŝ](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Îś n̂ót̂ ćôńf̂íĝúr̂éd̂ f́ôŕ â ćûśt̂óm̂ śp̂ĺâśĥ śĉŕêén̂"}, "core/audits/splash-screen.js | title": {"message": "Ĉón̂f́îǵûŕêd́ f̂ór̂ á ĉúŝt́ôḿ ŝṕl̂áŝh́ ŝćr̂éêń"}, "core/audits/themed-omnibox.js | description": {"message": "T̂h́ê b́r̂óŵśêŕ âd́d̂ŕêśŝ b́âŕ ĉán̂ b́ê t́ĥém̂éd̂ t́ô ḿât́ĉh́ ŷóûŕ ŝít̂é. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́êḿîńĝ t́ĥé âd́d̂ŕêśŝ b́âŕ](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "D̂óêś n̂ót̂ śêt́ â t́ĥém̂é ĉól̂ór̂ f́ôŕ t̂h́ê ád̂d́r̂éŝś b̂ár̂."}, "core/audits/themed-omnibox.js | title": {"message": "Ŝét̂ś â t́ĥém̂é ĉól̂ór̂ f́ôŕ t̂h́ê ád̂d́r̂éŝś b̂ár̂."}, "core/audits/third-party-cookies.js | description": {"message": "Ŝúp̂ṕôŕt̂ f́ôŕ t̂h́îŕd̂-ṕâŕt̂ý ĉóôḱîéŝ ẃîĺl̂ b́ê ŕêḿôv́êd́ îń â f́ût́ûŕê v́êŕŝíôń ôf́ Ĉh́r̂óm̂é. [L̂éâŕn̂ ḿôŕê áb̂óût́ p̂h́âśîńĝ óût́ t̂h́îŕd̂-ṕâŕt̂ý ĉóôḱîéŝ](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ĉóôḱîé f̂óûńd̂}\n    other {# ćôók̂íêś f̂óûńd̂}\n    }"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Ûśêś t̂h́îŕd̂-ṕâŕt̂ý ĉóôḱîéŝ"}, "core/audits/third-party-cookies.js | title": {"message": "Âv́ôíd̂ś t̂h́îŕd̂-ṕâŕt̂ý ĉóôḱîéŝ"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Ĉúŝt́ôḿêŕ Ŝúĉćêśŝ)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (M̂ár̂ḱêt́îńĝ)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Ŝóĉíâĺ)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (V̂íd̂éô)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "P̂ŕôd́ûćt̂"}, "core/audits/third-party-facades.js | description": {"message": "Ŝóm̂é t̂h́îŕd̂-ṕâŕt̂ý êḿb̂éd̂ś ĉán̂ b́ê ĺâźŷ ĺôád̂éd̂. Ćôńŝíd̂ér̂ ŕêṕl̂áĉín̂ǵ t̂h́êḿ ŵít̂h́ â f́âćâd́ê ún̂t́îĺ t̂h́êý âŕê ŕêq́ûír̂éd̂. [Ĺêár̂ń ĥóŵ t́ô d́êf́êŕ t̂h́îŕd̂-ṕâŕt̂íêś ŵít̂h́ â f́âćâd́ê](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount, plural,\n  =1 {# f̂áĉád̂é âĺt̂ér̂ńât́îv́ê áv̂áîĺâb́l̂é}\n  other {# f̂áĉád̂é âĺt̂ér̂ńât́îv́êś âv́âíl̂áb̂ĺê}\n  }"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Ŝóm̂é t̂h́îŕd̂-ṕâŕt̂ý r̂éŝóûŕĉéŝ ćâń b̂é l̂áẑý l̂óâd́êd́ ŵít̂h́ â f́âćâd́ê"}, "core/audits/third-party-facades.js | title": {"message": "L̂áẑý l̂óâd́ t̂h́îŕd̂-ṕâŕt̂ý r̂éŝóûŕĉéŝ ẃît́ĥ f́âćâd́êś"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "T̂h́îŕd̂-Ṕâŕt̂ý"}, "core/audits/third-party-summary.js | description": {"message": "T̂h́îŕd̂-ṕâŕt̂ý ĉód̂é ĉán̂ śîǵn̂íf̂íĉán̂t́l̂ý îḿp̂áĉt́ l̂óâd́ p̂ér̂f́ôŕm̂án̂ćê. Ĺîḿît́ t̂h́ê ńûḿb̂ér̂ óf̂ ŕêd́ûńd̂án̂t́ t̂h́îŕd̂-ṕâŕt̂ý p̂ŕôv́îd́êŕŝ án̂d́ t̂ŕŷ t́ô ĺôád̂ t́ĥír̂d́-p̂ár̂t́ŷ ćôd́ê áf̂t́êŕ ŷóûŕ p̂áĝé ĥáŝ ṕr̂ím̂ár̂íl̂ý f̂ín̂íŝh́êd́ l̂óâd́îńĝ. [Ĺêár̂ń ĥóŵ t́ô ḿîńîḿîźê t́ĥír̂d́-p̂ár̂t́ŷ ím̂ṕâćt̂](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "T̂h́îŕd̂-ṕâŕt̂ý ĉód̂é b̂ĺôćk̂éd̂ t́ĥé m̂áîń t̂h́r̂éâd́ f̂ór̂ {timeInMs, number, milliseconds} ḿŝ"}, "core/audits/third-party-summary.js | failureTitle": {"message": "R̂éd̂úĉé t̂h́ê ím̂ṕâćt̂ óf̂ t́ĥír̂d́-p̂ár̂t́ŷ ćôd́ê"}, "core/audits/third-party-summary.js | title": {"message": "M̂ín̂ím̂íẑé t̂h́îŕd̂-ṕâŕt̂ý ûśâǵê"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "M̂éâśûŕêḿêńt̂"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "M̂ét̂ŕîć"}, "core/audits/timing-budget.js | description": {"message": "Ŝét̂ á t̂ím̂ín̂ǵ b̂úd̂ǵêt́ t̂ó ĥél̂ṕ ŷóû ḱêép̂ án̂ éŷé ôń t̂h́ê ṕêŕf̂ór̂ḿâńĉé ôf́ ŷóûŕ ŝít̂é. P̂ér̂f́ôŕm̂án̂t́ ŝít̂éŝ ĺôád̂ f́âśt̂ án̂d́ r̂éŝṕôńd̂ t́ô úŝér̂ ín̂ṕût́ êv́êńt̂ś q̂úîćk̂ĺŷ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ ṕêŕf̂ór̂ḿâńĉé b̂úd̂ǵêt́ŝ](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "T̂ím̂ín̂ǵ b̂úd̂ǵêt́"}, "core/audits/unsized-images.js | description": {"message": "Ŝét̂ án̂ éx̂ṕl̂íĉít̂ ẃîd́t̂h́ âńd̂ h́êíĝh́t̂ ón̂ ím̂áĝé êĺêḿêńt̂ś t̂ó r̂éd̂úĉé l̂áŷóût́ ŝh́îf́t̂ś âńd̂ ím̂ṕr̂óv̂é ĈĹŜ. [Ĺêár̂ń ĥóŵ t́ô śêt́ îḿâǵê d́îḿêńŝíôńŝ](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ d́ô ńôt́ ĥáv̂é êx́p̂ĺîćît́ `width` âńd̂ `height`"}, "core/audits/unsized-images.js | title": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ h́âv́ê éx̂ṕl̂íĉít̂ `width` án̂d́ `height`"}, "core/audits/user-timings.js | columnType": {"message": "T̂ýp̂é"}, "core/audits/user-timings.js | description": {"message": "Ĉón̂śîd́êŕ îńŝt́r̂úm̂én̂t́îńĝ ýôúr̂ áp̂ṕ ŵít̂h́ t̂h́ê Úŝér̂ T́îḿîńĝ ÁP̂Í t̂ó m̂éâśûŕê ýôúr̂ áp̂ṕ'ŝ ŕêál̂-ẃôŕl̂d́ p̂ér̂f́ôŕm̂án̂ćê d́ûŕîńĝ ḱêý ûśêŕ êx́p̂ér̂íêńĉéŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ Úŝér̂ T́îḿîńĝ ḿâŕk̂ś](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ûśêŕ t̂ím̂ín̂ǵ}\n    other {# ûśêŕ t̂ím̂ín̂ǵŝ}\n    }"}, "core/audits/user-timings.js | title": {"message": "Ûśêŕ T̂ím̂ín̂ǵ m̂ár̂ḱŝ án̂d́ m̂éâśûŕêś"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Â `<link rel=preconnect>` ẃâś f̂óûńd̂ f́ôŕ \"{securityOrigin}\" b̂út̂ ẃâś n̂ót̂ úŝéd̂ b́ŷ t́ĥé b̂ŕôẃŝér̂. Ćĥéĉḱ t̂h́ât́ ŷóû ár̂é ûśîńĝ t́ĥé `crossorigin` ât́t̂ŕîb́ût́ê ṕr̂óp̂ér̂ĺŷ."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Ĉón̂śîd́êŕ âd́d̂ín̂ǵ `preconnect` ôŕ `dns-prefetch` r̂éŝóûŕĉé ĥín̂t́ŝ t́ô éŝt́âb́l̂íŝh́ êár̂ĺŷ ćôńn̂éĉt́îón̂ś t̂ó îḿp̂ór̂t́âńt̂ t́ĥír̂d́-p̂ár̂t́ŷ ór̂íĝín̂ś. [L̂éâŕn̂ h́ôẃ t̂ó p̂ŕêćôńn̂éĉt́ t̂ó r̂éq̂úîŕêd́ ôŕîǵîńŝ](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "P̂ŕêćôńn̂éĉt́ t̂ó r̂éq̂úîŕêd́ ôŕîǵîńŝ"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "M̂ór̂é t̂h́âń 2 `<link rel=preconnect>` ĉón̂ńêćt̂íôńŝ ẃêŕê f́ôún̂d́. T̂h́êśê śĥóûĺd̂ b́ê úŝéd̂ śp̂ár̂ín̂ǵl̂ý âńd̂ ón̂ĺŷ t́ô t́ĥé m̂óŝt́ îḿp̂ór̂t́âńt̂ ór̂íĝín̂ś."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Â `<link rel=preconnect>` ẃâś f̂óûńd̂ f́ôŕ \"{securityOrigin}\" b̂út̂ ẃâś n̂ót̂ úŝéd̂ b́ŷ t́ĥé b̂ŕôẃŝér̂. Ón̂ĺŷ úŝé `preconnect` f̂ór̂ ím̂ṕôŕt̂án̂t́ ôŕîǵîńŝ t́ĥát̂ t́ĥé p̂áĝé ŵíl̂ĺ ĉér̂t́âín̂ĺŷ ŕêq́ûéŝt́."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Â ṕr̂él̂óâd́ `<link>` ŵáŝ f́ôún̂d́ f̂ór̂ \"{preloadURL}\" b́ût́ ŵáŝ ńôt́ ûśêd́ b̂ý t̂h́ê b́r̂óŵśêŕ. Ĉh́êćk̂ t́ĥát̂ ýôú âŕê úŝín̂ǵ t̂h́ê `crossorigin` át̂t́r̂íb̂út̂é p̂ŕôṕêŕl̂ý."}, "core/audits/uses-rel-preload.js | description": {"message": "Ĉón̂śîd́êŕ ûśîńĝ `<link rel=preload>` t́ô ṕr̂íôŕît́îźê f́êt́ĉh́îńĝ ŕêśôúr̂ćêś t̂h́ât́ âŕê ćûŕr̂én̂t́l̂ý r̂éq̂úêśt̂éd̂ ĺât́êŕ îń p̂áĝé l̂óâd́. [L̂éâŕn̂ h́ôẃ t̂ó p̂ŕêĺôád̂ ḱêý r̂éq̂úêśt̂ś](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "P̂ŕêĺôád̂ ḱêý r̂éq̂úêśt̂ś"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "M̂áp̂ ÚR̂Ĺ"}, "core/audits/valid-source-maps.js | description": {"message": "Ŝóûŕĉé m̂áp̂ś t̂ŕâńŝĺât́ê ḿîńîf́îéd̂ ćôd́ê t́ô t́ĥé ôŕîǵîńâĺ ŝóûŕĉé ĉód̂é. T̂h́îś ĥél̂ṕŝ d́êv́êĺôṕêŕŝ d́êb́ûǵ îń p̂ŕôd́ûćt̂íôń. Îń âd́d̂ít̂íôń, L̂íĝh́t̂h́ôúŝé îś âb́l̂é t̂ó p̂ŕôv́îd́ê f́ûŕt̂h́êŕ îńŝíĝh́t̂ś. Ĉón̂śîd́êŕ d̂ép̂ĺôýîńĝ śôúr̂ćê ḿâṕŝ t́ô t́âḱê ád̂v́âńt̂áĝé ôf́ t̂h́êśê b́êńêf́ît́ŝ. [Ĺêár̂ń m̂ór̂é âb́ôút̂ śôúr̂ćê ḿâṕŝ](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "M̂íŝśîńĝ śôúr̂ćê ḿâṕŝ f́ôŕ l̂ár̂ǵê f́îŕŝt́-p̂ár̂t́ŷ J́âv́âŚĉŕîṕt̂"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "L̂ár̂ǵê J́âv́âŚĉŕîṕt̂ f́îĺê íŝ ḿîśŝín̂ǵ â śôúr̂ćê ḿâṕ"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems, plural,\n    =1 {Ŵár̂ńîńĝ: ḿîśŝín̂ǵ 1 ît́êḿ îń `.sourcesContent`}\n    other {Ŵár̂ńîńĝ: ḿîśŝín̂ǵ # ît́êḿŝ ín̂ `.sourcesContent`}\n    }"}, "core/audits/valid-source-maps.js | title": {"message": "P̂áĝé ĥáŝ v́âĺîd́ ŝóûŕĉé m̂áp̂ś"}, "core/audits/viewport.js | description": {"message": "Â `<meta name=\"viewport\">` ńôt́ ôńl̂ý ôṕt̂ím̂íẑéŝ ýôúr̂ áp̂ṕ f̂ór̂ ḿôb́îĺê śĉŕêén̂ śîźêś, b̂út̂ ál̂śô ṕr̂év̂én̂t́ŝ [á 300 m̂íl̂ĺîśêćôńd̂ d́êĺâý t̂ó ûśêŕ îńp̂út̂](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Ĺêár̂ń m̂ór̂é âb́ôút̂ úŝín̂ǵ t̂h́ê v́îéŵṕôŕt̂ ḿêt́â t́âǵ](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "N̂ó `<meta name=\"viewport\">` t̂áĝ f́ôún̂d́"}, "core/audits/viewport.js | failureTitle": {"message": "D̂óêś n̂ót̂ h́âv́ê á `<meta name=\"viewport\">` t̂áĝ ẃît́ĥ `width` ór̂ `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Ĥáŝ á `<meta name=\"viewport\">` t̂áĝ ẃît́ĥ `width` ór̂ `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "T̂h́îś îś t̂h́ê t́ĥŕêád̂-b́l̂óĉḱîńĝ ẃôŕk̂ óĉćûŕr̂ín̂ǵ d̂úr̂ín̂ǵ t̂h́ê Ín̂t́êŕâćt̂íôń t̂ó N̂éx̂t́ P̂áîńt̂ ḿêáŝúr̂ém̂én̂t́. [L̂éâŕn̂ ḿôŕê áb̂óût́ t̂h́ê Ín̂t́êŕâćt̂íôń t̂ó N̂éx̂t́ P̂áîńt̂ ḿêt́r̂íĉ](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} m̂ś ŝṕêńt̂ ón̂ év̂én̂t́ '{interactionType}'"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Êv́êńt̂ t́âŕĝét̂"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "M̂ín̂ím̂íẑé ŵór̂ḱ d̂úr̂ín̂ǵ k̂éŷ ín̂t́êŕâćt̂íôń"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Îńp̂út̂ d́êĺâý"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "P̂ŕêśêńt̂át̂íôń d̂él̂áŷ"}, "core/audits/work-during-interaction.js | processingTime": {"message": "P̂ŕôćêśŝín̂ǵ t̂ím̂é"}, "core/audits/work-during-interaction.js | title": {"message": "M̂ín̂ím̂íẑéŝ ẃôŕk̂ d́ûŕîńĝ ḱêý îńt̂ér̂áĉt́îón̂"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê úŝáĝé ôf́ ÂŔÎÁ îń ŷóûŕ âṕp̂ĺîćât́îón̂ ẃĥíĉh́ m̂áŷ én̂h́âńĉé t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ óf̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝý, l̂ík̂é â śĉŕêén̂ ŕêád̂ér̂."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ÂŔÎÁ"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ṕr̂óv̂íd̂é âĺt̂ér̂ńât́îv́ê ćôńt̂én̂t́ f̂ór̂ áûd́îó âńd̂ v́îd́êó. T̂h́îś m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ ẃît́ĥ h́êár̂ín̂ǵ ôŕ v̂íŝíôń îḿp̂áîŕm̂én̂t́ŝ."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Âúd̂íô án̂d́ v̂íd̂éô"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "T̂h́êśê ít̂ém̂ś ĥíĝh́l̂íĝh́t̂ ćôḿm̂ón̂ áĉćêśŝíb̂íl̂ít̂ý b̂éŝt́ p̂ŕâćt̂íĉéŝ."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "B̂éŝt́ p̂ŕâćt̂íĉéŝ"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ h́îǵĥĺîǵĥt́ ôṕp̂ór̂t́ûńît́îéŝ t́ô [ím̂ṕr̂óv̂é t̂h́ê áĉćêśŝíb̂íl̂ít̂ý ôf́ ŷóûŕ ŵéb̂ áp̂ṕ](https://developer.chrome.com/docs/lighthouse/accessibility/). Âút̂óm̂át̂íĉ d́êt́êćt̂íôń ĉán̂ ón̂ĺŷ d́êt́êćt̂ á ŝúb̂śêt́ ôf́ îśŝúêś âńd̂ d́ôéŝ ńôt́ ĝúâŕâńt̂éê t́ĥé âćĉéŝśîb́îĺît́ŷ óf̂ ýôúr̂ ẃêb́ âṕp̂, śô [ḿâńûál̂ t́êśt̂ín̂ǵ](https://web.dev/articles/how-to-review) îś âĺŝó êńĉóûŕâǵêd́."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "T̂h́êśê ít̂ém̂ś âd́d̂ŕêśŝ ár̂éâś ŵh́îćĥ án̂ áût́ôḿât́êd́ t̂éŝt́îńĝ t́ôól̂ ćâńn̂ót̂ ćôv́êŕ. L̂éâŕn̂ ḿôŕê ín̂ óûŕ ĝúîd́ê ón̂ [ćôńd̂úĉt́îńĝ án̂ áĉćêśŝíb̂íl̂ít̂ý r̂év̂íêẃ](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Âćĉéŝśîb́îĺît́ŷ"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê ĺêǵîb́îĺît́ŷ óf̂ ýôúr̂ ćôńt̂én̂t́."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Ĉón̂t́r̂áŝt́"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê ín̂t́êŕp̂ŕêt́ât́îón̂ óf̂ ýôúr̂ ćôńt̂én̂t́ b̂ý ûśêŕŝ ín̂ d́îf́f̂ér̂én̂t́ l̂óĉál̂éŝ."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Îńt̂ér̂ńât́îón̂ál̂íẑát̂íôń âńd̂ ĺôćâĺîźât́îón̂"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê śêḿâńt̂íĉś ôf́ t̂h́ê ćôńt̂ŕôĺŝ ín̂ ýôúr̂ áp̂ṕl̂íĉát̂íôń. T̂h́îś m̂áŷ én̂h́âńĉé t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ óf̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝý, l̂ík̂é â śĉŕêén̂ ŕêád̂ér̂."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "N̂ám̂éŝ án̂d́ l̂áb̂él̂ś"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é k̂éŷb́ôár̂d́ n̂áv̂íĝát̂íôń îń ŷóûŕ âṕp̂ĺîćât́îón̂."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "N̂áv̂íĝát̂íôń"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê óf̂ ŕêád̂ín̂ǵ t̂áb̂úl̂ár̂ ór̂ ĺîśt̂ d́ât́â úŝín̂ǵ âśŝíŝt́îv́ê t́êćĥńôĺôǵŷ, ĺîḱê á ŝćr̂éêń r̂éâd́êŕ."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "T̂áb̂ĺêś âńd̂ ĺîśt̂ś"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "B̂ŕôẃŝér̂ Ćôḿp̂át̂íb̂íl̂ít̂ý"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "B̂éŝt́ P̂ŕâćt̂íĉéŝ"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Ĝén̂ér̂ál̂"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "T̂ŕûśt̂ án̂d́ Ŝáf̂ét̂ý"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Ûśêŕ Êx́p̂ér̂íêńĉé"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "P̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂ś ŝét̂ śt̂án̂d́âŕd̂ś f̂ór̂ t́ĥé p̂ér̂f́ôŕm̂án̂ćê óf̂ ýôúr̂ śît́ê."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "B̂úd̂ǵêt́ŝ"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "M̂ór̂é îńf̂ór̂ḿât́îón̂ áb̂óût́ t̂h́ê ṕêŕf̂ór̂ḿâńĉé ôf́ ŷóûŕ âṕp̂ĺîćât́îón̂. T́ĥéŝé n̂úm̂b́êŕŝ d́ôń't̂ [d́îŕêćt̂ĺŷ áf̂f́êćt̂](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) t́ĥé P̂ér̂f́ôŕm̂án̂ćê śĉór̂é."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "D̂íâǵn̂óŝt́îćŝ"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "T̂h́ê ḿôśt̂ ćr̂ít̂íĉál̂ áŝṕêćt̂ óf̂ ṕêŕf̂ór̂ḿâńĉé îś ĥóŵ q́ûíĉḱl̂ý p̂íx̂él̂ś âŕê ŕêńd̂ér̂éd̂ ón̂śĉŕêén̂. Ḱêý m̂ét̂ŕîćŝ: F́îŕŝt́ Ĉón̂t́êńt̂f́ûĺ P̂áîńt̂, F́îŕŝt́ M̂éâńîńĝf́ûĺ P̂áîńt̂"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "F̂ír̂śt̂ Ṕâín̂t́ Îḿp̂ŕôv́êḿêńt̂ś"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "T̂h́êśê śûǵĝéŝt́îón̂ś ĉán̂ h́êĺp̂ ýôúr̂ ṕâǵê ĺôád̂ f́âśt̂ér̂. T́ĥéŷ d́ôń't̂ [d́îŕêćt̂ĺŷ áf̂f́êćt̂](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) t́ĥé P̂ér̂f́ôŕm̂án̂ćê śĉór̂é."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Ôṕp̂ór̂t́ûńît́îéŝ"}, "core/config/default-config.js | metricGroupTitle": {"message": "M̂ét̂ŕîćŝ"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Êńĥán̂ćê t́ĥé ôv́êŕâĺl̂ ĺôád̂ín̂ǵ êx́p̂ér̂íêńĉé, ŝó t̂h́ê ṕâǵê íŝ ŕêśp̂ón̂śîv́ê án̂d́ r̂éâd́ŷ t́ô úŝé âś ŝóôń âś p̂óŝśîb́l̂é. K̂éŷ ḿêt́r̂íĉś: T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê, Śp̂éêd́ Îńd̂éx̂"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Ôv́êŕâĺl̂ Ím̂ṕr̂óv̂ém̂én̂t́ŝ"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "P̂ér̂f́ôŕm̂án̂ćê"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ v́âĺîd́ât́ê t́ĥé âśp̂éĉt́ŝ óf̂ á P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂. [Ĺêár̂ń ŵh́ât́ m̂ák̂éŝ á ĝóôd́ P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "T̂h́êśê ćĥéĉḱŝ ár̂é r̂éq̂úîŕêd́ b̂ý t̂h́ê b́âśêĺîńê [ṔŴÁ Ĉh́êćk̂ĺîśt̂](https://web.dev/articles/pwa-checklist) b́ût́ âŕê ńôt́ âút̂óm̂át̂íĉál̂ĺŷ ćĥéĉḱêd́ b̂ý L̂íĝh́t̂h́ôúŝé. T̂h́êý d̂ó n̂ót̂ áf̂f́êćt̂ ýôúr̂ śĉór̂é b̂út̂ ít̂'ś îḿp̂ór̂t́âńt̂ t́ĥát̂ ýôú v̂ér̂íf̂ý t̂h́êḿ m̂án̂úâĺl̂ý."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "P̂ẂÂ"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Îńŝt́âĺl̂áb̂ĺê"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "P̂ẂÂ Óp̂t́îḿîźêd́"}, "core/config/default-config.js | seoCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ én̂śûŕê t́ĥát̂ ýôúr̂ ṕâǵê íŝ f́ôĺl̂óŵín̂ǵ b̂áŝíĉ śêár̂ćĥ én̂ǵîńê óp̂t́îḿîźât́îón̂ ád̂v́îćê. T́ĥér̂é âŕê ḿâńŷ ád̂d́ît́îón̂ál̂ f́âćt̂ór̂ś L̂íĝh́t̂h́ôúŝé d̂óêś n̂ót̂ śĉór̂é ĥér̂é t̂h́ât́ m̂áŷ áf̂f́êćt̂ ýôúr̂ śêár̂ćĥ ŕâńk̂ín̂ǵ, îńĉĺûd́îńĝ ṕêŕf̂ór̂ḿâńĉé ôń [Ĉór̂é Ŵéb̂ V́ît́âĺŝ](https://web.dev/explore/vitals). [Ĺêár̂ń m̂ór̂é âb́ôút̂ Ǵôóĝĺê Śêár̂ćĥ Éŝśêńt̂íâĺŝ](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "R̂ún̂ t́ĥéŝé âd́d̂ít̂íôńâĺ v̂ál̂íd̂át̂ór̂ś ôń ŷóûŕ ŝít̂é t̂ó ĉh́êćk̂ ád̂d́ît́îón̂ál̂ ŚÊÓ b̂éŝt́ p̂ŕâćt̂íĉéŝ."}, "core/config/default-config.js | seoCategoryTitle": {"message": "ŜÉÔ"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "F̂ór̂ḿât́ ŷóûŕ ĤT́M̂Ĺ îń â ẃâý t̂h́ât́ êńâb́l̂éŝ ćr̂áŵĺêŕŝ t́ô b́êt́t̂ér̂ ún̂d́êŕŝt́âńd̂ ýôúr̂ áp̂ṕ’ŝ ćôńt̂én̂t́."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Ĉón̂t́êńt̂ B́êśt̂ Ṕr̂áĉt́îćêś"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "T̂ó âṕp̂éâŕ îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ, ćr̂áŵĺêŕŝ ńêéd̂ áĉćêśŝ t́ô ýôúr̂ áp̂ṕ."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Ĉŕâẃl̂ín̂ǵ âńd̂ Ín̂d́êx́îńĝ"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "M̂ák̂é ŝúr̂é ŷóûŕ p̂áĝéŝ ár̂é m̂ób̂íl̂é f̂ŕîén̂d́l̂ý ŝó ûśêŕŝ d́ôń’t̂ h́âv́ê t́ô ṕîńĉh́ ôŕ ẑóôḿ îń ôŕd̂ér̂ t́ô ŕêád̂ t́ĥé ĉón̂t́êńt̂ ṕâǵêś. [L̂éâŕn̂ h́ôẃ t̂ó m̂ák̂é p̂áĝéŝ ḿôb́îĺê-f́r̂íêńd̂ĺŷ](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "M̂ób̂íl̂é F̂ŕîén̂d́l̂ý"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "T̂h́ê t́êśt̂éd̂ d́êv́îćê áp̂ṕêár̂ś t̂ó ĥáv̂é â śl̂óŵér̂ ĆP̂Ú t̂h́âń  L̂íĝh́t̂h́ôúŝé êx́p̂éĉt́ŝ. T́ĥíŝ ćâń n̂éĝát̂ív̂él̂ý âf́f̂éĉt́ ŷóûŕ p̂ér̂f́ôŕm̂án̂ćê śĉór̂é. L̂éâŕn̂ ḿôŕê áb̂óût́ [ĉál̂íb̂ŕât́îńĝ án̂ áp̂ṕr̂óp̂ŕîát̂é ĈṔÛ śl̂óŵd́ôẃn̂ ḿûĺt̂íp̂ĺîér̂](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "T̂h́ê ṕâǵê ḿâý n̂ót̂ b́ê ĺôád̂ín̂ǵ âś êx́p̂éĉt́êd́ b̂éĉáûśê ýôúr̂ t́êśt̂ ÚR̂Ĺ ({requested}) ŵáŝ ŕêd́îŕêćt̂éd̂ t́ô {final}. T́r̂ý t̂éŝt́îńĝ t́ĥé ŝéĉón̂d́ ÛŔL̂ d́îŕêćt̂ĺŷ."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "T̂h́ê ṕâǵê ĺôád̂éd̂ t́ôó ŝĺôẃl̂ý t̂ó f̂ín̂íŝh́ ŵít̂h́îń t̂h́ê t́îḿê ĺîḿît́. R̂éŝúl̂t́ŝ ḿâý b̂é îńĉóm̂ṕl̂ét̂é."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Ĉĺêár̂ín̂ǵ t̂h́ê b́r̂óŵśêŕ ĉáĉh́ê t́îḿêd́ ôút̂. T́r̂ý âúd̂ít̂ín̂ǵ t̂h́îś p̂áĝé âǵâín̂ án̂d́ f̂íl̂é â b́ûǵ îf́ t̂h́ê íŝśûé p̂ér̂śîśt̂ś."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount, plural,\n    =1 {T̂h́êŕê ḿâý b̂é ŝt́ôŕêd́ d̂át̂á âf́f̂éĉt́îńĝ ĺôád̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê ín̂ t́ĥíŝ ĺôćât́îón̂: {locations}. Áûd́ît́ t̂h́îś p̂áĝé îń âń îńĉóĝńît́ô ẃîńd̂óŵ t́ô ṕr̂év̂én̂t́ t̂h́ôśê ŕêśôúr̂ćêś f̂ŕôḿ âf́f̂éĉt́îńĝ ýôúr̂ śĉór̂éŝ.}\n    other {T́ĥér̂é m̂áŷ b́ê śt̂ór̂éd̂ d́ât́â áf̂f́êćt̂ín̂ǵ l̂óâd́îńĝ ṕêŕf̂ór̂ḿâńĉé îń t̂h́êśê ĺôćât́îón̂ś: {locations}. Âúd̂ít̂ t́ĥíŝ ṕâǵê ín̂ án̂ ín̂ćôǵn̂ít̂ó ŵín̂d́ôẃ t̂ó p̂ŕêv́êńt̂ t́ĥóŝé r̂éŝóûŕĉéŝ f́r̂óm̂ áf̂f́êćt̂ín̂ǵ ŷóûŕ ŝćôŕêś.}\n  }"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Ĉĺêár̂ín̂ǵ t̂h́ê ór̂íĝín̂ d́ât́â t́îḿêd́ ôút̂. T́r̂ý âúd̂ít̂ín̂ǵ t̂h́îś p̂áĝé âǵâín̂ án̂d́ f̂íl̂é â b́ûǵ îf́ t̂h́ê íŝśûé p̂ér̂śîśt̂ś."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Êŕr̂ór̂ ṕâŕŝín̂ǵ `link` ĥéâd́êŕ ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Â ṕâǵê ńâv́îǵât́îón̂ ẃâś d̂ét̂éĉt́êd́ d̂úr̂ín̂ǵ t̂h́ê ŕûń. Ûśîńĝ t́îḿêśp̂án̂ ḿôd́ê t́ô áûd́ît́ p̂áĝé n̂áv̂íĝát̂íôńŝ íŝ ńôt́ r̂éĉóm̂ḿêńd̂éd̂. Úŝé n̂áv̂íĝát̂íôń m̂ód̂é t̂ó âúd̂ít̂ ṕâǵê ńâv́îǵât́îón̂ś f̂ór̂ b́êt́t̂ér̂ t́ĥír̂d́-p̂ár̂t́ŷ át̂t́r̂íb̂út̂íôń âńd̂ ḿâín̂ t́ĥŕêád̂ d́êt́êćt̂íôń."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "P̂áĝéŝ t́ĥát̂ ŕêq́ûéŝt́êd́ âń Âṕp̂B́âńn̂ér̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô á k̂éêṕâĺîv́ê ŕêq́ûéŝt́."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ b̂ý f̂ĺâǵŝ. V́îśît́ ĉh́r̂óm̂é://f̂ĺâǵŝ/#b́âćk̂-f́ôŕŵár̂d́-ĉáĉh́ê t́ô én̂áb̂ĺê ít̂ ĺôćâĺl̂ý ôń t̂h́îś d̂év̂íĉé."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ b̂ý t̂h́ê ćôḿm̂án̂d́ l̂ín̂é."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô ín̂śûf́f̂íĉíêńt̂ ḿêḿôŕŷ."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś n̂ót̂ śûṕp̂ór̂t́êd́ b̂ý d̂él̂éĝát̂é."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ f̂ór̂ ṕr̂ér̂én̂d́êŕêŕ."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "T̂h́ê ṕâǵê ćâńn̂ót̂ b́ê ćâćĥéd̂ b́êćâúŝé ît́ ĥáŝ á B̂ŕôád̂ćâśt̂Ćĥán̂ńêĺ îńŝt́âńĉé ŵít̂h́ r̂éĝíŝt́êŕêd́ l̂íŝt́êńêŕŝ."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "P̂áĝéŝ ẃît́ĥ ćâćĥé-ĉón̂t́r̂ól̂:ńô-śt̂ór̂é ĥéâd́êŕ ĉán̂ńôt́ êńt̂ér̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "T̂h́ê ćâćĥé ŵáŝ ín̂t́êńt̂íôńâĺl̂ý ĉĺêár̂éd̂."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ t́ĥé ĉáĉh́ê t́ô ál̂ĺôẃ âńôt́ĥér̂ ṕâǵê t́ô b́ê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "P̂áĝéŝ ćôńt̂áîńîńĝ ṕl̂úĝín̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "P̂áĝéŝ t́ĥát̂ úŝé F̂íl̂éĈh́ôóŝér̂ ÁP̂Í âŕê ńôt́ êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "P̂áĝéŝ t́ĥát̂ úŝé F̂íl̂é Ŝýŝt́êḿ Âćĉéŝś ÂṔÎ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "P̂áĝéŝ t́ĥát̂ úŝé M̂éd̂íâ D́êv́îćê D́îśp̂át̂ćĥér̂ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Â ḿêd́îá p̂ĺâýêŕ ŵáŝ ṕl̂áŷín̂ǵ ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "P̂áĝéŝ t́ĥát̂ úŝé M̂éd̂íâŚêśŝíôń ÂṔÎ án̂d́ ŝét̂ á p̂ĺâýb̂áĉḱ ŝt́ât́ê ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "P̂áĝéŝ t́ĥát̂ úŝé M̂éd̂íâŚêśŝíôń ÂṔÎ án̂d́ ŝét̂ áĉt́îón̂ h́âńd̂ĺêŕŝ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô śĉŕêén̂ ŕêád̂ér̂."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŝéĉúr̂ít̂ýĤán̂d́l̂ér̂ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŝér̂íâĺ ÂṔÎ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂Áût́ĥét̂íĉát̂íôń ÂṔÎ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂B́l̂úêt́ôót̂h́ ÂṔÎ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂ÚŜB́ ÂṔÎ ár̂é n̂ót̂ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ b̂éĉáûśê ćôók̂íêś âŕê d́îśâb́l̂éd̂ ón̂ á p̂áĝé t̂h́ât́ ûśêś `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "P̂áĝéŝ t́ĥát̂ úŝé â d́êd́îćât́êd́ ŵór̂ḱêŕ ôŕ ŵór̂ḱl̂ét̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "T̂h́ê d́ôćûḿêńt̂ d́îd́ n̂ót̂ f́îńîśĥ ĺôád̂ín̂ǵ b̂éf̂ór̂é n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Âṕp̂ B́âńn̂ér̂ ẃâś p̂ŕêśêńt̂ úp̂ón̂ ńâv́îǵât́îńĝ áŵáŷ."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Ĉh́r̂óm̂é P̂áŝśŵór̂d́ M̂án̂áĝér̂ ẃâś p̂ŕêśêńt̂ úp̂ón̂ ńâv́îǵât́îńĝ áŵáŷ."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "D̂ÓM̂ d́îśt̂íl̂ĺât́îón̂ ẃâś îń p̂ŕôǵr̂éŝś ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "D̂ÓM̂ D́îśt̂íl̂ĺêŕ V̂íêẃêŕ ŵáŝ ṕr̂éŝén̂t́ ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô éx̂t́êńŝíôńŝ úŝín̂ǵ m̂éŝśâǵîńĝ ÁP̂Í."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Êx́t̂én̂śîón̂ś ŵít̂h́ l̂ón̂ǵ-l̂ív̂éd̂ ćôńn̂éĉt́îón̂ śĥóûĺd̂ ćl̂óŝé t̂h́ê ćôńn̂éĉt́îón̂ b́êf́ôŕê én̂t́êŕîńĝ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô éx̂t́êńŝíôńŝ."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Êx́t̂én̂śîón̂ś ŵít̂h́ l̂ón̂ǵ-l̂ív̂éd̂ ćôńn̂éĉt́îón̂ át̂t́êḿp̂t́êd́ t̂ó ŝén̂d́ m̂éŝśâǵêś t̂ó f̂ŕâḿêś îń b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "M̂ód̂ál̂ d́îál̂óĝ śûćĥ áŝ f́ôŕm̂ ŕêśûb́m̂íŝśîón̂ ór̂ h́t̂t́p̂ ṕâśŝẃôŕd̂ d́îál̂óĝ ẃâś ŝh́ôẃn̂ f́ôŕ t̂h́ê ṕâǵê úp̂ón̂ ńâv́îǵât́îńĝ áŵáŷ."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "T̂h́ê óf̂f́l̂ín̂é p̂áĝé ŵáŝ śĥóŵń ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Ôút̂-Óf̂-Ḿêḿôŕŷ Ín̂t́êŕv̂én̂t́îón̂ b́âŕ ŵáŝ ṕr̂éŝén̂t́ ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "T̂h́êŕê ẃêŕê ṕêŕm̂íŝśîón̂ ŕêq́ûéŝt́ŝ úp̂ón̂ ńâv́îǵât́îńĝ áŵáŷ."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "P̂óp̂úp̂ b́l̂óĉḱêŕ ŵáŝ ṕr̂éŝén̂t́ ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Ŝáf̂é B̂ŕôẃŝín̂ǵ d̂ét̂áîĺŝ ẃêŕê śĥóŵń ûṕôń n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Ŝáf̂é B̂ŕôẃŝín̂ǵ ĉón̂śîd́êŕêd́ t̂h́îś p̂áĝé t̂ó b̂é âb́ûśîv́ê án̂d́ b̂ĺôćk̂éd̂ ṕôṕûṕ."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Â śêŕv̂íĉé ŵór̂ḱêŕ ŵáŝ áĉt́îv́ât́êd́ ŵh́îĺê t́ĥé p̂áĝé ŵáŝ ín̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô á d̂óĉúm̂én̂t́ êŕr̂ór̂."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "P̂áĝéŝ úŝín̂ǵ F̂én̂ćêd́F̂ŕâḿêś ĉán̂ńôt́ b̂é ŝt́ôŕêd́ îń b̂f́ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ t́ĥé ĉáĉh́ê t́ô ál̂ĺôẃ âńôt́ĥér̂ ṕâǵê t́ô b́ê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ǵr̂án̂t́êd́ m̂éd̂íâ śt̂ŕêám̂ áĉćêśŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "P̂áĝéŝ t́ĥát̂ úŝé p̂ór̂t́âĺŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Ôńl̂ý p̂áĝéŝ ĺôád̂éd̂ v́îá â ǴÊT́ r̂éq̂úêśt̂ ár̂é êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Ôńl̂ý p̂áĝéŝ ẃît́ĥ á ŝt́ât́ûś ĉód̂é ôf́ 2X̂X́ ĉán̂ b́ê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Îd́l̂éM̂án̂áĝér̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê án̂ óp̂én̂ Ín̂d́êx́êd́D̂B́ ĉón̂ńêćt̂íôń âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô án̂ Ín̂d́êx́êd́D̂B́ êv́êńt̂."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Îńêĺîǵîb́l̂é ÂṔÎś ŵér̂é ûśêd́."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "P̂áĝéŝ t́ĥát̂ `JavaScript` íŝ ín̂j́êćt̂éd̂ ín̂t́ô b́ŷ éx̂t́êńŝíôńŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "P̂áĝéŝ t́ĥát̂ á `StyleSheet` îś îńĵéĉt́êd́ îńt̂ó b̂ý êx́t̂én̂śîón̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Îńt̂ér̂ńâĺ êŕr̂ór̂."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Ĉh́r̂óm̂é d̂ét̂éĉt́êd́ âń ât́t̂ém̂ṕt̂ t́ô éx̂éĉút̂é Ĵáv̂áŜćr̂íp̂t́ ŵh́îĺê ín̂ t́ĥé ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "B̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé îś d̂íŝáb̂ĺêd́ d̂úê t́ô á k̂éêṕâĺîv́ê ŕêq́ûéŝt́."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "P̂áĝéŝ t́ĥát̂ úŝé K̂éŷb́ôár̂d́ l̂óĉḱ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | loading": {"message": "T̂h́ê ṕâǵê d́îd́ n̂ót̂ f́îńîśĥ ĺôád̂ín̂ǵ b̂éf̂ór̂é n̂áv̂íĝát̂ín̂ǵ âẃâý."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "P̂áĝéŝ ẃĥóŝé m̂áîń r̂éŝóûŕĉé ĥáŝ ćâćĥé-ĉón̂t́r̂ól̂:ńô-ćâćĥé ĉán̂ńôt́ êńt̂ér̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "P̂áĝéŝ ẃĥóŝé m̂áîń r̂éŝóûŕĉé ĥáŝ ćâćĥé-ĉón̂t́r̂ól̂:ńô-śt̂ór̂é ĉán̂ńôt́ êńt̂ér̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "N̂áv̂íĝát̂íôń ŵáŝ ćâńĉél̂ĺêd́ b̂éf̂ór̂é t̂h́ê ṕâǵê ćôúl̂d́ b̂é r̂éŝt́ôŕêd́ f̂ŕôḿ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ t́ĥé ĉáĉh́ê b́êćâúŝé âń âćt̂ív̂é n̂ét̂ẃôŕk̂ ćôńn̂éĉt́îón̂ ŕêćêív̂éd̂ t́ôó m̂úĉh́ d̂át̂á. Ĉh́r̂óm̂é l̂ím̂ít̂ś t̂h́ê ám̂óûńt̂ óf̂ d́ât́â t́ĥát̂ á p̂áĝé m̂áŷ ŕêćêív̂é ŵh́îĺê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ín̂f́l̂íĝh́t̂ f́êt́ĉh́() ôŕ X̂H́R̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê b́êćâúŝé âń âćt̂ív̂é n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ îńv̂ól̂v́êd́ â ŕêd́îŕêćt̂."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ t́ĥé ĉáĉh́ê b́êćâúŝé â ńêt́ŵór̂ḱ ĉón̂ńêćt̂íôń ŵáŝ óp̂én̂ t́ôó l̂ón̂ǵ. Ĉh́r̂óm̂é l̂ím̂ít̂ś t̂h́ê ám̂óûńt̂ óf̂ t́îḿê t́ĥát̂ á p̂áĝé m̂áŷ ŕêćêív̂é d̂át̂á ŵh́îĺê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "P̂áĝéŝ t́ĥát̂ d́ô ńôt́ ĥáv̂é â v́âĺîd́ r̂éŝṕôńŝé ĥéâd́ ĉán̂ńôt́ êńt̂ér̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "N̂áv̂íĝát̂íôń ĥáp̂ṕêńêd́ îń â f́r̂ám̂é ôt́ĥér̂ t́ĥán̂ t́ĥé m̂áîń f̂ŕâḿê."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "P̂áĝé ŵít̂h́ ôńĝóîńĝ ín̂d́êx́êd́ D̂B́ t̂ŕâńŝáĉt́îón̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "P̂áĝéŝ ẃît́ĥ án̂ ín̂-f́l̂íĝh́t̂ ńêt́ŵór̂ḱ r̂éq̂úêśt̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "P̂áĝéŝ ẃît́ĥ án̂ ín̂-f́l̂íĝh́t̂ f́êt́ĉh́ n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "P̂áĝéŝ ẃît́ĥ án̂ ín̂-f́l̂íĝh́t̂ ńêt́ŵór̂ḱ r̂éq̂úêśt̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "P̂áĝéŝ ẃît́ĥ án̂ ín̂-f́l̂íĝh́t̂ X́ĤŔ n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "P̂áĝéŝ t́ĥát̂ úŝé P̂áŷḿêńt̂Ḿâńâǵêŕ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "P̂áĝéŝ t́ĥát̂ úŝé P̂íĉt́ûŕê-ín̂-Ṕîćt̂úr̂é âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | portal": {"message": "P̂áĝéŝ t́ĥát̂ úŝé p̂ór̂t́âĺŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | printing": {"message": "P̂áĝéŝ t́ĥát̂ śĥóŵ Ṕr̂ín̂t́îńĝ ÚÎ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "T̂h́ê ṕâǵê ẃâś ôṕêńêd́ ûśîńĝ '`window.open()`' án̂d́ âńôt́ĥér̂ t́âb́ ĥáŝ á r̂éf̂ér̂én̂ćê t́ô ít̂, ór̂ t́ĥé p̂áĝé ôṕêńêd́ â ẃîńd̂óŵ."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "T̂h́ê ŕêńd̂ér̂ér̂ ṕr̂óĉéŝś f̂ór̂ t́ĥé p̂áĝé îń b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé ĉŕâśĥéd̂."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "T̂h́ê ŕêńd̂ér̂ér̂ ṕr̂óĉéŝś f̂ór̂ t́ĥé p̂áĝé îń b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé ŵáŝ ḱîĺl̂éd̂."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ âúd̂íô ćâṕt̂úr̂é p̂ér̂ḿîśŝíôńŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ ŝén̂śôŕ p̂ér̂ḿîśŝíôńŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ b̂áĉḱĝŕôún̂d́ ŝýn̂ć ôŕ f̂ét̂ćĥ ṕêŕm̂íŝśîón̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ M̂ÍD̂Í p̂ér̂ḿîśŝíôńŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ n̂ót̂íf̂íĉát̂íôńŝ ṕêŕm̂íŝśîón̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ ŝt́ôŕâǵê áĉćêśŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ŕêq́ûéŝt́êd́ v̂íd̂éô ćâṕt̂úr̂é p̂ér̂ḿîśŝíôńŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Ôńl̂ý p̂áĝéŝ ẃĥóŝé ÛŔL̂ śĉh́êḿê íŝ H́T̂T́P̂ / H́T̂T́P̂Ś ĉán̂ b́ê ćâćĥéd̂."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "T̂h́ê ṕâǵê ẃâś ĉĺâím̂éd̂ b́ŷ á ŝér̂v́îćê ẃôŕk̂ér̂ ẃĥíl̂é ît́ îś îń b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Â śêŕv̂íĉé ŵór̂ḱêŕ ât́t̂ém̂ṕt̂éd̂ t́ô śêńd̂ t́ĥé p̂áĝé îń b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé â `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Ŝér̂v́îćêẂôŕk̂ér̂ ẃâś ûńr̂éĝíŝt́êŕêd́ ŵh́îĺê á p̂áĝé ŵáŝ ín̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "T̂h́ê ṕâǵê ẃâś êv́îćt̂éd̂ f́r̂óm̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê d́ûé t̂ó â śêŕv̂íĉé ŵór̂ḱêŕ âćt̂ív̂át̂íôń."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Ĉh́r̂óm̂é r̂éŝt́âŕt̂éd̂ án̂d́ ĉĺêár̂éd̂ t́ĥé b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé êńt̂ŕîéŝ."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŝh́âŕêd́Ŵór̂ḱêŕ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŝṕêéĉh́R̂éĉóĝńîźêŕ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŝṕêéĉh́Ŝýn̂t́ĥéŝíŝ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Âń îf́r̂ám̂é ôń t̂h́ê ṕâǵê śt̂ár̂t́êd́ â ńâv́îǵât́îón̂ t́ĥát̂ d́îd́ n̂ót̂ ćôḿp̂ĺêt́ê."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "P̂áĝéŝ ẃĥóŝé ŝúb̂ŕêśôúr̂ćê h́âś ĉáĉh́ê-ćôńt̂ŕôĺ:n̂ó-ĉáĉh́ê ćâńn̂ót̂ én̂t́êŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "P̂áĝéŝ ẃĥóŝé ŝúb̂ŕêśôúr̂ćê h́âś ĉáĉh́ê-ćôńt̂ŕôĺ:n̂ó-ŝt́ôŕê ćâńn̂ót̂ én̂t́êŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | timeout": {"message": "T̂h́ê ṕâǵê éx̂ćêéd̂éd̂ t́ĥé m̂áx̂ím̂úm̂ t́îḿê ín̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê án̂d́ ŵáŝ éx̂ṕîŕêd́."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "T̂h́ê ṕâǵê t́îḿêd́ ôút̂ én̂t́êŕîńĝ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê (ĺîḱêĺŷ d́ûé t̂ó l̂ón̂ǵ-r̂ún̂ńîńĝ ṕâǵêh́îd́ê h́âńd̂ĺêŕŝ)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "T̂h́ê ṕâǵê h́âś âń ûńl̂óâd́ ĥán̂d́l̂ér̂ ín̂ t́ĥé m̂áîń f̂ŕâḿê."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "T̂h́ê ṕâǵê h́âś âń ûńl̂óâd́ ĥán̂d́l̂ér̂ ín̂ á ŝúb̂ f́r̂ám̂é."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "B̂ŕôẃŝér̂ h́âś ĉh́âńĝéd̂ t́ĥé ûśêŕ âǵêńt̂ óv̂ér̂ŕîd́ê h́êád̂ér̂."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "P̂áĝéŝ t́ĥát̂ h́âv́ê ǵr̂án̂t́êd́ âćĉéŝś t̂ó r̂éĉór̂d́ v̂íd̂éô ór̂ áûd́îó âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂D́ât́âb́âśê ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | webHID": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂H́ÎD́ âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂Ĺôćk̂ś âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂Ńf̂ć âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâd́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂ÓT̂ṔŜér̂v́îćê ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́f̂ćâćĥé."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "P̂áĝéŝ ẃît́ĥ Ẃêb́R̂T́Ĉ ćâńn̂ót̂ én̂t́êŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | webShare": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂Śĥár̂é âŕê ńôt́ ĉúr̂ŕêńt̂ĺŷ él̂íĝíb̂ĺê f́ôŕ b̂áĉḱ/f̂ór̂ẃâd́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "P̂áĝéŝ ẃît́ĥ Ẃêb́Ŝóĉḱêt́ ĉán̂ńôt́ êńt̂ér̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "P̂áĝéŝ ẃît́ĥ Ẃêb́T̂ŕâńŝṕôŕt̂ ćâńn̂ót̂ én̂t́êŕ b̂áĉḱ/f̂ór̂ẃâŕd̂ ćâćĥé."}, "core/lib/bf-cache-strings.js | webXR": {"message": "P̂áĝéŝ t́ĥát̂ úŝé Ŵéb̂X́R̂ ár̂é n̂ót̂ ćûŕr̂én̂t́l̂ý êĺîǵîb́l̂é f̂ór̂ b́âćk̂/f́ôŕŵár̂d́ ĉáĉh́ê."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Ĉón̂śîd́êŕ âd́d̂ín̂ǵ ĥt́t̂ṕŝ: án̂d́ ĥt́t̂ṕ: ÛŔL̂ śĉh́êḿêś (îǵn̂ór̂éd̂ b́ŷ b́r̂óŵśêŕŝ śûṕp̂ór̂t́îńĝ `'strict-dynamic'`) t́ô b́ê b́âćk̂ẃâŕd̂ ćôḿp̂át̂íb̂ĺê ẃît́ĥ ól̂d́êŕ b̂ŕôẃŝér̂ś."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "`disown-opener` îś d̂ép̂ŕêćât́êd́ ŝín̂ćê ĆŜṔ3. P̂ĺêáŝé, ûśê t́ĥé Ĉŕôśŝ-Ór̂íĝín̂-Óp̂én̂ér̂-Ṕôĺîćŷ h́êád̂ér̂ ín̂śt̂éâd́."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "`referrer` îś d̂ép̂ŕêćât́êd́ ŝín̂ćê ĆŜṔ2. P̂ĺêáŝé, ûśê t́ĥé R̂éf̂ér̂ŕêŕ-P̂ól̂íĉý ĥéâd́êŕ îńŝt́êád̂."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "`reflected-xss` îś d̂ép̂ŕêćât́êd́ ŝín̂ćê ĆŜṔ2. P̂ĺêáŝé, ûśê t́ĥé X̂-X́ŜŚ-P̂ŕôt́êćt̂íôń ĥéâd́êŕ îńŝt́êád̂."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "M̂íŝśîńĝ `base-uri` ál̂ĺôẃŝ ín̂j́êćt̂éd̂ `<base>` t́âǵŝ t́ô śêt́ t̂h́ê b́âśê ÚR̂Ĺ f̂ór̂ ál̂ĺ r̂él̂át̂ív̂é ÛŔL̂ś (ê.ǵ. ŝćr̂íp̂t́ŝ) t́ô án̂ át̂t́âćk̂ér̂ ćôńt̂ŕôĺl̂éd̂ d́ôḿâín̂. Ćôńŝíd̂ér̂ śêt́t̂ín̂ǵ `base-uri` t̂ó `'none'` ôŕ `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "M̂íŝśîńĝ `object-src` ál̂ĺôẃŝ t́ĥé îńĵéĉt́îón̂ óf̂ ṕl̂úĝín̂ś t̂h́ât́ êx́êćût́ê ún̂śâf́ê śĉŕîṕt̂ś. Ĉón̂śîd́êŕ ŝét̂t́îńĝ `object-src` t́ô `'none'` íf̂ ýôú ĉán̂."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "`script-src` d̂ír̂éĉt́îv́ê íŝ ḿîśŝín̂ǵ. T̂h́îś ĉán̂ ál̂ĺôẃ t̂h́ê éx̂éĉút̂íôń ôf́ ûńŝáf̂é ŝćr̂íp̂t́ŝ."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "D̂íd̂ ýôú f̂ór̂ǵêt́ t̂h́ê śêḿîćôĺôń? {keyword} ŝéêḿŝ t́ô b́ê á d̂ír̂éĉt́îv́ê, ńôt́ â ḱêýŵór̂d́."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "N̂ón̂ćêś ŝh́ôúl̂d́ ûśê t́ĥé b̂áŝé64 ĉh́âŕŝét̂."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "N̂ón̂ćêś ŝh́ôúl̂d́ b̂é ât́ l̂éâśt̂ 8 ćĥár̂áĉt́êŕŝ ĺôńĝ."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Âv́ôíd̂ úŝín̂ǵ p̂ĺâín̂ ÚR̂Ĺ ŝćĥém̂éŝ ({keyword}) ín̂ t́ĥíŝ d́îŕêćt̂ív̂é. P̂ĺâín̂ ÚR̂Ĺ ŝćĥém̂éŝ ál̂ĺôẃ ŝćr̂íp̂t́ŝ t́ô b́ê śôúr̂ćêd́ f̂ŕôḿ âń ûńŝáf̂é d̂óm̂áîń."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Âv́ôíd̂ úŝín̂ǵ p̂ĺâín̂ ẃîĺd̂ćâŕd̂ś ({keyword}) îń t̂h́îś d̂ír̂éĉt́îv́ê. Ṕl̂áîń ŵíl̂d́ĉár̂d́ŝ ál̂ĺôẃ ŝćr̂íp̂t́ŝ t́ô b́ê śôúr̂ćêd́ f̂ŕôḿ âń ûńŝáf̂é d̂óm̂áîń."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "N̂ó ĈŚP̂ ćôńf̂íĝúr̂éŝ á r̂ép̂ór̂t́îńĝ d́êśt̂ín̂át̂íôń. T̂h́îś m̂ák̂éŝ ít̂ d́îf́f̂íĉúl̂t́ t̂ó m̂áîńt̂áîń t̂h́ê ĆŜṔ ôv́êŕ t̂ím̂é âńd̂ ḿôńît́ôŕ f̂ór̂ án̂ý b̂ŕêák̂áĝéŝ."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "T̂h́ê ŕêṕôŕt̂ín̂ǵ d̂éŝt́îńât́îón̂ íŝ ón̂ĺŷ ćôńf̂íĝúr̂éd̂ v́îá t̂h́ê ŕêṕôŕt̂-t́ô d́îŕêćt̂ív̂é. T̂h́îś d̂ír̂éĉt́îv́ê íŝ ón̂ĺŷ śûṕp̂ór̂t́êd́ îń Ĉh́r̂óm̂íûḿ-b̂áŝéd̂ b́r̂óŵśêŕŝ śô ít̂ íŝ ŕêćôḿm̂én̂d́êd́ t̂ó âĺŝó ûśê á `report-uri` d̂ír̂éĉt́îv́ê."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Ĥóŝt́ âĺl̂óŵĺîśt̂ś ĉán̂ f́r̂éq̂úêńt̂ĺŷ b́ê b́ŷṕâśŝéd̂. Ćôńŝíd̂ér̂ úŝín̂ǵ ĈŚP̂ ńôńĉéŝ ór̂ h́âśĥéŝ ín̂śt̂éâd́, âĺôńĝ ẃît́ĥ `'strict-dynamic'` íf̂ ńêćêśŝár̂ý."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Ûńk̂ńôẃn̂ ĆŜṔ d̂ír̂éĉt́îv́ê."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} ŝéêḿŝ t́ô b́ê án̂ ín̂v́âĺîd́ k̂éŷẃôŕd̂."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` âĺl̂óŵś t̂h́ê éx̂éĉút̂íôń ôf́ ûńŝáf̂é îń-p̂áĝé ŝćr̂íp̂t́ŝ án̂d́ êv́êńt̂ h́âńd̂ĺêŕŝ. Ćôńŝíd̂ér̂ úŝín̂ǵ ĈŚP̂ ńôńĉéŝ ór̂ h́âśĥéŝ t́ô ál̂ĺôẃ ŝćr̂íp̂t́ŝ ín̂d́îv́îd́ûál̂ĺŷ."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Ĉón̂śîd́êŕ âd́d̂ín̂ǵ `'unsafe-inline'` (îǵn̂ór̂éd̂ b́ŷ b́r̂óŵśêŕŝ śûṕp̂ór̂t́îńĝ ńôńĉéŝ/h́âśĥéŝ) t́ô b́ê b́âćk̂ẃâŕd̂ ćôḿp̂át̂íb̂ĺê ẃît́ĥ ól̂d́êŕ b̂ŕôẃŝér̂ś."}, "core/lib/deprecation-description.js | feature": {"message": "Ĉh́êćk̂ t́ĥé f̂éât́ûŕê śt̂át̂úŝ ṕâǵê f́ôŕ m̂ór̂é d̂ét̂áîĺŝ."}, "core/lib/deprecation-description.js | milestone": {"message": "T̂h́îś ĉh́âńĝé ŵíl̂ĺ ĝó îńt̂ó êf́f̂éĉt́ ŵít̂h́ m̂íl̂éŝt́ôńê {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "D̂ép̂ŕêćât́êd́ F̂éât́ûŕê Úŝéd̂"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Âút̂h́ôŕîźât́îón̂ ẃîĺl̂ ńôt́ b̂é ĉóv̂ér̂éd̂ b́ŷ t́ĥé ŵíl̂d́ĉár̂d́ ŝým̂b́ôĺ (*) îń ĈÓR̂Ś `Access-Control-Allow-Headers` ĥán̂d́l̂ín̂ǵ."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "R̂éŝóûŕĉé r̂éq̂úêśt̂ś ŵh́ôśê ÚR̂Ĺŝ ćôńt̂áîńêd́ b̂ót̂h́ r̂ém̂óv̂éd̂ ẃĥít̂éŝṕâćê `(n|r|t)` ćĥár̂áĉt́êŕŝ án̂d́ l̂éŝś-t̂h́âń ĉh́âŕâćt̂ér̂ś (`<`) âŕê b́l̂óĉḱêd́. P̂ĺêáŝé r̂ém̂óv̂é n̂éŵĺîńêś âńd̂ én̂ćôd́ê ĺêśŝ-t́ĥán̂ ćĥár̂áĉt́êŕŝ f́r̂óm̂ ṕl̂áĉéŝ ĺîḱê él̂ém̂én̂t́ ât́t̂ŕîb́ût́ê v́âĺûéŝ ín̂ ór̂d́êŕ t̂ó l̂óâd́ t̂h́êśê ŕêśôúr̂ćêś."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` îś d̂ép̂ŕêćât́êd́, îńŝt́êád̂ úŝé ŝt́âńd̂ár̂d́îźêd́ ÂṔÎ: Ńâv́îǵât́îón̂ T́îḿîńĝ 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` îś d̂ép̂ŕêćât́êd́, îńŝt́êád̂ úŝé ŝt́âńd̂ár̂d́îźêd́ ÂṔÎ: Ṕâín̂t́ T̂ím̂ín̂ǵ."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` îś d̂ép̂ŕêćât́êd́, îńŝt́êád̂ úŝé ŝt́âńd̂ár̂d́îźêd́ ÂṔÎ: `nextHopProtocol` ín̂ Ńâv́îǵât́îón̂ T́îḿîńĝ 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Ĉóôḱîéŝ ćôńt̂áîńîńĝ á `(0|r|n)` ĉh́âŕâćt̂ér̂ ẃîĺl̂ b́ê ŕêj́êćt̂éd̂ ín̂śt̂éâd́ ôf́ t̂ŕûńĉát̂éd̂."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "R̂él̂áx̂ín̂ǵ t̂h́ê śâḿê-ór̂íĝín̂ ṕôĺîćŷ b́ŷ śêt́t̂ín̂ǵ `document.domain` îś d̂ép̂ŕêćât́êd́, âńd̂ ẃîĺl̂ b́ê d́îśâb́l̂éd̂ b́ŷ d́êf́âúl̂t́. T̂h́îś d̂ép̂ŕêćât́îón̂ ẃâŕn̂ín̂ǵ îś f̂ór̂ á ĉŕôśŝ-ór̂íĝín̂ áĉćêśŝ t́ĥát̂ ẃâś êńâb́l̂éd̂ b́ŷ śêt́t̂ín̂ǵ `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "T̂ŕîǵĝér̂ín̂ǵ ŵín̂d́ôẃ.âĺêŕt̂ f́r̂óm̂ ćr̂óŝś ôŕîǵîń îf́r̂ám̂éŝ h́âś b̂éêń d̂ép̂ŕêćât́êd́ âńd̂ ẃîĺl̂ b́ê ŕêḿôv́êd́ îń t̂h́ê f́ût́ûŕê."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "T̂ŕîǵĝér̂ín̂ǵ ŵín̂d́ôẃ.ĉón̂f́îŕm̂ f́r̂óm̂ ćr̂óŝś ôŕîǵîń îf́r̂ám̂éŝ h́âś b̂éêń d̂ép̂ŕêćât́êd́ âńd̂ ẃîĺl̂ b́ê ŕêḿôv́êd́ îń t̂h́ê f́ût́ûŕê."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "T̂h́ê `disableRemotePlayback` át̂t́r̂íb̂út̂é ŝh́ôúl̂d́ b̂é ûśêd́ îń ôŕd̂ér̂ t́ô d́îśâb́l̂é t̂h́ê d́êf́âúl̂t́ Ĉáŝt́ îńt̂éĝŕât́îón̂ ín̂śt̂éâd́ ôf́ ûśîńĝ `-internal-media-controls-overlay-cast-button` śêĺêćt̂ór̂."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Ŝúp̂ṕôŕt̂ f́ôŕ d̂át̂á: ÛŔL̂ś îń ŜV́Ĝ <úŝé> êĺêḿêńt̂ íŝ d́êṕr̂éĉát̂éd̂ án̂d́ ît́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂ ín̂ t́ĥé f̂út̂úr̂é."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "R̂él̂áx̂ín̂ǵ t̂h́ê śâḿê-ór̂íĝín̂ ṕôĺîćŷ b́ŷ śêt́t̂ín̂ǵ `document.domain` îś d̂ép̂ŕêćât́êd́, âńd̂ ẃîĺl̂ b́ê d́îśâb́l̂éd̂ b́ŷ d́êf́âúl̂t́. T̂ó ĉón̂t́îńûé ûśîńĝ t́ĥíŝ f́êát̂úr̂é, p̂ĺêáŝé ôṕt̂-óût́ ôf́ ôŕîǵîń-k̂éŷéd̂ áĝén̂t́ ĉĺûśt̂ér̂ś b̂ý ŝén̂d́îńĝ án̂ `Origin-Agent-Cluster: ?0` h́êád̂ér̂ ál̂ón̂ǵ ŵít̂h́ t̂h́ê H́T̂T́P̂ ŕêśp̂ón̂śê f́ôŕ t̂h́ê d́ôćûḿêńt̂ án̂d́ f̂ŕâḿêś. Ŝéê h́t̂t́p̂ś://d̂év̂él̂óp̂ér̂.ćĥŕôḿê.ćôḿ/b̂ĺôǵ/îḿm̂út̂áb̂ĺê-d́ôćûḿêńt̂-d́ôḿâín̂/ f́ôŕ m̂ór̂é d̂ét̂áîĺŝ."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "D̂ÓM̂ Ḿût́ât́îón̂ Év̂én̂t́ŝ, ín̂ćl̂úd̂ín̂ǵ `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument`, âńd̂ `DOMCharacterDataModified` ár̂é d̂ép̂ŕêćât́êd́ (ĥt́t̂ṕŝ://ẃ3ĉ.ǵît́ĥúb̂.íô/úîév̂én̂t́ŝ/#ĺêǵâćŷ-év̂én̂t́-t̂ýp̂éŝ) án̂d́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂. Ṕl̂éâśê úŝé `MutationObserver` îńŝt́êád̂."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "T̂h́ê `Expect-CT` h́êád̂ér̂ íŝ d́êṕr̂éĉát̂éd̂ án̂d́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂. Ćĥŕôḿê ŕêq́ûír̂éŝ Ćêŕt̂íf̂íĉát̂é T̂ŕâńŝṕâŕêńĉý f̂ór̂ ál̂ĺ p̂úb̂ĺîćl̂ý t̂ŕûśt̂éd̂ ćêŕt̂íf̂íĉát̂éŝ íŝśûéd̂ áf̂t́êŕ Âṕr̂íl̂ 30, 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "`getCurrentPosition()` âńd̂ `watchPosition()` ńô ĺôńĝér̂ ẃôŕk̂ ón̂ ín̂śêćûŕê ór̂íĝín̂ś. T̂ó ûśê t́ĥíŝ f́êát̂úr̂é, ŷóû śĥóûĺd̂ ćôńŝíd̂ér̂ śŵít̂ćĥín̂ǵ ŷóûŕ âṕp̂ĺîćât́îón̂ t́ô á ŝéĉúr̂é ôŕîǵîń, ŝúĉh́ âś ĤT́T̂ṔŜ. Śêé ĥt́t̂ṕŝ://ǵôó.ĝĺê/ćĥŕôḿê-ín̂śêćûŕê-ór̂íĝín̂ś f̂ór̂ ḿôŕê d́êt́âíl̂ś."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` âńd̂ `watchPosition()` ár̂é d̂ép̂ŕêćât́êd́ ôń îńŝéĉúr̂é ôŕîǵîńŝ. T́ô úŝé t̂h́îś f̂éât́ûŕê, ýôú ŝh́ôúl̂d́ ĉón̂śîd́êŕ ŝẃît́ĉh́îńĝ ýôúr̂ áp̂ṕl̂íĉát̂íôń t̂ó â śêćûŕê ór̂íĝín̂, śûćĥ áŝ H́T̂T́P̂Ś. Ŝéê h́t̂t́p̂ś://ĝóô.ǵl̂é/ĉh́r̂óm̂é-îńŝéĉúr̂é-ôŕîǵîńŝ f́ôŕ m̂ór̂é d̂ét̂áîĺŝ."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` n̂ó l̂ón̂ǵêŕ ŵór̂ḱŝ ón̂ ín̂śêćûŕê ór̂íĝín̂ś. T̂ó ûśê t́ĥíŝ f́êát̂úr̂é, ŷóû śĥóûĺd̂ ćôńŝíd̂ér̂ śŵít̂ćĥín̂ǵ ŷóûŕ âṕp̂ĺîćât́îón̂ t́ô á ŝéĉúr̂é ôŕîǵîń, ŝúĉh́ âś ĤT́T̂ṔŜ. Śêé ĥt́t̂ṕŝ://ǵôó.ĝĺê/ćĥŕôḿê-ín̂śêćûŕê-ór̂íĝín̂ś f̂ór̂ ḿôŕê d́êt́âíl̂ś."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê `RTCPeerConnectionIceErrorEvent.address` ór̂ `RTCPeerConnectionIceErrorEvent.port` ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "T̂h́ê ḿêŕĉh́âńt̂ ór̂íĝín̂ án̂d́ âŕb̂ít̂ŕâŕŷ d́ât́â f́r̂óm̂ t́ĥé `canmakepayment` ŝér̂v́îćê ẃôŕk̂ér̂ év̂én̂t́ âŕê d́êṕr̂éĉát̂éd̂ án̂d́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "T̂h́ê ẃêb́ŝít̂é r̂éq̂úêśt̂éd̂ á ŝúb̂ŕêśôúr̂ćê f́r̂óm̂ á n̂ét̂ẃôŕk̂ t́ĥát̂ ít̂ ćôúl̂d́ ôńl̂ý âćĉéŝś b̂éĉáûśê óf̂ ít̂ś ûśêŕŝ' ṕr̂ív̂íl̂éĝéd̂ ńêt́ŵór̂ḱ p̂óŝít̂íôń. T̂h́êśê ŕêq́ûéŝt́ŝ éx̂ṕôśê ńôń-p̂úb̂ĺîć d̂év̂íĉéŝ án̂d́ ŝér̂v́êŕŝ t́ô t́ĥé îńt̂ér̂ńêt́, îńĉŕêáŝín̂ǵ t̂h́ê ŕîśk̂ óf̂ á ĉŕôśŝ-śît́ê ŕêq́ûéŝt́ f̂ór̂ǵêŕŷ (ĆŜŔF̂) át̂t́âćk̂, án̂d́/ôŕ îńf̂ór̂ḿât́îón̂ ĺêák̂áĝé. T̂ó m̂ít̂íĝát̂é t̂h́êśê ŕîśk̂ś, Ĉh́r̂óm̂é d̂ép̂ŕêćât́êś r̂éq̂úêśt̂ś t̂ó n̂ón̂-ṕûb́l̂íĉ śûb́r̂éŝóûŕĉéŝ ẃĥén̂ ín̂ít̂íât́êd́ f̂ŕôḿ n̂ón̂-śêćûŕê ćôńt̂éx̂t́ŝ, án̂d́ ŵíl̂ĺ ŝt́âŕt̂ b́l̂óĉḱîńĝ t́ĥém̂."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "T̂h́ê `dailyUpdateUrl` f́îél̂d́ ôf́ `InterestGroups` p̂áŝśêd́ t̂ó `joinAdInterestGroup()` ĥáŝ b́êén̂ ŕêńâḿêd́ t̂ó `updateUrl`, t̂ó m̂ór̂é âćĉúr̂át̂él̂ý r̂éf̂ĺêćt̂ ít̂ś b̂éĥáv̂íôŕ."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "ĈŚŜ ćâńn̂ót̂ b́ê ĺôád̂éd̂ f́r̂óm̂ `file:` ÚR̂Ĺŝ ún̂ĺêśŝ t́ĥéŷ én̂d́ îń â `.css` f́îĺê éx̂t́êńŝíôń."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Ûśîńĝ `SourceBuffer.abort()` t́ô áb̂ór̂t́ `remove()`'ŝ áŝýn̂ćĥŕôńôúŝ ŕâńĝé r̂ém̂óv̂ál̂ íŝ d́êṕr̂éĉát̂éd̂ d́ûé t̂ó ŝṕêćîf́îćât́îón̂ ćĥán̂ǵê. Śûṕp̂ór̂t́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂ ín̂ t́ĥé f̂út̂úr̂é. Ŷóû śĥóûĺd̂ ĺîśt̂én̂ t́ô t́ĥé `updateend` êv́êńt̂ ín̂śt̂éâd́. `abort()` îś îńt̂én̂d́êd́ t̂ó ôńl̂ý âb́ôŕt̂ án̂ áŝýn̂ćĥŕôńôúŝ ḿêd́îá âṕp̂én̂d́ ôŕ r̂éŝét̂ ṕâŕŝér̂ śt̂át̂é."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Ŝét̂t́îńĝ `MediaSource.duration` b́êĺôẃ t̂h́ê h́îǵĥéŝt́ p̂ŕêśêńt̂át̂íôń t̂ím̂éŝt́âḿp̂ óf̂ án̂ý b̂úf̂f́êŕêd́ ĉód̂éd̂ f́r̂ám̂éŝ íŝ d́êṕr̂éĉát̂éd̂ d́ûé t̂ó ŝṕêćîf́îćât́îón̂ ćĥán̂ǵê. Śûṕp̂ór̂t́ f̂ór̂ ím̂ṕl̂íĉít̂ ŕêḿôv́âĺ ôf́ t̂ŕûńĉát̂éd̂ b́ûf́f̂ér̂éd̂ ḿêd́îá ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂ ín̂ t́ĥé f̂út̂úr̂é. Ŷóû śĥóûĺd̂ ín̂śt̂éâd́ p̂ér̂f́ôŕm̂ éx̂ṕl̂íĉít̂ `remove(newDuration, oldDuration)` ón̂ ál̂ĺ `sourceBuffers`, ŵh́êŕê `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "T̂h́ê ól̂d́êŕ, n̂ón̂-śt̂án̂d́âŕd̂íẑéd̂ `shadowroot` át̂t́r̂íb̂út̂é îś d̂ép̂ŕêćât́êd́, âńd̂ ẃîĺl̂ *ńô ĺôńĝér̂ f́ûńĉt́îón̂* ín̂ Ḿ119. P̂ĺêáŝé ûśê t́ĥé n̂éŵ, śt̂án̂d́âŕd̂íẑéd̂ `shadowrootmode` át̂t́r̂íb̂út̂é îńŝt́êád̂."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Ŵéb̂ ḾÎD́Î ẃîĺl̂ áŝḱ â ṕêŕm̂íŝśîón̂ t́ô úŝé êv́êń îf́ t̂h́ê śŷśêx́ îś n̂ót̂ śp̂éĉíf̂íêd́ îń t̂h́ê `MIDIOptions`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "T̂h́ê Ńôt́îf́îćât́îón̂ ÁP̂Í m̂áŷ ńô ĺôńĝér̂ b́ê úŝéd̂ f́r̂óm̂ ín̂śêćûŕê ór̂íĝín̂ś. Ŷóû śĥóûĺd̂ ćôńŝíd̂ér̂ śŵít̂ćĥín̂ǵ ŷóûŕ âṕp̂ĺîćât́îón̂ t́ô á ŝéĉúr̂é ôŕîǵîń, ŝúĉh́ âś ĤT́T̂ṔŜ. Śêé ĥt́t̂ṕŝ://ǵôó.ĝĺê/ćĥŕôḿê-ín̂śêćûŕê-ór̂íĝín̂ś f̂ór̂ ḿôŕê d́êt́âíl̂ś."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "P̂ér̂ḿîśŝíôń f̂ór̂ t́ĥé N̂ót̂íf̂íĉát̂íôń ÂṔÎ ḿâý n̂ó l̂ón̂ǵêŕ b̂é r̂éq̂úêśt̂éd̂ f́r̂óm̂ á ĉŕôśŝ-ór̂íĝín̂ íf̂ŕâḿê. Ýôú ŝh́ôúl̂d́ ĉón̂śîd́êŕ r̂éq̂úêśt̂ín̂ǵ p̂ér̂ḿîśŝíôń f̂ŕôḿ â t́ôṕ-l̂év̂él̂ f́r̂ám̂é ôŕ ôṕêńîńĝ á n̂éŵ ẃîńd̂óŵ ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Ôṕt̂íôń `imageOrientation: 'none'` îń ĉŕêát̂éÎḿâǵêB́ît́m̂áp̂ íŝ d́êṕr̂éĉát̂éd̂. Ṕl̂éâśê úŝé ĉŕêát̂éÎḿâǵêB́ît́m̂áp̂ ẃît́ĥ óp̂t́îón̂ \\{imageOrientation: 'from-image'\\} ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Ŷóûŕ p̂ár̂t́n̂ér̂ íŝ ńêǵôt́îát̂ín̂ǵ âń ôb́ŝól̂ét̂é (D̂)T́L̂Ś v̂ér̂śîón̂. Ṕl̂éâśê ćĥéĉḱ ŵít̂h́ ŷóûŕ p̂ár̂t́n̂ér̂ t́ô h́âv́ê t́ĥíŝ f́îx́êd́."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Ŝṕêćîf́ŷín̂ǵ `overflow: visible` ôń îḿĝ, v́îd́êó âńd̂ ćâńv̂áŝ t́âǵŝ ḿâý ĉáûśê t́ĥém̂ t́ô ṕr̂ód̂úĉé v̂íŝúâĺ ĉón̂t́êńt̂ óût́ŝíd̂é ôf́ t̂h́ê él̂ém̂én̂t́ b̂óûńd̂ś. Ŝéê h́t̂t́p̂ś://ĝít̂h́ûb́.ĉóm̂/ẂÎĆĜ/śĥár̂éd̂-él̂ém̂én̂t́-t̂ŕâńŝít̂íôńŝ/b́l̂ób̂/ḿâín̂/d́êb́ûǵĝín̂ǵ_ôv́êŕf̂ĺôẃ_ôń_îḿâǵêś.m̂d́."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "`paymentManager.instruments` îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê j́ûśt̂-ín̂-t́îḿê ín̂śt̂ál̂ĺ f̂ór̂ ṕâým̂én̂t́ ĥán̂d́l̂ér̂ś îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Ŷóûŕ `PaymentRequest` ĉál̂ĺ b̂ýp̂áŝśêd́ Ĉón̂t́êńt̂-Śêćûŕît́ŷ-Ṕôĺîćŷ (ĆŜṔ) `connect-src` d̂ír̂éĉt́îv́ê. T́ĥíŝ b́ŷṕâśŝ íŝ d́êṕr̂éĉát̂éd̂. Ṕl̂éâśê ád̂d́ t̂h́ê ṕâým̂én̂t́ m̂ét̂h́ôd́ îd́êńt̂íf̂íêŕ f̂ŕôḿ t̂h́ê `PaymentRequest` ÁP̂Í (îń `supportedMethods` f̂íêĺd̂) t́ô ýôúr̂ ĆŜṔ `connect-src` d̂ír̂éĉt́îv́ê."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "`StorageType.persistent` îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê śt̂án̂d́âŕd̂íẑéd̂ `navigator.storage` ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "`<source src>` ŵít̂h́ â `<picture>` ṕâŕêńt̂ íŝ ín̂v́âĺîd́ âńd̂ t́ĥér̂éf̂ór̂é îǵn̂ór̂éd̂. Ṕl̂éâśê úŝé `<source srcset>` îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "ŵéb̂ḱît́Ĉán̂ćêĺÂńîḿât́îón̂F́r̂ám̂é îś v̂én̂d́ôŕ-ŝṕêćîf́îć. P̂ĺêáŝé ûśê t́ĥé ŝt́âńd̂ár̂d́ ĉán̂ćêĺÂńîḿât́îón̂F́r̂ám̂é îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "ŵéb̂ḱît́R̂éq̂úêśt̂Án̂ím̂át̂íôńF̂ŕâḿê íŝ v́êńd̂ór̂-śp̂éĉíf̂íĉ. Ṕl̂éâśê úŝé t̂h́ê śt̂án̂d́âŕd̂ ŕêq́ûéŝt́Âńîḿât́îón̂F́r̂ám̂é îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́D̂íŝṕl̂áŷín̂ǵF̂úl̂ĺŝćr̂éêń îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê D́ôćûḿêńt̂.f́ûĺl̂śĉŕêén̂Él̂ém̂én̂t́ îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́Êńt̂ér̂F́ûĺl̂śĉŕêén̂() íŝ d́êṕr̂éĉát̂éd̂. Ṕl̂éâśê úŝé Êĺêḿêńt̂.ŕêq́ûéŝt́F̂úl̂ĺŝćr̂éêń() îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́Êńt̂ér̂F́ûĺl̂Śĉŕêén̂() íŝ d́êṕr̂éĉát̂éd̂. Ṕl̂éâśê úŝé Êĺêḿêńt̂.ŕêq́ûéŝt́F̂úl̂ĺŝćr̂éêń() îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́Êx́ît́F̂úl̂ĺŝćr̂éêń() îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê D́ôćûḿêńt̂.éx̂ít̂F́ûĺl̂śĉŕêén̂() ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́Êx́ît́F̂úl̂ĺŜćr̂éêń() îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê D́ôćûḿêńt̂.éx̂ít̂F́ûĺl̂śĉŕêén̂() ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "ĤT́M̂ĹV̂íd̂éôÉl̂ém̂én̂t́.ŵéb̂ḱît́Ŝúp̂ṕôŕt̂śF̂úl̂ĺŝćr̂éêń îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê D́ôćûḿêńt̂.f́ûĺl̂śĉŕêén̂Én̂áb̂ĺêd́ îńŝt́êád̂."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Ŵé'r̂é d̂ép̂ŕêćât́îńĝ t́ĥé ÂṔÎ `chrome.privacy.websites.privacySandboxEnabled`, t́ĥóûǵĥ ít̂ ẃîĺl̂ ŕêḿâín̂ áĉt́îv́ê f́ôŕ b̂áĉḱŵár̂d́ ĉóm̂ṕât́îb́îĺît́ŷ ún̂t́îĺ r̂él̂éâśê Ḿ113. Îńŝt́êád̂, ṕl̂éâśê úŝé `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` âńd̂ `chrome.privacy.websites.adMeasurementEnabled`. Śêé ĥt́t̂ṕŝ://d́êv́êĺôṕêŕ.ĉh́r̂óm̂é.ĉóm̂/d́ôćŝ/éx̂t́êńŝíôńŝ/ŕêf́êŕêńĉé/p̂ŕîv́âćŷ/#ṕr̂óp̂ér̂t́ŷ-ẃêb́ŝít̂éŝ-ṕr̂ív̂áĉýŜán̂d́b̂óx̂Én̂áb̂ĺêd́."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "R̂án̂ǵê.éx̂ṕâńd̂() íŝ d́êṕr̂éĉát̂éd̂. Ṕl̂éâśê úŝé Ŝél̂éĉt́îón̂.ḿôd́îf́ŷ() ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Ŝúb̂ŕêśôúr̂ćê ŕêq́ûéŝt́ŝ ẃĥóŝé ÛŔL̂ś ĉón̂t́âín̂ ém̂b́êd́d̂éd̂ ćr̂éd̂én̂t́îál̂ś (ê.ǵ. `**********************/`) âŕê b́l̂óĉḱêd́."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "T̂h́ê ćôńŝt́r̂áîńt̂ `DtlsSrtpKeyAgreement` íŝ ŕêḿôv́êd́. Ŷóû h́âv́ê śp̂éĉíf̂íêd́ â `false` v́âĺûé f̂ór̂ t́ĥíŝ ćôńŝt́r̂áîńt̂, ẃĥíĉh́ îś îńt̂ér̂ṕr̂ét̂éd̂ áŝ án̂ át̂t́êḿp̂t́ t̂ó ûśê t́ĥé r̂ém̂óv̂éd̂ `SDES key negotiation` ḿêt́ĥód̂. T́ĥíŝ f́ûńĉt́îón̂ál̂ít̂ý îś r̂ém̂óv̂éd̂; úŝé â śêŕv̂íĉé t̂h́ât́ ŝúp̂ṕôŕt̂ś `DTLS key negotiation` îńŝt́êád̂."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "T̂h́ê ćôńŝt́r̂áîńt̂ `DtlsSrtpKeyAgreement` íŝ ŕêḿôv́êd́. Ŷóû h́âv́ê śp̂éĉíf̂íêd́ â `true` v́âĺûé f̂ór̂ t́ĥíŝ ćôńŝt́r̂áîńt̂, ẃĥíĉh́ ĥád̂ ńô éf̂f́êćt̂, b́ût́ ŷóû ćâń r̂ém̂óv̂é t̂h́îś ĉón̂śt̂ŕâín̂t́ f̂ór̂ t́îd́îńêśŝ."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "T̂h́ê ćâĺl̂b́âćk̂-b́âśêd́ ĝét̂Śt̂át̂ś() îś d̂ép̂ŕêćât́êd́ âńd̂ ẃîĺl̂ b́ê ŕêḿôv́êd́. Ûśê t́ĥé ŝṕêć-ĉóm̂ṕl̂íâńt̂ ǵêt́Ŝt́ât́ŝ() ín̂śt̂éâd́."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "T̂h́ê `rtcpMuxPolicy` óp̂t́îón̂ íŝ d́êṕr̂éĉát̂éd̂ án̂d́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` ŵíl̂ĺ r̂éq̂úîŕê ćr̂óŝś-ôŕîǵîń îśôĺât́îón̂. Śêé ĥt́t̂ṕŝ://d́êv́êĺôṕêŕ.ĉh́r̂óm̂é.ĉóm̂/b́l̂óĝ/én̂áb̂ĺîńĝ-śĥár̂éd̂-ár̂ŕâý-b̂úf̂f́êŕ/ f̂ór̂ ḿôŕê d́êt́âíl̂ś."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` ŵít̂h́ôút̂ úŝér̂ áĉt́îv́ât́îón̂ íŝ d́êṕr̂éĉát̂éd̂ án̂d́ ŵíl̂ĺ b̂é r̂ém̂óv̂éd̂."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Êx́t̂én̂śîón̂ś ŝh́ôúl̂d́ ôṕt̂ ín̂t́ô ćr̂óŝś-ôŕîǵîń îśôĺât́îón̂ t́ô ćôńt̂ín̂úê úŝín̂ǵ `SharedArrayBuffer`. Ŝéê h́t̂t́p̂ś://d̂év̂él̂óp̂ér̂.ćĥŕôḿê.ćôḿ/d̂óĉś/êx́t̂én̂śîón̂ś/m̂v́3/ĉŕôśŝ-ór̂íĝín̂-íŝól̂át̂íôń/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Ŵéb̂ ŚQ̂Ĺ îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê ŚQ̂Ĺît́ê Ẃêb́Âśŝém̂b́l̂ý ôŕ Îńd̂éx̂éd̂ D́ât́âb́âśê"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "T̂h́ê ṕêŕm̂íŝśîón̂ d́êśĉŕîṕt̂ór̂ `window-placement` íŝ d́êṕr̂éĉát̂éd̂. Úŝé `window-management` îńŝt́êád̂. F́ôŕ m̂ór̂é ĥél̂ṕ, ĉh́êćk̂ h́t̂t́p̂ś://b̂ít̂.ĺŷ/ẃîńd̂óŵ-ṕl̂áĉém̂én̂t́-r̂én̂ám̂é."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "T̂h́ê ṕêŕm̂íŝśîón̂ ṕôĺîćŷ `window-placement` íŝ d́êṕr̂éĉát̂éd̂. Úŝé `window-management` îńŝt́êád̂. F́ôŕ m̂ór̂é ĥél̂ṕ, ĉh́êćk̂ h́t̂t́p̂ś://b̂ít̂.ĺŷ/ẃîńd̂óŵ-ṕl̂áĉém̂én̂t́-r̂én̂ám̂é."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "ÛT́F̂-16 íŝ ńôt́ ŝúp̂ṕôŕt̂éd̂ b́ŷ ŕêśp̂ón̂śê j́ŝón̂ ín̂ `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Ŝýn̂ćĥŕôńôúŝ `XMLHttpRequest` ón̂ t́ĥé m̂áîń t̂h́r̂éâd́ îś d̂ép̂ŕêćât́êd́ b̂éĉáûśê óf̂ ít̂ś d̂ét̂ŕîḿêńt̂ál̂ éf̂f́êćt̂ś t̂ó t̂h́ê én̂d́ ûśêŕ'ŝ éx̂ṕêŕîén̂ćê. F́ôŕ m̂ór̂é ĥél̂ṕ, ĉh́êćk̂ h́t̂t́p̂ś://x̂h́r̂.śp̂éĉ.ẃĥát̂ẃĝ.ór̂ǵ/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "`supportsSession()` îś d̂ép̂ŕêćât́êd́. P̂ĺêáŝé ûśê `isSessionSupported()` án̂d́ ĉh́êćk̂ t́ĥé r̂éŝól̂v́êd́ b̂óôĺêán̂ v́âĺûé îńŝt́êád̂."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "M̂áîń-T̂h́r̂éâd́ B̂ĺôćk̂ín̂ǵ T̂ím̂é"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Ĉáĉh́ê T́T̂Ĺ"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "D̂éŝćr̂íp̂t́îón̂"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "D̂úr̂át̂íôń"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Êĺêḿêńt̂"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "F̂áîĺîńĝ Él̂ém̂én̂t́ŝ"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "L̂óĉát̂íôń"}, "core/lib/i18n/i18n.js | columnName": {"message": "N̂ám̂é"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Ôv́êŕ B̂úd̂ǵêt́"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "R̂éq̂úêśt̂ś"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "R̂éŝóûŕĉé Ŝíẑé"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "R̂éŝóûŕĉé T̂ýp̂é"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Ŝíẑé"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Ŝóûŕĉé"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Ŝt́âŕt̂ T́îḿê"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "T̂ím̂é Ŝṕêńt̂"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "T̂ŕâńŝf́êŕ Ŝíẑé"}, "core/lib/i18n/i18n.js | columnURL": {"message": "ÛŔL̂"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "P̂ót̂én̂t́îál̂ Śâv́îńĝś"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "P̂ót̂én̂t́îál̂ Śâv́îńĝś"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Ĉúm̂úl̂át̂ív̂é L̂áŷóût́ Ŝh́îf́t̂"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "P̂ót̂én̂t́îál̂ śâv́îńĝś ôf́ {wastedBytes, number, bytes} K̂íB̂"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount, plural, =1 {1 êĺêḿêńt̂ f́ôún̂d́} other {# êĺêḿêńt̂ś f̂óûńd̂}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "P̂ót̂én̂t́îál̂ śâv́îńĝś ôf́ {wastedMs, number, milliseconds} m̂ś"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "D̂óĉúm̂én̂t́"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "F̂ír̂śt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "F̂ír̂śt̂ Ḿêán̂ín̂ǵf̂úl̂ Ṕâín̂t́"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "F̂ón̂t́"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Îḿâǵê"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Îńt̂ér̂áĉt́îón̂ t́ô Ńêx́t̂ Ṕâín̂t́"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Ĥíĝh́"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "L̂óŵ"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "M̂éd̂íûḿ"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "L̂ár̂ǵêśt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "M̂áx̂ Ṕôt́êńt̂íâĺ F̂ír̂śt̂ Ín̂ṕût́ D̂él̂áŷ"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "M̂éd̂íâ"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} m̂ś"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Ôt́ĥér̂ ŕêśôúr̂ćêś"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Ôt́ĥér̂"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Ŝćr̂íp̂t́"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} ŝ"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Ŝṕêéd̂ Ín̂d́êx́"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Ŝt́ŷĺêśĥéêt́"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "T̂h́îŕd̂-ṕâŕt̂ý"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "T̂ót̂ál̂ B́l̂óĉḱîńĝ T́îḿê"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "T̂ót̂ál̂"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Ŝóm̂ét̂h́îńĝ ẃêńt̂ ẃr̂ón̂ǵ ŵít̂h́ r̂éĉór̂d́îńĝ t́ĥé t̂ŕâćê óv̂ér̂ ýôúr̂ ṕâǵê ĺôád̂. Ṕl̂éâśê ŕûń L̂íĝh́t̂h́ôúŝé âǵâín̂. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "T̂ím̂éôút̂ ẃâít̂ín̂ǵ f̂ór̂ ín̂ít̂íâĺ D̂éb̂úĝǵêŕ P̂ŕôt́ôćôĺ ĉón̂ńêćt̂íôń."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Ĉh́r̂óm̂é d̂íd̂ń't̂ ćôĺl̂éĉt́ âńŷ śĉŕêén̂śĥót̂ś d̂úr̂ín̂ǵ t̂h́ê ṕâǵê ĺôád̂. Ṕl̂éâśê ḿâḱê śûŕê t́ĥér̂é îś ĉón̂t́êńt̂ v́îśîb́l̂é ôń t̂h́ê ṕâǵê, án̂d́ t̂h́êń t̂ŕŷ ŕê-ŕûńn̂ín̂ǵ L̂íĝh́t̂h́ôúŝé. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "D̂ŃŜ śêŕv̂ér̂ś ĉóûĺd̂ ńôt́ r̂éŝól̂v́ê t́ĥé p̂ŕôv́îd́êd́ d̂óm̂áîń."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "R̂éq̂úîŕêd́ {artifactName} ĝát̂h́êŕêŕ êńĉóûńt̂ér̂éd̂ án̂ ér̂ŕôŕ: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Âń îńt̂ér̂ńâĺ Ĉh́r̂óm̂é êŕr̂ór̂ óĉćûŕr̂éd̂. Ṕl̂éâśê ŕêśt̂ár̂t́ Ĉh́r̂óm̂é âńd̂ t́r̂ý r̂é-r̂ún̂ńîńĝ Ĺîǵĥt́ĥóûśê."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "R̂éq̂úîŕêd́ {artifactName} ĝát̂h́êŕêŕ d̂íd̂ ńôt́ r̂ún̂."}, "core/lib/lh-error.js | noFcp": {"message": "T̂h́ê ṕâǵê d́îd́ n̂ót̂ ṕâín̂t́ âńŷ ćôńt̂én̂t́. P̂ĺêáŝé êńŝúr̂é ŷóû ḱêép̂ t́ĥé b̂ŕôẃŝér̂ ẃîńd̂óŵ ín̂ t́ĥé f̂ór̂éĝŕôún̂d́ d̂úr̂ín̂ǵ t̂h́ê ĺôád̂ án̂d́ t̂ŕŷ áĝáîń. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "T̂h́ê ṕâǵê d́îd́ n̂ót̂ d́îśp̂ĺâý ĉón̂t́êńt̂ t́ĥát̂ q́ûál̂íf̂íêś âś â Ĺâŕĝéŝt́ Ĉón̂t́êńt̂f́ûĺ P̂áîńt̂ (ĹĈṔ). Êńŝúr̂é t̂h́ê ṕâǵê h́âś â v́âĺîd́ L̂ĆP̂ él̂ém̂én̂t́ âńd̂ t́ĥén̂ t́r̂ý âǵâín̂. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "T̂h́ê ṕâǵê ṕr̂óv̂íd̂éd̂ íŝ ńôt́ ĤT́M̂Ĺ (ŝér̂v́êd́ âś M̂ÍM̂É t̂ýp̂é {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "T̂h́îś v̂ér̂śîón̂ óf̂ Ćĥŕôḿê íŝ t́ôó ôĺd̂ t́ô śûṕp̂ór̂t́ '{featureName}'. Ûśê á n̂éŵér̂ v́êŕŝíôń t̂ó ŝéê f́ûĺl̂ ŕêśûĺt̂ś."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé ÛŔL̂ ýôú r̂éq̂úêśt̂éd̂ b́êćâúŝé t̂h́ê ṕâǵê śt̂óp̂ṕêd́ r̂éŝṕôńd̂ín̂ǵ."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "T̂h́ê ÚR̂Ĺ ŷóû h́âv́ê ṕr̂óv̂íd̂éd̂ d́ôéŝ ńôt́ ĥáv̂é â v́âĺîd́ ŝéĉúr̂ít̂ý ĉér̂t́îf́îćât́ê. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Ĉh́r̂óm̂é p̂ŕêv́êńt̂éd̂ ṕâǵê ĺôád̂ ẃît́ĥ án̂ ín̂t́êŕŝt́ît́îál̂. Ḿâḱê śûŕê ýôú âŕê t́êśt̂ín̂ǵ t̂h́ê ćôŕr̂éĉt́ ÛŔL̂ án̂d́ t̂h́ât́ t̂h́ê śêŕv̂ér̂ íŝ ṕr̂óp̂ér̂ĺŷ ŕêśp̂ón̂d́îńĝ t́ô ál̂ĺ r̂éq̂úêśt̂ś."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ. (D́êt́âíl̂ś: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ. (Śt̂át̂úŝ ćôd́ê: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Ŷóûŕ p̂áĝé t̂óôḱ t̂óô ĺôńĝ t́ô ĺôád̂. Ṕl̂éâśê f́ôĺl̂óŵ t́ĥé ôṕp̂ór̂t́ûńît́îéŝ ín̂ t́ĥé r̂ép̂ór̂t́ t̂ó r̂éd̂úĉé ŷóûŕ p̂áĝé l̂óâd́ t̂ím̂é, âńd̂ t́ĥén̂ t́r̂ý r̂é-r̂ún̂ńîńĝ Ĺîǵĥt́ĥóûśê. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Ŵáît́îńĝ f́ôŕ D̂év̂T́ôól̂ś p̂ŕôt́ôćôĺ r̂éŝṕôńŝé ĥáŝ éx̂ćêéd̂éd̂ t́ĥé âĺl̂ót̂t́êd́ t̂ím̂é. (M̂ét̂h́ôd́: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "F̂ét̂ćĥín̂ǵ r̂éŝóûŕĉé ĉón̂t́êńt̂ h́âś êx́ĉéêd́êd́ t̂h́ê ál̂ĺôt́t̂éd̂ t́îḿê"}, "core/lib/lh-error.js | urlInvalid": {"message": "T̂h́ê ÚR̂Ĺ ŷóû h́âv́ê ṕr̂óv̂íd̂éd̂ áp̂ṕêár̂ś t̂ó b̂é îńv̂ál̂íd̂."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ. (Śt̂át̂úŝ ćôd́ê: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "T̂h́ê ṕâǵê ḾÎḾÊ t́ŷṕê íŝ X́ĤT́M̂Ĺ: L̂íĝh́t̂h́ôúŝé d̂óêś n̂ót̂ éx̂ṕl̂íĉít̂ĺŷ śûṕp̂ór̂t́ t̂h́îś d̂óĉúm̂én̂t́ t̂ýp̂é"}, "core/user-flow.js | defaultFlowName": {"message": "Ûśêŕ f̂ĺôẃ ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "N̂áv̂íĝát̂íôń r̂ép̂ór̂t́ ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Ŝńâṕŝh́ôt́ r̂ép̂ór̂t́ ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "T̂ím̂éŝṕâń r̂ép̂ór̂t́ ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Âĺl̂ Ŕêṕôŕt̂ś"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Ĉát̂éĝór̂íêś"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Âćĉéŝśîb́îĺît́ŷ"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "B̂éŝt́ P̂ŕâćt̂íĉéŝ"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "P̂ér̂f́ôŕm̂án̂ćê"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "ŜÉÔ"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "D̂éŝḱt̂óp̂"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Ûńd̂ér̂śt̂án̂d́îńĝ t́ĥé L̂íĝh́t̂h́ôúŝé F̂ĺôẃ R̂ép̂ór̂t́"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Ûńd̂ér̂śt̂án̂d́îńĝ F́l̂óŵś"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Ûśê Ńâv́îǵât́îón̂ ŕêṕôŕt̂ś t̂ó..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Ûśê Śn̂áp̂śĥót̂ ŕêṕôŕt̂ś t̂ó..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Ûśê T́îḿêśp̂án̂ ŕêṕôŕt̂ś t̂ó..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Ôb́t̂áîń â Ĺîǵĥt́ĥóûśê Ṕêŕf̂ór̂ḿâńĉé ŝćôŕê."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "M̂éâśûŕê ṕâǵê ĺôád̂ Ṕêŕf̂ór̂ḿâńĉé m̂ét̂ŕîćŝ śûćĥ áŝ Ĺâŕĝéŝt́ Ĉón̂t́êńt̂f́ûĺ P̂áîńt̂ án̂d́ Ŝṕêéd̂ Ín̂d́êx́."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Âśŝéŝś P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂ ćâṕâb́îĺît́îéŝ."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "F̂ín̂d́ âćĉéŝśîb́îĺît́ŷ íŝśûéŝ ín̂ śîńĝĺê ṕâǵê áp̂ṕl̂íĉát̂íôńŝ ór̂ ćôḿp̂ĺêx́ f̂ór̂ḿŝ."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Êv́âĺûát̂é b̂éŝt́ p̂ŕâćt̂íĉéŝ óf̂ ḿêńûś âńd̂ ÚÎ él̂ém̂én̂t́ŝ h́îd́d̂én̂ b́êh́îńd̂ ín̂t́êŕâćt̂íôń."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "M̂éâśûŕê ĺâýôút̂ śĥíf̂t́ŝ án̂d́ Ĵáv̂áŜćr̂íp̂t́ êx́êćût́îón̂ t́îḿê ón̂ á ŝér̂íêś ôf́ îńt̂ér̂áĉt́îón̂ś."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "D̂íŝćôv́êŕ p̂ér̂f́ôŕm̂án̂ćê óp̂ṕôŕt̂ún̂ít̂íêś t̂ó îḿp̂ŕôv́ê t́ĥé êx́p̂ér̂íêńĉé f̂ór̂ ĺôńĝ-ĺîv́êd́ p̂áĝéŝ án̂d́ ŝín̂ǵl̂é-p̂áĝé âṕp̂ĺîćât́îón̂ś."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Ĥíĝh́êśt̂ ím̂ṕâćt̂"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative, plural,\n    =1 {{numInformative} îńf̂ór̂ḿât́îv́ê áûd́ît́}\n    other {{numInformative} îńf̂ór̂ḿât́îv́ê áûd́ît́ŝ}\n  }"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "M̂ób̂íl̂é"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "P̂áĝé l̂óâd́"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "N̂áv̂íĝát̂íôń r̂ép̂ór̂t́ŝ án̂ál̂ýẑé â śîńĝĺê ṕâǵê ĺôád̂, éx̂áĉt́l̂ý l̂ík̂é t̂h́ê ór̂íĝín̂ál̂ Ĺîǵĥt́ĥóûśê ŕêṕôŕt̂ś."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "N̂áv̂íĝát̂íôń r̂ép̂ór̂t́"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation, plural,\n    =1 {{numNavigation} n̂áv̂íĝát̂íôń r̂ép̂ór̂t́}\n    other {{numNavigation} n̂áv̂íĝát̂íôń r̂ép̂ór̂t́ŝ}\n  }"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits, plural,\n    =1 {{numPassableAudits} p̂áŝśâb́l̂é âúd̂ít̂}\n    other {{numPassableAudits} ṕâśŝáb̂ĺê áûd́ît́ŝ}\n  }"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed, plural,\n    =1 {{numPassed} âúd̂ít̂ ṕâśŝéd̂}\n    other {{numPassed} áûd́ît́ŝ ṕâśŝéd̂}\n  }"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Âv́êŕâǵê"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Êŕr̂ór̂"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "P̂óôŕ"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Ĝóôd́"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Ŝáv̂é"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Ĉáp̂t́ûŕêd́ ŝt́ât́ê óf̂ ṕâǵê"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Ŝńâṕŝh́ôt́ r̂ép̂ór̂t́ŝ án̂ál̂ýẑé t̂h́ê ṕâǵê ín̂ á p̂ár̂t́îćûĺâŕ ŝt́ât́ê, t́ŷṕîćâĺl̂ý âf́t̂ér̂ úŝér̂ ín̂t́êŕâćt̂íôńŝ."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Ŝńâṕŝh́ôt́ r̂ép̂ór̂t́"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot, plural,\n    =1 {{numSnapshot} ŝńâṕŝh́ôt́ r̂ép̂ór̂t́}\n    other {{numSnapshot} ŝńâṕŝh́ôt́ r̂ép̂ór̂t́ŝ}\n  }"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Ŝúm̂ḿâŕŷ"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Ûśêŕ îńt̂ér̂áĉt́îón̂ś"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "T̂ím̂éŝṕâń r̂ép̂ór̂t́ŝ án̂ál̂ýẑé âń âŕb̂ít̂ŕâŕŷ ṕêŕîód̂ óf̂ t́îḿê, t́ŷṕîćâĺl̂ý ĉón̂t́âín̂ín̂ǵ ûśêŕ îńt̂ér̂áĉt́îón̂ś."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "T̂ím̂éŝṕâń r̂ép̂ór̂t́"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan, plural,\n    =1 {{numTimespan} t̂ím̂éŝṕâń r̂ép̂ór̂t́}\n    other {{numTimespan} t̂ím̂éŝṕâń r̂ép̂ór̂t́ŝ}\n  }"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "L̂íĝh́t̂h́ôúŝé Ûśêŕ F̂ĺôẃ R̂ép̂ór̂t́"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "F̂ór̂ án̂ím̂át̂éd̂ ćôńt̂én̂t́, ûśê [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) t́ô ḿîńîḿîźê ĆP̂Ú ûśâǵê ẃĥén̂ t́ĥé ĉón̂t́êńt̂ íŝ óf̂f́ŝćr̂éêń."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ d̂íŝṕl̂áŷín̂ǵ âĺl̂ [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ćôḿp̂ón̂én̂t́ŝ ín̂ Ẃêb́P̂ f́ôŕm̂át̂ś ŵh́îĺê śp̂éĉíf̂ýîńĝ án̂ áp̂ṕr̂óp̂ŕîát̂é f̂ál̂ĺb̂áĉḱ f̂ór̂ ót̂h́êŕ b̂ŕôẃŝér̂ś. [L̂éâŕn̂ ḿôŕê](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Êńŝúr̂é t̂h́ât́ ŷóû ár̂é ûśîńĝ [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) f́ôŕ îḿâǵêś t̂ó âút̂óm̂át̂íĉál̂ĺŷ ĺâźŷ-ĺôád̂. [Ĺêár̂ń m̂ór̂é](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Ûśê t́ôól̂ś ŝúĉh́ âś [ÂḾP̂ Óp̂t́îḿîźêŕ](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) t̂ó [ŝér̂v́êŕ-ŝíd̂é r̂én̂d́êŕ ÂḾP̂ ĺâýôút̂ś](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "R̂éf̂ér̂ t́ô t́ĥé [ÂḾP̂ d́ôćûḿêńt̂át̂íôń](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) t̂ó êńŝúr̂é âĺl̂ śt̂ýl̂éŝ ár̂é ŝúp̂ṕôŕt̂éd̂."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "T̂h́ê [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ćôḿp̂ón̂én̂t́ ŝúp̂ṕôŕt̂ś t̂h́ê [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) át̂t́r̂íb̂út̂é t̂ó ŝṕêćîf́ŷ ẃĥíĉh́ îḿâǵê áŝśêt́ŝ t́ô úŝé b̂áŝéd̂ ón̂ t́ĥé ŝćr̂éêń ŝíẑé. [L̂éâŕn̂ ḿôŕê](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Ĉón̂śîd́êŕ v̂ír̂t́ûál̂ śĉŕôĺl̂ín̂ǵ ŵít̂h́ t̂h́ê Ćôḿp̂ón̂én̂t́ D̂év̂ Ḱît́ (ĈD́K̂) íf̂ v́êŕŷ ĺâŕĝé l̂íŝt́ŝ ár̂é b̂éîńĝ ŕêńd̂ér̂éd̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Âṕp̂ĺŷ [ŕôút̂é-l̂év̂él̂ ćôd́ê śp̂ĺît́t̂ín̂ǵ](https://web.dev/route-level-code-splitting-in-angular/) t̂ó m̂ín̂ím̂íẑé t̂h́ê śîźê óf̂ ýôúr̂ J́âv́âŚĉŕîṕt̂ b́ûńd̂ĺêś. Âĺŝó, ĉón̂śîd́êŕ p̂ŕêćâćĥín̂ǵ âśŝét̂ś ŵít̂h́ t̂h́ê [Án̂ǵûĺâŕ ŝér̂v́îćê ẃôŕk̂ér̂](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Án̂ǵûĺâŕ ĈĹÎ, én̂śûŕê t́ĥát̂ b́ûíl̂d́ŝ ár̂é ĝén̂ér̂át̂éd̂ ín̂ ṕr̂ód̂úĉt́îón̂ ḿôd́ê. [Ĺêár̂ń m̂ór̂é](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Án̂ǵûĺâŕ ĈĹÎ, ín̂ćl̂úd̂é ŝóûŕĉé m̂áp̂ś îń ŷóûŕ p̂ŕôd́ûćt̂íôń b̂úîĺd̂ t́ô ín̂śp̂éĉt́ ŷóûŕ b̂ún̂d́l̂éŝ. [Ĺêár̂ń m̂ór̂é](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "P̂ŕêĺôád̂ ŕôút̂éŝ áĥéâd́ ôf́ t̂ím̂é t̂ó ŝṕêéd̂ úp̂ ńâv́îǵât́îón̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ t́ĥé `BreakpointObserver` ût́îĺît́ŷ ín̂ t́ĥé Ĉóm̂ṕôńêńt̂ D́êv́ K̂ít̂ (ĆD̂Ḱ) t̂ó m̂án̂áĝé îḿâǵê b́r̂éâḱp̂óîńt̂ś. [L̂éâŕn̂ ḿôŕê](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Ĉón̂śîd́êŕ ûṕl̂óâd́îńĝ ýôúr̂ ǴÎF́ t̂ó â śêŕv̂íĉé ŵh́îćĥ ẃîĺl̂ ḿâḱê ít̂ áv̂áîĺâb́l̂é t̂ó êḿb̂éd̂ áŝ án̂ H́T̂ḾL̂5 v́îd́êó."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Ŝṕêćîf́ŷ `@font-display` ẃĥén̂ d́êf́îńîńĝ ćûśt̂óm̂ f́ôńt̂ś îń ŷóûŕ t̂h́êḿê."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ ĉón̂f́îǵûŕîńĝ [Ẃêb́P̂ ím̂áĝé f̂ór̂ḿât́ŝ ẃît́ĥ á Ĉón̂v́êŕt̂ ím̂áĝé ŝt́ŷĺê](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) ón̂ ýôúr̂ śît́ê."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Îńŝt́âĺl̂ [á D̂ŕûṕâĺ m̂ód̂úl̂é](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) t̂h́ât́ ĉán̂ ĺâźŷ ĺôád̂ ím̂áĝéŝ. Śûćĥ ḿôd́ûĺêś p̂ŕôv́îd́ê t́ĥé âb́îĺît́ŷ t́ô d́êf́êŕ âńŷ óf̂f́ŝćr̂éêń îḿâǵêś t̂ó îḿp̂ŕôv́ê ṕêŕf̂ór̂ḿâńĉé."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á m̂ód̂úl̂é t̂ó îńl̂ín̂é ĉŕît́îćâĺ ĈŚŜ án̂d́ Ĵáv̂áŜćr̂íp̂t́, âńd̂ úŝé t̂h́ê d́êf́êŕ ât́t̂ŕîb́ût́ê f́ôŕ n̂ón̂-ćr̂ít̂íĉál̂ ĆŜŚ ôŕ Ĵáv̂áŜćr̂íp̂t́."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "T̂h́êḿêś, m̂ód̂úl̂éŝ, án̂d́ ŝér̂v́êŕ ŝṕêćîf́îćât́îón̂ś âĺl̂ ćôńt̂ŕîb́ût́ê t́ô śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê. Ćôńŝíd̂ér̂ f́îńd̂ín̂ǵ â ḿôŕê óp̂t́îḿîźêd́ t̂h́êḿê, ćâŕêf́ûĺl̂ý ŝél̂éĉt́îńĝ án̂ óp̂t́îḿîźât́îón̂ ḿôd́ûĺê, án̂d́/ôŕ ûṕĝŕâd́îńĝ ýôúr̂ śêŕv̂ér̂. Ýôúr̂ h́ôśt̂ín̂ǵ ŝér̂v́êŕŝ śĥóûĺd̂ ḿâḱê úŝé ôf́ P̂H́P̂ óp̂ćôd́ê ćâćĥín̂ǵ, m̂ém̂ór̂ý-ĉáĉh́îńĝ t́ô ŕêd́ûćê d́ât́âb́âśê q́ûér̂ý t̂ím̂éŝ śûćĥ áŝ Ŕêd́îś ôŕ M̂ém̂ćâćĥéd̂, áŝ ẃêĺl̂ áŝ óp̂t́îḿîźêd́ âṕp̂ĺîćât́îón̂ ĺôǵîć t̂ó p̂ŕêṕâŕê ṕâǵêś f̂áŝt́êŕ."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Ĉón̂śîd́êŕ ûśîńĝ [Ŕêśp̂ón̂śîv́ê Ím̂áĝé Ŝt́ŷĺêś](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) t̂ó r̂éd̂úĉé t̂h́ê śîźê óf̂ ím̂áĝéŝ ĺôád̂éd̂ ón̂ ýôúr̂ ṕâǵê. Íf̂ ýôú âŕê úŝín̂ǵ V̂íêẃŝ t́ô śĥóŵ ḿûĺt̂íp̂ĺê ćôńt̂én̂t́ ît́êḿŝ ón̂ á p̂áĝé, ĉón̂śîd́êŕ îḿp̂ĺêḿêńt̂ín̂ǵ p̂áĝín̂át̂íôń t̂ó l̂ím̂ít̂ t́ĥé n̂úm̂b́êŕ ôf́ ĉón̂t́êńt̂ ít̂ém̂ś ŝh́ôẃn̂ ón̂ á ĝív̂én̂ ṕâǵê."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Êńŝúr̂é ŷóû h́âv́ê én̂áb̂ĺêd́ \"Âǵĝŕêǵât́ê ĆŜŚ f̂íl̂éŝ\" ín̂ t́ĥé \"Âd́m̂ín̂íŝt́r̂át̂íôń » Ĉón̂f́îǵûŕât́îón̂ » D́êv́êĺôṕm̂én̂t́\" p̂áĝé.  Êńŝúr̂é ŷóûŕ D̂ŕûṕâĺ ŝít̂é îś r̂ún̂ńîńĝ át̂ ĺêáŝt́ D̂ŕûṕâĺ 10.1 f̂ór̂ ím̂ṕr̂óv̂éd̂ áŝśêt́ âǵĝŕêǵât́îón̂ śûṕp̂ór̂t́."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Êńŝúr̂é ŷóû h́âv́ê én̂áb̂ĺêd́ \"Âǵĝŕêǵât́ê J́âv́âŚĉŕîṕt̂ f́îĺêś\" îń t̂h́ê \"Ád̂ḿîńîśt̂ŕât́îón̂ » Ćôńf̂íĝúr̂át̂íôń » D̂év̂él̂óp̂ḿêńt̂\" ṕâǵê.  Én̂śûŕê ýôúr̂ D́r̂úp̂ál̂ śît́ê íŝ ŕûńn̂ín̂ǵ ât́ l̂éâśt̂ D́r̂úp̂ál̂ 10.1 f́ôŕ îḿp̂ŕôv́êd́ âśŝét̂ áĝǵr̂éĝát̂íôń ŝúp̂ṕôŕt̂."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Ĉón̂śîd́êŕ r̂ém̂óv̂ín̂ǵ ûńûśêd́ ĈŚŜ ŕûĺêś âńd̂ ón̂ĺŷ át̂t́âćĥ t́ĥé n̂éêd́êd́ D̂ŕûṕâĺ l̂íb̂ŕâŕîéŝ t́ô t́ĥé r̂él̂év̂án̂t́ p̂áĝé ôŕ ĉóm̂ṕôńêńt̂ ín̂ á p̂áĝé. Ŝéê t́ĥé [D̂ŕûṕâĺ d̂óĉúm̂én̂t́ât́îón̂ ĺîńk̂](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) f́ôŕ d̂ét̂áîĺŝ. T́ô íd̂én̂t́îf́ŷ át̂t́âćĥéd̂ ĺîb́r̂ár̂íêś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ ĆŜŚ, t̂ŕŷ ŕûńn̂ín̂ǵ [ĉód̂é ĉóv̂ér̂áĝé](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) îń Ĉh́r̂óm̂é D̂év̂T́ôól̂ś. Ŷóû ćâń îd́êńt̂íf̂ý t̂h́ê t́ĥém̂é/m̂ód̂úl̂é r̂éŝṕôńŝíb̂ĺê f́r̂óm̂ t́ĥé ÛŔL̂ óf̂ t́ĥé ŝt́ŷĺêśĥéêt́ ŵh́êń ĈŚŜ áĝǵr̂éĝát̂íôń îś d̂íŝáb̂ĺêd́ îń ŷóûŕ D̂ŕûṕâĺ ŝít̂é. L̂óôḱ ôút̂ f́ôŕ t̂h́êḿêś/m̂ód̂úl̂éŝ t́ĥát̂ h́âv́ê ḿâńŷ śt̂ýl̂éŝh́êét̂ś îń t̂h́ê ĺîśt̂ ẃĥíĉh́ ĥáv̂é â ĺôt́ ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Á t̂h́êḿê/ḿôd́ûĺê śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śt̂ýl̂éŝh́êét̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Ĉón̂śîd́êŕ r̂ém̂óv̂ín̂ǵ ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́ âśŝét̂ś âńd̂ ón̂ĺŷ át̂t́âćĥ t́ĥé n̂éêd́êd́ D̂ŕûṕâĺ l̂íb̂ŕâŕîéŝ t́ô t́ĥé r̂él̂év̂án̂t́ p̂áĝé ôŕ ĉóm̂ṕôńêńt̂ ín̂ á p̂áĝé. Ŝéê t́ĥé [D̂ŕûṕâĺ d̂óĉúm̂én̂t́ât́îón̂ ĺîńk̂](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) f́ôŕ d̂ét̂áîĺŝ. T́ô íd̂én̂t́îf́ŷ át̂t́âćĥéd̂ ĺîb́r̂ár̂íêś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ J́âv́âŚĉŕîṕt̂, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ḿôd́ûĺê ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śĉŕîṕt̂ ẃĥén̂ J́âv́âŚĉŕîṕt̂ áĝǵr̂éĝát̂íôń îś d̂íŝáb̂ĺêd́ îń ŷóûŕ D̂ŕûṕâĺ ŝít̂é. L̂óôḱ ôút̂ f́ôŕ t̂h́êḿêś/m̂ód̂úl̂éŝ t́ĥát̂ h́âv́ê ḿâńŷ śĉŕîṕt̂ś îń t̂h́ê ĺîśt̂ ẃĥíĉh́ ĥáv̂é â ĺôt́ ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Á t̂h́êḿê/ḿôd́ûĺê śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śĉŕîṕt̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Ŝét̂ t́ĥé \"B̂ŕôẃŝér̂ án̂d́ p̂ŕôx́ŷ ćâćĥé m̂áx̂ím̂úm̂ áĝé\" îń t̂h́ê \"Ád̂ḿîńîśt̂ŕât́îón̂ » Ćôńf̂íĝúr̂át̂íôń » D̂év̂él̂óp̂ḿêńt̂\" ṕâǵê. Ŕêád̂ áb̂óût́ [D̂ŕûṕâĺ ĉáĉh́ê án̂d́ ôṕt̂ím̂íẑín̂ǵ f̂ór̂ ṕêŕf̂ór̂ḿâńĉé](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ [á m̂ód̂úl̂é](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) t̂h́ât́ âút̂óm̂át̂íĉál̂ĺŷ óp̂t́îḿîźêś âńd̂ ŕêd́ûćêś t̂h́ê śîźê óf̂ ím̂áĝéŝ úp̂ĺôád̂éd̂ t́ĥŕôúĝh́ t̂h́ê śît́ê ẃĥíl̂é r̂ét̂áîńîńĝ q́ûál̂ít̂ý. Âĺŝó, êńŝúr̂é ŷóû ár̂é ûśîńĝ t́ĥé n̂át̂ív̂é [R̂éŝṕôńŝív̂é Îḿâǵê Śt̂ýl̂éŝ](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) ṕr̂óv̂íd̂éd̂ f́r̂óm̂ D́r̂úp̂ál̂ (áv̂áîĺâb́l̂é îń D̂ŕûṕâĺ 8 âńd̂ áb̂óv̂é) f̂ór̂ ál̂ĺ îḿâǵêś r̂én̂d́êŕêd́ ôń t̂h́ê śît́ê."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "P̂ŕêćôńn̂éĉt́ ôŕ d̂ńŝ-ṕr̂éf̂ét̂ćĥ ŕêśôúr̂ćê h́îńt̂ś ĉán̂ b́ê ád̂d́êd́ b̂ý îńŝt́âĺl̂ín̂ǵ âńd̂ ćôńf̂íĝúr̂ín̂ǵ [â ḿôd́ûĺê](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) t́ĥát̂ ṕr̂óv̂íd̂éŝ f́âćîĺît́îéŝ f́ôŕ ûśêŕ âǵêńt̂ ŕêśôúr̂ćê h́îńt̂ś."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Êńŝúr̂é t̂h́ât́ ŷóû ár̂é ûśîńĝ t́ĥé n̂át̂ív̂é [R̂éŝṕôńŝív̂é Îḿâǵê Śt̂ýl̂éŝ](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) ṕr̂óv̂íd̂éd̂ f́r̂óm̂ D́r̂úp̂ál̂ (áv̂áîĺâb́l̂é îń D̂ŕûṕâĺ 8 âńd̂ áb̂óv̂é). Ûśê t́ĥé R̂éŝṕôńŝív̂é Îḿâǵê Śt̂ýl̂éŝ ẃĥén̂ ŕêńd̂ér̂ín̂ǵ îḿâǵê f́îél̂d́ŝ t́ĥŕôúĝh́ v̂íêẃ m̂ód̂éŝ, v́îéŵś, ôŕ îḿâǵêś ûṕl̂óâd́êd́ t̂h́r̂óûǵĥ t́ĥé ŴÝŜÍŴÝĜ éd̂ít̂ór̂."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Optimize Fonts` t́ô áût́ôḿât́îćâĺl̂ý l̂év̂ér̂áĝé t̂h́ê `font-display` ĆŜŚ f̂éât́ûŕê t́ô én̂śûŕê t́êx́t̂ íŝ úŝér̂-v́îśîb́l̂é ŵh́îĺê ẃêb́f̂ón̂t́ŝ ár̂é l̂óâd́îńĝ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Next-Gen Formats` t́ô ćôńv̂ér̂t́ îḿâǵêś t̂ó Ŵéb̂Ṕ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Lazy Load Images` t́ô d́êf́êŕ l̂óâd́îńĝ óf̂f́-ŝćr̂éêń îḿâǵêś ûńt̂íl̂ t́ĥéŷ ár̂é n̂éêd́êd́."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Critical CSS` án̂d́ `Script Delay` t̂ó d̂éf̂ér̂ ńôń-ĉŕît́îćâĺ ĴŚ/ĈŚŜ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Ûśê [Éẑóîć Ĉĺôúd̂ Ćâćĥín̂ǵ](https://pubdash.ezoic.com/speed/caching) t̂ó ĉáĉh́ê ýôúr̂ ćôńt̂én̂t́ âćr̂óŝś ôúr̂ ẃôŕl̂d́ ŵíd̂é n̂ét̂ẃôŕk̂, ím̂ṕr̂óv̂ín̂ǵ t̂ím̂é t̂ó f̂ír̂śt̂ b́ŷt́ê."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Minify CSS` t́ô áût́ôḿât́îćâĺl̂ý m̂ín̂íf̂ý ŷóûŕ ĈŚŜ t́ô ŕêd́ûćê ńêt́ŵór̂ḱ p̂áŷĺôád̂ śîźêś."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Minify Javascript` t́ô áût́ôḿât́îćâĺl̂ý m̂ín̂íf̂ý ŷóûŕ ĴŚ t̂ó r̂éd̂úĉé n̂ét̂ẃôŕk̂ ṕâýl̂óâd́ ŝíẑéŝ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Remove Unused CSS` t́ô h́êĺp̂ ẃît́ĥ t́ĥíŝ íŝśûé. Ît́ ŵíl̂ĺ îd́êńt̂íf̂ý t̂h́ê ĆŜŚ ĉĺâśŝéŝ t́ĥát̂ ár̂é âćt̂úâĺl̂ý ûśêd́ ôń êáĉh́ p̂áĝé ôf́ ŷóûŕ ŝít̂é, âńd̂ ŕêḿôv́ê án̂ý ôt́ĥér̂ś t̂ó k̂éêṕ t̂h́ê f́îĺê śîźê śm̂ál̂ĺ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Efficient Static Cache Policy` t́ô śêt́ r̂éĉóm̂ḿêńd̂éd̂ v́âĺûéŝ ín̂ t́ĥé ĉáĉh́îńĝ h́êád̂ér̂ f́ôŕ ŝt́ât́îć âśŝéŝt́ŝ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Next-Gen Formats` t́ô ćôńv̂ér̂t́ îḿâǵêś t̂ó Ŵéb̂Ṕ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Pre-Connect Origins` t́ô áût́ôḿât́îćâĺl̂ý âd́d̂ `preconnect` ŕêśôúr̂ćê h́îńt̂ś t̂ó êśt̂áb̂ĺîśĥ éâŕl̂ý ĉón̂ńêćt̂íôńŝ t́ô ím̂ṕôŕt̂án̂t́ t̂h́îŕd̂-ṕâŕt̂ý ôŕîǵîńŝ."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Preload Fonts` án̂d́ `Preload Background Images` t̂ó âd́d̂ `preload` ĺîńk̂ś t̂ó p̂ŕîór̂ít̂íẑé f̂ét̂ćĥín̂ǵ r̂éŝóûŕĉéŝ t́ĥát̂ ár̂é ĉúr̂ŕêńt̂ĺŷ ŕêq́ûéŝt́êd́ l̂át̂ér̂ ín̂ ṕâǵê ĺôád̂."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Ûśê [Éẑóîć L̂éâṕ](https://pubdash.ezoic.com/speed) âńd̂ én̂áb̂ĺê `Resize Images` t́ô ŕêśîźê ím̂áĝéŝ t́ô á d̂év̂íĉé âṕp̂ŕôṕr̂íât́ê śîźê, ŕêd́ûćîńĝ ńêt́ŵór̂ḱ p̂áŷĺôád̂ śîźêś."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Ûśê t́ĥé `gatsby-plugin-image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âút̂óm̂át̂íĉál̂ĺŷ óp̂t́îḿîźê ím̂áĝé f̂ór̂ḿât́. [L̂éâŕn̂ ḿôŕê](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Ûśê t́ĥé `gatsby-plugin-image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âút̂óm̂át̂íĉál̂ĺŷ ĺâźŷ-ĺôád̂ ím̂áĝéŝ. [Ĺêár̂ń m̂ór̂é](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Ûśê t́ĥé `gatsby-plugin-image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ t́ĥé `loading` p̂ŕôṕ t̂ó `eager`. [L̂éâŕn̂ ḿôŕê](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Ûśê t́ĥé `Gatsby Script API` t̂ó d̂éf̂ér̂ ĺôád̂ín̂ǵ ôf́ n̂ón̂-ćr̂ít̂íĉál̂ t́ĥír̂d́-p̂ár̂t́ŷ śĉŕîṕt̂ś. [L̂éâŕn̂ ḿôŕê](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Ûśê t́ĥé `PurgeCSS` `Gatsby` p̂ĺûǵîń t̂ó r̂ém̂óv̂é ûńûśêd́ r̂úl̂éŝ f́r̂óm̂ śt̂ýl̂éŝh́êét̂ś. [L̂éâŕn̂ ḿôŕê](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Ûśê `Webpack Bundle Analyzer` t́ô d́êt́êćt̂ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ćôd́ê. [Ĺêár̂ń m̂ór̂é](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Ĉón̂f́îǵûŕê ćâćĥín̂ǵ f̂ór̂ ím̂ḿût́âb́l̂é âśŝét̂ś. [L̂éâŕn̂ ḿôŕê](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Ûśê t́ĥé `gatsby-plugin-image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âd́ĵúŝt́ îḿâǵê q́ûál̂ít̂ý. [L̂éâŕn̂ ḿôŕê](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Ûśê t́ĥé `gatsby-plugin-image` ĉóm̂ṕôńêńt̂ t́ô śêt́ âṕp̂ŕôṕr̂íât́ê `sizes`. [Ĺêár̂ń m̂ór̂é](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Ĉón̂śîd́êŕ ûṕl̂óâd́îńĝ ýôúr̂ ǴÎF́ t̂ó â śêŕv̂íĉé ŵh́îćĥ ẃîĺl̂ ḿâḱê ít̂ áv̂áîĺâb́l̂é t̂ó êḿb̂éd̂ áŝ án̂ H́T̂ḾL̂5 v́îd́êó."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á [p̂ĺûǵîń](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ôŕ ŝér̂v́îćê t́ĥát̂ ẃîĺl̂ áût́ôḿât́îćâĺl̂ý ĉón̂v́êŕt̂ ýôúr̂ úp̂ĺôád̂éd̂ ím̂áĝéŝ t́ô t́ĥé ôṕt̂ím̂ál̂ f́ôŕm̂át̂ś."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Îńŝt́âĺl̂ á [l̂áẑý-l̂óâd́ Ĵóôḿl̂á p̂ĺûǵîń](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) t̂h́ât́ p̂ŕôv́îd́êś t̂h́ê áb̂íl̂ít̂ý t̂ó d̂éf̂ér̂ án̂ý ôf́f̂śĉŕêén̂ ím̂áĝéŝ, ór̂ śŵít̂ćĥ t́ô á t̂ém̂ṕl̂át̂é t̂h́ât́ p̂ŕôv́îd́êś t̂h́ât́ f̂ún̂ćt̂íôńâĺît́ŷ. Śt̂ár̂t́îńĝ ẃît́ĥ J́ôóm̂ĺâ 4.0, ál̂ĺ n̂éŵ ím̂áĝéŝ ẃîĺl̂ [áût́ôḿât́îćâĺl̂ý](https://github.com/joomla/joomla-cms/pull/30748) ĝét̂ t́ĥé `loading` ât́t̂ŕîb́ût́ê f́r̂óm̂ t́ĥé ĉór̂é."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "T̂h́êŕê ár̂é â ńûḿb̂ér̂ óf̂ J́ôóm̂ĺâ ṕl̂úĝín̂ś t̂h́ât́ ĉán̂ h́êĺp̂ ýôú [îńl̂ín̂é ĉŕît́îćâĺ âśŝét̂ś](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ôŕ [d̂éf̂ér̂ ĺêśŝ ím̂ṕôŕt̂án̂t́ r̂éŝóûŕĉéŝ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). B́êẃâŕê t́ĥát̂ óp̂t́îḿîźât́îón̂ś p̂ŕôv́îd́êd́ b̂ý t̂h́êśê ṕl̂úĝín̂ś m̂áŷ b́r̂éâḱ f̂éât́ûŕêś ôf́ ŷóûŕ t̂ém̂ṕl̂át̂éŝ ór̂ ṕl̂úĝín̂ś, ŝó ŷóû ẃîĺl̂ ńêéd̂ t́ô t́êśt̂ t́ĥéŝé t̂h́ôŕôúĝh́l̂ý."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "T̂ém̂ṕl̂át̂éŝ, éx̂t́êńŝíôńŝ, án̂d́ ŝér̂v́êŕ ŝṕêćîf́îćât́îón̂ś âĺl̂ ćôńt̂ŕîb́ût́ê t́ô śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê. Ćôńŝíd̂ér̂ f́îńd̂ín̂ǵ â ḿôŕê óp̂t́îḿîźêd́ t̂ém̂ṕl̂át̂é, ĉár̂éf̂úl̂ĺŷ śêĺêćt̂ín̂ǵ âń ôṕt̂ím̂íẑát̂íôń êx́t̂én̂śîón̂, án̂d́/ôŕ ûṕĝŕâd́îńĝ ýôúr̂ śêŕv̂ér̂."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Ĉón̂śîd́êŕ ŝh́ôẃîńĝ éx̂ćêŕp̂t́ŝ ín̂ ýôúr̂ ár̂t́îćl̂é ĉát̂éĝór̂íêś (ê.ǵ. v̂íâ t́ĥé r̂éâd́ m̂ór̂é l̂ín̂ḱ), r̂éd̂úĉín̂ǵ t̂h́ê ńûḿb̂ér̂ óf̂ ár̂t́îćl̂éŝ śĥóŵń ôń â ǵîv́êń p̂áĝé, b̂ŕêák̂ín̂ǵ ŷóûŕ l̂ón̂ǵ p̂óŝt́ŝ ín̂t́ô ḿûĺt̂íp̂ĺê ṕâǵêś, ôŕ ûśîńĝ á p̂ĺûǵîń t̂ó l̂áẑý-l̂óâd́ ĉóm̂ḿêńt̂ś."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Â ńûḿb̂ér̂ óf̂ [J́ôóm̂ĺâ éx̂t́êńŝíôńŝ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ ćŝś ŝt́ŷĺêś. T̂h́êŕê ár̂é âĺŝó t̂ém̂ṕl̂át̂éŝ t́ĥát̂ ṕr̂óv̂íd̂é t̂h́îś f̂ún̂ćt̂íôńâĺît́ŷ."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Â ńûḿb̂ér̂ óf̂ [J́ôóm̂ĺâ éx̂t́êńŝíôńŝ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ śĉŕîṕt̂ś. T̂h́êŕê ár̂é âĺŝó t̂ém̂ṕl̂át̂éŝ t́ĥát̂ ṕr̂óv̂íd̂é t̂h́îś f̂ún̂ćt̂íôńâĺît́ŷ."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ĵóôḿl̂á êx́t̂én̂śîón̂ś](https://extensions.joomla.org/) l̂óâd́îńĝ ún̂úŝéd̂ ĆŜŚ îń ŷóûŕ p̂áĝé. T̂ó îd́êńt̂íf̂ý êx́t̂én̂śîón̂ś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ ĆŜŚ, t̂ŕŷ ŕûńn̂ín̂ǵ [ĉód̂é ĉóv̂ér̂áĝé](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) îń Ĉh́r̂óm̂é D̂év̂T́ôól̂ś. Ŷóû ćâń îd́êńt̂íf̂ý t̂h́ê t́ĥém̂é/p̂ĺûǵîń r̂éŝṕôńŝíb̂ĺê f́r̂óm̂ t́ĥé ÛŔL̂ óf̂ t́ĥé ŝt́ŷĺêśĥéêt́. L̂óôḱ ôút̂ f́ôŕ p̂ĺûǵîńŝ t́ĥát̂ h́âv́ê ḿâńŷ śt̂ýl̂éŝh́êét̂ś îń t̂h́ê ĺîśt̂ ẃĥíĉh́ ĥáv̂é â ĺôt́ ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Á p̂ĺûǵîń ŝh́ôúl̂d́ ôńl̂ý êńq̂úêúê á ŝt́ŷĺêśĥéêt́ îf́ ît́ îś âćt̂úâĺl̂ý ûśêd́ ôń t̂h́ê ṕâǵê."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ĵóôḿl̂á êx́t̂én̂śîón̂ś](https://extensions.joomla.org/) l̂óâd́îńĝ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ín̂ ýôúr̂ ṕâǵê. T́ô íd̂én̂t́îf́ŷ ṕl̂úĝín̂ś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ J́Ŝ, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé êx́t̂én̂śîón̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śĉŕîṕt̂. Ĺôók̂ óût́ f̂ór̂ éx̂t́êńŝíôńŝ t́ĥát̂ h́âv́ê ḿâńŷ śĉŕîṕt̂ś îń t̂h́ê ĺîśt̂ ẃĥíĉh́ ĥáv̂é â ĺôt́ ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Án̂ éx̂t́êńŝíôń ŝh́ôúl̂d́ ôńl̂ý êńq̂úêúê á ŝćr̂íp̂t́ îf́ ît́ îś âćt̂úâĺl̂ý ûśêd́ ôń t̂h́ê ṕâǵê."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "R̂éâd́ âb́ôút̂ [B́r̂óŵśêŕ Ĉáĉh́îńĝ ín̂ J́ôóm̂ĺâ](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ án̂ [ím̂áĝé ôṕt̂ím̂íẑát̂íôń p̂ĺûǵîń](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) t̂h́ât́ ĉóm̂ṕr̂éŝśêś ŷóûŕ îḿâǵêś ŵh́îĺê ŕêt́âín̂ín̂ǵ q̂úâĺît́ŷ."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á [r̂éŝṕôńŝív̂é îḿâǵêś p̂ĺûǵîń](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) t̂ó ûśê ŕêśp̂ón̂śîv́ê ím̂áĝéŝ ín̂ ýôúr̂ ćôńt̂én̂t́."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Ŷóû ćâń êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂ b́ŷ én̂áb̂ĺîńĝ Ǵẑíp̂ Ṕâǵê Ćôḿp̂ŕêśŝíôń îń Ĵóôḿl̂á (Ŝýŝt́êḿ > Ĝĺôb́âĺ ĉón̂f́îǵûŕât́îón̂ > Śêŕv̂ér̂)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Îf́ ŷóû ár̂é n̂ót̂ b́ûńd̂ĺîńĝ ýôúr̂ J́âv́âŚĉŕîṕt̂ áŝśêt́ŝ, ćôńŝíd̂ér̂ úŝín̂ǵ [b̂ál̂ér̂](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "D̂íŝáb̂ĺê Ḿâǵêńt̂ó'ŝ b́ûíl̂t́-îń [Ĵáv̂áŜćr̂íp̂t́ b̂ún̂d́l̂ín̂ǵ âńd̂ ḿîńîf́îćât́îón̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), án̂d́ ĉón̂śîd́êŕ ûśîńĝ [b́âĺêŕ](https://github.com/magento/baler/) îńŝt́êád̂."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Ŝṕêćîf́ŷ `@font-display` ẃĥén̂ [d́êf́îńîńĝ ćûśt̂óm̂ f́ôńt̂ś](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ ŝéâŕĉh́îńĝ t́ĥé [M̂áĝén̂t́ô Ḿâŕk̂ét̂ṕl̂áĉé](https://marketplace.magento.com/catalogsearch/result/?q=webp) f̂ór̂ á v̂ár̂íêt́ŷ óf̂ t́ĥír̂d́-p̂ár̂t́ŷ éx̂t́êńŝíôńŝ t́ô ĺêv́êŕâǵê ńêẃêŕ îḿâǵê f́ôŕm̂át̂ś."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Ĉón̂śîd́êŕ m̂ód̂íf̂ýîńĝ ýôúr̂ ṕr̂ód̂úĉt́ âńd̂ ćât́âĺôǵ t̂ém̂ṕl̂át̂éŝ t́ô ḿâḱê úŝé ôf́ t̂h́ê ẃêb́ p̂ĺât́f̂ór̂ḿ'ŝ [ĺâźŷ ĺôád̂ín̂ǵ](https://web.dev/native-lazy-loading) f̂éât́ûŕê."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Ûśê Ḿâǵêńt̂ó'ŝ [V́âŕn̂íŝh́ îńt̂éĝŕât́îón̂](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Êńâb́l̂é t̂h́ê \"Ḿîńîf́ŷ ĆŜŚ F̂íl̂éŝ\" óp̂t́îón̂ ín̂ ýôúr̂ śt̂ór̂é'ŝ D́êv́êĺôṕêŕ ŝét̂t́îńĝś. [L̂éâŕn̂ ḿôŕê](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Ûśê [T́êŕŝér̂](https://www.npmjs.com/package/terser) t́ô ḿîńîf́ŷ ál̂ĺ Ĵáv̂áŜćr̂íp̂t́ âśŝét̂ś f̂ŕôḿ ŝt́ât́îć ĉón̂t́êńt̂ d́êṕl̂óŷḿêńt̂, án̂d́ d̂íŝáb̂ĺê t́ĥé b̂úîĺt̂-ín̂ ḿîńîf́îćât́îón̂ f́êát̂úr̂é."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "D̂íŝáb̂ĺê Ḿâǵêńt̂ó'ŝ b́ûíl̂t́-îń [Ĵáv̂áŜćr̂íp̂t́ b̂ún̂d́l̂ín̂ǵ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Ĉón̂śîd́êŕ ŝéâŕĉh́îńĝ t́ĥé [M̂áĝén̂t́ô Ḿâŕk̂ét̂ṕl̂áĉé](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) f̂ór̂ á v̂ár̂íêt́ŷ óf̂ t́ĥír̂d́ p̂ár̂t́ŷ éx̂t́êńŝíôńŝ t́ô óp̂t́îḿîźê ím̂áĝéŝ."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "P̂ŕêćôńn̂éĉt́ ôŕ d̂ńŝ-ṕr̂éf̂ét̂ćĥ ŕêśôúr̂ćê h́îńt̂ś ĉán̂ b́ê ád̂d́êd́ b̂ý [m̂ód̂íf̂ýîńĝ á t̂h́êḿêś'ŝ ĺâýôút̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` t̂áĝś ĉán̂ b́ê ád̂d́êd́ b̂ý [m̂ód̂íf̂ýîńĝ á t̂h́êḿêś'ŝ ĺâýôút̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âút̂óm̂át̂íĉál̂ĺŷ óp̂t́îḿîźê ím̂áĝé f̂ór̂ḿât́. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âút̂óm̂át̂íĉál̂ĺŷ ĺâźŷ-ĺôád̂ ím̂áĝéŝ. [Ĺêár̂ń m̂ór̂é](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ \"ṕr̂íôŕît́ŷ\" t́ô t́r̂úê t́ô ṕr̂él̂óâd́ L̂ĆP̂ ím̂áĝé. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Ûśê t́ĥé `next/script` ĉóm̂ṕôńêńt̂ t́ô d́êf́êŕ l̂óâd́îńĝ óf̂ ńôń-ĉŕît́îćâĺ t̂h́îŕd̂-ṕâŕt̂ý ŝćr̂íp̂t́ŝ. [Ĺêár̂ń m̂ór̂é](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ t́ô ḿâḱê śûŕê ím̂áĝéŝ ár̂é âĺŵáŷś ŝíẑéd̂ áp̂ṕr̂óp̂ŕîát̂él̂ý. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Ĉón̂śîd́êŕ ŝét̂t́îńĝ úp̂ `PurgeCSS` ín̂ `Next.js` ćôńf̂íĝúr̂át̂íôń t̂ó r̂ém̂óv̂é ûńûśêd́ r̂úl̂éŝ f́r̂óm̂ śt̂ýl̂éŝh́êét̂ś. [L̂éâŕn̂ ḿôŕê](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Ûśê `Webpack Bundle Analyzer` t́ô d́êt́êćt̂ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ćôd́ê. [Ĺêár̂ń m̂ór̂é](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Ĉón̂śîd́êŕ ûśîńĝ `Next.js Analytics` t́ô ḿêáŝúr̂é ŷóûŕ âṕp̂'ś r̂éâĺ-ŵór̂ĺd̂ ṕêŕf̂ór̂ḿâńĉé. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Ĉón̂f́îǵûŕê ćâćĥín̂ǵ f̂ór̂ ím̂ḿût́âb́l̂é âśŝét̂ś âńd̂ `Server-side Rendered` (ŚŜŔ) p̂áĝéŝ. [Ĺêár̂ń m̂ór̂é](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ ín̂śt̂éâd́ ôf́ `<img>` t̂ó âd́ĵúŝt́ îḿâǵê q́ûál̂ít̂ý. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Ûśê t́ĥé `next/image` ĉóm̂ṕôńêńt̂ t́ô śêt́ t̂h́ê áp̂ṕr̂óp̂ŕîát̂é `sizes`. [L̂éâŕn̂ ḿôŕê](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Êńâb́l̂é ĉóm̂ṕr̂éŝśîón̂ ón̂ ýôúr̂ Ńêx́t̂.j́ŝ śêŕv̂ér̂. [Ĺêár̂ń m̂ór̂é](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Ĉón̂t́âćt̂ ýôúr̂ áĉćôún̂t́ m̂án̂áĝér̂ t́ô én̂áb̂ĺê [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Ćôńf̂íĝúr̂ín̂ǵ ît́ ŵíl̂ĺ p̂ŕîór̂ít̂íẑé âńd̂ óp̂t́îḿîźê ýôúr̂ ṕâǵê ŕêńd̂ér̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Ûśê t́ĥé [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) ôṕt̂íôń îń N̂ít̂ŕôṔâćk̂ t́ô śêt́ â d́êśîŕêd́ v̂ál̂úê f́ôŕ t̂h́ê ĆŜŚ f̂ón̂t́-d̂íŝṕl̂áŷ ŕûĺê."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Ûśê [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) t́ô áût́ôḿât́îćâĺl̂ý ĉón̂v́êŕt̂ ýôúr̂ ím̂áĝéŝ t́ô Ẃêb́P̂."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "D̂éf̂ér̂ óf̂f́ŝćr̂éêń îḿâǵêś b̂ý êńâb́l̂ín̂ǵ [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Êńâb́l̂é [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) îń N̂ít̂ŕôṔâćk̂ f́ôŕ f̂áŝt́êŕ îńît́îál̂ ĺôád̂ t́îḿêś."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Îḿp̂ŕôv́ê śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê án̂d́ ôṕt̂ím̂íẑé p̂ér̂ćêív̂éd̂ ṕêŕf̂ór̂ḿâńĉé b̂ý âćt̂ív̂át̂ín̂ǵ [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Êńâb́l̂é [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) îń ŷóûŕ Ĉáĉh́îńĝ śêt́t̂ín̂ǵŝ t́ô ŕêd́ûćê t́ĥé ŝíẑé ôf́ ŷóûŕ ĈŚŜ, H́T̂ḾL̂, án̂d́ Ĵáv̂áŜćr̂íp̂t́ f̂íl̂éŝ f́ôŕ f̂áŝt́êŕ l̂óâd́ t̂ím̂éŝ."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Êńâb́l̂é [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) îń ŷóûŕ Ĉáĉh́îńĝ śêt́t̂ín̂ǵŝ t́ô ŕêd́ûćê t́ĥé ŝíẑé ôf́ ŷóûŕ ĴŚ, ĤT́M̂Ĺ, âńd̂ ĆŜŚ f̂íl̂éŝ f́ôŕ f̂áŝt́êŕ l̂óâd́ t̂ím̂éŝ."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Êńâb́l̂é [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) t̂ó r̂ém̂óv̂é ĈŚŜ ŕûĺêś t̂h́ât́ âŕê ńôt́ âṕp̂ĺîćâb́l̂é t̂ó t̂h́îś p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Ĉón̂f́îǵûŕê [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) ín̂ Ńît́r̂óP̂áĉḱ t̂ó d̂él̂áŷ ĺôád̂ín̂ǵ ôf́ ŝćr̂íp̂t́ŝ ún̂t́îĺ t̂h́êý âŕê ńêéd̂éd̂."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Ĝó t̂ó t̂h́ê [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) f́êát̂úr̂é îń t̂h́ê `Caching` ḿêńû án̂d́ âd́ĵúŝt́ ŷóûŕ p̂áĝé ĉáĉh́ê éx̂ṕîŕât́îón̂ t́îḿê t́ô ím̂ṕr̂óv̂é l̂óâd́îńĝ t́îḿêś âńd̂ úŝér̂ éx̂ṕêŕîén̂ćê."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Âút̂óm̂át̂íĉál̂ĺŷ ćôḿp̂ŕêśŝ, óp̂t́îḿîźê, án̂d́ ĉón̂v́êŕt̂ ýôúr̂ ím̂áĝéŝ ín̂t́ô Ẃêb́P̂ b́ŷ én̂áb̂ĺîńĝ t́ĥé [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack) ŝét̂t́îńĝ."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Êńâb́l̂é [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) t̂ó p̂ŕêém̂ṕt̂ív̂él̂ý ôṕt̂ím̂íẑé ŷóûŕ îḿâǵêś âńd̂ ḿâḱê t́ĥém̂ ḿât́ĉh́ t̂h́ê d́îḿêńŝíôńŝ óf̂ t́ĥé ĉón̂t́âín̂ér̂ś t̂h́êý’r̂é d̂íŝṕl̂áŷéd̂ ín̂ áĉŕôśŝ ál̂ĺ d̂év̂íĉéŝ."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Ûśê [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) ín̂ Ńît́r̂óP̂áĉḱ t̂ó r̂éd̂úĉé t̂h́ê śîźê óf̂ t́ĥé f̂íl̂éŝ t́ĥát̂ ár̂é ŝén̂t́ t̂ó t̂h́ê b́r̂óŵśêŕ."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ `format=\"webp\"`. [Ĺêár̂ń m̂ór̂é](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ `loading=\"lazy\"` f́ôŕ ôf́f̂śĉŕêén̂ ím̂áĝéŝ. [Ĺêár̂ń m̂ór̂é](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝṕêćîf́ŷ `preload` f́ôŕ L̂ĆP̂ ím̂áĝé. [L̂éâŕn̂ ḿôŕê](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝṕêćîf́ŷ éx̂ṕl̂íĉít̂ `width` án̂d́ `height`. [L̂éâŕn̂ ḿôŕê](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ t́ĥé âṕp̂ŕôṕr̂íât́ê `quality`. [Ĺêár̂ń m̂ór̂é](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Ûśê t́ĥé `nuxt/image` ĉóm̂ṕôńêńt̂ án̂d́ ŝét̂ t́ĥé âṕp̂ŕôṕr̂íât́ê `sizes`. [Ĺêár̂ń m̂ór̂é](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[R̂ép̂ĺâćê án̂ím̂át̂éd̂ ǴÎF́ŝ ẃît́ĥ v́îd́êó](https://web.dev/replace-gifs-with-videos/) f̂ór̂ f́âśt̂ér̂ ẃêb́ p̂áĝé l̂óâd́ŝ án̂d́ ĉón̂śîd́êŕ ûśîńĝ ḿôd́êŕn̂ f́îĺê f́ôŕm̂át̂ś ŝúĉh́ âś [Ŵéb̂Ḿ](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ôŕ [ÂV́1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) t̂ó îḿp̂ŕôv́ê ćôḿp̂ŕêśŝíôń êf́f̂íĉíêńĉý b̂ý ĝŕêát̂ér̂ t́ĥán̂ 30% óv̂ér̂ t́ĥé ĉúr̂ŕêńt̂ śt̂át̂é-ôf́-t̂h́ê-ár̂t́ v̂íd̂éô ćôd́êć, V̂Ṕ9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á [p̂ĺûǵîń](https://octobercms.com/plugins?search=image) ôŕ ŝér̂v́îćê t́ĥát̂ ẃîĺl̂ áût́ôḿât́îćâĺl̂ý ĉón̂v́êŕt̂ t́ĥé ûṕl̂óâd́êd́ îḿâǵêś t̂ó t̂h́ê óp̂t́îḿâĺ f̂ór̂ḿât́ŝ. [Ẃêb́P̂ ĺôśŝĺêśŝ ím̂áĝéŝ](https://developers.google.com/speed/webp) ár̂é 26% ŝḿâĺl̂ér̂ ín̂ śîźê ćôḿp̂ár̂éd̂ t́ô ṔN̂Ǵŝ án̂d́ 25-34% ŝḿâĺl̂ér̂ t́ĥán̂ ćôḿp̂ár̂áb̂ĺê J́P̂ÉĜ ím̂áĝéŝ át̂ t́ĥé êq́ûív̂ál̂én̂t́ ŜŚÎḾ q̂úâĺît́ŷ ín̂d́êx́. Âńôt́ĥér̂ ńêx́t̂-ǵêń îḿâǵê f́ôŕm̂át̂ t́ô ćôńŝíd̂ér̂ íŝ [ÁV̂ÍF̂](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Ĉón̂śîd́êŕ îńŝt́âĺl̂ín̂ǵ âń [îḿâǵê ĺâźŷ ĺôád̂ín̂ǵ p̂ĺûǵîń](https://octobercms.com/plugins?search=lazy) t̂h́ât́ p̂ŕôv́îd́êś t̂h́ê áb̂íl̂ít̂ý t̂ó d̂éf̂ér̂ án̂ý ôf́f̂śĉŕêén̂ ím̂áĝéŝ, ór̂ śŵít̂ćĥ t́ô á t̂h́êḿê t́ĥát̂ ṕr̂óv̂íd̂éŝ t́ĥát̂ f́ûńĉt́îón̂ál̂ít̂ý. Âĺŝó ĉón̂śîd́êŕ ûśîńĝ [t́ĥé ÂḾP̂ ṕl̂úĝín̂](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "T̂h́êŕê ár̂é m̂án̂ý p̂ĺûǵîńŝ t́ĥát̂ h́êĺp̂ [ín̂ĺîńê ćr̂ít̂íĉál̂ áŝśêt́ŝ](https://octobercms.com/plugins?search=css). T́ĥéŝé p̂ĺûǵîńŝ ḿâý b̂ŕêák̂ ót̂h́êŕ p̂ĺûǵîńŝ, śô ýôú ŝh́ôúl̂d́ t̂éŝt́ t̂h́ôŕôúĝh́l̂ý."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "T̂h́êḿêś, p̂ĺûǵîńŝ án̂d́ ŝér̂v́êŕ ŝṕêćîf́îćât́îón̂ś âĺl̂ ćôńt̂ŕîb́ût́ê t́ô t́ĥé ŝér̂v́êŕ r̂éŝṕôńŝé t̂ím̂é. Ĉón̂śîd́êŕ f̂ín̂d́îńĝ á m̂ór̂é ôṕt̂ím̂íẑéd̂ t́ĥém̂é, ĉár̂éf̂úl̂ĺŷ śêĺêćt̂ín̂ǵ âń ôṕt̂ím̂íẑát̂íôń p̂ĺûǵîń âńd̂/ór̂ úp̂ǵr̂ád̂é t̂h́ê śêŕv̂ér̂. Óĉt́ôb́êŕ ĈḾŜ ál̂śô ál̂ĺôẃŝ d́êv́êĺôṕêŕŝ t́ô úŝé [`Queues`](https://octobercms.com/docs/services/queues) t̂ó d̂éf̂ér̂ t́ĥé p̂ŕôćêśŝín̂ǵ ôf́ â t́îḿê ćôńŝúm̂ín̂ǵ t̂áŝḱ, ŝúĉh́ âś ŝén̂d́îńĝ án̂ é-m̂áîĺ. T̂h́îś d̂ŕâśt̂íĉál̂ĺŷ śp̂éêd́ŝ úp̂ ẃêb́ r̂éq̂úêśt̂ś."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Ĉón̂śîd́êŕ ŝh́ôẃîńĝ éx̂ćêŕp̂t́ŝ ín̂ t́ĥé p̂óŝt́ l̂íŝt́ŝ (é.ĝ. úŝín̂ǵ â `show more` b́ût́t̂ón̂), ŕêd́ûćîńĝ t́ĥé n̂úm̂b́êŕ ôf́ p̂óŝt́ŝ śĥóŵń ôń â ǵîv́êń ŵéb̂ ṕâǵê, b́r̂éâḱîńĝ ĺôńĝ ṕôśt̂ś îńt̂ó m̂úl̂t́îṕl̂é ŵéb̂ ṕâǵêś, ôŕ ûśîńĝ á p̂ĺûǵîń t̂ó l̂áẑý-l̂óâd́ ĉóm̂ḿêńt̂ś."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "T̂h́êŕê ár̂é m̂án̂ý [p̂ĺûǵîńŝ](https://octobercms.com/plugins?search=css) t́ĥát̂ ćâń ŝṕêéd̂ úp̂ á ŵéb̂śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ án̂d́ ĉóm̂ṕr̂éŝśîńĝ t́ĥé ŝt́ŷĺêś. Ûśîńĝ á b̂úîĺd̂ ṕr̂óĉéŝś t̂ó d̂ó t̂h́îś m̂ín̂íf̂íĉát̂íôń ûṕ-f̂ŕôńt̂ ćâń ŝṕêéd̂ úp̂ d́êv́êĺôṕm̂én̂t́."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "T̂h́êŕê ár̂é m̂án̂ý [p̂ĺûǵîńŝ](https://octobercms.com/plugins?search=javascript) t́ĥát̂ ćâń ŝṕêéd̂ úp̂ á ŵéb̂śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ án̂d́ ĉóm̂ṕr̂éŝśîńĝ t́ĥé ŝćr̂íp̂t́ŝ. Úŝín̂ǵ â b́ûíl̂d́ p̂ŕôćêśŝ t́ô d́ô t́ĥíŝ ḿîńîf́îćât́îón̂ úp̂-f́r̂ón̂t́ ĉán̂ śp̂éêd́ ûṕ d̂év̂él̂óp̂ḿêńt̂."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Ĉón̂śîd́êŕ r̂év̂íêẃîńĝ t́ĥé [p̂ĺûǵîńŝ](https://octobercms.com/plugins) ĺôád̂ín̂ǵ ûńûśêd́ ĈŚŜ ón̂ t́ĥé ŵéb̂śît́ê. T́ô íd̂én̂t́îf́ŷ ṕl̂úĝín̂ś t̂h́ât́ âd́d̂ ún̂ńêćêśŝár̂ý ĈŚŜ, ŕûń [ĉód̂é ĉóv̂ér̂áĝé](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) îń Ĉh́r̂óm̂é D̂év̂T́ôól̂ś. Îd́êńt̂íf̂ý t̂h́ê t́ĥém̂é/p̂ĺûǵîń r̂éŝṕôńŝíb̂ĺê f́r̂óm̂ t́ĥé ŝt́ŷĺêśĥéêt́ ÛŔL̂. Ĺôók̂ f́ôŕ p̂ĺûǵîńŝ ẃît́ĥ ḿâńŷ śt̂ýl̂éŝh́êét̂ś ŵít̂h́ l̂ót̂ś ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Á p̂ĺûǵîń ŝh́ôúl̂d́ ôńl̂ý âd́d̂ á ŝt́ŷĺêśĥéêt́ îf́ ît́ îś âćt̂úâĺl̂ý ûśêd́ ôń t̂h́ê ẃêb́ p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Ĉón̂śîd́êŕ r̂év̂íêẃîńĝ t́ĥé [p̂ĺûǵîńŝ](https://octobercms.com/plugins?search=javascript) t́ĥát̂ ĺôád̂ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ín̂ t́ĥé ŵéb̂ ṕâǵê. T́ô íd̂én̂t́îf́ŷ ṕl̂úĝín̂ś t̂h́ât́ âd́d̂ ún̂ńêćêśŝár̂ý Ĵáv̂áŜćr̂íp̂t́, r̂ún̂ [ćôd́ê ćôv́êŕâǵê](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ṕl̂úĝín̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śĉŕîṕt̂. Ĺôók̂ f́ôŕ p̂ĺûǵîńŝ ẃît́ĥ ḿâńŷ śĉŕîṕt̂ś ŵít̂h́ l̂ót̂ś ôf́ r̂éd̂ ín̂ ćôd́ê ćôv́êŕâǵê. Á p̂ĺûǵîń ŝh́ôúl̂d́ ôńl̂ý âd́d̂ á ŝćr̂íp̂t́ îf́ ît́ îś âćt̂úâĺl̂ý ûśêd́ ôń t̂h́ê ẃêb́ p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "R̂éâd́ âb́ôút̂ [ṕr̂év̂én̂t́îńĝ ún̂ńêćêśŝár̂ý n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ŝ ẃît́ĥ t́ĥé ĤT́T̂Ṕ Ĉáĉh́ê](https://web.dev/http-cache/#caching-checklist). T́ĥér̂é âŕê ḿâńŷ [ṕl̂úĝín̂ś](https://octobercms.com/plugins?search=Caching) t̂h́ât́ ĉán̂ b́ê úŝéd̂ t́ô śp̂éêd́ ûṕ ĉáĉh́îńĝ."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ án̂ [ím̂áĝé ôṕt̂ím̂íẑát̂íôń p̂ĺûǵîń](https://octobercms.com/plugins?search=image) t̂ó ĉóm̂ṕr̂éŝśêś îḿâǵêś ŵh́îĺê ŕêt́âín̂ín̂ǵ t̂h́ê q́ûál̂ít̂ý."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Ûṕl̂óâd́ îḿâǵêś d̂ír̂éĉt́l̂ý îń t̂h́ê ḿêd́îá m̂án̂áĝér̂ t́ô én̂śûŕê t́ĥé r̂éq̂úîŕêd́ îḿâǵê śîźêś âŕê áv̂áîĺâb́l̂é. Ĉón̂śîd́êŕ ûśîńĝ t́ĥé [r̂éŝíẑé f̂íl̂t́êŕ](https://octobercms.com/docs/markup/filter-resize) ôŕ âń [îḿâǵê ŕêśîźîńĝ ṕl̂úĝín̂](https://octobercms.com/plugins?search=image) t́ô én̂śûŕê t́ĥé ôṕt̂ím̂ál̂ ím̂áĝé ŝíẑéŝ ár̂é ûśêd́."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂ ín̂ t́ĥé ŵéb̂ śêŕv̂ér̂ ćôńf̂íĝúr̂át̂íôń."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á \"ŵín̂d́ôẃîńĝ\" ĺîb́r̂ár̂ý l̂ík̂é `react-window` t̂ó m̂ín̂ím̂íẑé t̂h́ê ńûḿb̂ér̂ óf̂ D́ÔḾ n̂ód̂éŝ ćr̂éât́êd́ îf́ ŷóû ár̂é r̂én̂d́êŕîńĝ ḿâńŷ ŕêṕêát̂éd̂ él̂ém̂én̂t́ŝ ón̂ t́ĥé p̂áĝé. [L̂éâŕn̂ ḿôŕê](https://web.dev/virtualize-long-lists-react-window/). Ál̂śô, ḿîńîḿîźê ún̂ńêćêśŝár̂ý r̂é-r̂én̂d́êŕŝ úŝín̂ǵ [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent), ôŕ [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) âńd̂ [śk̂íp̂ éf̂f́êćt̂ś](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) ôńl̂ý ûńt̂íl̂ ćêŕt̂áîń d̂ép̂én̂d́êńĉíêś ĥáv̂é ĉh́âńĝéd̂ íf̂ ýôú âŕê úŝín̂ǵ t̂h́ê `Effect` h́ôók̂ t́ô ím̂ṕr̂óv̂é r̂ún̂t́îḿê ṕêŕf̂ór̂ḿâńĉé."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Ŕêáĉt́ R̂óût́êŕ, m̂ín̂ím̂íẑé ûśâǵê óf̂ t́ĥé `<Redirect>` ĉóm̂ṕôńêńt̂ f́ôŕ [r̂óût́ê ńâv́îǵât́îón̂ś](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Îf́ ŷóû ár̂é ŝér̂v́êŕ-ŝíd̂é r̂én̂d́êŕîńĝ án̂ý R̂éâćt̂ ćôḿp̂ón̂én̂t́ŝ, ćôńŝíd̂ér̂ úŝín̂ǵ `renderToPipeableStream()` ôŕ `renderToStaticNodeStream()` t̂ó âĺl̂óŵ t́ĥé ĉĺîén̂t́ t̂ó r̂éĉéîv́ê án̂d́ ĥýd̂ŕât́ê d́îf́f̂ér̂én̂t́ p̂ár̂t́ŝ óf̂ t́ĥé m̂ár̂ḱûṕ îńŝt́êád̂ óf̂ ál̂ĺ ât́ ôńĉé. [L̂éâŕn̂ ḿôŕê](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Îf́ ŷóûŕ b̂úîĺd̂ śŷśt̂ém̂ ḿîńîf́îéŝ ĆŜŚ f̂íl̂éŝ áût́ôḿât́îćâĺl̂ý, êńŝúr̂é t̂h́ât́ ŷóû ár̂é d̂ép̂ĺôýîńĝ t́ĥé p̂ŕôd́ûćt̂íôń b̂úîĺd̂ óf̂ ýôúr̂ áp̂ṕl̂íĉát̂íôń. Ŷóû ćâń ĉh́êćk̂ t́ĥíŝ ẃît́ĥ t́ĥé R̂éâćt̂ D́êv́êĺôṕêŕ T̂óôĺŝ éx̂t́êńŝíôń. [L̂éâŕn̂ ḿôŕê](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Îf́ ŷóûŕ b̂úîĺd̂ śŷśt̂ém̂ ḿîńîf́îéŝ J́Ŝ f́îĺêś âút̂óm̂át̂íĉál̂ĺŷ, én̂śûŕê t́ĥát̂ ýôú âŕê d́êṕl̂óŷín̂ǵ t̂h́ê ṕr̂ód̂úĉt́îón̂ b́ûíl̂d́ ôf́ ŷóûŕ âṕp̂ĺîćât́îón̂. Ýôú ĉán̂ ćĥéĉḱ t̂h́îś ŵít̂h́ t̂h́ê Ŕêáĉt́ D̂év̂él̂óp̂ér̂ T́ôól̂ś êx́t̂én̂śîón̂. [Ĺêár̂ń m̂ór̂é](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Îf́ ŷóû ár̂é n̂ót̂ śêŕv̂ér̂-śîd́ê ŕêńd̂ér̂ín̂ǵ, [ŝṕl̂ít̂ ýôúr̂ J́âv́âŚĉŕîṕt̂ b́ûńd̂ĺêś](https://web.dev/code-splitting-suspense/) ŵít̂h́ `React.lazy()`. Ôt́ĥér̂ẃîśê, ćôd́ê-śp̂ĺît́ ûśîńĝ á t̂h́îŕd̂-ṕâŕt̂ý l̂íb̂ŕâŕŷ śûćĥ áŝ [ĺôád̂áb̂ĺê-ćôḿp̂ón̂én̂t́ŝ](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Ûśê t́ĥé R̂éâćt̂ D́êv́T̂óôĺŝ Ṕr̂óf̂íl̂ér̂, ẃĥíĉh́ m̂ák̂éŝ úŝé ôf́ t̂h́ê Ṕr̂óf̂íl̂ér̂ ÁP̂Í, t̂ó m̂éâśûŕê t́ĥé r̂én̂d́êŕîńĝ ṕêŕf̂ór̂ḿâńĉé ôf́ ŷóûŕ ĉóm̂ṕôńêńt̂ś. [L̂éâŕn̂ ḿôŕê.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "P̂ĺâćê v́îd́êóŝ ín̂śîd́ê `VideoBoxes`, ćûśt̂óm̂íẑé t̂h́êḿ ûśîńĝ `Video Masks` ór̂ ád̂d́ `Transparent Videos`. [L̂éâŕn̂ ḿôŕê](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Ûṕl̂óâd́ îḿâǵêś ûśîńĝ `Wix Media Manager` t́ô én̂śûŕê t́ĥéŷ ár̂é âút̂óm̂át̂íĉál̂ĺŷ śêŕv̂éd̂ áŝ Ẃêb́P̂. F́îńd̂ [ḿôŕê ẃâýŝ t́ô óp̂t́îḿîźê](https://support.wix.com/en/article/site-performance-optimizing-your-media) ýôúr̂ śît́ê'ś m̂éd̂íâ."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Ŵh́êń [âd́d̂ín̂ǵ t̂h́îŕd̂-ṕâŕt̂ý ĉód̂é](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) îń t̂h́ê `Custom Code` t́âb́ ôf́ ŷóûŕ ŝít̂é'ŝ d́âśĥb́ôár̂d́, m̂ák̂é ŝúr̂é ît́'ŝ d́êf́êŕr̂éd̂ ór̂ ĺôád̂éd̂ át̂ t́ĥé êńd̂ óf̂ t́ĥé ĉód̂é b̂ód̂ý. Ŵh́êŕê ṕôśŝíb̂ĺê, úŝé Ŵíx̂’ś [îńt̂éĝŕât́îón̂ś](https://support.wix.com/en/article/about-marketing-integrations) t̂ó êḿb̂éd̂ ḿâŕk̂ét̂ín̂ǵ t̂óôĺŝ ón̂ ýôúr̂ śît́ê. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Ŵíx̂ út̂íl̂íẑéŝ ĆD̂Ńŝ án̂d́ ĉáĉh́îńĝ t́ô śêŕv̂é r̂éŝṕôńŝéŝ áŝ f́âśt̂ áŝ ṕôśŝíb̂ĺê f́ôŕ m̂óŝt́ v̂íŝít̂ór̂ś. Ĉón̂śîd́êŕ [m̂án̂úâĺl̂ý êńâb́l̂ín̂ǵ ĉáĉh́îńĝ](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) f́ôŕ ŷóûŕ ŝít̂é, êśp̂éĉíâĺl̂ý îf́ ûśîńĝ `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "R̂év̂íêẃ âńŷ t́ĥír̂d́-p̂ár̂t́ŷ ćôd́ê ýôú'v̂é âd́d̂éd̂ t́ô ýôúr̂ śît́ê ín̂ t́ĥé `Custom Code` t̂áb̂ óf̂ ýôúr̂ śît́ê'ś d̂áŝh́b̂óâŕd̂ án̂d́ ôńl̂ý k̂éêṕ t̂h́ê śêŕv̂íĉéŝ t́ĥát̂ ár̂é n̂éĉéŝśâŕŷ t́ô ýôúr̂ śît́ê. [F́îńd̂ óût́ m̂ór̂é](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Ĉón̂śîd́êŕ ûṕl̂óâd́îńĝ ýôúr̂ ǴÎF́ t̂ó â śêŕv̂íĉé ŵh́îćĥ ẃîĺl̂ ḿâḱê ít̂ áv̂áîĺâb́l̂é t̂ó êḿb̂éd̂ áŝ án̂ H́T̂ḾL̂5 v́îd́êó."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Ĉón̂śîd́êŕ ûśîńĝ t́ĥé [P̂ér̂f́ôŕm̂án̂ćê Ĺâb́](https://wordpress.org/plugins/performance-lab/) p̂ĺûǵîń t̂ó âút̂óm̂át̂íĉál̂ĺŷ ćôńv̂ér̂t́ ŷóûŕ ûṕl̂óâd́êd́ ĴṔÊǴ îḿâǵêś îńt̂ó Ŵéb̂Ṕ, ŵh́êŕêv́êŕ ŝúp̂ṕôŕt̂éd̂."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Îńŝt́âĺl̂ á [l̂áẑý-l̂óâd́ Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂](https://wordpress.org/plugins/search/lazy+load/) t́ĥát̂ ṕr̂óv̂íd̂éŝ t́ĥé âb́îĺît́ŷ t́ô d́êf́êŕ âńŷ óf̂f́ŝćr̂éêń îḿâǵêś, ôŕ ŝẃît́ĉh́ t̂ó â t́ĥém̂é t̂h́ât́ p̂ŕôv́îd́êś t̂h́ât́ f̂ún̂ćt̂íôńâĺît́ŷ. Ál̂śô ćôńŝíd̂ér̂ úŝín̂ǵ [t̂h́ê ÁM̂Ṕ p̂ĺûǵîń](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "T̂h́êŕê ár̂é â ńûḿb̂ér̂ óf̂ Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ t́ĥát̂ ćâń ĥél̂ṕ ŷóû [ín̂ĺîńê ćr̂ít̂íĉál̂ áŝśêt́ŝ](https://wordpress.org/plugins/search/critical+css/) ór̂ [d́êf́êŕ l̂éŝś îḿp̂ór̂t́âńt̂ ŕêśôúr̂ćêś](https://wordpress.org/plugins/search/defer+css+javascript/). B̂éŵár̂é t̂h́ât́ ôṕt̂ím̂íẑát̂íôńŝ ṕr̂óv̂íd̂éd̂ b́ŷ t́ĥéŝé p̂ĺûǵîńŝ ḿâý b̂ŕêák̂ f́êát̂úr̂éŝ óf̂ ýôúr̂ t́ĥém̂é ôŕ p̂ĺûǵîńŝ, śô ýôú ŵíl̂ĺ l̂ík̂él̂ý n̂éêd́ t̂ó m̂ák̂é ĉód̂é ĉh́âńĝéŝ."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "T̂h́êḿêś, p̂ĺûǵîńŝ, án̂d́ ŝér̂v́êŕ ŝṕêćîf́îćât́îón̂ś âĺl̂ ćôńt̂ŕîb́ût́ê t́ô śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê. Ćôńŝíd̂ér̂ f́îńd̂ín̂ǵ â ḿôŕê óp̂t́îḿîźêd́ t̂h́êḿê, ćâŕêf́ûĺl̂ý ŝél̂éĉt́îńĝ án̂ óp̂t́îḿîźât́îón̂ ṕl̂úĝín̂, án̂d́/ôŕ ûṕĝŕâd́îńĝ ýôúr̂ śêŕv̂ér̂."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Ĉón̂śîd́êŕ ŝh́ôẃîńĝ éx̂ćêŕp̂t́ŝ ín̂ ýôúr̂ ṕôśt̂ ĺîśt̂ś (ê.ǵ. v̂íâ t́ĥé m̂ór̂é t̂áĝ), ŕêd́ûćîńĝ t́ĥé n̂úm̂b́êŕ ôf́ p̂óŝt́ŝ śĥóŵń ôń â ǵîv́êń p̂áĝé, b̂ŕêák̂ín̂ǵ ŷóûŕ l̂ón̂ǵ p̂óŝt́ŝ ín̂t́ô ḿûĺt̂íp̂ĺê ṕâǵêś, ôŕ ûśîńĝ á p̂ĺûǵîń t̂ó l̂áẑý-l̂óâd́ ĉóm̂ḿêńt̂ś."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Â ńûḿb̂ér̂ óf̂ [Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ](https://wordpress.org/plugins/search/minify+css/) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ śt̂ýl̂éŝ. Ýôú m̂áŷ ál̂śô ẃâńt̂ t́ô úŝé â b́ûíl̂d́ p̂ŕôćêśŝ t́ô d́ô t́ĥíŝ ḿîńîf́îćât́îón̂ úp̂-f́r̂ón̂t́ îf́ p̂óŝśîb́l̂é."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Â ńûḿb̂ér̂ óf̂ [Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ](https://wordpress.org/plugins/search/minify+javascript/) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ śĉŕîṕt̂ś. Ŷóû ḿâý âĺŝó ŵán̂t́ t̂ó ûśê á b̂úîĺd̂ ṕr̂óĉéŝś t̂ó d̂ó t̂h́îś m̂ín̂íf̂íĉát̂íôń ûṕ f̂ŕôńt̂ íf̂ ṕôśŝíb̂ĺê."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂ś](https://wordpress.org/plugins/) l̂óâd́îńĝ ún̂úŝéd̂ ĆŜŚ îń ŷóûŕ p̂áĝé. T̂ó îd́êńt̂íf̂ý p̂ĺûǵîńŝ t́ĥát̂ ár̂é âd́d̂ín̂ǵ êx́t̂ŕâńêóûś ĈŚŜ, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developer.chrome.com/docs/devtools/coverage/) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ṕl̂úĝín̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śt̂ýl̂éŝh́êét̂. Ĺôók̂ óût́ f̂ór̂ ṕl̂úĝín̂ś t̂h́ât́ ĥáv̂é m̂án̂ý ŝt́ŷĺêśĥéêt́ŝ ín̂ t́ĥé l̂íŝt́ ŵh́îćĥ h́âv́ê á l̂ót̂ óf̂ ŕêd́ îń ĉód̂é ĉóv̂ér̂áĝé. Â ṕl̂úĝín̂ śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śt̂ýl̂éŝh́êét̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂ś](https://wordpress.org/plugins/) l̂óâd́îńĝ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ín̂ ýôúr̂ ṕâǵê. T́ô íd̂én̂t́îf́ŷ ṕl̂úĝín̂ś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ J́Ŝ, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developer.chrome.com/docs/devtools/coverage/) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ṕl̂úĝín̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śĉŕîṕt̂. Ĺôók̂ óût́ f̂ór̂ ṕl̂úĝín̂ś t̂h́ât́ ĥáv̂é m̂án̂ý ŝćr̂íp̂t́ŝ ín̂ t́ĥé l̂íŝt́ ŵh́îćĥ h́âv́ê á l̂ót̂ óf̂ ŕêd́ îń ĉód̂é ĉóv̂ér̂áĝé. Â ṕl̂úĝín̂ śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śĉŕîṕt̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "R̂éâd́ âb́ôút̂ [B́r̂óŵśêŕ Ĉáĉh́îńĝ ín̂ Ẃôŕd̂Ṕr̂éŝś](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ án̂ [ím̂áĝé ôṕt̂ím̂íẑát̂íôń Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂](https://wordpress.org/plugins/search/optimize+images/) t́ĥát̂ ćôḿp̂ŕêśŝéŝ ýôúr̂ ím̂áĝéŝ ẃĥíl̂é r̂ét̂áîńîńĝ q́ûál̂ít̂ý."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Ûṕl̂óâd́ îḿâǵêś d̂ír̂éĉt́l̂ý t̂h́r̂óûǵĥ t́ĥé [m̂éd̂íâ ĺîb́r̂ár̂ý](https://wordpress.org/support/article/media-library-screen/) t̂ó êńŝúr̂é t̂h́ât́ t̂h́ê ŕêq́ûír̂éd̂ ím̂áĝé ŝíẑéŝ ár̂é âv́âíl̂áb̂ĺê, án̂d́ t̂h́êń îńŝér̂t́ t̂h́êḿ f̂ŕôḿ t̂h́ê ḿêd́îá l̂íb̂ŕâŕŷ ór̂ úŝé t̂h́ê ím̂áĝé ŵíd̂ǵêt́ t̂ó êńŝúr̂é t̂h́ê óp̂t́îḿâĺ îḿâǵê śîźêś âŕê úŝéd̂ (ín̂ćl̂úd̂ín̂ǵ t̂h́ôśê f́ôŕ t̂h́ê ŕêśp̂ón̂śîv́ê b́r̂éâḱp̂óîńt̂ś). Âv́ôíd̂ úŝín̂ǵ `Full Size` îḿâǵêś ûńl̂éŝś t̂h́ê d́îḿêńŝíôńŝ ár̂é âd́êq́ûát̂é f̂ór̂ t́ĥéîŕ ûśâǵê. [Ĺêár̂ń M̂ór̂é](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Ŷóû ćâń êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂ ín̂ ýôúr̂ ẃêb́ ŝér̂v́êŕ ĉón̂f́îǵûŕât́îón̂."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Êńâb́l̂é 'Îḿâǵîf́ŷ' f́r̂óm̂ t́ĥé Îḿâǵê Óp̂t́îḿîźât́îón̂ t́âb́ îń 'ŴṔ R̂óĉḱêt́' t̂ó ĉón̂v́êŕt̂ ýôúr̂ ím̂áĝéŝ t́ô Ẃêb́P̂."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Êńâb́l̂é [L̂áẑýL̂óâd́](https://docs.wp-rocket.me/article/1141-lazyload-for-images) îń ŴṔ R̂óĉḱêt́ t̂ó f̂íx̂ t́ĥíŝ ŕêćôḿm̂én̂d́ât́îón̂. T́ĥíŝ f́êát̂úr̂é d̂él̂áŷś t̂h́ê ĺôád̂ín̂ǵ ôf́ t̂h́ê ím̂áĝéŝ ún̂t́îĺ t̂h́ê v́îśît́ôŕ ŝćr̂ól̂ĺŝ d́ôẃn̂ t́ĥé p̂áĝé âńd̂ áĉt́ûál̂ĺŷ ńêéd̂ś t̂ó ŝéê t́ĥém̂."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Êńâb́l̂é [R̂ém̂óv̂é Ûńûśêd́ ĈŚŜ](https://docs.wp-rocket.me/article/1529-remove-unused-css) án̂d́ [L̂óâd́ Ĵáv̂áŜćr̂íp̂t́ d̂éf̂ér̂ŕêd́](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) îń 'ŴṔ R̂óĉḱêt́' t̂ó âd́d̂ŕêśŝ t́ĥíŝ ŕêćôḿm̂én̂d́ât́îón̂. T́ĥéŝé f̂éât́ûŕêś ŵíl̂ĺ r̂éŝṕêćt̂ív̂él̂ý ôṕt̂ím̂íẑé t̂h́ê ĆŜŚ âńd̂ J́âv́âŚĉŕîṕt̂ f́îĺêś ŝó t̂h́ât́ t̂h́êý d̂ón̂'t́ b̂ĺôćk̂ t́ĥé r̂én̂d́êŕîńĝ óf̂ ýôúr̂ ṕâǵê."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Êńâb́l̂é [M̂ín̂íf̂ý ĈŚŜ f́îĺêś](https://docs.wp-rocket.me/article/1350-css-minify-combine) îń 'ŴṔ R̂óĉḱêt́' t̂ó f̂íx̂ t́ĥíŝ íŝśûé. Âńŷ śp̂áĉéŝ án̂d́ ĉóm̂ḿêńt̂ś îń ŷóûŕ ŝít̂é'ŝ ĆŜŚ f̂íl̂éŝ ẃîĺl̂ b́ê ŕêḿôv́êd́ t̂ó m̂ák̂é t̂h́ê f́îĺê śîźê śm̂ál̂ĺêŕ âńd̂ f́âśt̂ér̂ t́ô d́ôẃn̂ĺôád̂."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Êńâb́l̂é [M̂ín̂íf̂ý Ĵáv̂áŜćr̂íp̂t́ f̂íl̂éŝ](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) ín̂ 'ẂP̂ Ŕôćk̂ét̂' t́ô f́îx́ t̂h́îś îśŝúê. Ém̂ṕt̂ý ŝṕâćêś âńd̂ ćôḿm̂én̂t́ŝ ẃîĺl̂ b́ê ŕêḿôv́êd́ f̂ŕôḿ Ĵáv̂áŜćr̂íp̂t́ f̂íl̂éŝ t́ô ḿâḱê t́ĥéîŕ ŝíẑé ŝḿâĺl̂ér̂ án̂d́ f̂áŝt́êŕ t̂ó d̂óŵńl̂óâd́."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Êńâb́l̂é [R̂ém̂óv̂é Ûńûśêd́ ĈŚŜ](https://docs.wp-rocket.me/article/1529-remove-unused-css) ín̂ 'ẂP̂ Ŕôćk̂ét̂' t́ô f́îx́ t̂h́îś îśŝúê. Ít̂ ŕêd́ûćêś p̂áĝé ŝíẑé b̂ý r̂ém̂óv̂ín̂ǵ âĺl̂ ĆŜŚ âńd̂ śt̂ýl̂éŝh́êét̂ś t̂h́ât́ âŕê ńôt́ ûśêd́ ŵh́îĺê ḱêép̂ín̂ǵ ôńl̂ý t̂h́ê úŝéd̂ ĆŜŚ f̂ór̂ éâćĥ ṕâǵê."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Êńâb́l̂é [D̂él̂áŷ J́âv́âŚĉŕîṕt̂ éx̂éĉút̂íôń](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) îń 'ŴṔ R̂óĉḱêt́' t̂ó f̂íx̂ t́ĥíŝ ṕr̂ób̂ĺêḿ. Ît́ ŵíl̂ĺ îḿp̂ŕôv́ê t́ĥé l̂óâd́îńĝ óf̂ ýôúr̂ ṕâǵê b́ŷ d́êĺâýîńĝ t́ĥé êx́êćût́îón̂ óf̂ śĉŕîṕt̂ś ûńt̂íl̂ úŝér̂ ín̂t́êŕâćt̂íôń. Îf́ ŷóûŕ ŝít̂é ĥáŝ íf̂ŕâḿêś, ŷóû ćâń ûśê ẂP̂ Ŕôćk̂ét̂'ś [L̂áẑýL̂óâd́ f̂ór̂ íf̂ŕâḿêś âńd̂ v́îd́êóŝ](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) án̂d́ [R̂ép̂ĺâćê ÝôúT̂úb̂é îf́r̂ám̂é ŵít̂h́ p̂ŕêv́îéŵ ím̂áĝé](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) âś ŵél̂ĺ."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Êńâb́l̂é 'Îḿâǵîf́ŷ' f́r̂óm̂ t́ĥé Îḿâǵê Óp̂t́îḿîźât́îón̂ t́âb́ îń 'ŴṔ R̂óĉḱêt́' âńd̂ ŕûń B̂úl̂ḱ Ôṕt̂ím̂íẑát̂íôń t̂ó ĉóm̂ṕr̂éŝś ŷóûŕ îḿâǵêś."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Ûśê [Ṕr̂éf̂ét̂ćĥ D́N̂Ś R̂éq̂úêśt̂ś](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) îń 'ŴṔ R̂óĉḱêt́' t̂ó âd́d̂ \"d́n̂ś-p̂ŕêf́êt́ĉh́\" âńd̂ śp̂éêd́ ûṕ t̂h́ê ćôńn̂éĉt́îón̂ ẃît́ĥ éx̂t́êŕn̂ál̂ d́ôḿâín̂ś. Âĺŝó, 'ŴṔ R̂óĉḱêt́' âút̂óm̂át̂íĉál̂ĺŷ ád̂d́ŝ \"ṕr̂éĉón̂ńêćt̂\" t́ô [Ǵôóĝĺê F́ôńt̂ś d̂óm̂áîń](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) âńd̂ án̂ý ĈŃÂḾÊ(Ś) âd́d̂éd̂ v́îá t̂h́ê [Én̂áb̂ĺê ĆD̂Ń](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) f̂éât́ûŕê."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "T̂ó f̂íx̂ t́ĥíŝ íŝśûé f̂ór̂ f́ôńt̂ś, êńâb́l̂é [R̂ém̂óv̂é Ûńûśêd́ ĈŚŜ](https://docs.wp-rocket.me/article/1529-remove-unused-css) ín̂ 'ẂP̂ Ŕôćk̂ét̂'. Ýôúr̂ śît́ê'ś ĉŕît́îćâĺ f̂ón̂t́ŝ ẃîĺl̂ b́ê ṕr̂él̂óâd́êd́ ŵít̂h́ p̂ŕîór̂ít̂ý."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Ŝéê ćâĺĉúl̂át̂ór̂."}, "report/renderer/report-utils.js | collapseView": {"message": "Ĉól̂ĺâṕŝé v̂íêẃ"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Îńît́îál̂ Ńâv́îǵât́îón̂"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "M̂áx̂ím̂úm̂ ćr̂ít̂íĉál̂ ṕât́ĥ ĺât́êńĉý:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Ĉóp̂ý ĴŚÔŃ"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "T̂óĝǵl̂é D̂ár̂ḱ T̂h́êḿê"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "P̂ŕîńt̂ Éx̂ṕâńd̂éd̂"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "P̂ŕîńt̂ Śûḿm̂ár̂ý"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Ŝáv̂é âś Ĝíŝt́"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Ŝáv̂é âś ĤT́M̂Ĺ"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Ŝáv̂é âś ĴŚÔŃ"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Ôṕêń îń V̂íêẃêŕ"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "V̂íêẃ Ûńt̂h́r̂ót̂t́l̂éd̂ T́r̂áĉé"}, "report/renderer/report-utils.js | errorLabel": {"message": "Êŕr̂ór̂!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "R̂ép̂ór̂t́ êŕr̂ór̂: ńô áûd́ît́ îńf̂ór̂ḿât́îón̂"}, "report/renderer/report-utils.js | expandView": {"message": "Êx́p̂án̂d́ v̂íêẃ"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "1ŝt́ p̂ár̂t́ŷ"}, "report/renderer/report-utils.js | footerIssue": {"message": "F̂íl̂é âń îśŝúê"}, "report/renderer/report-utils.js | hide": {"message": "Ĥíd̂é"}, "report/renderer/report-utils.js | labDataTitle": {"message": "L̂áb̂ D́ât́â"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[L̂íĝh́t̂h́ôúŝé](https://developers.google.com/web/tools/lighthouse/) âńâĺŷśîś ôf́ t̂h́ê ćûŕr̂én̂t́ p̂áĝé ôń âń êḿûĺât́êd́ m̂ób̂íl̂é n̂ét̂ẃôŕk̂. V́âĺûéŝ ár̂é êśt̂ím̂át̂éd̂ án̂d́ m̂áŷ v́âŕŷ."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Âd́d̂ít̂íôńâĺ ît́êḿŝ t́ô ḿâńûál̂ĺŷ ćĥéĉḱ"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "N̂ót̂ áp̂ṕl̂íĉáb̂ĺê"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Ôṕêń îń â ńêẃ t̂áb̂"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Ôṕp̂ór̂t́ûńît́ŷ"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Êśt̂ím̂át̂éd̂ Śâv́îńĝś"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "P̂áŝśêd́ âúd̂ít̂ś"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Âś p̂ér̂ [Ćĥŕôḿê’ś ûṕd̂át̂éd̂ Ín̂śt̂ál̂ĺâb́îĺît́ŷ Ćr̂ít̂ér̂íâ](https://developer.chrome.com/blog/update-install-criteria), Ĺîǵĥt́ĥóûśê ẃîĺl̂ b́ê d́êṕr̂éĉát̂ín̂ǵ t̂h́ê ṔŴÁ ĉát̂éĝór̂ý îń â f́ût́ûŕê ŕêĺêáŝé. P̂ĺêáŝé r̂éf̂ér̂ t́ô t́ĥé [ûṕd̂át̂éd̂ ṔŴÁ d̂óĉúm̂én̂t́ât́îón̂](https://developer.chrome.com/docs/devtools/progressive-web-apps/) f́ôŕ f̂út̂úr̂é P̂ẂÂ t́êśt̂ín̂ǵ."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Îńît́îál̂ ṕâǵê ĺôád̂"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "P̂óîńt̂-ín̂-t́îḿê śn̂áp̂śĥót̂"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Ûśêŕ îńt̂ér̂áĉt́îón̂ś t̂ím̂éŝṕâń"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Ĉúŝt́ôḿ t̂h́r̂ót̂t́l̂ín̂ǵ"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Êḿûĺât́êd́ D̂éŝḱt̂óp̂"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Êḿûĺât́êd́ M̂ót̂ó Ĝ Ṕôẃêŕ"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "N̂ó êḿûĺât́îón̂"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Âx́ê v́êŕŝíôń"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Ûńt̂h́r̂ót̂t́l̂éd̂ ĆP̂Ú/M̂ém̂ór̂ý P̂óŵér̂"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "ĈṔÛ t́ĥŕôt́t̂ĺîńĝ"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "D̂év̂íĉé"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "N̂ét̂ẃôŕk̂ t́ĥŕôt́t̂ĺîńĝ"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Ŝćr̂éêń êḿûĺât́îón̂"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Ûśêŕ âǵêńt̂ (ńêt́ŵór̂ḱ)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Ŝín̂ǵl̂é p̂áĝé ŝéŝśîón̂"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "T̂h́îś d̂át̂á îś t̂ák̂én̂ f́r̂óm̂ á ŝín̂ǵl̂é p̂áĝé ŝéŝśîón̂, áŝ óp̂ṕôśêd́ t̂ó f̂íêĺd̂ d́ât́â śûḿm̂ár̂íẑín̂ǵ m̂án̂ý ŝéŝśîón̂ś."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Ŝĺôẃ 4Ĝ t́ĥŕôt́t̂ĺîńĝ"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Ûńk̂ńôẃn̂"}, "report/renderer/report-utils.js | show": {"message": "Ŝh́ôẃ"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Ŝh́ôẃ âúd̂ít̂ś r̂él̂év̂án̂t́ t̂ó:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Ĉól̂ĺâṕŝé ŝńîṕp̂ét̂"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Êx́p̂án̂d́ ŝńîṕp̂ét̂"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Ŝh́ôẃ 3r̂d́-p̂ár̂t́ŷ ŕêśôúr̂ćêś"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "P̂ŕôv́îd́êd́ b̂ý êńv̂ír̂ón̂ḿêńt̂"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "T̂h́êŕê ẃêŕê íŝśûéŝ áf̂f́êćt̂ín̂ǵ t̂h́îś r̂ún̂ óf̂ Ĺîǵĥt́ĥóûśê:"}, "report/renderer/report-utils.js | unattributable": {"message": "Ûńât́t̂ŕîb́ût́âb́l̂é"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "V̂ál̂úêś âŕê éŝt́îḿât́êd́ âńd̂ ḿâý v̂ár̂ý. T̂h́ê [ṕêŕf̂ór̂ḿâńĉé ŝćôŕê íŝ ćâĺĉúl̂át̂éd̂](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) d́îŕêćt̂ĺŷ f́r̂óm̂ t́ĥéŝé m̂ét̂ŕîćŝ."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "V̂íêẃ T̂ŕâćê"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "V̂íêẃ T̂ŕêém̂áp̂"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "P̂áŝśêd́ âúd̂ít̂ś b̂út̂ ẃît́ĥ ẃâŕn̂ín̂ǵŝ"}, "report/renderer/report-utils.js | warningHeader": {"message": "Ŵár̂ńîńĝś: "}, "treemap/app/src/util.js | allLabel": {"message": "Âĺl̂"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Âĺl̂ Śĉŕîṕt̂ś"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Ĉóv̂ér̂áĝé"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "D̂úp̂ĺîćât́ê Ḿôd́ûĺêś"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "R̂éŝóûŕĉé B̂ýt̂éŝ"}, "treemap/app/src/util.js | tableColumnName": {"message": "N̂ám̂é"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "T̂óĝǵl̂é T̂áb̂ĺê"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Ûńûśêd́ B̂ýt̂éŝ"}}