{"core/audits/accessibility/accesskeys.js | description": {"message": "Naudodami prieigos raktus naudotojai gali greitai suaktyvinti puslapio dalį. Kad nar<PERSON><PERSON> veikt<PERSON> tin<PERSON>, kiekvienas prieigos raktas turi būti unikalus. [Sužinokite daugiau apie prieigos raktus](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Elemento „`[accesskey]`“ vertės nėra unikalios"}, "core/audits/accessibility/accesskeys.js | title": {"message": "„`[accesskey]`“ vert<PERSON><PERSON> yra un<PERSON>s"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Kiekviename ARIA elemente „`role`“ palaikoma tik dalis konkrečių atributų „`aria-*`“. Jei jie nebus suderinti, atributai „`aria-*`“ taps netinkami. [<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> suderinti ARIA atributus su jų vaidmenimis](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributai „`[aria-*]`“ neatitinka savo vaidmenų"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributai „`[aria-*]`“ atitinka savo vaidmenis"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "ARIA „`role`“ leid<PERSON><PERSON> pagalbinėms technologijoms sužinoti kiekvieno tinklalapio elemento vaidmenį. <PERSON>i „`role`“ vert<PERSON><PERSON> su klaid<PERSON>, nėra esamų ARIA „`role`“ verčių arba abstrakčių vaidmenų, elemento tikslas nebus pateiktas pagalbinių technologijų naudotojams. [Sužinokite daugiau apie ARIA vaidmenis](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "„`role=\"\"`“ priskirtos vertė<PERSON> nėra tin<PERSON>mi ARIA vaidmenys."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "„`role=\"\"`“ priskirtos vertė<PERSON> yra tin<PERSON> vaidmenys."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "<PERSON> nenurodytas pasiekiamasis elemento pavadinimas, e<PERSON>no skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvo naudotojai. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip komandų elementus padaryti lengviau pasiekiamus](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti elementų „`button`“, „`link`“ ir „`menuitem`“ pavadinimai."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti elementų „`button`“, „`link`“ ir „`menuitem`“ pavadinimai"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "ARIA dialogo elementai be pasiekiamųjų pavadinimų gali neleisti ekrano skaitytuvų naudotojams matyti šių elementų tikslo. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip padaryti ARIA dialogo elementus lengviau pasiekiamus](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Elementuose su `role=\"dialog\"` arba `role=\"alertdialog\"` nėra pasiekiamųjų pavadinimų."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Elementuose su `role=\"dialog\"` arba `role=\"alertdialog\"` yra pasiekiam<PERSON>jų pavadinimų."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Pagalbinės technologijos, pvz., e<PERSON><PERSON>, ve<PERSON>a <PERSON>, kai dokument<PERSON> „`<body>`“ nustatytas elementas „`aria-hidden=\"true\"`“. [<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> „`aria-hidden`“ paveikia dokumento turinį](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Dokumente „`<body>`“ yra elementas „`[aria-hidden=\"true\"]`“"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Dokumente „`<body>`“ nėra elemento „`[aria-hidden=\"true\"]`“"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "<PERSON><PERSON>l elemente „`[aria-hidden=\"true\"]`“ esančių suaktyvinamų poelemenčių tie interaktyvūs elementai tampa nepasiekiami pagalbinių technologijų, pvz., ekrano skaitytuvų, naudotojams. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip „`aria-hidden`“ paveikia suaktyvinamus elementus](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementuose „`[aria-hidden=\"true\"]`“ yra i<PERSON>amų mažėjančia tvarka pateiktų elementų"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Elementuose „`[aria-hidden=\"true\"]`“ nėra išryškinamų mažėjančia tvarka pateiktų elementų"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON> nenurod<PERSON>as pasiek<PERSON> įvesties lauko pava<PERSON>, e<PERSON>no skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvo naudotojai. [Sužinokite daugiau apie įvesties lauko etiketes](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA įvesties laukų pavadinimai"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA įvesties laukų pavadinimai"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "<PERSON> skaitiklio elementas neturi pasiek<PERSON> pava<PERSON>, e<PERSON><PERSON> skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvais pasikliaujantys naudotojai. [<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> pavadinti elementus „`meter`“](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA elementų „`meter`“ pavadinimai."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA elementų „`meter`“ pavadinimai"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "<PERSON> elementas „`progressbar`“ neturi pasiek<PERSON> pava<PERSON>, e<PERSON>no skaitytuvai apie jį praneša naudodami bendrąjį pavadinim<PERSON>, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvais pasikliaujantys naudotojai. [<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> p<PERSON> „`progressbar`“ elementus](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA elementų „`progressbar`“ pavadinimai."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA elementų „`progressbar`“ pavadinimai"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Kai kuriems ARIA vaidmenims reikia atributų, aprašančių elementų būseną ekrano skaitytuvams. [Sužinokite daugiau apie vaidmenis ir būtinus atributus](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementuose „`[role]`“ nėra visų privalomų atributų „`[aria-*]`“"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Elementuose „`[role]`“ yra visi būtini atributai „`[aria-*]`“"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "<PERSON> kuriuose ARIA pirminiuose vaidmenyse turi būti konkrečių antrinių vaidmenų, kad būtų galima atlikti numatytas pritaikomumo funkcijas. [Sužinokite daugiau apie vaidmenis ir būtinus antrinius elementus](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementuose su <PERSON> vaidmeniu „`[role]`“, kurių antriniuose elementuose turi būti nurodytas konkretus „`[role]`“, trūksta kai kurių arba visų būtinų antrinių elementų."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementuose su <PERSON> vaid<PERSON>iu „`[role]`“, kurių antriniuose elementuose turi būti nurodytas konkretus „`[role]`“, yra visi būtini antriniai elementai."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON> kurie ARIA antriniai vaidmenys turi būti konkrečiuose pirminiuose vaidmenyse, kad būt<PERSON> tinkamai vykdomos numatytosios pritaikomumo funkcijos. [Sužinokite daugiau apie ARIA vaidmenis ir būtiną pirminį elementą](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Elementų „`[role]`“ n<PERSON>ra būtiname pirminiame elemente"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Elementai „`[role]`“ yra bū<PERSON>me pirminiame elemente"}, "core/audits/accessibility/aria-roles.js | description": {"message": "ARIA vaidmenų vertės turi būti tin<PERSON>, kad būt<PERSON> galima atlikti numatytas pritaikomumo funkcijas. [Sužinokite daugiau apie tinkamus ARIA vaidmenis](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Elemento „`[role]`“ vert<PERSON>s yra net<PERSON>s"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Element<PERSON> „`[role]`“ vert<PERSON>s yra tin<PERSON>mos"}, "core/audits/accessibility/aria-text.js | description": {"message": "P<PERSON><PERSON><PERSON>s `role=text` aplink teksto mazgo padalijimą pagal žym<PERSON>ji<PERSON>, „VoiceOver“ gali laikyti jį viena fraze, bet išryškinami elemento poelemenčiai nebus skelbiami. [Sužinokite daugiau apie atributą „`role=text`“](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Elementuose su atributu „`role=text`“ yra suaktyvinamų poelemenčių."}, "core/audits/accessibility/aria-text.js | title": {"message": "Elementuose su atributu „`role=text`“ nėra suaktyvinamų poelemenčių."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON> nenurod<PERSON>as pasiekiam<PERSON> perjungimo lauko p<PERSON>, e<PERSON><PERSON> skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvo naudotojai. [Sužinokite daugiau apie laukų perjungimą](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA perjungimo laukų pavadinimai"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA perjungimo laukų pavadinimai"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Kai patarimo elementas neturi pasiek<PERSON> pava<PERSON>, e<PERSON><PERSON> skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvais pasikliaujantys naudotojai. [<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> pavadinti elementus „`tooltip`“](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA elementų „`tooltip`“ pavadinimai."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA elementų „`tooltip`“ pavadinimai"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON> elementas „`treeitem`“ neturi pasiekiam<PERSON>jo pavadin<PERSON>, ekrano skaitytuvai apie jį praneša naudodami bendrąjį pavadinimą, tod<PERSON><PERSON> jo negali naudoti ekrano skaitytuvais pasikliaujantys naudotojai. [Sužinokite daugiau apie elementų „`treeitem`“ žymėjimą etiketėmis](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti ARIA elementų „`treeitem`“ pavadinimai."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Nurodyti neįgaliesiems pritaikyti ARIA elementų „`treeitem`“ pavadinimai"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Pagalbinės technologijos, pvz., e<PERSON><PERSON>, negali interpretuoti ARIA atributų su netinkamomis vertėmis. [Sužinokite daugiau apie tinkamas ARIA atributų vertes](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributuose „`[aria-*]`“ nėra tinkamų verčių"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atribut<PERSON> „`[aria-*]`“ vert<PERSON>s yra tin<PERSON>mos"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Pagalbinės technologi<PERSON>, pvz., e<PERSON><PERSON>, negali tinkamai interpretuoti ARIA atributų su netinkamais pavadinimais. [Sužinokite daugiau apie tinkamus ARIA atributus](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributai „`[aria-*]`“ netinkami arba yra rašybos klaidų"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributai „`[aria-*]`“ yra <PERSON>, rašybos klaidų nėra"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Netinkami elementai"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON> ne<PERSON>rod<PERSON>as pasiekiamasis mygtuko p<PERSON>, e<PERSON><PERSON> skaitytuvai apie jį praneša kaip „button“ (mygtukas), to<PERSON><PERSON><PERSON> jo negali naudoti ekrano skaitytuvo naudotojai. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip mygtukus padaryti lengviau pasiekiamus](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti mygtukų pavadinimai"}, "core/audits/accessibility/button-name.js | title": {"message": "Mygtukų pavadinimai pritaikyti neįgaliesiems"}, "core/audits/accessibility/bypass.js | description": {"message": "Prid<PERSON><PERSON>s būd<PERSON> apeiti pasikartojantį turinį, klaviatūros naudotojai galės efektyviau naršyti puslapį. [Sužinokite daugiau apie apėjimo užblokavimus](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Puslapyje nėra <PERSON>, praleidimo nuorodos arba orientyro regiono"}, "core/audits/accessibility/bypass.js | title": {"message": "Puslapyje yra ant<PERSON>š<PERSON>ė, praleidžiama nuoroda arba orientyro regionas"}, "core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON><PERSON><PERSON> kontrasto tekstą daugumai naudotojų yra sudėtinga arba neįmanoma perskaityti. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip užtikrinti tinkamą spalvų kontrastą](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Nepakankamas fono ir priekinio fono spalvų kontrasto <PERSON>."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Fono ir priekinio plano spalvų kontrasto santyk<PERSON> paka<PERSON>"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON> apraš<PERSON> są<PERSON>šai netinkamai p<PERSON><PERSON><PERSON>, e<PERSON>no skaitytuvai gali pateikti klaidinančią arba netikslią išvestį. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip tinkamai nustatyti aprašo sąrašų struktūrą](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementuose „`<dl>`“ nurodytos ne tik tinkamai surikiuotos grupės „`<dt>`“ ir „`<dd>`“ bei elementai „`<script>`“, „`<template>`“ arba „`<div>`“."}, "core/audits/accessibility/definition-list.js | title": {"message": "Elementuose „`<dl>`“ nurodytos tik tinkamai surikiuotos grupės „`<dt>`“ ir „`<dd>`“ bei elementai „`<script>`“, „`<template>`“ arba „`<div>`“."}, "core/audits/accessibility/dlitem.js | description": {"message": "<PERSON>ašo sąra<PERSON> elementai („`<dt>`“ ir „`<dd>`“) turi būti sujungti pirminiame elemente „`<dl>`“, kad ekrano skaitytuvai galėtų apie juos tinkamai pranešti. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip tinkamai nustatyti aprašo sąrašų struktūrą](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Apr<PERSON>šo sąrašo elementai nesujungti elementuose „`<dl>`“"}, "core/audits/accessibility/dlitem.js | title": {"message": "Apr<PERSON>šo sąrašo elementai sujungti elementuose „`<dl>`“"}, "core/audits/accessibility/document-title.js | description": {"message": "Pagal pavadinimą ekrano skaitytuvo naudotojai su<PERSON>, apie ką yra puslapio turiny<PERSON>, o paieškos variklio naudotojai gali nuspręsti, ar puslapis atitinka jų paiešką. [Sužinokite daugiau apie dokumentų pavadinimus](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumente nėra elemento „`<title>`“"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumente yra elementas „`<title>`“"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "<PERSON><PERSON><PERSON> su<PERSON>ų elementų `id` turi būti <PERSON>, siek<PERSON> užti<PERSON>, kad elementai bus matomi pagalbinėms technologijoms. [Su<PERSON><PERSON>kite, kaip išspręsti pasikartojančių `id` problemą](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "`[id]` atributai aktyviuose, i<PERSON><PERSON><PERSON>kinamuose elementuose nėra unikalūs"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "`[id]` atributai aktyviuose, i<PERSON><PERSON><PERSON>kinamuose elementuose yra unikalūs"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA ID vertė turi b<PERSON><PERSON>, kad paga<PERSON>ės technologijos nepraleistų kitų objektų. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išspręsti pasikartojančių ARIA ID problemą](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA ID nėra unikalūs"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA ID yra unika<PERSON>s"}, "core/audits/accessibility/empty-heading.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, ar nepasiekiamas tekstas neleidžia ekrano skaitytuvų naudotojams pasiekti puslapio struktūros informacijos. [Sužinokite daugiau apie antraštes](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elementuose nėra turinio."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Visuose antra<PERSON><PERSON>ės elementuose yra turinio."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Apie formos laukus su keliomis etiketėmis gali klaidinančiai pranešti pagalbinės technologijos, pvz., ekrano s<PERSON>, naudojan<PERSON>s p<PERSON>, paskutinę arba visas etiketes. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip naudoti formų etiketes](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Formos laukai yra su keliomis etike<PERSON>ėmis"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nėra jokių formos laukų su keliomis etiketėmis"}, "core/audits/accessibility/frame-title.js | description": {"message": "Pagal rėmelių pavadinimus ekrano skaitytuvų naudotojai sužino, koks yra rėmelių turinys. [Sužinokite daugiau apie rėmelių pavadinimus](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementas „`<frame>`“ arba „`<iframe>`“ neturi pavadinimo"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementai „`<frame>`“ arba „`<iframe>`“ turi pavadin<PERSON>ą"}, "core/audits/accessibility/heading-order.js | description": {"message": "Tinkamai pagal lygius surikiuotos antraštės perteikia semantinę puslapio struktūr<PERSON>, to<PERSON><PERSON><PERSON> jas galima lengviau naršyti ir suprasti, naudojant pagalbines technologijas. [Sužinokite daugiau apie antraštės tvarką](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Antraščių elementai nepateikiami nuosekliai mažėjimo tvarka"}, "core/audits/accessibility/heading-order.js | title": {"message": "Antraščių elementai pateikiami nuosekliai mažėjimo tvarka"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Jei puslapyje nenurodytas atributas „`lang`“, ekrano skaitytuvas manys, kad puslapio kalba atitinka naudotojo pasirinktą numatytąją kalbą, kai buvo nustatomas ekrano skaitytuvas. Jei puslapio kalba neatitinka numatytosios kalbos, gali b<PERSON><PERSON>, kad ekrano skaitytuvas netinkamai skaitys puslapio tekstą. [Sužinokite daugiau apie atributą „`lang`“](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elemente „`<html>`“ nėra atributo „`[lang]`“"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Elemente „`<html>`“ yra atributas „`[lang]`“"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tinkamą [BCP 47 kalbą](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ekrano skaitytuvai gali tinkamai pranešti tekstą. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip naudoti atributą „`lang`“](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid),"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elemente „`<html>`“ nenurodyta tinkama atributo „`[lang]`“ vertė."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Elemento „`<html>`“ atributo „`[lang]`“ vertė yra tinkama"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Jei tinklalapyje nenurodyta nuo<PERSON>, ekrano skaitytuvas gali netinkamai pranešti puslapio tekstą. [Sužinokite daugiau apie atributą „`lang`“](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Elemente „`<html>`“ nėra atributo „`[xml:lang]`“ su ta pačia pagrindine kalba kaip atribute „`[lang]`“."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Elemente „`<html>`“ yra atributas „`[xml:lang]`“, kurio pagrindin<PERSON> kalba sutampa su atributo „`[lang]`“ kalba."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Nuorodų su ta pačia paskirties vieta aprašas turi būti toks pats, kad naudotojai lengviau suprastų nuorodos tikslą ir nuspręstų, ar ją naudoti. [Sužinokite daugiau apie identiškas nuorodas](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identiškų nuorodų tikslas nėra tas pats."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Identiškų nuorodų tikslas yra tas pats."}, "core/audits/accessibility/image-alt.js | description": {"message": "Informaciniuose elementuose turėtų būti pateiktas trumpas, aprašomasis alternatyvus tekstas. Dekoratyvinių elementų galima nepaisyti nurodžius tuščią alternatyvų atributą. [Sužinokite daugiau apie atributą „`alt`“](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Vaizdo elementuose nėra atributų „`[alt]`“"}, "core/audits/accessibility/image-alt.js | title": {"message": "Vaizdo elementuose yra atributų „`[alt]`“"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Informaciniuose elementuose turėtų būti pateiktas trumpas, a<PERSON>šomasis alternatyvus tekstas. Alternatyvus tekstas, kuris tiksliai atitinka tekstą, esantį šalia nuorodos ar v<PERSON>, ekrano skaitytuvo naudotojams gali būti k<PERSON>, nes tekstas bus skaitomas du kartus. [Sužinokite daugiau apie atributą „`alt`“](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Vaizdo elementuose yra atributų „`[alt]`“, kurie yra nereikalingas tekstas."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Vaizdo elementuose nėra atributų „`[alt]`“, kurie yra nereikalingas tekstas."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Jei prie įvesties mygtukų pridėsite pastebimą ir pasiekiamą tekstą, ekrano skaitytuvo naudotojams bus lengviau suprasti įvesties mygtuko paskirtį. [Sužinokite daugiau apie įvesties mygtukus](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Prie įvesties mygtukų nėra pastebimo teksto."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Prie įvesties mygtukų yra pastebimas tekstas."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Kai vaizdas naudojamas kaip mygtukas „`<input>`“, pateikus alternatyvų tekstą ekrano skaitytuvo naudotojams bus lengviau suprasti mygtuko tikslą. [Sužinokite apie įvesties vaizdo alternatyvųjį tekstą](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementuose „`<input type=\"image\">`“ n<PERSON>ra „`[alt]`“ teksto"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementuose „`<input type=\"image\">`“ yra „`[alt]`“ teksto"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "<PERSON><PERSON><PERSON>, neatitinkan<PERSON><PERSON> pasiekiam<PERSON> pavadin<PERSON>, gali lemti klaid<PERSON>čią ekrano skaitytuvo naudotojų patirtį. [Sužinokite daugiau apie pasiekiamuosius pavadinimus](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Elementuose, kuriuose yra matomų teksto etikečių, nėra atitinkančių pasiekiamų pavadinimų."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Elementuose, kuriuose yra matomų teksto etikečių, yra atitinkančių pasiekiamų pavadinimų."}, "core/audits/accessibility/label.js | description": {"message": "Etiket<PERSON><PERSON>, kad <PERSON><PERSON><PERSON><PERSON> technologijos, pvz., e<PERSON>no <PERSON>, tinkamai praneštų apie formų valdiklius. [Sužinokite daugiau apie formų elementų etiketes](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Nėra susietų formos elementų etikečių"}, "core/audits/accessibility/label.js | title": {"message": "Formos elementuose yra atitinkamų etikečių"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Vienas pagrindinis orientyras padeda ekrano skaitytuvo naudotojams naršyti tinklalapį. [Sužinokite daugiau apie orientyrus](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokumente nėra pagrindinio orientyro."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokumente yra pagrindinis orientyras."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "<PERSON><PERSON><PERSON> kont<PERSON>to tekstą daugumai naudotojų yra sudėtinga arba neįmanoma perskaityti. Atskiriamas nuorodos tekstas pagerina patirtį sutrikusio regėjimo naudotojams. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip nuorodas padaryti atskiriamas](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Nuorodas galima atskirti pagal spalvą."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Nuorodas galima atskirti ne pagal spalvą."}, "core/audits/accessibility/link-name.js | description": {"message": "Aiškus ir unikalus nuorodos tekstas (ir alternatyvusis vaizdų tekstas, kai jie naudojami kaip nuorod<PERSON>), į kurį lengva sutelkti dėmesį, pagerina nar<PERSON>ymo patirtį ekrano skaitytuvų naudotojams. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip nuorodas padaryti pasiek<PERSON>](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Nėra aiškių nuorodų pavadinimų"}, "core/audits/accessibility/link-name.js | title": {"message": "Nuorodų pavadinimai aiškūs"}, "core/audits/accessibility/list.js | description": {"message": "Ekrano skaitytuvai apie sąrašus praneša tam tikru būdu. Tinkama sąrašo struktūra padeda ekrano skaitytuvams pateikti tinkamą išvestį. [Sužinokite daugiau apie tinkamą sąrašo struktūrą](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Sąrašuose patei<PERSON>ami ne tik elementai „`<li>`“ ir scenarijaus palaikymo elementai („`<script>`“ ir „`<template>`“)."}, "core/audits/accessibility/list.js | title": {"message": "Sąrašuose pateikiami tik elementai „`<li>`“ ir scenarijaus palaikymo elementai („`<script>`“ ir „`<template>`“)."}, "core/audits/accessibility/listitem.js | description": {"message": "Naudo<PERSON>t ekrano skaitytuvus reikia, kad sąrašo elementai („`<li>`“) būtų pirminiame elemente „`<ul>`“, „`<ol>`“ arba „`<menu>`, kad apie juos būtų galima tinkamai pranešti. [Sužinokite daugiau apie tinkamą sąrašo struktūrą](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Sąrašo elementų („`<li>`“) nėra pirminiuose elementuose „`<ul>`“, „`<ol>`“ arba „`<menu>`“."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> elementai („`<li>`“) yra pirminiuose elementuose „`<ul>`“, „`<ol>`“ arba „`<menu>`“"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Naudoto<PERSON><PERSON> nesiti<PERSON>, kad puslapis bus atnaujintas automatiškai. Tai atlikus dėmesys vėl sutelkiamas į puslapio viršų. Dėl to galima erzinanti arba klaidinanti patirtis. [Sužinokite daugiau apie atnaujinimo meta<PERSON>](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumente naudojama metažyma „`<meta http-equiv=\"refresh\">`“"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumente nenaudojama metažyma „`<meta http-equiv=\"refresh\">`“"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Išjungus mastelio keitimą gali kilti problemų sutrikusio regėjimo naudotojams, kurie padid<PERSON> e<PERSON>, kad tinkamai matytų tinklalapio turinį. [Sužinokite daugiau apie peržiūros srities metažymą](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Atributas „`[user-scalable=\"no\"]`“ naudojamas elemente „`<meta name=\"viewport\">`“ arba atributas „`[maximum-scale]`“ yra maž<PERSON> nei 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Atributas „`[user-scalable=\"no\"]`“ nenaudojamas elemente „`<meta name=\"viewport\">`“, o atributas „`[maximum-scale]`“ yra ne mažes<PERSON> nei 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Ekrano skaitytuvai negali išversti turinio, kuris nėra tekstas. Pridėję alternatyviojo teksto prie elementų „`<object>`“, padėsite ekrano skaitytuvams geriau perteikti reikšmę naudotojams. [Sužinokite daugiau apie „`object`“ elementų alternatyvųjį tekstą](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementuose „`<object>`“ nėra alternatyviojo teksto"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementuose „`<object>`“ yra alternatyviojo teksto"}, "core/audits/accessibility/select-name.js | description": {"message": "Dėl formos elementų be veiksmingų etikečių ekrano skaitytuvo naudotojams gali būti nepatogu naudotis funkcijomis. [Sužinokite daugiau apie elementą „`select`“](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Kai kuriuose elementuose nėra susietų etikečių elementų."}, "core/audits/accessibility/select-name.js | title": {"message": "Pasirinktuose elementuose yra susijusių etikečių elementų."}, "core/audits/accessibility/skip-link.js | description": {"message": "Įtraukę praleidimo nuorodą naudotojai gali pereiti prie pagrindinio turinio ir sutaupyti laiko. [Sužinokite daugiau apie praleidimo nuorodas](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Praleidimo nuorodų negalima suaktyvinti."}, "core/audits/accessibility/skip-link.js | title": {"message": "Praleidimo nuorodos yra suaktyvinamos."}, "core/audits/accessibility/tabindex.js | description": {"message": "Didesnė nei 0 vertė aiškiai nurodo naršymo tvarką. <PERSON><PERSON> tai yra tinkamas sprendimas, tačiau dažnai erzina pagalbinių technologijų naudotojus. [Sužinokite daugiau apie atributą „`tabindex`“](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Kai kurių elementų „`[tabindex]`“ vertė yra did<PERSON>nė nei 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nėra elemento su didesne nei 0 „`[tabindex]`“ verte"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Suvestinės atribute turėtų būti aprašyta lentelės struktūra, o „`<caption>`“ turėtų būti nurodytas ekrane pateiktas pavadinimas. Tikslus lentelės žymėjimas naudingas ekrano skaitytuvų naudotojams. [Sužinokite daugiau apie suvestinę ir antraštę](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Lentelėse yra tas pats turinys suvestin<PERSON>s atribute ir „`<caption>.`“"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Lentelėse yra kitoks turinys suvestinės atribute ir „`<caption>`“"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Ekrano skaitytuvuose naudoja<PERSON>, pad<PERSON><PERSON><PERSON><PERSON> lengviau naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad lentelėse naudojamas faktinis subtitrų elementas, o ne langeliai su atributu „`[colspan]`“, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau apie subtitrus.](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Lentelėse nenaudojamas elementas „`<caption>`“ vietoje langelių su atributu „`[colspan]`“ antraštei nurodyti."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Lentelė<PERSON> naudo<PERSON> „`<caption>`“ vietoje langelių su atributu „`[colspan]`“ antraštei nurodyti."}, "core/audits/accessibility/target-size.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> dydis ir tarpai yra <PERSON>, pad<PERSON>, kuri<PERSON> sudėting<PERSON> taikyti pagal ne<PERSON><PERSON> vald<PERSON>, su<PERSON><PERSON><PERSON><PERSON> taikymo viet<PERSON>. [Sužinokite daugiau apie jutiklines sritis](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Jutiklinės sritys arba jų tarpai nepakankamo d<PERSON>žio."}, "core/audits/accessibility/target-size.js | title": {"message": "Jutiklinių sričių ir jų tarpų dydis yra pakanka<PERSON>."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Ekrano skaitytuvuose naudo<PERSON>, pad<PERSON><PERSON><PERSON><PERSON> lengviau naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad elementai „`<td>`“ didel<PERSON><PERSON> lentel<PERSON> (trys ar daugiau langelių iš pločio ir aukščio) turi susietą lentelės antraštę, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau apie lentelių antraštes](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Elementuose „`<td>`<PERSON> did<PERSON><PERSON><PERSON> „`<table>`“ nėra lentelės ant<PERSON>ščių."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Elementuose „`<td>`<PERSON> did<PERSON><PERSON><PERSON> „`<table>`“ yra viena ar daugiau lentel<PERSON>."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Ekrano skaitytuvuose naudojamos <PERSON>, pad<PERSON><PERSON><PERSON><PERSON> lengviau naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad langeliai „`<td>`“, kuriuose naudojamas atributas „`[headers]`“, nurodo tik langelius toje pa<PERSON> lent<PERSON>, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau apie atributą „`headers`“](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Langeliai elemente „`<table>`“, kuriuose naudojamas atributas „`[headers]`“, nurodo elementą „`id`“, nerastą toje pačioje <PERSON>."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Langeliai elemente „`<table>`“, kuriuose naudojamas atributas „`[headers]`“, nuro<PERSON> lent<PERSON> la<PERSON>, es<PERSON><PERSON><PERSON> toje pač<PERSON> lent<PERSON>."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "<PERSON>krano skaitytuvuose naudo<PERSON>, pad<PERSON><PERSON><PERSON><PERSON> lengviau naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad lentelės antraštės visada nurodo tam tikrą langelių rinkinį, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau apie lentelių antraštes](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementuose „`<th>`“ ir elementuose su atributu „`[role=\"columnheader\"/\"rowheader\"]`“ nėra duomenų langelių, kuriuos jie a<PERSON>."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementuose „`<th>`“ ir elementuose su atributu „`[role=\"columnheader\"/\"rowheader\"]`“ yra duomenų langelių, kuriuos jie a<PERSON>."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Nurodžius tinkamą elementų [BCP 47 kalbą](https://www.w3.org/International/questions/qa-choosing-language-tags#question), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad ekrano skaitytuvas tinkamai ištars tekstą. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip naudoti atributą „`lang`“](https://dequeuniversity.com/rules/axe/4.8/valid-lang),"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributuose „`[lang]`“ nėra tinkamos vertės"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributų „`[lang]`“ vert<PERSON> tin<PERSON>ma"}, "core/audits/accessibility/video-caption.js | description": {"message": "Kai pateikiama vaizdo įrašo antraštė, kurtie<PERSON> ir sutrikusios klausos naudotojams lengviau suprasti pateikiamą informaciją. [Sužinokite daugiau apie vaizdo įrašų antraštes](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementuose „`<video>`“ nėra elemento „`<track>`“ su atributu „`[kind=\"captions\"]`“."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementuose „`<video>`“ yra elementas „`<track>`“ su atributu „`[kind=\"captions\"]`“"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON><PERSON><PERSON> vertė"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON> prieigos raktas"}, "core/audits/autocomplete.js | description": {"message": "Funkcija „`autocomplete`“ padeda naudotojams greičiau pateikti formas. Kad naudotojams būtų paprasčiau, apsvarstykite galimybę įgalinti šią funkciją nustatydami tinkamą atributo „`autocomplete`“ vertę. [Sužinokite daugiau apie „`autocomplete`“ formose](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "<PERSON> kuriuose elementuose (`<input>`) nėra tinkamų atributų „`autocomplete`“"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON>ling<PERSON> neautomatin<PERSON>"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Prieigos raktų išdėstymo tvarkos peržiūra"}, "core/audits/autocomplete.js | title": {"message": "Elementuose „`<input>`“ atributas „`autocomplete`“ naudo<PERSON><PERSON>i"}, "core/audits/autocomplete.js | warningInvalid": {"message": "<PERSON><PERSON><PERSON>s raktas (-ai) „`autocomplete`“: „{token}“ yra netinkamas sistemoje {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Peržiūrėkite prieigos raktų išdėstymo tvarką: „{tokens}“ sistemoje {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Reikia imtis ve<PERSON>smų"}, "core/audits/bf-cache.js | description": {"message": "Daug naršymo veiksmų atliekama grįžus į ankstesnį arba perėjus į tolesnį puslapį. Ilgalaikis viso puslapio saugojimas talpykloje (bfcache) gali padėti greičiau naršyti. [Sužinokite daugiau apie ilgalaikį viso puslapio saugojimą talpykloje](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 trikties priežastis}one{# trikties priežastis}few{# trikties priežastys}many{# trikties priežasties}other{# trikties priežasčių}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Trikties priežastis"}, "core/audits/bf-cache.js | failureTitle": {"message": "Puslapiui neleidžiama atkurti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Trikties tipas"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Negalima išspręsti"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Laukiama narš<PERSON><PERSON> p<PERSON>"}, "core/audits/bf-cache.js | title": {"message": "Puslapiui nepavyko atkurti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcijos negalima išbandyti naudojant seną „Chrome“ be grafinės naudotojo sąsajos (`--chrome-flags=\"--headless=old\"`). <PERSON><PERSON><PERSON><PERSON> per<PERSON><PERSON> audito rezultatus, naudokite naują „Chrome“ be grafinės naudotojo sąsajos (`--chrome-flags=\"--headless=new\"`) arba įprastą „Chrome“."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "„Chrome“ plėtiniai neigiamai paveikė šio puslapio įkėlimo našumą. Pabandykite patikrinti puslapį inkognito režimu arba naudodami „Chrome“ profilį be plėtinių."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Scenarijaus įvertinimas"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Scenarijaus analiz<PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Bendras centrinio procesoriaus laikas"}, "core/audits/bootup-time.js | description": {"message": "Apsvarstykite galimybę sutrumpinti JS analizuoti, kompiliuoti ir vykdyti skiriamą laiką. Mažesnės JS naudingosios apkrovos gali padėti tai padaryti. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sutrumpinti „JavaScript“ vykdymo laiką](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Sutrumpinkite „JavaScript“ vykdymo laik<PERSON>"}, "core/audits/bootup-time.js | title": {"message": "„JavaScript“ vykdymo laikas"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> did<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „JavaScript“ modulius iš grupių, kad tinklo veikla be reikalo nenaudotų baitų. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Pašalinkite tikslias modulių kopijas „JavaScript“ grupėse"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Dideli GIF failai nėra efektyvus animuoto turinio pat<PERSON> b<PERSON>. Kad sutaupytumėte tinklo baitų, vietoje GIF failų galite naudoti MPEG4 ar „WebM“ vaizdo įrašų animacijai ir PNG ar „WebP“ statiniams vaizdams pateikti. [Sužinokite daugiau apie efektyvius vaizdo įrašų formatus](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Naudokite vaizdo įrašo formatus animuotam turiniui pateikti"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Naudojant kodus ir transformacijas pasenusiose naršyklėse galima naudoti naujas „JavaScript“ funkcijas. Tačiau dauguma jų nereikalingi modernioms naršyklėms. Į grupę įtrauktai „JavaScript“ pritaikykite modernią scenarijaus diegimo strategiją naudodami modulio ir (arba) ne modulio funkcijos aptikimą, kad sumažintumėte į modernias naršykles siunčiamo kodo kiekį, išlaikydami pasenusių naršyklių palaikymą. [Sužinokite, kaip naudoti modernią „JavaScript“](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Neteikite pasenusios „JavaScript“ modernioms naršyklėms"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Tokių formatų kaip „WebP“ ir AVIF vaizdai dažniausiai glaudinami geriau nei PNG ar JPEG vaizdai, tod<PERSON>l yra atsisiunčiami greičiau ir sunaudoja mažiau duomenų. [Sužinokite daugiau apie modernius vaizdų formatus](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Pateikite naujos kartos formatų vaizdus"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Apsvarstykite galimybę įkelti ne ekraninius ir paslėptus vaizdus tik tada, kai bus įkelti visi svarbiausi ištekliai, kad sutrump<PERSON>tų laikas iki sąveikos. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip atidėti ne ekraninius vaizdus](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Atidėkite ne ekraninius vaizdus"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Šaltiniai blokuoja puslapio pirmą žymėjimą. Apsvarstykite galimybę pateikti svarbiausius JS ar CSS kaip eilutinius elementus ir atidėti visus nesvarbius JS ar stilius. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip pašalinti pateikim<PERSON> blokuojanč<PERSON> i<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Pašalinkite pateikim<PERSON> blo<PERSON><PERSON><PERSON> i<PERSON>"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Naudotojai turi mokėti už <PERSON>eles tinklo na<PERSON>, be to, jos glaud<PERSON> susijusios su ilgu įkėlimo laiku. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sumažinti naudingąją apkrovą](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Bendras dydis: {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Išvenkite didelių tinklo naudingųjų apkrovų"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Išvengiama didelių tinklo naudingųjų apkrovų"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Sumažinus CSS failų dydį, gali sumažėti tinklo naudingosios apkrovos. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sumažinti CSS dydį](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Sumažinkite CSS failus"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failus, galima sumažinti naudingąsias apkrovas ir sutrumpinti scenarijaus analizavimo laiką. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sumažinti „JavaScript“ failus](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Sumažinkite „JavaScript“"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Sumažinkite nenaudojamas taisykles iš stiliaus aprašų ir atidėkite CSS scenarijų, nenaudojamų turiniui virš <PERSON>, įkėlimą, kad tinklo veikla sunaudotų mažiau baitų. [Su<PERSON><PERSON>kite, kaip sumažinti nenaudojamą CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Sumažinkite nenaudojamą CSS turinį"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Sumažinkite nenaudojamą „JavaScript“ turinį ir atidėkite scenarijų įkėlimą, kol jų prireiks, kad tinklo veikla sunaudotų mažiau baitų. [Sužinokite, kaip sumažinti nenaudojamą „JavaScript“ turinį](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Sumažinkite nenaudojamą „JavaScript“ turinį"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Jei talpykla galios ilgiau, greičiau sulauksite pakartotinių apsilankymų puslapyje. [Sužinokite daugiau apie efektyvią talpyklos politiką](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>sta<PERSON> 1 i<PERSON><PERSON><PERSON><PERSON>}one{<PERSON>sta<PERSON> # i<PERSON><PERSON><PERSON><PERSON>}few{Rasti # ištekliai}many{Rasta # ištekliaus}other{Rasta # išteklių}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statiniams ištekliams taikykite efektyvią talpyklos politiką"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Naudojama efektyvi statinių išteklių talpyklos politika"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizuoti vaizdai įkeliami greičiau ir sunaudoja mažiau mobiliojo ryšio duomenų. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip efektyviai koduoti vaizdus](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efektyviai koduokite vaizdus"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Vaizdai buvo didesni nei nurodyti dydžiai"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> atitiko nurody<PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Teikite tinkamo d<PERSON> v<PERSON>, kad būtų taupomi mobiliojo ryšio duomenys ir puslapis būtų įkeliamas greičiau. [<PERSON><PERSON><PERSON>kite, kaip nustatyti vaizdų dydį](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Pasirinkite tinkamo d<PERSON>ž<PERSON> v<PERSON>dus"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> re<PERSON> suglaudin<PERSON> (naudo<PERSON><PERSON> „Gzip“, „Deflate“ arba „Brot<PERSON>“), kad bendrai būtų sunaudojama kuo mažiau tinklo baitų. [Sužinokite daugiau apie teksto glaudin<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Įgalinkite teksto glaudin<PERSON>"}, "core/audits/content-width.js | description": {"message": "Jei programos turinio plotis nesutampa su peržiūros srities pločiu, jūsų programa gali būti neoptimizuota mobiliųjų įrenginių ekranams. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip nustatyti peržiūros srities turinio dydį](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "{innerWidth} tšk. per<PERSON><PERSON><PERSON><PERSON> sritis neatitinka {outerWidth} tšk. lango dydž<PERSON>."}, "core/audits/content-width.js | failureTitle": {"message": "Turinys nėra tinkamo d<PERSON>č<PERSON>"}, "core/audits/content-width.js | title": {"message": "Turinys yra tin<PERSON>mo d<PERSON>"}, "core/audits/critical-request-chains.js | description": {"message": "Toliau pateiktose svarbiausių užklausų grandinėse nurodoma, kurie ištekliai įkelti nurodant aukštą prioritetą. Kad puslapio įkėlimas būt<PERSON> sklandes<PERSON>, apsvarstykite galimybę sutrumpinti grandines, sumažinti atsisiunčiamų išteklių dydį arba atidėti nebūtinų išteklių atsisiuntimą. [Sužinokite, kaip išvengti svarbiausių užklausų grandinių](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 grandinė}one{Rasta # grandinė}few{Rastos # grandinės}many{Rasta # grandinės}other{Rasta # grandinių}}"}, "core/audits/critical-request-chains.js | title": {"message": "Nekurkite svarbių užklausų grandinių"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktyva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "Griežta turinio apsaugos politika (CSP) gerokai sumažina svetainių scenarijų (XSS) atakų riziką. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip naudoti CSP, kad išvengtumėte XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaksė"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Puslapyje yra CSP, apibrėžta žymoje „`<meta>`“. Apsvarstykite galimybę perkelti CSP į HTTP antraštę arba apibrėžti kitą griežtą CSP HTTP antraštėje."}, "core/audits/csp-xss.js | noCsp": {"message": "Nerasta jokios vykdomos CSP"}, "core/audits/csp-xss.js | title": {"message": "Įsitikinkite, kad CSP efektyviai saugo nuo XSS atakų"}, "core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON> nutraukimas / įspėjimas"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Nebenaudojamos API galiausiai bus pašalintos i<PERSON>. [Sužinokite daugiau apie API, kurių teikimas nutrauktas](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Rastas 1 įspėjimas}one{Rastas # įspėjimas}few{<PERSON><PERSON><PERSON> # įspėjimai}many{Rasta # įspėjimo}other{Rasta # įspėjimų}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Naudojamos p<PERSON> API"}, "core/audits/deprecations.js | title": {"message": "Vengiama nebenaudojamų API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Reikalinga ženklų koduotės deklaracija. Tai galima atlikti naudojant žymą „`<meta>`“ pirmuose 1 024 HTML baituose arba turinio tipo HTTP atsako antraštėje. [Sužinokite daugiau apie ženklų koduotės deklaravimą](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Nėra simbolių rinkinio deklaracijos arba HTML ji pateikiama per vėlai"}, "core/audits/dobetterweb/charset.js | title": {"message": "Tinkamai apibrėžia simbolių rinkinį"}, "core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> DOCTYPE, na<PERSON><PERSON><PERSON><PERSON>ė neperjungia į seno standarto tinklalapių palaikymo režimą. [Sužinokite daugiau apie DOCTYPE deklaraciją](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE pavadinimas turi būti e<PERSON>ut<PERSON> „`html`“"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokumente yra „`doctype`“, <PERSON><PERSON><PERSON> „`limited-quirks-mode`“"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumente turi būti nurodytas DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Laukas „publicId“ turėtų būti tušč<PERSON>"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Laukas „systemId“ turėtų būti tušč<PERSON>"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokumente yra „`doctype`“, k<PERSON><PERSON> „`quirks-mode`“"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Puslapyje trūksta HTML DOCTYPE, todėl aktyvinamas seno standarto tinklalapių palaikymo režimas"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Puslapyje yra HTML DOCTYPE"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vertė"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Dėl didelio DOM elementų skaičiaus bus sunaudojama daugiau atminties, ilgiau [skaičiu<PERSON><PERSON> stiliai](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ir gali reikėti brangi<PERSON> [išdėsty<PERSON> perskaičiavimų](https://developers.google.com/speed/articles/reflow). [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išvengti per didelio DOM dydžio](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elementas}one{# elementas}few{# elementai}many{# elemento}other{# elementų}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Venkite per didelių DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimalus D<PERSON> gylis"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Bendras DOM elementų skaičius"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksimalus antrinių elementų s<PERSON>čius"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Išvengiama per didelių DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Naudotojai gali būti įtarūs arba sumišę, jei svet<PERSON> bus prašoma suteikti leidimą pasiekti vietovės duomenis be jokio konte<PERSON>. Vietoj to apsvarstykite galimybę susieti užklausą su naudotojų veiksmu. [Sužinokite daugiau apie leidimą nustatyti geografinę vietovę](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Įkeliant puslapį pateikiama užklausa dėl pranešimų leidimo"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Įkeliant puslapį vengiama pateikti užklausą dėl leidimo nustatyti geografinę vietovę"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Problemos tipas"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "<PERSON><PERSON>, užregis<PERSON><PERSON><PERSON> skydelyje „`Issues`“ naudojant „Chrome“ kūrėjo įrankius, parodo, kad yra neišspręstų problemų. Jos gal<PERSON> kilti nepavykus pateikti tinklo už<PERSON>, dėl nepakankamų saugos valdiklių arba dėl kitų naršyklės problemų. Jei reikia daugiau informacijos apie kiekvieną problemą, atidarykite problemų skydelį naudodami „Chrome“ kūrėjo įrankius."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problemos užregistruotos skydelyje „`Issues`“ naudojant „Chrome“ kūrėjo įrankius"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Užblokuota pagal kelių šaltinių politiką"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Skelbimai naudoja daug išteklių"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Skydelyje „`Issues`“ naudojant „Chrome“ kūrėjo įrankius problemų nėra"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Puslapyje aptiktos visos „JavaScript“ sąsajos bibliotekos. [Sužinokite daugiau apie šį „JavaScript“ bibliotekos aptikimo diagnostikos auditą](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Aptikta „JavaScript“ bibliotekų"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON> l<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>nar<PERSON>, din<PERSON><PERSON><PERSON> įterpiami naudojant „`document.write()`“, gali atidėti puslapio įkėlimą dešimtimis sekundžių. [<PERSON><PERSON><PERSON><PERSON>te, ka<PERSON> i<PERSON>i „document.write()“](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "„`document.write()`“ vengimas"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> naudo<PERSON> „`document.write()`“"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Naudotojai gali būti įtarūs arba sumišę, jei svetain<PERSON> bus prašoma suteikti pranešimų leidimą be jokio konteks<PERSON>. Vietoj to apsvarstykite galimybę susieti užklausą su naudotojų gestais. [Sužinokite daugiau apie tai, kaip atsakingai gauti pranešimų leidimus](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Įkeliant puslapį pateikiama užklausa dėl pranešimų leidimo"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Įkeliant puslapį vengiama pateikti užklausą dėl p<PERSON>š<PERSON> leidimo"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Neleidimas įklijuoti įvesties yra netinkama NS praktika ir silpn<PERSON>ja sauga, blo<PERSON><PERSON>nt slaptažodžių tvarkykles.[Sužinokite daugiau apie patogius įvesties laukus](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Naudotojams nele<PERSON>ž<PERSON> įklijuoti turinio į įvesties laukus"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Naudotojams leidžiama įklijuoti turinį į įvesties laukus"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokolas"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Naudodami HTTP/2 gaunate daugiau privalumų, nei naudodami HTTP/1.1, įskaitant dvigubas antraštes ir tankinimą. [Sužinokite daugiau apie HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 užklausa nepateikta naudojant HTTP/2}one{# užklausa nepateikta naudojant HTTP/2}few{# užklausos nepateiktos naudojant HTTP/2}many{# užklausos nepateikta naudojant HTTP/2}other{# užklausų nepateikta naudojant HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Naudokite HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Apsvarstykite galimybę pažymėti lietimo ir pelės ratuko sukimo įvykių apdorojimo priemones kaip „`passive`“, kad pagerintum<PERSON>te puslapio slinkimo našumą. [Sužinokite daugiau apie pasyvių įvykių apdorojimo priemonių pritaikymą](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nenaudojamos pasyvios apdorojimo priemonės siekiant pagerinti slink<PERSON>"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Naudojamos pasyvios apdorojimo priemonės siekiant pagerinti slink<PERSON>"}, "core/audits/errors-in-console.js | description": {"message": "Pulte užregistruotos klaid<PERSON> n<PERSON>, kad esama neišspręstų problemų. Jos gal<PERSON> kilti nepavykus pateikti tinklo užklausų arba dėl kitų naršyklės problemų. [Sužinokite daugiau apie šias klaidas pulto diagnostikos audito skiltyje](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klaidos registruojamos pulte"}, "core/audits/errors-in-console.js | title": {"message": "Pulte neužregistruota naršyklės klaidų"}, "core/audits/font-display.js | description": {"message": "Pasinaudokite CSS funkcija „`font-display`“, kad tekstas būtų matoma<PERSON>, kol įkeliami žiniatinklio šriftai. [Su<PERSON><PERSON><PERSON> daugiau apie „`font-display`“](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad įkeliant žiniatinklio šriftą būtų matomas te<PERSON>tas"}, "core/audits/font-display.js | title": {"message": "Įkeliant žiniatinklio šriftą matomas visas tekstas"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{„Lighthouse“ nepavyko automatiškai patikrinti `font-display` vertės (pradinis URL: {fontOrigin}).}one{„Lighthouse“ nepavyko automatiškai patikrinti `font-display` vertės (pradinis URL: {fontOrigin}).}few{„Lighthouse“ nepavyko automatiškai patikrinti `font-display` verčių (pradinis URL: {fontOrigin}).}many{„Lighthouse“ nepavyko automatiškai patikrinti `font-display` vertės (pradinis URL: {fontOrigin}).}other{„Lighthouse“ nepavyko automatiškai patikrinti `font-display` verčių (pradinis URL: {fontOrigin}).}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Formato <PERSON> (faktinis)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (rodomas)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Rodomo vaizdo matmenys turi atitikti natūralų kraštinių santykį. [Sužinokite daugiau apie vaizdo kraštinių santykį](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Rodomi vaizdai su netinkamu formato koeficientu"}, "core/audits/image-aspect-ratio.js | title": {"message": "Rod<PERSON> vaizdai su tinkamu formato koeficientu"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Pateikto vaizdo d<PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON><PERSON> dydis"}, "core/audits/image-size-responsive.js | description": {"message": "<PERSON>d vaizdas būtų kuo <PERSON>, nat<PERSON><PERSON><PERSON><PERSON> vaizdo aspektai turi būti proporcingi ekrano dydžiui ir taškų koeficientui. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip pateikti interaktyvius vaizdus](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Teikiami žemos raiškos vaizdai"}, "core/audits/image-size-responsive.js | title": {"message": "Teikiami tinkamos raiškos vaizdai"}, "core/audits/installable-manifest.js | already-installed": {"message": "Programa jau įdiegta"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Nepavyko atsisiųsti būtinos piktogramos iš a<PERSON>o"}, "core/audits/installable-manifest.js | columnValue": {"message": "Trikties priežastis"}, "core/audits/installable-manifest.js | description": {"message": "Pa<PERSON><PERSON><PERSON> „JavaScript“ failas – tai technologija, įgalinanti jūsų programą naudoti daug laipsniškosios žiniatinklio programos funkcijų, pvz., naudoti neprisijungus, pridėti prie pagrindinio ekrano ar naudoti iš karto gaunamus pranešimus. Pasirinkus tinkamą pagalbinį „JavaScript“ failą ir aprašą, naršyklės gali aktyviai raginti naudotojus pridėti jūsų programą prie pagrindinio ekrano, tai paskatintų geresnį įtraukimą. [Sužinokite daugiau apie aprašo diegiamumo reikalavimus](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{Viena priežastis}one{# priežastis}few{# priežastys}many{# priežasties}other{# priežasčių}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Žiniatinklio programos aprašas arba pagalbinis „JavaScript“ failas neatitinka diegiamumo reikalavimų."}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Neatitinka „Play“ parduotuvės programos URL ir „Play“ parduotuvės ID"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Puslapis įkeliamas inkognito lange"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "<PERSON>a<PERSON><PERSON> „`display`“ nuosavy<PERSON><PERSON> turi būti viena iš „`standalone`“, „`fullscreen`“ arba „`minimal-ui`“"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "<PERSON>a<PERSON><PERSON> yra la<PERSON> „display_override“ ir pirmas palaikomas rodymo re<PERSON> turi būti „standalone“, „fullscreen“ arba „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "<PERSON><PERSON><PERSON><PERSON>, yra tu<PERSON><PERSON> arba nepavyko jo išanalizuoti"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Gaunant aprašą pasikeitė jo URL."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "<PERSON><PERSON><PERSON><PERSON> „`name`“ arba „`short_name`“"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Apraše nėra tinkamos piktogramos – reikia PNG, SVG ar „WebP“ formato, ne mažiau kaip {value0} tšk., turi būti nustatytas atributas „sizes“, o jei nustatytas atributas „purpose“, jis turi apimti parinktį „any“."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Neteikiama piktograma yra ne ma<PERSON> nei {value0} tšk. PNG, SVG ar „WebP“ formato kvadratas, kurio paskirties atributas nenustatytas arba nustatytas kaip „any“."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Atsisiųsta piktograma buvo tuščia arba sugadinta"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nepateiktas „Play“ parduotuvės ID"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Puslapyje nėra aprašo <link> URL"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> „JavaScript“ failo, nes apraše nėra lauk<PERSON> „start_url“"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Diegiamumo klaidos ID „{errorId}“ neatpažintas"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> i<PERSON>"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Puslapis neįkeliamas pagrindiniame kadre"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "<PERSON><PERSON><PERSON><PERSON> neveikia nep<PERSON>"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "LŽP pašalinta ir diegimo patikros nustatomos iš naujo."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Nurodyta programų platforma nepalaikoma sistemoje „Android“"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Apraše nurodoma „prefer_related_applications: true“"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "„prefer_related_applications“ palaikoma tik beta versijos „Chrome“ ir pastoviuose kanaluose sistemoje „Android“."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "„Lighthouse“ nepavyko nustatyti, ar puslapį galima įdiegti. Bandykite naudodami naujesnės versijos „Chrome“."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Aprašo pradžios URL netinkamas"}, "core/audits/installable-manifest.js | title": {"message": "Žiniatinklio programos aprašas ir paga<PERSON>binis „JavaScript“ failas atitinka diegiamumo reikalavimus"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Aprašo URL yra naudo<PERSON>, slap<PERSON>žod<PERSON> arba prievadas"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Puslapis neveikia neprisijungus. 2021 m. rugpjūtį išleidus stabilią „Chrome“ 93 versiją puslapis nebus laikomas tinkamu diegti."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Užblokuota"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nesaugus URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Užklausos sprendimas"}, "core/audits/is-on-https.js | description": {"message": "Visos svetainės tur<PERSON> būti apsaugotos naudojant HTTPS, net ir tos, kuriose nėra neskelbtinų duomenų. Reikėtų vengti [įvairaus turinio](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), kai tam tikri šaltiniai įkeliami naudojant HTTP, nors pradinė užklausa pateikiama naudojant HTTPS. Naudojant HTTPS įsibrovėliams neleidžiama gadinti ar pasyviai klausytis jūsų programos ir jos naudotojų komunikacijos, be to, jį privaloma naudoti HTTP/2 ir daugelyje naujų žiniatinklio platformų API. [Sužinokite daugiau apie HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 nesaugi užklausa}one{Rasta # nesaugi užklausa}few{Rastos # nesaugios užklausos}many{Rasta # nesaugos užklausos}other{Rasta # nesaugių užklausų}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Nenaudojamas HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Naudojamas HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatiškai naujovinta į HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Leidžiama su įspėjimu"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "DTŽ proc."}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Fazė"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "<PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Tai didžiausias turiningas elementas, pa<PERSON><PERSON>ėtas peržiūros srityje. [Sužinokite daugiau apie didžiausio turiningo žymėjimo elementą](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Įkėlimo delsa"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Įkėlimo laikas"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Pateikimo delsa"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "LPB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Didžiausio turiningo žymėjimo elementas"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Išdėstymo poslinkio poveikio"}, "core/audits/layout-shift-elements.js | description": {"message": "Šiems DOM elementams didžiausią poveikį turėjo išdėstymo poslinkiai. Kai kurie išdėstymo poslinkiai gali būti neįtraukti į KIP metrikos vertę dėl [patei<PERSON>o lange](https://web.dev/articles/cls#what_is_cls). [Sužinokite, kaip pagerinti KIP](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Venkite didelių išdėstymo poslinkių"}, "core/audits/layout-shifts.js | columnScore": {"message": "Išdėstymo poslinkio rezultatas"}, "core/audits/layout-shifts.js | description": {"message": "Tai yra didžiausi puslapyje pastebėti išdėstymo poslinkiai. Kiekvienas lentelės elementas atspindi vieną išdėstymo poslinkį ir rodo labiausiai pasislinkusį elementą. Po kiekvienu elementu pateikiamos galimos pagrindin<PERSON> prie<PERSON>, lėmusios išdėstymo poslinkį. Kai kurie iš šių išdėstymo poslinkių gali būti neįtraukti į KIP metrikos vertę dėl [pateikimo lange](https://web.dev/articles/cls#what_is_cls). [Sužinokite, kaip pagerinti KIP](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{<PERSON><PERSON><PERSON> vienas išdė<PERSON> poslinkis}one{Rastas # išdėstymo poslinkis}few{Rasti # išdėstymo poslinkiai}many{Rasta # išdėstymo poslinkio}other{Rasta # išdėstymo poslinkių}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Žiniatinklio šriftas įkeltas"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Įterptas „iframe“"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Pavėluota tinklo užklausa pakoregavo puslapio išdėstymą"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Trūksta aiškaus medijos elemento dydžio"}, "core/audits/layout-shifts.js | title": {"message": "Venkite didelių išdėstymo poslinkių"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "<PERSON><PERSON><PERSON> rib<PERSON> es<PERSON> v<PERSON>, kuri<PERSON> įkeliami as<PERSON><PERSON>ron<PERSON>š<PERSON>, patei<PERSON>ami pu<PERSON>pio veik<PERSON> cik<PERSON> v<PERSON>, o tai gali lemti didžiausio turiningo žymėjimo delsą. [Sužinokite daugiau apie optimalų asinchroninį įkėlimą](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Didžiausio turiningo žymėjimo vaizdas įkeltas asinchroniškai"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Didžiausio turiningo žymėjimo vaizdas nebuvo įkeltas asinchroniškai"}, "core/audits/long-tasks.js | description": {"message": "<PERSON><PERSON><PERSON>s il<PERSON> pagrindinės grupės uždu<PERSON>č<PERSON> s<PERSON>, na<PERSON><PERSON> siek<PERSON> nustatyti, kas labiausiai prisideda prie įvesties delsos. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išvengti ilgų pagrindinės grupės užduočių](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Rasta # ilga užduotis}one{Rasta # ilga užduotis}few{Rastos # ilgos užduotys}many{Rasta # ilgos užduoties}other{Rasta # ilgų užduočių}}"}, "core/audits/long-tasks.js | title": {"message": "Venkite ilgų pagrindinės grupės užduočių"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Apsvarstykite galimybę sutrumpinti JS analizuoti, kompiliuoti ir vykdyti skiriamą laiką. Mažesnės JS naudingosios apkrovos gali padėti tai padaryti. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sumažinti pagrindinės grupės veikimą](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Pagrindinės grup<PERSON>s ve<PERSON> sutr<PERSON>"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Su<PERSON><PERSON><PERSON><PERSON> pag<PERSON> grup<PERSON>"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "<PERSON><PERSON> pasi<PERSON>ti daugiausia naudotojų, s<PERSON><PERSON><PERSON><PERSON> turi veikti visose svarbiausiose naršyklėse. [Sužinokite apie suderinamumą skirtingose naršyklėse](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>a skirtingose na<PERSON>š<PERSON>ė<PERSON>"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad atskiri puslapiai būtų susiejami giliąja nuoroda naudojant URL ir kad URL yra unikalūs pagal bendrinimo visuomeninėje medijoje tikslą. [Sužinokite daugiau apie giliųjų nuorodų pateikimą](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Kiekvienas puslapis turi URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON><PERSON>tus e<PERSON> perėjimai turi būti s<PERSON>, net esant lėtam interneto ryšiui. Tai labai svarbu, kad naudotojas tinkamai suvoktų našumą. [Sužinokite daugiau apie puslapių perėjimus](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad pu<PERSON>i būtų blokuojami tinklo"}, "core/audits/maskable-icon.js | description": {"message": "Maskuojama piktograma užtikrina, kad vaizdas užpildytų visą formą netaikant pašto dėžut<PERSON> efekto, įdiegiant programą įrenginyje. [Sužinokite apie maskuojamas aprašo piktogramas](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Nėra maskuojamos aprašo piktogramos"}, "core/audits/maskable-icon.js | title": {"message": "Pateikta maskuojama aprašo piktograma"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Nustatant kaupiamąjį išdėstymo poslinkį įvertinamas peržiūros srityje matomų elementų judėjimas. [Sužinokite daugiau apie kaupiamojo išdėstymo poslinkio metriką](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Pirmas turiningas žym<PERSON> nuro<PERSON>, kada pažymimas pirmasis tekstas ar vaizdas. [Sužinokite daugiau apie pirmo turiningo žymėjimo metriką](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON> reikšmingas parodymas nurodo, kada parodomas pagrindinis puslapio turinys. [Sužinokite daugiau apie pirmojo reikšmingo parodymo metriką](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Laikas nuo sąveikos iki kito žymėjimo įvertina puslapio atsako laiką: kiek laiko praeina, kol matomas puslapio atsakas į naudotojo įvestį. [Sužinokite daugiau apie laiką nuo sąveikos iki kito žymėjimo](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "Laikas iki sąveikos yra vert<PERSON>, <PERSON><PERSON><PERSON><PERSON>, kiek laiko reiki<PERSON>, kol puslapis tampa visiškai interaktyvus. [Sužinokite daugiau apie laiko iki sąveikos metriką](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Didžiausias turiningas žymėjimas nurodo laik<PERSON>, kada pažymimas didžiausias tekstas ar vaizdas. [Sužinokite daugiau apie didžiausio turiningo žymėjimo metriką](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Didžiausia potenciali naudotojų patiriama pirmosios įvesties delsa yra ilgiausios užduoties trukmė. [Sužinokite daugiau apie didžiausios potencialios pirmosios įvesties delsos metriką](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rodik<PERSON> parodo, kaip greitai pavaiz<PERSON><PERSON> puslapio turinys. [Sužinokite daugiau apie spartos rodik<PERSON> metrik<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Visų laikotarpių tarp PTŽ ir laiko iki sąveikos suma milisekundėmis, kai užduoties atlikimo laikas viršija 50 ms. [Sužinokite daugiau apie bendro blokavimo laiko metrik<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Abipus<PERSON> tinklo laika<PERSON> (RTT) turi didel<PERSON> įtakos našumui. Didelė RTT į šaltinį vertė nurodo, kad arčiau naudotojo esantys serveriai galėtų padidinti našumą. [Sužinokite daugiau apie abipusį tinklo laiką](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON>"}, "core/audits/network-server-latency.js | description": {"message": "Serverio delsa gali turėti įtakos žiniatinklio našumui. Didelė šaltinio serverio delsa nurodo, kad serveris per daug apkrautas arba mažas vidinės pusės našumas. [Sužinokite daugiau apie serverio atsako laiką](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON> vid<PERSON> pu<PERSON>"}, "core/audits/no-unload-listeners.js | description": {"message": "Įvykis „`unload`“ patikimai nesuaktyvinamas ir jį apdorojant gali būti nele<PERSON>a optimizuoti narš<PERSON>, pvz., persiuntimo atgal ir pirmyn talpyklos. Vietoj to naudokite įvykį „`pagehide`“ arba „`visibilitychange`“. [Sužinokite daugiau apie įvykių apdorojimo priemonių iškėlimą](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Užregis<PERSON><PERSON><PERSON><PERSON> „`unload`“ apdorojimo priemonė"}, "core/audits/no-unload-listeners.js | title": {"message": "Vengiama įvykio „`unload`“ apdorojimo priemonių"}, "core/audits/non-composited-animations.js | description": {"message": "Nesudėtinės animacijos gali būti prastos kokybės ir dėl jų gali padidėti KIP. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išvengti nesudėtinių animacijų](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Rastas # animuotas elementas}one{Rastas # animuotas elementas}few{Rasti # animuoti elementai}many{Rasta # animuoto elemento}other{Rasta # animuotų elementų}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Su filtrais susijusi nuosavybė gali perkelti taškus"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Elemente yra kita animacija, kuri yra nesuderinama"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Nustatytas sudėtinis efekto režimas, kuris n<PERSON> „replace“"}, "core/audits/non-composited-animations.js | title": {"message": "Venkite nesudėtinių animacijų"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Su transformavimu susijusi nuosavybė priklauso nuo laukelio d<PERSON>"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepalaikoma CSS nuosavybė: {properties}}one{Nepalaikomos CSS nuosavybės: {properties}}few{Nepalaikomos CSS nuosavybės: {properties}}many{Nepalaikomos CSS nuosavybės: {properties}}other{Nepalaikomos CSS nuosavybės: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekto laiko parametrai nepalaikomi"}, "core/audits/performance-budget.js | description": {"message": "Tinklo u<PERSON>ų kiekis ir dydis neturi viršyti tikslinių verčių, nustatytų našumo biudžete. [Sužinokite daugiau apie našumo biudžetus](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 užklausa}one{# užklausa}few{# užklausos}many{# užklausos}other{# užklausų}}"}, "core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/preload-fonts.js | description": {"message": "<PERSON><PERSON> įkelkite „`optional`“ šriftus, kad pirmą kartą apsilankantieji galėtų juos naudoti. [Sužinokite daugiau apie išankstinį šriftų įkėlimą](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Šriftai su „`font-display: optional`“ nebuvo iš anksto įkelti"}, "core/audits/preload-fonts.js | title": {"message": "Šriftai su „`font-display: optional`“ buvo iš anksto įkelti"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Jei DTŽ elementas dinamiškai pridedam<PERSON> pu<PERSON>, t<PERSON><PERSON>tumėte iš an<PERSON>to įkelti vaizdą, kad patobulintum<PERSON>te DTŽ. [Sužinokite daugiau apie išankstinį DTŽ elementų įkėlimą](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "<PERSON><PERSON> įkelkite didžiausio turiningo žymėjimo vaizdą"}, "core/audits/redirects.js | description": {"message": "Peradresuojant puslapio įkėlimo delsos laikas dar labiau pailg<PERSON>ja. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip išvengti puslapio peradresavimų](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Venkite kelių puslapio peradresavimų"}, "core/audits/seo/canonical.js | description": {"message": "Standartizuotos nuorodos siūlo, kurį URL rodyti paieškos rezultatuose. [Sužinokite daugiau apie standartizuotas nuorodas](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON> URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Negaliojantis URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Nukreipia į kitą „`hreflang`“ vietą ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Tai nėra absoliutusis URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Nukreipiama į domeno šakninį URL (pagrindinį puslapį) vietoje atitinkamo turinio puslapio"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumente nėra tinkamo atributo „`rel=canonical`“"}, "core/audits/seo/canonical.js | title": {"message": "Dokumente yra tinkamas atributas „`rel=canonical`“"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Netik<PERSON><PERSON> nuoroda"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> varik<PERSON>, nuorodose į tik<PERSON><PERSON> svet<PERSON>, gali būti naudojami „`href`“ atributai. Įsitikinkite, kad prieraišo elementų „`href`“ atributas susieja su tinkama paskirties vieta, kad būtų galima rasti daugiau svetainės puslapių. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip nuorodas padaryti tikrinamas](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Nuorodos netikrinamos"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Nuorod<PERSON> t<PERSON>"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Papildomas neįskaitomas tekstas"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Puslapio tekstas, %"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Mažesnis nei 12 piks. šriftas yra per <PERSON>, kad b<PERSON><PERSON><PERSON> įskaitomas, to<PERSON><PERSON><PERSON> mobiliuosius įrenginius naudojantys lankytojai turi keisti mastelį su<PERSON><PERSON><PERSON> pirštai<PERSON>, kad galėtų perskaityti. Pasistenkite, kad daugiau nei 60 proc. puslapio teksto šrifto dydis būtų 12 piks. arba didesnis. [Sužinokite daugiau apie įskaitomų dydžių šriftą](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} a<PERSON><PERSON><PERSON><PERSON> teksto"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Te<PERSON><PERSON> neįskaitomas, nes nėra mobiliųjų įrenginių ekranams optimizuotos peržiūros srities metažymos"}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumente naudojami neįskaitomo d<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | legibleText": {"message": "Įskaitomas tekstas"}, "core/audits/seo/font-size.js | title": {"message": "Dokumente naudojami a<PERSON>š<PERSON>us d<PERSON><PERSON>"}, "core/audits/seo/hreflang.js | description": {"message": "„hreflang“ nuorodos nurodo paieškos varikliams, koki<PERSON> puslapio versiją jie turėtų pateikti paieškos rezultatuose pagal nurodytą kalbą ar regioną. [Sužinokite daugiau apie „`hreflang`“](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumente nėra tinkamo atributo „`hreflang`“"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Santykinė atributo „href“ vertė"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumente yra tinkamas atributas „`hreflang`“"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Netikėtas kalbos kodas"}, "core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurių HTTP būsenos kodų nepavyko pateikti, gali būti indek<PERSON> netink<PERSON>i. [Sužinokite daugiau apie HTTP būsenos kodus](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Nepavyko pateikti puslapio HTTP būsenos kodo"}, "core/audits/seo/http-status-code.js | title": {"message": "Sėkmingai pateiktas puslapio HTTP būsenos kodas"}, "core/audits/seo/is-crawlable.js | description": {"message": "Paieškos varikliai negali įtraukti puslapių į paie<PERSON>kos rezultatus, jei neturi leidimo jų tikrinti. [Sužinokite daugiau apie tikrintuvų direktyvas](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> indek<PERSON> blo<PERSON>"}, "core/audits/seo/is-crawlable.js | title": {"message": "Puslapio indeksavimas nėra užblokuotas"}, "core/audits/seo/link-text.js | description": {"message": "Pagal aprašomąjį nuorodų tekstą paieškos varikliai gali lengviau suprasti jūsų turinį. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip padaryti nuorodas lengviau pasiekiamas](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 nuoroda}one{Rasta # nuoroda}few{Rastos # nuorodos}many{Rasta # nuorodos}other{Rasta # nuorodų}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Nuorodose nėra aprašomojo teksto"}, "core/audits/seo/link-text.js | title": {"message": "Nuorodose yra aprašomojo teksto"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Paleiskite [Struktūrinių duomenų bandymo įrankį](https://search.google.com/structured-data/testing-tool/) ir [„Structured Data Linter“](http://linter.structured-data.org/), kad būt<PERSON> patvirtinti struktūriniai duomenys. [Sužinokite daugiau apie struktūrinius duomenis](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Struktūriniai duomenys <PERSON>"}, "core/audits/seo/meta-description.js | description": {"message": "<PERSON>int glaustai apibendrinti puslapio turinį, į paie<PERSON>kos rezultatus galima įtraukti metaaprašų. [Sužinokite daugiau apie metaaprašą](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Nepateiktas apraš<PERSON> teks<PERSON>."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumente nėra metaaprašo"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumente yra <PERSON>"}, "core/audits/seo/plugins.js | description": {"message": "Paieškos varikliai negali indeksuoti papildinių turinio ir daug įrenginių riboja papildinius arba jų nepalaiko. [Sužinokite daugiau apie tai, kaip išvengti papildinių](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumente naudojami papil<PERSON>i"}, "core/audits/seo/plugins.js | title": {"message": "Dokumente vengiama papildinių"}, "core/audits/seo/robots-txt.js | description": {"message": "<PERSON>i failas „robots.txt“ netinkamai suformatuotas, tikrintuvai gali nesuprasti, kaip norite tikrinti ar indeksuoti svetainę. [Sužinokite daugiau apie failą „robots.txt“](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Pateikus „robots.txt“ užklausą gauta HTTP būsena: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Rasta 1 klaida}one{Rasta # klaida}few{Rastos # klaidos}many{Rasta # klaidos}other{Rasta # klaidų}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "„Lighthouse“ nepavyko atsisiųsti failo robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Failas robots.txt netinkamas"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt tinkamas"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktyvūs element<PERSON>, pvz., my<PERSON><PERSON><PERSON> ir nuo<PERSON>, turi būti pakanka<PERSON> did<PERSON> (48 x 48 piks.) arba aplink juos turi būti pakankamai vietos, kad būtų galima lengvai paliesti ir kad jie nepersidengtų su kitais elementais. [Sužinokite daugiau apie liečiamus elementus](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} tinkamo dydžio liečiamų objektų"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Liečiami objektai per ma<PERSON><PERSON>, nes nėra mobiliųjų įrenginių ekranams optimizuotos peržiūros srities metažymos"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Liečiami objektai netinkamo dydžio"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Persideng<PERSON><PERSON> objektas"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Liečiamas objektas"}, "core/audits/seo/tap-targets.js | title": {"message": "Liečiami objektai tinkamo dydžio"}, "core/audits/server-response-time.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad pagrindinio dokumento serverio atsako laikas būt<PERSON> trumpas, nes nuo jo priklauso visos kitos užklausos. [Sužinokite daugiau apie laiko iki pirmojo baito metrik<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Šakninio dokumento įkėlimas užtruko {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Pradinio serverio atsako laiko sumaž<PERSON>mas"}, "core/audits/server-response-time.js | title": {"message": "Pradinio serverio atsako laikas buvo trumpas"}, "core/audits/splash-screen.js | description": {"message": "Teminis prisistatymo langas užtikrina kokybišką patirtį naudotojui paleidžiant jūsų programą iš pagrindinio ekrano. [Sužinokite daugiau apie prisistatymo langus](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Nesukonfigūruota tinkintam prisistatymo langui"}, "core/audits/splash-screen.js | title": {"message": "Sukonfigūruota tinkintam prisistatymo langui"}, "core/audits/themed-omnibox.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s adreso juostos temą galima pakeisti taip, kad atitiktų svetainės temą. [Sužinokite daugiau apie adreso juostos temą](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Nenustatoma adreso juostos temos spalva."}, "core/audits/themed-omnibox.js | title": {"message": "Nustatoma adreso juostos temos spalva."}, "core/audits/third-party-cookies.js | description": {"message": "Trečiųjų šalių slapukų palaikymas bus pašalintas būsimos versijos naršyklėje „Chrome“. [Sužinokite daugiau apie trečiųjų šalių slapukų atsisakymą](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>stas 1 slapukas}one{Rastas # slapukas}few{Rasti # slapukai}many{Rasta # slapuko}other{Rasta # slapukų}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Naudojami trečiųjų šalių slapukai"}, "core/audits/third-party-cookies.js | title": {"message": "Vengiama trečiųjų šalių slapukų"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (klientų sėkmės istorijos)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (R<PERSON><PERSON>ra)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (<PERSON><PERSON>i tinkla<PERSON>)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vaizdo įrašas)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produktas"}, "core/audits/third-party-facades.js | description": {"message": "Kai kurių trečiųjų šalių įterptų elementų įkėlimas gali būti atidėtas. Apsvarstykite galimybę pakeisti juos fasadu, kol nėra būtini. [<PERSON><PERSON><PERSON>kite, kaip naudojant fasadą atidėti trečiųjų šalių elementus](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Pasiekiamas # alternatyvus fasadas}one{Pasiekiamas # alternatyvus fasadas}few{Pasiekiami # alternatyvūs fasadai}many{Pasiekiama # alternatyvaus fasado}other{Pasiekiama # alternatyvių fasadų}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Kai kurių trečiųjų šalių išteklių įkėlimas gali būti atidėtas naudojant fasadą"}, "core/audits/third-party-facades.js | title": {"message": "Atidėtojo įkėlimo trečiųjų šalių ištekliai su fasadais"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Treč<PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "Trečiosios šalies kodas gali smarkiai paveikti įkėlimo našumą. Apribokite trečiosios šalies teikėjų skaičių ir pabandykite įkelti trečiosios šalies kodą, kai puslapis bus įkeltas. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sumažinti trečiosios šalies poveikį](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Trečiosios šalies kodas {timeInMs, number, milliseconds} ms užblokavo pagrindinę grupę"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Sumažina trečiosios šalies kodo poveikį"}, "core/audits/third-party-summary.js | title": {"message": "Trečiųjų šalių kodo naudojimo sumažinimas"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Met<PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Nustatykite la<PERSON>, kad gal<PERSON>te lengviau stebėti svetainės našum<PERSON>. Na<PERSON><PERSON> svetain<PERSON> įkeliamos sparčiau ir greičiau reaguoja į naudotojų įvesties įvykius. [Sužinokite daugiau apie našumo bi<PERSON>](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "<PERSON><PERSON>"}, "core/audits/unsized-images.js | description": {"message": "Nustatykite tikslų vaizdo elementų plotį ir aukštį, kad būtų mažiau išdėstymo poslinkių ir pagerinamas KIP. [<PERSON><PERSON><PERSON>kite, kaip nustatyti vaizdo matmen<PERSON>](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Vaizdo elementų matmenys nėra tikslūs: `width` ir `height`"}, "core/audits/unsized-images.js | title": {"message": "Tikslūs vaizdo elementų matmenys: `width` ir `height`"}, "core/audits/user-timings.js | columnType": {"message": "Tipas"}, "core/audits/user-timings.js | description": {"message": "Apsvarstykite galimybę stebėti ir vertinti programą naudojant naudotojo laiko API, kad būtų nustatytas realus programos našumas, kai naudotojas naudoja pagrindines funkcijas. [Sužinokite daugiau apie naudotojo laiko žymes](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 naudotojo laikas}one{# naudotojo laikas}few{# naudotojo laikai}many{# naudotojo laiko}other{# naudotojo laikų}}"}, "core/audits/user-timings.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> „{security<PERSON><PERSON>in}“ atributas „`<link rel=preconnect>`“, tačiau jis nebuvo naudojamas naršyklėje. Patikrinkite, ar atributas „`crossorigin`“ naudo<PERSON><PERSON>."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Apsvarstykite galimybę pridėti „`preconnect`“ arba „`dns-prefetch`“ ištekliaus nurodymus, kad ryšys su svarbiais trečiųjų šalių šaltiniais būtų užmezgamas iš anksto. [Su<PERSON><PERSON>kite, kaip iš anksto prijungti prie būtinų šaltinių](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON> prisijunkite prie reikiamų šaltinių"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON><PERSON> da<PERSON> nei dvi „`<link rel=preconnect>`“ sąsajos. <PERSON><PERSON> turi būti naudojamos taupiai ir susiejant tik su svarbiausiais šaltiniais."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "<PERSON><PERSON><PERSON> „{security<PERSON><PERSON>in}“ atributas „`<link rel=preconnect>`“, tačiau jis nebuvo naudojamas naršyklėje. Svarbiuose šaltiniuose naudokite tik atributą „`preconnect`“, kurio už<PERSON> puslapis tikrai pateiks."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> „{preloadURL}“ iš anksto įkeliamas atributas „`<link>`“, tačiau jis nebuvo naudojamas naršyklėje. Patikrinkite, ar atributas „`crossorigin`“ naudoja<PERSON>."}, "core/audits/uses-rel-preload.js | description": {"message": "Apsvarstykite galimybę naudoti „`<link rel=preload>`“ ir suteikti pirmenybę gaunamiems ištekliams, kurių užklausos šiuo metu teikiamos vėliau įkeliant puslapį. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip iš anksto įkelti pagrindines užklausas](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON> įkelkite svar<PERSON>us<PERSON> užklausa<PERSON>"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Žemėlapio URL"}, "core/audits/valid-source-maps.js | description": {"message": "Šaltinio žemėlapiai išverčia sumažintą kodą į originalų šaltinio kodą. Tai kūrėjams padeda derinti kuriant gamybinę versiją. Be to, „Lighthouse“ gali pateikti daugiau įžvalgų. Apsvarstykite galimybę pritaikyti šaltinio žemėlapius ir pasinaudoti šiais privalumais. [Sužinokite daugiau apie šaltinio žemėlapius](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Trūks<PERSON> didelio p<PERSON> „JavaScript“ failo šaltinio žemėlapių"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "<PERSON><PERSON><PERSON>JavaScript“ faile nėra šaltinio žemėlapio"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Įspėjimas: trūksta 1 elemento atribute „`.sourcesContent`“}one{Įspėjimas: trūksta # elemento atribute „`.sourcesContent`“}few{Įspėjimas: trūksta # elementų atribute „`.sourcesContent`“}many{Įspėjimas: trūksta # elemento atribute „`.sourcesContent`“}other{Įspėjimas: trūksta # elementų atribute „`.sourcesContent`“}}"}, "core/audits/valid-source-maps.js | title": {"message": "Puslapyje yra galiojančių šaltinio žemėlapių"}, "core/audits/viewport.js | description": {"message": "„`<meta name=\"viewport\">`“ ne tik optimizuoja programą pagal mobiliojo įrenginio ekrano dydį, bet taip pat padeda išvengti [300 milisekundžių naudotojo įvesties delsos](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Sužinokite daugiau apie peržiūros srities metažymos naudojimą](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON> `<meta name=\"viewport\">` nerasta"}, "core/audits/viewport.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><meta name=\"viewport\">` su `width` arba `initial-scale`"}, "core/audits/viewport.js | title": {"message": "<PERSON>ra <PERSON> `<meta name=\"viewport\">` su `width` arba `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Tai yra grupių blokavimas, atliekamas įvertinant laiką nuo sąveikos iki kito žymėjimo. [Sužinokite daugiau apie laiką nuo sąveikos iki kito žymėjimo](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "Įvykis „{interactionType}“ truko {timeInMs, number, milliseconds} ms"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Įvykio tikslas"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Sutrumpinkite veikimą per svarbiausią sąveiką"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Įvesties delsa"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Pateikimo delsa"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>"}, "core/audits/work-during-interaction.js | title": {"message": "Su<PERSON><PERSON><PERSON> ve<PERSON> per svarbiausią sąveiką"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON> yra <PERSON>, <PERSON>os pagerinti ARIA naudojimą programoje. Jos gali pagerinti pagalbinių technologijų, pvz., ekrano s<PERSON>tytuv<PERSON>, naudotojų patirtį."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Tai yra gal<PERSON>, skirtos alternatyviam garso ir vaizdo įrašų turiniui teikti. Tai gali pagerinti klausos ar regėjimo sutrikimų turinčių naudotojų patirtį."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> ir vaizda<PERSON>"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Šie elementai paryškina dažniausiai naudojamus pritaikomumo geriausios praktikos metodus."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Geriausia praktika"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Atlikę šias patikras sužinosite, kaip galite [geriau pritaikyti žiniatinklio programą](https://developer.chrome.com/docs/lighthouse/accessibility/). Naudojant automatinį aptikimą galima aptikti tik dalį problemų ir negarantuojamas žiniatinklio programos pritaikomumas, tod<PERSON><PERSON> taip pat rekomenduojama atlikti [neautomatinį bandymą](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON>ie elementai apima srit<PERSON>, kurių automatinio bandymo įrankis negali aprėpti. Mūsų vadove sužinokite daugiau apie tai, kaip [atlikti pritaikomumo peržiūrą](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON> y<PERSON>, ka<PERSON> <PERSON><PERSON><PERSON>."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON> yra <PERSON>, <PERSON><PERSON>, ka<PERSON> skirtingų lokalių naudotojai interpretuoja jūsų turinį."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizavimas ir lokalizavimas"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Tai yra <PERSON>, skirtos pagerinti programos valdiklių semantiką. Tai gali pagerinti pagalbinių technologijų, pvz., ekrano s<PERSON>tytuv<PERSON>, naudotojų patirtį."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Pavadinimai ir et<PERSON>"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Tai yra <PERSON>, skirtos pagerinti naršymą klaviatūra programoje."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Tai y<PERSON>, <PERSON><PERSON> ar sąrašuose pateiktų duomenų skaitymą naudojant pagalbines technologijas, pvz., ekrano skaitytuvą."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Geriausios praktikos pavyzdžiai"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Bendra"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Pat<PERSON><PERSON> ir saugu"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Nuo kate<PERSON><PERSON><PERSON><PERSON> „Našumas“ nustatyto biudžeto priklauso svet<PERSON> našuma<PERSON>."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Biudžetai"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Daugiau informacijos apie programos našumą. <PERSON>ie skaič<PERSON> [tiesiogiai nepaveikia](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) našumo įvertinimo."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Svarbiausias našum<PERSON> rod<PERSON> – kaip greitai taškai pateikiami ekrane. Svarbiausia metrika: pirmasis „Contentful“ parodymas, pirmasis reikšmingas parodymas"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Pirm<PERSON>jo <PERSON><PERSON>"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Pa<PERSON>aud<PERSON><PERSON><PERSON> šiais pasiūlymais puslapis gali būti įkeliamas greičiau. Tai [tiesiogiai nepaveiks](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) našumo įvertinimo."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "Met<PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Pagerinkite bendrą įkėlimo našumą, kad puslapis reaguotų ir būtų parengtas naudoti kuo greičiau. Svarbiausia metrika: laikas iki <PERSON>, g<PERSON><PERSON><PERSON><PERSON> rod<PERSON>"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Per šias patikras vertinamos laipsniškosios žiniatinklio programos aspekto paprogramės. [<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sukurti gerą laipsniškąją žiniatinklio programą](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON><PERSON> pat<PERSON> bū<PERSON> pagal pradinį [LŽP kontrolinį sąrašą](https://web.dev/articles/pwa-checklist), tačiau „Lighthouse“ jų neatlieka automatiškai. Jos neturi įtakos jūsų rezultatui, ta<PERSON><PERSON><PERSON> svarbu, kad pat<PERSON> pat<PERSON>."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "LŽP"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "G<PERSON><PERSON> įdiegti"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizuota PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> pat<PERSON>, kad puslapyje laikomasi pagrindinių paieškos variklio optimizavimo rekomendacijų. Yra daugybė veiksnių, į kuriuos „Lighthouse“ neatsižvelgia ir kurie gali turėti įtakos paieškos reitingavimui, įskaitant našumą pagal [s<PERSON><PERSON><PERSON><PERSON><PERSON> žiniatinklio „Vitals“ metriką](https://web.dev/explore/vitals). [Sužinokite daugiau apie „Google“ paieškos pagrindus](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Paleiskite š<PERSON> papildomas patvirtinimo priemon<PERSON> s<PERSON>, kad patik<PERSON>te papildomus PVO geriausios praktikos metodus."}, "core/config/default-config.js | seoCategoryTitle": {"message": "PVO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatuokite HTML taip, kad tikrintuvai galėtų geriau suprasti programos turinį."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Geriausios turinio praktikos pavyzdžiai"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Kad programa būtų rodoma paieškos rezultatuose, tikrintuvams reikalinga prieiga prie jos."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ir in<PERSON><PERSON>"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Įsitikinkite, kad puslapiai yra pritaikyti mobiliesiems, kad naudotojams nereikėtų keisti mastelio arba suėmus artinti norint perskaityti puslapių turinį. [<PERSON><PERSON><PERSON>kite, kaip pritaikyti puslapius mobiliesiems](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Pritaikyta mobiliesiems"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Išbandyto įrenginio centrinis procesorius pasirod<PERSON>, nei tik<PERSON> „Lighthouse“. Tai gali neigiamai paveikti našumo įvertinimą. Sužinokite daugiau apie [atitinkamo centrinio procesoriaus sulėtėjimo koeficiento kalibravimą](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON><PERSON><PERSON><PERSON> gali būti neįkeliamas, ka<PERSON>, nes bandomasis URL ({requested}) peradresavo į {final}. Išbandykite antrąjį URL tiesiogiai."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Puslapis įkeliamas per lėtai, kad įkėlimas būtų baigtas atitinkant laiko apribojimą. Rezultatai gali būti nei<PERSON>."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Baigėsi narš<PERSON>lės talpyklos valymo skirtasis laikas. Pabandykite patikrinti šį puslapį dar kartą ir praneškite apie riktą, jei <PERSON>a <PERSON>."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Šioje vietoje gali būti išsaugotų duomenų, turinčių poveikio įkėlimo našumui: {locations}. Patikrinkite šį puslapį inkognito lange, kad šie šaltiniai nepaveiktų rezultatų.}one{Šiose vietose gali būti išsaugotų duomenų, turinčių poveikio įkėlimo našumui: {locations}. Patikrinkite šį puslapį inkognito lange, kad šie šaltiniai nepaveiktų rezultatų.}few{Šiose vietose gali būti išsaugotų duomenų, turinčių poveikio įkėlimo našumui: {locations}. Patikrinkite šį puslapį inkognito lange, kad šie šaltiniai nepaveiktų rezultatų.}many{Šiose vietose gali būti išsaugotų duomenų, turinčių poveikio įkėlimo našumui: {locations}. Patikrinkite šį puslapį inkognito lange, kad šie šaltiniai nepaveiktų rezultatų.}other{Šiose vietose gali būti išsaugotų duomenų, turinčių poveikio įkėlimo našumui: {locations}. Patikrinkite šį puslapį inkognito lange, kad šie šaltiniai nepaveiktų rezultatų.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Baigėsi šaltinio duomenų šalinimo skirtasis laikas. Pabandykite patikrinti šį puslapį dar kartą ir praneškite apie riktą, jei <PERSON>a i<PERSON>."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "<PERSON><PERSON><PERSON> anal<PERSON> „`link`“ antraštę ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Paleidžiant aptiktas puslapio naršymas. Nerekomenduojama naudoti laikotarpio režimo tikrinant puslapių naršymą. Naudokite narš<PERSON>o re<PERSON>, kad patikrintumėte puslapių naršymą, siekdami geriau jį priskirti ir aptikti pagrindinę grupę."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON>, įkeltiems naudojant GET <PERSON>, galima taik<PERSON>i il<PERSON>ai<PERSON>o viso puslapio saugojimo talpykloje funkciją."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Talpykloje galima saugoti tik puslapius, kuri<PERSON> būsenos kodas yra 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "„Chrome“ aptiko bandymą vykdyti „JavaScript“, kol saugoma talpykloje."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> „AppBanner“ <PERSON><PERSON><PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl palaikymo užklausos."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta taikant žymas. Jei norite įgalinti ją vietiniu mastu šiame įrenginyje, apsilankykite adresu chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta taikant komandos eilutę."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl nepakankamos atminties."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Perduodant il<PERSON><PERSON><PERSON>o viso puslapio saugojimo talpykloje funkcija nepalaikoma."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl išankstinio pateikimo priemonės."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Puslapio negalima saugoti talpykloje, nes jame yra „BroadcastChannel“ objektas su registruotomis apdorojimo priemonėmis."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Puslapiams su antrašte „cache-control:no-store“ negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Talpykla tyčia išvalyta."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, kad būt<PERSON> leidžiama talpykloje saugoti kitą puslapį."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra papildinių, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudoja<PERSON> „FileChooser“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose naudojama failų sistemos prieigos API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojamas „Media Device Dispatcher“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON><PERSON><PERSON> le<PERSON> buvo leidž<PERSON>a prieš i<PERSON> iš jos."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „MediaSession“ API ir nustatyta atkūrimo būsena, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „MediaSession“ API ir nustatytos veiksmų doroklės, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl ekrano skaitytuvo."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „SecurityHandler“, negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose naudojama serijos API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebAuthetication“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebBluetooth“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebUSB“ API, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta, nes slapukai išjungti puslapyje, k<PERSON><PERSON> naudo<PERSON> „`Cache-Control: no-store`“."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojamas <PERSON>asis scenarijus arba scenarija<PERSON> da<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokumentas nebuvo visiškai įkeltas prieš išeinant iš jo."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Na<PERSON>šant buvo rodoma programų reklamjuostė."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> „Chrome“ slaptažodžių tvarkyklė."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON><PERSON><PERSON><PERSON> DOM distiliavimas."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON><PERSON> „DOM Distiller Viewer“."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl plėtinių, kuriems naudojama susirašinėjimo API."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Ilgalaikio ryšio plėtiniai turi nutraukti ryšį prieš ilgalaikį viso puslapio iš<PERSON>ugojimą talpykloje."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Ilgalaikio ryšio plėtiniai bandė siųsti pranešimus į ilgalaikio viso puslapio saugojimo talpyklo<PERSON> kadrus."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl plėtinių."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodomas puslapio modalinio dialogo langas, pvz., pakartotinio formos pateikimo arba HTTP slaptažodžio dialogo langas."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodomas neprisi<PERSON>gus pasiekiamas pusla<PERSON>."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rod<PERSON> „Out-Of-Memory Intervention“ juosta."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo pateikta leidimo užklausų."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON><PERSON> su<PERSON> i<PERSON>š<PERSON>nčiųjų langų blokavimo priemonė."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON><PERSON> buvo rodoma saugaus naršymo išsami informacija."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON><PERSON>us naršymo įrankis nusprend<PERSON>, kad <PERSON><PERSON>o puslapiu p<PERSON><PERSON><PERSON><PERSON><PERSON>, ir u<PERSON><PERSON><PERSON><PERSON><PERSON> iššokan<PERSON><PERSON><PERSON><PERSON> langus."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas buvo suaktyvintas, kol puslapiui buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Ilgalaikis viso puslapio sa<PERSON>jimas talpykloje išjungtas dėl dokumento klaidos."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „FencedFrames“, negalima taikyti il<PERSON>aikio viso puslapio saugojimo talpykloje."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, kad būt<PERSON> leidžiama talpykloje saugoti kitą puslapį."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON>uose suteikta medijos srautinio perdavimo prieiga, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> na<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „IdleManager“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyk<PERSON>."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>se veikia atviras „IndexedDB“ ry<PERSON><PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Ilgalaikis viso puslapio saug<PERSON>jimas talpykloje išjungtas dėl įvykio „IndexedDB“."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Buvo naudojamos netinkamos API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> įterpta „`JavaScript`“ naudo<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> įterpta „`StyleSheet`“ naudo<PERSON><PERSON>, š<PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Ilgalaikio viso puslapio saugojimo talpykloje funkcija išjungta dėl palaikymo užklausos."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON> funk<PERSON>."}, "core/lib/bf-cache-strings.js | loading": {"message": "Puslapis nebuvo visiškai įkeltas prieš išeinant iš jo."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> pagrindiniame šaltinyje yra „cache-control:no-cache“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> pagrindiniame šaltinyje yra „cache-control:no-store“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> buvo at<PERSON><PERSON><PERSON>, p<PERSON><PERSON> atkurti puslapį, kuriam taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Puslapis buvo p<PERSON><PERSON><PERSON><PERSON> i<PERSON>, nes aktyvus tinklo r<PERSON>š<PERSON> gavo per daug duomenų. „Chrome“ riboja duomenų, kuriuos gali gauti talpykloje sa<PERSON>, kiekį."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> „fetch()“ arba XHR, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Puslapiui nebetaikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija, nes į aktyvaus tinklo užklausą buvo įtrauktas peradresavimas."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Puslapis buvo pa<PERSON><PERSON><PERSON> i<PERSON>, nes tinklo <PERSON> per ilgai buvo atviras. „Chrome“ riboja laikotarpį, per kurį talpykloje saugomas puslapis gali gauti duomenis."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> nėra tin<PERSON> at<PERSON>, negalima ta<PERSON>i ilgalaikio viso puslapio saugojimo talpyk<PERSON>."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> veiksma<PERSON> atliktas ne pagrindiniame rėmelyje."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuose yra vykdomų indeksuotų DB operacijų, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> si<PERSON>, <PERSON><PERSON><PERSON> metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> si<PERSON> g<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON> funkci<PERSON>."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> si<PERSON>, <PERSON><PERSON><PERSON> metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> si<PERSON>čiama XHR tink<PERSON>, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „PaymentManager“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> v<PERSON> v<PERSON>, <PERSON><PERSON><PERSON> metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> na<PERSON><PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> rod<PERSON>imo NS, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Puslapis buvo atidarytas naudojant „`window.open()`“ ir kitame skirtuke yra jo nuoroda arba puslapyje atidarytas langas."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Pateikėjo procesas pusla<PERSON>, kuriam taikoma il<PERSON>o viso puslapio saugojimo talpyk<PERSON>, užstrigo."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Pateikėjo procesas pusla<PERSON>, kuriam taikoma il<PERSON>o viso puslapio saugojimo talpykloje funkci<PERSON>, buvo nutrauktas."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> g<PERSON>o įrašo fiks<PERSON>mo leid<PERSON> už<PERSON>us<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> juti<PERSON> leidim<PERSON> užklaus<PERSON>, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> sinchronizavimo fone arba gavimo leid<PERSON> už<PERSON>us<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> MIDI leidimų užklausą, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pat<PERSON> p<PERSON>š<PERSON>ų leidimų užklausą, <PERSON>iuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> sa<PERSON> p<PERSON>igo<PERSON>, <PERSON><PERSON>o metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> vaiz<PERSON> įrašo fiks<PERSON>mo leidim<PERSON> už<PERSON>, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Talpykloje galima saugoti tik puslapius, kurių URL schema yra HTTP / HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas patvirtino puslapį, kol jam buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas bandė siųsti „`MessageEvent`“ pusla<PERSON>, kuriam taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas buvo išregistruotas, kol puslapiui buvo taikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Suaktyvinus pagalbinį „JavaScript“ failą, puslapiui nebetaikoma ilgalaikio viso puslapio saugojimo talpykloje funkcija."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "„Chrome“ paleido iš naujo ir i<PERSON><PERSON> ilgalaikio viso puslapio saugojimo talpykloje įrašus."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „SharedWorker“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje <PERSON>."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „SpeechRecognizer“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „SpeechSynthesis“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "„Iframe“ puslapyje pradėjo naršymo procesą, kuris nebuvo užbaigtas."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> papildomame šaltinyje yra „cache-control:no-cache“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> papildomame šaltinyje yra „cache-control:no-store“, negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> pusla<PERSON> la<PERSON>, taikant il<PERSON><PERSON><PERSON><PERSON> viso puslapio saugojimo talpy<PERSON><PERSON>, ir puslapis nebegal<PERSON>."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Baigėsi puslapio skirtasis laikas bandant pritaikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>ją (veikiausiai dėl ilgai vykdomų „pagehide“ doroklių)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Puslapio pagrindiniame rėmelyje yra iškėlimo doroklė."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Puslapio papildomame rėmelyje yra iškėlimo doroklė."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pakeitė naudotojo priemonės nepaisymo <PERSON>š<PERSON>ę."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> suteikta vaizdo ar garso įrašymo prieiga, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkci<PERSON>."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebDatabase“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „WebHID“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON><PERSON> „WebLock“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudo<PERSON>ma „WebNfc“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebOTPService“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Pus<PERSON>iams su „WebRTC“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funk<PERSON>."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebShare“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Pus<PERSON>iams su „WebSocket“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Pus<PERSON>iams su „WebTransport“ negalima taikyti ilgalaikio viso puslapio saugojimo talpykloje funkcijos."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudojama „WebXR“, šiuo metu negalima taikyti ilgalaikio viso puslapio saugojimo talpyklo<PERSON>."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Apsvarstykite galimybę pridėti „https:“ ir „http:“ URL schemas (nepaisoma naršyklėse, palaikančiose „`'strict-dynamic'`“), kad būt<PERSON> užtikrintas atgalinis suderinamumas su senesnėmis naršyklėmis."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "„`disown-opener`“ teikimas nutrauktas nuo CSP3. Vietoj jos naudokite antraštę „Cross-Origin-Opener-Policy“."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "„`referrer`“ teikimas nutrauktas nuo CSP2. Vietoj jos naudokite antraštę „Referrer-Policy“."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "„`reflected-xss`“ teikimas nutrauktas nuo CSP2. Vietoj jos naudokite antraštę „X-XSS-Protection“."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "<PERSON><PERSON> n<PERSON> „`base-uri`“, galima įterpti žymas „`<base>`“. Jas naudojant galima nustatyti visų susijusių URL (pvz., scenarijų) pagrindinį URL kaip atakos vykdytojo kontroliuojamą domeną. Apsvarstykite galimybę nustatyti „`base-uri`“ kaip „`'none'`“ arba „`'self'`“."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "<PERSON> n<PERSON>ra „`object-src`“, leidžiama įterpti papildinių, vykdančių nesaugius scenarijus. <PERSON><PERSON> galite, apsvarstykite galimybę nustatyti „`object-src`“ kaip „`'none'`“."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Trūksta direktyvos „`script-src`“. <PERSON><PERSON> b<PERSON><PERSON> le<PERSON> v<PERSON>dyti ne<PERSON>ug<PERSON> scenari<PERSON>."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Gal p<PERSON> įvesti kabliataškį? <PERSON><PERSON><PERSON><PERSON>, kad „{keyword}“ yra dire<PERSON>, o ne raktin<PERSON> žod<PERSON>."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "<PERSON><PERSON><PERSON><PERSON> „nonce“ reikia naudoti „base64“ simbolių rinkinį."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "<PERSON><PERSON><PERSON><PERSON> „nonce“ turi būti bent 8 simbolių ilgio."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Venkite naudoti trumpų URL schemas ({keyword}) šioje direktyvoje. Naudojant trumpų URL schemas, scenarijai gali būti patei<PERSON> iš nesa<PERSON> domeno."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Venkite naudoti grynuosius pakaitos simbolius ({keyword}) šioje direktyvoje. Naudojant grynuosius pakaitos simbolius, scenarijai gali būti patei<PERSON> iš nesa<PERSON> domeno."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Ataskaitų teikimo paskirties vietą galima sukonfigūruoti tik naudojant direktyvą „report-to“. Ši direktyva palaikoma tik „Chromium“ pagrindo na<PERSON>, tod<PERSON><PERSON> taip pat rekomenduojama naudoti direktyvą „`report-uri`“."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Jokioje CSP nesukonfigūruota ataskaitų teikimo paskirties vieta. Tai apsunkins tolesnę CSP priežiūrą ir problemų stebėjimo procesą."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Prieglobos leidžiamųjų sąrašus neretai galima apeiti. Apsvarstykite galimybę naudoti CSP vertes „nonce“ arba „hash“ su „`'strict-dynamic'`“, jei reikia."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Nežinoma CSP direktyva."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad „{keyword}“ yra net<PERSON> r<PERSON>."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Naudojant „`'unsafe-inline'`“ leidžiama vykdyti nesaugius puslapio scenarijus ir įvykių dorokles. Apsvarstykite galimybę naudoti CSP vertes „nonce“ arba „hash“, kad scenarijų vykdymas būtų leidžiamas atskirai."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Apsvarstykite galimybę pridėti „`'unsafe-inline'`“ (nepaisoma naršyklėse, palaikančiose vertes „nonce“ / „hash“), kad būtų užtikrintas atgalinis suderinamumas su senesnėmis naršyklėmis."}, "core/lib/deprecation-description.js | feature": {"message": "Jei reikia daugiau išsamios informacijos, žr. <PERSON><PERSON><PERSON><PERSON> būsen<PERSON> puslapį."}, "core/lib/deprecation-description.js | milestone": {"message": "<PERSON><PERSON> pakeitimas įsigalios paskelbus pagrindinės versijos numerį {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> te<PERSON>"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Prieigos teisė nebus įtraukta naudojant pakaitos simbolį (*) vykdant CORS funkcijos „`Access-Control-Allow-Headers`“ apdorojimą."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "<PERSON><PERSON> numatytąjį „Cast“ integravimą turėtų būti naudojamas atributas „`disableRemotePlayback`“ atributas vietoj parinkiklio „`-internal-media-controls-overlay-cast-button`“."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Šaltinių užklausos, kurių URL buvo pašalintų matomų tarpų „`(n|r|t)`“ simbolių ir simbolių „mažiau ne“ („`<`“), yra u<PERSON>. Pašalinkite naujas eilutes ir užkoduokite simbolius „mažiau nei“ tokiose vietose kaip elementų atributų vertės, kad įkeltumėte šiu<PERSON>."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Funkcijos „`chrome.loadTimes()`“ teikimas nutrauktas; vietoj jos naudokite standartizuotą API: „2 naršymo laiko <PERSON>“."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "„`chrome.loadTimes()`“ teiki<PERSON> nutrauk<PERSON>; vietoj to naudokite standartizuotą API: „Vizualizavimo la<PERSON>“."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "„`chrome.loadTimes()`“ teikimas nutrauktas; vietoj jos naudokite standartizuotą API: „`nextHopProtocol`“ skiltyje „2 naršymo laiko <PERSON>“."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> yra „`(0|r|n)`“ simbolis, bus atmesti, o ne sutrumpinti."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "To paties šaltinio politikos apribojimų atlaisvinimo nustatant „`document.domain`“ funkcijos teikimas nutrauktas ir ji bus išjungta pagal numatytuosius nustatymus. Šis teikimo nutraukimo perspėjimas skirtas skirtingų šaltinių prieigai, kuri buvo įgalinta nustačius „`document.domain`“."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Funkcijos „window.alert“ su<PERSON><PERSON><PERSON><PERSON> i<PERSON> skirtingų šaltinių „iframe“ teikimas nutrauktas ir ateityje ji bus pašalinta."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Funkcijos „window.confirm“ suak<PERSON><PERSON><PERSON> i<PERSON> skirtingų šaltinių „iframe“ teikimas nutrauktas ir ateityje ji bus pašalinta."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "DOM pakeitimų įvykių, įskaitant „`DOMSubtreeModified`“, „`DOMNodeInserted`“, „`DOMNodeRemoved`“, „`DOMNodeRemovedFromDocument`“, „`DOMNodeInsertedIntoDocument`“ ir „`DOMCharacterDataModified`“, teikimas nutrauktas (https://w3c.github.io/uievents/#legacy-event-types) ir jie bus pašalinti. Vietoj jų naudokite „`MutationObserver`“."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Duomenų palaikymas: SVG elemento <use> URL teikimas nutrauktas ir jis ateityje bus pašalintas."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "To paties šaltinio politikos apribojimų atlaisvinimo nustatant „`document.domain`“ funkcijos teikimas nutrauktas ir ji bus išjungta pagal numatytuosius nustatymus. Jei norite toliau naudoti šią funkciją, atsisakykite šaltiniu pagrįstų priemonės grupių siųsdami „`Origin-Agent-Cluster: ?0`“ antraštę su HTTP atsaku dokumentui ir rėmeliams. Jei reikia išsamesnės informacijos, žr. https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "„`Expect-CT`“ antraštės teikimas nutrauktas ir ji bus pa<PERSON>lint<PERSON>. „Chrome“ reikalauja visų viešai pateikiamų sertifikatų, išduotų po 2018 m. balandžio 30 d., sertif<PERSON><PERSON> skaidrumo."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "„`getCurrentPosition()`“ ir „`watchPosition()`“ nebeveikia nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Funkcij<PERSON> „`getCurrentPosition()`“ ir „`watchPosition()`“ teikimas nutrauktas nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "„`getUserMedia()`“ nebeveikia nesaugiuose šaltiniuose. Jei norite naudoti šią funkciją, turite apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "„`RTCPeerConnectionIceErrorEvent.hostCandidate`“ teikimas nutrauktas. Vietoj to naudokite „`RTCPeerConnectionIceErrorEvent.address`“ arba „`RTCPeerConnectionIceErrorEvent.port`“."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Prekybininko šaltinio ir atsitiktinių duomenų iš „`canmakepayment`“ pagalbinio „JavaScript“ failo įvykio teikimas nutrauktas ir jie bus pašalinti: „`topOrigin`“, „`paymentRequestOrigin`“, „`methodData`“, „`modifiers`“."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "S<PERSON><PERSON><PERSON> pateikė papildomo šaltinio užklausą iš tinklo, kurį ji galėtų pasiekti tik dėl savo naudotojų privilegijuotosios tinklo pozicijos. Šiose užklausose parodomi ne vieši įrenginiai ir serveriai internete, todėl padidėja užklausos iš trečiosios šalies svetainės klastojimo (angl. „cross-site request forgery“, CSRF) atakos ir (arba) informacijos nutekėjimo rizika. Siekdama sumažinti šią riziką, „Chrome“ nutraukia užklausų ne viešiems papildomiems šaltiniams teikimą, kai jos inicijuojamos iš nesaugios aplinkos, ir pradės jas blokuoti."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "„`InterestGroups`“ laukas „`dailyUpdateUrl`“, perdu<PERSON>s „`joinAdInterestGroup()`“, pervardytas į „`updateUrl`“, kad tiksliau atspindėtų jo elgseną."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "CSS kalbos negalima įkelti iš „`file:`“ U<PERSON>, nebent jie baigiasi `.css` failo pl<PERSON>."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "„`SourceBuffer.abort()`“ naudojimas siekiant nutraukti „`remove()`“ nesinchronizuoto diapazono pašalinimą nutrauktas dėl specifikacijos pakeitimo. Ateityje palaikymas bus pašalintas. Turėtumėte klausyt<PERSON> `updateend` įvykio. „`abort()`“ skirtas naudoti tik norint nutraukti nesinchronizuotos medijos pridėjimą arba iš naujo nustatyti analizavimo įrankio būseną."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "„`MediaSource.duration`“ nustatymas žemiau nei bet kurio į buferį įrašyto užkoduoto rėmelio aukščiausia pristatymo laiko žymė nutrauktas dėl specifikacijos pakeitimo. Sutrumpintos į buferį įrašytos medijos numatomo pašalinimo palaikymas ateityje bus pašalintas. Vietoj to turėtumėte atlikti aiškų „`remove(newDuration, oldDuration)`“ visuose „`sourceBuffers`“, kur „`newDuration < oldDuration`“."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Žiniatinklio MIDI prašys naudo<PERSON>, net jei sistemos išskirtinis pranešimas nenurodytas funkcijoje „`MIDIOptions`“."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "<PERSON><PERSON><PERSON>, nestandartinio atributo „`shadowroot`“ teikimas nutrauktas ir M119 *nebeveiks*. Vietoj jo naudokite naują, standartizuotą atributą „`shadowrootmode`“."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Pranešimų API nebegalima naudoti iš nesaugių šaltinių. Turėtumėte apsvarstyti galimybę perjungti programą į saugų šaltinį, pvz., HTTPS. Jei reikia išsamesnės informacijos, žr. https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Pranešimų API leidimo užklausų nebegalima teikti iš skirtingų šaltinių „iframe“. Vietoj to turėtumėte apsvarstyti galimybę teikti leidimo užklausą iš aukščiausio lygio rėmelio arba atidaryti naują langą."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Parinkties „`imageOrientation: 'none'`“, <PERSON><PERSON><PERSON><PERSON> „createImageBitmap“, teikimas nutrauktas. Vietoj jos naudokite „createImageBitmap“ su parinktimi „\\{imageOrientation: 'from-image'\\}“."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "Jūsų partneris derasi <PERSON> (D)TLS versijos. Susisiekite su partneriu, kad tai sutvarkytumėte."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "<PERSON><PERSON><PERSON> „img“, „video“ ir „canvas“ nuro<PERSON><PERSON><PERSON> „`overflow: visible`“, vaizdo turinys gali būti sukurtas už elemento ribų. Žr. https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "„`paymentManager.instruments`“ teikimas nutrauktas. Mokėjimų doroklėms naudokite tiesioginį diegimą."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "„`PaymentRequest`“ iškvietimas apėjo turinio apsaugos politikos (CSP) direktyvą „`connect-src`“. Šis apėjimas nebenaudojamas. Pridėkite mokėjimo metodo identifikatorių iš „`PaymentRequest`“ API (lauke „`supportedMethods`“) prie CSP direktyvos „`connect-src`“."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "„`StorageType.persistent`“ teikimas nutrauktas. Vietoj to naudokite standartizuotą „`navigator.storage`“."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Elementas „`<source src>`“ su „`<picture>`“ aukštesniuoju elementu netinkamas, todėl jo nepaisoma. Vietoj jo naudokite „`<source srcset>`“."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "„webkitCancelAnimationFrame“ priklauso nuo konkretaus paslaugų teikėjo. Vietoj jo naudokite įprastą metodą „cancelAnimationFrame“."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "„webkitRequestAnimationFrame“ priklauso nuo konkretaus paslaugų teikėjo. Vietoj jo naudokite įprastą metodą „requestAnimationFrame“."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "„HTMLVideoElement.webkitDisplayingFullscreen“ teikimas nutrauktas. Vietoj jos naudokite „Document.fullscreenElement“."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "„HTMLVideoElement.webkitEnterFullScreen()“ teikimas nutrauktas. Vietoj jos naudokite „Element.requestFullscreen()“."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "„HTMLVideoElement.webkitEnterFullscreen()“ teikimas nutrauktas. Vietoj jos naudokite „Element.requestFullscreen()“."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "„HTMLVideoElement.webkitExitFullScreen()“ teikimas nutrauktas. Vietoj jos naudokite „Document.exitFullscreen()“."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "„HTMLVideoElement.webkitExitFullscreen()“ teikimas nutrauktas. Vietoj jos naudokite „Document.exitFullscreen()“."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "„HTMLVideoElement.webkitSupportsFullscreen“ teikimas nutrauktas. Vietoj jos naudokite „Document.fullscreenEnabled“."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Nutrauksime API „`chrome.privacy.websites.privacySandboxEnabled`“ te<PERSON><PERSON><PERSON>, bet ji liks a<PERSON>y<PERSON>, kad būt<PERSON> užtikrintas atgalinis suderinamumas, kol bus išleista M113 versija. Dabar naudokite „`chrome.privacy.websites.topicsEnabled`“, „`chrome.privacy.websites.fledgeEnabled`“ ir „`chrome.privacy.websites.adMeasurementEnabled`“. Žr. https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Apribo<PERSON>s „`DtlsSrtpKeyAgreement`“ pašalintas. Nurodėte šio a<PERSON> „`false`“ vertę, kuri interpretuojama kaip bandymas naudoti pašalintą „`SDES key negotiation`“ metodą. Ši funkcija pašalinta; vietoj jos naudokite paslaugą, kuri palaiko „`DTLS key negotiation`“."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Apribo<PERSON>s „`DtlsSrtpKeyAgreement`“ pa<PERSON>lintas. Nurodėte šio apribojimo „`true`“ vert<PERSON>, kuri neturėjo jokio poveikio, bet galite pašalinti šį apribojimą, kad būtų tvarkingiau."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Atskambinimo parinkties „getStats()“ teikimas nutrauktas ir ji bus pašalinta. Vietoj jos naudokite specifikaciją atitinkančią parinktį „getStats()“."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "„Range.expand()“ teikimas nutrauktas. Vietoj jos naudokite „Selection.modify()“."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Papildomų šaltinių užklausos, kurių URL yra įterptų prisijungimo duomenų (pvz., `**********************/`), yra u<PERSON><PERSON>."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Parinkties „`rtcpMuxPolicy`“ teikimas nutrauktas ir ji bus pašalint<PERSON>."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "Funkcijai „`SharedArrayBuffer`“ reikalinga apsauga nuo subjektų iš kitų domenų. Jei reikia išsamesnės informacijos, žr. https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Funkcijos „`speechSynthesis.speak()`“ be naudo<PERSON><PERSON> suakty<PERSON>imo teiki<PERSON> nutrauktas ir ji bus pašalint<PERSON>."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Plėtiniams reikia pasirinkti apsaugą nuo subjektų iš kitų domenų, norint toliau naudoti „`SharedArrayBuffer`“. Žr. https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Žiniatinklio SQL teikimas nutrauktas. Naudokite „SQLite WebAssembly“ arba indeksuotą duomenų bazę"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Leidimų deskriptoriaus „`window-placement`“ teikimas nutrauktas. Vietoj jo naudokite „`window-management`“. Daugiau pagalbos ieškokite https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Leidimų politikos „`window-placement`“ teikimas nutrauktas. Vietoj jos naudokite „`window-management`“. Daugiau pagalbos ieškokite https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "UTF-16 nepalaikomas atsako JSON naudojant funk<PERSON>ją „`XMLHttpRequest`“"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinchronin<PERSON><PERSON> funk<PERSON> „`XMLHttpRequest`“ pagrindinėje grupėje teikimas nutrauktas dėl jos žalingo poveikio galutinio naudotojo funkcijoms. Jei reikia daugiau pagalbos, žr. https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "„`supportsSession()`“ teikimas nutrauktas. Vietoj to naudokite funkciją „`isSessionSupported()`“ ir patikrinkite priskirtą loginę vertę."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> blo<PERSON><PERSON> la<PERSON>s"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Talpyklos TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Trukmė"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elementas"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Netinkami elementai"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Vietovė"}, "core/lib/i18n/i18n.js | columnName": {"message": "Pavadinimas"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tipas"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON><PERSON> laikas"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Failų perkėlimo dyd<PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON> su<PERSON>"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON> su<PERSON>"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Galima sutaupyti {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Rastas 1 elementas}one{Rastas # elementas}few{Rasti # elementai}many{Rasta # elemento}other{Rasta # elementų}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Galima sutaupyti {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumentas"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON><PERSON><PERSON> re<PERSON>"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Vaizdas"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Laikas nuo sąveikos iki kito žymėjimo"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "<PERSON><PERSON><PERSON><PERSON> potenciali pirmosios įvesties delsa"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medija"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Kiti ištekliai"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sek."}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> failas"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Treč<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "<PERSON><PERSON> viso"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Įkeliant puslapį kilo su pėdsako įrašymu susijusi problema. Dar kartą paleiskite „Lighthouse“. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON><PERSON>, kol bus inicijuotas derintuvės protokolo <PERSON>, baigėsi skirtasis laikas."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Įkeliant puslapį „Chrome“ nesukūrė ekrano kopijų. <PERSON><PERSON><PERSON>krinkite, kad puslapyje yra matomo turinio, tada pabandykite iš naujo paleisti „Lighthouse“. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS serveriams nepavyko nustatyti pateikto domeno."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Įvyko su reikiamo ištekliaus „{artifactName}“ rinkimo priemone susijusi klaida: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Įvyko vid<PERSON><PERSON>s „Chrome“ klaida. <PERSON><PERSON> naujo paleiskite „Chrome“ ir pabandykite iš naujo paleisti „Lighthouse“."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Reikiamo ištekliaus „{artifactName}“ rinkimo priemonė nebuvo paleista."}, "core/lib/lh-error.js | noFcp": {"message": "Puslapyje nėra jokio turinio. Įsitikinkite, kad įkeliant nar<PERSON><PERSON><PERSON><PERSON>s langas veikia priekiniame plane, ir bandykite dar kartą. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Puslapyje nebuvo pateiktas turinys, kuri<PERSON> la<PERSON><PERSON> didžiausiu turiningu žymėjimu (DTŽ). Įsitikinkite, kad puslapyje yra tinkamas DTŽ elementas, tada bandykite dar kartą. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Pateiktas puslapis nėra HTML (pateiktas kaip MIME tipas {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "<PERSON><PERSON> „Chrome“ versija yra per sena ir nepalaiko „{featureName}“. Jei norite peržiūr<PERSON><PERSON> visus rezultatus, naudokite naujesnę versiją."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio už<PERSON> patei<PERSON>. Įsitikinkite, kad testuojate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "„Lighthouse“ nepavyko patikimai įkelti URL, kurio už<PERSON>, nes puslapis ne<PERSON>."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Pateiktame URL nėra tinkamo saugos sertifikato: {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "„Chrome“ neleido įkelti puslapio ir buvo parodytas tarpinis ekranas. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio už<PERSON> patei<PERSON>. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas. (Išsami informacija: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio u<PERSON> patei<PERSON>. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas. (Būsenos kodas: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Puslapis buvo įkeliamas per ilgai. Pasinaudokite ataskaitoje pateiktomis galimybėmis ir sumažinkite puslapio įkėlimo laiką, tada pabandykite iš naujo paleisti „Lighthouse“. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "<PERSON><PERSON><PERSON>, kol „DevTools“ protokolo atsakymas viršys skirtąjį laiką. (Metodas: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON> turinio gavi<PERSON> vir<PERSON> skirtąjį laiką"}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad pateiktas URL netinkamas."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio u<PERSON> patei<PERSON>. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas. (Būsenos kodas: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Puslapio MIME tipas yra XHTML: „Lighthouse“ tikrai nepalaiko šio dokumento tipo"}, "core/user-flow.js | defaultFlowName": {"message": "Naudotoj<PERSON> srautas ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "<PERSON><PERSON><PERSON> at<PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "<PERSON><PERSON><PERSON><PERSON> ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Visos ataskaitos"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Geriausios praktikos pavyzdžiai"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Laipsniškoji žiniatinklio programa"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "PVO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON> kom<PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "<PERSON><PERSON> „Lighthouse“ srauto ataskaitą"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON> srautus"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Naudokite narš<PERSON> at<PERSON>, kad <PERSON>…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Naudokite dienos a<PERSON> ataskaitas, kad gal<PERSON>…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Naudokite la<PERSON> at<PERSON>, kad <PERSON>…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "<PERSON><PERSON><PERSON> „Lighthouse“ našumo balą."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Įvertinti puslapio įkėlimo na<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> turi<PERSON>o žymėjimo ir spartos rod<PERSON>."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Įvertinti laipsniškųjų žiniatinklio programų galimybes."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "A<PERSON><PERSON><PERSON> prita<PERSON>mumo problemas atskiro puslapio programose ar sudėtingose formose."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Įvertinti už sąveikų slypinčių meniu ir NS elementų geriausią praktiką."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Įvertinti iš<PERSON><PERSON><PERSON><PERSON> poslinkius ir „JavaScript“ sąveikų serijų vykdymo laiką."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Atrasti našumo galimybes ir pagerinti ilgai veikiančių puslapių bei atskiro puslapio programų funkcijas."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informatyvi patikra}one{{numInformative} informatyvi patikra}few{{numInformative} informatyvios patik<PERSON>}many{{numInformative} informatyvios patik<PERSON>}other{{numInformative} informatyvių patikrų}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobiliesiems"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Puslapio įkėlimas"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ataskaitose analizuojamas kiekvienas puslapio įkėlimas, v<PERSON><PERSON><PERSON> taip pat, kaip ir originaliose „Lighthouse“ ataskaitose."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} nar<PERSON><PERSON><PERSON> ataskaita}one{{numNavigation} nar<PERSON><PERSON>o ataskaita}few{{numNavigation} nar<PERSON><PERSON>o ataskaitos}many{{numNavigation} nar<PERSON><PERSON>o ataskaitos}other{{numNavigation} nar<PERSON><PERSON>o ataskaitų}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} patikra, kuri gali būti atlikta sėkmingai}one{{numPassableAudits} patikra, kuri gali būti atlikta sėkmingai}few{{numPassableAudits} pat<PERSON><PERSON>, kurios gali būti atliktos s<PERSON>}many{{numPassableAudits} patik<PERSON>, kuri gali būti atlikta sėkmingai}other{{numPassableAudits} patikrų, kurios gali būti atliktos sėkmingai}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} sėkmingai atlikta patikra}one{{numPassed} sėkmingai atlikta patikra}few{{numPassed} sėkmingai atliktos patikros}many{{numPassed} s<PERSON><PERSON><PERSON><PERSON> atliktos patikros}other{{numPassed} sėkmingai atliktų patikrų}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Vidutiniška"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON>ž<PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "<PERSON><PERSON> ataskaitose analizuojamas tam tikros bū<PERSON>, paprastai po naudotojų sąveikų."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} konkretaus momento ataskaita}one{{numSnapshot} konkretaus momento ataskaita}few{{numSnapshot} konkretaus momento ataskaitos}many{{numSnapshot} konkretaus momento ataskaitos}other{{numSnapshot} konkretaus momento ataskaitų}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Su<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Naudotojo sąveikos"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ataskaitose analizuojamas tam tikras laikotarpis, p<PERSON><PERSON><PERSON> tas, per kurį vyko sąveik<PERSON>."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Lai<PERSON><PERSON><PERSON> ataskaita"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} laikotarpio ataskaita}one{{numTimespan} laikotarpio ataskaita}few{{numTimespan} laikotarpio ataskaitos}many{{numTimespan} laikotarpio ataskaitos}other{{numTimespan} laikotarpio ataskaitų}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "„Lighthouse“ naudotojų srauto ataskaita"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Jei teikiate animuotą turinį, naudokite [„`amp-anim`“](https://amp.dev/documentation/components/amp-anim/), kad būt<PERSON> naudojama mažiau centrinio procesoriaus galios, kai turinys nerodoma<PERSON> e<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Apsvarstykite galimybę pateikti visus [„`amp-img`“](https://amp.dev/documentation/components/amp-img/?format=websites) komponentus „WebP“ formatais ir nurodyti atitinkamus kitų naršyklių atsarginius elementus. [Sužinokite daugiau](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Įsitikinkite, kad naudojate [„`amp-img`“](https://amp.dev/documentation/components/amp-img/?format=websites), kad vaizdai būtų įkeliami automatiškai naudojant atidėtąjį įkėlimą. [Sužinokite daugiau](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[<PERSON><PERSON>je pateikdami AMP išdėstymus](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) naudokite tokius įrankius kaip [AMP optimizavimo priemonė](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [AMP dokumentus](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) ir is<PERSON><PERSON>, kad visi stiliai yra palaikomi."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponentas [„`amp-img`“](https://amp.dev/documentation/components/amp-img/?format=websites) palaiko atributą [„`srcset`“](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), kad būt<PERSON> galima nurodyti, k<PERSON><PERSON><PERSON> v<PERSON> naudoti pagal ekrano dydį. [Sužinokite daugiau](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "<PERSON>i pateikiami labai did<PERSON>, apsvarstykite galimybę naudoti virtualų slinkimą naudojant komponentų kūrimo rinkinį (CDK). [Sužinokite daugiau](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Jei norite sumažinti „JavaScript“ grupių dydį, taikykite [mar<PERSON><PERSON><PERSON> lygio kodo skaidymą](https://web.dev/route-level-code-splitting-in-angular/). Taip pat a<PERSON>sty<PERSON>te galimybę išteklius iš anksto įdėti į talpyklą naudojant [pagalbinį „JavaScript“ failą „Angular“](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "<PERSON><PERSON> naudo<PERSON>te „Angular CLI“, įsitikinkite, kad versijos generuojamos gamybos režimu. [Sužinokite daugiau](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "<PERSON><PERSON> naudo<PERSON>te „Angular CLI“, į gamybinę versiją įtraukite šaltinio žemėlapių, kad būtų tikrinamos jūsų grupės. [Sužinokite daugiau](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON> įkelkite ma<PERSON><PERSON><PERSON>, kad būtų galima greičiau naršyti. [Sužinokite daugiau](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Apsvarstykite galimybę naudoti komponentų kūrimo rinkinyje (CDK) esančią paslaugų priemonę „`BreakpointObserver`“ vaizdų atskaitos taškams tvarkyti. [Sužinokite daugiau](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Apsvarstykite galimybę įkelti savo GIF į paslaugą, kuri le<PERSON> jį įterpti kaip HTML5 vaizdo įrašą."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Nurodykite „`@font-display`“ apibrėždami tinkintus temos <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Apsvarstykite galimybę konfigūruoti [„WebP“ vaizdų formatus naudodami vaizdo stiliaus konvertavimo funkciją](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) svetainėje."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Įdiekite [„Drupal“ modulį](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), kuris gal<PERSON>t<PERSON> įkelti vaizdus naudojant atidėtąjį įkėlimą. Naudojant tokius modulius galima atidėti bet kokius ne ekraninius vaizdus, kad būtų padidintas našumas."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Apsvarstykite galimybę naudoti modulį, kad galėtumėte įterpti kritines CSS kalbos ir „JavaScript“ funkcijas, ir naudoti atidėjimo atributą nekritinėms CSS kalbos ar „JavaScript“ funkcijoms."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON>, moduliai ir serverio specifikacijos – visa tai turi įtakos serverio atsako laikui. Galbūt vertėtų surasti geriau optimizuotą temą, atidžiai pasirinkti optimizavimo modulį ir (arba) naujovinti serverį. Prieglobos serveriai turėtų naudoti PHP operacijos kodų talpyklą, atminties talpyklą, kad sumažintų duomenų bazės užklausų skaičių, pvz., „Redis“ ar „Memcached“, bei optimizuotą programos logiką, kad puslapiai būtų sparčiau parengti."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Apsvarstykite galimybę naudoti [interaktyvių vaizdų stilius](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), kad sumažintumėte puslapyje įkeliamus vaizdus. Jei naudojate rodinius keliems turinio elementams rodyti puslapyje, apsvarstykite galimybę įdiegti puslapių numeravimą, kad apribotumėte nurodytame puslapyje rodomų turinio elementų skaičių."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Būtinai įgalinkite funkciją „Apibendrinti CSS kalbos failus“ puslapyje „Administravimas“ » „Konfigūravimas“ » „Diegimas“.  Įsitikinkite, kad „Drupal“ svetainė veikia naudojant bent „Drupal“ 10.1 versiją, kad būtų teikiamas patobulintas išteklių apibendrinimo palaikymas."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Būtinai įgalinkite funkciją „Apibendrinti „JavaScript“ failus“ puslapyje „Administravimas“ » „Konfigūravimas“ » „Diegimas“.  Įsitikinkite, kad „Drupal“ svetainė veikia naudojant bent „Drupal“ 10.1 versiją, kad būtų teikiamas patobulintas išteklių apibendrinimo palaikymas."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Apsvarstykite galimybę pašalinti nenaudojamas CSS kalbos taisykles ir pridėti tik reikalingas „Drupal“ bibliotekas prie atitinkamo puslapio ar komponento puslapyje. Jei reikia išsamios informacijos, žr. [„Drupal“ dokumentų nuorodą](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Kad nustatytumėte pridėtas bibliotekas, pridedan<PERSON><PERSON> nesusijusios CSS kalbos, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusią temą ir (arba) modulį galite nustatyti pagal stiliaus aprašo URL, kai CSS kalbos apibendrinimas išjungtas „Drupal“ svetainėje. Ieškokite temų ir (arba) modulių, kurių sąraše pateikta daug stiliaus aprašų, kurių kodo aprėptyje yra daug raudonos spalvos. Tema ir (arba) modulis įtraukti stiliaus aprašą į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Apsvarstykite galimybę pašalinti nenaudojamus „JavaScript“ išteklius ir pridėti tik reikalingas „Drupal“ bibliotekas prie atitinkamo puslapio ar komponento puslapyje. Jei reikia išsamios informacijos, žr. [„Drupal“ dokumentų nuorodą](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Kad nustatytumėte pridėtas bibliotekas, pridedan<PERSON><PERSON> nesusijusios „JavaScript“ kalbos, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusią temą ir (arba) modulį galite nustatyti pagal scenarijaus URL, kai „JavaScript“ apibendrinimas išjungtas „Drupal“ svetainėje. Ieškokite temų ir (arba) modulių, kurių sąraše pateikta daug scenarijų, kurių kodo aprėptyje yra daug raudonos spalvos. Tema ir (arba) modulis įtraukti scenarijų į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Nustatykite parinktį „Naršyklės ir tarpinio serverio talpyklos maksimalus am<PERSON>“ puslapyje „Administravimas“ » „Konfigūravimas“ » „Diegimas“. Skaitykite apie [„Drupal“ talpyklą ir našumo optimizavimą](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Apsvarstykite galimybę naudoti [modulį](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), kuris automatiškai optimizuoja ir sumažina svetainėje įkeltus vaizdus, išlaikydamas jų kokybę. Be to, įsitikinkite, kad naudojate „Drupal“ teikiamus savuosius [interaktyvių vaizdų stilius](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) visiems svetainėje pateikiamiems vaizdams (pasiekiama naudojant 8 ar naujesnės versijos „Drupal“)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Įdiegiant ir konfigūruojant [modulį](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), kurį naudojant galima teikti naudotojo priemonės šaltinio nurodymus, galima pridėti išankstinio prijungimo arba DNS išankstinės iškvietos ištekliaus nurodymų."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Įsitikinkite, kad naudojate „<PERSON><PERSON>al“ teikiamus savuosius [interaktyvių vaizdų stilius](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (pasiekiama naudojant 8 ar naujesnės versijos „Drupal“). Naudokite interaktyvių vaizdų stilius pateikdami vaizdų failus perž<PERSON> re<PERSON>, per rodinius ar vaizdus, įkeltus naudojant WYSIWYG redagavimo priemonę."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Naudodami [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Optimize Fonts`“, kad automatiškai naudotumėte PAP funkciją „`font-display`“ ir užti<PERSON>tum<PERSON>, kad tekstas matoma<PERSON> naudo<PERSON>, kol įkeliami žiniatinklio šriftai."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Next-Gen Formats`“, kad konvertuotum<PERSON>te vaizdus į „WebP“."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`<PERSON>zy Load Images`“, kad atidėtumėte vaizdų ne ekrane įkėlimą, kol jų prireiks."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymus „`Critical CSS`“ ir „`Script Delay`“, kad atidėtumėte nebūtinus JS / PAP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Ezoic“ TPT](https://pubdash.ezoic.com/speed/caching) savo turinį išsaugokite talpykloje visą pasaulį jungiančiame tinkle ir pagerinkite laiką iki pirmojo baito."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Na<PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Minify CSS`“, kad automatiškai sumažintumėte PAP ir tinklo naudingąsias apkrovas."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Na<PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Minify Javascript`“, kad automatiškai sumažintumėte JS ir tinklo naudingąsias apkrovas."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Naudodami [„E<PERSON> Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Remove Unused CSS`“, kad padėtum<PERSON>te išspręsti šią problemą. Bus nustatytos PAP klasės, kurios iš tiesų naudojamos kiekviename jūsų svetainės puslapyje, ir p<PERSON><PERSON>lint<PERSON> visos kitos, kad failas nebūtų didelis."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Na<PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Efficient Static Cache Policy`“, kad nustatytumėte rekomenduojamas statinių išteklių vertes saugojimo talpykloje antraštėje."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Next-Gen Formats`“, kad konvertuotum<PERSON>te vaizdus į „WebP“."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Naudo<PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Pre-Connect Origins`“ ir automatiškai pridėkite šaltinio nurodymus „`preconnect`“, kad ryšys su svarbiais trečiųjų šalių šaltiniais būtų užmezgamas iš anksto."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Na<PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymus „`Preload Fonts`“ ir „`Preload Background Images`“, kad pridėtumėte nuorodų „`preload`“ ir suteiktumėte pirmenybę gaunamiems ištekliams, kurių užklausos šiuo metu teikiamos vėliau įkeliant puslapį."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Na<PERSON><PERSON><PERSON> [„Ezoic Leap“](https://pubdash.ezoic.com/speed) įgalinkite nustatymą „`Resize Images`“, kad pakeistumėte vaizdų dydį pagal įrenginį ir sumažintumėte tinklo naudingąją apkrovą."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Naudokite komponentą „`gatsby-plugin-image`“ vietoj „`<img>`“ vaizdų formatui automatiškai optimizuoti. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Naudokite komponentą „`gatsby-plugin-image`“ vietoj „`<img>`“ vaizdams automatiškai asinchroniškai įkelti. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Naudokite komponentą „`gatsby-plugin-image`“ ir nustatykite „`loading`“ pasi<PERSON><PERSON><PERSON><PERSON> kaip „`eager`“. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Naudokite „`Gatsby Script API`“, kad atid<PERSON>tumėte neesminių trečiosios šalies scenarijų įkėlimą. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Naudokite papildinį „`PurgeCSS` `Gatsby`“, kad pa<PERSON>te nenaudojamas taisykles iš stiliaus apraš<PERSON>. [Sužinokite daugiau](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Naudokite „`Webpack Bundle Analyzer`“, kad aptiktum<PERSON>te nenaudojamą „JavaScript“ kodą. [Sužinokite daugiau](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Konfigūruokite nekintamų išteklių saugojimą talpykloje. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Naudokite komponentą „`gatsby-plugin-image`“ vietoj „`<img>`“ vaizdų kokybei koreguoti. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Naudokite komponentą „`gatsby-plugin-image`“ atitinkamam „`sizes`“ nustatyti. [Sužinokite daugiau](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Apsvarstykite galimybę įkelti savo GIF į paslaugą, kuri le<PERSON> jį įterpti kaip HTML5 vaizdo įrašą."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Apsvarstykite galimybę naudoti [papildinį](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) arba paslaug<PERSON>, kuri automatiškai konvertuotų įkeltus vaizdus į optimalius formatus."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Įdiekite [atidėtojo įkėlimo „<PERSON>om<PERSON>“ papildinį](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), leidžiantį atidėti bet kokius ne ekraninius vaizdus, arba perjunkite į šią funkciją teikiantį šabloną. Nuo „Joomla“ 4.0 versijos visi nauji vaizdai [automatiškai](https://github.com/joomla/joomla-cms/pull/30748) gaus svarbų atributą „`loading`“."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "<PERSON>ra įvairių „<PERSON>om<PERSON>“ papildini<PERSON>, kuriuos naudodami galite [įterpti svarbių išteklių](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) arba [atidėti mažiau svar<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Atminkite, kad dėl šių papildinių teikiamo optimizavimo gali būti pažeistos jūsų šablonų ar papildinių funkcijos, todėl reikės juos nuodugniai patikrinti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON>, plėtiniai ir serverio specifikacijos – visa tai turi įtakos serverio atsako laikui. Galbūt vertėtų surasti geriau optimizuotą <PERSON>ą, atidžiai pasirinkti optimizavimo plėtinį ir (arba) naujovinti serverį."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Apsvarstykite galimybę rodyti ištraukas straipsnių kategorijose (pvz., naudodami daugiau informacijos skaitymo nuorodą), sumažinti nurodytame puslapyje rodomų straipsnių skaičių, suskaidyti ilgus įrašus į kelis puslapius arba naudoti komentarų atidėtojo įkėlimo papildinį."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Įvairūs [„<PERSON><PERSON><PERSON>“ plėtiniai](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) gali padėti svetainei sparčiau veikti su<PERSON>, sug<PERSON>dindami CSS kalbos stilius ar pašalindami jų nereikalingus duomenis. Be to, yra šias funkcijas teikiančių šablonų."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Įvairūs [„<PERSON><PERSON><PERSON>“ plėtiniai](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) gali padėti svetainei sparčiau veikti su<PERSON>, suglau<PERSON>dami scenarijus ar pašalindami jų nereikalingus duomenis. Be to, yra šias funkcijas teikiančių šablonų."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Apsvarstykite galimybę sumažinti [„Joom<PERSON>“ plėtinių](https://extensions.joomla.org/), įkeliančių nenaudojamą CSS kalbą jūsų puslapyje, skaičių arba juos pakeisti. Ka<PERSON> nustat<PERSON>, <PERSON><PERSON><PERSON><PERSON> ne<PERSON>s CSS kalbos, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusią temą ir (arba) papildinį galite nustatyti pagal stiliaus aprašo URL. Ieškokite papildinių, kurių sąraše pateikta daug stiliaus aprašų, kurių kodo aprėptyje yra daug raudonos spalvos. Papildinys turėtų įtraukti į eilę stiliaus apraš<PERSON>, tik jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Apsvarstykite galimybę sumažinti [„Jo<PERSON><PERSON>“ plėtinių](https://extensions.joomla.org/), įkeliančių nenaudojamą „JavaScript“ jūsų puslapyje, skaičių arba juos pakeisti. Kad nustatyt<PERSON><PERSON> papildinius, <PERSON><PERSON><PERSON><PERSON> ne<PERSON>iju<PERSON>s JS, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusį plėtinį galite nustatyti pagal scenarijaus URL. Ieškokite plėtinių, kurių sąraše pateikta daug scenarijų, kurių kodo aprėptyje yra daug raudonos spalvos. Plėtinys įtraukti scenarijų į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Skaitykite apie [nar<PERSON><PERSON><PERSON><PERSON><PERSON> saugojimo talpykloje funkciją sistemoje „Joomla“](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Apsvarstykite galimybę naudoti [vaizdų optimizavimo papildinį](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), leidžiantį suglaudinti vaizdus išlaikant kokybę."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Apsvarstykite galimybę naudoti [interaktyvių vaizdų papildinį](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), kad gal<PERSON>tumėte naudoti interaktyvius vaizdus turinyje."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "<PERSON><PERSON><PERSON> glaudin<PERSON> galite įgalinti įgalinę „Gzip“ puslapių glaudinimą sistemoje „Joomla“ („Sistema“ > „Visuotinė konfigūracija“ > „Serveris“)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Jei negrupuojate „JavaScript“ išteklių, apsvarstykite galimybę naudoti [presavimo įrankį](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Išjunkite platformos „Magento“ įtaisytą [„JavaScript“ grupavimą ir nereikalingų duomenų pašalinimą](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ir apsvarstykite galimybę vietoje jų naudoti [presavimo įrankį](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "[A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tin<PERSON>](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) nurodykite „`@font-display`“."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Apsvarstykite galimybę [„Magento Marketplace“](https://marketplace.magento.com/catalogsearch/result/?q=webp) ieškoti įvairių trečiųjų šalių plėtinių, kad galėtumėte pasinaudoti naujesniais vaizdo formatais."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Apsvarstykite galimybę keisti produkto ir ka<PERSON><PERSON>, kad gal<PERSON>te išnaudoti žiniatinklio platformos [atidėtojo įkėlimo](https://web.dev/native-lazy-loading) funkciją."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Naudokite platformos „Magento“ [„Varnish“ integravimą](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Parduo<PERSON><PERSON><PERSON><PERSON> skiltyje „Kūrėjų nustatymai“ įgalinkite parinktį „Pašalinti nereikalingus CSS failų duomenis“. [Sužinokite daugiau](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [„Terser“](https://www.npmjs.com/package/terser) pašalinkite visų „JavaScript“ išteklių nereikalingus duomenis iš statinio turinio diegimo ir išjunkite įtaisytą nereikalingų duomenų pašalinimo funkciją."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Išjunkite platformos „Magento“ įtaisytą [„JavaScript“ grupavimą](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Apsvarstykite galimybę [„Magento Marketplace“](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) ieškoti įvairių trečiųjų šalių plėtinių, kad galėtumėte optimizuoti vaizdus."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "[Keičiant temos išdėstymą](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) galima pridėti išankstinio prijungimo arba DNS išankstinės iškvietos ištekliaus nurodymų."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> „`<link rel=preload>`“ galima pridėti [keičiant temos išdėstymą](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Naudokite komponentą „`next/image`“ vietoj „`<img>`“ vaizdų formatui automatiškai optimizuoti. [Sužinokite daugiau](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Naudokite komponentą „`next/image`“ vietoj „`<img>`“ vaizdams automatiškai asinchroniškai įkelti. [Sužinokite daugiau](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON><PERSON> komponentą „`next/image`“ nustatykite prioritetą į „True“ ir iš anksto įkelkite DTŽ vaizdą. [Sužinokite daugiau](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Naudokite komponentą „`next/script`“, kad atid<PERSON>te neesminių trečiosios šalies scenarijų įkėlimą. [Sužinokite daugiau](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Naudokite komponentą „`next/image`“, kad <PERSON><PERSON><PERSON>, jog visada nustatomas tinkamas vaizdų dydis. [Sužinokite daugiau](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Apsvarstykite galimybę nustatyti „`PurgeCSS`“ konfigūruodami „`Next.js`“, kad pa<PERSON>lintumėte nenaudojamas taisykles iš stiliaus aprašų. [Sužinokite daugiau](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Naudokite „`Webpack Bundle Analyzer`“, kad aptiktum<PERSON>te nenaudojamą „JavaScript“ kodą. [Sužinokite daugiau](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Apsvarstykite galimybę naudoti „`Next.js Analytics`“ norėdami įvertinti programos realų našumą. [Sužinokite daugiau](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigūruokite nekintamų išteklių ir „`Server-side Rendered`“ (SSR) puslapių saugojimą talpykloje. [Sužinokite daugiau](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Naudokite komponentą „`next/image`“ vietoj „`<img>`“ vaizdų kokybei koreguoti. [Sužinokite daugiau](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Naudokite komponentą „`next/image`“ atitinkamam „`sizes`“ nustatyti. [Sužinokite daugiau](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Įgalinkite glaudinimą Next.js serveryje. [<PERSON><PERSON><PERSON><PERSON><PERSON> daugiau](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Susisiekite su paskyros valdytoju, kad įgalintumėte [„`HTML Lazy Load`“](https://support.nitropack.io/hc/en-us/articles/17144942904337). Sukonfigūravus jį bus suteikta pirmenybė ir optimizuojamas puslapio pateikimo našumas."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Naudokite „NitroPack“ [„`Override Font Rendering Behavior`“](https://support.nitropack.io/hc/en-us/articles/16547358865041) parinktį ir nustatykite norimą CSS kalbos šrifto rodymo ta<PERSON>yklės vertę."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Naudokite [„`Image Optimization`“](https://support.nitropack.io/hc/en-us/articles/16547237162513), kad automatiškai konvertuotumėte vaizdus į „WebP“."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Atidėkite ne ekrano vaizdus įgalindami [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Įgalinkite [„`Remove render-blocking resources`“](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) „NitroPack“, kad pradinis įkėlimas būtų trumpesnis."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Padidinkite serverio atsako laiką ir optimizuokite suvokiamą našumą suaktyvindami [„`Instant Load`“](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Įgalinkite [„`Minify resources`“](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) saugojimo talpykloje nustatymuose, kad sumažintumėte CSS kalbos, HTML ir „JavaScript“ failų dydį, kad būtų greičiau įkeliama."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Įgalinkite [„`Minify resources`“](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) saugojimo talpykloje nustatymuose, kad sumažintumėte JS, HTML ir CSS kalbos failų dydį, kad būtų greičiau įkeliama."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Įgalinkite [„`Reduce Unused CSS`“](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS), kad <PERSON><PERSON><PERSON><PERSON>te CSS kalbos taisykles, kurios netaiko<PERSON> š<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Konfigūruokite [„`Delayed Scripts`“](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) „NitroPack“, kad atid<PERSON>tum<PERSON>te scenarijų įkėlimą, kol jų prireiks."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Pasirinkite [„`Improve Server Response Time`“](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) funkciją „`Caching`“ meniu ir koreguokite puslapio talpyklos galioji<PERSON> laik<PERSON>, kad pagerin<PERSON>te įkėlimo laiką ir naudotojų patirtį."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Automatiškai suglaudinkite, optimizuokite ir konvertuokite vaizdus į „WebP“ įgalinę nustatymą [„`Image Optimization`“](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Įgalinkite [„`Adaptive Image Sizing`“](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site), kad i<PERSON> anks<PERSON>te optimizuoti vaizdus ir pritaikyti juos pagal sudėtinių failų, kuriuose jie patei<PERSON>ami, matmenis visuose įrenginiuose."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Naudokite [„`Gzip compression`“](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) „NitroPack“, kad sumažintumėte į naršyklę siunčiamų failų dydį."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Naudokite komponentą „`nuxt/image`“ ir nustatykite „`format=\"webp\"`“. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Naudokite komponentą „`nuxt/image`“ ir nustatykite „`loading=\"lazy\"`“ ne ekraniniams vaizda<PERSON>. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON><PERSON> komponentą „`nuxt/image`“ nustatykite „`preload`“ DTŽ vaizdui. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Naudokite komponentą „`nuxt/image`“ ir nustatykite aiškius „`width`“ ir „`height`“. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Naudokite komponentą „`nuxt/image`“ ir nustatykite atitinkamą „`quality`“. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Naudokite komponentą „`nuxt/image`“ ir nustatykite atitinkamą „`sizes`“. [Sužinokite daugiau](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Pakeiskite animuotus GIF failus vaizdo įrašu](https://web.dev/replace-gifs-with-videos/), taip tinklalapis bus įkeliamas greičiau. Taip pat apsvarstykite galimybę naudoti modernius failų formatus, pvz., [„WebM“](https://web.dev/replace-gifs-with-videos/#create-webm-videos) arba [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), taip pagerinsite glaudinimo efektyvumą daugiau nei 30 proc., palyginti su dabartiniu naujausiu vaizdo kodeku – VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Apsvarstykite galimybę naudoti [papildinį](https://octobercms.com/plugins?search=image) arba paslaug<PERSON>, kuri automatiškai konvertuotų įkeltus vaizdus į optimalius formatus. [„WebP“ nenuostolingai suglaudinti vaizdai](https://developers.google.com/speed/webp) yra 26 proc. maž<PERSON>ni, palyginti su PNG, ir 25–34 proc. maž<PERSON>ni, palyginti su JPEG formato tapataus SSIM kokybės indekso vaizdais. Apsvarstykite galimybę naudoti ir kitą naujos kartos vaizdo formatą – [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Apsvarstykite galimybę įdiegti [atidėtojo vaizdų įkėlimo papildinį](https://octobercms.com/plugins?search=lazy), kuriuo galima atidėti bet kokius ne ekraninius vaizdus, arba perjunkite į šią funkciją teikiantį šabloną. Be to, apsvarstykite galimybę naudoti [AMP papildinį](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "<PERSON>ra <PERSON> papil<PERSON>, kuriuos naudodami galite [įterpti svarbių išteklių](https://octobercms.com/plugins?search=css). Šie papildiniai gali sugadinti kitus papildinius, todėl reikia bandyti atidžiai."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Temos, papildiniai ir serverio specifikacijos – visa tai turi įtakos serverio atsako laikui. Apsvarstykite galimybę paieškoti geriau optimizuotos temos, atidžiau pasirinkti optimizavimo papildinį ir (arba) naujovinti serverį. Platfomoje „October CMS“ kūrėjams taip pat leidžiama naudoti atributą[„`Queues`“](https://octobercms.com/docs/services/queues), kad galėtų atidėti daug laiko reikalaujančių užduočių, pvz., el. laiško siuntimo, apdorojimą. Tai žymiai paspartina žiniatinklio užklausas."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Apsvarstykite galimybę rodyti ištraukas įrašų sąrašuose (pvz., naudo<PERSON><PERSON> my<PERSON> „`show more`“), sumažinti nurodytame tinklalapyje rodomų įrašų skaičių, suskaidyti ilgus įrašus į kelis tinklalapius arba naudoti komentarų atidėtojo įkėlimo papildinį."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "<PERSON><PERSON> [papildini<PERSON>](https://octobercms.com/plugins?search=css), kuriuos naudodami galite paspartinti svetain<PERSON>, sujungę ir suglaudinę stilius bei pašalinę nereikalingus duomenis. Pasinaudoję kompiliavimo procesu nereikalingiems duomenims iš anksto <PERSON>, galite paspartinti kūrim<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "<PERSON><PERSON> [papildini<PERSON>](https://octobercms.com/plugins?search=javascript), kuriuos naudodami galite paspartinti svetain<PERSON>, sujungę ir suglaudinę scenarijus bei pašalinę nereikalingus duomenis. Pasinaudoję kompiliavimo procesu nereikalingiems duomenims iš anksto p<PERSON>, galite paspartinti kūrim<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Apsvarstykite galimybę per<PERSON> [papildin<PERSON>](https://octobercms.com/plugins) svetainėje įkeldami nenaudojamą CSS. Norėdami nustatyti, kurie plėtiniai prideda nenaudojamą CSS, vykdykite [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome“ kūrėjo įrankius. Susijusią temą ir (arba) papildinį nustatykite pagal stiliaus aprašo URL. Ieškokite papildinių su daug stiliaus aprašų, kuriuose naudojamoje kodo dalyje yra daug raudonų sričių. Papildinys turėtų pridėti stiliaus aprašą tik jei jis tikrai naudojamas tinklalapyje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Apsvarstykite galimybę per<PERSON> [papildinius](https://octobercms.com/plugins?search=javascript), įkeliančius nenaudojamą „JavaScript“ tinklalapyje. Norėdami nustatyti, kurie plėtiniai prideda nenaudojamą „JavaScript“, vykdykite [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome“ kūrėjo įrankius. Susijusią temą ir (arba) papildinį nustatykite pagal scenarijaus URL. Ieškokite papildinių su daug scenarijų, kuriuose naudojamoje kodo dalyje yra daug raudonų sričių. Papildinys turėtų pridėti scenarijų tik jei jis tikrai naudojamas tinklalapyje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Skaitykite apie tai, [kaip išvengti nereikalingų tinklo užklausų naudojant HTTP talpyklą](https://web.dev/http-cache/#caching-checklist). Yra daugybė [papildinių](https://octobercms.com/plugins?search=Caching), kuriuos naudodami galite paspartinti išsaugojimą talpykloje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Apsvarstykite galimybę naudoti [vaizdų optimizavimo papildinį](https://octobercms.com/plugins?search=image), kuriuo galima suglaudinti vaizdus išlaikant kokybę."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Įkelkite vaizdus tiesiai į medijos biblioteką, kad būtų pasiekiami reikiami vaizdų dydžiai. Apsvarstykite galimybę naudoti [dydžio keitimo filtrą](https://octobercms.com/docs/markup/filter-resize) arba [vaizdo dydžio keitimo papildinį](https://octobercms.com/plugins?search=image), kad būtų naudojami optimalaus dydžio vaizdai."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Įgalinkite teksto glaudin<PERSON> žiniatinklio serverio konfigūracijoje."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Jei puslapyje pateikiate daug pasikartojančių elementų, apsvarstykite galimybę naudoti langų pateikimo biblioteką, pvz., „`react-window`“, kad sumažintumėte sukurtų DOM mazgų skaičių. [Sužinokite daugiau](https://web.dev/virtualize-long-lists-react-window/). Be to, sumažinkite nebūtinus pakartotinius pateikimus naudodami elementą [„`shouldComponentUpdate`“](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [„`PureComponent`“](https://reactjs.org/docs/react-api.html#reactpurecomponent) arba [„`React.memo`“](https://reactjs.org/docs/react-api.html#reactmemo) ir [praleiskite efektus](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) tik iki to laiko, kai bus pakeistos tam tikros p<PERSON>, jei naudojate „`Effect` Hook“ siekdami pagerinti vykdymo laiko našumą."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON>i naudo<PERSON><PERSON> „React“ mar<PERSON><PERSON><PERSON> par<PERSON>, sumažinkite komponento „`<Redirect>`“ naudojimą [parenkant maršrutą](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Jei serveryje pateikiate „React“ komponentų, apsvarstykite galimybę naudoti „`renderToPipeableStream()`“ arba „`renderToStaticNodeStream()`“, kad klientas galėtų gauti ir užpildyti skirtinga<PERSON> žym<PERSON> da<PERSON>, o ne viską iš karto. [Sužinokite daugiau](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Jei versijų sistemoje iš CSS failų nereikalingi duomenys pašalinami automatiškai, įsitikinkite, kad pritaikote gamybinę programos versiją. Galite tai patikrinti naudodami „React“ kūrėjo įrankių plėtinį. [Sužinokite daugiau](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Jei versijų sistemoje iš JS failų nereikalingi duomenys pašalinami automatiškai, įsitikinkite, kad pritaikote gamybinę programos versiją. Galite tai patikrinti naudodami „React“ kūrėjo įrankių plėtinį. [Sužinokite daugiau](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON>i nepateikiate serveryje, [išskaidykite „JavaScript“ grupes](https://web.dev/code-splitting-suspense/) naudodami „`React.lazy()`“. Kitu atveju išskaidykite kodą naudodami trečiosios šalies biblioteką, pvz., [„loadable-components“](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Naudokite „React“ kūrimo įrankių analizės įrankį (kurį naudojant pasitelkiama analizės įrankio API) komponentų pateikimo našumui įvertinti. [Sužinokite daugiau.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Įdėkite vaizdo įrašus į „`VideoBoxes`“, tinkinkite juos naudodami „`Video Masks`“ arba pridėkite „`Transparent Videos`“. [Sužinokite daugiau](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Įkelkite vaiz<PERSON> naudodami „`Wix Media Manager`“, kad jie būtų automatiškai teikiami kaip „WebP“. Raskite [daugiau būdų optimizuoti](https://support.wix.com/en/article/site-performance-optimizing-your-media) svetainės mediją."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Svetainės informacijos suvestinės skirtuke „`Custom Code`“ pridėdami [trečiosios šalies kodą](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site), kodo pabaigoje patik<PERSON>te, ar jis yra atidėtas arba įkeltas. Jei įmanoma, naudokite „Wix“ [integravimą](https://support.wix.com/en/article/about-marketing-integrations) svetainėje įterpdami rinkodaros įrankių. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "„Wix“ naudoja TPT ir saugojimą talpykloje, kad kuo greičiau pateiktų atsakymus daugumai lankytojų. Apsvarstykite galimybę [neautomatiškai įgalinti saugojimo talpykloje funkciją](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) svet<PERSON>, ypač jei naudojate „`Velo`“."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Svetainės informacijos suvestinės skirtuke „`Custom Code`“ peržiūrėkite trečiosios šalies kodą, kurį pridėjote prie svetainės, ir palikite tik paslaugas, kurios būtino<PERSON> jūsų svetainei. [Sužinokite daugiau](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Apsvarstykite galimybę įkelti savo GIF į paslaugą, kuri le<PERSON> jį įterpti kaip HTML5 vaizdo įrašą."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Apsvarstykite galimybę naudoti [„Performance Lab“](https://wordpress.org/plugins/performance-lab/) papildinį, kad automatiškai konvertuotumėte įkeltus JPEG vaizdus į „WebP“ ten, kur palaikoma."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Įdiekite [atidėtojo įkėlimo „WordPress“ papildinį](https://wordpress.org/plugins/search/lazy+load/), leidžiantį atidėti bet kokius ne ekraninius vaizdus, arba perjunkite į šią funkciją teikiančią temą. Be to, apsvarstykite galimybę naudoti [AMP papildinį](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "<PERSON>ra įvairių „WordPress“ papildini<PERSON>, kuriuos naudodami galite [įterpti svarbių išteklių](https://wordpress.org/plugins/search/critical+css/) arba [atidėti mažiau svar<PERSON>](https://wordpress.org/plugins/search/defer+css+javascript/). Atminkite, kad dėl šių papildinių teikiamo optimizavimo gali būti pažeistos jūsų temos ar papildinių funkcijos, todėl tikriausiai turėsite atlikti kodo pakeitimų."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Te<PERSON>, papildiniai ir serverio specifikacijos – visa tai turi įtakos serverio atsako laikui. Galbūt vertėtų surasti geriau optimizuotą temą, atidžiai pasirinkti optimizavimo papildinį ir (arba) naujovinti serverį."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Apsvarstykite galimybę rodyti ištraukas įrašų sąra<PERSON><PERSON>se (pvz., naudodami daugiau elementų žymą), sumažinti nurodytame puslapyje rodomų įrašų skaičių, suskaidyti ilgus įrašus į kelis puslapius arba naudoti komentarų atidėtojo įkėlimo papildinį."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Įvairūs [„WordPress“ papildiniai](https://wordpress.org/plugins/search/minify+css/) gali padėti svetainei sparčiau veikti sujungdami, suma<PERSON><PERSON>mi ar suglaudindami stilius. Be to, galb<PERSON>t norėsite pasinaudoti kūrimo procesu, kad suma<PERSON> i<PERSON> (jei tai įmanoma)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Įvairūs [„WordPress“ papildiniai](https://wordpress.org/plugins/search/minify+javascript/) gali padėti svetainei sparčiau veikti sujun<PERSON>dam<PERSON>, suma<PERSON><PERSON>mi ar suglaudindami scenarijus. Be to, galb<PERSON><PERSON> norėsite pasinaudoti kūrimo procesu, kad suma<PERSON> i<PERSON> (jei tai įmanoma)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Apsvarstykite galimybę sumažinti [„WordPress“ papildinių](https://wordpress.org/plugins/), įkeliančių nenaudojamą CSS kalbą jūsų puslapyje, skaičių arba juos pakeisti. Kad nustat<PERSON> papildinius, pride<PERSON><PERSON><PERSON> nesusiju<PERSON>s CSS kalbos, pabandykite vykdyti [kodo aprėpties procesą](https://developer.chrome.com/docs/devtools/coverage/) naudodami „Chrome DevTools“. Susijusią temą / papildinį galite nustatyti pagal stiliaus failo URL. Ieškokite papildinių, kurių sąraše pateikta daug stiliaus failų, kurių kodo aprėptyje yra daug raudonos spalvos. Papildinys įtraukti stiliaus failą į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Apsvarstykite galimybę sumažinti [„WordPress“ papildinių](https://wordpress.org/plugins/), įkeliančių nenaudojamą „JavaScript“ jūsų puslapyje, skaičių arba juos pakeisti. Kad nustatyt<PERSON><PERSON> papildinius, <PERSON><PERSON><PERSON><PERSON> nesusiju<PERSON>s JS, pabandykite vykdyti [kodo aprėpties procesą](https://developer.chrome.com/docs/devtools/coverage/) naudodami „Chrome DevTools“. Susijusią temą / papildinį galite nustatyti pagal scenarijaus URL. Ieškokite papildinių, kurių sąraše pateikta daug scenarijų, kurių kodo aprėptyje yra daug raudonos spalvos. Papildinys įtraukti scenarijų į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Skaitykite apie [nar<PERSON><PERSON><PERSON><PERSON><PERSON> saugojimo talpykloje funkciją sistemoje „WordPress“](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Apsvarstykite galimybę naudoti [vaizdų optimizavimo „WordPress“ papildinį](https://wordpress.org/plugins/search/optimize+images/), leidžiantį suglaudinti vaizdus išlaikant kokybę."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Įkelkite vaizdus tiesiai per [medijos biblioteką](https://wordpress.org/support/article/media-library-screen/), kad būtų pasiekiami reikiami vaizdų dydžiai, tada įterpkite juos iš medijos bibliotekos arba naudokite vaizdų valdiklį, kad <PERSON><PERSON><PERSON>, jog naudojami optimalaus dydžio vaizda<PERSON> (įskaitant tuos, kurie naudojami interaktyviems atskaitos taškams). Stenkitės nenaudoti vaizdų „`Full Size`“, nebent galima naudoti tokių matmenų vaizdus. [Sužinokite daugiau](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Galite įgalinti teksto glaudinimą žiniatinklio serverio konfigūracijoje."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "„WP Rocket“ skirtuke „Vaizdo optimizavimas“ įgalinkite funkciją „Imagify“, kad konvertuotumėte vaizdus į „WebP“."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Įgalinkite [„LazyLoad“](https://docs.wp-rocket.me/article/1141-lazyload-for-images) si<PERSON><PERSON><PERSON> „WP Rocket“, kad išspręstumėte šią rekomendaciją. Ši funkcija uždelsia vaizdų įkėlimą, kol lankytojas slenka žemyn puslapiu ir iš <PERSON>ų turi juos matyti."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "„WP Rocket“ įgalinkite [„Remove Unused CSS“](https://docs.wp-rocket.me/article/1529-remove-unused-css) ir [„Load JavaScript deferred“](https://docs.wp-rocket.me/article/1265-load-javascript-deferred), kad išspręstumėte šią rekomendaciją. Šios funkcijos atitinkamai optimizuos CSS ir „JavaScript“ failus, kad neblokuotų puslapio pateikimo."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Norėdami pa<PERSON>linti ši<PERSON> problemą, „WP Rocket“ įgalinkite [„Minify CSS files“](https://docs.wp-rocket.me/article/1350-css-minify-combine). Visi tarpai ir komentarai jūsų svetainės CSS failuose bus pa<PERSON>linti, kad failas būtų mažesnis ir greičiau atsisiunčiamas."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Norėdami išspręsti šią problemą, „WP Rocket“ įgalinkite [„Minify JavaScript files“](https://docs.wp-rocket.me/article/1351-javascript-minify-combine). Tušti tarpai ir komentarai bus pašalinti iš „JavaScript“ failų, kad jų dydis būtų mažesnis ir būtų galima greičiau atsisiųsti."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Norėdami išspręsti š<PERSON> problemą, „WP Rocket“ įgalinkite [„Minify CSS files“](https://docs.wp-rocket.me/article/1529-remove-unused-css). Sumažina<PERSON> puslapio dyd<PERSON>, pašalinant visas CSS kalbas ir stilia<PERSON>, k<PERSON><PERSON> naudo<PERSON>, i<PERSON><PERSON><PERSON>t tik naudojamą kiekvieno puslapio CSS kalbą."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "„WP Rocket“ įgalinkite [„Delay JavaScript execution“](https://docs.wp-rocket.me/article/1349-delay-javascript-execution), kad išspręstumėte šią problemą. Tai padės pagerinti puslapio įkėlimą uždelsiant scenarijų vykdymą iki naudotojų sąveikos. <PERSON>i svetain<PERSON> yra „iframe“, galite naudoti „WP Rocket“ [„LazyLoad“, skirtą „iframe“ ir vaizdo įrašams](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos), ir [pakeisti „YouTube“ „iframe“ peržiūros vaizdu](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "„WP Rocket“ skirtuke „<PERSON>aiz<PERSON> optimizavimas“ įgalinkite funkciją „Imagify“ ir paleiskite masinį optimizavimą, kad suglaudintum<PERSON>te vaizdus."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "„WP Rocket“ naudokite [„Prefetch DNS Requests“](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests), ka<PERSON> <PERSON><PERSON> „dns-prefetch“ ir paspartintumėte ryšį su išoriniais domenais. Be to, „WP Rocket“ automatiškai prideda „preconnect“ prie [„Google Fonts“ domeno](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) ir bet kurio CNAME, pridė<PERSON> per funkcij<PERSON> [„Enable CDN“](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Jei norite išspręsti šią šriftų problemą, „WP Rocket“ įgalinkite[„Remove Unused CSS“](https://docs.wp-rocket.me/article/1529-remove-unused-css). Svarbiausi svetainės šriftai bus iš anksto įkelti su prioritetu."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON><PERSON>."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON><PERSON><PERSON> rodinį"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Didžiausia s<PERSON>usio kelio <PERSON>:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopijuoti JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Perjungti Tamsiąją temą"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Išskleista spausdintinė kopija"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> su<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaip „G<PERSON>“"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Išsaugoti kaip HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON> kaip <PERSON>"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> p<PERSON>"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON><PERSON><PERSON> naudo<PERSON> žiūriklį"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON><PERSON>!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Ataskaitos klaida: n<PERSON><PERSON> pat<PERSON> informaci<PERSON>"}, "report/renderer/report-utils.js | expandView": {"message": "Išskleisti rodinį"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | footerIssue": {"message": "Pateikti problemą"}, "report/renderer/report-utils.js | hide": {"message": "Slėpti"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratorijos duomenys"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[„Lighthouse“](https://developers.google.com/web/tools/lighthouse/) dabartinio puslapio analizė emuliuotame mobiliojo ryšio tinkle. Vertės yra a<PERSON>s ir gali skirt<PERSON>."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>s reikia patikrinti <PERSON>automa<PERSON>"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Galimybė"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Numatomos <PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> [atnaujintų „Chrome“ diegiamumo kriterijų](https://developer.chrome.com/blog/update-install-criteria), „Lighthouse“ būsimame leidime nebebus naudojama LŽP kategorija. Peržiūrėkite [atnaujintus LŽP dokumentus](https://developer.chrome.com/docs/devtools/progressive-web-apps/), skirtus būsimiems LŽP bandymams."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Pirminis pu<PERSON> įkėlimas"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Glausta informacija konkrečiu metu"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Naudotojų sąveikų laikotarpis"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Tinkintas duomenų srauto <PERSON>s"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON> stalini<PERSON> kom<PERSON>"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emuliuotas „Moto G Power“"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "„Axe“ versija"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Neribojamas duomenų srautas iš centrinio procesoriaus / atminties galios"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Centrinio procesoriaus pralaidumo ribo<PERSON>s"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Įrenginys"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Ekrano emuliacija"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> (tinklas)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "<PERSON><PERSON><PERSON> viename <PERSON>"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Šie duomenys gauti išanalizavus seansą viename pusla<PERSON>, o lauko duomenys apibendrina daug seansų."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Lėto 4G duomenų srauto ribojimas"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Nežinoma"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "<PERSON><PERSON><PERSON>, aktualias:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Sutraukti fragmentą"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Išplėsti fragmentą"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Rodyti trečiųjų šalių išteklius"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Pateikta pagal aplinką"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Paleidžiant „Lighthouse“ kilo problemų."}, "report/renderer/report-utils.js | unattributable": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "<PERSON>ert<PERSON><PERSON> yra a<PERSON> ir gali skirt<PERSON>. [<PERSON><PERSON><PERSON><PERSON> įvertinimas a<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) tiesiogiai pagal š<PERSON> metrikas."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> žemėlapį"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet pateikta įspėjimų"}, "report/renderer/report-utils.js | warningHeader": {"message": "Įspėjimai: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Visi scenarijai"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Tikslios modulių kopijos"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Šaltinių baitai"}, "treemap/app/src/util.js | tableColumnName": {"message": "Pavadinimas"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}