{"core/audits/accessibility/accesskeys.js | description": {"message": "Prístupové klávesy umožňujú používateľom rýchlejšie vybrať časť stránky. Každý prístupový kláves musí byť jedinečný, aby navigácia správne fungovala. [Ďalšie informácie o prístupových klávesoch](https://dequeuniversity.com/rules/axe/4.8/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Hodnoty `[accesskey]` nie s<PERSON> jed<PERSON>é"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Poč<PERSON> jed<PERSON>č<PERSON><PERSON><PERSON> ho<PERSON>: `[accesskey]`"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> ARIA `role` podporuje konkrétnu podmnožinu atri<PERSON> `aria-*`. Ak ich pomiešate, znehodnotia sa atribúty `aria-*`. [Ako priradiť atribúty ARIA k ich rolám](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atrib<PERSON><PERSON> `[aria-*]` nezo<PERSON><PERSON><PERSON><PERSON><PERSON> svojim rolám"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON> `[aria-*]` z<PERSON><PERSON><PERSON><PERSON><PERSON> svojim rolám"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "<PERSON><PERSON> <PERSON> `role` um<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> asistenčným technológiám poznať rolu jednotlivých prvkov na webovej stránke. Ak sú hodnoty rol `role` nap<PERSON><PERSON><PERSON>, hodnoty rol ARIA `role` neexistuj<PERSON> alebo sú roly abstraktné, účel prvku nebude používateľom asistenčných technológií komunikovaný. [Ďalšie informácie o rolách ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Hodnoty priradené prvku `role=\"\"` nie s<PERSON> platné roly <PERSON>."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Hodnoty priradené prvku `role=\"\"` s<PERSON> platné roly <PERSON>."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Keď prvok nemá dostupný názov, čítačky obrazovky ho oznamujú pod generickým názvom, takže je pre používateľov nepoužiteľný. [<PERSON><PERSON> zlepš<PERSON>ť dostupnosť prvkov príkazov](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Prvky `button`, `link` a `menuitem` ne<PERSON><PERSON><PERSON> dostu<PERSON>."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Prvky `button`, `link` a `menuitem` ma<PERSON><PERSON>"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Prvky ARIA dialógových okien bez dostupných názvov môžu brániť používateľom čítačiek obrazovky rozpoznať ich účel. [Ako zlepšiť dostupnosť prvkov ARIA dialógových okien](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Prvky s atrib<PERSON>tom `role=\"dialog\"` alebo `role=\"alertdialog\"` ne<PERSON><PERSON><PERSON>."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Prvky s atrib<PERSON>tom `role=\"dialog\"` alebo `role=\"alertdialog\"` ma<PERSON><PERSON>."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Asist<PERSON><PERSON><PERSON><PERSON>, ako sú č<PERSON>čky obrazovky, nepra<PERSON>j<PERSON> k<PERSON>ne, keď je v dokumente `<body>` nastavený atribút `aria-hidden=\"true\"`. [Ako `aria-hidden` ovplyvňuje textovú časť dokumentov](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` sa v dokumente `<body>` nach<PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` sa v dokumente `<body>` ne<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Označiteľné podradené prvky v rámci prvku `[aria-hidden=\"true\"]` zneprístupňujú tieto interaktívne prvky používateľom asistenčných technológií, napríklad čítačiek obrazovky. [Ako `aria-hidden` ovplyvňuje označiteľné prvky](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Prvky `[aria-hidden=\"true\"]` obsahu<PERSON><PERSON>ľné odvodené prvky"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Prvky `[aria-hidden=\"true\"]` neo<PERSON><PERSON><PERSON><PERSON>ľné odvodené <PERSON>"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON><PERSON> vstupné pole nemá dostupný názov, <PERSON><PERSON><PERSON><PERSON><PERSON> obrazovky ho oznamujú pod generickým názvom, takže je pre používateľov nepoužiteľné. [Ďalšie informácie o štítkoch vstupných polí](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Vstupné polia ARIA nemajú dostupné n<PERSON>"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Vstupné polia ARIA majú dos<PERSON> n<PERSON>"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Keď prvok merača nemá dostupný názov, čítačky obrazovky ho oznamujú pod generickým názvom, tak<PERSON>e je nanič pre tých, ktor<PERSON> ich používajú. [Ako pomenovať prvky `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Prvky `meter` ARIA nemaj<PERSON> dostupné názvy."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Prvky `meter` ARIA maj<PERSON>"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Keď prvok `progressbar` nem<PERSON> dostupný názov, čítačky obrazovky ho oznamujú pod generickým názvom, takže je pre používateľov nepoužiteľný. [<PERSON><PERSON> oz<PERSON>ť prvky`progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Prvky `progressbar` ARIA nemajú dostupné názvy."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Prvky `progressbar` ARIA maj<PERSON>"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Niektoré roly <PERSON> majú povinn<PERSON>, k<PERSON><PERSON> obrazovky opisujú stav prvku. [Ďalšie informácie o rolách a povinných atribútoch](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "<PERSON>oly `[role]` ne<PERSON><PERSON><PERSON> všetky požadované atribúty `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "<PERSON><PERSON> `[role]` maj<PERSON> v<PERSON><PERSON><PERSON> požadované atribú<PERSON> `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Ak majú posky<PERSON>ť správne funkcie dostupnosti, musia niektoré nadradené roly ARIA obsahovať určité podradené roly. [Ďalšie informácie o rolách a povinných podradených prvkoch](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Prvkom so štítkom ARIA `[role]`, k<PERSON><PERSON> v<PERSON>, aby podradené prvky obsahovali konkrétnu rolu `[role]`, chý<PERSON><PERSON>ú niektoré alebo všetky požadované podradené prvky."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Prvky so štítkom ARIA `[role]`, k<PERSON><PERSON> v<PERSON>, aby podradené prvky obsahovali konkrétnu rolu `[role]`, majú všetky požadované podradené prvky."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Ak majú niektoré podradené roly ARIA poskytovať správne funkcie dostupnosti, musia byť obsiahnuté v konkrétnych nadradených rolách. [Ďalšie informácie o rolách ARIA a požadovanom nadradenom prvku](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON>oly `[role]` sa nenachádzajú v požadovanom nadradenom prvku"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Roly `[role]` sa nachádzajú v požadovanom nadradenom prvku"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Ak majú roly ARIA v<PERSON>oná<PERSON>ť zamýšľané funk<PERSON>, musia mať platné hodnoty. [Ďalšie informácie o platných rolách ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Hodn<PERSON>y `[role]` nie s<PERSON> plat<PERSON>é"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Ho<PERSON><PERSON>y `[role]` s<PERSON> platn<PERSON>"}, "core/audits/accessibility/aria-text.js | description": {"message": "<PERSON><PERSON> pridáte `role=text` okolo rozdelenia uzlov textu pomocou značiek, VoiceOver bude text považovať za jednu frázu, ale označiteľné podradené prvky daného prvku nebudú oznamované. [Ďalšie informácie o atribúte `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Prvky s atrib<PERSON>tom `role=text` maj<PERSON>é podradené prvky."}, "core/audits/accessibility/aria-text.js | title": {"message": "Prvky s atrib<PERSON>tom `role=text` ne<PERSON><PERSON><PERSON>ľné podradené prvky."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON> pole prepínača nemá dostupný názov, č<PERSON><PERSON>č<PERSON> obrazovky ho oznamujú pod generickým názvom, takže je pre používateľov nepoužiteľné. [Ďalšie informácie o poliach prepínačov](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Polia prepínačov ARIA nemajú dostupné n<PERSON>vy"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Polia prepín<PERSON>ov ARIA maj<PERSON>"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Keď prvok opisu nemá dostupný názov, čítačky obrazovky ho oznamujú pod generickým názvom, tak<PERSON>e je nanič pre tých, ktor<PERSON> ich používajú. [Ako pomenovať prvky `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Prvky `tooltip` ARIA nemajú dostupné názvy."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Prvky `tooltip` ARIA maj<PERSON> dos<PERSON>"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Keď prvok `treeitem` nem<PERSON> dostupný názov, čítačky obrazovky ho oznamujú pod generickým názvom, takže je pre používateľov nepoužiteľný. [Ďalšie informácie o označovaní prvkov `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Prvky `treeitem` ARIA nemajú dostupné názvy."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Prvky `treeitem` ARIA maj<PERSON> dos<PERSON> n<PERSON>"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Asistenčn<PERSON>, ako s<PERSON> č<PERSON> o<PERSON>zovky, nedo<PERSON><PERSON><PERSON><PERSON> interpretovať atribúty ARIA s neplatnými hodnotami. [Ďalšie informácie o platných hodnotách atribútov ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "At<PERSON><PERSON><PERSON><PERSON> `[aria-*]` nema<PERSON><PERSON> plat<PERSON> hodnoty"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON> `[aria-*]` majú p<PERSON> hodn<PERSON>y"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Asisten<PERSON><PERSON><PERSON>, ako s<PERSON> o<PERSON>ky, nedo<PERSON><PERSON><PERSON><PERSON> interpretovať atribúty ARIA s neplatnými názvami. [Ďalšie informácie o platných atribútoch ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atrib<PERSON>ty `[aria-*]` nie sú platné ani neo<PERSON>ahu<PERSON> preklepy"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atrib<PERSON><PERSON> `[aria-*]` sú platné a neobs<PERSON><PERSON>ú preklepy"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Prvky s chybami"}, "core/audits/accessibility/button-name.js | description": {"message": "Keď tlačidlo nemá dostupný názov, čítačky obrazovky ho oznamujú ako „tlačidlo“, takže je pre používateľov v podstate nepoužiteľný. [Ako zlepšiť dostupnosť tlačidiel](https://dequeuniversity.com/rules/axe/4.8/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Tlačidlá nemajú dostupný názov"}, "core/audits/accessibility/button-name.js | title": {"message": "Tlačidlá majú <PERSON>ý názov"}, "core/audits/accessibility/bypass.js | description": {"message": "Ke<PERSON> pridáte mo<PERSON>, ako obísť opakujúci sa obsah, umožníte tým efektívnejšiu navigáciu na stránke pomocou klávesnice. [Ďalšie informácie o obchád<PERSON><PERSON> blokov](https://dequeuniversity.com/rules/axe/4.8/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Stránka neobsahuje hlavičku, odkaz na preskočenie či orientačný bod regióna."}, "core/audits/accessibility/bypass.js | title": {"message": "Stránka obsahuje hlavičku, odkaz na preskočenie alebo orientačný bod regiónu"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Mnohí používatelia majú problémy s čítaním textu s nízkym kontrastom, pr<PERSON><PERSON><PERSON> to vôbec nedokážu. [<PERSON><PERSON> poskytnú<PERSON> dostačujúci kontrast farieb](https://dequeuniversity.com/rules/axe/4.8/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> pozadia a popredia nemajú dostatočný kontrastný pomer."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON> pozadia a popredia majú dostatočný kontrastný pomer"}, "core/audits/accessibility/definition-list.js | description": {"message": "Keď zoznamy definícií nie sú správne oz<PERSON>en<PERSON>, čítačky obrazovky môžu generovať mätúci alebo nepresný výstup. [Ako správne štruktúrovať zoznamy definícií](https://dequeuniversity.com/rules/axe/4.8/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` neobs<PERSON><PERSON> iba správne usporiadané sku<PERSON> `<dt>` a `<dd>`, prvky `<script>`, `<template>` alebo `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` obsahuje iba správne usporiadané skupin<PERSON> `<dt>` a `<dd>`, prvky `<script>`, `<template>` alebo `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Položky zoznamu definícií (`<dt>` a `<dd>`) musia byť zabalené v nadradenom prvku `<dl>`, aby ich čítačky obrazovky dokázali správne oznamovať. [Ako správne štruktúrovať zoznamy definícií](https://dequeuniversity.com/rules/axe/4.8/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Položky zoznamu definícií nie sú zabalené v prvkoch `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Položky zoznamu definícií sú zabalené v prvkoch `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Názov poskytuje používateľom čítačiek obrazovky prehľadné informácie o stránke a používatelia vyhľadávačov sa podľa neho rozhodujú, či je stránka pre ich vyhľadávanie relevantná. [Ďalšie informácie o názvoch dokumentov](https://dequeuniversity.com/rules/axe/4.8/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument nemá prvok `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument má prvok `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Všetky označiteľné prvky musia mať jedinečný atribút `id`, aby ich dokázali rozpoznať asistenčné technológie. [Ako opraviť duplicitné atribúty `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atribúty `[id]` aktívnych označiteľných prvkov nie sú jedinečné"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atribúty `[id]` aktívnych označiteľných prvkov sú jedinečné"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Hodnota identifikátora ARIA musí by<PERSON> jed<PERSON>, aby asistenčné technológie neprehliadli ďalšie výskyty. [Ako opraviť duplicitné identifikátory ARIA](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Identifikátory ARIA nie s<PERSON> jed<PERSON>č<PERSON>é"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Identifikátory ARIA nie s<PERSON> jed<PERSON>č<PERSON>é"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Nadpis bez obsahu alebo nedostupný text bránia prístupu používateľov čítačiek obrazovky k informáciám v štruktúre stránky. [Ďalšie informácie o nadpisoch](https://dequeuniversity.com/rules/axe/4.8/empty-heading)"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Prvky nadpisu nezahŕňajú obsah."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Všetky prvky napisov zahŕňajú obsah."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Asistenčn<PERSON> tech<PERSON>, ako napríklad čí<PERSON> o<PERSON>zo<PERSON>, k<PERSON><PERSON> p<PERSON> buď prvý, <PERSON><PERSON><PERSON><PERSON><PERSON>, alebo v<PERSON><PERSON>ky štítky, môž<PERSON> polia formulárov s viacerými štítkami ohlásiť nepresne. [Ako používať štítky formulárov](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Polia formulára maj<PERSON> via<PERSON>"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Žiadne polia formulára nemajú via<PERSON>"}, "core/audits/accessibility/frame-title.js | description": {"message": "Používatelia čítačiek obrazovky zistia obsah rámov pomocou ich názvov. [Ďalšie informácie o názvoch rámov](https://dequeuniversity.com/rules/axe/4.8/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Prvky `<frame>` alebo `<iframe>` ne<PERSON><PERSON><PERSON>v"}, "core/audits/accessibility/frame-title.js | title": {"message": "Prvo<PERSON> `<frame>` alebo `<iframe>` má názov"}, "core/audits/accessibility/heading-order.js | description": {"message": "Správne usporiadané <PERSON>, k<PERSON><PERSON>, u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>tr<PERSON>, vďaka čomu zjednodušujú navigáciu a porozumenie pri používaní asistenčných technológií. [Ďalšie informácie o poradí nadpisov](https://dequeuniversity.com/rules/axe/4.8/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Prvky nadpisu sa nezobrazujú postupne v zostupnom poradí"}, "core/audits/accessibility/heading-order.js | title": {"message": "Prvky nadpisu sa zobrazujú postupne v zostupnom poradí"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ak stránka neuvádza atribút `lang`, čítačka obrazovky predpokladá, že je v predvolenom jazyku vybranom používateľom pri nastavovaní čítačky obrazovky. Ak stránka v skutočnosti nie je v predvolenom jazyku, čítačka obrazovky nemusí jej text prečítať správne. [Ďalšie informácie o atribúte `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "<PERSON>r<PERSON><PERSON> `<html>` nem<PERSON> atribút `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "<PERSON>r<PERSON><PERSON> `<html>` má atrib<PERSON>t `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Ak je uvedený platný [jazyk BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to pomô<PERSON>e správne oznamovať text. [<PERSON><PERSON> použí<PERSON> atribút `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Prvok `<html>` nemá platnú hodnotu pre atribút `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Prvok `<html>` má platnú hodnotu pre svoj atribút `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Ak na webovej stránke nie je uvedený konzistentný jazyk, čítačka obrazovky nemusí jej text oznámiť správne. [Ďalšie informácie o atribúte `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Prvok `<html>` nem<PERSON> atribút `[xml:lang]` s rovnakým základným jazykom ako atribút `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Prvok `<html>` má atribút `[xml:lang]` s rovnak<PERSON>m základným jazykom ako atribút `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> s rovnakým cieľom musia mať rovnaký opis, aby používatelia porozumeli ich účelu a mohli sa rozhodnúť, či ich použijú. [Ďalšie informácie o rovnakých odkazoch](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identické odkazy nemajú rovnaký účel."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Identické odkazy majú rovna<PERSON>čel."}, "core/audits/accessibility/image-alt.js | description": {"message": "Informatívne prvky by ma<PERSON> ma<PERSON> krátky opisný alternatívny text. Dekoratívne prvky je možné ignorovať pomocou prázdneho atribútu alt. [Ďalšie informácie o atribúte `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Prvky obrázkov nemajú atribúty `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Prvky obrázka majú atrib<PERSON> `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Informatívne prvky by mali ma<PERSON> krátky opisný alternatívny text. Alternatívny text, ktorý je presne rovnaký ako text vedľa odkazu alebo obrázka, je pre používateľov čítačiek obrazovky mätúci, pretože sa prečíta dvakrát. [Ďalšie informácie o atribúte `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Prvky obr<PERSON><PERSON><PERSON> maj<PERSON> (`[alt]`), ktor<PERSON> s<PERSON> nadbytočný text."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Prvky obr<PERSON>zkov nemajú atribú<PERSON> `[alt]`, ktoré s<PERSON> nadbytočný text."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Pridaním rozlíšiteľného a prístupného textu k tlačidlám vstupu môžete pomôcť používateľom čítačiek obrazovky porozumieť účelu tlačidla vstupu. [Ďalšie informácie o tlačidlách vstupu](https://dequeuniversity.com/rules/axe/4.8/input-button-name)"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Tlačidlá vstupu nemajú rozlíšiteľný text."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Tlačidlá vstupu majú rozlíšiteľný text."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Ke<PERSON> je ako t<PERSON> `<input>` pou<PERSON><PERSON><PERSON> o<PERSON>r<PERSON>, uvedenie alternatívneho textu môže pomôcť používateľom čítačky obrazovky pochopiť účel tlačidla. [Ďalšie informácie o alternatívnom texte obrázka vstupu](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Prvky `<input type=\"image\">` nemajú text `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Prvky `<input type=\"image\">` majú text `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Viditeľné textové <PERSON>, ktor<PERSON> sa nezhodujú s dostupným názvom, môžu u používateľov čítačiek obrazovky spôsobiť zmätok. [Ďalšie informácie o dostupných názvoch](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Prvky s viditeľnými textovými štítkami nemajú zodpovedajúce dostupné názvy."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Prvky s viditeľnými textovými štítkami majú zodpovedajúce dostupné názvy."}, "core/audits/accessibility/label.js | description": {"message": "Štítky <PERSON>, aby asistenč<PERSON>é technoló<PERSON> (napríklad čítačky obrazovky) správne oznamovali ovládacie prvky formulárov. [Ďalšie informácie o štítkoch prvkov formulárov](https://dequeuniversity.com/rules/axe/4.8/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "Prvky formulárov nemajú priradené <PERSON>"}, "core/audits/accessibility/label.js | title": {"message": "K prvkom formulára sú priradené š<PERSON>ítky"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Jeden hlavný orientačný bod pomáha používateľom čítačiek obrazovky prechádzať webovú stránku. [Ďalšie informácie o orientačných bodoch](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokument nemá hlavný orientačný bod."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokument má hlavný orientačný bod."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Mnohí používatelia majú problémy s čítaním textu s nízkym kontrastom, pr<PERSON><PERSON><PERSON> to vôbec nedokážu. Rozpoznateľný text odkazu zlepšuje dojem slabozrakých používateľov. [Ako nastaviť rozlíšiteľné odkazy](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Odkazy sú rozlíšiteľné iba na základe farebného odlíšenia."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Od<PERSON>zy sú rozlíšiteľné aj bez farebného odlíšenia."}, "core/audits/accessibility/link-name.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON><PERSON><PERSON> a zamerateľný text odkazu (a alternatívny text pre o<PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré sa používajú ako odkazy) zlepšuje navigáciu pomocou čítačky obrazovky. [<PERSON><PERSON> sprístupniť odkazy](https://dequeuniversity.com/rules/axe/4.8/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> rozpoznateľné názvy"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> roz<PERSON>nateľné názvy"}, "core/audits/accessibility/list.js | description": {"message": "Čítačky obrazovky oznamujú zoznamy špeciálnym spôsobom. Použitím správnej štruktúry zoznamu pomôžete čítačkám obrazovky s výstupom. [Ďalšie informácie o správnej štruktúre zoznamov](https://dequeuniversity.com/rules/axe/4.8/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Zoznamy neobsahujú iba prvky `<li>` a prvky podporujúce skript (`<script>` a `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Zoznamy obsahujú iba prvky `<li>` a prvky podporujúce skript (`<script>` a `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Položky zoznamu (`<li>`) sa musia nachádzať v nadradenom prvku `<ul>`, `<ol>` alebo `<menu>`, aby ich dokázali čítačky obrazovky správne oznámiť. [Ďalšie informácie o správnej štruktúre zoznamov](https://dequeuniversity.com/rules/axe/4.8/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Položky zoznamu (`<li>`) sa nenachádzajú v nadradenom prvku `<ul>`, `<ol>` ani `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Položky zoznamu (`<li>`) sa nachádzajú v nadradenom prvku `<ul>`, `<ol>` alebo `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Používatelia neočakávajú, že sa stránka bude automaticky obnovovať. Pri automatickom obnovení sa zameranie vráti späť na začiatok stránky. Na používateľov to môže pôsobiť nepríjemne alebo mätúco. [Ďalšie informácie o metaznačke obnovenia](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument používa metaznačku `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument nepoužíva `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Deaktivácia približovania spôsobuje problémy slabozrakým používateľom, ktorí pri čítaní obsahu webovej stránky využívajú priblíženie obrazovky. [Ďalšie informácie o metaznačke oblasti zobrazenia](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` sa nepoužíva v prvku `<meta name=\"viewport\">` alebo hodnota atribútu `[maximum-scale]` je nižšia ako 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` sa nepoužíva v prvku `<meta name=\"viewport\">` a atribút `[maximum-scale]` nie je menší ako 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Čítačky obrazovky nedokážu preložiť netextový obsah. Pridaním alternatívneho textu k prvkom `<object>` pomôžete čítačkám obrazovky sprostredkovať používateľom význam. [Ďalšie informácie o alternatívnom texte prvkov `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Prvky `<object>` nemajú alternatívny text"}, "core/audits/accessibility/object-alt.js | title": {"message": "Prvky `<object>` majú alternatívny text"}, "core/audits/accessibility/select-name.js | description": {"message": "Prvky formulárov bez efektívnych štítkov môžu byť pre používateľov čítačiek obrazovky frustrujúce. [Ďalšie informácie o prvku `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Vybrané prvky nemajú spojené prvky štítkov."}, "core/audits/accessibility/select-name.js | title": {"message": "Vybrané prvky majú spojené prvky štítkov."}, "core/audits/accessibility/skip-link.js | description": {"message": "Zahrnutie odkazu na preskočenie môže pomôcť používateľom preskočiť na hlavný obsah a ušetriť tak čas. [Ďalšie informácie o odkazoch na preskočenie](https://dequeuniversity.com/rules/axe/4.8/skip-link)"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Odkazy na preskočenie nie sú označiteľné."}, "core/audits/accessibility/skip-link.js | title": {"message": "Odkazy na preskočenie sú označiteľné."}, "core/audits/accessibility/tabindex.js | description": {"message": "Hodnota väčšia ako 0 označuje explicitné usporiadanie navigácie. Ide o technicky platn<PERSON> r<PERSON>, ale často vedie k nepríjemným skúsenostiam <PERSON>, k<PERSON><PERSON> potrebuj<PERSON> asistenčné technológie. [Ďalšie informácie o atribúte `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Niektoré prvky majú hodnotu `[tabindex]` vyššiu ako 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Žiadny prvok nemá hodnotu `[tabindex]` vyššiu ako 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Atribút súhrnu by mal opisovať štrukt<PERSON><PERSON>, zatiaľ čo atribút `<caption>` by mal ma<PERSON> názov na obrazovke. Presné značenie tabuľky pomáha používateľom čítačiek obrazovky. [Ďalšie informácie o súhrne a titulkoch](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Tabuľky majú rovnaký obsah v atribúte súhrnu a `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Tabuľky majú odlišný obsah v atribúte súhrnu a atribúte`<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Čítačky obrazovky maj<PERSON>, ktor<PERSON> z<PERSON>uj<PERSON> prehliadanie tabuliek. <PERSON>k <PERSON>ais<PERSON>, že tabuľky budú používať príslušný prvok titulku namiesto buniek s atribútom `[colspan]`, môžete zlepšiť prostredie pre používateľov čítačiek obrazovky. [Ďalšie informácie o titulkoch](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "Tabuľky neuvádzajú titulok pomocou `<caption>` na<PERSON><PERSON> buniek s atribútom `[cols<PERSON>]`."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "Tabuľky uvádzajú titulok pomocou `<caption>` na<PERSON><PERSON> buniek s atribútom `[cols<PERSON>]`."}, "core/audits/accessibility/target-size.js | description": {"message": "Do<PERSON><PERSON><PERSON><PERSON> veľk<PERSON> a priestorné miesta dotyku pomáhajú aktivovať ciele používateľom, ktorí môžu mať problémy zacieliť na malé ovládacie prvky. [Ďalšie informácie o miestach dotyku](https://dequeuniversity.com/rules/axe/4.8/target-size)"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "Miesta dotyku nie sú dostatočne veľké ani <PERSON>."}, "core/audits/accessibility/target-size.js | title": {"message": "Miesta dotyku sú dostatočne veľké aj priestorné."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Čítačky obrazovky maj<PERSON>, ktor<PERSON> z<PERSON>uj<PERSON> prehliadanie tabuliek. Ak zaistí<PERSON>, že prvky `<td>` vo veľkej tabuľke (minimálne tri bunky na šírku a výšku) budú mať spojenú hlavičku tabuľky, môžete tým zlepšiť prostredie pre používateľov čítačiek obrazovky. [Ďalšie informácie o hlavičkách tabuliek](https://dequeuniversity.com/rules/axe/4.8/td-has-header)"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Prvky `<td>` vo veľ<PERSON>j `<table>` nema<PERSON><PERSON> hlav<PERSON>ky tabuľky."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Prvky `<td>` vo veľ<PERSON> `<table>` maj<PERSON> jednu alebo viac hlavičiek tabuľky."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Čítačky obrazovky maj<PERSON>, k<PERSON><PERSON> z<PERSON>uj<PERSON> prehliadanie tabuliek. Ke<PERSON>, aby bunky `<td>` s atribútom `[headers]` odkazovali iba na bunky v rovnakej tabuľke, môžete tým používateľom čítačiek obrazovky zjednodušiť prehliadanie. [Ďalšie informácie o atribúte `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Bunky v prvku `<table>`, ktoré používajú atribút `[headers]`, odkazujú na prvok `id`, ktorý nie je v rovnakej tabuľke."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Bunky v prvku `<table>`, ktoré používajú atribút `[headers]`, odkazujú na bunky v tej istej tabuľke."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čítačky obrazovky maj<PERSON>, ktor<PERSON><PERSON><PERSON><PERSON> prehliadanie tabuliek. Prostredie používateľov čítačiek obrazovky môžete vylepšiť tým, že hlavičky tabuliek budú odkazovať na určitú skupinu buniek. [Ďalšie informácie o hlavičkách tabuliek](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Prvky `<th>` a prvky s rolou `[role=\"columnheader\"/\"rowheader\"]` ne<PERSON><PERSON><PERSON> bunky s <PERSON><PERSON><PERSON>, ktor<PERSON> opisuj<PERSON>."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Prvky `<th>` a prvky s rolou `[role=\"columnheader\"/\"rowheader\"]` maj<PERSON> bunky s <PERSON><PERSON><PERSON>, ktor<PERSON> opisujú."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ak je v prvkoch uvedený platný [jazyk BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), pomôže to zaistiť, aby čítačky obrazovky čítali text správne. [<PERSON><PERSON> p<PERSON> atribút `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atrib<PERSON><PERSON> `[lang]` nema<PERSON><PERSON> platn<PERSON> hodnotu"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atrib<PERSON><PERSON> `[lang]` majú p<PERSON> hodnotu"}, "core/audits/accessibility/video-caption.js | description": {"message": "Keď má video k dispozícii titulky, ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a sluchovo postihnutí používatelia jednoduchšie získajú informácie vo videu. [Ďalšie informácie o titulkoch videí](https://dequeuniversity.com/rules/axe/4.8/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Prvky `<video>` neo<PERSON><PERSON><PERSON><PERSON> prvok `<track>` s titulkami `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Prvky `<video>` obsahuj<PERSON> prvok `<track>` s titulkami `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Aktuálna hodnota"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` pomáha používateľom rýchlejšie odosielať formuláre. Ak im chcete ušetriť prácu, zv<PERSON>žte aktiváciu tejto funkcie nastavením atribútu `autocomplete` na platnú hodnotu. [Ďalšie informácie o atribúte `autocomplete` vo formulároch](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Prvky `<input>` nema<PERSON><PERSON> správne atribúty `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Vyžaduje sa manuálna kontrola"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Skontrolujte poradie tokenov"}, "core/audits/autocomplete.js | title": {"message": "Prvky `<input>` používaj<PERSON> atribút `autocomplete` správne"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Tokeny funkcie `autocomplete`: token {token} je neplatný v atribúte {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Skontrolujte poradie tokenov: {tokens} v {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Opraviteľné"}, "core/audits/bf-cache.js | description": {"message": "Mnohé navigácie sa vykonávajú návratom na predchádzajúcu stránku alebo opätovným prejdením dopredu. Spätná vyrovnávacia pamäť môže tieto navigácie, ktoré s<PERSON> návraty, ur<PERSON><PERSON><PERSON><PERSON>. [Ďalšie informácie o spätnej vyrovnávacej pamäti](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 dôvod, prečo sa to nepodarilo}few{# dôvody, prečo sa to nepodarilo}many{# failure reasons}other{# dôvodov, prečo sa to nepodarilo}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Dôvod neúspechu"}, "core/audits/bf-cache.js | failureTitle": {"message": "Stránka zabránila obnoveniu spätnej vyrovnávacej pamäte"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Neopraviteľné"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Čaká sa na podporu prehliadača"}, "core/audits/bf-cache.js | title": {"message": "Stránka nezabránila obnoveniu spätnej vyrovnávacej pamäte"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Spätná vyrovnávacia pamäť sa nedá testovať v starom Chrome bez grafického rozhrania (`--chrome-flags=\"--headless=old\"`). Ak si chcete pozrieť výsledky kontroly, použite nový Chrome bez grafického rozhrania (`--chrome-flags=\"--headless=new\"`) alebo štandardný Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Rozšírenia pre Chrome negatívne ovplyvnili výkonnosť načítania tejto stránky. Skúste stránku skontrolovať v režime inkognito alebo profile Chrome bez rozšírení."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Hodnotenie skriptu"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Analýza sk<PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Celkový čas využitia procesora"}, "core/audits/bootup-time.js | description": {"message": "Zvážte skrátenie času strá<PERSON> analý<PERSON>u, zostavovaním a spustením JavaScriptu. Možno vám s tým pomôže zobrazovanie menších prenášaných dát JavaScriptu. [Ako skrátiť čas spustenia JavaScriptu](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Skráťte čas spustenia JavaScriptu"}, "core/audits/bootup-time.js | title": {"message": "Čas spustenia JavaScriptu"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Odstráňte ve<PERSON> a duplicitné moduly JavaScriptu, aby ste zredukovali nepotrebné bajty spotrebované aktivitou siete. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Odstráňte duplicitné moduly v balíkoch JavaScriptu"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veľké gify nie sú vhodné na zobrazovanie animovaného obsahu. Namiesto nich odporúčame použiť videá MPEG4/WebM pre animácie a PNG/WebP pre statick<PERSON> obr<PERSON>, aby ste ušetrili spotrebu bajtov v sieti. [Ďalšie informácie o efektívnych formátoch videa](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Pre animovaný obsah použite formáty videa"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Viacnásobné vyplnenia a transformácie umožňujú starým prehliadačom používať nové funkcie JavaScriptu. Mnohé z nich však nie sú pre moderné prehliadače potrebné. Adoptujte pre svoj zabalený JavaScript modernejšiu stratégiu nasadzovania skriptov využívajúcu detekciu modulových a nemodulových funkcií a zredukujte tak množstvo kódu dodávaného do moderných prehliadačov, pričom zachováte podporu starých prehliadačov. [Ako používať moderný JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Nezobrazujte starý JavaScript v moderných prehliadačoch"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formáty obrázka ako WebP a AVIF zvyčajne poskytujú lepšiu kompresiu než PNG alebo JPEG, ktorou sa zrýchli sťahovanie a zníži spotreba dát. [Ďalšie informácie o moderných formátoch obrázka](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Zobrazujte obrázky vo formátoch ďalšej generácie"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Zv<PERSON>žte lenivé načí<PERSON>, ktor<PERSON> sú mimo obrazovky alebo skryté, a to až po dokončení načítania všetkých podstatných zdrojov, <PERSON><PERSON><PERSON> skrátite čas do interaktivty. [<PERSON><PERSON> odložiť obrázky mimo obrazovky](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Oddiaľte načítanie obrázkov mimo obrazovky"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Zdroje blokujú prvé vykreslenie stránky. Zvážte zobrazovanie podstatných JavaScriptov alebo šablón CSS v texte a oddialenie všetkých nepodstatných JavaScriptov alebo štýlov. [<PERSON><PERSON> odstrániť zdroje blokujúce vykresľovanie](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Odstráňte zdroje blokujúce vykreslenie"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Veľké sieťové prenosy dát stoja používateľov mnoho peňazí a často sú spájané s dlhými časmi načítania. [Ako znížiť veľkosti sieťových prenosov dát](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "<PERSON><PERSON><PERSON> veľ<PERSON>ť bola {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Zabráňte nadmerným sieťovým prenosom dát"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Zabráni nadmerným sieťovým prenosom dát"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifikáciou súborov šablón CSS môžete znížiť veľkosti sieťových prenosov dát. [Ako minifikovať šablóny CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifikujte súbory CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifikáciou súborov JavaScriptu môžete znížiť veľkosti prenášaných dát a čas analýzy skriptu. [Ako minifikovať JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifikujte JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Zredukujte nepoužívané pravidlá v šablónach štýlov a odložte šablóny CSS, ktoré sa nepoužívajú pre obsah nad záhybom stránky, čím znížite spotrebu bajtov aktivitou v sieti. [Ako zredukovať nepoužívané šalbóny CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Zredukujte nepoužívané šablóny CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Zredukujte nepoužívaný jazyk JavaScript a odložte načítavanie skriptov do okamihu, kedy budú potrebné, čím znížite spotrebu bajtov aktivitou v sieti. [Ako zredukovať nepoužívaný JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Zredukujte nepoužívaný jazyk JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dlhá životnosť vyrovnávacej pamäte môže zrýchliť opakované návštevy stránky. [Ďalšie informácie o účinných pravidlách vyrovnávacej pamäte](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Naš<PERSON> sa 1 zdroj}few{Našli sa # zdroje}many{# resources found}other{Našlo sa # zdrojov}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Zobrazujte statické podklady s účinnými pravidlami ukladania do vyrovnávacej pamäte"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Používa účinné pravidlá ukladania do vyrovnávacej pamäte pre statické podklady"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimalizované obrázky sa načítavajú rýchlejšie a spotrebúvajú menej mobilných dát. [Ako efektívne kódovať obrázky](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Účinne zakódujte obrázky"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Obrázky boli väčšie než zobrazovaná veľkosť"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Obrázky boli vhodné pre zobrazovanú veľkosť"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Zobrazujte obrázky s prime<PERSON><PERSON>, <PERSON><PERSON><PERSON> ušetríte mobilné dáta a skrátite čas načítania. [Ako nastaviť veľkosť obrázkov](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Nastavte primeranú veľkosť obrázkov"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textové zdroje by sa mali zobrazovať komprimované (gzip, deflate alebo brotli), aby sa minimalizovala celková spotreba bajtov v sieti. [Ďalšie informácie o kompresii textu](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Povoľte kompresiu textu"}, "core/audits/content-width.js | description": {"message": "Ak šírka obsahu vašej aplikácie nezodpovedá šírke oblasti zobrazenia, aplikácia nemusí byť optimalizovaná pre obrazovky mobilných zariadení. [Ako zmeniť veľkosť obsahu v oblasti zobrazenia](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Veľkosť oblasti zobrazenia {innerWidth} px nezodpovedá veľkosti okna {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "<PERSON>bsah nemá správnu veľkosť pre oblasť zobrazenia"}, "core/audits/content-width.js | title": {"message": "<PERSON><PERSON>ah má správnu veľkosť pre oblasť zobrazenia"}, "core/audits/critical-request-chains.js | description": {"message": "Reťazce podstatných žiadostí uvedené nižšie z<PERSON>zorňujú, ktoré zdroje sú načítané s vysokou prioritou. Zvážte skrátenie dĺžky reťazcov, aby ste zn<PERSON><PERSON><PERSON> veľkosť sťahovan<PERSON><PERSON> zdr<PERSON>jov, alebo odlo<PERSON>te sťahovanie nepotrebných zdr<PERSON>jov, <PERSON><PERSON><PERSON> zlepšíte načítanie stránky. [Ako sa vyhnúť reťazeniu kľúčových žiadostí](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Našiel sa 1 reťazec}few{Našli sa # reťazce}many{# chains found}other{Našlo sa # reťazcov}}"}, "core/audits/critical-request-chains.js | title": {"message": "Vyhýbajte sa reťazeniu podstatných žiadostí"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktíva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Závažnosť"}, "core/audits/csp-xss.js | description": {"message": "Prísne pravidlá na zabezpečenie obsahu (PZO) výrazne znižujú riziko útokov skriptovaním medzi webmi. [Ako pomocou PZO zabrániť útokom XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntax"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Stránka obsahuje PZO definované v značke `<meta>`. Zvážte presun PZO do hlavičky HTTP alebo definovanie iných prísnych PZO v hlavičke HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "V režime presadzovania neboli nájdené žiadne PZO"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON>, aby PZO účinne blokovali útoky XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Ukončenie podpory / upozornenie"}, "core/audits/deprecations.js | columnLine": {"message": "Riadok"}, "core/audits/deprecations.js | description": {"message": "Rozhrania API s ukončenou podporou budú nakoniec z prehliadača odstránené. [Ďalšie informácie o rozhraniach API s ukončenou podporou](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Zistilo sa 1 upozornenie}few{Zistili sa # upozornenia}many{# warnings found}other{Zistilo sa # upozornení}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Používa rozhrania API s ukončenou podporou"}, "core/audits/deprecations.js | title": {"message": "Nepoužíva rozhrania API s ukončenou podporou"}, "core/audits/dobetterweb/charset.js | description": {"message": "Vyžaduje sa deklarácia kódovania znakov. Môžete to vykonať pomocou značky `<meta>` v prvých 1 024 bajtoch kódu HTML alebo hlavičke odpovede HTTP Content-Type. [Ďalšie informácie o deklarácii kódovania znakov](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Deklarácia charset chýba alebo je v kóde HTML uvedená príliš <PERSON>"}, "core/audits/dobetterweb/charset.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> definuje charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Špecifikáciou prvku Doctype zabránite prechodu prehliadača do režimu kompatibility. [Ďalšie informácie o deklarácii prvku Doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Názov prvku Doctype musí byť reťazec `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokument obsahuje at<PERSON> `doctype`, k<PERSON><PERSON> spú<PERSON>a `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument musí obsahovať prvok doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Očakávalo sa, že publicId bude prázdny reťazec"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Očakávalo sa, že systemId bude prázdny reťazec"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokument obsahuje atri<PERSON> `doctype`, k<PERSON><PERSON> spú<PERSON>a `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stránke chýba prvok HTML doctype, preto spúšťa režim kompatibility"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Stránka obsahuje prvok HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Štatistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Hodnota"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Rozsiahly model DOM zvýši využiti<PERSON> pamäte, predĺži [v<PERSON><PERSON><PERSON><PERSON>](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) a spôsobí nákladné [preformátovania rozloženia](https://developers.google.com/speed/articles/reflow). [Ako sa vyhnúť nadmernej veľkosti modelu DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 prvok}few{# prvky}many{# elements}other{# prvkov}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Nepoužívajte nadmerne veľký prvok DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximálna hĺbka prvku DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Celkový počet prvkov DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximálny počet podradených prvkov"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Zabráni použitiu nadmerne veľkého prvku DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Používatelia nedô<PERSON><PERSON><PERSON><PERSON> webom, k<PERSON><PERSON> po<PERSON>u<PERSON><PERSON> ich polohu bez kontextu, alebo ich považujú za mätúce. Zvážte namiesto toho spojenie žiadosti s akciou používateľa. [Ďalšie informácie o povolení geolokácie](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Požaduje povolenie na geolokáciu pri načítaní stránky"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Nepožaduje povolenie na geolokáciu pri načítaní stránky"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON> pro<PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problémy zaznamenané v paneli `Issues` Nástrojov pre vývojárov v prehliadači Chrome označujú, že sú nevyriešené. Môžu pochádzať z neúspešných požiadaviek siete, nedostatočného ovládania zabezpečenia a ďalších problémov prehliadača. Viac sa o jednotlivých problémoch dozviete po otvorení panela Problémy v Nástrojoch pre vývojárov v prehliadači Chrome."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problémy boli zaznamenané v paneli `Issues` Nástrojov pre vývojárov v prehliadači Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokované pravidlami pre cross origin"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Intenzívne využívanie zdrojov reklamami"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "V paneli `Issues` Nástrojov pre vývojárov v prehliadači Chrome nie sú žiadne problémy"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verzia"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Na stránke boli zistené všetky JavaScriptové knižnice klientskeho rozhrania. [Ďalšie informácie o tejto diagnostickej kontrole JavaScriptových knižníc](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Zistené knižnice JavaScriptu"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "V prípade p<PERSON>žívateľov s pomalým pripojením môžu externé skripty, ktoré sú <PERSON>ky vložené prostredníctvom funkcie `document.write()`, oneskoriť načítavanie stránky o desiatky sekúnd. [Ako sa vyhnúť funkcii document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Nepoužívajte `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Nepoužíva `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Používatelia nedô<PERSON><PERSON><PERSON><PERSON> webom, ktor<PERSON> po<PERSON>adu<PERSON><PERSON> odosielanie upozornení bez kontextu, alebo ich považujú za mätúce. Zvážte namiesto toho spojenie žiadosti s gestami používateľa. [Ďalšie informácie o zodpovednom získavaní povolenia pre upozornenia](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Požaduje povolenie na upozornenie pri načítaní stránky"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Nepožaduje povolenie na upozornenie pri načítaní stránky"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Bránenie prilepovaniu obsahu do vstupných polí nepriaznivo pôsobí na dojem používateľa a oslabuje zabezpečenie tým, že blokuje správcov hesiel.[Ďalšie informácie o pohodlných vstupných poliach](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Bráni používateľom prilepiť obsah do vstupných polí"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Povoľte používateľom prilepovať obsah do vstupných polí"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ponúka v porovnaní s HTTP/1.1 mnoho výhod vrátane binárnych hlavičiek a multiplexovania. [Ďalšie informácie o protokole HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 žiadosť nebola odoslaná cez HTTP/2}few{# žiadosti neboli odoslané cez HTTP/2}many{# requests not served via HTTP/2}other{# žiadostí nebolo odoslaných cez HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Použite HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Odporúčame zmeniť prijímače udalostí aktivovaných dotykom alebo kolieskom myši na `passive`, čí<PERSON> zlepšíte výkonnosť posúvania stránky. [Ďalšie informácie o adopcii pasívnych prijímačov udalostí](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nepoužíva pasívne prijímače na zlepšenie posúvania"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Používa pasívne prijímače na zlepšenie posúvania"}, "core/audits/errors-in-console.js | description": {"message": "Chyby zapísané do denníka konzoly označujú nevyriešené problémy. Môžu pochádzať zo zlyhaní žiadostí siete a ďalších problémov prehliadača. [Ďalšie informácie o týchto chybách v diagnostickej kontrole v konzole](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Chyby prehliadača boli zaznamenané do konzoly"}, "core/audits/errors-in-console.js | title": {"message": "Do konzoly neboli zaznamenané žiadne chyby prehliadača"}, "core/audits/font-display.js | description": {"message": "Využite funkciu CSS `font-display`, ktor<PERSON> pomôže zaistiť, aby bol text viditeľný používateľom počas načítavania webfontov. [Ďalšie informácie o funkcii `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, aby text zostal počas načítania webfontov viditeľný"}, "core/audits/font-display.js | title": {"message": "Všetok text zostane počas načítania webfontov viditeľný"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON><PERSON><PERSON>,plural, =1{Nástroju Lighthouse sa nepodarilo automaticky skontrolovať hodnotu `font-display` webu {fontOrigin}.}few{Nástroju Lighthouse sa nepodarilo automaticky skontrolovať hodnoty `font-display` webu {fontOrigin}.}many{Nástroju Lighthouse sa nepodarilo automaticky skontrolovať hodnoty `font-display` webu {fontOrigin}.}other{Nástroju Lighthouse sa nepodarilo automaticky skontrolovať hodnoty `font-display` webu {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON> (skutočný)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (zobrazený)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Rozmery zobrazenia obrázka by mali zod<PERSON><PERSON>ť prirodzenému pomeru strán. [Ďalšie informácie o pomere strán obrázka](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Zobrazuje obrázky s nesprávnym pomerom strán"}, "core/audits/image-aspect-ratio.js | title": {"message": "Zobrazuje obrázky so správnym pomerom strán"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Skutočná veľkosť"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Zobrazená veľkosť"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Očakávaná veľ<PERSON>ť"}, "core/audits/image-size-responsive.js | description": {"message": "Prirodzené rozmery obrázka by mali by<PERSON> priamo <PERSON> veľ<PERSON> obrazovky a pomeru pixelov, aby sa dosiahla maximálna jasnosť. [Ako poskytovať responzívne obrázky](https://web.dev/articles/serve-responsive-images)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Zobrazuje obrázky v nízkom rozlíšení"}, "core/audits/image-size-responsive.js | title": {"message": "Zobrazuje obrázky v príslušnom rozlíšení"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikácia je už na<PERSON>š<PERSON>ná"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Z manifestu sa nepodarilo stiahnuť požadovanú ikonu"}, "core/audits/installable-manifest.js | columnValue": {"message": "Dôvod neúspechu"}, "core/audits/installable-manifest.js | description": {"message": "Obsluha je technológia, k<PERSON><PERSON> umožňuje vašej aplikácii používať mnoho funkcií progresívnej webovej aplikácie, napríklad offline režim, pridanie na plochu a upozornenia aplikácie. So správnou implementáciou obsluhy a manifestu môžu prehliadače proaktívne vyzývať používateľov, aby si pridali vašu aplikáciu na plochu, čo môže viesť k ich lepšiemu zapojeniu. [Ďalšie informácie o požiadavkách inštalovateľnosti manifestu](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 dôvod}few{# dôvody}many{# reasons}other{# dôvodov}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifest webovej aplikácie alebo obsluha nespĺňajú požiadavky na inštaláciu"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Webová adresa aplikácie a jej identifikátor v Obchode Play sa nezhodujú"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Stránka je načítaná v okne inkognito"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Vlastníctvo manifestu `display` musí byť `standalone`, `fullscreen` alebo `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest obsahuje pole „display_override“ a prvý podporovaný režim obrazovky musí mať hodnotu „standalone“, „fullscreen“ alebo „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest sa nepodarilo načítať alebo analyzovať, prípadne je prázdny"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Počas načítavania manifestu sa zmenila jeho webová adresa."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest neo<PERSON><PERSON><PERSON> pole `name` alebo `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest neobsahuje vhodnú ikonu. Vyžaduje sa formát PNG, SVG alebo WebP s minimálnou veľkosťou {value0} px, musí byť nastavený atribút Veľkosti a ak je nastavený atribút Účel, musí obsahovať hodnotu „všetko“."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Žiadna dodaná ikona nie je štvorec s minimálnou veľkosťou {value0} px vo formáte PNG, SVG alebo WebP s atribútom účelu nastaveným na hodnotu „všetko“, prípadne vôbec nenastaveným"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Stiahnutá ikona bola prázdna alebo poškodená"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nie je uvedený identifiká<PERSON>"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Stránka nemá webovú adresu <link> manifestu"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Nepodarilo sa skontrolovať obsluhu bez poľa „start_url“ v manifeste"}, "core/audits/installable-manifest.js | noErrorId": {"message": "Identifik<PERSON><PERSON> chyby inštalácie {errorId} nebol rozpoznaný"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Stránka sa nenačíta z bezpečného zdroja"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Stránka nie je načítaná v hlavnom rámci"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Stránka nefunguje offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Progresívna webová aplikácia bola odinštalovaná a resetujú sa kontroly inštalovateľnosti."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Špecifikovaná platforma aplikácie nie je v Androide podporovaná"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest špecifikuje parameter prefer_related_applications: pravda"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Parameter prefer_related_applications je podporovaný iba v beta verzii a stabilných verziách Chromu v Androide."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Nástroj Lighthouse nedokázal určiť, či je stránka inštalovateľná. Skúste to s novou verziou Chromu."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Z<PERSON>č<PERSON><PERSON>čná webová adresa manifestu nie je platná"}, "core/audits/installable-manifest.js | title": {"message": "Manifest webovej aplikácie a obsluha spĺňajú požiadavky na inštaláciu"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Webová adresa v manifeste obsahuje používateľské meno, heslo alebo port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Stránka nefunguje offline. Od stabilnej verzie Chromu 93 (august 2021) už stránku nebude možné inštalovať."}, "core/audits/is-on-https.js | allowed": {"message": "Povolené"}, "core/audits/is-on-https.js | blocked": {"message": "Blokované"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nezabezpečená webová adresa"}, "core/audits/is-on-https.js | columnResolution": {"message": "Riešenie žiadosti"}, "core/audits/is-on-https.js | description": {"message": "V<PERSON><PERSON>ky weby by mali by<PERSON> zabezpečené protokolom HTTPS, dokonca aj tie, ktoré ne<PERSON>jú citlivé údaje. Zahrnuje to nepoužívanie [z<PERSON><PERSON><PERSON><PERSON><PERSON> obsahu](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), v rámci ktorého sú niektoré zdroje načítané cez HTTP napriek odoslaniu pôvodnej žiadosti cez HTTPS. HTTPS bráni útočníkom narušovať komunikáciu medzi vašou aplikáciou a používateľmi alebo ju pasívne odpočúvať, a je podmienkou pre HTTP/2 a mnoho nových rozhraní API webových platforiem. [Ďalšie informácie o protokole HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Bola zistená 1 nezabezpečená žiadosť}few{Boli zistené # nezabezpečené žiadosti}many{# insecure requests found}other{Bolo zistených # nezabezpečených žiadostí}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Nepoužíva HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Používa HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automaticky inovované na HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Povolené s upozornením"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% vykreslenia najväčšieho prvku"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Časovanie"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Toto je prvok s najväčším obsahom vykresleným v oblasti zobrazenia. [Ďalšie informácie o prvku s vykreslením najväčšieho obsahu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Oneskorenie načítania"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Čas načítania"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Oneskorenie vykreslenia"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "Oneskorenie prvého bajtu"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Prvok vykreslenia najväčšieho obsahu"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Vplyv zmeny rozloženia"}, "core/audits/layout-shift-elements.js | description": {"message": "Tieto prvky DOM boli najviac ovplyvnené zmenami rozloženia. Niektoré zmeny rozloženia nemusia byť zahrnuté v hodnote metriky súhrnu posunov rozloženia stránky, pretože bol použitý [windowing](https://web.dev/articles/cls#what_is_cls). [Ako zlepšiť súhrn posunov rozloženia stránky](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "Nepoužívajte veľké posuny rozloženia"}, "core/audits/layout-shifts.js | columnScore": {"message": "Skóre posunu rozloženia"}, "core/audits/layout-shifts.js | description": {"message": "Toto sú najväčšie posuny rozloženia zaznamenané na danej stránke. Každá položka tabuľky predstavuje jeden posun rozloženia a zobrazuje prvok, ktorý sa posunul najviac. Pod každou položkou sú uvedené možné hlavné príčiny, ktoré viedli k posunu rozloženia. Niektoré z týchto posunov rozloženia nemusia byť zahrnuté v hodnote metriky Súhrn posunov rozloženia stránky v dôsledku [windowingu](https://web.dev/articles/cls#what_is_cls). [Ako zlepšiť súhrn posunov rozloženia stránky](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{Bol nájdený 1 posun rozloženia}few{Boli nájdené # posuny rozloženia}many{# layout shifts found}other{Bolo nájdených # posunov rozloženia}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Webové písmo bolo načí<PERSON>"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Vložený prvok iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Posledná sieťová požiadavka upravila rozloženie stránky"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Mediálny prvok nemá explicitnú veľkosť"}, "core/audits/layout-shifts.js | title": {"message": "Nepoužívajte veľké posuny rozloženia"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Lenivo načítané obrázky nad záhybom stránky sa vykreslia v neskoršej fáze životného cyklu stránky, čo môže oneskoriť vykreslenie najväčšieho obsahu. [Ďalšie informácie o optimálnom lenivom načítaní](https://web.dev/articles/lcp-lazy-loading)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Obrázok s vykreslením najväčšieho obsahu bol lenivo načítaný"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Obrázok s vykreslením najväčšieho obsahu nebol lenivo načítaný"}, "core/audits/long-tasks.js | description": {"message": "Sú tu uvedené najdlhšie úlohy hlavného vlákna slúžiace na identifikáciu faktorov, ktoré najväčšou mierou prispievajú k oneskoreniu vstupu. [Ako sa vyhnúť dlhým úlohám hlavného vlákna](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Našla sa # dlhá <PERSON>}few{Našli sa # dlhé ú<PERSON>}many{# long tasks found}other{Našlo sa # dlhých ú<PERSON>}}"}, "core/audits/long-tasks.js | title": {"message": "Nepoužívajte dlhé úlohy v hlavnom vlákne"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategória"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Zvážte skrátenie času strá<PERSON>ého analý<PERSON>u, zostavovaním a spustením JavaScriptu. Možno vám s tým pomôže zobrazovanie menších prenášaných dát JavaScriptu. [Ako minimalizovať prácu v hlavnom vlákne](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalizujte prá<PERSON> hlavného vlákna"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizuje prácu hlavného vlákna"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Ak chcete osloviť maximálny počet používateľov, weby by mali fungova<PERSON> vo všetk<PERSON>ch hlavných prehliadačoch. [Ďalšie informácie o kompatibilite s rôznymi prehliadačmi](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Web funguje v rôznych prehliadačoch"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON>, aby jednotlivé stránky podporovali priame odkazovanie prostredníctvom webovej adresy a aby dané webové adresy boli jedinečné na účely zdieľania v sociálnych médiách. [Ďalšie informácie o poskytnutí priamych odkazov](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> stránka má webovú adresu"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Prechody by pri klepaní mali pôsobiť plynulo, dokonca aj v pomalej sieti. Ide o kľúčový faktor ovplyvňujúci vnímanú výkonnosť. [Ďalšie informácie o prechodoch stránok](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON>dy stránok zrejme nie sú blokované sieťou"}, "core/audits/maskable-icon.js | description": {"message": "Maskovateľná ikona zaisťuje, že obrázok pri inštalácii aplikácie v zariadení zaplní celý tvar bez čiernych okrajov hore a dole. [Ďalšie informácie o maskovateľných ikonách manifestov](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest nemá maskovateľnú ikonu"}, "core/audits/maskable-icon.js | title": {"message": "Manifest má maskovateľnú ikonu"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulatívna zmena rozloženia meria pohyb viditeľných prvkov v rámci oblasti zobrazenia. [Ďalšie informácie o metrike Kumulatívna zmena rozloženia](https://web.dev/articles/cls)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvé vykreslenie obsahu označuje č<PERSON>, za ktorý je vykreslený prvý text alebo obrázok. [Ďalšie informácie o metrike Prvé vykreslenie obsahu](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON>rv<PERSON> zmy<PERSON>lu<PERSON>lné vykreslenie meria, kedy je hlavný obsah stránky viditeľný. [Ďalšie informácie o metrike Prvé zmysluplné vykreslenie](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Čas od interakcie do ďalšieho vykreslenia meria responzívnosť stránky, teda ako dlho jej trvá viditeľne odpovedať na vstup používateľa. [Ďalšie informácie o metrike Čas od interakcie do ďalšieho vykreslenia](https://web.dev/articles/inp)"}, "core/audits/metrics/interactive.js | description": {"message": "Čas do interaktivity je údaj o tom, ko<PERSON><PERSON> času prejde, kým bude stránka plne interaktívna. [Ďalšie informácie o metrike Čas do interaktivity](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Vykreslenie najväčšieho obsahu označuje čas, za ktorý sa vykreslí najväčší text alebo obrázok. [Ďalšie informácie o metrike Vykreslenie najväčšieho obsahu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Maximálne potenciálne oneskorenie prvého vstupu, ktoré sa u vašich používateľov môže vyskytnúť, je trvanie najdlh<PERSON>. [Ďalšie informácie o metrike Maximálne potenciálne oneskorenie prvého vstupu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "Index rýchlosti znázorňuje, za aký čas sa viditeľne doplní obsah stránky. [Ďalšie informácie o metrike Index rýchlosti](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Súčet všetkých časových období medzi prvým vykreslením obsahu a časom do interaktivity, kedy dĺžka úlohy presiahla 50 ms (vyjadrený v milisekundách). [Ďalšie informácie o metrike Celkový čas blokovania](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Časy vrátenia žiadostí v rámci siete majú veľký vplyv na výkonnosť. Ak je čas vrátenia žiadostí v rámci siete k pôvodnému zdroju vysoký, znamená to, že servery umiestnené bližšie k používateľovi by moh<PERSON> výkonnosť. [Ďalšie informácie o čase vrátenia žiadosti](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "Časy vrátania žiadostí v rámci siete"}, "core/audits/network-server-latency.js | description": {"message": "Výkonnosť webu môžu ovplyvniť latencie servera. Ak je latencia zdrojového servera vysoká, znamená to, že je preťažený alebo má slabú výkonnosť. [Ďalšie informácie o čase odpovede servera](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Latencie na strane servera"}, "core/audits/no-unload-listeners.js | description": {"message": "Udal<PERSON>ť `unload` sa nespúšťa spoľahlivo a jej prijímanie môže brániť optimalizáciám prehlia<PERSON>, ako je spätná vyrovnávacia pamäť. Použite namiesto nej udalosť `pagehide` alebo `visibilitychange`. [Ďalšie informácie o uvoľňovacích prijímačoch udalostí](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registruje sa ako prijímač udalosti `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Nepoužíva prijímače udalosti `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Nezložené animácie môžu byť nekvalitné a môžu zvýšiť kumulatívnu zmenu rozloženia. [Ako sa vyhnúť nezloženým animáciám](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Bol nájdený # animovaný prvok}few{Boli nájdené # animované prvky}many{# animated elements found}other{<PERSON><PERSON> nájdených # animovaných prvkov}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Vlastnosť súvisiaca s filtrom môže posunúť pixely"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON><PERSON> obsahuje inú animáciu, k<PERSON><PERSON> nie je kompatibilná"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efekt má zložený režim, ktorý je iný ako „nahradiť“"}, "core/audits/non-composited-animations.js | title": {"message": "Nepoužívajte nezložené animácie"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Vlastnosť súvisiaca s transformáciou závisí od veľkosti poľa"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepodporovaná vlastnosť šablóny CSS: {properties}}few{Nepodporované vlastnosti šablóny CSS: {properties}}many{Nepodporované vlastnosti šablóny CSS: {properties}}other{Nepodporované vlastnosti šablóny CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekt má nepodporované parametre časovania"}, "core/audits/performance-budget.js | description": {"message": "Udržujte množstvo a veľkosť žiadostí siete pod cieľovými hodnotami stanovenými poskytnutým rozpočtom výkonnosti. [Ďalšie informácie o výkonnostných rozpočtoch](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 žiadosť}few{# žia<PERSON><PERSON>}many{# requests}other{# žiadostí}}"}, "core/audits/performance-budget.js | title": {"message": "Rozpočet výkonnosti"}, "core/audits/preload-fonts.js | description": {"message": "Prednačítajte písma s atribútom `optional`, aby ich mohli použí<PERSON>ť noví návštevníci. [Ďalšie informácie o prednačítavaní písiem](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Písma s atribútom `font-display: optional` nie sú prednač<PERSON>é"}, "core/audits/preload-fonts.js | title": {"message": "Písma s atribútom `font-display: optional` sú prednač<PERSON>tané"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ak bol na stránku dynamicky pridaný prvok vykreslenia najväčšieho obsahu, ob<PERSON><PERSON><PERSON><PERSON> by ste mali predn<PERSON>, aby ste zlepšili vykreslenie najväčšieho obsahu. [Ďalšie informácie o prednačítaní prvkov vykreslenia najväčšieho obsahu](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Prednačítajte obrázok vykreslenia najväčšieho obsahu"}, "core/audits/redirects.js | description": {"message": "Presmerovania spôsobujú ďalšie oneskorenia pri načíta<PERSON>í strán<PERSON>. [Ako sa vyhnúť presmerovaniam stránky](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Vyhnite sa viacnásobným presmerovaniam stránky"}, "core/audits/seo/canonical.js | description": {"message": "Kanonické od<PERSON>, k<PERSON><PERSON> webová adresa sa má zobraziť vo výsledkoch vyhľadávania. [Ďalšie informácie o kanonických odkazoch](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON>du<PERSON><PERSON><PERSON><PERSON> ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Neplatná webová adresa ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Odkazuje na iné umiestnenie `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "<PERSON>ová adresa nie je absolútna ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Odkazuje na koreňovú webovú adresu domény (domovskú stránku), a nie na ekvivalentnú stránku s obsahom"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nemá platný odkaz `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument má platný odkaz `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON>d<PERSON><PERSON> nie je možné <PERSON>"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Vyhľadávače môžu na prehľadávanie webov používať v odkazoch atribúty `href`. Uistite sa, či je atribút `href` prvkov kotvy prepojený s príslušným cieľom, aby bolo možné zobraziť viac stránok webu. [<PERSON><PERSON>, aby boli odkazy prehľadávateľné](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> nie je možn<PERSON>"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> je mož<PERSON>"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Ďalší nečitateľný text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Veľkosť písma"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% textu na stránke"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Veľkosti písma menšie ako 12 px sú príliš malé na to, aby boli čitateľné, a nútia používateľov mobilných zariadení, aby si ich priblížili stiahnutím prstov. Zaistite, aby viac ako 60 % textu na stránke malo veľ<PERSON>ť ≥ 12 px. [Ďalšie informácie o čitateľných veľkostiach písma](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} čitateľného textu"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Text nie je <PERSON>, pretože neexistuje žiadna metaznačka oblasti zobrazenia optimalizovaná pre mobilné obrazovky."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokument nepoužíva čitateľné veľkosti písma"}, "core/audits/seo/font-size.js | legibleText": {"message": "Čitateľný text"}, "core/audits/seo/font-size.js | title": {"message": "Dokument používa čitateľné veľkosti písma"}, "core/audits/seo/hreflang.js | description": {"message": "O<PERSON><PERSON><PERSON> hreflang informujú v<PERSON>, a<PERSON><PERSON> by ma<PERSON> u<PERSON> vo výsledkoch vyhľadávania pre daný jazyk alebo oblasť. [Ďalšie informácie o útržku `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nemá platný atribút `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relatívna hodnota href"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument má platný odkaz `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Neočakávaný kód jazyka"}, "core/audits/seo/http-status-code.js | description": {"message": "Stránky s neúspešnými stavovými kódmi HTTP sa nemusia správne indexovať. [Ďalšie informácie o stavových kódoch HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Stránka má neúspešný stavový kód HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "Stránka má úspešný stavový kód HTTP"}, "core/audits/seo/is-crawlable.js | description": {"message": "Ak nemajú vyhľadávače povolenie prehľadávať va<PERSON><PERSON>, nemô<PERSON>u ich zahrnúť do výsledkov vyhľadávania. [Ďalšie informácie o direktívach indexového prehľadávača](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Strán<PERSON> má blokované indexovanie"}, "core/audits/seo/is-crawlable.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> blokované indexovanie"}, "core/audits/seo/link-text.js | description": {"message": "Opisný text odkazu pomáha vyhľadávačom pochopiť váš obsah. [<PERSON><PERSON> zlep<PERSON>ť dostupnosť odkazov](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Naš<PERSON> sa 1 odkaz}few{Našli sa # odkazy}many{# links found}other{Našlo sa # odkazov}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> popisný text"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> text"}, "core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON> [tester štruk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dát](https://search.google.com/structured-data/testing-tool/) a nástroj [Structured Data Linter](http://linter.structured-data.org/) na overenie štruktúrovaných dát. [Ďalšie informácie o štruktúrovaných dátach](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Štruktúrov<PERSON>é dáta s<PERSON> p<PERSON>"}, "core/audits/seo/meta-description.js | description": {"message": "Vo výsledkoch vyhľadávania môžu byť zahrnuté metaopisy, ktoré stručne zhrnú obsah stránky. [Ďalšie informácie o metaopise](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Text popisu ch<PERSON>."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nemá metapopis"}, "core/audits/seo/meta-description.js | title": {"message": "Do<PERSON><PERSON> má metapopis"}, "core/audits/seo/plugins.js | description": {"message": "Vyhľadávače nemôžu indexovať obsah doplnkov a mnoho zariadení obmedzuje doplnky alebo ich nepodporuje. [Ako sa vyhnúť doplnkom](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument používa doplnky"}, "core/audits/seo/plugins.js | title": {"message": "Dokument nepoužíva doplnky"}, "core/audits/seo/robots-txt.js | description": {"message": "Ak máte chybný súbor robots.txt, prehľadávače nemusia pochopiť, akým spôsobom majú v<PERSON>š web prehľadávať alebo indexovať. [Ďalšie informácie o súboroch robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Žiadosť o súbor robots.txt vrátila stav HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Našla sa 1 chyba}few{Našli sa # chyby}many{# errors found}other{Našlo sa # chýb}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Nástroj Lighthouse nedokázal stiahnuť súbor robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Súbor robots.txt nie je platný"}, "core/audits/seo/robots-txt.js | title": {"message": "Súbor robots.txt je platný"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktívne prvky, ako s<PERSON> t<PERSON>čidl<PERSON> a od<PERSON><PERSON>, by ma<PERSON> by<PERSON> dostato<PERSON> (48 × 48 px) a mať okolo seba dostatočné voľné miesto, aby sa na ne dalo ľahko klepnúť a neprekrývali ďalšie prvky. [Ďalšie informácie o cieľoch klepnutí](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} cieľových oblastí s vhodnou veľkosťou"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Cieľové oblasti klepnutia sú pr<PERSON><PERSON>, pretože neexistuje žiadna metaznačka oblasti zobrazenia optimalizovaná pre mobilné obrazovky"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Cieľové oblasti klepnutia nemajú vhodnú veľkosť"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Prekrývajúce sa cieľové oblasti"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON>ová oblasť klepnutia"}, "core/audits/seo/tap-targets.js | title": {"message": "Cieľové oblasti klepnutia majú vhodnú veľkosť"}, "core/audits/server-response-time.js | description": {"message": "<PERSON><PERSON><PERSON>, aby bol čas odozvy servera pre hlavný dokument krátky, preto<PERSON>e od neho závisia všetky ostatné žiadosti. [Ďalšie informácie o metrike Čas do prvého bajtu](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Hlavný dokument trval {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Skráťte počiatočný čas odpovede servera"}, "core/audits/server-response-time.js | title": {"message": "Počiatočný čas odpovede servera bol krátky"}, "core/audits/splash-screen.js | description": {"message": "Úvodná obrazovka s motívom zaistí skvelý dojem používateľa, keď si spustí vašu aplikáciu z plochy. [Ďalšie informácie o úvodných obrazovkách](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "Nie je nakonfigurovaný pre vlastnú úvodnú obrazovku"}, "core/audits/splash-screen.js | title": {"message": "Nakonfigurované pre vlastnú úvodnú obrazovku"}, "core/audits/themed-omnibox.js | description": {"message": "Panel s adresou prehlia<PERSON>a je možné upraviť motívom, aby zodpovedal vášmu webu. [Ďalšie informácie o pridaní motívu panela s adresou](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Nenastavuje farbu motívu pre panel s adres<PERSON>."}, "core/audits/themed-omnibox.js | title": {"message": "Nastavuje farbu motívu pre panel s adres<PERSON>."}, "core/audits/third-party-cookies.js | description": {"message": "Podpora súborov cookie tretej strany bude v budúcej verzii Chromu odstránená. [Ďalšie informácie o ukončení používania súborov cookie tretej strany](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON> nájdený 1 súbor cookie}few{Boli nájdené # súbory cookie}many{# cookies found}other{<PERSON><PERSON> nájdených # súborov cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Používa súbory cookie tretej strany"}, "core/audits/third-party-cookies.js | title": {"message": "Nepoužíva súbory cookie tretej strany"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (zákaznícka podpora)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (soci<PERSON><PERSON>e)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Služba"}, "core/audits/third-party-facades.js | description": {"message": "Niektoré vložené prvky tretej strany je možné načítať lenivo. <PERSON><PERSON>ž budú pož<PERSON>, zv<PERSON>žte ich nahradenie fasádou. [<PERSON><PERSON> odložiť tretie strany pomocou fasády](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{K dispozícii je # alternatíva fasády}few{K dispozícii sú # alternatívy fasády}many{# facade alternatives available}other{K dispozícii je # alternatív fasády}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Niektoré zdroje tretej strany je možné odložene načítať s fasádou"}, "core/audits/third-party-facades.js | title": {"message": "Odložené načítanie zdrojov tretej strany s fasádami"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON>ia strana"}, "core/audits/third-party-summary.js | description": {"message": "<PERSON><PERSON><PERSON> tretích strán môže výrazne ovplyvniť výkonnosť načítavania. Obmedzte počet nadbytočných poskytovateľov tretích strán a skúste načítavať kód tretích strán po dokončení načítavania stránky. [<PERSON><PERSON> minimaliz<PERSON>ť vplyv tretích strán](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> vl<PERSON>no bolo {timeInMs, number, milliseconds} ms zablokované kódom tretej strany"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Zníženie vplyvu kódu tretích strán"}, "core/audits/third-party-summary.js | title": {"message": "Minimalizujte použitie riešení tretích strán"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Met<PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Nastavte časový rozpočet, ktorý vám pomôže dohliadať na výkonnosť svojho webu. Výkonné stránky sa načítajú rýchlo a okamžite reagujú na udalosti vstupu používateľov. [Ďalšie informácie o výkonnostných rozpočtoch](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Časový rozpočet"}, "core/audits/unsized-images.js | description": {"message": "V prvkoch obrázka nastavte explicitnú šírku aj výšku, aby ste znížili posuny rozloženia a zlepšili kumulatívnu zmenu rozloženia. [Ako nastaviť rozmery obrázkov](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Prvky obr<PERSON><PERSON>kov nemaj<PERSON> <PERSON><PERSON><PERSON> hodn<PERSON>y `width` a `height`"}, "core/audits/unsized-images.js | title": {"message": "Prvky obrázka majú <PERSON> hodnoty `width` a `height`"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Odporúčame do aplikácie implementovať rozhranie User Timing API, ktoré umožňuje odmerať jej skutočnú výkonnosť počas udalostí kľúčových pre dojem používateľov. [Ďalšie informácie o značkách Trvania aktivít používateľov](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 trvanie aktivít používateľov}few{# trvania aktivít používateľov}many{# user timings}other{# trvaní aktivít používateľov}}"}, "core/audits/user-timings.js | title": {"message": "Značky a merania trvania aktivít používateľov"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Pre web {securityOrigin} sa našiel atribút `<link rel=preconnect>`, prehliadač ho však nepoužil. Skontrolujte, či atribút `crossorigin` používate správne."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Zvážte pridanie indikátorov zdrojov `preconnect` a `dns-prefetch`, ktor<PERSON> vám pomôžu zriadiť predbežné pripojenia k dôležitým zdrojom tretích strán. [Ako sa predbežne pripojiť k požadovaným zdrojom](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Nastavte predbežné pripojenie k požadovaným zdrojom"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Naš<PERSON> sa viac ako dve pripojenia `<link rel=preconnect>`. Mali by byť používané <PERSON>orne a iba pre najdôležitejšie zdroje."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Pre web {securityOrigin} sa našiel atribút `<link rel=preconnect>`, prehliadač ho však nepoužil. `preconnect` používajte iba v prípade dôležitých zdrojov, o ktoré stránka určite požiada."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Pre web {preloadURL} sa našiel atribút `<link>`, prehliadač ho však nepoužil. Skontrolujte, či atribút `crossorigin` používate správne."}, "core/audits/uses-rel-preload.js | description": {"message": "Zvážte použitie funkcie `<link rel=preload>`, čím uprednostníte načítanie zdrojov momentálne požadovaných v neskoršej fáze načítania stránky. [Ako prednačítavať kľúčové žiadosti](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Predbežne načítavajte kľúčové žiadosti"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Webová adresa mapy"}, "core/audits/valid-source-maps.js | description": {"message": "Mapy zdroja prekladajú minifikovaný kód na pôvodný zdrojový kód. Vývojárom to pomáha pri ladení ostrej verzie. Lighthouse dokáže okrem toho poskytnúť ďalšie štatistiky. Zvážte nasadenie máp zdroja, aby ste tieto výhody vyu<PERSON>. [Ďalšie informácie o mapách zdroja](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Veľkej vlastnej knižnici JavaScriptu chýbajú mapy zdroja"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Veľkému súboru JavaScript chýba mapa zdroja"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Upozornenie: V atribúte `.sourcesContent` chýba 1 položka}few{Upozornenie: V atribúte `.sourcesContent` chýbajú # položky}many{Warning: missing # items in `.sourcesContent`}other{Upozornenie: V atribúte `.sourcesContent` chýba # položiek}}"}, "core/audits/valid-source-maps.js | title": {"message": "Stránka obsahuje platné mapy zdroja"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` nielen optimalizuje vašu aplikáciu pre veľkosti obrazoviek mobilných zariadení, ale zabraňuje aj [300 milisekundovému oneskoreniu vstupu používateľa](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Ďalšie informácie o používaní metaznačky oblasti zobrazenia](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Nenašla sa žiadna značka `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "<PERSON><PERSON>á <PERSON> `<meta name=\"viewport\">` s vlast<PERSON><PERSON> `width` alebo `initial-scale`"}, "core/audits/viewport.js | title": {"message": "O<PERSON><PERSON><PERSON> z<PERSON> `<meta name=\"viewport\">` s vlast<PERSON><PERSON> `width` alebo `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Ide o prá<PERSON> blokovania vlákien počas merania času od interakcie do ďalšieho vykreslenia. [Ďalšie informácie o metrike Čas od interakcie do ďalšieho vykreslenia](https://web.dev/articles/inp)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "<PERSON> udalosť {interactionType} bolo minutých {timeInMs, number, milliseconds} ms"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimalizujte prácu počas interakcie s klávesmi"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Oneskorenie vstupu"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Oneskorenie prezentácie"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Čas spracovania"}, "core/audits/work-during-interaction.js | title": {"message": "Minimalizácia práce počas interakcie s klávesmi"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Toto sú možnosti zlepšenia technológie ARIA vo va<PERSON><PERSON> aplik<PERSON>i, čo môže pomôcť používateľom asistenčných technológií (napr. čítačiek obrazovky)."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Toto sú možnosti na poskytnutie alternatívneho obsahu pre zvuk a video. <PERSON><PERSON><PERSON><PERSON> to zlepšiť dojem používateľov so slabým sluchom alebo zrakom."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk a video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Tieto položky zvýrazňujú bežné osvedčené postupy týkajúce sa dostupnosti."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>upy"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Tieto kontroly zvýrazňujú príležitosti na [zlepšenie dostupnosti vašej webovej aplikácie](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatické rozpoznávanie môže rozpoznať iba podskupinu problémov a nezaručuje dostupnosť vašej webovej aplikácie, preto odporúčame použiť aj [manu<PERSON>lne testovanie](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Tieto položky sa zaoberajú oblasťami, ktor<PERSON>ké testovanie nemôže obsiahnuť. Ďalšie informácie získate v [sprievodcovi vykonávaním kontroly dostupnosti](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Dostupnosť"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Toto sú možnosti vylepšenia čitateľnosti obsahu."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Toto sú možnosti zlepšenia interpretácie vášho obsahu používateľmi rôznych jazykov."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizácia a lokalizácia"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Toto sú možnosti vylepšenia sémantiky ovládacích prvkov vašej apliká<PERSON>. Môže to pomôcť používateľom asistenčných technológií, ako sú čítačky obrazovky."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Názvy a štítky"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Toto sú možnosti zlepšenia prechádzania vašej aplikácie pomocou klávesnice."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigácia"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Toto sú možnosti na zlepšenie dojmu z čítania údajov v tabuľke alebo zozname pomocou asistenčných technológií, ako je čítačka obrazovky."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabuľky a zoznamy"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilita prehliadača"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>upy"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Všeobecné"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Dôvera a zabezpečenie"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Používateľské prostredie"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Rozpočty výkonnosti sú štandardy výkonnosti vášho webu."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Ďalšie informácie o výkonnosti vašej aplikácie. Tieto hodnoty [priamo ovplyvňuj<PERSON>](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) skóre výkonnosti."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najpodstatnejší aspekt výkonnosti je čas, za ktorý sa pixely vykreslia na obrazovke. Kľúčové metriky: <PERSON>rvé obsahové vyfarbenie, <PERSON>rvé účelné vyfarbenie."}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Vylepšenia prvého vyfarbenia"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Tieto návrhy môžu pomôcť s rýchlejším načítavaním va<PERSON><PERSON> str<PERSON>. [Neovplyvňu<PERSON><PERSON> priamo](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) skóre výkonnosti."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Príležitosti"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Vylepšite celkové načítavanie, aby bola stránka čo najskôr responzívna a pripravená na použitie. Kľúčové metriky: Čas do interaktívneho vykreslenia, Index rýchlosti"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Celkové vylepšenia"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Výkonnosť"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Tieto kontroly overujú aspekty progresívnej webovej aplikácie. [Čím sa vyznačuje dobrá progresívna webová aplikácia](https://web.dev/articles/pwa-checklist)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Tieto kontroly vyžaduje základný [kontrolný zoznam PWA](https://web.dev/articles/pwa-checklist), ale Lighthouse ich automaticky nevykonáva. Nemajú vplyv na vaše skóre, ale je dôležité, aby ste ich overili ručne."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Inštalovateľné"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimalizované pre PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Tieto kontroly zaisťujú, že va<PERSON> stránka nasleduje radu týkajúcu sa základnej optimalizácie pre vyhľadávače. Vašu pozíciu vo vyhľadávaní vrátane výkonnosti v prehľade [Core Web Vitals](https://web.dev/explore/vitals) môže ovplyvňovať mnoho faktorov, ktoré tu Lighthouse neuvádza. [Ďalšie informácie o programe Google Search Essentials](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Ak chcete skontrolovať ďalšie osvedčené postupy SEO, spusťte na svojom webe tieto ďalšie nástroje na overenie."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Naformátujte kód HTML svojej aplikácie spôsobom, ktor<PERSON> umožní prehľadávačom lepšie pochopiť jej obsah."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Osvedčené postupy pre obsah"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Prehľadávače potrebujú prístup k vašej aplikácii, aby sa mohla zobraziť vo výsledkoch vyhľadávania."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Prehľadávanie a indexovanie"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Upravte svoje stránky pre mobilné zariadenia, aby používatelia pri čítaní ich obsahu nemuseli sťahovať prsty ani približovať. [Ako upraviť stránky, aby boli vhodné pre mobilné zariadenia](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Vhodné pre mobilné zariadenia"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Testované zariadenie má zrejme pomalší procesor ako Lighthouse očakáva. M<PERSON>ž<PERSON> to záporne ovplyvniť vaše skóre výkonnosti. [Ďalšie informácie o kalibrácii vhodného multiplikátora spomalenia procesora](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)"}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON><PERSON><PERSON> str<PERSON> sa nemusí načítavať podľa očakávaní, preto<PERSON><PERSON> testovacia webová adresa ({requested}) bola presmerovaná na {final}. Skúste priamo otestovať druhú webovú adresu."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Načítanie stránky bolo pr<PERSON><PERSON><PERSON> p<PERSON>, aby sa dokončilo v časovom limite. Výsledky nemusia byť úplné."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Vymazanie vyrovnávacej pamäte prehliadača vypršalo. Skúste túto stránku skontrolovať znova a ak problém pretrváva, nahláste chybu."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{V nasledujúcom umiestnení môžu byť uložené údaje, ktoré ovplyvňujú výkonnosť načítavania: {locations}. Skontrolujte túto stránku v okne inkognito, aby príslušné zdroje neovplyvnili vaše skóre.}few{V nasledujúcich umiestneniach môžu byť uložené údaje, ktoré ovplyvňujú výkonnosť načítavania: {locations}. Skontrolujte túto stránku v okne inkognito, aby príslušné zdroje neovplyvnili vaše skóre.}many{V nasledujúcich umiestneniach môžu byť uložené údaje, ktoré ovplyvňujú výkonnosť načítavania: {locations}. Skontrolujte túto stránku v okne inkognito, aby príslušné zdroje neovplyvnili vaše skóre.}other{V nasledujúcich umiestneniach môžu byť uložené ú<PERSON>, ktoré ovplyvňujú výkonnosť načítavania: {locations}. Skontrolujte túto stránku v okne inkognito, aby príslušné zdroje neovplyvnili vaše skóre.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Vymazávanie údajov o zdroji vypršalo. Skúste túto stránku skontrolovať znova a ak problém pretrváva, nahláste chybu."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Chyba pri analý<PERSON> `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Počas spustenia bola zistená navigácia na stránke. Používanie režimu časového rozsahu na kontrolu navigácie na stránke sa neodporúča. Navigácie na stránke môžete skontrolovať v režime navigácie. Získate lepšiu atribúciu tretích strán a detekciu hlavného vlákna."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Iba stránky načítané prostredníctvom požiadavky GET môžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Do vyrovnávacej pamäte sa dajú ukladať iba stránky so stavovým kódom 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome rozpoznal pokus o spustenie JavaScriptu počas uloženia vo vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> o AppBanner, <PERSON><PERSON>lne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná v dôsledku žiadosti so signálom keepalive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná experimentálnymi funkciami. Prejdite na chrome://flags/#back-forward-cache a aktivujte ju miestne v tomto zariadení."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná príkazovým riadkom."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná z dôvodu nedostatočnej pamäte."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Delegát nepodporuje spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná pre predbežný vykresľovací modul."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Stránka sa nedá uložiť do vyrovnávacej pamäte, pretože má inštanciu BroadcastChannel s registrovanými prijímačmi."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Strán<PERSON> s hlavičkou cache-control:no-store nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Vyrovnávacia pamäť bola zámerne vyčistená."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, aby bolo do nej možné ulož<PERSON>ť inú."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Stránky s doplnkami momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Stránky s rozhraním FileChooser API nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Stránky s rozhraním File System Access API momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Stránky s nástrojom Media Device Dispatcher nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "V čase odchodu hral Prehrávač médií."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Stránky s rozhraním MediaSession API a nastaveným stavom prehrávania nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Stránky s rozhraním MediaSession API a nastavenými obslužnými nástrojmi akcií momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Čítačka obrazovky spôsobila deaktiváciu spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Stránky s nástrojom SecurityHandler nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Stránky s rozhraním Serial API nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Stránky s rozhraním WebAuthetication API nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Stránky s rozhraním WebBluetooth API nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Stránky s rozhraním WebUSB API nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná, preto<PERSON><PERSON> sú<PERSON> cookie sú z<PERSON>zané na stránke, ktorá používa `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Stránky s vyhradenou obsluhou alebo workletom momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Načítavanie dokumentu nebolo dokončené, kým ste odli<PERSON> preč."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "V čase odchodu bol zobrazený banner aplikácie."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "V čase odchodu bol zobrazený správca hesiel Chromu"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "V čase odchodu prebiehala extrakcia pomocou nástroja DOM."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "V čase odchodu bol otvorený zobrazovač nástroja DOM Distiller."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Rozšírenia používajúce četové rozhranie API spôsobili deaktiváciu spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Rozšírenia s dlhotrvajúcim pripojením by mali pred získaním prístupu k spätnej vyrovnávacej pamäti pripojenie ukončiť."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Rozšírenia s dlhotrvajúcim pripojením sa pokúsili odoslať správy rámom v spätnej vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Rozšírenia spôsobili deaktiváciu spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "V čase odchodu bolo na stránke zobrazené modálne dialógové okno, ktoré sa mohlo napríklad týkať opätovného odoslania formulára alebo hesla HTTP."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "V čase odchodu bola zobrazená offline stránka."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "V čase odchodu bol zobrazený intervenčný pruh znamenajúci nedostatok pamäte."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "V čase odchodu bola odoslaná žiadosť o povolenie."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "V čase odchodu bola zobrazená možnosť blokovania vyskakovacích okien."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "V čase odchodu boli zobrazené podrobnosti Bezpečného prehliadania."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezpečné prehliadanie považuje túto stránku sa obťažujúcu a zablokovalo vyskakovacie okno."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Keď sa stránka nachádzala v spätnej vyrovnávacej pamäti, bola aktivovaná obsluha."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Spätná vyrovnávacia pamäť je deaktivovaná pre chybu v dokumente."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, nie je mož<PERSON>é uložiť v spätnej vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, aby bolo do nej možné ulož<PERSON>ť inú."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> k <PERSON>u <PERSON>, <PERSON><PERSON>lne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Stránky s portálmi momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Stránky s nástrojom IdleManager momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Stránky s otvoreným pripojením IndexedDB momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná v dôsledku udalosti IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Boli použité nevhodné rozhrania API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, do ktor<PERSON>ch rozšírenia vložili `JavaScript`, nemôžu momentálne používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON>, do ktorých rozšírenia vložili šablónu`StyleSheet`, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Interná chyba."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Spätná vyrovnávacia pamäť bola deaktivovaná v dôsledku žiadosti so signálom keepalive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Strán<PERSON> so zámkou klávesnice momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | loading": {"message": "Načítavanie stránky nebolo dokončené, kým ste odli<PERSON> pre<PERSON>."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hlavný zdroj má hlavičku cache-control:no-cache, momentálne nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> hlavný zdroj má hlavičku cache-control:no-store, nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Na<PERSON><PERSON><PERSON><PERSON> bola zrušená skôr, ako do<PERSON>lo k obnoveniu stránky zo spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, pretože aktívne pripojenie k sieti dostalo príliš veľa dát. Chrome obmedzuje objem dát, ktor<PERSON> môže dostávať stránka vo vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stránky s načítavaním počas prenosu() alebo XHR momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Strán<PERSON> bola vylúčená zo spätnej vyrovnávacej pamäte, pretože aktívna sieťová požiadavka zahŕňala presmerovanie."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Stránka bola vylúčená z vyrovnávacej pamäte, pretože pripojenie k sieti bolo príliš dlho otvorené. Chrome obmedzuje obdo<PERSON>, počas ktorého môže stránka vo vyrovnávacej pamäti dostávať dáta."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Stránky bez platnej hlavičky odpovede nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigácia sa uskutočnila v inom ako hlavnom ráme."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Stránky s prebiehajúcimi transakciami IndexedDB momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Strán<PERSON> so sieťovou požiadavkou počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Stránky so sieťovou požiadavkou na načítanie počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Strán<PERSON> so sieťovou požiadavkou počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Stránky so sieťovou požiadavkou XHR počas prenosu momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Stránky s nástrojom PaymentManager momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Stránky s obrazom v obraze momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | portal": {"message": "Stránky s portálmi momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | printing": {"message": "Stránky zobrazujúce používateľské rozhranie na tlač momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "St<PERSON>án<PERSON> bola otvorená pomocou prvku `window.open()` a ďalšia karta má odkaz na ňu, pr<PERSON><PERSON>ne stránka otvorila okno."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Proces vykresľovacieho modulu pre danú stránku v spätnej vyrovnávacej pamäti spadol."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Proces vykresľovacieho modulu pre danú stránku v spätnej vyrovnávacej pamäti bol zrušený."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON>dali o povolenia na snímanie zvuku, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre senzory, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, ktor<PERSON> p<PERSON>ž<PERSON>i o synchronizáciu na pozadí alebo načítanie povolení, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre MIDI, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o povolenia pre upozornenia, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o prístup k ukladaciemu <PERSON>u, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON>dal<PERSON> o povolenia na snímanie videí, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Do vyrovnávacej pamäte sa dajú uložiť iba str<PERSON>, k<PERSON><PERSON><PERSON> schéma webovej adresy je HTTP alebo HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Stránku si nárokovala obsluha počas obdobia, kedy sa nachádza v spätnej vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Obsluha sa pokúsila odoslať stránke v spätnej vyrovnávacej pamäti `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Registrácia funkcie ServiceWorker bola zrušená, keď sa stránka nachádzala v spätnej vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Stránka bola vylúčená zo spätnej vyrovnávacej pamäte z dôvodu aktivácie obsluhy."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome reštartoval a vymazal záznamy v spätnej vyrovnávacej pamäti."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Stránky s rozhraním SharedWorker momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Stránky s rozhraním SpeechRecognizer momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Stránky s rozhraním SpeechSynthesis momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Prvok iframe na stránke spustil navigáciu, ktorá nebola dokončená."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> podzdroj má hlavičku cache-control:no-cache, nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> podzdroj má hlavičku cache-control:no-store, momentálne nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Stránka prekročila maximálny čas v spätnej vyrovnávacej pamäti a vypršala."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Pri zadávaní stránky do spätnej vyrovnávacej pamäte vypršal časový limit (pravdepodobne to spôsobili dlhodobo spustené obslužné nástroje pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Stránka má v hlavnom ráme obslužný nástroj uvoľnenia z pamäte."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Stránka má v podráme obslužný nástroj uvoľnenia z pamäte."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Prehliadač zmenil hlavičku prepísania používateľského agenta."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> u<PERSON> pr<PERSON>p na nahrávanie videa alebo zvuku, momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Stránky s rozhraním WebDatabase momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Stránky s rozhraním WebHID momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Stránky s rozhraním WebLocks momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Stránky s rozhraním WebNfc momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Stránky s rozhraním WebOTPService momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Stránky s rozhraním WebRTC nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Stránky s rozhraním WebShare momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Stránky s rozhraním WebSocket nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Stránky s rozhraním WebTransport nemajú prístup do spätnej vyrovnávacej pamäte."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Stránky s rozhraním WebXR momentálne nemôžu používať spätnú vyrovnávaciu pamäť."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Zvážte pridanie schémy webovej adresy HTTPS alebo HTTP (ignorované prehliadačmi podporujúcimi direktívu `'strict-dynamic'`), aby boli pravidlá spätne kompatibilné so staršími prehliadačmi."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Podpora direktívy `disown-opener` bola ukončená vo verzii PZO3. Použitie namiesto nej hlavičku Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Podpora direktívy `referrer` bola ukončená vo verzii PZO2. Použite namiesto nej hlavičku Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Podpora direktívy `reflected-xss` bola ukončená vo verzii PZO2. Použite namiesto nej hlavičku X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Chýbajúca direktíva `base-uri` umožňuje vloženým značkám `<base>` nastaviť základnú webovú adresu pre všetky relatívne webové adresy (napr. skripty) na doménu ovládanú útočníkom. Zvážte nastavenie direktívy `base-uri` na `'none'` alebo `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "V prípade chýbajúcej direktívy `object-src` bude možné vkladať doplnky, ktoré spúš<PERSON>ajú nebezpečné skripty. Ak je to možn<PERSON>, zvážte nastavenie direktívy `object-src` na `'none'`."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Chýba direktíva `script-src`. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to spustenie nebezpečn<PERSON>ch skriptov."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "<PERSON>ab<PERSON>li ste na bodkočiarku? Z<PERSON><PERSON>, že {keyword} je direkt<PERSON>va, nie kľ<PERSON><PERSON><PERSON> slovo."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Iba raz použiteľné čísla by ma<PERSON> pou<PERSON> charset base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "<PERSON>ba raz použiteľné čísla by ma<PERSON> ma<PERSON> aspo<PERSON> osem miest."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Nepoužívajte v tejto direktíve čisté schémy webovej adresy ({keyword}). Čisté schémy webovej adresy umožňujú získavať skripty z nebezpečnej domény."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Nepoužívajte v tejto direktíve čisté zástupné znaky ({keyword}). Čisté zástupné znaky umožňujú získavať skripty z nebezpečnej domény."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Cieľ reportov sa konfiguruje iba prostredníctvom direktívy report-to. Táto direktíva podporovaná iba v prehliadačoch založených na platforme Chromium, takže sa odporúča použiť aj direktívu `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Žiadne PZO nekonfiguruje cieľ hlásení. Sťažuje to udržiavanie PZO v priebehu času a monitorovanie prípadných porúch."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Zoznamy povolených hostiteľov sa dajú často obísť. Ak je to potrebné, zv<PERSON>žte namiesto toho použitie iba raz použiteľných čísiel alebo hodnôt haš PZO spolu s direktívou `'strict-dynamic'`."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Neznáma direktíva PZO."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON>, <PERSON>e {keyword} je nep<PERSON><PERSON>é k<PERSON> slovo."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` umožňuje spustenie nebezpečných stránkových skriptov a obslužných nástrojov udalostí. Odporúčame povoliť skripty individuálne pomocou iba raz použiteľných čísiel alebo hodnôt haš PZO."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Zvážte pridať direktívu `'unsafe-inline'` (ignorovanú prehliadačmi podporujúcimi iba raz použiteľné čísla a hodnoty haš), aby boli pravidlá spätne kompatibilné so staršími prehlia<PERSON>čmi."}, "core/lib/deprecation-description.js | feature": {"message": "Ďalšie podrobnosti nájdete na stránke stavu funkcie."}, "core/lib/deprecation-description.js | milestone": {"message": "<PERSON><PERSON><PERSON> z<PERSON>a začne platiť po dosiahnutí míľnika {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Používa sa funkcia s ukončenou podporou"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Autorizácie sa nebude týkať zástupný symbol (*) pri spracovaní CORS `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Ak chcete zakázať predvolenú integráciu prenosu, použite namiesto selektora `-internal-media-controls-overlay-cast-button` atrib<PERSON>t `disableRemotePlayback`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Požiadavky zdr<PERSON><PERSON><PERSON>, k<PERSON><PERSON>ch webové adresy zahrnujú odstránené prázdne znaky `(n|r|t)` a znaky „menej ako“ (`<`), s<PERSON> blokovan<PERSON>. Ak chcete načítať tieto zdroje, odstráňte nové riadky a kódujte znaky „menej ako“ z miest, ako sú hodnoty atribútu prvkov."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Podpora funkcie `chrome.loadTimes()` bola ukončená. Namiesto nej použite štandardizované rozhranie API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "Podpora funkcie `chrome.loadTimes()` bola ukončená. Namiesto nej použite štandardizované rozhranie API: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Podpora funkcie `chrome.loadTimes()` bola ukončená. Namiesto nej použite štandardizované rozhranie API: `nextHopProtocol` v rámci funkcie Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON> cookie so znakom `(0|r|n)` budú namiesto skrátenia odmietnuté."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Podpora uvoľnenia pravidla z rovnakých zdrojov nastavením funkcie `document.domain` bola ukončená a táto možnosť bude predvolene zakázaná. Upozornenie na ukončenie podpory pochádza z prístupu z iných zdrojov, ktorý bol povolený nastavením funkcie `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Podpora spúšťania funkcie window.alert z prvkov iframe z rôznych zdrojov bola ukončená a v budúcnosti bude odstránená."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Podpora spúšťania funkcie window.confirm z prvkov iframe z rôznych zdrojov bola ukončená a v budúcnosti bude odstránená."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Podpora udalostí DOM Mutation vrátane `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` a `DOMCharacterDataModified` bola ukončená (https://w3c.github.io/uievents/#legacy-event-types) a udalosti budú odstránené. Použite namiesto nich `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Podpora údajov: podpora webových adries v prvku SVG <use> bola ukončená a v budúcnosti budú odstránené."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Podpora uvoľnenia pravidla z rovnakých zdrojov nastavením funkcie `document.domain` bola ukončená a táto možnosť bude predvolene zakázaná. Ak chcete naďalej používať túto funkciu, odhláste sa z klastrov zástupcu spojených so zdrojom odoslaním hlavičky `Origin-Agent-Cluster: ?0` spolu s odpoveďou HTTP pre dokument a rámy. Viac sa dozviete na https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "Podpora hlavičky `Expect-CT` bola ukončená a bude odstránená. Chrome vyžaduje transparentnosť pre všetky verejne dôveryhodné certifikáty vydané po 30. apríli 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Funkcie `getCurrentPosition()` a `watchPosition()` už nefungujú v nezabezpečených zdrojoch. Ak chcete túto funkciu používať, mali by ste zv<PERSON><PERSON><PERSON><PERSON> prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Podpora funkcií `getCurrentPosition()` a `watchPosition()` bola ukončená v nezabezpečených zdrojoch. Ak chcete túto funkciu používať, mali by ste zv<PERSON>žiť prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` už nefunguje v nezabezpečených zdrojoch. Ak chcete túto funkciu používať, mali by ste zv<PERSON>žiť prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Podpora funkcie `RTCPeerConnectionIceErrorEvent.hostCandidate` bola ukončená. Namiesto nej použite funkciu `RTCPeerConnectionIceErrorEvent.address` alebo `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Podpora údajov o zdroji obchodníka a ľubovoľných údajov z udalosti obsluhy `canmakepayment` bola ukončená a tieto údaje budú odstránené: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Web požiadal o podzdroj zo siete, ku ktorému má prístup iba na základe privilegovanej pozície siete svojich používateľov. Tieto požiadavky vystavujú neverejné zariadenia a servery internetu, čím zvyšujú riziko útoku zahrnujúceho falšovanie požiadaviek z iného webu alebo úniku informácií. Na zmiernenie týchto rizík Chrome ukončuje podporu požiadaviek neverejných podzdrojov pri ich odoslaní z nezabezpečených kontextov a začne ich blokovať."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Pole `dailyUpdateUrl` položky `InterestGroups` odovzdané položke `joinAdInterestGroup()` bolo premenované na `updateUrl`, aby presnejšie vyjadrovalo vlastné správanie."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Šablóny CSS nie je možné načítať z webových adries `file:`, pokiaľ sa nekončia príponou súboru `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Podpora použitia funkcie `SourceBuffer.abort()` na prerušenie odstránenia asynchrónneho rozsahu možnosti `remove()` bola ukončená z dôvodu zmeny špecifikácie. Podpora bude v budúcnosti odstránená. Namiesto toho by ste mali pri<PERSON> udal<PERSON>ť `updateend`. Funkcia `abort()` je určená iba na prerušenie pripojenia asynchrónnych médií alebo resetovanie stavu analyzátora."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Podpora nastavenia funkcie `MediaSource.duration` pod najvyššiu časovú pečiatku prezentácie akýchkoľvek kódovaných rámov vo vyrovnávacej pamäti bola ukončená z dôvodu zmeny špecifikácie. V budúcnosti bude odstránená podpora implicitného odstránenia skrátených médií vo vyrovnávacej pamäti. Namiesto toho by ste mali vykonať explicitnú funkciu `remove(newDuration, oldDuration)` v prípade všetkých možností `sourceBuffers`, kde platí `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Web MIDI požiada o povolenie na použitie, aj keď exkluzívne správy systému (SysEx) nie sú vo funkcii `MIDIOptions` špecifikované."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "Podpora staršieho neštandardizovaného atribútu `shadowroot` bola ukončená a vo verzii M119 *už nebude fungovať*. Použite namiesto neho nový štandardizovaný atribút `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Rozhranie Notification API už nie je možné používať z nezabezpečených zdrojov. Mali by ste zv<PERSON>ži<PERSON> prechod svojej aplikácie na zabezpečený zdroj, ako je HTTPS. Viac sa dozviete na https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Povolenie pre rozhranie Notification API už nie je možné požadovať z prvku iframe od iných zdrojov. Namiesto toho by ste mali zvážiť požadovanie povolenia z rámu najvyššej úrovne alebo otvorenie nového okna."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Podpora možnosti `imageOrientation: 'none'` v nástroji createImageBitmap bola ukončená. Namiesto nej použite nástroj createImageBitmap pomocou možnosti \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> partner p<PERSON><PERSON><PERSON><PERSON> (D)TLS. Požiada<PERSON><PERSON> ho, aby tento problém odstránil."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Ak v značkách img, značkách videa a značkách canvas špecifikujete atribút `overflow: visible`, môže to spôsobiť, že vytvoria vizuálny obsah mimo hraníc prvku. Prejdite na https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "Podpora rozhrania `paymentManager.instruments` bola ukončená. Použite namiesto nej inštaláciu v pravej chvíli pre obslužné nástroje platieb."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Volanie `PaymentRequest` obišlo direktívu `connect-src` pravidiel na zabezpečenie obsahu (PZO). Podpora tohto obchádzania bola ukončená. Pridajte identifikátor spôsobu platby z rozhrania API `PaymentRequest` (v poli `supportedMethods`) do direktívy `connect-src` svojich PZO."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Podpora rozhrania `StorageType.persistent` bola ukončená. Použite namiesto nej štandardizovaný parameter `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Prvok `<source src>` s nadradeným prvkom `<picture>` je <PERSON>, a preto bude ignorovaný. Použ<PERSON> radšej `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "webkitCancelAnimationFrame je metóda špecifická pre konkrétneho dodávateľa. Použite namiesto nej štandardnú metódu cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "webkitRequestAnimationFrame je metóda špecifická pre konkrétneho dodávateľa. Použite namiesto nej štandardnú metódu requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitDisplayingFullscreen bola ukončená. Použite namiesto neho Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitEnterFullScreen() bola ukončená. Použite namiesto neho Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitEnterFullscreen() bola ukončená. Použite namiesto neho Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitExitFullScreen() bola ukončená. Použite namiesto neho Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitExitFullscreen() bola ukončená. Použite namiesto neho Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Podpora rozhrania HTMLVideoElement.webkitSupportsFullscreen bola ukončená. Použite namiesto neho Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Ukončujeme podporu rozhrania API `chrome.privacy.websites.privacySandboxEnabled`, ale zostane aktívne na zaistenie spätnej kompatibility až do vydania verzie M113. Používajte namiesto neho `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` a `chrome.privacy.websites.adMeasurementEnabled`. Viac sa dozviete na https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "Ob<PERSON><PERSON><PERSON> `DtlsSrtpKeyAgreement` bolo odstr<PERSON>. Pre toto obmedzenie ste špecifikovali hodnotu `false`, ktor<PERSON> je interpretovaná ako pokus o použitie odstránenej metódy `SDES key negotiation`. T<PERSON><PERSON> funkcia bola odstránená. Namiesto nej použite službu, ktorá podporuje funkciu `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Obmed<PERSON><PERSON> `DtlsSrtpKeyAgreement` bolo odstr<PERSON>. Pre toto obmedzenie ste špecifikovali hodnotu `true`, ktor<PERSON> nemala žiadny účinok. Toto obmedzenie však môžete odstrániť pre poriadok."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Podpora metódy callback-based getStats() bola ukončená. <PERSON><PERSON><PERSON> metóda bude odstránená. Použite namiesto nej spec-compliant getStats()."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Podpora rozhrania Range.expand() bola ukončená. Použite namiesto neho Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Požiadav<PERSON> podzdr<PERSON>jov, ktor<PERSON>ch webové adresy zahrnujú vložen<PERSON> p<PERSON>ova<PERSON> ú<PERSON> (napr. `**********************/`), s<PERSON> blo<PERSON>n<PERSON>."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Podpora možnosti `rtcpMuxPolicy` bola ukončená a táto možnosť bude odstránená."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` bude vyžadovať izoláciu od iných zdrojov. Viac sa dozviete na https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "Podpora funkcie `speechSynthesis.speak()` bez aktivácie používateľa bola ukončená a funkcia bude odstránená."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Rozšírenia by sa mali prihlásiť do izolovania od iných zdrojov, ak chcú ďalej používať funkciu `SharedArrayBuffer`. Prejdite na https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Podpora jazyka Web SQL bola ukončená. Použite SQLite WebAssembly alebo indexovanú databázu."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Podpora deskriptora povolenia `window-placement` bola ukončená. Rad<PERSON><PERSON> p<PERSON> `window-management`. Ďalšiu pomoc nájdete na https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Podpora pravidiel povolenia `window-placement` bola ukončená. Rad<PERSON><PERSON> p<PERSON> `window-management`. Ďalšiu pomoc nájdete na https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "JSON odpoveď nepodporuje kódovanie UTF‑16 v rámci funkcie `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Podpora synchrónnej funkcie `XMLHttpRequest` v hlavnom vlákne bola ukončená pre nepriaznivé účinky na prostredie koncového používateľa. Viac sa dozviete na https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Podpora funkcie `supportsSession()` bola ukončená. Namiesto toho použite značku `isSessionSupported()` a skontrolujte vybranú logickú hodnotu."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Čas blokovania hlavného vlákna"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL vyrovnávacej pamäte"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Trvanie"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Prvok"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Prvky s chybami"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Umiestnenie"}, "core/lib/i18n/i18n.js | columnName": {"message": "N<PERSON>zov"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Cez rozpočet"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Veľkosť zdroja"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Veľkosť"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Čas začatia"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Strávený čas"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Veľkosť prenosu"}, "core/lib/i18n/i18n.js | columnURL": {"message": "Webová adresa"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenciálna úspora"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenciálna úspora"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenciálna úspora: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{<PERSON><PERSON> nájdený 1 prvok}few{Boli nájdené # prvky}many{# elements found}other{<PERSON>lo nájdených # prvkov}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenciálna úspora: {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Prvé <PERSON>é vyfarbenie"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Písmo"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Obrázok"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Od interakcie po ďalšie vykreslenie"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Vysoká"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Nízka"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Stredné"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Max. potenc. oneskorenie prvého vstupu"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Médiá"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Ďalšie zdroje"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Šablóna štýlu"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON>ia strana"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Celkovo"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Pri zaznamenávaní stopy načítania stránky sa vyskytol problém. Znova spustite Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Časový limit čaká na počiatočné pripojenie protokolu ladiaceho nástroja."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome počas načítavania stránky nevytvoril žiadne snímky obrazovky. Skontrolujte, či je na stránke viditeľný obsah, a potom skúste znova spustiť Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Servery DNS nedokázali rozpoznať poskytnutú <PERSON>."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "V súvislosti s požadovaným zberačom {artifactName} sa vyskytla chyba: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Vyskytla sa interná chyba Chromu. Reštartujte Chrome a skúste nástroj Lighthouse spustiť znova."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Požadovaný zberač {artifactName} nebol spustený."}, "core/lib/lh-error.js | noFcp": {"message": "Stránka nevykreslila žiadny obsah. <PERSON><PERSON><PERSON>, aby bolo okno prehliadača počas načítavania v popredí, a skúste to znova. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Na stránke sa nezobrazoval obsah, ktorý sa kvalifikuje ako vykreslenie najväčšieho obsahu. Uistite sa, že stránka obsahuje platný prvok vykreslenia najväčšieho obsahu, a potom to skúste znova. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Poskytnutá stránka nie je vo formáte HTML (zobrazená ako typ média {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "<PERSON><PERSON>to verzia Chromu je na podporu funkcie {featureName} pr<PERSON><PERSON><PERSON>. Ak chcete zobraziť úplné výsledky, pou<PERSON><PERSON> nov<PERSON><PERSON> verz<PERSON>."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Nástroj Lighthouse nedokázal spoľahlivo načítať požadovanú stránku. Skontrolujte, či testujete správnu webovú adresu a či server správne odpovedá na všetky žiadosti."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Nástroj Lighthouse nedokázal spoľahlivo načítať webovú adresu, pretože stránka prestala reagovať."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Poskytnutá webová adresa nemá platný bezpečnostný certifikát. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome zabránil načítaniu stránky s intersticiálnou reklamou. Skontrolujte, či testujete správnu webovú adresu a server správne reaguje na všetky žiadosti."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Nástroj Lighthouse nedokázal spoľahlivo načítať požadovanú stránku. Skontrolujte, či testujete správnu webovú adresu a server správne reaguje na všetky žiadosti. (Podrobnosti: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Nástroj Lighthouse nedokázal spoľahlivo načítať požadovanú stránku. Skontrolujte, či testujete správnu webovú adresu a server správne reaguje na všetky žiadosti. (Stavový kód: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Načítanie stránky trvalo príliš dlho. Riaďte sa príležitosťami v prehľade a skráťte tak načítavanie stránky. Potom skúste Lighthouse znova spustiť. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Čakanie na odpoveď protokolu DevTools presiahlo pridelený čas. (Metóda: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Pri načítaní obsahu zdroja bol prekročený pridelený čas"}, "core/lib/lh-error.js | urlInvalid": {"message": "Poskytnutá webová adresa je zrejme neplatná."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Nástroj Lighthouse nedokázal spoľahlivo načítať požadovanú stránku. Skontrolujte, či testujete správnu webovú adresu a server správne reaguje na všetky žiadosti. (Stavový kód: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Typ média stránky je XHTML: Lighthouse výslovne nepodporuje tento typ dokumentu"}, "core/user-flow.js | defaultFlowName": {"message": "Cesta p<PERSON>žívateľa ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Report navigácie ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Report prehľadu stavu ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Report čas<PERSON><PERSON><PERSON> ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Všetky pre<PERSON>ľady"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kategórie"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Dostupnosť"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>upy"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Výkonnosť"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresívna <PERSON>ová aplikácia"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Vysvetlenie prehľadu cesty používateľov v službe Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Vysvetlenie cesty p<PERSON>ží<PERSON>ľov"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Pomocou prehľadov navigácie môžete..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Pomocou prehľadov stavu môžete…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Pomocou prehľadov časového rozsahu môžete..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Získať skóre výkonnosti v nástroji Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Merať metriky výkonnosti načítania stránok, ako sú vykreslenie najväčšieho obsahu a index rýchlosti."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Získať prístup k možnostiam progresívnych webových aplikácií."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Nájsť problémy s dostupnosťou v jednostránkových aplikáciách alebo zložitých formulároch."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Ohodnotiť osvedčené postupy ponúk a prvkov používateľského rozhrania skrytých za interakciou."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Merať posuny rozloženia a čas spustenia kódu JavaScript v rámci radu interakcií."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Objaviť príležitosti na zvýšenie výkonnosti s cieľom zlepšiť prostredie dlhodobých stránok a jednostránkových aplikácií."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Najvyšší vplyv"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informatívna kontrola}few{{numInformative} informatívne kontroly}many{{numInformative} informative audits}other{{numInformative} informatívnych kontrol}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobilná verzia"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Prehľady navigácie analyzujú jedno načítanie stránky rovnako ako pôvodné prehľady nástroja Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Prehľad navigácie"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} prehľad navigácie}few{{numNavigation} prehľady navigácie}many{{numNavigation} navigation reports}other{{numNavigation} prehľadov navigácie}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} kontrola, ktor<PERSON> môž<PERSON> byť úsp<PERSON>š<PERSON>}few{{numPassableAudits} kontroly, ktor<PERSON> môžu byť úspešné}many{{numPassableAudits} passable audits}other{{numPassableAudits} kontrol, ktor<PERSON> mô<PERSON> byť úspešné}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} úspešná kontrola}few{{numPassed} úsp<PERSON><PERSON><PERSON><PERSON> kontroly}many{{numPassed} audits passed}other{{numPassed} ú<PERSON><PERSON>šn<PERSON><PERSON> kontrol}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Chyba"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "S<PERSON>bé"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Uložiť"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Zachytený stav stránky"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Prehľady stavu analyzujú stránku v konkrétnom stave, zvyčajne po interakciách používateľov."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "<PERSON><PERSON><PERSON><PERSON> stavu"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} prehľad stavu}few{{numSnapshot} prehľady stavu}many{{numSnapshot} snapshot reports}other{{numSnapshot} prehľ<PERSON>v stavu}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interakcie používateľov"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Prehľady č<PERSON>ho rozsahu analyzujú <PERSON>ľné obdobie zvyčajne obsahujúce interakcie používateľov."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Prehľad <PERSON> r<PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} prehľad časového rozsahu}few{{numTimespan} prehľady časového rozsahu}many{{numTimespan} timespan reports}other{{numTimespan} prehľadov časového rozsahu}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Prehľad cesty používateľov v službe Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "V prípade animovaného obsahu minimalizujete využitie procesora pomocou komponenta [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), keď je tento obsah mimo obrazovky."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Skúste zobraziť všetky komponenty [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) vo formátoch WebP a zároveň nastaviť vhodné záložné riešenie pre ďalšie prehliadače. [Ďalšie informácie](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, či p<PERSON> atribút [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) na automatické načítavanie obrázkov až v poslednej chvíli. [Ďalšie informácie](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Na [serverové vykresľovanie rozložení AMP](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) použí<PERSON>jte nástroje, ako je [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Ak chcete zaistiť, aby všetky použité štýly boli podporované, prečítajte si [dokumentáciu pre stránky AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponent [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) podporuje atribút [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) na určenie toho, ktor<PERSON> obrázky použiť na základe veľkosti obrazovky. [Ďalšie informácie](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Ak vykresľujete veľmi dlhé zoz<PERSON>, môž<PERSON> použiť virtuálne posúvanie pomocou súpravy Component Dev Kit (CDK). [Ďalšie informácie](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Veľkosť balíkov súborov JavaScript minimalizujte [rozdelením kódu na úrovni smerovania](https://web.dev/route-level-code-splitting-in-angular/). Môžete tiež predbežne načítať prvky do vyrovnávacej pamäte pomocou [obsluhy Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Ak používate príkazový riadok Angular, zostavy by ste mali generovať v produkčnom režime. [Ďalšie informácie](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Ak používate príkazový riadok <PERSON>, zah<PERSON><PERSON>te zdrojové mapy do ostrej verzie zostavy na účely kontroly balíkov. [Ďalšie informácie](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Prednačítajte smerovanie vopred, aby ste urýchlili navigáciu. [Ďalšie informácie](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Na správu zlomových bodov obrázkov môžete použiť nástroj `BreakpointObserver` zo súpravy Component Dev Kit (CDK). [Ďalšie informácie](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Zvážte nahranie gifu do služby, ktorá umožní jeho vloženie v podobe videa HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Pri definovaní vlastných písiem vo svojom motíve špecifikujte `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Zvážte konfiguráciu [<PERSON><PERSON><PERSON> o<PERSON>r<PERSON>kov WebP pomocou možnosti Konvertovať štýl obr<PERSON>zkov](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) na svojom webe."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Nainš<PERSON><PERSON>jte [modul Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), ktorý môže načítavať obrázky až v poslednej chvíli. Tieto moduly umožňujú odložiť obrázky mimo obrazovky na zlepšenie výkonnosti."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Zvážte použitie modulu na vloženie kľúčovej šablóny CSS a JavaScriptu do textu a na nedôležité súbory CSS alebo JavaScript použite atribút defer."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Čas odpovede servera ovplyvňuj<PERSON> motívy, moduly a špecifikácie servera. Odporúčame nájsť optimalizovanejší motív, opatrne vybrať modul na optimalizáciu alebo inovovať server. Vaše hostiteľské servery by mali využívať ukladanie kódov operácií PHP do vyrovnávacej pamäte, ukladanie do vyrovnávacej pamäte na zníženie časov dopytov databázy (napríklad Redis alebo Memcached), ako aj optimalizovanú aplikačnú logiku na skrátenie prípravy stránok."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Zvážte použitie [št<PERSON><PERSON> responzív<PERSON>ch obrázkov](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), aby ste znížili veľkosť obrázkov načítavaných na stránke. Ak zobrazujete viacero položiek obsahu na stránke pomocou Zobrazení, zvážte implementáciu stránkovania, aby ste obmedzili počet položiek obsahu zobrazovaných na danej stránke."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, či ste povolili „Agregovať súbory CSS“ na stránke Správa » Konfigurácia » Vývoj.  Ak chcete zlepšiť podporu agregácie podkladov, uistite sa, že váš web Drupal používa minimálne Drupal 10.1."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, či ste povolili „Agregovať súbory JavaScriptu“ na stránke Správa » Konfigurácia » Vývoj.  Ak chcete zlepšiť podporu agregácie podkladov, uistite sa, že váš web Drupal používa minimálne Drupal 10.1."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Zvážte odstránenie nepoužívaných pravidiel šablóny CSS a na relevantnú stránku alebo do komponentu na stránke priložte iba potrebné knižnice Drupal. Podrobnosti si zobrazíte pomocou [odkazu na dokumentáciu Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Priložené knižnice pridávajúce externý JavaScript skúste identifikovať spustením funkcie [zahrnutia v kóde](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástroji Chrome DevTools. Príslušný motív alebo modul identifikujete pomocou webovej adresy šablóny so štýlmi, keď je na webe Drupal deaktivovaná agregácia šablón CSS. Nájdite motívy alebo moduly, ktoré majú v zozname mnoho šablón so štýlmi s veľkým počtom červených hodnôt vo funkcii zahrnutia v kóde. Motív alebo modul by mal šablónu so štýlmi zaradiť do poradia iba vtedy, keď sa používa na príslušnej stránke."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Zvážte odstránenie nepoužívaných podkladov JavaSciptu a na relevantnú stránku alebo do komponentu na stránke priložte iba potrebné knižnice Drupal. Podrobnosti si zobrazíte pomocou [odkazu na dokumentáciu Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Priložené knižnice pridávajúce externý JavaScript skúste identifikovať spustením [využitia kódu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástroji Chrome DevTools. Príslušný motív alebo modul identifikujete pomocou webovej adresy skriptu, keď je na webe Drupal deaktivovaná agregácia JavaScriptu. Nájdite motívy alebo moduly, ktoré majú v zozname mnoho skriptov s veľkým počtom červených hodnôt vo využití kódu. Motív alebo modul by mal skript zaradiť do poradia iba vtedy, keď sa používa na príslušnej stránke."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Nastavte „Maximálny vek v prehliadači a vyrovnávacej pamäti proxy servera“ na stránke Správa » Konfigurácia » Vývoj. Prečítajte si o [vyrovnávacej pamäti Drupal a optimalizácii výkonnosti](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Zvážte použitie [modulu](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), ktorý automaticky optimalizuje a zníži veľkosť obrázkov nahraných prostredníctvom daného webu a zároveň zachová kvalitu. T<PERSON><PERSON> skontrolujte, či používate natívne [štýly responzívnych obrázkov](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) poskytnuté z webu Drupal (k dispozícii na webe Drupal verzie 8 a novšej) v prípade všetkých obrázkov vykresľovaných na danom webe."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Indikátory zdrojov predbežného pripojenia alebo predbežného načítania DNS môžete pridať inštaláciou a konfiguráciou [modulu](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), ktorý poskytuje vybavenie pre indikátory zdrojov používateľských agentov."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, či používate natívne [štýly responzívnych obrázkov](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) poskytnuté z webu Drupal (k dispozícii na webe Drupal verzie 8 a novšej). Štýly responzívnych obrázkov používajte pri vykresľovaní polí obrázkov prostredníctvom režimov čítania, zobrazení alebo obrázkov nahraných prostredníctvom editora WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Použím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Optimize Fonts` automaticky zaisťujte pomocou funkcie CSS `font-display` viditeľnosť textu pre používateľov pri načítavaní webfontov."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Next-Gen Formats` konvertujte obrázky na formát WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Lazy Load Images` odložte načítavanie obrázkov mi<PERSON> o<PERSON>, dokým nebudú potrebné."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponentov `Critical CSS` aj `Script Delay` odložte nepodstatný JavaScript a šablóny CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Použitím funkcie [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) uložte svoj obsah do vyrovnávacej pamäte v našej globálnej sieti, čím sa skráti čas do prvého bajtu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Minify CSS` automaticky minifikujte svoje šablóny CSS, aby sa znížili veľkosti sieťových prenosov dát."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Minify Javascript` automaticky minifikujte svoj JavaScript, aby sa znížili veľkosti sieťových prenosov dát."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Remove Unused CSS` pomôžte s týmto problémom. Identifikuje triedy šablón CSS, ktoré sú skutočne použité na každej stránke vášho webu, a odstráni všetky ostatné, aby súbor zostal malý."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Efficient Static Cache Policy` nastavte odporúčané hodnoty statických podkladov v hlavičke vyrovnávacej pamäte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Next-Gen Formats` konvertujte obrázky na formát WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Pre-Connect Origins` automaticky pridávajte návrhy zdrojov `preconnect` a nadväzujte tak skoré pripojenia k dôležitým zdrojom tretích strán."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponentov `Preload Fonts` aj `Preload Background Images` pridajte odkazy `preload` umožňuj<PERSON><PERSON> priorizovať momentálne požadované zdroje načítavania neskôr pri načítaní strán<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Použitím súpravy [Ezoic Leap](https://pubdash.ezoic.com/speed) a povolením komponenta `Resize Images` zmeňte obrázky na veľkosť vhodnú pre zariadenie, aby sa znížili veľkosti sieťových prenosov dát."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> neoptimalizujte automaticky pomocou komponenta `<img>`, ale `gatsby-plugin-image`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> o<PERSON>r<PERSON><PERSON> nespúšťajte automaticky pomocou komponenta `<img>`, ale `gatsby-plugin-image`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "<PERSON>u<PERSON>ite komponent `gatsby-plugin-image` a `loading` nasta<PERSON>te na `eager`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Odložte načítanie nepodstatných skriptov tretej strany pomocou komponenta `Gatsby Script API`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Pomocou doplnku `PurgeCSS` `Gatsby` odstráňte nepoužívané pravidlá zo šablón so štýlmi. [Ďalšie informácie](https://purgecss.com/plugins/gatsby.html)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Nepoužitý kód Java<PERSON>u rozpoznávajte pomocou komponenta `Webpack Bundle Analyzer`. [Ďalšie informácie](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Nakonfigurujte ukladanie nezmeniteľných podkladov do vyrovnávacej pamäte. [Ďalšie informácie](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Kvalitu o<PERSON>r<PERSON><PERSON>kov neupravujte pomocou komponenta `<img>`, ale `gatsby-plugin-image`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Príslušné `sizes` nastavte pomocou komponenta `gatsby-plugin-image`. [Ďalšie informácie](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Zvážte nahranie gifu do služby, ktorá umožní jeho vloženie v podobe videa HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Zvážte použitie [doplnku](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) alebo s<PERSON>, ktorá automaticky konvertuje nahrané obrázky do optimálnych formátov."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Nainštalujte si [doplnok Joomla na odložené načítavanie](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) umožňujúci odloženie načítania všetkých obrázkov mimo obrazovky alebo prejdite na šablónu, ktorá poskytuje túto funkciu. Od verzie Joomla 4.0 budú všetky nové obrázky [automaticky](https://github.com/joomla/joomla-cms/pull/30748) získavať z jadra atribút `loading`."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Existuje niekoľ<PERSON> doplnkov <PERSON>, ktor<PERSON> vám pomôžu [vloži<PERSON> do textu kľúčové podklady](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) alebo [odloži<PERSON> menej dôležité zdroje](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Dá<PERSON>jte pozor, pretože optimalizácie poskytované týmito doplnkami môžu porušiť funkcie vašich šablón či doplnkov, takže ich budete musieť dôkladne otestovať."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Čas odpovede servera ovplyv<PERSON><PERSON><PERSON><PERSON>, rozšírenia a špecifikácie servera. Zvážte vyhľadanie optimalizovanej<PERSON><PERSON>, opatrný výber rozšírenia na optimalizáciu alebo inováciu servera."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Zvážte zobrazovanie úryvkov v kategóriách článkov (napríklad prostredníctvom odkazu Ďalšie informácie), zníženie počtu príspevkov zobrazovaných na danej stránke alebo rozdelenie dlhých príspevkov na niekoľko stránok, prípadne použitie doplnku na lenivé načítavanie komentárov."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) môže váš web zrýchliť zreťazením, minifikáciou a kompresiou štýlov. K dispozícii sú aj šablóny poskytujúce tieto funkcie."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) môže váš web zrýchliť zreťazením, minifikáciou a kompresiou skriptov. K dispozícii sú aj šablóny poskytujúce tieto funkcie."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Zvážte zníženie alebo prepnutie počtu [roz<PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) načítavajúcich nepoužívané šablóny CSS na stránke. Rozšírenia pridávajúce externú šablónu CSS skúste identifikovať spustením funkcie [zahrnutia v kóde](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástroji Chrome DevTools. Príslušný motív alebo doplnok môžete identifikovať pomocou webovej adresy danej šablóny štýlov. Nájdite doplnky, ktoré majú v zozname mnoho šablón štýlov s veľkým počtom červených hodnôt vo funkcii zahrnutia v kóde. Doplnok by nemal šablónu štýlov zaradiť do poradia, keď sa používa na stránke."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Zvážte zníženie alebo prepnutie počtu [r<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) načítavajúcich nepoužívaný JavaScript na stránke. Doplnky pridávajúce externý JavaScript skúste identifikovať pomocou funkcie [zahrnutia v kóde](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástroji Chrome DevTools. Príslušné rozšírenie môžete identifikovať pomocou webovej adresy daného skriptu. Nájdite rozšírenia, ktoré majú v zozname mnoho skriptov s veľkým počtom červených hodnôt vo funkcii zahrnutia v kóde. Rozšírenie by malo skript zaradiť do poradia iba vtedy, keď sa používa na stránke."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Prečítajte si o [ukladaní obsahu do vyrovnávacej pamäte prehliadača na webe Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Zvážte použitie [doplnku na optimalizáciu obr<PERSON><PERSON>kov](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), ktorý komprimuje o<PERSON>, ale zachová ich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Zvážte použitie [doplnku responzívnych obrázkov](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), aby ste mohli v obsahu zahŕňať responzívne obrázky."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Kompresiu textu môžete povoliť aktivovaním kompresie stránok Gzip v systéme Joomla (Systém > Globálna konfigurácia > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ak prvky JavaScriptu nezoskupujete, zv<PERSON>žte použitie [funkcie baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Vypnite v platforme Magento vstavané funkcie [zoskupovania a minifikácie súborov JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) a zvážte namiesto toho použitie funkcie [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Pri [defino<PERSON><PERSON> v<PERSON> p<PERSON>](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) nastavte parameter `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Na trhovisku [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) nájdete veľké množstvo rozšírení tret<PERSON> str<PERSON>, ktoré podporujú nové form<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Ak chcete využiť funkciu [oneskoreného načítania](https://web.dev/native-lazy-loading) danej webovej platformy, upravte šablóny svojho produktu a katalógu."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON>ž<PERSON> [integráciu Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) platformy Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Povoľte možnosť Minifikovať súbory CSS v nastaveniach vášho obchodu pre vývojárov. [Ďalšie informácie](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Použite nástroj [Terser](https://www.npmjs.com/package/terser) na minifikáciu všetkých podkladov JavaScriptu z nasadenia statického obsahu a vypnite vstavanú funkciu minifikácie."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Vypnite vs<PERSON><PERSON><PERSON> [zoskupovanie súborov JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) v platforme Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Na trhovisku [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) nájdete veľké množstvo rozšírení tretích strán určených na optimalizáciu obrázkov."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Indikátory zdrojov predbežného pripojenia alebo predbežného načítania DNS môžete pridať [úpravou rozloženia motívu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON><PERSON> `<link rel=preload>` je možn<PERSON> pridať [úpravou rozloženia motívu](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> neoptimalizujte automaticky pomocou komponenta `<img>`, ale `next/image`. [Ďalšie informácie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> o<PERSON>r<PERSON><PERSON> nespúšťajte automaticky pomocou komponenta `<img>`, ale `next/image`. [Ďalšie informácie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Nastavte použitím komponenta `next/image` prioritu na pravdu, aby sa prednačítaval obrázok vykreslenia najväčšieho obsahu. [Ďalšie informácie](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Odložte načítanie nepodstatných skriptov tretej strany pomocou komponentu `next/script`. [Ďalšie informácie](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Pomocou komponenta `next/image` z<PERSON><PERSON>, aby o<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> mali náležitú veľkosť. [Ďalšie informácie](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Zvážte nastavenie prvku `PurgeCSS` v konfigurácii `Next.js` na odstránenie nepoužitých pravidiel zo šablón so štýlmi. [Ďalšie informácie](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Nepoužitý kód <PERSON>u rozpoznávajte pomocou komponenta `Webpack Bundle Analyzer`. [Ďalšie informácie](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Zvážte meranie skutočnej výkonnosti svojej aplikácie pomocou nástroja `Next.js Analytics`. [Ďalšie informácie](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Nakonfigurujte ukladanie nemenných podkladov a stránok `Server-side Rendered` do vyrovnávacej pamäte. [Ďalšie informácie](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Kvalitu obr<PERSON><PERSON>kov neupravujte pomocou komponenta `<img>`, ale `next/image`. [Ďalšie informácie](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Príslušné `sizes` nastavte pomocou komponentu `next/image`. [Ďalšie informácie](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Povoľte kompresiu na serveri Next.js. [Ďalšie informácie](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Kontaktujte sprá<PERSON>, aby povo<PERSON><PERSON> [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). <PERSON>j konfiguráciou sa uprednostní a optimalizuje výkon vykresľovania stránky."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Pomocou možnosti [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) v sekcii NitroPack nastavte požadovanú hodnotu pre pravidlo zobrazenia písma CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Pomocou funkcie [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) môžete automaticky konvertovať obrázky do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Odložte obrázky mimo obrazovky povolením možnosti [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Povoľte v službe NitroPack funkciu [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack), aby bolo úvodné načítanie rýchlejšie."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Aktivuj<PERSON> [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361), <PERSON><PERSON><PERSON> skr<PERSON><PERSON> čas odpovede servera a optimalizujete vnímanú výkonnosť."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Ak chcete skrátiť čas načítania, v nastaveniach vyrovnávacej pamäte povoľte funkciu [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources), aby ste znížili veľkosť súborov CSS, HTML a JavaScript."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Ak chcete skrátiť čas načítania, v nastaveniach vyrovnávacej pamäte povoľte funkciu [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources), aby ste znížili veľkosť súborov JS, HTML a CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Ak chcete odstrániť pravidlá CSS, ktoré sa nevzťahujú na túto stránku, povoľte funkciu [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Nakonfigurujte [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) v sekcii NitroPack, aby ste mohli oneskoriť načítavanie skriptov, pokým nie sú potrebné."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Prejdite na funkciu [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) v ponuke `Caching` a upravte čas vypršania vyrovnávacej pamäte stránky, aby ste zlepšili čas načítania a dojem používateľov."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Obrázky môžete automaticky komprimovať, optimalizovať a konvertovať na formát WebP zapnutím nastavenia [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Ak chcete preventívne optimalizovať svoje obrázky a prispôsobiť ich veľkosti kontajnerov, v ktorých sa zobrazujú vo všetkých zariadeniach, povoľte nastavenie [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Pomocou funkcie [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) v sekcii NitroPack zmenšite veľkosť súborov odosielaných do prehliadača."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Použite komponent `nuxt/image` a nastavte `format=\"webp\"`. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Použite komponent `nuxt/image` a nastavte `loading=\"lazy\"` pre obrázky mimo obrazovky. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Pou<PERSON>ite komponent `nuxt/image` a špecifikujte `preload` pre obrázok vykreslenia najväčšieho obsahu. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Použite komponent `nuxt/image` a špecifikujte explicitnú hodnotu parametrov `width` a `height`. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#width-height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Použite komponent `nuxt/image` a nastavte náležitú hodnotu parametra `quality`. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Použite komponent `nuxt/image` a nastavte náležitú hodnotu parametra `sizes`. [Ďalšie informácie](https://image.nuxt.com/usage/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Nahraďte animované obrázky GIF videom](https://web.dev/replace-gifs-with-videos/), aby ste zrýchlili načítavanie stránok. Zvážte použitie moderných form<PERSON> súborov, napríklad [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) alebo [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), ktoré v porovnaní s aktuálnym špičkovým videokodekom VP9 zvyšujú účinnosť kompresie o viac ako 30 %."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Zvážte použitie [doplnku](https://octobercms.com/plugins?search=image) alebo služby, ktorá automaticky konvertuje nahrané obrázky do optimálnych formátov. Veľkosť [bezstratových obrázkov WebP](https://developers.google.com/speed/webp) je o 26 % menšia než veľkosť obrázkov PNG a o 25 až 34 % menšia ako veľkosť porovnateľných obrázkov JPEG pri ekvivalentnom indexe kvality SSIM. Prípadne zvážte formát obrázkov ďalšej generácie [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Zvážte inštaláciu [doplnku na odložené načítavanie obrázkov](https://octobercms.com/plugins?search=lazy), ktorý umožňuje odložiť načítanie všetkých obrá<PERSON>kov mi<PERSON> o<PERSON>, alebo prejdite na motív, ktorý poskytuje túto funkciu. Tiež zvážte použitie [doplnku AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Existuje m<PERSON><PERSON>, k<PERSON><PERSON> fungovanie [vložených kľúčových podkladov](https://octobercms.com/plugins?search=css). Tieto doplnky môžu narušiť fungovanie iných, tak<PERSON><PERSON> to dôsledne otestujte."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Čas odpovede servera ovplyvňu<PERSON><PERSON> motívy, doplnky a jeho špecifikácie. Odporúčame nájsť optimalizovanejší motív, opatrne vybrať doplnok na optimalizáciu alebo server aktualizovať. October CMS tiež vývojárom umožňuje odložiť spracovanie časovo náročnej úlohy, napríklad odoslanie e‑mailu, pomocou funkcie [`Queues`](https://octobercms.com/docs/services/queues). To výrazne zrýchli webové požiadavky."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Zvážte zobrazovanie úryvkov v zoznamoch príspevkov (napríklad pomocou tlačidla `show more`), zníženie počtu príspevkov zobrazovaných na danej webovej stránke alebo rozdelenie dlhých príspevkov na niekoľko stránok, prípadne použitie doplnku na odložené načítanie komentárov."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Existu<PERSON> mno<PERSON> [doplnkov](https://octobercms.com/plugins?search=css), ktoré môžu zrýchliť web zreťazením, minifikáciou a kompresiou štýlov. Vývoj môžete zrýchliť pomocou procesu kompilácie, ktorý dokáže túto minifikáciu vykonať vopred."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Existuje mno<PERSON> [doplnkov](https://octobercms.com/plugins?search=javascript), ktoré môžu zrýchliť web zreťazením, minifikáciou a kompresiou skriptov. Vývoj môžete zrýchliť pomocou procesu kompilácie, ktorý dokáže túto minifikáciu vykonať vopred."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Zvážte kontrolu [dopln<PERSON>](https://octobercms.com/plugins), ktoré na webe načítavajú nevyužité šablóny CSS. Ak chcete zistiť, ktoré to sú, spustite v Nástrojoch pre vývojárov v prehliadači Chrome [využitie kódu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Z webovej adresy šablóny so štýlmi identifikujte príslušný motív alebo doplnok. Hľadajte doplnky, ktoré majú vo využití kódu mnoho šablón so štýlmi s veľkým množstvom červeného kódu. Doplnok by mal pridať šablónu so štýlmi iba v prípade, ak sa na webovej stránke skutočne použije."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Zvážte kontrolu [dopln<PERSON>](https://octobercms.com/plugins?search=javascript), ktoré do webovej stránky načítavajú nevyužitý JavaScript. Ak chcete zistiť, ktor<PERSON> to sú, spustite v Nástrojoch pre vývojárov v prehliadači Chrome [využitie kódu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Z webovej adresy skriptu identifikujte príslušný motív alebo doplnok. Hľadajte doplnky, ktoré majú vo využit<PERSON> kódu mnoho skriptov s veľkým množstvom červeného kódu. Doplnok by mal pridať skript iba v prípade, že sa na webovej stránke skutočne používa."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Prečítajte si, ako [zabrániť nepotrebným požiadavkám siete pomocou vyrovnávacej pamäte HTTP](https://web.dev/http-cache/#caching-checklist). Existuje mno<PERSON> [doplnkov](https://octobercms.com/plugins?search=Caching), pomocou ktorých je možné zrýchliť ukladanie do vyrovnávacej pamäte."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Zvážte použitie [doplnku na optimalizáciu obr<PERSON><PERSON>kov](https://octobercms.com/plugins?search=image), ktorý komprimu<PERSON> o<PERSON><PERSON>, ale zachová ich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Obrázky nahrávajte priamo do správcu médií, aby ste tak zaistili dostupnosť ich požadovaných veľkostí. Zvážte použitie [filtra zmeny veľkosti](https://octobercms.com/docs/markup/filter-resize) alebo [doplnku na zmenu veľkosti obrázkov](https://octobercms.com/plugins?search=image), aby boli použité optimálne veľkosti obrázkov."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Povoľte kompresiu textu v konfigurácii webového servera."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Ak na stránke opakovane vykresľujete určité prvky, zv<PERSON>žte použitie „okennej“ kniž<PERSON><PERSON>, ako je `react-window`, k<PERSON><PERSON> vám pomôže minimalizovať počet vytvorených uzlov DOM. [Ďalšie informácie](https://web.dev/virtualize-long-lists-react-window/) Minimalizujte aj zbytočné opakované vykresľovanie. Môžete na to použiť funkciu [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) alebo [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo). [Efekty preskakujte](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) iba v pr<PERSON>pad<PERSON>, ak sa zmenili určité závislosti a zlepšujete výkonnosť počas spustenia pomocou nástroja `Effect`."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Ak používate React Router, minimalizujte používanie komponentu `<Redirect>` na [určenie smerovania](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ak na strane servera vykresľujete komponenty knižnice React, zvážte použitie funkcie `renderToPipeableStream()` alebo `renderToStaticNodeStream()`, aby ste klientovi umožnili prijať a spracovať rôzne časti značiek namiesto všetkých naraz. [Ďalšie informácie](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ak vaša zostava automaticky minifikuje súbory CSS, mali by ste nasadzovať ostrú verziu zostavy aplikácie. Môžete to skontrolovať pomocou rozšírenia React Developer Tools. [Ďalšie informácie](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ak váš systém zostavy automaticky minifikuje súbory JS, mali by ste nasadzovať ostrú verziu zostavy aplikácie. Môžete to skontrolovať pomocou rozšírenia React Developer Tools. [Ďalšie informácie](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Ak nevykresľujete na strane servera, [rozdeľte balíky súborov JavaScript](https://web.dev/code-splitting-suspense/) pomocou funkcie `React.lazy()`. V opačnom prípade rozdeľte kód pomocou knižnice tretej strany, napríklad [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Na meranie výkonnosti vykresľovania svojich komponentov použite profilovač React DevTools Profiler, ktorý využíva rozhranie Profiler API. [Ďalšie informácie](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Vložte videá do atribútu `VideoBoxes`, prisp<PERSON>sobte ich pomocou atribútu `Video Masks` alebo pridajte `Transparent Videos`. [Ďalšie informácie](https://support.wix.com/en/article/wix-video-about-wix-video)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Nahrajte obrázky pomocou formátu `Wix Media Manager`, <PERSON><PERSON><PERSON>, že sa budú automaticky zobrazovať ako WebP. Zistite [ďal<PERSON>ie spôsoby optimalizácie](https://support.wix.com/en/article/site-performance-optimizing-your-media) médií webu."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Pri [prid<PERSON><PERSON><PERSON> kódu tretej strany](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) na karte `Custom Code` hlavného panela vášho webu sa uistite, že je odložený alebo načítavaný na konci textu kódu. Ak je to možné, vložte marketingové nástroje na svoj web pomocou [integrácii](https://support.wix.com/en/article/about-marketing-integrations) služby Wix. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "Wix zobrazuje väčšine návštevníkov odpovede čo najrýchlejšie pomocou sietí na distribúciu obsahu a ukladaním do vyrovnávacej pamäte. Zvážte [manuálne povolenie ukladania do vyrovnávacej pamäte](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) v súvislosti so svojím webom, obzvlášť vtedy, keď používate `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Skontrolujte každý kód tretej strany, ktorý ste pridali na svoj web na karte `Custom Code` jeho hlavného panela, a ponechajte si iba slu<PERSON>, ktoré sú preň nevyhnutné. [Ďalšie informácie](https://support.wix.com/en/article/site-performance-removing-unused-javascript)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Zvážte nahranie gifu do služby, ktorá umožní jeho vloženie v podobe videa HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Zvážte automatickú konverziu nahraných súborov JPEG do formátu WebP pomocou doplnku [ Performance Lab](https://wordpress.org/plugins/performance-lab/), keď je podporovaný."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Nainštalujte si [doplnok WordPress na predbežné načítanie](https://wordpress.org/plugins/search/lazy+load/) poskytujúci možnosť odložiť všetky obrázky mimo obrazovky alebo prepnúť na motív, ktorý túto funkciu poskytuje. Tiež zvážte použitie [doplnku AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Existuje niekoľ<PERSON> doplnkov WordPress, ktoré vám pomôžu [vložiť do textu kľúčové podklady](https://wordpress.org/plugins/search/critical+css/) alebo [odložiť menej dôležité zdroje](https://wordpress.org/plugins/search/defer+css+javascript/). <PERSON><PERSON><PERSON> pozor, pretože optimalizácie poskytované týmito doplnkami môžu porušiť funkcie motívu či doplnkov, takže budete musieť zrejme zmeniť kód."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Čas odpovede servera ovplyvň<PERSON>j<PERSON> motívy, doplnky a špecifikácie servera. Odporúčame nájsť optimalizovanejší motív, opatrne vybrať doplnok na optimalizáciu a/alebo aktualizovať server."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Zvážte zobrazovanie úryvkov v zoznamoch príspevkov (napr. prostredníctvom značky Viac), znížte počet príspevkov zobrazovaných na danej stránke alebo rozdeľte dlhé príspevky na niekoľko stránok. Prípadne použite doplnok na predbežné načítavanie komentárov."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [doplnkov WordPress](https://wordpress.org/plugins/search/minify+css/) môže váš web zrýchliť konkatenáciou, minifikáciou a komprimáciou štýlov. Ak je to mo<PERSON><PERSON><PERSON>, tiež odporúčame túto minifikáciu vykonať vopred pomocou procesu zostavy."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [doplnkov WordPress](https://wordpress.org/plugins/search/minify+javascript/) môže váš web zrýchliť konkatenáciou, minifikáciou a komprimáciou skriptov. Ak je to mo<PERSON><PERSON><PERSON>, odporúčame túto minifikáciu vykonať vopred pomocou procesu zostavy."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Zvážte zníženie alebo prepnutie počtu [doplnkov WordPress](https://wordpress.org/plugins/) načítavajúcich nepoužívané šablóny CSS na stránke. Na identifikáciu doplnkov pridávajúcich externé šablóny CSS skúste spustiť funkciu [zahrnutia v kóde](https://developer.chrome.com/docs/devtools/coverage/) v nástroji Chrome DevTools. Príslušný motív alebo doplnok môžete identifikovať pomocou webovej adresy danej šablóny štýlov. Nájdite doplnky, ktoré majú v zozname mnoho šablón štýlov s veľkým počtom červených hodnôt vo funkcii zahrnutia v kóde. Doplnok by mal šablónu štýlov zaradiť do poradia iba vtedy, keď sa používa na stránke."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Zvážte zníženie alebo prepnutie počtu [doplnkov WordPress](https://wordpress.org/plugins/) načítavajúcich nepoužívaný JavaScript na stránke. Doplnky pridávajúce externý JavaScript skúste identifikovať pomocou funkcie [zahrnutia v kóde](https://developer.chrome.com/docs/devtools/coverage/) v nástroji Chrome DevTools. Príslušný motív alebo doplnok môžete identifikovať pomocou webovej adresy daného skriptu. Nájdite doplnky, ktoré majú v zozname mnoho skriptov s veľkým počtom červených hodnôt vo funkcii zahrnutia v kóde. Doplnok by nemal skript zaradiť do poradia, keď sa používa na stránke."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Prečítajte si o [ukladaní obsahu do vyrovnávacej pamäte prehliadača na webe WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Zvážte použitie [doplnku WordPress na optimalizáciu obrázkov](https://wordpress.org/plugins/search/optimize+images/), ktorý skomprimuje obr<PERSON>ky, ale zachová ich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Nahrajte obrázky priamo prostredníctvom [knižnice médií](https://wordpress.org/support/article/media-library-screen/), čím zaistíte dostupnosť ich požadovaných veľkostí. Potom ich vložte z knižnice médií alebo zaistite použitie optimálnych veľkostí obrázkov pomocou ich miniaplikácií (vr<PERSON>tane tých, ktoré sú určené pre responzívne zlomové body). Obrázky s veľkosťou `Full Size` použite iba vtedy, keď sú dané rozmery vhodné na použitie. [Ďalšie informácie](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "V konfigurácii webového servera môžete povoliť kompresiu textu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Ak chcete obrázky konvertovať na formát WebP, povoľte Imagify na karte Image Optimization v doplnku WP Rocket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Ak chcete toto odporúčanie opraviť, povoľte možnosť [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) v doplnku WP Rocket. Táto funkcia oneskorí načítanie obrázkov, dokým návštevník neposunie stránku nadol a nepotrebuje ich vidieť."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Ak chcete vyriešiť toto odporúčanie, povoľte možnosti [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) a [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) v doplnku WP Rocket. Tieto funkcie budú optimalizovať súbory CSS a JavaScript tak, aby neblokovali vykreslenie stránky."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Ak chcete tento problém odstrániť, povoľte možnosť [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) v doplnku WP Rocket. Všetok priestor a komentáre v súboroch CSS vášho webu budú odstránené, aby sa zmenšila ich veľkosť a urýchlilo ich sťahovanie."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Ak chcete tento problém odstrániť, povoľte možnosť [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) v doplnku WP Rocket. Prázdny priestor a komentáre budú odstránené zo súborov JavaScriptu, aby sa zmenšila ich veľkosť a urýchlilo ich sťahovanie."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Ak chcete tento problém odstrániť, povoľte možnosť [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) v doplnku WP Rocket. Zmenšuje veľkosť stránok odstránením všetkých šablón CSS a šablón so štýlmi, ktoré sa nepoužívajú, pričom na každej stránke ponechá iba používané šablóny CSS."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Ak chcete tento problém odstrániť, povoľte možnosť [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) v doplnku WP Rocket. Zlepší načítanie stránky odložením vykonania skriptov až do interakcie používateľa. Ak váš web obsahuje prvky iframe, môžete použiť [LazyLoad pre prvky iframe a videá](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) doplnku WP Rocket a tiež [nahradiť prvok iframe služby YouTube obrázkom ukážky](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Ak chcete obrázky komprimovať, povoľte Imagify na karte Image Optimization v doplnku WP Rocket a spustite hromadnú optimalizáciu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Pomocou možnosti [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) v doplnku WP Rocket pridajte atribút dns-prefetch a zrýchlite pripojenie pomocou externých domén. WP Rocket okrem toho automaticky pridáva atribút preconnect do [domény Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) aj všetky nastavenia CNAME pridané pomocou funkcie [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Ak chcete vyriešiť tento problém s písmami, povoľte možnosť [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) v zozname WP Rocket. Kľúčové písma vášho webu sa budú prioritne prednačítavať."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Zobraziť kalkulačku"}, "report/renderer/report-utils.js | collapseView": {"message": "Zbaliť zobrazenie"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Počiatočná navigácia"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximálna latencia cesty dôležitých žiadostí:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopírovať JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Prepnúť tmavý motív"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Vytlačiť rozbalené"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Uložiť ako Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Uložiť ako HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Uložiť ako JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Zobraziť neobmedzenú stopu"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Otvoriť v zobrazovači"}, "report/renderer/report-utils.js | errorLabel": {"message": "Ch<PERSON>ba!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Nahlásenie chyby: žiadne informácie o kontrole"}, "report/renderer/report-utils.js | expandView": {"message": "Rozbaliť zobrazenie"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "Prvá strana"}, "report/renderer/report-utils.js | footerIssue": {"message": "Nahlásiť problém"}, "report/renderer/report-utils.js | hide": {"message": "Skryť"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Údaje laboratória"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Analýza aktuálnej stránky v emulovanej mobilnej sieti nástrojom [Lighthouse](https://developers.google.com/web/tools/lighthouse/). Hodnoty sú odhady, ktoré sa môžu líšiť."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Ďalšie položky na manuálnu kontrolu"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Nehodí sa"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Otvoriť na novej karte"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Príležitosti"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Odhadovaná úspora"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Absolvované kont<PERSON>y"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "Na základe [aktualizovaných kritérií inštalovateľnosti Chromu](https://developer.chrome.com/blog/update-install-criteria) ukončí Lighthouse v budúcom vydaní podporu kategórie PWA. Ďalšie informácie o budúcom testovaní PWA nájdete v [aktualizovanej dokumentácii k PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/)."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Počiatočné načítanie s<PERSON>ánky"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Prehľad stavu v konkrétnom čase"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Časový rozsah interakcií používateľov"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "<PERSON><PERSON><PERSON><PERSON> obmedzenie toku dát"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emulovaná pracovná plocha"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulované zariadenie Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Bez emulá<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Verzia knižnice Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Neobmedzený výkon procesora alebo pamäte"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rý<PERSON>ti procesora"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Zariadenie"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Používateľský agent (sieť)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Okamžitý odchod"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Tieto údaje sú prevzaté z okamžitého odchodu na rozdiel od údajov poľa zhrnujúcich mnoho relácií."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Pomalé obmedzenie toku dát 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Zobraziť"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Zobraziť kontroly relevantné pre:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Zbaliť útržok"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Rozbaliť útržok"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Zobrazovať zdroje tretích strán"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Poskytnuté prostredím"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Vyskytli sa problémy ovplyvňujúce funkčnosť nástroja Lighthouse:"}, "report/renderer/report-utils.js | unattributable": {"message": "<PERSON>e je možné p<PERSON>"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Hodnot<PERSON> s<PERSON>, ktoré sa môžu líšiť. [Skóre výkonnosti sa vypočítava](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) priamo z týchto metrík."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Zobraziť stopu"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Zobraziť stromovú mapu"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Úspešne absolvované kontroly, ale s upozorneniami"}, "report/renderer/report-utils.js | warningHeader": {"message": "Upozornenia: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Všetky skripty"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Duplikovať moduly"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Počet bajtov zdroja"}, "treemap/app/src/util.js | tableColumnName": {"message": "N<PERSON>zov"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Prepnúť tabuľku"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Nepoužité bajty"}}