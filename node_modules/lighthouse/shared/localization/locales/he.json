{"core/audits/accessibility/accesskeys.js | description": {"message": "מקשי גישה מאפשרים למשתמשים להתמקד במהירות בחלק מסוים בדף. כדי לאפשר ניווט תקין, כל מקש גישה צריך להיות ייחודי. [למידע נוסף על מקשי גישה](https://dequeuniversity.com/rules/axe/4.8/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "יש ערכי `[accesskey]` שאינם ייחודיים"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` הערכים ייחודיים"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "כל `role` ARIA תומך בקבוצת משנה ספציפית של מאפייני `aria-*`. אי התאמה ביניהם מובילה לשלילת התוקף של מאפייני `aria-*`. [כך מתאימים מאפייני ARIA לתפקידים שלהם](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "יש מאפייני `[aria-*]` שלא תואמים לתפקידים שלהם"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "מאפייני ה-`[aria-*]`‎ תואמים לתפקידים שלהם"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "באמצעות מאפייני `role` של ARIA מציינים לאמצעי הטכנולוגיה המסייעת את התפקיד של כל רכיב בדף האינטרנט. אם יש שגיאות איות בערכים של `role`, אם הם לא ערכי `role` קיימים של ARIA או שהתפקידים מופשטים, המשתמשים באמצעי הטכנולוגיה המסייעת לא יוכלו להבין את מטרת הרכיב. [מידע נוסף על תפקידים ב-ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "הערכים שהוקצו לרכיב `role=\"\"` הם לא תפקידים חוקיים של ARIA."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "הערכים שהוקצו לרכיב `role=\"\"` הם תפקידים חוקיים של ARIA."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "כשאין לרכיב תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה הרכיב לא שימושי לאנשים שמסתמכים על קוראי מסך. [איך לשפר את הנגישות של רכיבי פקודות](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)?"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "לרכיבים `button`, `link` וגם `menuitem` אין שמות נגישים."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "לרכיבים `button`, `link` וגם `menuitem` יש שמות נגישים"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "רכיבים של תיבת דו-שיח מסוג ARIA שאין בהם תוויות נגישות עלולים למנוע ממשתמשים בקורא מסך להבין מה מטרת הרכיבים האלה. [כך משפרים את הנגישות ברכיבים של תיבת דו-שיח מסוג ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)"}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "לרכיבים עם `role=\"dialog\"` או `role=\"alertdialog\"` אין תוויות נגישות."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "לרכיבים עם `role=\"dialog\"` או `role=\"alertdialog\"` יש תוויות נגישות."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "טכנולוגיות מסייעות, כמו קוראי מסך, פועלות באופן לא עקבי כשמוגדר `aria-hidden=\"true\"` ב-`<body>` של המסמך. [מה ההשפעה של `aria-hidden` על גוף המסמך](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)?"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "יש `[aria-hidden=\"true\"]` ב-`<body>` של המסמך"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "אין `[aria-hidden=\"true\"]` ב-`<body>` של המסמך"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "רכיבי צאצא הניתנים למיקוד וממוקמים בתוך רכיב `[aria-hidden=\"true\"]` גורמים לכך שהרכיבים האינטראקטיביים האלה לא יהיו זמינים למשתמשים בטכנולוגיות מסייעות, כמו קוראי מסך. [איך `aria-hidden` משפיע על רכיבים שניתנים למיקוד](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)?"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "רכיבי `[aria-hidden=\"true\"]` כוללים רכיבי צאצא שיכולים לקבל פוקוס"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "רכיבי `[aria-hidden=\"true\"]` אינם מכילים רכיבי צאצא שיכולים לקבל פוקוס"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "כשאין לשדה להזנת קלט תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בשדה. [מידע נוסף על תוויות של שדות להזנת קלט](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "לשדות הזנת קלט של ARIA אין שמות נגישים"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "לשדות ARIA להזנת קלט יש שמות נגישים"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "כשלרכיב מסוג מדד אין תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בשדה. [כך נותנים שם לרכיבים מסוג `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "לרכיבי `meter` מסוג ARIA אין שמות נגישים."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "לרכיבי `meter` מסוג ARIA יש שמות נגישים"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "כשאין לרכיב `progressbar` תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש ברכיב. [כאן מוסבר איך להוסיף תוויות לרכיבי `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "לרכיבי `progressbar` מסוג ARIA אין שמות נגישים."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "לרכיבי `progressbar` מסוג ARIA יש שמות נגישים"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "חלק מתפקידי ה-ARIA כוללים מאפיינים נדרשים שמתארים לקוראי המסך את מצב הרכיב. [מידע נוסף על תפקידים ומאפיינים נדרשים](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "יש רכיבים מסוג `[role]` שאין להם את כל מאפייני ה-`[aria-*]` הנדרשים"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "לכל הרכיבים מסוג `[role]` יש את כל מאפייני ה-`[aria-*]` הדרושים"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "תפקידי הורה מסוימים של ARIA צריכים לכלול תפקידי צאצא ספציפיים כדי לבצע את פונקציות הנגישות שלהם. [מידע נוסף על תפקידים ורכיבי צאצא נדרשים](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ברכיבים עם `[role]` של ARIA שדורשים מצאצאים לכלול `[role]` ספציפי, חסרים חלק מהצאצאים הנדרשים האלה, או שכולם חסרים בהם."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "ברכיבים עם `[role]` של ARIA שדורשים מצאצאים לכלול `[role]` ספציפי, קיימים כל הצאצאים הנדרשים."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "חלק מתפ<PERSON>י<PERSON>י הצאצא ב-ARIA חייבים להיכלל בין תפקידי הורה ספציפיים כדי למלא באופן תקין את פונקציות הנגישות שלהם. [מידע נוסף על תפקידים ב-ARIA ועל רכיב הורה נדרש](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "מאפייני `[role]` לא נמצאים בתוך רכיב ההורה הנדרש שלהם"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "רכיבים מסוג `[role]` נמצאים בתוך רכיב ההורה הנדרש שלהם"}, "core/audits/accessibility/aria-roles.js | description": {"message": "תפקידי ARIA חייבים לכלול ערכים חוקיים כדי לבצע את פונקציות הנגישות שלהם. [מידע נוסף על תפקידי ARIA חוקיים](https://dequeuniversity.com/rules/axe/4.8/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "יש ערכי `[role]` לא חוקיים"}, "core/audits/accessibility/aria-roles.js | title": {"message": "ערכי ה-`[role]` חוקיים"}, "core/audits/accessibility/aria-text.js | description": {"message": "הוספת `role=text` מסביב לפיצול טקסט לצמתים באמצעות תגי עיצוב מאפשרת ל-VoiceOver להתייחס לטקסט כביטוי יחיד, אבל לא תתבצע הקראה של צאצאי הרכיב שניתן להתמקד בהם. [מידע נוסף על המאפיין `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "ברכיבים עם המא<PERSON>יין `role=text` יש צאצאים שניתן להתמקד בהם."}, "core/audits/accessibility/aria-text.js | title": {"message": "ברכיבים עם המא<PERSON>יין `role=text` אין צאצאים שניתן להתמקד בהם."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "כשאין לשדה החלפת מצב תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בשדה כזה. [מידע נוסף על שדות החלפת מצב](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "לשדות החלפת מצב (toggle) של ARIA אין שמות נגישים"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "לשדות החלפת מצב (toggle) של ARIA יש שמות נגישים"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "כשלרכיב מסוג הסבר קצר אין תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בשדה. [כך נותנים שם לרכיבים מסוג `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "לרכיבי `tooltip` מסוג ARIA אין שמות נגישים."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "לרכיבי `tooltip` מסוג ARIA יש שמות נגישים"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "כשאין לרכיב `treeitem` תווית נגישות, קוראי מסך מציינים שם גנרי, ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש ברכיב. [מידע נוסף על יצירת תוויות לרכיבי `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "לרכיבי `treeitem` מסוג ARIA אין שמות נגישים."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "לרכיבי `treeitem` מסוג ARIA יש שמות נגישים"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "טכנולוגיות מסייעות, כמ<PERSON> קוראי מסך, לא יכולות לפרש מאפייני ARIA שהערכים שלהם לא חוקיים. [מידע נוסף על ערכים חוקיים במאפייני ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "למאפייני ‎`[aria-*]`‎ אין ערכים חוקיים"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "למאפייני ה-`[aria-*]` יש ערכים חוקיים"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "טכנולוגיות מסייעות, כמ<PERSON> קוראי מסך, לא יכולות לפרש מאפייני ARIA עם שמות לא חוקיים. [מידע נוסף על מאפייני ARIA חוקיים](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "יש מאפייני `[aria-*]` שאינם חוקיים או שכוללים שגיאות איות"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "מאפייני ה-`[aria-*]` חוקיים ולא כוללים שגיאות איות"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "רכיבים שנכשלו בבדיקה"}, "core/audits/accessibility/button-name.js | description": {"message": "כשאין ללחצן תווית נגישות, קוראי מסך אומרים את המילה 'לחצן', ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בלחצן. [איך לשפר את רמת הנגישות של הלחצנים](https://dequeuniversity.com/rules/axe/4.8/button-name)?"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "ללחצנים אין שמות ייחודיים"}, "core/audits/accessibility/button-name.js | title": {"message": "ללחצנים יש שם נגיש"}, "core/audits/accessibility/bypass.js | description": {"message": "כשמוסיפים דרכים לעקיפת תוכן שחוזר על עצמו, אנשים שמשתמשים במקלדת יכולים לנווט בדף בצורה יעילה יותר. [מידע נוסף על שיטות לעקיפת בלוקים של תוכן](https://dequeuniversity.com/rules/axe/4.8/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "הדף לא כולל כותרת, קישור לדילוג או קטע שמסומן כ-landmark"}, "core/audits/accessibility/bypass.js | title": {"message": "הדף כולל כותרת, קישור לדילוג או קטע שמסומן כ-landmark"}, "core/audits/accessibility/color-contrast.js | description": {"message": "למשתמשים רבים קשה לקרוא טקסט עם ניגודיות נמוכה, או שהם לא יכולים לקרוא אותו. [איך להגדיר ניגודיות צבעים מספקת](https://dequeuniversity.com/rules/axe/4.8/color-contrast)?"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "יחס הניגודיות של צבעי הרקע והחזית אינו מספיק."}, "core/audits/accessibility/color-contrast.js | title": {"message": "יש יחס ניגודיות מספיק בין צבעי הרקע והחזית"}, "core/audits/accessibility/definition-list.js | description": {"message": "כשרשימות של הגדרות לא מסומנות כראוי, קוראי מסך עשויים לספק פלט מבלבל או לא מדויק. [כאן מוסבר איך ליצור מבנה תקין ברשימות של הגדרות](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "יש רכיבי `<dl>` שלא מכילים רק קבוצות `<dt>` ו-`<dd>` עם סדר תקין, `<script>`, `<template>` או רכיבי `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "רכיבי `<dl>` מכילים רק קבוצות `<dt>` ו-`<dd>` עם סדר תקין, `<script>`, `<template>` או רכיבי `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "פריטים ברשימות של הגדרות (`<dt>` ו-`<dd>`) צריכים להיות תחומים בתוך רכיב הורה מסוג `<dl>` כדי שקוראי מסך יוכלו להקריא אותם בצורה נכונה. [כאן מוסבר איך ליצור מבנה תקין ברשימות של הגדרות](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "יש פריטים ברשימות של הגדרות שלא תחומים בין רכיבי `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "פריטים ברשימות של הגדרות מוצבים בין רכיבי `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "הכותרת מספקת למשתמשים של קוראי מסך סקירה כללית של הדף. בנוסף, משתמשים של מנועי חיפוש מסתמכים במידה רבה על הכותרת כדי להבין אם הדף רלוונטי לחיפוש שלהם. [מידע נוסף על כותרות של מסמכים](https://dequeuniversity.com/rules/axe/4.8/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "למסמך אין רכיב `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "המסמך מכיל רכיב `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "כל הרכיבים הניתנים למיקוד חייבים לכלול `id` ייחודי כדי לוודא שטכנולוגיות מסייעות יוכלו לזהות אותם. [כך פותרים בעיות בכפילויות של `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "נעשה שימוש במאפייני `[id]` לא ייחודיים ברכיבים פעילים שיכולים לקבל פוקוס"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "נעשה שימוש במאפייני `[id]` ייחודיים ברכיבים פעילים שיכולים לקבל פוקוס"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "הערך של מזהה ARIA חייב להיות ייחודי כדי למנוע מצבים שבהם טכנולוגיות מסייעות מתעלמות ממופעים אחרים. [כאן מוסבר איך לתקן מצבים של מזהי ARIA כפולים](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "מזהי ה-ARIA אינם ייחודיים"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "מזהי ה-ARIA ייחודיים"}, "core/audits/accessibility/empty-heading.js | description": {"message": "כותרת ללא תוכן או טקסט לא נגיש מונעים ממשתמשים בקורא מסך לגשת למידע שבמבנה הדף. [מידע נוסף על כותרות](https://dequeuniversity.com/rules/axe/4.8/empty-heading)"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "אין תו<PERSON>ן ברכיבי הכותרת."}, "core/audits/accessibility/empty-heading.js | title": {"message": "יש תוכן בכל רכיבי הכותרת."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "טכנולוגיות מסייעות עלולות להקריא באופן מבלבל שדות טופס עם תוויות מרובות. קוראי מסך, למשל, מקריאים את התווית הראשונה, האחרונה או את כולן. [כאן מוסבר איך משתמשים בתוויות של טפסים](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "לשדות טופס יש תוויות מרובות"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "אין שדות טופס עם תוויות מרובות"}, "core/audits/accessibility/frame-title.js | description": {"message": "משתמשים הנעזרים בקוראי מסך מסתמכים על כותרות של מסגרות כדי להבין מה תוכן המסגרות. [מידע נוסף על כותרות של מסגרות](https://dequeuniversity.com/rules/axe/4.8/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "לרכיבי `<frame>` או `<iframe>` אין מאפיין title"}, "core/audits/accessibility/frame-title.js | title": {"message": "לרכיבי `<frame>` או `<iframe>` יש מאפיין title"}, "core/audits/accessibility/heading-order.js | description": {"message": "כשהכותרות מופיעות בסדר הנכון ואינן מדלגות על רמות, הן מבהירות את המבנה הסמנטי של הדף. כך קל יותר לנווט ולהבין את הדף כשמשתמשים בטכנולוגיות מסייעות. [מידע נוסף על סדר של כותרות](https://dequeuniversity.com/rules/axe/4.8/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "רכיבי כותרת אינם מופיעים ברצף יורד"}, "core/audits/accessibility/heading-order.js | title": {"message": "רכיבי כותרת מופיעים ברצף יורד"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "אם בדף לא מצוין מאפיין `lang`, קורא מסך יפעל כאילו שהדף כתוב בשפת ברירת המחדל שהמשתמש בחר במהלך ההגדרה של קורא המסך. אם שפת הדף שונה משפת ברירת המחדל, יכול להיות שקורא המסך לא יקרא בצורה נכונה את הטקסט שבדף. [מידע נוסף על המאפיין `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "לרכיב `<html>` אין רכיב `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "לרכיב `<html>` יש מאפיין `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "מומלץ לציין [שפת BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) חוקית, כי בעזרתה קוראי מסך יכולים להקריא טקסט בצורה נכונה. [כאן מוסבר איך להשתמש במאפיין `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "לרכיב `<html>` אין ערך חוקי עבור המאפיין `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "לרכיב `<html>` יש ערך חוקי עבור המאפיין `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "אם בדף האינטרנט לא מצוינת שפה עקבית, יכול להיות שקורא המסך לא יקריא את הטקסט שבדף בצורה נכונה. [מידע נוסף על המאפיין `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "לרכיב `<html>` אין מאפיין `[xml:lang]` ששפת הבסיס שלו זהה לשפה במאפיין `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "לרכיב `<html>` יש מאפיין `[xml:lang]` ששפת הבסיס שלו זהה לשפה במאפיין `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "לקישורים עם אותו יעד צריך להיות תיאור זהה, כדי לעזור למשתמשים להבין מה מטרת הקישור ולהחליט אם לנווט אליו. [מידע נוסף על קישורים זהים](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)"}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "לקישורים זהים אין מטרה זהה."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "לקישורים זהים יש מטרה זהה."}, "core/audits/accessibility/image-alt.js | description": {"message": "רכיבים אינפורמטיביים צריכים לכלול טקסט חלופי קצר ותיאורי. אפשר להתעלם מרכיבי עיצוב עם מאפיין alt ריק. [מידע נוסף על המאפיין `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "יש רכיבי תמונה ללא מאפייני `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "לרכיבי תמונה יש מאפייני `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "רכיבים אינפורמטיביים צריכים לכלול טקסט חלופי קצר ותיאורי. טקסט חלופי שזהה לטקסט שקרוב לקישור או לתמונה עלול לבלבל את המשתמשים בקורא מסך, כי הטקסט יוקרא פעמיים. [מידע נוסף על המאפיין `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "לרכיבי התמונה יש מאפייני `[alt]` שהם טקסט מיותר."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "לרכיבי התמונה אין מאפייני `[alt]` שהם טקסט מיותר."}, "core/audits/accessibility/input-button-name.js | description": {"message": "הוספה של טקסט נגיש וברור ללחצני קלט עשויה לעזור למשתמשים בקורא מסך להבין את המטרה של לחצן הקלט. [מידע נוסף על לחצני קלט](https://dequeuniversity.com/rules/axe/4.8/input-button-name)"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "אין טקסט ברור בלח<PERSON>ני הקלט"}, "core/audits/accessibility/input-button-name.js | title": {"message": "יש טקסט ברור בלחצני הקלט"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "כשתמונה משמשת כלחצן `<input>`, הוספה של טקסט חלופי יכולה לעזור למשתמשים הנעזרים בקוראי מסך להבין מה מטרת הלחצן. [מידע על טקסט חלופי של תמונה בקלט](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "יש רכיבי `<input type=\"image\">` שאין להם טקסט `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "לרכיבים מסוג `<input type=\"image\">` יש טקסט `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "תוויות טקסט גלויות שלא תואמות לתווית הנגישות עלולות לגרום לחוויה מבלבלת בקרב משתמשים בקורא מסך. [מידע נוסף על תוויות נגישות](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "לרכיבים עם תוויות טקסט גלויות אין תוויות נגישות תואמות."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "לרכיבים עם תוויות טקסט גלויות יש תוויות נגישות תואמות."}, "core/audits/accessibility/label.js | description": {"message": "בעזרת תוויות אפשר לוודא ששמות של פקדי טפסים מוקראים באופן תקין על-ידי טכנולוגיות מסייעות, כמו קוראי מסך. [מידע נוסף על תוויות של רכיבי טפסים](https://dequeuniversity.com/rules/axe/4.8/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "לא שויכו תוויות אל רכיבי טופס"}, "core/audits/accessibility/label.js | title": {"message": "לרכיבי טופס יש תוויות המשויכות אליהם"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "מאפיין עיקרי אחד של ARIA עוזר למשתמשים בקורא מסך לנווט בדף אינטרנט. [מידע נוסף על מאפיינים של ARIA](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "אין במסמך מאפיין עיקרי של ARIA."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "המסמך הוא מאפיין עיקרי של ARIA."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "למשתמשים רבים קשה או בלתי אפשרי לקרוא טקסט עם ניגודיות נמוכה. כשקל להבדיל בין טקסט רגיל לבין טקסט של קישור, זה משפר את החוויה של משתמשים עם ליקויי ראייה. [כך מגדירים קישורים שניתן להבדיל ביניהם](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "לקישורים יש צבעים שונים כדי שניתן יהיה להבדיל ביניהם."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "ניתן להבדיל בין קישורים בלי להסתמך על צבע."}, "core/audits/accessibility/link-name.js | description": {"message": "כשהטקסט של הקישור מובן וייחודי וניתן למיקוד, משתמשים שנעזרים בקורא מסך נהנים מחוויית ניווט משופרת. המצב הזה נכון גם לגבי טקסט חלופי של תמונות כשנעשה בהן שימוש כקישורים. [כך מגדירים קישורים נגישים](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "לקישורים אין שמות ייחודיים"}, "core/audits/accessibility/link-name.js | title": {"message": "לקישורים יש שמות ייחודיים"}, "core/audits/accessibility/list.js | description": {"message": "קוראי מסך מקריאים רשימות בצורה ספציפית. כשמבנה הרשימה תקין, מתאפשרת הקראה תקינה על-ידי קורא המסך. [מידע נוסף על מבנה רשימות תקין](https://dequeuniversity.com/rules/axe/4.8/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "הרשימות לא מכילות רק רכיבי `<li>` ורכיבים שתומכים בסקריפט (`<script>` ו- `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "הרשימות מכילות רק רכיבי `<li>` ורכיבים שתומכים בסקריפט (`<script>` ו-`<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "כדי שקוראי מסך יוכלו להקריא כראוי פריטים ברשימות (`<li>`), הם צריכים להופיע בין רכיבי הורה מסוג `<ul>`,‏ `<ol>` או `<menu>`. [מידע נוסף על מבנה רשימות תקין](https://dequeuniversity.com/rules/axe/4.8/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "יש פריטים ברשימות (`<li>`) שלא נמצאים בין רכיבי הורה מהסוגים `<ul>`, `<ol>` או `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "פריטים ברשימות (`<li>`) נמצאים בין רכיבי הורה מהסוגים `<ul>`, `<ol>` או `<menu>`."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "משתמשים לא מצפים לרענון אוטומטי של הדף, ושימוש ברענון כזה יחזיר את המיקוד אל ראש הדף. מצב כזה יכול ליצור חוויה מתסכלת או מבלבלת. [מידע נוסף על מטא התג של רענון](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "המסמך מכיל `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "המסמך לא כולל שימוש ב-`<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "השבתת האפשרות לשנות את מרחק התצוגה יוצרת בעיה בקרב משתמשים עם ליקויי ראייה, שנוהגים להגדיל את המסך כדי לראות היטב את התוכן של דפי אינטרנט. [מידע נוסף על המטא-תג של אזור התצוגה](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "נעשה שימוש ב-`[user-scalable=\"no\"]` בר<PERSON><PERSON><PERSON> `<meta name=\"viewport\">`, או שערך המאפיין `[maximum-scale]` קטן מ-5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "לא נעשה שימוש ב-`[user-scalable=\"no\"]` ברכיב `<meta name=\"viewport\">` וערך המאפיין `[maximum-scale]` לא קטן מ-5."}, "core/audits/accessibility/object-alt.js | description": {"message": "קוראי מסך לא יכולים לתרגם תוכן שאינו טקסט. הוספה של טקסט חלופי לרכיבי `<object>` עוזרת להבהיר את המשמעות כשמשתמשים בקוראי מסך. [מידע נוסף על טקסט חלופי ברכיבי `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "לרכיבי `<object>` אין טקסט חלופי"}, "core/audits/accessibility/object-alt.js | title": {"message": "לרכיבי `<object>` יש טקסט חלופי"}, "core/audits/accessibility/select-name.js | description": {"message": "רכיבי טופס שאין בהם תוויות מועילות יכולים לגרום לתסכול בחוויות של משתמשים בקורא מסך. [מידע נוסף על רכיבים מסוג `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "לרכיבים נבחרים אין רכיבי תווית משויכים."}, "core/audits/accessibility/select-name.js | title": {"message": "לרכיבים נבחרים יש רכיבי תווית משויכים."}, "core/audits/accessibility/skip-link.js | description": {"message": "הוספה של קישור לדילוג למקום מסוים בדף יכולה לעזור למשתמשים לדלג לתוכן הראשי ולחסוך זמן. [מידע נוסף על קישורים לדילוג למקום מסוים בדף](https://dequeuniversity.com/rules/axe/4.8/skip-link)"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "לא ניתן להתמקד בקישורים לדילוג למקום מסוים בדף."}, "core/audits/accessibility/skip-link.js | title": {"message": "ניתן להתמקד בקישורים לדילוג למקום מסוים בדף."}, "core/audits/accessibility/tabindex.js | description": {"message": "ערך גדול מ-0 מציין סדר ניווט מפורש. למרות שאפשרות זו תקינה מבחינה טכנית, במקרים רבים היא מובילה לחוויה מתסכלת בקרב משתמשים שמסתמכים על טכנולוגיות מסייעות. [מידע נוסף על המאפיין `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "לחלק מהרכיבים יש ערך `[tabindex]` גדול מ-0"}, "core/audits/accessibility/tabindex.js | title": {"message": "לאף רכיב אין ערך `[tabindex]` גדול מ-0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "מאפיין הסיכום צריך לתאר את מבנה הטבלה ובמאפיין `<caption>` צריכה להיות הכותרת שמופיעה במסך. תגי עיצוב מדויקים בטבלה עוזרים למשתמשים בקוראי מסך. [מידע נוסף על סיכום וכיתוב](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "הטבלאות מכילות תוכן זהה במאפיין הסיכום וברכיב `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "הטבלאות מכילות תוכן שונה במאפיין הסיכום וברכיב `<caption>`"}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "קוראי מסך כוללים תכונות שעוזרות לנווט בטבלאות. כדאי לוודא שבטבלאות יש שימוש ברכיב הכיתוב בפועל במקום בתאים עם המאפיין `[colspan]`, כדי לשפר את החוויה של המשתמשים בקורא מסך. [מידע נוסף על רכיבי כיתוב](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "אין שימוש במאפיין `<caption>` במקום בתאים עם המאפיין `[colspan]` כדי לציין כיתוב בטבלאות."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "יש שימוש במא<PERSON>יין `<caption>` במקום בתאים עם המאפיין `[colspan]` כדי לציין כיתוב בטבלאות."}, "core/audits/accessibility/target-size.js | description": {"message": "משתמשים שמתקשים בהפעלת פקדים קטנים יכולים להפעיל את המשטח בעזרת משטחי מגע גדולים ועם מספיק מרווח. [מידע נוסף על משטחי מגע](https://dequeuniversity.com/rules/axe/4.8/target-size)"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "משטחי המגע לא גדולים ומרווחים מספיק."}, "core/audits/accessibility/target-size.js | title": {"message": "משטחי המגע גדולים ומרווחים מספיק."}, "core/audits/accessibility/td-has-header.js | description": {"message": "קוראי מסך כוללים תכונות שעוזרות לנווט בטבלאות. בטבלאות גדולות (3 תאים לפחות ברוחב ובגובה), כדאי להקפיד לשייך לרכיבי `<td>` כותרת טבלה כדי לשפר את החוויה של המשתמשים בקורא מסך. [מידע נוסף על כותרות של טבלאות](https://dequeuniversity.com/rules/axe/4.8/td-has-header)"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "לרכיבי `<td>` ב<PERSON><PERSON><PERSON><PERSON><PERSON>ן `<table>` גדול אין כותרות של טבלאות."}, "core/audits/accessibility/td-has-header.js | title": {"message": "לרכיבי `<td>` ב<PERSON><PERSON><PERSON><PERSON><PERSON>ן `<table>` גדול יש כותרת טבלה אחת או יותר."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "קוראי מסך כוללים תכונות שעוזרות לנווט בטבלאות. כשתאי `<td>` שמשתמשים במאפיין `[headers]` מתייחסים רק לתאים אחרים באותה טבלה, משתמשים הנעזרים בקוראי מסך יכולים ליהנות מחוויה טובה יותר. [מידע נוסף על המאפיין `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "תאים ברכיב `<table>` שמשתמשים במא<PERSON>יין `[headers]`, מתייחסים לרכיב `id` שלא נמצא באותה טבלה."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "תאים ברכיב `<table>` שמשתמשים במא<PERSON>יין `[headers]`, מתייחסים לתאים אחרים באותה הטבלה."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "קוראי מסך כוללים תכונות שעוזרות לנווט בטבלאות. מומלץ להקפיד שהכותרות של הטבלאות יתייחסו תמיד לקבוצה מסוימת של תאים כדי לשפר את החוויה של המשתמשים הנעזרים בקורא מסך. [למידע נוסף על כותרות של טבלאות](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "יש רכיבי `<th>` ורכיבים עם `[role=\"columnheader\"/\"rowheader\"]` שאין להם את תאי הנתונים שהם מתארים."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "לרכיבי `<th>` ולרכיבים עם `[role=\"columnheader\"/\"rowheader\"]` יש תאי נתונים שהם מתארים."}, "core/audits/accessibility/valid-lang.js | description": {"message": "מומלץ לציין [שפת BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) חוקית ברכיבים, כי בעזרתה קוראי מסך יכולים להקריא טקסט בצורה נכונה. [כאן מוסבר איך להשתמש במאפיין `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "יש מאפייני `[lang]` שאין להם ערך חוקי"}, "core/audits/accessibility/valid-lang.js | title": {"message": "למאפייני `[lang]` יש ערך חוקי"}, "core/audits/accessibility/video-caption.js | description": {"message": "כשסרטון כולל כתוביות, המידע שבו נגיש יותר למשתמשים חירשים ולקויי שמיעה. [מידע נוסף על כתוביות בסרטונים](https://dequeuniversity.com/rules/axe/4.8/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "יש רכיבי `<video>` שלא מכילים רכיב `<track>` עם `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "רכיבי `<video>` מ<PERSON>י<PERSON>ים רכיב `<track>` עם `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "ערך נוכחי"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "האסימון המוצע"}, "core/audits/autocomplete.js | description": {"message": "בעזרת `autocomplete`, משתמשים יכולים לשלוח טפסים מהר יותר. כדי להקל על משתמשים, מומלץ להפעיל זאת על ידי הגדרת ערך חוקי למאפיין `autocomplete`. [למידע נוסף על `autocomplete` בטפסים](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "רכיבי `<input>` לא מכילים את מאפייני `autocomplete` הנכונים"}, "core/audits/autocomplete.js | manualReview": {"message": "נדרשת בדיקה ידנית"}, "core/audits/autocomplete.js | reviewOrder": {"message": "בדיקת סדר האסימונים"}, "core/audits/autocomplete.js | title": {"message": "רכיבי `<input>` משתמשים באופן תקין במאפיין `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "אסימוני `autocomplete`: הא<PERSON><PERSON><PERSON><PERSON><PERSON> \"{token}\" לא חוקי ב-{snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "בדיקת סדר האסימונים: \"{tokens}\" בתוך {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "ניתן לבצע פעולה"}, "core/audits/bf-cache.js | description": {"message": "הרבה ניווטים מתבצעים באמצעות חזרה לדף קודם ומעבר נוסף קדימה. התכונה 'מטמון לדף הקודם/הבא' (bfcache) יכולה לזרז את הניווטים האלה לחזרה. [מידע נוסף על bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{סיבה אחת לכשל}one{# סיבות לכשל}two{# סיבות לכשל}other{# סיבות לכשל}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "סיבה לכשל"}, "core/audits/bf-cache.js | failureTitle": {"message": "הדף מנע שחזור מתוך המטמון לדף הקודם/הבא"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "סוג הכשל"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "לא ניתן לבצע פעולה"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "בהמ<PERSON><PERSON>ה לתמיכה בדפדפן"}, "core/audits/bf-cache.js | title": {"message": "הדף לא מנע שחזור מתוך המטמון לדף הקודם/הבא"}, "core/audits/bf-cache.js | warningHeadless": {"message": "לא ניתן לבדוק את התכונה 'מטמון לדף הקודם/הבא' בגרסה הישנה של Headless Chrome ‏(`--chrome-flags=\"--headless=old\"`). כדי לראות את תוצאות הבדיקה, יש להשתמש בגרסה החדשה של Headless Chrome ‏(`--chrome-flags=\"--headless=new\"`) או בגרסה הרגילה של Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "תוספים ל-Chrome השפיעו לרעה על ביצועי הטעינה של הדף הזה. כדאי לבדוק את הדף במצב גלישה בסתר או באמצעות פרופיל Chrome שאינו כולל תוספים."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "הערכת סקריפט"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "ניתו<PERSON>קריפט"}, "core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON><PERSON> כולל של CPU (יחידת עיבוד מרכזית)"}, "core/audits/bootup-time.js | description": {"message": "כדאי לשקול את האפשרות לקצר את הזמן הדרוש לניתוח, להידור ולביצוע של JS. לשם כך, אפשר להשתמש במטענים ייעודיים (payloads) קטנים יותר של JS. [כאן מוסבר איך לקצר את זמן הביצוע של תוכניות JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "יש לקצר את זמן הביצוע של JavaScript"}, "core/audits/bootup-time.js | title": {"message": "<PERSON><PERSON><PERSON> ביצוע של JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "אם קיימים מודולים משוכפלים גדולים בחבילות JavaScript, יש להסיר אותם מהחבילות האלה כדי לצמצם צריכה של בייטים מיותרים על ידי פעילות הרשת. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "יש להסיר עותקים כפולים של מודולים בחבילות JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "קובצי GIF גדולים לא פועלים באופן יעיל להצגת אנימציה. כדי לצמצם את מספר הבייטים שמועברים ברשת, במקום קובצי GIF כדאי לשקול את האפשרות להשתמש בסרטוני MPEG4/‏WebM בשביל אנימציות ובקובצי PNG/‏WebP בשביל תמונות סטטיות. [מידע נוסף על פורמטים יעילים של סרטונים](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "יש להשתמש בפורמטים של וידאו כדי להציג תוכן אנימציה"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "רכיבי Polyfill וטרנספורמציות מאפשרים לדפדפנים מדור קודם להשתמש בתכונות JavaScript חדשות. עם זאת, רבים מהם לא נחוצים לדפדפנים מודרניים. כשפורסים סקריפטים בחבילת JavaScript, מומלץ לעשות זאת בשיטה מודרנית שכוללת זיהוי של תכונות עם או בלי מודולים. שיטה כזו מאפשרת לצמצם את כמות הקוד שנשלחת לדפדפנים מתקדמים, מבלי לוותר על התמיכה בדפדפנים מדור קודם. [איך להשתמש ב-JavaScript בשיטה מודרנית](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "יש להימנע משימוש ב-JavaScript מדור קודם בדפדפנים מודרניים"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "לרוב, פורמטים של תמונות כמו WebP ו-AVIF מספקים רמה טובה יותר של דחיסת נתונים מאשר PNG או JPEG. הדחיסה המשופרת מקצרת את זמן ההורדות ומצמצמת את צריכת הנתונים. [מידע נוסף על פורמטים מודרניים של תמונות](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "יש להציג תמונות בפורמטים עדכניים"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "כדי לקצר את הזמן עד לפעילות מלאה, כדאי לשקול לבצע טעינה מדורגת של תמונות מוסתרות ותמונות שלא מופיעות מיד במסך, כך שייטענו רק אחרי סיום הטעינה של כל המשאבים הקריטיים. [איך לעכב טעינה של תמונות שלא מופיעות מיד במסך](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)?"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "יש לעכב טעינה של תמונות שאינן מופיעות במסך"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "משאבים חוסמים את הצגת התמונה הראשונית (FP) של הדף במסך. כדאי לשקול את האפשרות לספק תוכן קריטי של JS/‏CSS באופן מוטבע ולדחות את הטעינה של כל תוכן ה-JS/הסגנונות שאינם קריטיים. [כך אפשר להסיר משאבים שחוסמים עיבוד](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "יש להימנע ממשאבים שחוסמים עיבוד"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "מטענים ייעודיים (payload) בנפח גדול המועברים ברשת עולים למשתמשים כסף ולרוב מאריכים את זמני הטעינה. [כאן מוסבר איך להקטין נפח של מטען ייעודי (payload)](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "הגודל הכולל היה ‎{totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "יש להימנע מהעברה של מטענים ייעודיים ענקיים (payload) ברשת"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "נמנע מהעברה של מטענים ייעודיים ענקיים (payload) ברשת"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "הקטנה של קובצי CSS מאפשרת לצמצם את הגודל של מטענים ייעודיים (payload) שמועברים ברשת. [כאן מוסבר איך להקטין נפח של CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "יש להקטין קובצי CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "הקטנה של קובצי JavaScript יכולה לצמצם את המטען הייעודי (payload) ולקצר את משך הזמן הנדרש לניתוח סקריפט. [איך להקטין קובצי JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)?"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "יש לקצר את קוד JavaScript למינימום ההכרחי"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "ניתן לצמצם את צריכת הבייטים כתוצאה מפעילות הרשת על ידי הפחתת כללים שאינם בשימוש מגיליונות הסגנונות ודחיית הטעינה של רכיבי CSS שלא משמשים את התוכן בחלק העליון והקבוע. [איך להפחית כמות של רכיבי CSS שאינם בשימוש](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)?"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "הפחתת CSS שאינו בשימוש"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "ניתן לצמצם את צריכת הבייטים כתוצאה מפעילות הרשת על ידי הפחתת נפח של JavaScript שלא נמצא בשימוש ועיכוב טעינה של סקריפטים עד שהם יידרשו. [כאן מוסבר איך להפחית נפח של JavaScript שאינו בשימוש](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "הפחתת תוכן JavaScript שאינו בשימוש"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "עם מטמון בעל משך חיים ארוך, טעינת הדף שלך במסגרת ביקורים חוזרים יכולה להיות מהירה יותר. [מידע נוסף על המדיניות בנושא מטמון יעיל](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{נמצא משאב אחד}one{נמצאו # משאבים}two{נמצאו # משאבים}other{נמצאו # משאבים}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "יש להציג נכסים סטטיים בעזרת מדיניות מטמון יעילה"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "יש להשתמש במדיניות מטמון יעילה בנכסים סטטיים"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "תמונות שעברו אופטימיזציה נטענות מהר יותר וצורכות פחות נתונים בחבילת הגלישה. [כך מקודדים תמונות ביעילות](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "יש לקודד תמונות בצורה יעילה"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "הממדים בפועל"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "הממדים המוצגים"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "התמונות גדולות יותר מגודל התצוגה שלהן"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "התמונות מתאימות לגודל התצוגה שלהן"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "כדאי להציג תמונות שהגודל שלהן הוגדר בצורה נכונה כדי לחסוך בניצול חבילת הגלישה ולקצר את זמן הטעינה. [כך מגדירים גודל של תמונות](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "יש להגדיר את גודל התמונות בצורה נכונה"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "הצגת משאבים המבוססים על טקסט צריכה להתבצע בעזרת דחיסת נתונים (gzip‏, deflate או brotli) כדי לצמצם את כמות הבייטים שמועברים ברשת. [מידע נוסף על דחיסת טקסט](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "יש להפעיל דחיסת טקסט"}, "core/audits/content-width.js | description": {"message": "אם הרוחב של תוכן האפליקציה לא תואם לרוחב של אזור התצוגה, ייתכן שלא בוצעה אופטימיזציה לאפליקציה שלך עבור מסכים של ניידים. [כאן מוסבר איך להתאים את גודל התוכן לאזור התצוגה](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "גודל אזור התצוגה של {innerWidth} פיקסלים לא תואם לגודל החלון של {outerWidth} פיקסלים."}, "core/audits/content-width.js | failureTitle": {"message": "הגודל של התוכן הוגדר בצורה לא תקינה עבור אזור התצוגה"}, "core/audits/content-width.js | title": {"message": "הגודל של התוכן הוגדר בצורה תקינה עבור אזור התצוגה"}, "core/audits/critical-request-chains.js | description": {"message": "בקטע 'שרשראות בקשה קריטיות' שבהמשך מוצגים המשאבים שנטענים עם עדיפות גבוהה. כדי לשפר את מהירות טעינת הדף, מומלץ לקצר את השרשראות, להקטין את גודל ההורדה של משאבים או לעכב את ההורדה של משאבים לא נחוצים. [כאן מוסבר איך להימנע משרשור של בקשות קריטיות](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה שרשרת אחת}one{נמצאו # שרשראות}two{נמצאו # שרשראות}other{נמצאו # שרשראות}}"}, "core/audits/critical-request-chains.js | title": {"message": "יש להימנע משרשור של בקשות קריטיות"}, "core/audits/csp-xss.js | columnDirective": {"message": "הוראה"}, "core/audits/csp-xss.js | columnSeverity": {"message": "מידת החומרה"}, "core/audits/csp-xss.js | description": {"message": "שימוש במדיניות חזקה מסוג Content Security Policy (‏CSP) מפחית באופן משמעותי את הסיכון למתקפות שמקורן בפרצת אבטחה XSS ‏(cross-site scripting). [כאן מוסבר איך משתמשים ב-CSP כדי למנוע XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "תחב<PERSON>ר"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "הדף מכיל מדיניות CSP שהוגדרה בתג `<meta>`. כדאי להעביר את ה-CSP לכותרת HTTP או להגדיר מדיניות CSP מחמירה אחרת בכותרת HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "אין CSP במצב ה<PERSON>י<PERSON>ה"}, "core/audits/csp-xss.js | title": {"message": "איך לוודא שמדיניות CSP מגינה ביעילות ממתקפות XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "הוצאה משימוש/אזהרה"}, "core/audits/deprecations.js | columnLine": {"message": "שורה"}, "core/audits/deprecations.js | description": {"message": "ממשקי API שהוצאו משימוש יוסרו בסופו של דבר מהדפדפן. [מידע נוסף על ממשקי API שהוצאו משימוש](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה אזהרה אחת}one{נמצאה אזהרה אחת}two{נמצאו # אזהרות}other{נמצאו # אזהרות}}"}, "core/audits/deprecations.js | failureTitle": {"message": "הדף מכיל רכיבי API שהוצאו משימוש"}, "core/audits/deprecations.js | title": {"message": "אין רכיבי API שהוצאו משימוש"}, "core/audits/dobetterweb/charset.js | description": {"message": "נדרשת הצהרה לגבי קידוד תווים. ניתן להגדיר אותה באמצעות תג `<meta>` שממוקם ב-1,024 הבייטים הראשונים של ה-HTML או ברכיב ה-Content-Type בכותרת התגובה של ה-HTTP. [מידע נוסף על הצהרה לגבי קידוד התווים](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "חסרה הצהרה (declaration) של Charset ב-HTML, או שהיא ממוקמת בקטע מאוחר מדי בתוך ה-HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "ה-<PERSON><PERSON><PERSON> מוגדר כמו שצריך"}, "core/audits/dobetterweb/doctype.js | description": {"message": "אם מציינים DOCTYPE, הד<PERSON><PERSON><PERSON>ן לא עובר למצב תאימות (quirks mode). [מידע נוסף על הצהרת DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "שם DOCTYPE חייב להיות המחרוזת `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "המסמך מכיל `doctype` שמפעיל את המצב '`limited-quirks-mode`'"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "המסמך חייב להכיל doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId אמור להיות מחרוזת ריקה"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId אמור להיות מחרוזת ריקה"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "המסמך מכיל `doctype` שמפעיל את המצב '`quirks-mode`'"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "ה-doctype של הדף אינו מוגדר ל-HTML, ולכן הופעל מצב תאימות (quirks mode)"}, "core/audits/dobetterweb/doctype.js | title": {"message": "ה-doctype של הדף מוגדר ל-HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "נתון סטטיסטי"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "ערך"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "נפח DOM גדול מגביר את מידת השימוש בזיכרון, מאריך את הזמן הדרוש ל[חישובי סגנונות](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ומוביל ל[הזרמה חוזרת של פריסות](https://developers.google.com/speed/articles/reflow) שגוזלת משאבים יקרים. [כאן מוסבר איך להימנע מנפח DOM גדול מדי](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{רכ<PERSON><PERSON> אחד}one{# רכיבים}two{# רכיבים}other{# רכיבים}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "יש להימנע מ-<PERSON><PERSON> גדול מדי"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "עומ<PERSON> מרבי"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "סך רכיבי DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "מקסימום רכיבי צאצא"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "נמנע מ-<PERSON><PERSON> גדול מדי"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "אתרים שמבקשים לקבל גישה למיקום של המשתמשים בלי לציין הקשר, מעוררים אצל המשתמשים תחושת בלבול או חשדנות. במקום זאת, כדאי לקשר את הבקשות לפעולה של המשתמשים. [מידע נוסף על הרשאת המיקום הגיאוגרפי](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "הדף מבקש הרשאות למיקום גאוגרפי במהלך טעינת הדף"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "הדף לא מבקש הרשאות למיקום גאוגרפי במהלך טעינת הדף"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "סוג הבעיה"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "שגיאות שנרשמו בחלונית `Issues` ב-Chrome Devtools מעידות על בעיות לא פתורות. הן עשויות להופיע בעקבות כשל בבקשות ברשת, אמצעי אבטחה לא מספיקים או בעיות אחרות בדפדפן. יש לפתוח את חלונית הבעיות ב-Chrome DevTools כדי לקבל פרטים נוספים על כל בעיה."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "נרשמו בעיות בחלונית `Issues` ב-Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "נחסם עקב מדיניות ממקורות שונים"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "המודעות מנצלות הרבה מקורות"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "אין בעיות בחלונית `Issues` ב-Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "גרסה"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "מופיעות כל ספריות ה-JavaScript של ממשקי הקצה שזוהו בדף. [מידע נוסף על בדיקת האבחון הזו לזיהוי ספריות JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "ספריות JavaScript שזוהו"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "אם החיבור איטי, סקריפטים חיצוניים שמוחדרים באופן דינמי דרך `document.write()` יכולים לעכב טעינה של דף בעשרות שניות. [כך ניתן להימנע מ-document.write()‎](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "יש להימנע מ-`document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "לא נעשה שימוש ב-`document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "אתרים שמבקשים לשלוח התראות ללא הקשר מעוררים תחושת בלבול או חשדנות בקרב המשתמשים. במקום זאת, כדאי לקשר את הבקשות לתנועות של משתמשים. [מידע נוסף על קבלת הרשאה באופן אחראי להצגת התראות](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "הדף מבקש הרשאה להודעות במהלך טעינת הדף"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "הדף לא מבקש הרשאה להתראות במהלך טעינת הדף"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "חסימת האפשרות להדביק קלט פוגעת בחוויית המשתמש וגם חוסמת מנהלי סיסמאות, מה שמחליש את האבטחה.[מידע נוסף על שדות ידידותיים למשתמש להזנת קלט](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "משתמשים לא יכולים להדביק בשדות להזנת קלט"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "משתמשים יכולים להדביק בשדות להזנת קלט"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "פרוטוקול"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "ל-HTTP/2 יש הרבה יתרונות על-פני HTTP/1.1, ביניהם כותרות בינאריות וריבוב. [מידע נוסף על HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{בקשה אחת לא מולאה דרך HTTP/2}one{בקשה אחת לא מולאה דרך HTTP/2}two{# בקשות לא מולאו דרך HTTP/2}other{# בקשות לא מולאו דרך HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "עליך להשתמש ב-HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "כדי לשפר את ביצועי הגלילה של הדף, מומלץ לשקול להוסיף סימון `passive` לפונקציות ה-event listener הקשורות למגע ולגלגלת. [מידע נוסף על הטמעה של פונקציות event listener פסיביות](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "לא נעשה שימוש ברכיבי listener פסיביים לשיפור ביצועי הגלילה"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "נעשה שימוש ברכיבי listener פסיביים לשיפור ביצועי הגלילה"}, "core/audits/errors-in-console.js | description": {"message": "שגיאות שנרשמות במסוף מציינות בעיות לא פתורות. הן עשויות להופיע בעקבות כשל בבקשות ברשת או בעיות אחרות בדפדפן. [מידע נוסף על השגיאות האלה בבדיקת האבחון של המסוף](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "נרשמו שגיאות דפדפן במסוף"}, "core/audits/errors-in-console.js | title": {"message": "לא נרשמו במסוף שגיאות דפדפן"}, "core/audits/font-display.js | description": {"message": "כדאי להשתמש בתכונת ה-CSS של `font-display` כדי שהטקסט יהיה גלוי למשתמשים במהלך הטעינה של פונטים מסוג webfont. [מידע נוסף על `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "יש לוודא שטקסט ממשיך להופיע במהלך טעינת webfont"}, "core/audits/font-display.js | title": {"message": "כל הטקסט ממשיך להופיע במהלך טעינות של webfont"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON><PERSON><PERSON>,plural, =1{מערכת Lighthouse לא הצליחה לבדוק את הערך `font-display` של המקור {fontOrigin} באופן אוטומטי.}one{מערכת Lighthouse לא הצליחה לבדוק את הערך `font-display` של המקור {fontOrigin} באופן אוטומטי.}two{מערכת Lighthouse לא הצליחה לבדוק את ערכי `font-display` של המקור {fontOrigin} באופן אוטומטי.}other{מערכת Lighthouse לא הצליחה לבדוק את ערכי `font-display` של המקור {fontOrigin} באופן אוטומטי.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "יח<PERSON> גובה-רוחב (בפועל)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "יח<PERSON> גובה-רוחב (בתצוג<PERSON>)"}, "core/audits/image-aspect-ratio.js | description": {"message": "מידות התצוגה של התמונה צריכות להתאים ליחס הגובה-רוחב הטבעי. [מידע נוסף על יחסי גובה-רוחב של תמונות](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "יש תמונות עם יחס גובה-רוחב שגוי"}, "core/audits/image-aspect-ratio.js | title": {"message": "התמונות מוצגות ביחס גובה-רוח<PERSON> נכון"}, "core/audits/image-size-responsive.js | columnActual": {"message": "גודל בפועל"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "גודל מוצג"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "הגודל הצפוי"}, "core/audits/image-size-responsive.js | description": {"message": "המידות הטבעיות של התמונה צריכות להיות פרופורציונליות לגודל המסך וליחס הפיקסלים כדי שהתמונה תהיה ברורה ככל האפשר. [איך לספק תמונות רספונסיביות](https://web.dev/articles/serve-responsive-images)?"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "מציג תמונות עם רזולוציה נמוכה"}, "core/audits/image-size-responsive.js | title": {"message": "מציג תמונות עם רזולוציה מתאימה"}, "core/audits/installable-manifest.js | already-installed": {"message": "האפליקציה כבר מותקנת"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "לא ניתן היה להוריד סמל נדרש מהמניפסט"}, "core/audits/installable-manifest.js | columnValue": {"message": "סיבה לכשל"}, "core/audits/installable-manifest.js | description": {"message": "קובץ שירות (service worker) הו<PERSON> הטכנולוגיה שמאפשרת לאפליקציה שלך להשתמש במספר תכונות Progressive Web App, כמו 'מצב אופליין', 'הוספה למסך הבית' ו'התראות'. בעזרת הטמעה נכונה של קובצי מניפסט וקובצי שירות (service worker), הדפדפנים יכולים לבקש ממשתמשים באופן יזום להוסיף את האפליקציה שלך למסך הבית שלהם. הפעולה הזו מגדילה את הסיכוי להשגת מעורבות גבוהה יותר. [מידע נוסף על הדרישות בנוגע להתקנה של מניפסט](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{סיבה אחת}one{סיבה אחת}two{שתי סיבות}other{# סיבות}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "קובץ המניפסט של האפליקציה או קובץ השירות (service worker) אינם עומדים בדרישות יכולת ההתקנה"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "כתובת ה-<PERSON><PERSON> של האפליקציה בחנות Play והמזהה בחנות Play אינם תואמים"}, "core/audits/installable-manifest.js | in-incognito": {"message": "הדף נטען בחלון אנונימי"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "המא<PERSON>יין `display` במני<PERSON>סט חייב להיות אחד מהערכים `standalone`, `fullscreen` או `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "המניפסט מכיל את השדה 'display_override' ומצב התצוגה הראשון הנתמך צריך להיות 'standalone',‏ 'fullscreen' או 'minimal-ui'"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "לא ניתן לאחזר או לנתח את קובץ המניפסט, או שהוא ריק"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "כתובת ה-<PERSON><PERSON> של המניפסט השתנתה במהלך אחזור המניפסט."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "קובץ המניפסט לא מכיל את השדות '`name`' או '`short_name`'"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "קובץ המניפסט אינו מכיל סמל מתאים. דרוש פורמט PNG‏, SVG או WebP ברזולוציה של ‎{value0}px לפחות, ויש להגדיר את מאפיין הגודל. אם הוגדר המאפיין 'שימוש', הוא חייב לציין \"הכול\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "לא סיפקת סמל ריבועי באורך/רוחב של {value0} פיקסלים לפחות בפורמט PNG‏, SVG או WebP, עם מאפיין מטרה לא מוגדר או מוגדר ל\"הכול\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "הסמל שהורד היה ריק או פגום"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "לא סופק מזהה חנות Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "אין לדף כתובת URL של מניפסט <link>"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "לא ניתן לבדוק את קובץ השירות (service worker) ללא השדה 'כתובת url להתחלה'"}, "core/audits/installable-manifest.js | noErrorId": {"message": "המזהה '{errorId}' של השגיאה ביכולת ההתקנה אינו מוכר"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "הדף מוצג ממקור לא מאובטח"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "הדף לא נטען במסגרת הראשית"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "הדף לא פועל ללא חיבור לאינטרנט"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA הוסרה ומתבצע איפוס של בדיקות לגבי אפשרות התקנה."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "פלטפור<PERSON><PERSON> האפליקציה שסופקה לא נתמכת ב-Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "המניפסט מפרט אפליקציות שקשורות להעדפות: TRUE"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "התכונה prefer_related_applications נתמכת רק בגרסת הבטא של Chrome ובערוצים יציבים ב-Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "במסגרת Lighthouse אי אפשר היה לקבוע אם ניתן להתקין את הדף. צריך לנסות שוב בגרסה חדשה יותר של Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "כתובת ה-<PERSON><PERSON> להתחלה של המניפסט לא חוקית"}, "core/audits/installable-manifest.js | title": {"message": "המניפסט של אפליקציית האינטרנט וקובץ השירות (service worker) עומדים בדרישות יכולת ההתקנה"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "המניפסט מכיל כתובת URL הכוללת שם משתמש, סיסמה או יציאה"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "הדף לא פועל ללא חיבור לאינטרנט. הדף לא ייחשב כדף שניתן להתקנה אחרי Chrome 93, בגרסה היציבה של אוגוסט 2021."}, "core/audits/is-on-https.js | allowed": {"message": "הכתובת אושרה"}, "core/audits/is-on-https.js | blocked": {"message": "הכתובת נחסמה"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "כתובת URL של בקשה לא מאובטחת"}, "core/audits/is-on-https.js | columnResolution": {"message": "תגובה לבקשה"}, "core/audits/is-on-https.js | description": {"message": "יש להגן באמצעות פרוטוקול HTTPS על כל האתרים, גם אם הם לא מעבדים מידע אישי רגיש. ההגנה הזו כוללת גם הימנעות מ[תוכן מעורב](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), שבו חלק מהמשאבים נטענים באמצעות HTTP למרות העובדה שהבקשה הראשונית נשלחת באמצעות HTTPS. פרוטוקול HTTPS מונע מפורצים להשתמש לרעה בתקשורת המתקיימת בין האפליקציה למשתמשים או לצותת לה. השימוש בפרוטוקול הזה הוא תנאי מוקדם של HTTP/2 ושל רכיבי ה-API של רבות מפלטפורמות האינטרנט החדשות. [למידע נוסף על HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה בקשה לא מאובטחת אחת}one{נמצאה בקשה לא מאובטחת אחת}two{נמצאו # בקשות לא מאובטחות}other{נמצאו # בקשות לא מאובטחות}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "לא נעשה שימוש ב-HTTPS"}, "core/audits/is-on-https.js | title": {"message": "שימוש ב-HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "הכתובת שודרגה ל-HTTPS באופן אוטומטי"}, "core/audits/is-on-https.js | warning": {"message": "הכתובת אושרה עם אזהרה"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "א<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "שלב"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "תזמון"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "זהו הרכי<PERSON> הגדול ביותר המכיל תוכן שמוצג בתוך אזור התצוגה. [מידע נוסף על הרכיב מסוג Largest Contentful Paint ‏(LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "השהיית טעינה"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "<PERSON><PERSON><PERSON> טעינה"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "השהיית רינדור"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "רכיב ה-Largest Contentful Paint (‏LCP)"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "ההשפעה של שינוי הפריסה"}, "core/audits/layout-shift-elements.js | description": {"message": "רכיבי ה-DOM האלה הושפעו הכי הרבה משינויים בפריסה. יכול להיות ששינויי פריסה מסוימים לא ייכללו בערך של מדד CLS בגלל [עיבוד החלק הנצפה בלבד](https://web.dev/articles/cls#what_is_cls). [איך לשפר את ה-CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "יש להימנע משינויים משמעותיים בפריסה"}, "core/audits/layout-shifts.js | columnScore": {"message": "הציון של שינוי הפריסה"}, "core/audits/layout-shifts.js | description": {"message": "אלה שינויי הפריסה הגדולים ביותר שזוהו בדף. כל פריט בטבלה מייצג שינוי פריסה אחד, ומוצג בו הרכיב שהשתנה במידה הרבה ביותר. מתחת לכל פריט מופיעות הסיבות האפשריות לשינוי הפריסה. יכול להיות שחלק משינויי הפריסה האלה לא ייכללו בערך של מדד CLS בגלל [עיבוד החלק הנצפה בלבד](https://web.dev/articles/cls#what_is_cls). [איך לשפר את ה-CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{נמצא שינוי פריסה אחד}one{נמצאו # שינויי פריסה}two{נמצאו # שינויי פריסה}other{נמצאו # שינויי פריסה}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "טעינת גופן אינטרנט"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "החדרת iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "בקשת רשת מאוחרת גרמה לשינוי בפריסת הדף"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "רכיב מדיה שהגודל שלו לא מוגדר"}, "core/audits/layout-shifts.js | title": {"message": "יש להימנע משינויים משמעותיים בפריסה"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "תמונות בחלק העליון והקבוע שנטענות באופן מדורג יעברו רינדור בשלב מאוחר יותר במחזור החיים של הדף, לכן עשוי להיות עיכוב ב-Largest Contentful Paint ‏(LCP). [מידע נוסף על טעינה מדורגת אופטימלית](https://web.dev/articles/lcp-lazy-loading)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "התמונה מסוג Largest Contentful Paint ‏(LCP) נטענה באופן מדורג"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "התמונה מסוג Largest Contentful Paint ‏(LCP) לא נטענה באופן מדורג"}, "core/audits/long-tasks.js | description": {"message": "הבדיקה מציינת את המשימות הארוכות ביותר ב-thread הראשי. היא עוזרת לזהות את המשימות שמאטות את הקלט במידה הרבה ביותר. [איך להימנע ממשימות שנדרש להן משך זמן ארוך ב-thread הראשי](https://web.dev/articles/long-tasks-devtools)?"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה משימה ארוכה אחת}one{נמצאו # משימות ארוכות}two{נמצאו # משימות ארוכות}other{נמצאו # משימות ארוכות}}"}, "core/audits/long-tasks.js | title": {"message": "יש להימנע ממשימות ארוכות בתהליכון הראשי"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "קטגוריה"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "כדאי לשקול את האפשרות לקצר את הזמן הדרוש לניתוח, להידור ולביצוע של JS. לשם כך, אפשר להשתמש במטענים ייעודיים (payloads) קטנים יותר של JS. [כאן מוסבר איך לצמצם את זמן העיבוד ב-thread הראשי](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "צריך לצמצם את העבודה על התהליכון הראשי"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "מצמצם את העבודה על התהליכון הראשי"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "כדי להגדיל את פוטנציאל החשיפה לכמה שיותר משתמשים, האתרים צריכים לעבוד בכל הדפדפנים הנפוצים. [מידע על תאימות לדפדפנים שונים](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "האתר עובד בדפדפנים שונים"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "יש לוודא שניתן לבצע קישור עומק לדפים נפרדים דרך כתובת URL ושכתובות ה-URL ייחודיות, כדי שניתן יהיה לשתף אותן ברשתות חברתיות. [מידע נוסף על הוספת קישורי עומק](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "לכל דף יש כתובת URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "כשמשתמשים מקישים באפליקציה, המעברים צריכים להיראות להם מהירים, גם ברשת איטית. החוויה הזו היא אחד הגורמים החשובים לתפיסת הביצועים בעיני המשתמשים. [מידע נוסף על מעברים בין דפים](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "מעברי הדפים לא מרגישים חסומים ברשת"}, "core/audits/maskable-icon.js | description": {"message": "סמל שניתן להתאמה (maskable) מבטיח שהתמונה תמלא את כל הצורה במקום שתוצג בפורמט letterbox כשהאפליקציה תותקן במכשיר. [מידע על סמלי מניפסט שניתנים להתאמה (maskable)](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "המניפסט לא מכיל סמלים שניתנים למיסוך"}, "core/audits/maskable-icon.js | title": {"message": "המניפסט מכיל לפחות סמל אחד שניתן למיסוך"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "המדד Cumulative Layout Shift ‏(CLS) מודד את התנועה של הרכיבים הגלויים בתוך אזור התצוגה. [מידע נוסף על המדד Cumulative Layout Shift ‏(CLS)](https://web.dev/articles/cls)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "המדד 'הצגת תוכן ראשוני (FCP)' מציין את הזמן שבו הטקסט או התמונה הראשונים מוצגים. [מידע נוסף על המדד 'הצגת תוכן ראשוני (FCP)'](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "המדד 'הצגת התוכן העיקרי (FMP)' מציין מתי מוצג התוכן העיקרי של הדף. [מידע נוסף על המדד 'הצגת התוכן העיקרי (FMP)'](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "המדד 'מאינטראקציה ועד הצגת התגובה' מודד את רמת הרספונסיביות של הדף – כמה זמן נדרש עד להצגת תגובה בדף לקלט של משתמש. [מידע נוסף על המדד 'מאינטראקציה ועד הצגת התגובה'](https://web.dev/articles/inp)"}, "core/audits/metrics/interactive.js | description": {"message": "ה'זמן עד לפעילות מלאה' הוא משך הזמן שחולף עד שהדף מאפשר אינטראקציה מלאה. [מידע נוסף על הערך 'הזמן עד לפעילות מלאה'](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "המדד Largest Contentful Paint ‏(LCP) מציין את הזמן שבו התמונה או הטקסט הגדולים ביותר מוצגים. [מידע נוסף על המדד Largest Contentful Paint ‏(LCP)](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "ההשהיה האפשרית המקסימלית שהמשתמשים עשויים לחוות לאחר קלט ראשוני (MPFID) היא משך הזמן של המשימה הארוכה ביותר. [מידע נוסף על המדד 'השהיה אפשרית מקסימלית לאחר קלט ראשוני'](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "מדד המהירות (Speed Index) מראה באיזו מהירות מוצג התוכן בדף. [מידע נוסף על מדד המהירות](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "משך הזמן המצטבר של כל פרקי הזמן מרגע הצגת התוכן הראשוני (FCP) ועד לפעילות מלאה, במקרים שבהם משך המשימה חורג מ-50 אלפיות שנייה. הערך מבוטא באלפיות שנייה. [מידע נוסף על המדד 'זמן חסימה כולל'](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "לזמני הלוך ושוב (RTT) ברשת יש השפעה גדולה על הביצועים. אם נדרש RTT ארוך בתקשורת עם מקור, זה סימן לכך שאפשר לשפר את הביצועים בעזרת שרתים שממוקמים קרוב יותר אל המשתמש. [מידע נוסף על זמן הלוך ושוב](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "זמני הלוך ושוב ברשת"}, "core/audits/network-server-latency.js | description": {"message": "זמני האחזור של השרתים יכולים להשפיע על הביצועים של האתרים. אם נדרש זמן אחזור ארוך בתקשורת עם המקור, זה סימן לכך שהשרת עמוס או שביצועי הקצה העורפי שלו נמוכים. [מידע נוסף על זמן התגובה של השרת](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "ז<PERSON><PERSON>י אחז<PERSON><PERSON> בקצה עורפי של שרת"}, "core/audits/no-unload-listeners.js | description": {"message": "האירוע `unload` לא מופעל בצורה אמינה, והפעלת ממשקי listener כדי לקלוט את האירוע הזה עשויה למנוע את השימוש באופטימיזציות שונות של הדפדפן, כמו האופטימיזציה במסגרת התכונה 'מטמון לדף הקודם/הבא'. יש להשתמש באירועי `pagehide` או `visibilitychange` במקום זאת. [מידע נוסף על ממשקי event listener להסרת הנתונים שנטענו](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "רישום מעבד אירוע של `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "הימנעות ממעבדי אירוע של `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "אנימציות שאינן מורכבות עלולות להיות איטיות ולהגדיל את ה-CLS. [איך להימנע מאנימציות לא מורכבות](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)?"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{נמצא אלמנט אנימציה אחד}one{נמצאו # אלמנטים של אנימציה}two{נמצאו # אלמנטים של אנימציה}other{נמצאו # אלמנטים של אנימציה}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "נכ<PERSON> הקשור למסנן יכול להניע פיקסלים"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "קיימת אנימציה נוספת ביעד, שאינה תואמת"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "בא<PERSON><PERSON><PERSON> יש מצב הרכבה, השונה ממצב \"החלפה\""}, "core/audits/non-composited-animations.js | title": {"message": "אין להשת<PERSON>ש באנימציות ללא הרכבת שכבות"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "מא<PERSON><PERSON><PERSON>ן הקשור לטרנספורמציה תלוי בגודל התיבה"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{מאפיין CSS שאינו נתמך: {properties}}one{מאפייני CSS שאינם נתמכים: {properties}}two{מאפייני CSS שאינם נתמכים: {properties}}other{מאפייני CSS שאינם נתמכים: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "האפ<PERSON>ט כולל פרמטרים של תזמון שאינם נתמכים"}, "core/audits/performance-budget.js | description": {"message": "הכמות והגודל של בקשות ברשת צריכים להיות מתחת ליעדים שהוגדרו בתקציב הביצועים הרלוונטי. [מידע נוסף על תקציבי ביצועים](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{בקשה אחת}one{בקשה אחת}two{# בקשות}other{# בקשות}}"}, "core/audits/performance-budget.js | title": {"message": "תקציב ביצועים"}, "core/audits/preload-fonts.js | description": {"message": "כדאי לטעון מראש גופני `optional` כדי שמבקרים לראשונה יוכלו להשתמש בהם. [מידע נוסף על טעינה מראש של גופנים](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "גופנים עם `font-display: optional` לא נטענו מראש"}, "core/audits/preload-fonts.js | title": {"message": "גופנים עם `font-display: optional` נטענו מראש"}, "core/audits/prioritize-lcp-image.js | description": {"message": "אם רכיב ה-LCP נוסף באופן דינמי לדף, צריך לטעון מראש את התמונה כדי לשפר את ה-LCP. [מידע נוסף על טעינה מראש של רכיבי LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "טעינה מראש של תמונת ה-Largest Contentful Paint (‏LCP)"}, "core/audits/redirects.js | description": {"message": "הפניות אוטומטיות מעכבות את טעינת הדף. [איך להימנע מהפניות אוטומטיות לדפים](https://developer.chrome.com/docs/lighthouse/performance/redirects/)?"}, "core/audits/redirects.js | title": {"message": "יש להימנע מהפניות אוטומטיות מרובות"}, "core/audits/seo/canonical.js | description": {"message": "קישורים קנוניים מציעים את כתובת ה-URL שיש להציג בתוצאות החיפוש. [מידע נוסף על קישורים קנוניים](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "התנגשויות בין כתובות URL מרובות ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "כתובת אתר לא חוקית ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "הכתובת מפנה למיקום `hreflang` אחר ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "כתובת ה-U<PERSON> אינה אבסולוטית ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "מצביע אל כתובת ה-U<PERSON> הבסיסית של הדומיין (דף הבית), במקום אל דף תוכן מתאים"}, "core/audits/seo/canonical.js | failureTitle": {"message": "למסמך אין `rel=canonical` חוקי"}, "core/audits/seo/canonical.js | title": {"message": "למסמך יש `rel=canonical` חוקי"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "קישור שלא ניתן לסרוק"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "יכול להיות שמנועי חיפוש ישתמשו במאפייני `href` בקישורים כדי לסרוק אתרים. יש לוודא שהמאפיין `href` של רכיבי עוגן מקשר אל יעד מתאים, כך שניתן יהיה לגלות יותר דפים באתר. [איך להגדיר קישורים שניתן לסרוק](https://support.google.com/webmasters/answer/9112205)?"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "לא ניתן לסרוק את הקישורים"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "ניתן לסרוק את הקישורים"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "טקסט נוסף שאינו קריא"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "גודל גופן"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% מהטקסט בדף"}, "core/audits/seo/font-size.js | columnSelector": {"message": "בו<PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "גו<PERSON>ן בגודל של פחות מ-12px הוא קטן מדי לקריאה: מבקרים עם מכשיר נייד ייאלצו לעשות תנועת צביטה להגדלת התצוגה כדי לקרוא את הטקסט. ההמלצה היא שיותר מ-60% מהטקסט יהיה בגודל של 12px לפחות. [מידע נוסף על גדלים קריאים של גופנים](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "טקסט קריא: {decimalProportion, number, extendedPercent}"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "הטקסט לא קריא כי לא בוצעה אופטימיזציית מטא תג של אזור התצוגה בשביל מסכים של ניידים."}, "core/audits/seo/font-size.js | failureTitle": {"message": "מידות הגופן במסך מקשות על הקריאה"}, "core/audits/seo/font-size.js | legibleText": {"message": "ט<PERSON><PERSON><PERSON> קריא"}, "core/audits/seo/font-size.js | title": {"message": "במסמך נעשה שימוש בגופן בגודל קריא"}, "core/audits/seo/hreflang.js | description": {"message": "קישורי hreflang מציינים למנועי חיפוש איזו גרסה של דף הם צריכים להציג בתוצאות חיפוש לשפה נתונה או לאזור נתון. [מידע נוסף על `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "למסמך אין `hre<PERSON><PERSON>` חוקי"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "ערך href יחסי"}, "core/audits/seo/hreflang.js | title": {"message": "למסמך יש `hreflang` חוקי"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "קוד שפה בלתי צפוי"}, "core/audits/seo/http-status-code.js | description": {"message": "לדפים בעלי קוד מצב HTTP לא תקין עשויות להיות שגיאות בהוספה לאינדקס. [מידע נוסף על קודי מצב HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "לדף יש קוד מצב HTTP המצביע על בעיה"}, "core/audits/seo/http-status-code.js | title": {"message": "קוד מצב ה-HTTP של הדף הוא 'הצלחה'"}, "core/audits/seo/is-crawlable.js | description": {"message": "מנועי חיפוש לא יכולים לכלול את הדפים שלך בתוצאות החיפוש אם אין להם הרשאה לסרוק אותם. [מידע נוסף על הוראות לסורקים](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "הוספת הדף לאינדקס חסומה"}, "core/audits/seo/is-crawlable.js | title": {"message": "הוספת הדף לאינדקס אינה חסומה"}, "core/audits/seo/link-text.js | description": {"message": "טקסט תיאורי בקישורים עוזר למנועי חיפוש להבין את נושא התוכן. [איך לשפר את רמת הנגישות של קישורים](https://developer.chrome.com/docs/lighthouse/seo/link-text/)?"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{נמצא קישור אחד}one{נמצאו # קישורים}two{נמצאו # קישורים}other{נמצאו # קישורים}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "אין לקישורים טקסט תיאורי"}, "core/audits/seo/link-text.js | title": {"message": "לקישורים יש טקסט תיאורי"}, "core/audits/seo/manual/structured-data.js | description": {"message": "כדי לאמת את תקינות הנתונים המובְנים צריך להפעיל את [הכלי לבדיקת הנתונים המובְנים](https://search.google.com/structured-data/testing-tool/) ואת [הלינטר (Linter) לנתונים מובנְים](http://linter.structured-data.org/). [מידע נוסף על נתונים מובְנים](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "הנתונים המובְנים חוקיים"}, "core/audits/seo/meta-description.js | description": {"message": "תיאורי מטא יכולים להופיע בתוצאות החיפוש כדי לספק סיכום קצר של תוכן הדף. [מידע נוסף על תיאור המטא](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "טקסט התיאור ריק."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "אין למסמך מטא תיאור"}, "core/audits/seo/meta-description.js | title": {"message": "יש למסמך מטא תיאור"}, "core/audits/seo/plugins.js | description": {"message": "מנועי חיפוש לא יכולים להוסיף לאינדקס תוכן של פלאגין, ומכשירים רבים מגבילים יישומי פלאגין או לא תומכים בהם. [למה כדאי להימנע משימוש ביישומי פלאגין](https://developer.chrome.com/docs/lighthouse/seo/plugins/)?"}, "core/audits/seo/plugins.js | failureTitle": {"message": "במסמך נעשה שימוש ביישומי פלאגין"}, "core/audits/seo/plugins.js | title": {"message": "אין במסמך שימוש ביישומי פלאגין"}, "core/audits/seo/robots-txt.js | description": {"message": "אם קובץ robots.txt אינו תקין, ייתכן שסורקים לא יוכלו להבין איך ברצונך שהאתר ייסרק או יתווסף לאינדקס. [למידע נוסף על robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "הבקשה לקובץ robots.txt החזירה מצב HTTP‏: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{נמצאה שגיאה אחת}one{נמצאו # שגיאות}two{נמצאו # שגיאות}other{נמצאו # שגיאות}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "מערכת Lighthouse לא הצליחה להוריד קובץ robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt אינו <PERSON>ו<PERSON>י"}, "core/audits/seo/robots-txt.js | title": {"message": "הקובץ robots.txt חוקי"}, "core/audits/seo/tap-targets.js | description": {"message": "כדי שיהיה קל להקיש על רכיבים אינטראקטיביים, כמו לחצנים וקישורים, בלי לגעת ברכיבים אחרים, הם צריכים להיות גדולים מספיק (48x48 פיקסלים) או עם ריווח גדול מספיק סביבם. [מידע נוסף על יעדי הקשה](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} מבין יעדי ההקשה הם בגודל תקין"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "רכיבי ההקשה קטנים מדי כי לא בוצעה אופטימיזציית מטא תגים של אזור התצוגה למסכים של ניידים"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "הגודל של רכיבי ההקשה הוגדר בצורה לא תקינה"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "יעד חופף"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "הרכיבים להקשה הוגדרו בגודל המתאים"}, "core/audits/server-response-time.js | description": {"message": "כדאי לוודא שזמן התגובה של השרת בנוגע למסמך הראשי הוא קצר, כי כל שאר הבקשות תלויות בו. [מידע נוסף על המדד Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "טעינת מסמך השורש ארכה {timeInMs, number, milliseconds} אלפיות שנייה"}, "core/audits/server-response-time.js | failureTitle": {"message": "יש לקצר את זמן התגובה הראשונית של השרת"}, "core/audits/server-response-time.js | title": {"message": "<PERSON><PERSON><PERSON> התגו<PERSON><PERSON> הראשונית של השרת היה קצר"}, "core/audits/splash-screen.js | description": {"message": "מסך פתיחה מעוצב מבטיח חוויה באיכות גבוהה כשמשתמשים מפעילים את האפליקציה שלך ממסכי הבית שלהם. [מידע נוסף על מסכי פתיחה](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "לא מוגדר עבור מסך פתיחה בהתאמה אישית"}, "core/audits/splash-screen.js | title": {"message": "מוגדר עבור מסך פתיחה בהתאמה אישית"}, "core/audits/themed-omnibox.js | description": {"message": "ניתן לעצב את סרגל הכתובות של הדפדפן כך שיתאים לאתר שלך. [למידע נוסף על תהליך העיצוב של סרגל הכתובות](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "לא מגדיר צבע עיצוב עבור סרגל הכתובות."}, "core/audits/themed-omnibox.js | title": {"message": "מגדיר צבע עיצוב עבור סרגל הכתובות."}, "core/audits/third-party-cookies.js | description": {"message": "התמיכה בקובצי Cookie של צד שלישי תוסר בגרסה עתידית של Chrome. [מידע נוסף על הוצאה משימוש בהדרגה של קובצי Cookie של צד שלישי](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)"}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{נמצא קובץ Cookie אחד}one{נמצאו # קובצי Cookie}two{נמצאו # קובצי Cookie}other{נמצאו # קובצי Cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "הדף משתמש בקובצי Cookie של צד שלישי"}, "core/audits/third-party-cookies.js | title": {"message": "אין שימוש בקוב<PERSON>י Cookie של צד שלישי"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (הצלחה של לקוחות)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (שיווק)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (רשתות חברתיות)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (סרטון)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "מו<PERSON>ר"}, "core/audits/third-party-facades.js | description": {"message": "טעינה מדורגת אפשרית לרכיבים מסוימים שהוטמעו בדף, שיצרו צדדים שלישיים. ניתן להחליף את הרכיבים האלה בדוגמאות לפיתוח עד שהם יידרשו. [כאן מוסבר איך לעכב טעינת רכיבים של צדדים שלישיים באמצעות דוגמאות לפיתוח](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{יש תבנית Facade אחת שזמינה כחלופה}one{יש # תבניות Facade שזמינות כחלופות}two{יש # תבניות Facade שזמינות כחלופות}other{יש # תבניות Facade שזמינות כחלופות}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "טעינה מדורגת אפשרית במשאבים מסוימים של צדדים שלישיים בעזרת תבנית Facade"}, "core/audits/third-party-facades.js | title": {"message": "טעינה מדורגת של משאבים של צדדים שלישיים באמצעות תבניות Facade"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "צ<PERSON> שלישי"}, "core/audits/third-party-summary.js | description": {"message": "קוד של צד שלישי עשוי להשפיע בצורה משמעותית על ביצועי הטעינה. מומלץ להגביל את הכמות של ספקי צד שלישי שאינם הכרחיים, ולהשתדל לטעון קודים של צדדים שלישיים רק אחרי שמסתיימת הטעינה העיקרית של הדף. [איך אפשר לצמצם את ההשפעה של צדדים שלישיים](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)?"}, "core/audits/third-party-summary.js | displayValue": {"message": "קוד של צד שלישי חסם את התהליכון הראשי למשך {timeInMs, number, milliseconds} אלפיות השנייה"}, "core/audits/third-party-summary.js | failureTitle": {"message": "עליך להפחית את השפעת הקוד של צד שלישי"}, "core/audits/third-party-summary.js | title": {"message": "צמצום השימוש בצדדים שלישיים"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "הערך שנמדד"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "מדד"}, "core/audits/timing-budget.js | description": {"message": "עליך להגדיר תקציב תזמון שיעזור לך לעקוב אחר ביצועי האתר. אתרים עם ביצועים טובים נטענים מהר ומגיבים במהירות לאירועי קלט של משתמשים. [מידע נוסף על תקציבי ביצועים](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "תק<PERSON>יב תזמון"}, "core/audits/unsized-images.js | description": {"message": "כדי להפחית שינויים בפריסה וכדי לשפר את ה-CLS, יש להגדיר רוחב וגובה מפורשים ברכיבי תמונות. [כך מגדירים מידות תמונה](https://web.dev/articles/optimize-cls#images_without_dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "יש רכיבי תמונה ללא מאפייני `width` ו-`height` מפורשים"}, "core/audits/unsized-images.js | title": {"message": "לרכי<PERSON>י תמונה יש מאפייני `width` ו-`height` מפורשים"}, "core/audits/user-timings.js | columnType": {"message": "סוג"}, "core/audits/user-timings.js | description": {"message": "כדי למדוד את ביצועי האפליקציה בפועל במהלך חוויות משתמש חשובות, כדאי לשקול את האפשרות להוסיף לאפליקציה את User Timing API. [מידע נוסף על סימוני User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{תזמון משתמש אחד}one{# תזמוני משתמש}two{# תזמוני משתמש}other{# תזמוני משתמש}}"}, "core/audits/user-timings.js | title": {"message": "סימונים ומדידות של User Timing"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "נמצא `<link rel=preconnect>` עבור \"{security<PERSON><PERSON><PERSON>}\", אבל הדפדפן לא השתמש בו. יש לוודא שנעשה שימוש תקין במאפיין `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "כדאי לשקול להוסיף את ההינטים של המשאבים `preconnect` או `dns-prefetch` כדי ליצור מראש קישורים אל מקורות חשובים של צדדים שלישיים. [מידע נוסף על יצירת קישור מראש למקורות נדרשים](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "יש להתחבר מראש למקורות נדרשים"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "נמצאו יותר משני קישורים `<link rel=preconnect>`. השימוש בקישורים מקדימים צריך להיות מצומצם מאוד ומוגבל למקורות החשובים ביותר."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "נמצא `<link rel=preconnect>` עבור \"{security<PERSON><PERSON><PERSON>}\", אבל הדפדפן לא השתמש בו. יש לבצע `preconnect` רק למקורות חשובים שהדף יבקש בוודאות."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "נמצא קישור טעינה מראש `<link>` ל-\"{preloadURL}\", אך הדפד<PERSON>ן לא השתמש בו. יש לוודא שנעשה שימוש תקין במאפיין `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "כדאי לשקול את האפשרות להשתמש ב-`<link rel=preload>` כדי לקבוע את סדר העדיפויות של אחזור משאבים שנדרשים בשלב מאוחר יותר של טעינת הדף. [איך לטעון מראש בקשות חשובות](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)?"}, "core/audits/uses-rel-preload.js | title": {"message": "יש לטעון מראש בקשות עיקריות"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "כתובת URL של מפה"}, "core/audits/valid-source-maps.js | description": {"message": "מפות מקור מתרגמות קוד מוקטן לקוד המקורי. הפעולה הזו עוזרת למפתחים לנפות באגים בסביבת ייצור. כמו כן, ניתן לקבל תובנות נוספות ב-Lighthouse. כדאי לשקול לפרוס מפות מקור כדי ליהנות מהיתרונות האלה. [מידע נוסף על מפות מקור](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "חסרות מפות מקור לספריית JavaScript של צד ראשון"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "מפת מקור חסרה בקובץ JavaScript גדול"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{אזהרה: פריט אחד חסר ב-`.sourcesContent`}one{אזהרה: חסרים # פריטים ב-`.sourcesContent`}two{אזהרה: חסרים # פריטים ב-`.sourcesContent`}other{אזהרה: חסרים # פריטים ב-`.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "הדף מכיל מפות מקור חוקיות"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` מבצע אופטימיזציה באפליקציה לגדלים של מסכים בניידים, וגם מונע [עיכוב של 300 אלפיות השנייה בקלט של המשתמשים](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [מידע נוסף על השימוש במטא תג של אזור התצוגה](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "לא נמצא תג `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "אין תג `<meta name=\"viewport\">` עם `width` או `initial-scale`"}, "core/audits/viewport.js | title": {"message": "יש תג `<meta name=\"viewport\">` עם `width` או `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "זו המשימה של חסימת thread המתרחשת במהלך המדידה 'מאינטראקציה ועד הצגת התגובה'. [מידע נוסף על המדד 'מאינטראקציה ועד הצגת התגובה'](https://web.dev/articles/inp)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "עיבוד האירוע '{interactionType}' נמשך {timeInMs, number, milliseconds} אלפיות השנייה"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "יעד ה<PERSON>רוע"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "יש להפחית את משך העיבוד שמבוצע במהלך אינטראקציה חשובה"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "השהיה לאחר קלט"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "השהיה של הצגת תגובה"}, "core/audits/work-during-interaction.js | processingTime": {"message": "<PERSON><PERSON><PERSON> העיבוד"}, "core/audits/work-during-interaction.js | title": {"message": "המערכת מפחיתה את כמות העבודה שמבוצעת במהלך אינטראקציה חשובה"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "אלו הזדמנויות לשיפור השימוש ב-ARIA באפליקציה, והן יכולות לשפר את החוויה של משתמשים שנעזרים בטכנולוגיות לנגישות, כמו קוראי מסך."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "אלה הזדמנויות לספק תוכן חלופי לווידאו ואודיו. הפעולות האלה יכולות לשפר את החוויה של משתמשים עם לקויות שמיעה או ראייה."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "וידאו ואודיו"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "הפריטים האלה מדגישים שיטות מומלצות נפוצות בשביל נגישות."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "שיטות מומלצות"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "הבדיקות האלה מדגישות הזדמנויות [לשפר את הנגישות של אפליקציית האינטרנט](https://developer.chrome.com/docs/lighthouse/accessibility/). כשמשתמשים בזיהוי אוטומטי, מאתרים רק קבוצת משנה של בעיות, ורמת הנגישות של אפליקציית האינטרנט לא מובטחת. לכן, מומלץ לבצע גם [בדיקה ידנית](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "הפריטים האלה בודקים תחומים שלא ניתן לבדוק באמצעות כלי בדיקה אוטומטיים. מידע נוסף זמין במדריך שלנו שבו מוסבר [איך לערוך בדיקת נגישות](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "נגישות"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "אלו הזדמנויות לשיפורים שיאפשרו לקרוא את התוכן בצורה קלה יותר."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "ניגודיות"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "אלו הזדמנו<PERSON><PERSON><PERSON> לשיפור של פענוח התוכן על-ידי משתמשים בלוקאלים שונים."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "התאמה לשוק המקומי והבינלאומי"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "אלו הזדמנויות לשיפור הסמנטיקה של פקדים באפליקציה. הן יכולות לשפר את החוויה של משתמשים שנעזרים בטכנולוגיות לנגישות, כמו קורא מסך."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "שמות ותוויות"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "אלו הזדמנויות לשיפור הניווט באפליקציה באמצעות מקלדת."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "ניווט"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "אלו הזדמנויות לשיפור חוויית הקריאה של נתונים בטבלאות או ברשימות באמצעות טכנולוגיה מסייעת, כמו קורא מסך."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "טבלאות ורשימות"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "תאימות לדפדפנים"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "שיטות מומלצות"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "כללי"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "א<PERSON><PERSON><PERSON> ובטיחות"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "חוויית משתמש"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "באמצעות תקציבי ביצועים ניתן להגדיר יעדי ביצועים עבור האתר."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "תקציבים"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "מידע נוסף לגבי ביצועי האפליקציה. למספרים האלה אין [השפעה ישירה](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) על ציון הביצועים."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "ניתוחים"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "היבט הביצועים הקריטי ביותר הוא מהירות העיבוד של פיקסלים במסך. ערכי מפתח: הצגת התוכן הראשוני, הצגת התוכן העיקרי"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "שיפורים בעיבוד ראשון"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "ההצעות האלה יכולות לעזור לך להאיץ את טעינת הדף. אין להן [השפעה ישירה](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) על ציון הביצועים."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "הזדמנויות"}, "core/config/default-config.js | metricGroupTitle": {"message": "ערכים"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "צריך לשפר את חוויית הטעינה הכללית, כך שהדף יגיב ויהיה מוכן לשימוש במהירות האפשרית. ערכי מפתח: זמן עד לאינטראקטיביות (Time to Interactive), מדד מהירות (Speed Index)"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "סך השיפורים"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "ביצועים"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "הבדיקות האלה מאמתות את ההיבטים של Progressive Web App. [איך יוצרים אפליקציה מוצלחת מסוג Progressive Web App](https://web.dev/articles/pwa-checklist)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "בדיקות אלה נדרשות עבור שורת הבסיס [רשימת משימות של PWA](https://web.dev/articles/pwa-checklist), אך הן לא נבדקות באופן אוטומטי על ידי Lighthouse. הן לא משפיעות על הניקוד שלך, אך חשוב לאמת אותן באופן ידני."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "ניתן להתקנה"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "מותאם ל-PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "הבדיקות האלה עוזרות לוודא שהתוכן בדף שלך מותאם לעצות הבסיסיות בנוגע לאופטימיזציה למנועי חיפוש. יש הרבה גורמים אחרים שלא משוקללים על ידי Lighthouse בבדיקות האלה, ועשויים להשפיע על דירוג החיפוש, לרבות הביצועים ב[דוח ה-Web Vitals הבסיסיים](https://web.dev/explore/vitals). [מידע נוסף על היסודות של חיפוש Google](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "אפשר להפעיל באתר את המאמתים הנוספים האלה כדי לבדוק עוד שיטות מומלצות של אופטימיזציה למנועי חיפוש."}, "core/config/default-config.js | seoCategoryTitle": {"message": "אופטימיז<PERSON><PERSON>ה למנועי חיפוש"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "יש לכתוב את קוד ה-HTML באופן שיאפשר לסורקים להבין את תוכן האפליקציה בצורה טובה יותר."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "שיטות מומלצות לגבי תוכן"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "כדי שהא<PERSON>ליקציה תופיע בתוצאות החיפוש, סורקים צריכים לקבל גישה אליה."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "סריקה והוספה לאינדקס"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "הדפים צריכים להתאים לניידים, כדי שמשתמשים לא יצטרכו לעשות תנועת צביטה או התקרבות כדי לקרוא את דפי התוכן. [איך מתאימים דפים לניידים](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "התאמה לניידים"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "יחידת העיבוד המרכזית (CPU) במכשיר שנבדק איטית ממה שצפוי ב-Lighthouse. זה עשוי להשפיע לרעה על ציון הביצועים שלך. בקישור הבא אפשר למצוא מידע נוסף על [כיול של מכפיל מתאים להאטה ביחידת העיבוד המרכזית (CPU)](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "ייתכן שהדף לא נטען כמו שציפית כי כתובת ה-URL הנבדקת ({requested}) הופנתה לכתובת {final}. כדאי לנסות לבדוק ישירות את כתובת ה-URL השנייה."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "הדף נטען לאט מדי והפעולה לא הסתיימה במסגרת הזמן שהוקצבה. ייתכן שהתוצאות לא יהיו מלאות."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "פג הזמן הקצוב לניק<PERSON>י המטמון של הדפדפן. כדאי לבדוק שוב את הדף הזה ולדווח על באג אם הבעיה נמשכת."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{ייתכן שיש נתונים שמורים שמשפיעים על ביצועי הטעינה במיקום הזה: {locations}. יש לבדוק את הדף הזה בחלון אנונימי כדי למנוע מהמשאבים האלה להשפיע על תוצאות הניקוד.}one{ייתכן שיש נתונים שמורים שמשפיעים על ביצועי הטעינה במיקומים האלה: {locations}. יש לבדוק את הדף הזה בחלון אנונימי כדי למנוע מהמשאבים האלה להשפיע על תוצאות הניקוד.}two{ייתכן שיש נתונים שמורים שמשפיעים על ביצועי הטעינה במיקומים האלה: {locations}. יש לבדוק את הדף הזה בחלון אנונימי כדי למנוע מהמשאבים האלה להשפיע על תוצאות הניקוד.}other{ייתכן שיש נתונים שמורים שמשפיעים על ביצועי הטעינה במיקומים האלה: {locations}. יש לבדוק את הדף הזה בחלון אנונימי כדי למנוע מהמשאבים האלה להשפיע על תוצאות הניקוד.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "פג הזמן הקצוב לניקוי של נתוני המקור. כדאי לבדוק שוב את הדף הזה ולדווח על באג אם הבעיה נמשכת."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "שגיאה בניתוח הכותרת `link`‏ ({error}):‏ `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "זוהה ניווט בדפים במהלך ההפעלה. לא מומלץ להשתמש במצב טווח הזמן לבדיקת ניווטים בדפים. עדיף להשתמש במצב הניווט כדי לבדוק ניווטים בדפים לצורך שיפור השיוך לצד שלישי וזיהוי ה-thread הראשי."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "רק דפים שנטענים באמצעות בקשת GET מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "אפשר לשמור במטמון רק דפים עם קוד סטטוס של 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "מערכת Chrome זיהתה ניסיון להפעיל JavaScript בזמן שהדף היה במטמון."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "בשלב זה, דפים שנשלחה מהם בקשה ל-AppBanner לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה עקב בקשה מסוג הודעת keep-alive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה על ידי תכונות ניסיוניות. צריך לעבור לכתובת chrome://flags/#back-forward-cache כדי להפעיל אותה באופן מקומי במכשיר הזה."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה על ידי שורת הפקודה."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי אין מספיק זיכרון."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "התכונה 'מטמון לדף הקודם/הבא' לא נתמכת על ידי אובייקט בעל גישה."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה בשביל כלי לעיבוד מראש."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "אי אפשר לשמור את הדף במטמון כי יש בו מופע של BroadcastChannel עם ממשקי listener רשומים."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים עם כותרת 'cache-control:no-store'."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "המטמון נוקה באופן מכוון."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "הדף הוצא מהמטמון כדי לאפשר שמירה של דף אחר במטמון."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "בשלב זה, דפים שמכילים יישומי פלאגין לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "דפים שנעשה בהם שימוש ב-FileChooser API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "דפים שנעשה בהם שימוש ב-File System Access API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "דפים שנעשה בהם שימוש ב-Dispatcher של מכשירים לאחסון מדיה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "נגן מדיה פעל במהלך היציאה."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "דפים שנעשה בהם שימוש בממשק MediaSession API ושמוגדר בהם מצב הפעלה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "דפים שנעשה בהם שימוש ב-MediaSession API ושמוגדרים בהם רכיבי handler של פעולות, לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה בגלל קורא מסך."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "דפים שנעשה בהם שימוש ב-SecurityHandler לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "דפים שנעשה בהם שימוש ב-Serial API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "דפים שנעשה בהם שימוש ב-WebAuthetication API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "דפים שנעשה בהם שימוש ב-WebBluetooth API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "דפים שנעשה בהם שימוש ב-WebUSB API לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה כי קובצי cookie הושבתו בדף עם שימוש ב-`Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-worker או ב-worklet ייעודיים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "טעינת המסמך לא הסתיימה לפני היציאה."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "הב<PERSON><PERSON><PERSON> של האפליקציה פעל במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "מנהל הסיסמאות של Chrome פעל במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "התבצע זיקוק DOM במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "הכלי DOM Distiller Viewer פעל במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה בגלל תוספים שמשתמשים ב-API לשליחת הודעות."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "תוספים עם חיבור לאורך זמן אמורים לסגור את החיבור לפני ההוספה למטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "תוספים עם חיבור לאורך זמן ניסו לשלוח הודעות למסגרות במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה בגלל תוספים."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "חלון עזר של תיבת דו-שיח, למשל תיבת דו-שיח עם סיסמת http או שליחה מחדש של טופס, הוצג בדף במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "הדף ללא החיבור לאינטרנט הוצג במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "סרגל ההתערבות לגבי חוסר בזי<PERSON><PERSON><PERSON><PERSON> פנוי פעל במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "נשלחו בקשות להרשאות במהלך היציאה מהדף."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "תוכנת חסימה לחלונות קופצים פעלה במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "פרטי הגלישה הבטוחה הוצגו במהלך היציאה."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "במסגרת השימוש ב'גלישה בטוחה' נקבע שהדף הזה פוגעני והחלון הקופץ נחסם."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "קובץ שירות (service worker) הופעל בזמן שהדף היה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה עקב שגיאה במסמך."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "לא ניתן לשמור ב-bfcache דפים שנעשה בהם שימוש ב-FencedFrames."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "הדף הוצא מהמטמון כדי לאפשר שמירה של דף אחר במטמון."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "בשלב זה, ד<PERSON>ים שהעניקו הרשאת גישה לסטרימינג של מדיה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "בשלב זה, דפים שנעשה בהם שימוש בפורטלים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-IdleManager לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "בשלב זה, דפים שיש בהם חיבור IndexedDB פתוח לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה בגלל אירוע מסוג IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "נעשה שימוש בממשקי API לא מתאימים."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "בשלב זה, דפים שמחדירים אליהם `JavaScript` באמצעות תוספים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "בשלב זה, דפים שמחדירים אליהם `StyleSheet` באמצעות תוספים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | internalError": {"message": "שגיאה פנימית."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "התכונה 'מטמון לדף הקודם/הבא' הושבתה עקב בקשה מסוג הודעת keep-alive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "בשלב זה, דפים שנעשה בהם שימוש בנעילת מקלדת לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | loading": {"message": "טעינת הדף לא הסתיימה לפני היציאה."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב העיקרי שלהם מופיע 'cache-control:no-cache'."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב העיקרי שלהם מופיע 'cache-control:no-store'."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "הניווט בוטל לפני שהדף שוחזר מהמטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "הדף הוצא מהמטמון כי התקבלו יותר מדי נתונים בחיבור פעיל לרשת. יש הגבלה של Chrome על כמות הנתונים שדף יכול לקבל כשהוא שמור במטמון."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "בשלב זה, דפים שמכילים XHR או פונקציית fetch()‎ פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "הדף הוצא מהמטמון לדף הקודם/הבא כי בקשה פעילה לרשת כללה הפניה אוטומטית."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "הדף הוצא מהמטמון כי החיבור לרשת היה פתוח במשך יותר מדי זמן. יש הגבלה של Chrome בנוגע למשך הזמן שדף יכול לקבל נתונים כשהוא שמור במטמון."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שאין להם כותרת תגובה תקינה."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "הניווט התרחש במסגרת שאינה המסגרת הראשית."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "בשלב זה, דפים עם טרנזקציות פעילות של IndexedDB לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לאחזור לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "בשלב זה, דפים עם בקשת רשת פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "בשלב זה, דפים עם בקשת רשת XHR פעילה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-PaymentManager לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "בשלב זה, דפים שבהם נעשה שימוש ב'תמונה בתוך תמונה' לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | portal": {"message": "בשלב זה, דפים שנעשה בהם שימוש בפורטלים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | printing": {"message": "בשלב זה, דפים שמוצג בהם ממשק משתמש של הדפסה לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "הדף נפתח בעזרת '`window.open()`' ובכרטיסייה נוספת יש הפניה לזה, או שנפתח חלון על ידי הדף."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "תהליך הרינדור קרס לדף שנמצא במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "תהליך הרינדור הופסק לדף שנמצא במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "בשלב זה, דפים שנשלחה מהם בקשה להרשאות להקלטת אודיו לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "בשלב זה, דפים שנשלחה מהם בקשה להרשאות חיישן לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "בשלב זה, דפים שנשלחה מהם בקשה לסנכרון ברקע או להרשאות אחזור לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "בשלב זה, דפים שנשלחה מהם בקשה להרשאות MIDI לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "בשלב זה, דפים שנשלחה מהם בקשה להרשאות לשליחת התראות לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "בשלב זה, דפים שנשלחה מהם בקשת גישה לאחסון לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "בשלב זה, דפים שנשלחה מהם בקשה להרשאות לצילום סרטונים לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "אפשר לשמור במטמון רק דפים שהסכימה של כתובת ה-URL שלהם היא HTTP או HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "נתבעה בעלות על הדף על ידי קובץ שירות (service worker) בזמן שהדף היה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "קובץ שירות (service worker) ניסה לשלוח `MessageEvent` לדף שנמצא במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "בוטל הרישום של ServiceWorker כשדף היה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "הדף הוצא מהמטמון לדף הקודם/הבא עקב הפעלה של קובץ שירות (service worker)."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome הופעל מחדש וניקה את הרשומות במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SharedWorker לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SpeechRecognizer לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-SpeechSynthesis לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "החל ניווט בדף שלא הסתיים מצד iframe."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב המשנה שלהם מופיע 'cache-control:no-cache'."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים שבמשאב המשנה שלהם מופיע 'cache-control:no-store'."}, "core/lib/bf-cache-strings.js | timeout": {"message": "הדף חרג ממגבלת הזמן המקסימלית במטמון לדף הקודם/הבא, והתוקף שלו פג."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "פג הזמן הקצוב להוספה של הדף להוספה למטמון לדף הקודם/הבא (ככל הנראה עקב רכיבי handler לפעולות ממושכות של הסתרת דפים)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "לדף יש handler של הסרת נתונים שנטענו במסגרת הראשית."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "לדף יש handler של הסרת נתונים שנטענו במסגרת משנית."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "הכותרת של השינוי מברירת המחדל של סוכן המשתמש שונתה על ידי הדפדפן."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "בשלב זה, דפים שהעניקו הרשאת גישה לצילום סרטונים או להקלטת אודיו לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebDatabase לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webHID": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebHID לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebLocks לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebNfc לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebOTPService לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים עם WebRTC."}, "core/lib/bf-cache-strings.js | webShare": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebShare לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים עם WebSocket."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "אי אפשר להוסיף למטמון לדף הקודם/הבא דפים עם WebTransport."}, "core/lib/bf-cache-strings.js | webXR": {"message": "בשלב זה, דפים שנעשה בהם שימוש ב-WebXR לא מתאימים לשמירה במטמון לדף הקודם/הבא."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "כדאי להוסיף סכימות של כתובות URL מסוג https:‎ ו-http:‎ (דפדפנים שתומכים ב-`'strict-dynamic'` יתעלמו מהן) לצורך תאימות לאחור עם דפדפנים ישנים."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "ההוראה `disown-opener` הוצאה משימוש החל מ-CSP3. במקום זאת, יש להשתמש בכותרת Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "ההוראה `referrer` הוצאה משימוש החל מ-CSP2. במקום זאת, יש להשתמש בכותרת Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "ההוראה `reflected-xss` הוצאה משימוש החל מ-CSP2. במקום זאת, יש להשתמש בכותרת X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "חסר `base-uri`, שמ<PERSON><PERSON><PERSON><PERSON> להחדיר תגי `<base>` כדי להגדיר את כתובת ה-URL הבסיסית לכל כתובות ה-URL היחסיות (למשל, סקריפטים) לדומיין שנשלט על ידי תוקף. כדאי להגדיר את `base-uri` לערך '`'none'`' או '`'self'`'."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "כשלא מוגדר `object-src`, מתאפשרת החדרת יישומי פלאגין שמפעילים סקריפטים לא בטוחים. אם אפשר, כדאי להגדיר את `object-src` לערך '`'none'`'."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "חסרה ההוראה `script-src`. מצב כזה מאפשר הפעלה של סקריפטים לא בטוחים."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "שכחת להוסיף נקודה-פסיק? נראה ש-{keyword} זו הוראה ולא מילת מפתח."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "צפנים חד-פעמיים צריכים להיכתב ב-charset מסוג base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "צפנים חד-פעמיים (nonce) צריכים להכיל 8 תווים לפחות."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "עליך להימנע משימוש בסכמות פשוטות של כתובות URL ‏({keyword}) בהוראה הזו. סכמות פשוטות של כתובות URL מאפשרות להעלות סקריפטים ממקור דומיין לא בטוח."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "עליך להימנע משימוש בתווים כלליים פשוטים לחיפוש ‏({keyword}) בהוראה הזו. בתווים כלליים פשוטים לחיפוש מאפשרים להעלות סקריפטים ממקור דומיין לא בטוח."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "אפשר להגדיר יעד לדיווח רק באמצעות הוראת report-to. ההוראה הזו נתמכת רק בדפדפנים המבוססים על Chromium, לכן מומלץ להשתמש גם בהוראת `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "אין CSP עם הגדרה של יעד לדיווח. במצב כזה קשה לתחזק את ה-CSP לאורך זמן ולעקוב אחר פריצות."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "ניתן לעקוף את רשימת ההיתרים של המארח לעיתים קרובות. כדאי להשתמש במקום זאת בגיבובים (hash) או בצפנים חד-פעמיים של CSP, יחד עם `'strict-dynamic'`, אם יש צורך."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "הוראת CSP לא ידועה."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "נראה שמילת המפתח {keyword} אינה תקנית."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "הרכיב `'unsafe-inline'` מאפשר להפעיל בדף סקריפטים וגורמים מטפלים באירועים שאינם בטוחים. רצוי להשתמש בגיבובים או בצפנים חד-פעמיים של CSP כדי להתיר הפעלה של כל סקריפט בנפרד."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "כדאי להוסיף רכיב `'unsafe-inline'` (דפד<PERSON>נים שתומכים בגיבובים/צפנים חד-פעמיים יתעלמו ממנו) לצורך תאימות לאחור עם דפדפנים ישנים."}, "core/lib/deprecation-description.js | feature": {"message": "פרטים נוספים זמינים בדף של סטטוס התכונה."}, "core/lib/deprecation-description.js | milestone": {"message": "השינוי הזה ייכנס לתוקף בגרסה {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "נעשה שימוש בתכונה שהוצאה משימוש"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "ההרשאה לא תיכלל עם השימוש בסמל של התו הכללי לחיפוש (*) בהגדרות לטיפול ב-`Access-Control-Allow-Headers` של CORS."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "יש להשתמש במאפיין `disableRemotePlayback` כדי להשבית את שילוב ההעברה (cast) המוגדר כברירת מחדל במקום להשתמש בבורר `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "בקשות למשאבים שכתובות ה-URL שלהן מכילות גם תווי `(n|r|t)` של רווח לבן מחוק וגם תווי 'פחות מ-' (`<`) חסומות. כדי לטעון את המשאבים האלה, יש להסיר תווי 'פחות מ-' מקודדים ושורות חדשות ממקומות כמו ערכי מאפיינים של רכיבים."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "האפשרות `chrome.loadTimes()` הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: תזמון ניווט 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "האפשרות `chrome.loadTimes()` הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: תזמון המרת תמונה וקטורית למפת סיביות."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "האפשרות `chrome.loadTimes()` הוצאה משימוש. במקומה יש להשתמש ב-API הסטנדרטי: `nextHopProtocol` בתזמון ניווט 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "קובצי cookie המכילים תו `(0|r|n)` יידחו במקום להיחתך."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "האפשרות למתן את מדיניות המקור הזהה על ידי הגדרת `document.domain` הוצאה משימוש, ותושבת כברירת מחדל. האזהרה הזו על הוצאה משימוש מתייחסת לגישה ממקורות שונים שהופעלה על ידי הגדרת `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "האפשרות להפעיל את הפונקציה window.alert דרך רכיבי iframe ממקורות שונים הוצאה משימוש ותוסר בעתיד."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "האפשרות להפעיל את הפונקציה window.confirm דרך רכיבי iframe ממקורות שונים הוצאה משימוש ותוסר בעתיד."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "אירועי מוטציה של DOM, כולל `DOMSubtreeModified`,‏ `DOMNodeInserted`,‏ `DOMNodeRemoved`,‏ `DOMNodeRemovedFromDocument`,‏ `DOMNodeInsertedIntoDocument` ו-`DOMCharacterDataModified` הוצאו משימוש (https://w3c.github.io/uievents/#legacy-event-types) ויוסרו בעתיד. במקומם יש להשתמש ב-`MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "התמיכה בהקצאת כתובות URL מסוג data:‎ לרכי<PERSON> <use> מסוג SVG הוצאה משימוש ותוסר בעתיד."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "האפשרות למתן את מדיניות המקור הזהה על ידי הגדרת `document.domain` הוצאה משימוש, ותושבת כברירת מחדל. כדי להמשיך להשתמש בתכונה הזו, יש לבטל את ההסכמה לאשכולות סוכנים המשויכים למקור על ידי שליחת כותרת `Origin-Agent-Cluster: ?0` עם תגובת HTTP למסמך ולמסגרות. פרטים נוספים זמינים בכתובת https://developer.chrome.com/blog/immutable-document-domain/‎."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "הכותרת `Expect-CT` הוצאה משימוש ותוסר. ב-Chrome נדרשת שקיפות לגבי כל האישורים הציבוריים המהימנים שהונפקו אחרי 30 באפריל 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "האפשרויות `getCurrentPosition()` ו-`watchPosition()` לא פועלות יותר במקורות לא מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "האפשרויות `getCurrentPosition()` ו-`watchPosition()` הוצאו משימוש במקורות שאינם מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "האפשרות `getUserMedia()` לא פועלת יותר במקורות לא מאובטחים. כדי להשתמש בתכונה הזו, כדאי להעביר את האפליקציה למקור מאובטח כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "האפשרות `RTCPeerConnectionIceErrorEvent.hostCandidate` הוצאה משימוש. במקומה יש להשתמש באפשרות `RTCPeerConnectionIceErrorEvent.address` או באפשרות `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "המקור של המוכר ונתונים שרירותיים מאירוע של קובץ השירות (service worker)‏ `canmakepayment` הוצאו משימוש ויוסרו: `topOrigin`‏, `paymentRequestOrigin`‏, `methodData`‏, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "האתר שלח בקשה למשאב משנה מרשת שיש לו גישה אליה רק בגלל שלמשתמשים שלו יש הרשאות למיקום הרשת. הבקשות האלה חושפות שרתים ומכשירים שלא גלויים לכולם לאינטרנט, ומגבירות את הסיכון למתקפת Cross-Site Request Forgery‏ (CSRF) ולדליפת מידע. כדי להפחית את הסיכונים האלה, מערכת Chrome מוציאה משימוש בקשות למשאבי משנה שאינם גלויים לכולם כשהן מגיעות מהקשרים לא מאובטחים, ובקרוב המערכת תתחיל לחסום אותן."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "שם השדה `dailyUpdateUrl` של `InterestGroups` שהוע<PERSON>ר אל `joinAdInterestGroup()` שונה לשם `updateUrl` כדי לשקף את ההתנהגות בצורה מדויקת יותר."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "לא ניתן לטעון CSS מכתובות URL של `file:` אלא אם הן מסתיימות בסיומת הקובץ `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "האפשרות לבטל את הסרת הטווח האסינכר<PERSON><PERSON>י של `remove()` באמצעות `SourceBuffer.abort()` הוצאה משימוש עקב שינוי במפרט. התמיכה באפשרות הזו תוסר בעתיד. במקום זאת, יש להפעיל listener לאירוע של `updateend`. ניתן להשתמש ב-`abort()` רק כדי לבטל צירוף מדיה אסינכרוני או לאפס מצב מנתח."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "האפשרות להגדיר את `MediaSource.duration` מתחת לערך הגבוה ביותר של חותמת הזמן להצגה של כל המסגרות המקודדות השמורות במאגר נתונים זמני הוצאה משימוש עקב שינוי במפרט. בעתיד תוסר התמיכה בהסרה מרומזת של מדיה חתוכה ששמורה במאגר נתונים זמני. במקום זאת, יש להשתמש באפשרות `remove(newDuration, oldDuration)` מפורשת בכל `sourceBuffers`, ולהגדיר `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "תתקבל מ-Web MIDI בקשת הרשאה לשימוש גם אם לא צוין sysex ב-`MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "המא<PERSON>יין `shadowroot` שהוא ישן יותר ולא סטנדרטי הוצא משימוש, ו*לא יפעל יותר* בגרסה M119. במקומו צריך להשתמש במאפיין `shadowrootmode` שהוא חדש וסטנדרטי."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "אי אפשר יותר להשתמש ב-Notification API ממקורות לא מאובטחים. כדאי להעביר את האפליקציה למקור מאובטח, כמו HTTPS. פרטים נוספים זמינים בכתובת https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "לא ניתן יותר לבקש הרשאה ל-Notification API מ-iframe ממקורות שונים. במקום זאת, כדאי לבקש הרשאה ממסגרת ברמה עליונה או לפתוח חלון חדש."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "האפשרות `imageOrientation: 'none'` ב-createImageBitmap הוצאה משימוש. במקומה צריך להשתמש ב-createImageBitmap ולהוסיף את האפשרות ‎\\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "השותף שלך מנהל משא ומתן על גרסת ‎(D)TLS מיושנת. עליך לפנות לשותף שלך כדי לתקן זאת."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "ציון של '`overflow: visible`' בתגי img,‏ video ו-canvas עלול לגרום להופעת תוכן חזותי מחוץ לגבולות הרכיב. פרטים נוספים זמינים בכתובת https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "האפשרות `paymentManager.instruments` הוצאה משימוש. במקום זאת, אפשר להשתמש בהתקנה בזמן ריצה עבור רכיבי handler של תשלומים."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "הקריאה `PaymentRequest` עקפה את ההוראה `connect-src` של Content-Security-Policy ‏(CSP). המעקף הזה הוצא משימוש. יש להוסיף את מזהה אמצעי התשלום מ-`PaymentRequest` API (בשדה `supportedMethods`) להוראה `connect-src` של CSP."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "האפשרות `StorageType.persistent` הוצאה משימוש. במקומו יש להשתמש ב-`navigator.storage` רגיל."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "רכיב `<source src>` עם הורה מסוג `<picture>` נחש<PERSON> ללא תקין, והמערכת מתעלמת ממנו. במקומו יש להשתמש במאפיין `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "הפונקציה webkitCancelAnimationFrame היא ספציפית לספק. במקומה צריך להשתמש בפונקציה הסטנדרטית cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "הפונקציה webkitRequestAnimationFrame היא ספציפית לספק. במקומה צריך להשתמש בפונקציה הסטנדרטית requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitDisplayingFullscreen הוצא משימוש. במקומו צריך להשתמש ב-Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "ה-API‏ HTMLVideoElement.webkitEnterFullScreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Element.requestFullscreen()‎."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitEnterFullscreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Element.requestFullscreen()‎."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "ה-API‏ HTMLVideoElement.webkitExitFullScreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Document.exitFullscreen()‎."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitExitFullscreen()‎ הוצא משימוש. במקומו צריך להשתמש ב-Document.exitFullscreen()‎."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "ה-API‏ HTMLVideoElement.webkitSupportsFullscreen הוצא משימוש. במקומו צריך להשתמש ב-Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "אנחנו מוציאים משימוש את ה-API‏ `chrome.privacy.websites.privacySandboxEnabled`, אבל הוא ימשיך לפעול לצורך תאימות לאחור עד גרסה M113. במקומו צריך להשתמש ב-`chrome.privacy.websites.topicsEnabled`, ב-`chrome.privacy.websites.fledgeEnabled` וב-`chrome.privacy.websites.adMeasurementEnabled`. פרטים נוספים זמינים בכתובת https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "המגבלה `DtlsSrtpKeyAgreement` הוסרה. ציינת ערך `false` למגבלה הזו, והוא מפורש כניסיון להשתמש בשיטה `SDES key negotiation` שהוסרה. הפונקציונליות הזו הוסרה. במקומה יש להשתמש בשירות שתומך ב-`DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "המגבלה `DtlsSrtpKeyAgreement` הוסרה. ציינת ערך `true` למגבלה הזו ואין לו השפעה, אבל אפשר להסיר את המגבלה הזו כדי לשמור על סדר."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "הפונקציה getStats()‎ שמבוססת על קריאה חוזרת (callback) הוצאה משימוש ותוסר. במקומה אפשר להשתמש בפונקציה getStats()‎ שתואמת למפרט."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "ה-API‏ Range.expand()‎ הוצא משימוש. במקומו צריך להשתמש ב-Selection.modify()‎."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "בקשות למשאבי משנה שכתובות ה-URL שלהן מכילות פרטי כניסה מוטמעים (למשל `**********************/`) חסומות."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "האפשרות `rtcpMuxPolicy` הוצאה משימוש ותוסר."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "תידרש חסימה לגישה מדומיינים אחרים עבור `SharedArrayBuffer`. פרטים נוספים זמינים בכתובת https://developer.chrome.com/blog/enabling-shared-array-buffer/‎."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "האפשרות להפעיל את `speechSynthesis.speak()` ללא הפעלת משתמש הוצאה משימוש ותוסר."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "כדי להמשיך להשתמש ב-`Shared<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`, צריך לאשר חסימה לגישה מדומיינים אחרים בתוספים. הסבר זמין בכתובת https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/‎."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Web SQL הוצא משימוש. אפשר להשתמש ב-SQLite WebAssembly או ב-Indexed Database."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "רכיב descriptor של ההרשאה `window-placement` הוצא משימוש. במקומו צריך להשתמש ברכיב `window-management`. ניתן לקבל עוד עזרה בכתובת https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "מדיניות ההרשאות `window-placement` הוצאה משימוש. במקומה צריך להשתמש במדיניות `window-management`. ניתן לקבל עוד עזרה בכתובת https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "אין תמיכה ב-UTF-16 ב-J<PERSON><PERSON> של התגובה ב-`XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "האפשרות הסינכרונית `XMLHttpRequest` ב-thread הראשי הוצאה משימוש כי היא משפיעה לרעה על החוויה של משתמשי הקצה. ניתן לקבל עוד עזרה בכתובת https://xhr.spec.whatwg.org/‎."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "האפשרות `supportsSession()` הוצאה משימוש. במקומה, יש להשתמש באפשרות `isSessionSupported()` ולסמן את הערך הבוליאני שהתקבל בהתאמת הנתונים."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "משך החסימה של התהליכון הראשי"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "אורך חיים (TTL) של מטמון"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "תיאור"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "משך זמן"}, "core/lib/i18n/i18n.js | columnElement": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "רכיבים שנכשלו בבדיקה"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "מיקום"}, "core/lib/i18n/i18n.js | columnName": {"message": "שם"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "גובה החריגה מהתקציב"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "בקשות"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "גודל המשאב"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "סוג המשאב"}, "core/lib/i18n/i18n.js | columnSize": {"message": "גודל"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "שעת התחלה"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "משך הזמן שנדרש"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "גודל ההעברה"}, "core/lib/i18n/i18n.js | columnURL": {"message": "כתובת אתר"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "פו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> חי<PERSON><PERSON>ון"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "פו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> חי<PERSON><PERSON>ון"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> פוטנציאלי של ‎{wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{נמצא רכיב אחד}one{נמצאו # רכיבים}two{נמצאו # רכיבים}other{נמצאו # רכיבים}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "פוטנציאל לקיצור זמן הטעינה ב-{wastedMs, number, milliseconds} אלפיות שנייה"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "מסמך"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "הצגת התו<PERSON><PERSON> העיקרי"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "גו<PERSON>ן"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "תמונה"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "מאינטר<PERSON><PERSON>ציה ועד הצגת התגובה"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "גבוהה"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "נמוכה"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "בינונית"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "השהיה פוטנציאלית מרבית לאחר קלט ראשוני"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "מדיה"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} אלפיות שנייה"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "משאבים אחרים"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "סקריפט"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} שנ'"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "גליון סגנונות"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "צ<PERSON> שלישי"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "סה\"כ"}, "core/lib/lh-error.js | badTraceRecording": {"message": "משהו השת<PERSON><PERSON> בתיעוד המעקב אחרי טעינת הדף. יש להריץ שוב את Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "חלף הזמן הקצוב לתפוגה בהמתנה לחיבור הראשוני של פרוטוקול Debugger."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "לא נאספו צילומי מסך ב-Chrome במהלך טעינת הדף. כדאי לוודא תחילה שיש תוכן גלוי בדף, ורק אז להריץ מחדש את Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "שרתי DNS לא הצליחו לפענח את הדומיין שצוין."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "בפעולת האיסוף של המשאב הנדרש {artifactName} הייתה שגיאה: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "קרתה שגיאה פנימית של Chrome. יש להפעיל מחדש את Chrome ולנסות להריץ שוב את Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "פעולת האיסוף של המשאב הנדרש {artifactName} לא בוצעה."}, "core/lib/lh-error.js | noFcp": {"message": "לא בוצע רינדור של תוכן בדף. יש לוודא שהחלון של הדפדפן נמצא בחזית בזמן הטעינה, ולנסות שוב. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "בדף לא הוצג תוכן שמוגדר כתוכן הכי גדול שהוצג (LCP). יש לוודא שבדף יש רכיב LCP תקין ולנסות שוב. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "הדף שסופק אינו מסוג HTML (הוא הוגדר כסוג ה-MIME הבא: {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "זו גרסה ישנה מידי של Chrome שלא תומכת ב-'{featureName}'. כדי לראות תוצאות מלאות, יש להשתמש בגרסה חדשה יותר."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את כתובת ה-URL שביקשת, כי הדף הפסיק להגיב."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "לכתובת ה-<PERSON><PERSON> שסיפקת אין אישור אבטחה חוקי. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "דפד<PERSON>ן Chrome מנע טעינה של דף והציג מסך מעברון במקומו. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות. (פרטים: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות. (קוד הסטטוס: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "טעינת הדף ארכה זמן רב מדי. מומלץ ליישם את ההמלצות שבדוח כדי לקצר את זמן הטעינה של הדף, ואז לנסות להריץ שוב את Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "משך ההמתנה לפרוטוקול DevTools חרג מהזמן המוקצב. (שיטה: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "אחז<PERSON>ר תוכן של משאבים חרג מהזמן המוקצב"}, "core/lib/lh-error.js | urlInvalid": {"message": "נראה שכתובת ה-<PERSON><PERSON> שסיפקת אינה חוקית."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות. (קוד הסטטוס: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "סוג ה-<PERSON><PERSON> של הדף הוא XHTML: ב-Lighthouse אין תמיכה מפורשת בסוג המסמך הזה"}, "core/user-flow.js | defaultFlowName": {"message": "מסלול המשתמש באתר ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "דו<PERSON> לגבי ניווט ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "דוח תמונת מצב ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "דוח לגבי טווח זמן ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "כל הדוחות"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "קטגוריות"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "נגישות"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "שיטות מומלצות"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "ביצועים"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "אופטימיז<PERSON><PERSON>ה למנועי חיפוש"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "מחשבים"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "הסבר על דוח התהליכים של Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "הסבר על תהליכים"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "שימוש בדוחות ניווט לצורך..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "שימוש בדוחות של תמונת מצב לצורך..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "שימוש בדוחות של טווח זמן לצורך..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "קבלת ציון לגבי ביצועי Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "בדיקת מדדי ביצועים של טעינת דפים כמו Largest Contentful Paint ‏(LCP) ו-Speed Index."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "הערכת יכולות של Progressive Web App."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "איתור של בעיות נגישות באפליקציות שכוללות דף יחיד או בטפסים מורכבים."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "הערכת שיטות מומלצות הקשורות לתפריטים ולרכיבים בממשק המשתמש שמוסתרים עקב ביצוע אינטראקציה."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "מדידה של שינויי פריסה וזמן ריצה של JavaScript במסגרת סדרת אינטראקציות."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "גילוי הזדמנויות הקשורות לביצועים כדי לשפר את חוויית המשתמש בדפים שפתוחים זמן רב ובאפליקציות שכוללות דף יחיד."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "הכי הרבה השפעה"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{ביקורת אינפורמטיבית אחת ({numInformative})}one{{numInformative} ביקורות אינפורמטיביות}two{{numInformative} ביק<PERSON>רות אינפורמטיביות}other{{numInformative} ביקורות אינפורמטיביות}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "ניידים"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "טעינת דף"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "דוחות ניווט מיועדים לניתוח של טעינת דף יחידה, בדיוק כמו דוחות Lighthouse המקוריים."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON> לגבי ניווט"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{דוח אחד ({numNavigation}) לגבי ניווט}one{{numNavigation} דוחות לגבי ניווט}two{{numNavigation} דוחות לגבי ניווט}other{{numNavigation} דוחות לגבי ניווט}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{ביקורת עוברת אחת ({numPassableAudits})}one{{numPassableAudits} ביקורות עוברות}two{{numPassableAudits} ביקורות עוברות}other{{numPassableAudits} ביקורות עוברות}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{ביקורת אחת ({numPassed}) עברה}one{{numPassed} ביקורות עברו}two{{numPassed} ביקורות עברו}other{{numPassed} ביקורות עברו}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "ממוצעת"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "שגיאה"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "גרועה"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "טובה"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "שמירה"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "מצב דף בנקודת זמן"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "דוחות של תמונת מצב מיועדים לניתוח הדף במצב מסוים. לרוב, הניתוח מתבצע לאחר אינטראקציות של משתמשים."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "ד<PERSON><PERSON> תמונת מצב"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{דוח תמונת מצב אחד ({numSnapshot})}one{{numSnapshot} דוחות תמונת מצב}two{{numSnapshot} דוחות תמונת מצב}other{{numSnapshot} דוחות תמונת מצב}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "סיכום"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "אינטרא<PERSON>ציות של משתמשים"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "דוחות של טווח זמן מיועדים לניתוח של משך זמן אקראי, שלרוב מתרחשות בו אינטראקציות של משתמש."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "דוח על טווח זמן"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{דוח אחד ({numTimespan}) על טווח זמן}one{{numTimespan} דוחות על טווח זמן}two{{numTimespan} דוחות על טווח זמן}other{{numTimespan} דוחות על טווח זמן}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "דוח Lighthouse על מסלולי משתמשים בדף"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "להצגת תוכן באנימציה, כשהתוכן לא מוצג במסך כדאי להשתמש ב-[`amp-anim`](https://amp.dev/documentation/components/amp-anim/) כדי לצמצם את השימוש ביחידת העיבוד המרכזית (CPU)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "כדאי להציג את כל רכיבי [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) בפורמטים של WebP, וכן לציין פורמט גיבוי חלופי שמתאים לדפדפנים אחרים. [מידע נוסף](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "יש להקפיד להשתמש ב-[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) לטעינה הדרגתית של תמונות באופן אוטומטי. [מידע נוסף](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "אפשר להשתמש בכלים כמו [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) כדי [לרנדר פריסות AMP בצד השרת](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "יש לעיין ב[תיעוד של AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) כדי לוודא שכל הסגנונות שלך נתמכים."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "הרכיב [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) תומך במאפיין [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) כדי לציין באילו נכסי תמונות להשתמש על סמך גודל המסך. [מידע נוסף](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "אם מתבצע רינדור של רשימות גדולות מאוד, כדאי לשקול גלילה וירטואלית עם ה-Component Dev Kit ‏(CDK). [מידע נוסף](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "אפשר להשתמש ב[פיצול קוד ברמת המסלול](https://web.dev/route-level-code-splitting-in-angular/) כדי לצמצם את גודל חבילות JavaScript. בנוסף, כדאי לשקול העברה מראש למטמון בעזרת [קובץ השירות (service worker) של Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "אם בחרת להשתמש ב-Angular CLI, יש לוודא שגרסאות ה-build נוצרות במצב סביבת הייצור. [מידע נוסף](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "אם בחרת להשתמש ב-Angular CLI, יש לכלול מפות מקור ב-build של סביבת הייצור כדי לבדוק חבילות. [מידע נוסף](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "אפשר לטעון מראש מסלולים כדי להאיץ את הניווט. [מידע נוסף](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "כדאי לשקול להשתמש בכלי העזר `BreakpointObserver` ב-Component Dev Kit ‏(CDK) כדי לנהל נקודות מעבר בין תמונות. [מידע נוסף](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "כד<PERSON>י לשקול העלאה של ה-GIF לשירות שיאפשר להטמיע אותו כסרטון HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "ניתן לציין `@font-display` כשמגדירים גופנים בהתאמה אישית בעיצוב."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "כדאי לשקול להגדיר [פורמטים של תמונות WebP באמצעות סגנון המרת תמונות ](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) באתר."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "כדאי להתקין [מודול של Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) שיכול לטעון תמונות בהדרגה. כדי לשפר ביצועים, מודולים מסוג זה מאפשרים לדחות את טעינת התמונות שאינן מופיעות מייד במסך."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "כדאי להשת<PERSON><PERSON> במודול כדי להטביע נכסים קריטיים של CSS ושל JavaScript, ולהשתמש במאפיין העיכוב בנכסים לא קריטיים של CSS ושל JavaScript."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "עיצובים, מודולים ומפרטי שרתים משפיעים על זמן התגובה של השרת. אפשר להשתמש בעיצוב שעבר אופטימיזציה, לבחור בקפידה מודול לאופטימיזציה ו/או לשדרג את השרת. שרתי האירוח שלך צריכים להשתמש בהעברה למטמון של PHP opcode ושל הזיכרון כדי לצמצם את זמני השאילתות של מסדי נתונים כגון Redis או Memcache, וגם להשתמש בלוגיקה באיכות אופטימלית של האפליקציה כדי להכין דפים במהירות רבה יותר."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "כדאי לשקול שימוש ב[סגנונות התמונות הרספונסיביות](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) כדי לצמצם את הגודל של התמונות שטענת לדף. אם נעשה שימוש בתצוגות להצגת פריטי תוכן מרובים בדף, כדאי לשקול יישום עימוד כדי להגביל את מספר פריטי התוכן המופיעים בכל דף נתון."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "צריך לוודא שהאפשרות Aggregate CSS files (קובצי ה-CSS המצטברים) מופעלת בדף Administration (ניהול) » Configuration (הגדרות אישיות) » Development (פיתוח).  לתמיכה משופרת בצבירת נכסים, צריך לוודא שבאתר של Drupal מותקנת גרסה 10.1 Drupal לפחות, או גרסה עדכנית יותר."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "צריך לוודא שהאפשרות Aggregate JavaScript files (קובצי ה-JavaScript המצטברים) מופעלת בדף Administration (ניהול) » Configuration (הגדרות אישיות) » Development (פיתוח).  לתמיכה משופרת בצבירת נכסים, צריך לוודא שבאתר של Drupal מותקנת גרסה 10.1 Drupal לפחות, או גרסה עדכנית יותר."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "כדאי לשקול הסרה של כללי CSS שאינם בשימוש, ולצרף לדף הרלוונטי או לרכיב בדף רק את ספריות ה-Drupal הנדרשות. לפרטים נוספים, ניתן לעיין ב[קישור לתיעוד של Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). כדי לזהות ספריות מצורפות שמוסיפות תוכן CSS מיותר, כדאי להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. לאחר השבתה של צבירת CSS באתר ה-Drupal שלך, ניתן יהיה לזהות את העיצוב/המודול שאחראים להוספת גיליון הסגנונות. העיצובים/המודולים הבעייתיים הם אלה שברשימת גיליונות הסגנונות שלהם יש כמות גדולה של כיסוי קוד באדום. עיצוב/מודול צריך להכניס גיליון סגנונות לתור רק אם נעשה בו שימוש בפועל בדף."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "כדאי לשקול הסרה של נכסי JavaScript שאינם בשימוש, וצירוף של ספריות ה-Drupal הנדרשות בלבד לדף הרלוונטי או לרכיב בדף. לפרטים נוספים, ניתן לעיין ב[קישור לתיעוד של Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). כדי לזהות ספריות מצורפות שמוסיפות תוכן JavaScript מיותר, כדאי להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. לאחר השבתה של צבירת JavaScript באתר ה-Drupal שלך, ניתן יהיה לזהות את העיצוב/המודול שאחראים להוספת הסקריפט. העיצובים/המודולים הבעייתיים הם אלה שברשימת הסקריפטים שלהם יש כמות גדולה של כיסוי קוד באדום. עיצוב/מודול צריך להכניס סקריפט לתור רק אם נעשה בו שימוש בפועל בדף."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "בדף \"ניהול » הגדרה » פיתוח\" יש להגדיר \"גיל מרבי של דפדפן ומטמון של שרת proxy\". למידע על [מטמון Drupal ואופטימיזציה של ביצועים](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "כדאי לשקול שימוש ב[מודול](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) שמבצע אופטימיזציה ומצמצם את גודל התמונות שהעלית לאתר באופן אוטומטי, בלי לפגוע באיכות שלהן. כמו כן, יש לוודא שנעשה שימוש ב[סגנונות המותאמים של התמונות הרספונסיביות](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) שסופקו על ידי Drupal (זמינים ב-Drupal בגרסה 8 ואילך)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "ניתן להוסיף רמזים של משאב חיבור מראש או שליפה מראש של DNS באמצעות התקנה והגדרה של [מודול](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) שמוסיף מתקנים לרמזים של משאב סוכן משתמש."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "יש לוודא שנעשה שימוש ב[סגנונות המותאמים לתמונות הרספונסיביות](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) שסופקו על ידי Drupal (זמ<PERSON><PERSON>ים ב-Drupal בגרסה 8 ואילך). ניתן להשתמש בסגנונות התמונות הרספונסיביות כשמתבצע רינדור של שדות תמונה במצב תצוגה, בתצוגות, או בתמונות שהועלו באמצעות עורך WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Optimize Fonts` כדי להשתמש באופן אוטומטי בתכונת ה-CSS של `font-display` ולוודא שהטקסט גלוי למשתמש במהלך הטעינה של פונטים מסוג webfont."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "שימוש ב-[<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) והפעלת `Next-Gen Formats` כדי להמיר תמונות ל-WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Lazy Load Images` לדחיית הטעינה של תמונות מחוץ למסך עד שהן נחוצות."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Critical CSS` וגם `<PERSON>ript Delay` לדחיית תוכן JS/CSS שאינו קריטי."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "שימוש ב-[Ezoic Cloud Caching‏](https://pubdash.ezoic.com/speed/caching) כדי לשמור את התוכן שלך ברחבי רשת האינטרנט במטמון, לשיפור ה'זמן עד בייט ראשון'."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Minify CSS` כדי למזער באופן אוטומטי את ה-CSS ולהפחית את הגדלים של המטען הייעודי (payload) של הרשת."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Minify Javascript` כדי למזער באופן אוטומטי את ה-JS ולהפחית את גודלי המטען הייעודי (payload) של הרשת."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "שימוש ב-[<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) והפעלת `Remove Unused CSS` לעזרה בנושא הזה. יתבצע זיהוי של מחלקות CSS שנמצאות בשימוש בפועל בכל דף באתר ושאר המחלקות יוסרו כדי שהקובץ יהיה בגודל קטן."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Efficient Static Cache Policy` כדי להגדיר ערכים מומלצים בכותרת השמירה במטמון לנכסים סטטיים."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "שימוש ב-[<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) והפעלת `Next-Gen Formats` כדי להמיר תמונות ל-WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "שימוש ב-[<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) והפעלת `Pre-Connect Origins` כדי להוסיף אוטומטית רמזים למשאב `preconnect` לצורך ביסוס חיבורים מוקדמים למקורות חשובים של צד שלישי."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Preload Fonts` וגם `Preload Background Images` כדי להוסיף קישורים של `preload` לצורך תעדוף של אחזור משאבים שנדרשים בשלב מאוחר יותר של טעינת הדף."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "שימוש ב-[<PERSON><PERSON> Leap‏](https://pubdash.ezoic.com/speed) והפעלת `Resize Images` כדי לשנות את הגודל של התמונות כך שיתאים למכשיר, תוך הפחתת גודלי המטען הייעודי (payload) של הרשת."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "כדי לבצע אופטימיזציה אוטומטית של פורמט התמונות, עליך להשתמש ברכיב `gatsby-plugin-image` במקום ברכיב `<img>`. [מידע נוסף](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "כדי לבצע טעינה מדורגת של תמונות, עליך להשתמש ברכיב `gatsby-plugin-image` במקום ברכיב `<img>`. [מידע נוסף](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "עליך להשתמש ברכיב `gatsby-plugin-image` ולהגדיר את הערך `eager` במאפיין `loading`. [מידע נוסף](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "יש להשתמש ברכיב `Gatsby Script API` כדי לדחות טעינה של סקריפטים שאינם קריטיים של צדדים שלישיים. [מידע נוסף](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "יש להשתמש בפלאג<PERSON><PERSON> `PurgeCSS` `Gatsby` כדי להסיר כללים שלא נמצאים בשימוש מגיליונות של סגנונות. [מידע נוסף](https://purgecss.com/plugins/gatsby.html)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "יש להשתמש בכלי `Webpack Bundle Analyzer` כדי לזהות קוד JavaScript שלא נמצא בשימוש. [מידע נוסף](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "עליך להגדיר שמירה במטמון לנכסים שלא משתנים. [מידע נוסף](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "כדי לשנות את איכות התמונה, יש להשתמש ברכיב `gatsby-plugin-image` במקום ברכיב `<img>`. [מידע נוסף](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "עליך להשתמש ברכיב `gatsby-plugin-image` כדי לבצע הגדרה מתאימה של `sizes`. [מידע נוסף](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "כד<PERSON>י לשקול העלאה של ה-GIF לשירות שיאפשר להטמיע אותו כסרטון HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "כדאי לשקול להשתמש ב[פלאגי<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) או בשירות שימירו את התמונות שהועלו לפורמטים האופטימליים באופן אוטומטי."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "ניתן להתקין [פלאגין של Jo<PERSON>la לטעינה מדורגת](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) שמאפשר לדחות את הטעינה של תמונות שאינן מופיעות מיד במסך. ניתן גם לעבור לתבנית שמספקת את הפונקציונליות הזו. בגרסה 4.0 ואילך של Joomla, כל התמונות החדשות יקבלו את המאפיין `loading` [באו<PERSON><PERSON> אוטומטי](https://github.com/joomla/joomla-cms/pull/30748) מהליבה."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "יש כמה יישומי פלאגין של Joomla שיכולים לעזור לך [להטביע נכסים קריטיים](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) או [לדחות טעינה של משאבים פחות חשובים](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). חשוב: ייתכן שהאופטימיזציות המבוצעות על ידי יישומי הפלאגין האלה יגרמו לתקלות בתכונות של התבניות או יישומי הפלאגין האחרים, ולכן יהיה צורך לבדוק אותם באופן יסודי."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "עיצובים, תוספים ומפרטי שרתים משפיעים על זמן התגובה של השרת. אפשר להשתמש בתבנית שעברה אופטימיזציה, לבחור בקפידה תוסף לאופטימיזציה ו/או לשדרג את השרת."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "כדאי לשקול להציג קטעים בקטגוריות המאמרים (למשל, בעזרת קישור 'למידע נוסף'), לצמצם את מספר המאמרים המוצגים בדף נתון, לחלק פוסטים ארוכים למספר דפים או להשתמש בפלאגין כדי לטעון תגובות בצורה מדורגת."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "יש כמה [תוס<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) שיכולים להאיץ את האתר בעזרת שרשור, הקטנה ודחיסה של סגנונות CSS. קיימות גם תבניות עם הפונקציונליות הזו."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "יש כמה [תוס<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) שיכולים להאיץ את האתר בעזרת שרשור, הקטנה ודחיסה של סקריפטים. קיימות גם תבניות עם הפונקציונליות הזו."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "כדאי לשקול לצמצם את המספר של [תו<PERSON><PERSON><PERSON>](https://extensions.joomla.org/) שטוענים תוכן CSS שאינו בשימוש בדף שלך. כדי לזהות תוספים שמוסיפים תוכן CSS מיותר, א<PERSON><PERSON><PERSON> להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. ניתן לזהות את העיצוב/הפלאגין שאחראים להוספת התוכן המיותר לפי כתובת ה-URL שבגיליון הסגנונות. יישומי הפלאגין הבעייתיים הם אלה שברשימת גיליונות הסגנונות שלהם יש כמות גדולה של כיסוי קוד באדום. פלאגין צריך להכניס גיליון סגנונות לתור רק אם נעשה בו שימוש בדף."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "כדאי לשקול לצמצם את המספר של [תוס<PERSON><PERSON>](https://extensions.joomla.org/) שטוענים תוכן JavaScript שאינו בשימוש בדף שלך. כדי לזהות יישומי פלאגין שמוסיפים תוכן JavaScript מיותר, אפשר להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. ניתן לזהות את התוסף שאחראי לתוכן המיותר לפי כתובת ה-URL שבסקריפט. התוספים הבעייתיים הם אלה שברשימת הסקריפטים שלהם יש כמות גדולה של כיסוי קוד באדום. תוסף צריך להכניס סקריפט לתור רק אם נעשה בו שימוש בפועל בדף."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "למידע על [שמירה במטמון הדפדפן ב-<PERSON><PERSON><PERSON>](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "כדאי לשקול להשתמש ב[פלאגין לאופטימיזציית תמונות](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) שדוחס את התמונות בלי לפגוע באיכות שלהן."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "כדאי לשקול שימוש ב[פלאגין לתמונות רספונסיביות](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) לצורך הוספת תמונות רספונסיביות לתוכן."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "ניתן להפעיל דחיסת טקסט באמצעות הפעלת שיטת Gzip לדחיסת נתונים בדף במערכת Joomla (מערכת > הגדרה גלובלית > שרת)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "אם בחרת לא לקבץ את נכסי JavaScript בחבילות, כדאי לשקול שימוש ב[כלי ליצירת חבילות](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "אפשר להשבית את [יצירת והקטנת חבילות ה-JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) המובנית ב-<PERSON><PERSON><PERSON>, ולשקול להשתמש במקום זאת ב[כלי ליצירת חבילות](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "אפ<PERSON>ר לציין `@font-display` כש[מגדירים גופנים בהתאמה אישית](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "ב-[Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) אפשר למצוא מגוון תוספים של צד שלישי שבעזרתם ניתן להשתמש בפורמטים חדשים של תמונות, וכדאי לשקול לבצע בו חיפוש."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "כדאי לשקול את שינוי המוצר ותבניות הקטלוג כך שישתמשו בתכונת ה[טעינה המדורגת](https://web.dev/native-lazy-loading) של פלטפורמת האינטרנט."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "אפשר להשתמש ב[שילו<PERSON> Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) של Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "ניתן להפעיל את האפשרות \"הקטנת קובצי CSS\" בהגדרות המפתח של החנות. [מידע נוסף](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "ניתן להשתמש ב-[Terser](https://www.npmjs.com/package/terser) כדי להקטין את כל נכסי JavaScript מפריסת התוכן הסטטית ולהשבית את תכונת ההקטנה המובנית."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "השבתה של [יצירת חבילות JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) המובנית ב-Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "ב-[Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) אפשר למצוא מגוון תוספים של צד שלישי לאופטימיזציה של תמונות, וכד<PERSON><PERSON> לשקול לבצע בו חיפוש."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "על-ידי [שינוי פריסה של עיצוב](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) אפשר להוסיף רמזים של משאבי חיבור מראש או אחזור מראש של DNS."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "אפשר להוסיף תגי `<link rel=preload>` על-ידי [שינוי פריסה של עיצוב](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "כדי לבצע אופטימיזציה אוטומטית של פורמט התמונות, עליך להשתמש ברכיב `next/image` במקום ברכיב `<img>`. [מידע נוסף](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "כדי לבצע טעינה מדורגת של תמונות באופן אוטומטי, עליך להשתמש ברכיב `next/image` במקום ברכיב `<img>`. [מידע נוסף](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "שימוש ברכיב `next/image` והגדרת ה'עדיפות' כ-true כדי לטעון מראש תמונה מסוג LCP. [מידע נוסף](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "עליך להשתמש ברכיב `next/script` כדי לדחות טעינה של סקריפטים שאינם קריטיים של צדדים שלישיים. [מידע נוסף](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "עליך להשתמש ברכיב `next/image` כדי לוודא שהתמונות תמיד יופיעו בגודל הנכון. [מידע נוסף](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "כדאי לשקול להגדיר את `PurgeCSS` בהגדרות האישיות`Next.js`, כדי להסיר כללים שלא נמצאים בשימוש מגיליונות של סגנונות. [מידע נוסף](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "יש להשתמש בכלי `Webpack Bundle Analyzer` כדי לזהות קוד JavaScript שלא נמצא בשימוש. [מידע נוסף](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "כדאי להשתמש ב-`Next.js Analytics` כדי למדוד את ביצועי האפליקציה שלך בפועל. [מידע נוסף](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "צריך להגדיר שמירה במטמון לנכסים ולדפי `Server-side Rendered`‏ (SSR) שאינם משתנים. [מידע נוסף](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "כדי לשנות את איכות התמונה, יש להשתמש ברכיב `next/image` במקום ברכיב `<img>`. [מידע נוסף](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "עליך להשתמש ברכיב `next/image` כדי לבצע הגדרה מתאימה של `sizes`. [מידע נוסף](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "עליך להפעיל דחיסת נתונים בשרת Next.js. [למידע נוסף](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "צריך לפנות למנהל החשבון כדי להפעיל את [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). כשמגדירים את התכונה הזו, ניתנת עדיפות לביצועים של עיבוד דפים ומתבצעת אופטימיזציה שלהם."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "שימוש באפשרות [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) ב-NitroPack כדי להגדיר ערך רצוי לכלל ההצגה של גופנים ב-CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "שימוש בתכונה [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) כדי להמיר את התמונות ל-WebP באופן אוטומטי."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "הפעלת [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images) לעיכוב הטעינה של תמונות שלא מופיעות במסך."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "הפעלת [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) ב-NitroPack — לקיצור זמני הטעינה הראשונית."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "הפעלת [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361) לשיפור זמן התגובה של השרת ולאופטימיזציה של תפיסת הביצועים."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "הפעלת [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) בהגדרות השמירה במטמון כדי להקטין את הגודל של קובצי ה-CSS, ה-HTML וה-JavaScript — לקיצור זמני הטעינה."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "הפעלת [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) בהגדרות השמירה במטמון כדי להקטין את הגודל של קובצי ה-JS, ה-HTML וה-CSS — לקיצור זמני הטעינה."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "הפעלת [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) כדי להסיר את כללי ה-CSS שלא רלוונטיים לדף הזה."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "הגדרת [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) ב-NitroPack כדי לעכב את הטעינה של סקריפטים עד שהם נחוצים."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "כדי לשפר את זמני הטעינה ואת חוויית המשתמש, אפשר לעבור לתכונה [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) בתפריט `Caching` ולשנות את פרק הזמן לתפוגת השמירה במטמון של דפים."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "הפעלת ההגדרה [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack) כדי לדחוס את התמונות, לבצע אופטימיזציה שלהן ולהמיר אותן ל-WebP באופן אוטומטי."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "הפעלת [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) כדי לבצע אופטימיזציה מראש לתמונות כך שיתאימו למידות של המאגרים שבהם הן מוצגות בכל המכשירים."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "שימוש בתכונה [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) ב-NitroPack כדי להקטין את גודל הקבצים שנשלחים לדפדפן."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "עליך להשתמש ברכיב `nuxt/image` ולהגדיר את `format=\"webp\"`. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "עליך להשתמש ברכיב `nuxt/image` ולהגדיר את `loading=\"lazy\"` עבור תמונות שאינן מופיעות במסך. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "עליך להשתמש ברכיב `nuxt/image` ולציין את הרכיב `preload` לתמונות מסוג LCP. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "עליך להשתמש ברכיב `nuxt/image` ולציין את הרכיב המפורש `width` ואת `height`. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "עליך להשתמש ברכיב `nuxt/image` ולהגדיר את רכיב `quality` המתאים. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "עליך להשתמש ברכיב `nuxt/image` ולהגדיר את רכיב `sizes` המתאים. [מידע נוסף](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[כדאי להחליף אנימציות GIF בסרטונים](https://web.dev/replace-gifs-with-videos/) כדי להאיץ את הטעינה של דפי אינטרנט. מומלץ גם להשתמש בפורמטים מודרניים של קבצים כמו [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) או [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) כדי לייעל את דחיסת הנתונים ביותר מ-30% עם קידוד הווידאו המתקדם ביותר, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "כדאי לשקול להשתמש ב[פלאגין](https://octobercms.com/plugins?search=image) או בשירות שימירו את התמונות שהועלו לפורמטים האופטימליים באופן אוטומטי. הגודל של [תמונות WebP ללא אובדן נתונים](https://developers.google.com/speed/webp) קטן ב-26% ביחס לתמונות בפורמט PNG וקטן ב-25-34% ביחס לתמונות JPEG דומות באינדקס המתאים של ציוני איכות SSIM. אפשר גם להשתמש בפורמט תמונות נוסף מהדור הבא, [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "כדאי לשקול להתקין [פלאגין לטעינה מדורגת של תמונות](https://octobercms.com/plugins?search=lazy) שמאפשר לדחות את הטעינה של תמונות שאינן מופיעות מיד במסך. ניתן גם לעבור לעיצוב שמספק את הפונקציונליות הזו. אפשרות נוספת היא להשתמש ב[פלאגין של AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "יש הרבה יישומי פלאגין שעוזרים [להטביע נכסים קריטיים](https://octobercms.com/plugins?search=css). יישומי הפלאגין האלה עשויים לגרום לתקלות ביישומי פלאגין אחרים, לכן כדאי לבדוק אותם באופן יסודי."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "עיצובים, יישו<PERSON>י פלאגין ומפרטי שרתים משפיעים על זמן התגובה של השרת. אפשר להשתמש בעיצוב שעבר אופטימיזציה, לבחור בקפידה פלאגין לאופטימיזציה ו/או לשדרג את השרת. מערכת ניהול התוכן October גם מאפשרת למפתחים להשתמש ב[`Queues`](https://octobercms.com/docs/services/queues) כדי לדחות את העיבוד של משימה שגוזלת זמן רב, כמו שליחת אימייל. אפשרות זו מאיצה בקשות אינטרנט באופן משמעותי."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "אפשר להציג קטעים ברשימות הפוסטים (למשל, עם לחצן `show more`), לצמצם את מספר הפוסטים המוצגים בדף נתון, לחלק פוסטים ארוכים למספר דפים או להשתמש בפלאגין כדי לטעון תגובות בצורה מדורגת."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "יש הרבה [יישו<PERSON><PERSON> פלאגין](https://octobercms.com/plugins?search=css) שיכולים להאיץ את האתר בעזרת שרשור, הקטנה ודחיסה של סגנונות. ניתן להאיץ את הפיתוח בעזרת הקטנה מראש בתהליך build."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "יש הרבה [יישו<PERSON><PERSON> פלאגין](https://octobercms.com/plugins?search=javascript) שיכולים להאיץ את האתר בעזרת שרשור, הקטנה ודחיסה של סקריפטים. ניתן להאיץ את הפיתוח בעזרת הקטנה מראש בתהליך build."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "כדאי לעיין ב[יישומי פלאגין](https://octobercms.com/plugins) שטוענים תוכן CSS שאינו בשימוש בדף האינטרנט. כדי לזהות יישומי פלאגין שמוסיפים תוכן CSS מיותר, יש להפעיל את [רמת הכיסוי של הקוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. זיהוי העיצוב/הפלאגין שאחראים להוספת התוכן המיותר לפי כתובת ה-URL שבגיליון הסגנונות. יש לשים לב ליישומי פלאגין שיש להם הרבה גיליונות סגנונות שכמות גדולה של רמת כיסוי הקוד שלהם מופיעה באדום. פלאגין צריך להוסיף גיליון סגנונות רק אם נעשה בו שימוש בדף האינטרנט."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "כדאי לעיין [ביישומי פלאגין](https://octobercms.com/plugins?search=javascript) שטוענים תוכן JavaScript שאינו בשימוש בדף האינטרנט. כדי לזהות יישומי פלאגין שמוסיפים תוכן JavaScript מיותר, יש להפעיל את [רמת הכיסוי של הקוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. זיהוי העיצוב/הפלאגין שאחראים להוספת התוכן המיותר לפי כתובת ה-URL שבסקריפט. יש לשים לב ליישומי פלאגין שכמות גדולה של רמת כיסוי הקוד שלהם מופיעה באדום. פלאגין צריך להוסיף סקריפט רק אם נעשה בו שימוש בדף האינטרנט."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "אפשר לקרוא על [מניעת בקשות מיותרות ברשת באמצעות מטמון HTTP](https://web.dev/http-cache/#caching-checklist). יש הרבה [יישומי פלאגין](https://octobercms.com/plugins?search=Caching) שיכולים להאיץ את השמירה במטמון."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "כדאי לשקול להשתמש ב[פלאגין לאופטימיזציית תמונות](https://octobercms.com/plugins?search=image) כדי לדחוס את תמונות בלי לפגוע באיכות שלהן."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "העלאה של תמונות באופן ישיר למנהל המדיה תאפשר לך לוודא שהתמונות זמינות בגודל הדרוש. כדאי לשקול להשתמש ב[מסנן לשינוי גודל](https://octobercms.com/docs/markup/filter-resize) או ב[פלאגין לשינוי גודל תמונות](https://octobercms.com/plugins?search=image) כדי לוודא שהגודל של התמונות אופטימלי."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "יש להפעיל דחיסת טקסט בהגדרות של שרת האינטרנט."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "כדאי לשקול להשתמש בספריית \"windowing\", כמו `react-window`, כדי לצמצם את מספר צומתי DOM שנוצרים כשמתבצע רינדור של רכיבים רבים שחוזרים בדף. [למידע נוסף](https://web.dev/virtualize-long-lists-react-window/). כמו כן, אם בחרת להשתמש ב-hook של `Effect` כדי לשפר את הביצועים בזמן ריצה, יש להשתמש ב-[`shouldComponentUpdate`,‏ ](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) או ב-[`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) ו[לוותר על אפקטים](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), כדי לצמצם רינדור מחדש שאינו נחוץ עד לשינוי של יחסי תלות מסוימים בלבד."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "אם בחרת להשתמש ב-React Router, יש לצמצם את השימוש ברכיב `<Redirect>` בשביל [ניווט במסלול](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "אם מתבצע רינדור בצד השרת של רכיבי React כלשהם, כדאי לשקול להשתמש ב-`renderToPipeableStream()` או `renderToStaticNodeStream()` כדי לאפשר ללקוח לקבל ולמלא חלקים שונים של הסימון, במקום את הכול בבת אחת. [מידע נוסף](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "אם מערכת ה-build שלך מקטינה את קובצי ה-CSS באופן אוטומטי, יש להקפיד לפרוס את גרסת ה-build של סביבת הייצור של האפליקציה. א<PERSON><PERSON>ר לבדוק זאת עם תוסף הכלים למפתחים של React. [מידע נוסף](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "אם מערכת ה-build שלך מקטינה באופן אוטומטי את קובצי ה-JS, יש להקפיד לפרוס את גרסת ה-build של סביבת הייצור של האפליקציה. א<PERSON><PERSON>ר לבדוק זאת עם תוסף הכלים למפתחים של React. [מידע נוסף](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "אם לא מתבצע רינדור בצד השרת, [יש לפצל חבילות JavaScript](https://web.dev/code-splitting-suspense/) עם `React.lazy()`. אפשרות אחרת היא לפצל את הקוד בעזרת ספרייה של צד שלישי, כמו [רכיבים שניתן לטעון](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "כדי למדוד את ביצועי הרינדור של הרכיבים, אפשר להיעזר ב-React DevTools Profiler, שמשתמש ב-Profiler API. [מידע נוסף.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "צריך למקם סרטונים בתוך `VideoBoxes`, להתאים אותם אישית באמצעות `Video Masks` או להוסיף `Transparent Videos`. [מידע נוסף](https://support.wix.com/en/article/wix-video-about-wix-video)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "אפשר להעלות תמונות באמצעות `Wix Media Manager` כדי שהן יוצגו בפורמט WebP באופן אוטומטי. כדאי לבדוק [דרכים נוספות לאופטימיזציה](https://support.wix.com/en/article/site-performance-optimizing-your-media) של המדיה באתר."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "כש[מוסיפים קוד מצד שלישי](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) בכרטיסייה `Custom Code` שבמרכז הבקרה של האתר, ח<PERSON><PERSON><PERSON> לוודא שהוא נדחה או נטען בסוף גוף הקוד. אם אפשר, כדאי להשתמש ב[שילובים](https://support.wix.com/en/article/about-marketing-integrations) של Wix להטמעת כלי שיווק באתר. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "ב-Wix נעשה שימוש ברשתות CDN ובשמירה במטמון כדי שזמן התגובה יהיה קצר ככל שניתן עבור רוב המבקרים. כדאי [להפעיל שמירה במטמון באופן ידני](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) באתר, במיוחד אם משתמשים ב-`Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "עליך לבדוק את כל הקודים שהוספת לאתר מצדדים שלישיים בכרטיסייה `Custom Code` שבמרכז הבקרה של האתר. חשוב לשמור רק את השירותים הנחוצים לאתר. [מידע נוסף](https://support.wix.com/en/article/site-performance-removing-unused-javascript)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "כד<PERSON>י לשקול העלאה של ה-GIF לשירות שיאפשר להטמיע אותו כסרטון HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "כדאי להשתמש בפלאגין [Performance Lab](https://wordpress.org/plugins/performance-lab/) כדי להמיר באופן אוטומטי את תמונות ה-JPEG שהעלית לפורמט WebP, במקרים שבהם הוא נתמך."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "ניתן להתקין [פלאגין של WordPress לטעינה מדורגת](https://wordpress.org/plugins/search/lazy+load/) שמאפשר לדחות את הטעינה של רכיבים שאינם מופיעים מיד במסך. ניתן גם להשתמש בעיצוב שמספק את האפשרות הזו. אפשרות נוספת היא להשתמש ב[פלאגין של AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "יש כמה יישומי פלאגין של WordPress שיכולים לעזור לך [להטביע נכסים קריטיים](https://wordpress.org/plugins/search/critical+css/) או [לדחות טעינה של משאבים פחות חשובים](https://wordpress.org/plugins/search/defer+css+javascript/). חשוב: ייתכן שהאופטימיזציות המבוצעות על ידי יישומי הפלאגין האלה יגרמו לתקלות בתכונות של העיצוב או יישומי הפלאגין האחרים, ולכן כנראה שיהיה צורך לבצע שינויים בקוד."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "עיצובים, יישו<PERSON><PERSON> פלאגין ומפרטי שרתים משפיעים על זמן התגובה של השרת. אפשר להשתמש בעיצוב שעבר אופטימיזציה, לבחור בקפידה פלאגין לאופטימיזציה ו/או לשדרג את השרת."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "אפשר להציג קטעים ברשימות הפוסטים (למשל, עם תג 'עוד'), לצמצם את מספר הפוסטים המוצגים בדף נתון, לחלק פוסטים ארוכים למספר דפים או להשתמש בפלאגין כדי לטעון תגובות בצורה מדורגת."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "יש כמה [יישומי פלאגין של WordPress](https://wordpress.org/plugins/search/minify+css/) שיכולים להאיץ את האתר על ידי שרשור, הקטנה ודחיסה של סגנונות. ניתן גם להשתמש בתהליך build כדי לבצע את ההקטנה מראש, אם אפשר."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "יש כמה [יישומי פלאגין של WordPress](https://wordpress.org/plugins/search/minify+javascript/) שיכולים להאיץ את האתר על ידי שרשור, הקטנה ודחיסה של סקריפטים. ניתן גם להשתמש בתהליך build כדי לבצע את ההקטנה מראש, אם אפשר."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "כדאי לשקול לצמצם את מספר [יישומי הפלאגין של WordPress](https://wordpress.org/plugins/) שטוענים תוכן CSS בלתי נחוץ בדף, או להשבית אותם. כדי לזהות יישומי פלאגין שמוסיפים תוכן CSS מיותר, אפשר להפעיל [כיסוי קוד](https://developer.chrome.com/docs/devtools/coverage/) ב-Chrome DevTools. לפי כתובת ה-URL של גיליון הסגנונות, ניתן לזהות את העיצוב/הפלאגין שאחראים להוספת התוכן. יישומי הפלאגין הבעייתיים הם אלה שברשימת גיליונות הסגנונות שלהם יש כמות גדולה של כיסוי קוד באדום. פלאגין צריך להכניס גיליון סגנונות לתור רק אם נעשה בו שימוש בדף."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "כדאי לשקול לצמצם את מספר [יישומי הפלאגין של WordPress](https://wordpress.org/plugins/) שטוענים תוכן JavaScript בלתי נחוץ בדף, או להשבית אותם. כדי לזהות יישומי פלאגין שמוסיפים תוכן JS מיותר, אפשר להפעיל [כיסוי קוד](https://developer.chrome.com/docs/devtools/coverage/) ב-Chrome DevTools. לפי כתובת ה-URL של הסקריפט, ניתן לזהות את העיצוב/הפלאגין שאחראים להוספת התוכן. יישומי הפלאגין הבעייתיים הם אלה שברשימת הסקריפטים שלהם יש כמות גדולה של כיסוי קוד באדום. פלאגין צריך להכניס סקריפט לתור רק אם נעשה בו שימוש בדף."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "למידע על [שמירה במטמון הדפדפן ב-WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "כדאי לשקול להשתמש ב[פלאגין של WordPress לאופטימיזציית תמונות](https://wordpress.org/plugins/search/optimize+images/) שדוחס את התמונות בלי לפגוע באיכות שלהן."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "העלאה של תמונות באופן ישיר דרך [ספריית המדיה](https://wordpress.org/support/article/media-library-screen/) תאפשר לך לוודא שהתמונות זמינות במידות הדרושות. אחר כך אפשר להוסיף אותן מספריית המדיה או להשתמש בווידג'ט התמונות כדי לוודא שנעשה שימוש בתמונות במידות האופטימליות (כולל תמונות בשביל נקודות מעבר רספונסיביות). יש להימנע משימוש בתמונות ב`Full Size`, אלא אם המימדים מתאימים לשימוש שנעשה בהן. [מידע נוסף](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "אפשר להפעיל דחיסת טקסט בהגדרות שרת האינטרנט."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "כדי להמיר את התמונות לפורמט WebP, צריך להפעיל את האפשרות Imagify בכרטיסייה Image Optimization (אופטימיזציה של תמונות) ב-'WP Rocket'."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "כדי לתקן את הבעיה לפי ההמלצה, צריך להפעיל את התכונה [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) ב-'WP Rocket'. התכונה הזו תשהה את טעינת התמונות עד שהמבקר יגלול למטה ובאמת יצטרך לראות אותן."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "כדי לפעול לפי ההמלצה הזו, צריך להפעיל את התכונה [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (הסרת קובצי CSS שאינם בשימוש) ואת התכונה [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (טעינת JavaScript נדחתה) ב-'WP Rocket'. התכונות האלה יבצעו אופטימיזציה של קובצי ה-CSS וה-JavaScript בהתאמה, כך שהם לא יחסמו את הרינדור של הדף."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "כדי לפתור את הבעיה, צריך להפעיל את התכונה [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (הקטנת קובצי CSS) ב-'WP Rocket'. המערכת תסיר רווחים ותגובות מקובצי ה-CSS באתר כדי שהקבצים יהיו קטנים יותר וההורדה שלהם תהיה מהירה יותר."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "כדי לפתור את הבעיה, צריך להפעיל את התכונה [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (הקטנת קובצי JavaScript) ב-'WP Rocket'. המערכת תסיר תגובות ורווחים ריקים מקובצי JavaScript כדי שהקבצים יהיו קטנים יותר וההורדה שלהם תהיה מהירה יותר."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "כדי לפתור את הבעיה, צריך להפעיל את התכונה [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (הסרת קובצי CSS שאינם בשימוש) ב-'WP Rocket'. התכונה הזו מסירה את כל קובצי ה-CSS וגיליונות הסגנונות שאינם בשימוש, ומשאירה רק את קובצי ה-CSS שבשימוש בכל דף. כך היא מצמצמת את גודל הדף."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "כדי לפתור את הבעיה, צריך להפעיל את התכונה [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (ביצוע של השהיית JavaScript) ב-'WP Rocket'. התכונה הזו תשפר את טעינת הדף על ידי השהיה של ביצוע הסקריפטים עד לאינטראקציה מצד המשתמש. אם יש באתר מסגרות iframe, אפשר גם להשתמש בתכונה [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) ‏(LazyLoad למסגרות iframe ולסרטונים) ובתכונה [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)(החלפת iframe של YouTube בתמונה של תצוגה מקדימה) של 'WP Rocket'."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "כדי לדחוס את התמונות, צריך להפעיל את האפשרות Imagify בכרטיסייה Image Optimization (אופטימיזציה של תמונות) ב-'WP Rocket' ולהריץ את האפשרות Bulk Optimization (אופטימיזציה בכמות גדולה)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "אפשר להשתמש בתכונה [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (שליפה מראש של בקשות DNS) ב-'WP Rocket' כדי להוסיף את האפשרות 'dns-prefetch' ולזרז את החיבור עם דומיינים חיצוניים. כמו כן, האפשרות 'preconnect' מתווספת באופן אוטומטי על ידי 'WP Rocket' אל [דומיין Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) ולכל CNAME שנוסף באמצעות התכונה [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (הפעלת CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "כדי לפתור את הבעיה בגופנים, צריך להפעיל את התכונה [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (הסרת קובצי CSS שאינם בשימוש) ב-'WP Rocket'. הגופנים הקריטיים של האתר ייטענו מראש בעדיפות גבוהה."}, "report/renderer/report-utils.js | calculatorLink": {"message": "להצגת המחשבון."}, "report/renderer/report-utils.js | collapseView": {"message": "כיווץ התצוגה"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "ניווט התחלתי"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "ז<PERSON><PERSON> אחז<PERSON><PERSON> מקסימלי של נתיב קריטי:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "העתקת ה-JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "מעבר לעיצוב כהה"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "פרטי הדפסה מורחבים"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "סיכום הדפסות"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "שמירה כ-Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "שמירה כ-HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "שמירה כ-JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "הצגת נתוני המעקב המקוריים"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "פתיחה ב-Viewer"}, "report/renderer/report-utils.js | errorLabel": {"message": "שגיאה!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "שגיאה בדוח: אין מידע על הבדיקה"}, "report/renderer/report-utils.js | expandView": {"message": "הרחבת התצוגה"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "צ<PERSON> ראשון"}, "report/renderer/report-utils.js | footerIssue": {"message": "דיווח על בעיה"}, "report/renderer/report-utils.js | hide": {"message": "הסתרה"}, "report/renderer/report-utils.js | labDataTitle": {"message": "נתוני בדיקה"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "ניתוח [Lighthouse](https://developers.google.com/web/tools/lighthouse/) של הדף הנוכחי באמולציה של רשת סלולרית. הערכים מהווים אומדן והם עשויים להשתנות."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "פריטים נוספים שיש לבדוק באופן ידני"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "לא רלוונטי"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "פתיחה בכרטיסייה חדשה"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "הזדמנות"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "או<PERSON><PERSON><PERSON> חי<PERSON>ון"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "בדיקות עם ציון 'עובר'"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "בהתאם [לקריטריונים המעודכנים של Chrome לגבי אפשרות ההתקנה](https://developer.chrome.com/blog/update-install-criteria), הקטג<PERSON>ריה PWA תצא משימוש בגרסה עתידית במסגרת Lighthouse. אפשר לעיין ב[מסמכי התיעוד המעודכנים בנושא PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/) לצורך בדיקות עתידיות של PWA."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "טעינת הדף הראשונית"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "תמונת מצב בנקודת זמן"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "טווח הזמן לאינטראקציות של משתמשים"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "ויסות נתונים בהתאמה אישית"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "אמולציה של מחשב שולחני"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "אמולציה של Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "ללא אמולציה"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "גרס<PERSON>xe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "מתח לא מווסת של המעבד/הזיכרון"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "ויסות נתונים (throttle) של יחידת עיבוד מרכזית (CPU)"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "הגבלת רוחב פס"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "אמולציה של המסך"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "סוכן משתמש (רשת)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "סשן בדף יחיד"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "הנתונים האלה מגיעים מסשן בדף יחיד, לעומת נתוני שדות שמסכמים סשנים רבים."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "ויסות נתונים איטי ב-4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "ערך לא ידוע"}, "report/renderer/report-utils.js | show": {"message": "הצגה"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "הצגת הביקורות שרלוונטיות למדדים:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "כיווץ קטע הטקסט"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "הרחבת קטע הטקסט"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "הצגה של משאבי צד שלישי"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "שסיפקה הסביבה"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "היו בעיות שהשפיעו על ההרצה הזו של Lighthouse:"}, "report/renderer/report-utils.js | unattributable": {"message": "לא ניתנת לשיוך"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "הערכים משוערים והם עשויים להשתנות. [ציון הביצועים מחושב](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) ישירות לפי הערכים האלה."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "צפייה בעקבות"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "הצגת תרשים Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "בדיקות שהסתיימו ב<PERSON><PERSON>ון 'עובר', אבל עם אזהרות"}, "report/renderer/report-utils.js | warningHeader": {"message": "אזהרות: "}, "treemap/app/src/util.js | allLabel": {"message": "הכול"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "כל הסקריפטים"}, "treemap/app/src/util.js | coverageColumnName": {"message": "כיסוי"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "מודולים כפולים"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "בייטים בשימוש המשאב"}, "treemap/app/src/util.js | tableColumnName": {"message": "שם"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "החלפת מצב התצוגה של הטבלה"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "בייטים לא בשימוש"}}