{"core/audits/accessibility/accesskeys.js | description": {"message": "As chaves de acesso permitem que os utilizadores se concentrem rapidamente numa parte da página. Para uma navegação adequada, cada token de acesso tem de ser exclusiva. [Saiba mais acerca das chaves de acesso](https://dequeuniversity.com/rules/axe/4.8/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Os valores `[accesskey]` não são exclusivos"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Os valores `[accesskey]` são exclusivos"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Cada ARIA `role` suporta um subconjunto específico de atributos `aria-*`. A não correspondência destes invalida os atributos `aria-*`. [Saiba como corresponder os atributos ARIA às respetivas funções](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não correspondem às respetivas funções"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Os atributos `[aria-*]` correspondem às respetivas funções"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "As funções ARIA `role`s permitem às tecnologias de assistência conhecer a função de cada elemento na página Web. Se os valores `role` não estiverem escritos corretamente, não existirem valores ARIA `role` ou existirem funções abstratas, a finalidade do elemento não é comunicada aos utilizadores de tecnologias de assistência. [Saiba mais sobre as funções ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)."}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "Os valores atribuídos a `role=\"\"` não são funções ARIA válidas."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "Os valores atribuídos a `role=\"\"` são funções ARIA válidas."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Quando um elemento não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba como tornar os elementos de comandos mais acessíveis](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Os elementos `button`, `link` e `menuitem` não têm nomes acessíveis"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Os elementos `button`, `link` e `menuitem` têm nomes acessíveis"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Os elementos da caixa de diálogo ARIA sem nomes acessíveis podem impedir os utilizadores de leitores de ecrã de perceberem a finalidade destes elementos. [Saiba como tornar os elementos da caixa de diálogo ARIA mais acessíveis](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Os elementos com `role=\"dialog\"` ou `role=\"alertdialog\"` não têm nomes acessíveis."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Os elementos com `role=\"dialog\"` ou `role=\"alertdialog\"` têm nomes acessíveis."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "As tecnologias de assistência, que incluem os leitores de ecrã, funcionam de forma inconsistente quando `aria-hidden=\"true\"` está definido no `<body>` do documento. [Saiba como o `aria-hidden` afeta o corpo do documento](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` está presente no `<body>` do documento"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` não está presente no `<body>` do documento"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Os descendentes focalizáveis de um elemento `[aria-hidden=\"true\"]` impedem que esses elementos interativos fiquem disponíveis para os utilizadores de tecnologias de assistência, como leitores de ecrã. [Saiba como o atributo `aria-hidden` afeta os elementos focalizáveis](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Os elementos `[aria-hidden=\"true\"]` contêm descendentes focáveis"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Os elementos `[aria-hidden=\"true\"]` não contêm descendentes focáveis"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Quando um campo de entrada não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba mais acerca das etiquetas de campos de entrada](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Os campos de entrada ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Os campos de entrada ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Quando um elemento de contador não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba como atribuir um nome a elementos `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Os elementos `meter` ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Os elementos `meter` ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Quando um elemento `progressbar` não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba como etiquetar elementos `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Os elementos `progressbar` ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Os elementos `progressbar` ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Algumas funções ARIA têm atributos obrigatórios que descrevem o estado do elemento para os leitores de ecrã. [Saiba mais acerca das funções e dos atributos obrigatórios](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "O<PERSON> `[role]`s não têm todos os atributos `[aria-*]` obrigat<PERSON><PERSON>s"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Os `[role]`s têm todos os atributos `[aria-*]` obrigat<PERSON><PERSON>s"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Algumas funções superiores ARIA têm de conter funções secundárias específicas para desempenhar as respetivas funções de acessibilidade previstas. [Saiba mais acerca das funções e dos elementos secundários obrigatórios](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Os elementos com um `[role]` ARIA que requerem que os elementos secundários contenham um `[role]` específico têm alguns ou todos esses elementos secundários requeridos em falta."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Os elementos com um `[role]` ARIA que requerem que os elementos secundários contenham um `[role]` específico têm todos os elementos secundários requeridos."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Algumas funções secundárias ARIA têm de ser contidas por funções superiores específicas para desempenharem adequadamente as respetivas funções de acessibilidade previstas. [Saiba mais acerca das funções ARIA e do elemento superior necessário](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON> `[role]`s não são contidos pelo respetivo elemento superior obrigatório"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON> `[role]`s s<PERSON> contidos pelo respetivo elemento superior obrigatório"}, "core/audits/accessibility/aria-roles.js | description": {"message": "As funções ARIA têm de possuir valores válidos para desempenhar as funções de acessibilidade previstas. [Saiba mais acerca das funções ARIA válidas](https://dequeuniversity.com/rules/axe/4.8/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Os valores `[role]` não são válidos"}, "core/audits/accessibility/aria-roles.js | title": {"message": "Os valores `[role]` s<PERSON> válid<PERSON>"}, "core/audits/accessibility/aria-text.js | description": {"message": "Adicionar `role=text` à volta de um nó de texto dividido por marcação permite que o VoiceOver o trate como uma expressão, mas os descendentes focalizáveis do elemento não são anunciados. [Saiba mais acerca do atributo `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)."}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Os elementos com o atributo `role=text` têm descendentes focalizáveis."}, "core/audits/accessibility/aria-text.js | title": {"message": "Os elementos com o atributo `role=text` não têm descendentes focalizáveis."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Quando um campo ativar/desativar não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba mais acerca dos campos ativar/desativar](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Os campos ativar/desativar ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Os campos ativar/desativar ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Quando um elemento de sugestão não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba como atribuir um nome a elementos `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Os elementos `tooltip` ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Os elementos `tooltip` ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Quando um elemento `treeitem` não tem um nome acessível, os leitores de ecrã anunciam-no com um nome genérico, tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba mais acerca da etiquetagem de elementos `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Os elementos `treeitem` ARIA não têm nomes acessíveis"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Os elementos `treeitem` ARIA têm nomes acessíveis"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "As tecnologias de assistência, que incluem os leitores de ecrã, não conseguem interpretar atributos ARIA com valores inválidos. [Saiba mais acerca dos valores válidos para os atributos ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Os atributos `[aria-*]` não têm valores válidos"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Os atributos `[aria-*]` têm valores válidos"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "As tecnologias de assistência, que incluem os leitores de ecrã, não conseguem interpretar atributos ARIA com nomes inválidos. [Saiba mais acerca dos atributos ARIA válidos](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não são válidos ou têm erros ortográficos"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Os atributos `[aria-*]` são válidos e não têm erros ortográficos"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos reprovados"}, "core/audits/accessibility/button-name.js | description": {"message": "Quando um botão não tem um nome acessível, os leitores de ecrã anunciam-no como \"botão\", tornando-o inutilizável para os utilizadores que dependem de leitores de ecrã. [Saiba como tornar os botões mais acessíveis](https://dequeuniversity.com/rules/axe/4.8/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Os botões não têm um nome acessível"}, "core/audits/accessibility/button-name.js | title": {"message": "Os botões têm um nome acessível"}, "core/audits/accessibility/bypass.js | description": {"message": "Adicionar formas de ignorar conteúdo repetitivo permite que os utilizadores com teclado naveguem na página de forma mais eficiente. [Saiba mais acerca dos blocos para ignorar](https://dequeuniversity.com/rules/axe/4.8/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "A página não contém um título, um link para ignorar ou uma região de ponto de referência"}, "core/audits/accessibility/bypass.js | title": {"message": "A página contém um título, um link para ignorar ou uma região de ponto de referência"}, "core/audits/accessibility/color-contrast.js | description": {"message": "O texto de baixo contraste é difícil ou impossível de ler para muitos utilizadores. [Saiba como criar um contraste de cor suficiente](https://dequeuniversity.com/rules/axe/4.8/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "As cores de primeiro e de segundo plano não têm uma relação de contraste suficiente"}, "core/audits/accessibility/color-contrast.js | title": {"message": "As cores de segundo plano e de primeiro plano têm uma relação de contraste suficiente"}, "core/audits/accessibility/definition-list.js | description": {"message": "Quando as listas de definição não estão devidamente marcadas, os leitores de ecrã podem produzir um resultado confuso ou impreciso. [Saiba como estruturar corretamente listas de definições](https://dequeuniversity.com/rules/axe/4.8/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Os `<dl>`s não contêm apenas grupos de `<dt>` e `<dd>` devidamente ordenado<PERSON>, nem elementos `<script>`, `<template>` ou `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Os `<dl>`s contêm apenas grupos de `<dt>` e `<dd>` devidamente ordenados e elementos `<script>`, `<template>` ou `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Os itens de lista de definição (`<dt>` e `<dd>`) têm de estar unidos num elemento `<dl>` superior de modo a garantir que os leitores de ecrã os possam anunciar adequadamente. [Saiba como estruturar corretamente listas de definições](https://dequeuniversity.com/rules/axe/4.8/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Os itens de lista de definição não estão unidos em elementos `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Os itens de lista de definição estão unidos em elementos `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "O título proporciona aos utilizadores de leitores de ecrã uma vista geral da página, sendo que os utilizadores de motores de pesquisa dependem dele para determinar se uma página é relevante para a respetiva pesquisa. [Saiba mais acerca dos títulos de documentos](https://dequeuniversity.com/rules/axe/4.8/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "O documento não tem um elemento `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "O documento tem um elemento `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Todos os elementos focalizáveis têm de ter um `id` exclusivo para garantir que são visíveis para as tecnologias de assistência. [Saiba como corrigir `id`s duplicados](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Os atributos `[id]` nos elementos ativos e focáveis não são exclusivos"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Os atributos `[id]` nos elementos ativos e focáveis são exclusivos"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "O valor de um ID ARIA tem de ser exclusivo para evitar que outras instâncias sejam ignoradas pelas tecnologias de assistência. [Saiba como corrigir IDs ARIA duplicados](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Os ID ARIA não são exclusivos"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Os IDs ARIA são exclusivos"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Um título sem conteúdo ou com texto inacessível impede os utilizadores de leitores de ecrã de acederem a informações sobre a estrutura da página. [Saiba mais acerca dos títulos](https://dequeuniversity.com/rules/axe/4.8/empty-heading)."}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Os elementos do título não têm conteúdo."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Todos os elementos de título têm conteúdo."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Os campos de formulários com várias etiquetas podem ser anunciados de forma confusa por tecnologias de assistência, como leitores de ecrã que usam a primeira, a última ou todas as etiquetas. [Saiba como usar etiquetas de formulários](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Os campos do formulário contêm várias etiquetas"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nenhum campo do formulário contém várias etiquetas"}, "core/audits/accessibility/frame-title.js | description": {"message": "Os utilizadores com leitores de ecrã dependem dos títulos de frames para descrever o conteúdo dos frames. [Saiba mais acerca dos títulos de frames](https://dequeuniversity.com/rules/axe/4.8/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Os elementos `<frame>` ou `<iframe>` não têm um título"}, "core/audits/accessibility/frame-title.js | title": {"message": "Os elementos `<frame>` ou `<iframe>` têm um título"}, "core/audits/accessibility/heading-order.js | description": {"message": "Os títulos devidamente ordenados que não ignoram níveis transmitem a estrutura semântica da página, facilitando a navegação e a compreensão ao usar tecnologias de assistência. [Saiba mais acerca da ordem dos títulos](https://dequeuniversity.com/rules/axe/4.8/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Os elementos do título não se encontram numa ordem sequencialmente decrescente"}, "core/audits/accessibility/heading-order.js | title": {"message": "Os elementos do título encontram-se numa ordem sequencialmente decrescente"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Se uma página não especificar um atributo `lang`, um leitor de ecrã parte do princípio de que a página está no idioma predefinido que o utilizador escolheu quando configurou o leitor de ecrã. Se a página não estiver realmente no idioma predefinido, o leitor de ecrã pode não anunciar corretamente o texto da página. [Saiba mais acerca do atributo `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "O elemento `<html>` não tem um atributo `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "O elemento `<html>` tem um atributo `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Especificar um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido ajuda os leitores de ecrã a anunciar texto adequadamente. [Saiba como usar o atributo `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "O elemento `<html>` tem um valor válido para o respetivo atributo `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "O elemento `<html>` tem um valor válido para o respetivo atributo `[lang]`"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Se a página Web não especificar um idioma consistente, o leitor de ecrã pode não anunciar o texto da página corretamente. [Saiba mais acerca do atributo `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)."}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "O elemento `<html>` não tem um atributo `[xml:lang]` com o mesmo idioma base que o atributo `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "O elemento `<html>` tem um atributo `[xml:lang]` com o mesmo idioma base que o atributo `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "Os links com o mesmo destino devem ter a mesma descrição para ajudar os utilizadores a compreenderem o objetivo do link e decidirem se o devem seguir. [Saiba mais acerca dos links idênticos](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "<PERSON><PERSON> links idênticos não têm a mesma finalidade."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "<PERSON><PERSON> links idênticos têm a mesma finalidade."}, "core/audits/accessibility/image-alt.js | description": {"message": "Os elementos informativos devem procurar incluir texto curto, descritivo e alternativo. Os elementos decorativos podem ser ignorados com um atributo alternativo vazio. [Saiba mais acerca do atributo `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Os elementos de imagem não têm atributos `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Os elementos de imagem têm atributos `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Os elementos informativos devem procurar incluir texto curto, descritivo e alternativo. O texto alternativo, que é exatamente igual ao texto adjacente ao link ou à imagem, é potencialmente confuso para os utilizadores de leitores de ecrã, uma vez que o texto é lido duas vezes. [Saiba mais acerca do atributo `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)."}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Os elementos de imagem têm atributos `[alt]` que são texto redundante."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Os elementos de imagem não têm atributos `[alt]` que são texto redundante."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Adicionar texto percetível e acessível aos botões de entrada pode ajudar os utilizadores de leitores de ecrã a compreender a finalidade do botão de entrada. [Saiba mais acerca dos botões de entrada](https://dequeuniversity.com/rules/axe/4.8/input-button-name)."}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Os botões de entrada não têm texto percetível."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Os botões de entrada têm texto percetível."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Quando uma imagem está a ser usada como um botão `<input>`, facultar texto alternativo pode ajudar os utilizadores de leitores de ecrã a compreender a finalidade do botão. [Saiba mais acerca do texto alternativo da imagem de entrada](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Os elementos `<input type=\"image\">` não têm texto `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Os elementos `<input type=\"image\">` têm texto `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "As etiquetas de texto visíveis que não correspondam ao nome acessível podem resultar numa experiência confusa para os utilizadores de leitores de ecrã. [Saiba mais sobre os nomes acessíveis](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)."}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Os elementos com etiquetas de texto visíveis não têm nomes acessíveis correspondentes."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Os elementos com etiquetas de texto visíveis têm nomes acessíveis correspondentes."}, "core/audits/accessibility/label.js | description": {"message": "As etiquetas garantem que os controlos de formulários são anunciados adequadamente pelas tecnologias de assistência, que incluem os leitores de ecrã. [Saiba mais acerca das etiquetas de elementos de formulários](https://dequeuniversity.com/rules/axe/4.8/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Os elementos de formulário não têm etiquetas associadas"}, "core/audits/accessibility/label.js | title": {"message": "Os elementos de formulário têm etiquetas associadas"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Um ponto de referência principal ajuda os utilizadores de leitores de ecrã a navegar numa página Web. [Saiba mais sobre pontos de referência](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)."}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "O documento não tem um ponto de referência principal."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "O documento tem um ponto de referência principal."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "O texto de baixo contraste é difícil ou impossível de ler para muitos utilizadores. O texto do link percetível melhora a experiência dos utilizadores com visão reduzida. [Saiba como tornar os links distinguíveis](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)."}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Os links dependem da cor para serem distinguíveis."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "<PERSON><PERSON> links são distinguíveis sem depender da cor."}, "core/audits/accessibility/link-name.js | description": {"message": "O texto de link (e texto alternativo para imagens, quando usado como link) que seja percetível, exclusivo e focalizável melhora a experiência de navegação dos utilizadores de leitores de ecrã. [Saiba como tornar os links acessíveis](https://dequeuniversity.com/rules/axe/4.8/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON> links não têm um nome percetível"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON> links têm um nome percetível"}, "core/audits/accessibility/list.js | description": {"message": "Os leitores de ecrã têm uma forma específica de anunciar listas. Garantir uma estrutura de listas adequada é benéfico para o resultado do leitor de ecrã. [Saiba mais acerca da estrutura de listas adequada](https://dequeuniversity.com/rules/axe/4.8/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "As listas não contêm apenas elementos `<li>` e elementos de suporte de script (`<script>` e `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "As listas contêm apenas elementos `<li>` e elementos de suporte de script (`<script>` e `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Os leitores de ecrã necessitam que os itens de lista (`<li>`) sejam contidos num `<ul>`, `<ol>` ou `<menu>` superior para serem adequadamente anunciados. [Saiba mais acerca da estrutura de listas adequada](https://dequeuniversity.com/rules/axe/4.8/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Os itens de lista (`<li>`) não estão contidos nos elementos superiores `<ul>`, `<ol>` ou `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Os itens de lista (`<li>`) estão contidos nos elementos superiores `<ul>`, `<ol>` ou `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Os utilizadores não esperam que uma página se atualize automaticamente e, se tal acontecer, vai desviar o foco para a parte superior da página. Esta situação pode criar uma experiência frustrante ou confusa. [Saiba mais acerca da metatag de atualização](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "O documento utiliza `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "O documento não utiliza `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Desativar o zoom é problemático para os utilizadores com visão reduzida que dependem da ampliação do ecrã para ver adequadamente o conteúdo de uma página Web. [Saiba mais acerca da metatag de área visível](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "O `[user-scalable=\"no\"]` é utilizado no elemento `<meta name=\"viewport\">` ou o atributo `[maximum-scale]` é inferior a 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "O `[user-scalable=\"no\"]` não é utilizado no elemento `<meta name=\"viewport\">` e o atributo `[maximum-scale]` não é inferior a 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Os leitores de ecrã não conseguem traduzir conteúdo que não seja de texto. Adicionar texto alternativo a elementos `<object>` ajuda os leitores de ecrã a transmitir significado aos utilizadores. [Saiba mais acerca do texto alternativo para elementos `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Os elementos `<object>` não têm texto alternativo"}, "core/audits/accessibility/object-alt.js | title": {"message": "Os elementos `<object>` têm texto alternativo"}, "core/audits/accessibility/select-name.js | description": {"message": "Os elementos de formulário sem etiquetas eficazes podem criar experiências frustrantes para os utilizadores de leitores de ecrã. [Saiba mais acerca do elemento `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)."}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Os elementos Select não têm elementos de etiqueta associados."}, "core/audits/accessibility/select-name.js | title": {"message": "Os elementos Select têm elementos de etiqueta associados."}, "core/audits/accessibility/skip-link.js | description": {"message": "A inclusão de um link para ignorar pode ajudar os utilizadores a avançarem para o conteúdo principal para poupar tempo. [Saiba mais sobre os links para ignorar](https://dequeuniversity.com/rules/axe/4.8/skip-link)."}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Os links para ignorar não são focalizáveis."}, "core/audits/accessibility/skip-link.js | title": {"message": "Os links para ignorar são focalizáveis."}, "core/audits/accessibility/tabindex.js | description": {"message": "Um valor superior a 0 implica uma ordenação de navegação explícita. Embora seja tecnicamente válida, esta situação costuma criar experiências frustrantes para os utilizadores que dependem de tecnologias de assistência. [Saiba mais acerca do atributo `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Alguns elementos têm um valor `[tabindex]` superior a 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Nenhum elemento tem um valor `[tabindex]` superior a 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "O atributo summary deve descrever a estrutura da tabela, enquanto `<caption>` deve ter o título no ecrã. A marcação precisa da tabela ajuda os utilizadores de leitores de ecrã. [Saiba mais sobre os elementos summary e caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)."}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "As tabelas têm o mesmo conteúdo no atributo summary e no elemento `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "As tabelas têm conteúdo diferente no atributo summary e no elemento `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Os leitores de ecrã têm funcionalidades para facilitar a navegação em tabelas. Garantir que as tabelas usam o elemento de legenda real em vez das células com o atributo `[cols<PERSON>]` pode melhorar a experiência dos utilizadores de leitores de ecrã. [Saiba mais sobre as legendas](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)."}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "As tabelas não usam `<caption>` em vez de células com o atributo `[colspan]` para indicar uma legenda."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "As tabelas usam `<caption>` em vez de células com o atributo `[colspan]` para indicar uma legenda."}, "core/audits/accessibility/target-size.js | description": {"message": "As áreas de toque com tamanho e espaçamento suficientes ajudam os utilizadores que possam ter dificuldades em usar controlos pequenos a ativar os alvos. [Saiba mais acerca das áreas de toque](https://dequeuniversity.com/rules/axe/4.8/target-size)."}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "As áreas de toque não têm tamanho nem espaçamento suficientes."}, "core/audits/accessibility/target-size.js | title": {"message": "As áreas de toque têm tamanho e espaçamento suficientes."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Os leitores de ecrã têm funcionalidades para facilitar a navegação em tabelas. Garantir que os elementos `<td>` numa tabela grande (3 ou mais células de largura e altura) têm um cabeçalho de tabela associado pode melhorar a experiência para os utilizadores de leitores de ecrã. [Saiba mais acerca dos cabeçalhos de tabelas](https://dequeuniversity.com/rules/axe/4.8/td-has-header)."}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Os elementos `<td>` num `<table>` grande não têm cabeçalhos de tabela."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Os elementos `<td>` num elemento `<table>` grande têm um ou mais cabeçalhos de tabelas."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Os leitores de ecrã têm funcionalidades para facilitar a navegação em tabelas. Garantir que as células `<td>` que usam o atributo `[headers]` apenas referenciam outras células na mesma tabela pode melhorar a experiência para os utilizadores de leitores de ecrã. [Saiba mais acerca do atributo `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "As células num elemento `<table>` que utilizam o atributo `[headers]` referem-se a um elemento `id` que não se encontra dentro da mesma tabela."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "As células num elemento `<table>` que utilizam o atributo `[headers]` referem-se a células de tabela dentro da mesma tabela."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Os leitores de ecrã têm funcionalidades para facilitar a navegação em tabelas. Garantir que os cabeçalhos de tabelas referenciam sempre algum conjunto de células pode melhorar a experiência dos utilizadores com leitores de ecrã. [Saiba mais acerca dos cabeçalhos de tabelas](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` não têm as c<PERSON><PERSON><PERSON> de dados que descrevem."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` têm as c<PERSON><PERSON><PERSON> de dados que descrevem."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Especificar um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido para elementos ajuda a garantir que o texto é pronunciado corretamente por um leitor de ecrã. [Saiba como usar o atributo `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Os atributos `[lang]` não têm um valor válido"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Os atributos `[lang]` têm um valor válido"}, "core/audits/accessibility/video-caption.js | description": {"message": "Quando um vídeo oferece uma legenda, é mais fácil para os utilizadores surdos e com problemas de audição aceder às informações. [Saiba mais acerca das legendas de vídeos](https://dequeuniversity.com/rules/axe/4.8/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Os elementos `<video>` não contêm um elemento `<track>` com `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Os elementos `<video>` contêm um elemento `<track>` com `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON>or atual"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Símbolo sugerido"}, "core/audits/autocomplete.js | description": {"message": "O atributo `autocomplete` ajuda os utilizadores a enviarem os formulários mais rapidamente. Para reduzir o esforço dos utilizadores, considere ativá-lo ao definir o atributo `autocomplete` para um valor válido. [Saiba mais acerca do `autocomplete` em formulários](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Os elementos `<input>` não têm atributos `autocomplete` corretos"}, "core/audits/autocomplete.js | manualReview": {"message": "Requer revisão manual"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Reveja a ordem dos símbolos"}, "core/audits/autocomplete.js | title": {"message": "Os elementos `<input>` utilizam o atributo `autocomplete` corretamente"}, "core/audits/autocomplete.js | warningInvalid": {"message": "S<PERSON><PERSON>lo(s) de `autocomplete`\": \"{token}\" é inválido em {snippet}."}, "core/audits/autocomplete.js | warningOrder": {"message": "Reveja a ordem dos símbolos: \"{tokens}\" em {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Acionável"}, "core/audits/bf-cache.js | description": {"message": "Muitas navegações são realizadas ao retroceder para uma página anterior ou avançar novamente. A cache para a frente/para trás (bfcache) pode acelerar estas navegações de retorno. [Saiba mais sobre a bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo da falha}other{# motivos da falha}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Motivo da falha"}, "core/audits/bf-cache.js | failureTitle": {"message": "A página impediu o restauro da cache para a frente/para trás"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON> de falha"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Não acionável"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Suporte do navegador pendente"}, "core/audits/bf-cache.js | title": {"message": "A página não impediu o restauro da cache para a frente/para trás"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Não é possível testar a cache para a frente/para trás no Chrome sem interface antigo (`--chrome-flags=\"--headless=old\"`). Para ver os resultados da auditoria, use o novo Chrome sem interface (`--chrome-flags=\"--headless=new\"`) ou o Chrome padrão."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "As extensões do Chrome afetam negativamente o desempenho de carregamento desta página. Experimente efetuar uma auditoria à página no modo de navegação anónima ou com um perfil do Chrome sem extensões."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Avaliação do script"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Análise do script"}, "core/audits/bootup-time.js | columnTotal": {"message": "Tempo total da CPU"}, "core/audits/bootup-time.js | description": {"message": "Considere reduzir o tempo despendido a analisar, compilar e executar JS. Poderá descobrir que é útil publicar payloads de JS mais pequenos. [Saiba como reduzir o tempo de execução de JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Reduza o tempo de execução de JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Tempo de execução de JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Remova módulos de JavaScript grandes e duplicados de pacotes para reduzir bytes desnecessários consumidos pela atividade de rede. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Remova módulos duplicados em pacotes JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Os GIFs grandes são ineficientes para publicação de conteúdo animado. Para poupar bytes de rede, considere usar vídeos MPEG4/WebM para animações e ficheiros PNG/WebP para imagens estáticas em vez de GIFs. [Saiba mais acerca dos formatos de vídeo eficientes](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Utilize formatos de vídeo para conteúdo animado"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Os polyfills e as transformações permitem que os navegadores antigos usem novas funcionalidades de JavaScript. No entanto, muitos não são necessários para os navegadores modernos. Para o seu JavaScript integrado, adote uma estratégia de implementação de scripts moderna através da deteção da funcionalidade module/nomodule para reduzir a quantidade de código enviado para os navegadores modernos, mantendo, ao mesmo tempo, a compatibilidade com os navegadores antigos. [Saiba como usar o JavaScript moderno](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Evite a publicação do JavaScript antigo em navegadores modernos"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Muitas vezes, os formatos de imagens como WebP e AVIF proporcionam uma melhor compressão do que os formatos PNG ou JPEG, o que se traduz em transferências mais rápidas e num menor consumo de dados. [Saiba mais acerca dos formatos de imagens modernos](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Publique imagens em formatos de última geração"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Considere carregar as imagens não visíveis e ocultas em diferido após a conclusão do carregamento de todos os recursos críticos, para reduzir o tempo até à interação. [Saiba como adiar imagens não visíveis](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON> as imagens não visíveis"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Os recursos estão a bloquear o primeiro preenchimento da página. Considere publicar JS/CSS críticos inline e adiar todos os JS/estilos não críticos. [Saiba como eliminar os recursos que bloqueiam a renderização](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimine recursos que bloqueiam o processamento"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Os grandes payloads de rede têm custos reais para os utilizadores e estão fortemente correlacionados com tempos de carregamento demorados. [Saiba como reduzir os tamanhos dos payloads](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "O tamanho total era {totalBytes, number, bytes} KiB."}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evite enormes payloads de rede"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita enormes payloads de rede"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Reduzir os ficheiros CSS pode reduzir os tamanhos dos payloads de rede. [Saiba como reduzir o CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Reduza o CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Reduzir os ficheiros JavaScript pode reduzir os tamanhos dos payloads e o tempo de análise de scripts. [Saiba como reduzir o JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Reduza o JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reduza as regras não usadas das folhas de estilos e adie o CSS não usado de conteúdo na parte superior para diminuir a quantidade de bytes consumidos pela atividade da rede. [Saiba como reduzir o CSS não usado](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reduza o CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reduza o JavaScript não usado e adie o carregamento de scripts até serem necessários para diminuir a quantidade de bytes consumidos pela atividade da rede. [Saiba como reduzir o JavaScript não usado](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reduza o JavaScript não utilizado"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Uma longa duração total da cache pode acelerar as visitas repetidas à sua página. [Saiba mais acerca das políticas de cache eficientes](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 recurso encontrado}other{# recursos encontrados}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Publique recursos estáticos com uma política de cache eficiente"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Utiliza uma política de cache eficiente em recursos estáticos"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "As imagens otimizadas são carregadas mais rapidamente e consomem menos dados móveis. [Saiba como codificar imagens de forma eficiente](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifique as imagens de forma eficiente"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dimensõ<PERSON> reais"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Dimensões apresentadas"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "As imagens eram maiores do que o respetivo tamanho apresentado"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "As imagem eram adequadas para o respetivo tamanho apresentado"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Publique imagens com um tamanho adequado para poupar dados móveis e melhorar o tempo de carregamento. [Saiba como dimensionar imagens](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Dimensione adequadamente as imagens"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Os recursos baseados em texto devem ser publicados com compressão (gzip, Deflate ou Brotli) para reduzir o total de bytes de rede. [Saiba mais acerca da compressão de texto](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Ative a compressão de texto"}, "core/audits/content-width.js | description": {"message": "Se a largura do conteúdo da sua app não corresponder à largura da área visível, a sua app pode não estar otimizada para ecrãs de dispositivos móveis. [Saiba como dimensionar o conteúdo para a área visível](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "O tamanho da área visível de {innerWidth} px não corresponde ao tamanho da janela de {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "O conteúdo não é dimensionado corretamente para a área visível"}, "core/audits/content-width.js | title": {"message": "O conteúdo é dimensionado corretamente para a área visível"}, "core/audits/critical-request-chains.js | description": {"message": "As Cadeias de pedidos críticos abaixo apresentam os recursos que são carregados com uma prioridade elevada. Considere reduzir o tamanho das cadeias, reduzir o tamanho de transferência dos recursos ou adiar a transferência de recursos desnecessários para melhorar o carregamento de página. [Saiba como evitar encadear pedidos críticos](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 cadeia encontrada}other{# cadeias encontradas}}"}, "core/audits/critical-request-chains.js | title": {"message": "<PERSON><PERSON><PERSON> encadear pedidos c<PERSON>"}, "core/audits/csp-xss.js | columnDirective": {"message": "Diretiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Gravidade"}, "core/audits/csp-xss.js | description": {"message": "Uma Política de Segurança de Conteúdos (CSP) forte reduz significativamente o risco de ataques de cross-site scripting (XSS). [Saiba como usar uma CSP para evitar XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sintaxe"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "A página contém uma CSP definida numa etiqueta `<meta>`. Considere mover a CSP para um cabeçalho HTTP ou definir outra CSP rigorosa num cabeçalho HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "Não foi encontrada nenhuma CSP no modo de aplicação."}, "core/audits/csp-xss.js | title": {"message": "Certifique-se de que a CSP é eficaz contra ataques XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Descontinuação/aviso"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "As APIs descontinuadas serão eventualmente removidas do navegador. [Saiba mais acerca das APIs descontinuadas](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 aviso encontrado}other{# avisos encontrados}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Utiliza APIs descontinuadas"}, "core/audits/deprecations.js | title": {"message": "Evita APIs descontinuadas"}, "core/audits/dobetterweb/charset.js | description": {"message": "É obrigatória uma declaração de codificação de carateres. Pode ser realizada com uma tag `<meta>` nos primeiros 1024 bytes do HTML ou no cabeçalho da resposta do HTTP de tipo de conteúdo. [Saiba mais acerca da declaração de codificação de carateres](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "A declaração de charset está em falta ou ocorre demasiado tarde no HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "Define o charset adequadamente"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Especificar um doctype impede que o navegador mude para o modo quirks. [Saiba mais acerca da declaração doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "O nome do doctype deve ser a string `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "O documento contém um `doctype` que aciona o `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "O documento deve conter um doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Era esperado que o PublicId fosse uma string vazia"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Era esperado que o SystemId fosse uma string vazia."}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "O documento contém um `doctype` que aciona o `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "A página não possui o doctype HTML, o que faz com que o modo quirks seja acionado"}, "core/audits/dobetterweb/doctype.js | title": {"message": "A página possui o doctype HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estatística"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Um DOM grande irá aumentar a utilização da memória, gerar [cálculos de estilo](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) mais demorados e produzir [ajuste<PERSON> de esquema](https://developers.google.com/speed/articles/reflow) dispendiosos. [Saiba como evitar um tamanho excessivo do DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}other{# elementos}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evite um tamanho excessivo do DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidade máxima do DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Máximo de elementos secundários"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Evita um tamanho excessivo do DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Os utilizadores desconfiam ou ficam confusos perante os sites que pedem a sua localização sem contexto. Em vez disso, considere associar o pedido a uma ação do utilizador. [Saiba mais acerca da autorização de geolocalização](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita a autorização de geolocalização no carregamento da página"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita a solicitação da autorização de geolocalização no carregamento da página"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Tipo de problema"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Os erros registados no painel `Issues` das Chrome Devtools indicam problemas não resolvidos. Podem ser provenientes de falhas de pedidos de rede, controlos de segurança insuficientes e outras questões do navegador. Abra o painel Issues das Chrome DevTools para visualizar mais detalhes sobre cada erro."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Foram registados erros no painel `Issues` das Chrome Devtools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Bloqueado pela política de origem cruzada"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Utilização de recursos pesados por anúncios"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Não existem erros no painel `Issues` das Chrome Devtools"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Vers<PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Todas as bibliotecas de interface JavaScript detetadas na página. [Saiba mais acerca desta auditoria de diagnóstico de deteção da biblioteca de JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Bibliotecas JavaScript detetadas"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "No caso de utilizadores com ligações lentas, os scripts externos injetados dinamicamente através de `document.write()` podem atrasar o carregamento da página em dezenas de segundos. [Saiba como evitar document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Evite `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Os utilizadores desconfiam ou ficam confusos perante os sites que pedem o envio de notificações sem contexto. Em vez disso, considere associar o pedido aos gestos do utilizador. [Saiba como obter autorizações para notificações de forma responsável](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita a autorização de notificações no carregamento da página"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita a solicitação da autorização de notificações no carregamento da página"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Impedir a colagem de entradas é uma má prática para a experiência do utilizador e reduz a segurança através do bloqueio de gestores de palavras-passe.[Saiba mais sobre os campos de entrada intuitivos](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Impede que os utilizadores colem conteúdo em campos de entrada"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Permite que os utilizadores colem conteúdo nos campos de entrada"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "O HTTP/2 oferece muitas vantagens em relação ao HTTP/1.1, nomeadamente cabeçalhos binários e multiplexação. [Saiba mais acerca do HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 pedido não publicado através de HTTP/2}other{# pedidos não publicados através de HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Utilize HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Considere marcar os seus ouvintes de eventos de toque e roda como `passive` para melhorar o desempenho de deslocamento da sua página. [Saiba mais acerca da adoção de ouvintes de eventos passivos](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Não utiliza ouvintes passivos para melhorar o desempenho do deslocamento"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Utiliza ouvintes passivos para melhorar o desempenho do deslocamento"}, "core/audits/errors-in-console.js | description": {"message": "Os erros registados na consola indicam problemas não resolvidos. Estes podem ser provenientes de falhas de pedidos de rede e outras questões do navegador. [Saiba mais acerca destes erros na auditoria de diagnóstico da consola](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Os erros do navegador foram registados na consola"}, "core/audits/errors-in-console.js | title": {"message": "Nenhum erro do navegador registado na consola"}, "core/audits/font-display.js | description": {"message": "Tire partido da funcionalidade CSS `font-display` para garantir que o texto é visível para o utilizador enquanto os tipos de letra para Websites são carregados. [Saiba mais sobre `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Garanta que o texto permanece visível durante o carregamento de tipos de letra para Websites"}, "core/audits/font-display.js | title": {"message": "Todo o texto permanece visível durante os carregamentos de tipos de letra para Websites"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{O Lighthouse não conseguiu verificar automaticamente o valor `font-display` da origem {fontOrigin}.}other{O Lighthouse não conseguiu verificar automaticamente os valores `font-display` da origem {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Proporção (atual)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Formato (apresentado)"}, "core/audits/image-aspect-ratio.js | description": {"message": "As dimensões de apresentação das imagens devem corresponder ao formato natural. [Saiba mais acerca do formato das imagens](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Apresenta imagens com uma proporção incorreta"}, "core/audits/image-aspect-ratio.js | title": {"message": "Apresenta imagens com uma proporção correta"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Tamanho real"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> a<PERSON>resent<PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON> esperado"}, "core/audits/image-size-responsive.js | description": {"message": "As dimensões naturais da imagem devem ser proporcionais ao tamanho do ecrã e à relação de píxeis para maximizar a nitidez da imagem. [Saiba como disponibilizar imagens dinâmicas](https://web.dev/articles/serve-responsive-images)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Publica imagens com baixa resolução"}, "core/audits/image-size-responsive.js | title": {"message": "Publica imagens com a resolução adequada"}, "core/audits/installable-manifest.js | already-installed": {"message": "A app já está instalada."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Não foi possível transferir um ícone necessário do manifesto."}, "core/audits/installable-manifest.js | columnValue": {"message": "Motivo da falha"}, "core/audits/installable-manifest.js | description": {"message": "O service worker é a tecnologia que permite que a sua app use muitas funcionalidades de apps Web progressivas, tais como offline, adicionar ao ecrã principal e notificações push. Com as implementações adequadas do service worker e manifesto, os navegadores podem pedir proativamente aos utilizadores que adicionem a sua app ao respetivo ecrã principal, o que pode resultar numa maior interação. [Saiba mais acerca dos requisitos de capacidade de instalação de manifestos](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 motivo}other{# motivos}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "O manifesto da app para a Web ou o service worker não cumpre os requisitos de capacidade de instalação"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "O URL e o ID da app na Play Store não correspondem."}, "core/audits/installable-manifest.js | in-incognito": {"message": "A página está carregada numa janela de navegação anónima."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "A propriedade `display` do manifesto tem de ser uma das seguintes: `standalone`, `fullscreen` ou `minimal-ui`."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "O manifesto contém o campo \"display_override\" e o primeiro modo de apresentação suportado tem de ser um dos seguintes: \"standalone\", \"fullscreen\" ou \"minimal-ui\"."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Não foi possível obter ou analisar o manifesto ou o manifesto está vazio."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "O URL do manifesto foi alterado durante a operação de obtenção do manifesto."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "O manifesto não contém um campo `name` ou `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "O manifesto não contém um ícone adequado. É necessário um formato PNG, SVG ou WebP de, pelo menos, {value0} px, é necessário definir o atributo \"sizes\" e o atributo \"purpose\". Se definido, tem de incluir \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nenhum ícone fornecido tem, pelo menos, {value0} px quadrados no formato PNG, SVG ou WebP, com o atributo \"purpose\" não definido ou definido como \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "O ícone transferido estava vazio ou danificado."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Não foi fornecido um ID da Play Store."}, "core/audits/installable-manifest.js | no-manifest": {"message": "A página não tem um URL <link> de manifesto."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Não foi possível verificar o service worker sem um campo \"start_url\" no manifesto."}, "core/audits/installable-manifest.js | noErrorId": {"message": "O ID de erro de capacidade de instalação \"{errorId}\" não é reconhecido."}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "A página não é publicada a partir de uma origem segura."}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "A página não é carregada no frame principal."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "A página não funciona offline."}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "A PWA foi desinstalada e as verificações de capacidade de instalação estão a ser repostas."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "A plataforma da aplicação especificada não é suportada no Android."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "O manifesto especifica prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "A opção prefer_related_applications só é suportada no Chrome Beta e nos canais estáveis do Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "O Lighthouse não conseguiu determinar se a página é instalável. Tente com uma versão mais recente do Chrome."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "O URL de início do manifesto não é válido."}, "core/audits/installable-manifest.js | title": {"message": "O manifesto da app para a Web e o service worker cumprem os requisitos de capacidade de instalação"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Um URL do manifesto contém um nome de utilizador, palavra-passe ou porta."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "A página não funciona offline. A página não será considerada como passível de instalação após o lançamento da versão estável do Chrome 93, em agosto de 2021."}, "core/audits/is-on-https.js | allowed": {"message": "Permitido"}, "core/audits/is-on-https.js | blocked": {"message": "Bloqueado"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL inseguro"}, "core/audits/is-on-https.js | columnResolution": {"message": "Resolução de pedidos"}, "core/audits/is-on-https.js | description": {"message": "Todos os sites devem ser protegidos com HTTPS, mesmo aqueles que não lidam com dados confidenciais. Isto inclui evitar [conteúdo misto](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), em que alguns recursos são carregados por HTTP apesar de o pedido inicial ser publicado através de HTTPS. O HTTPS evita que os intrusos adulterem ou escutem passivamente as comunicações entre a sua app e os seus utilizadores, e é um pré-requisito para o HTTP/2 e muitas novas APIs de plataformas Web. [Saiba mais acerca do HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 pedido inseguro encontrado}other{# pedidos inseguros encontrados}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Não utiliza HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Utiliza HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Atualizado automaticamente para HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Permitido com aviso"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% de LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "Fase"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Tempo"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Este é o elemento de maior preenchimento com conteúdo na área visível. [Saiba mais acerca do elemento de Maior preenchimento com conteúdo](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Atraso no carregamento"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Tempo de carregamento"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "At<PERSON>o da renderização"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Elemento de Maior preenchimento com conteúdo"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Impacto da mudança de esquema"}, "core/audits/layout-shift-elements.js | description": {"message": "Estes elementos DOM foram os mais afetados por mudanças de esquema. Algumas mudanças de esquema podem não ser incluídas no valor da métrica CLS devido à [normalização](https://web.dev/articles/cls#what_is_cls). [Saiba como melhorar a CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON> esquemas grandes"}, "core/audits/layout-shifts.js | columnScore": {"message": "Pontuação da mudança de esquema"}, "core/audits/layout-shifts.js | description": {"message": "<PERSON><PERSON><PERSON> <PERSON> as maiores mudanças de esquema observadas na página. Cada item da tabela representa uma única mudança de esquema e mostra o elemento que mais mudou. Abaixo de cada item, encontram-se possíveis causas principais que levaram à mudança de esquema. Algumas destas mudanças de esquema podem não ser incluídas no valor da métrica CLS devido à [visualização baseada na janela atual](https://web.dev/articles/cls#what_is_cls). [Saiba como melhorar a CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{1 mudança de esquema encontrada}other{# mudanças de esquema encontradas}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "Tipo de letra para Websites carregado"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "iFrame injetado"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Um pedido de rede atrasado ajustou o esquema da página"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "Elemento de multimédia sem um tamanho explícito"}, "core/audits/layout-shifts.js | title": {"message": "<PERSON><PERSON><PERSON> esquemas grandes"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "As imagens na parte superior carregadas em diferido são renderizadas mais tarde no ciclo de vida da página, o que pode atrasar o Maior preenchimento com conteúdo. [Saiba mais acerca do carregamento em diferido ideal](https://web.dev/articles/lcp-lazy-loading)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "A imagem de Maior preenchimento com conteúdo foi carregada em diferido"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "A imagem de Maior preenchimento com conteúdo não foi carregada em diferido"}, "core/audits/long-tasks.js | description": {"message": "<PERSON><PERSON><PERSON> as tarefas mais longas na thread principal, o que é útil para identificar o que mais contribui para o atraso de entrada. [Saiba como evitar tarefas longas na thread principal](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# tarefa longa encontrada}other{# tarefas longas encontradas}}"}, "core/audits/long-tasks.js | title": {"message": "<PERSON><PERSON><PERSON> ta<PERSON> longas na <PERSON> principal"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoria"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Considere reduzir o tempo despendido a analisar, compilar e executar JS. Poderá descobrir que é útil publicar payloads de JS mais pequenos. [Saiba como minimizar as operações da thread principal](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> do thread principal"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> do thread principal"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Para alcançar o maior número possível de utilizadores, os sites devem funcionar em todos os navegadores principais. [Saiba mais acerca da compatibilidade entre navegadores](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "O site funciona em vários navegadores"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Confirme que as páginas individuais têm links diretos através de URLs e os URLs são exclusivos para a finalidade de serem partilhados em redes sociais. [Saiba mais acerca do fornecimento de links diretos](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tem um URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "As transições devem parecer rápidas à medida em que toca em qualquer local, mesmo numa rede lenta. Esta experiência é essencial para a perceção do desempenho de um utilizador. [Saiba mais acerca das transições de páginas](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "As transições da página não parecem ficar bloqueadas na rede"}, "core/audits/maskable-icon.js | description": {"message": "Um ícone mascarável garante que a imagem preenche toda a forma sem sofrer o efeito letterbox quando instala a app num dispositivo. [Saiba mais acerca dos ícones de manifestos mascaráveis](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "O manifesto não tem um ícone mascarável"}, "core/audits/maskable-icon.js | title": {"message": "O manifesto tem um ícone mascarável"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "A Mudança de esquema cumulativo mede o movimento dos elementos visíveis na área visível. [Saiba mais acerca da métrica Mudança de esquema cumulativo](https://web.dev/articles/cls)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "O Primeiro preenchimento com conteúdo assinala o momento de preenchimento com o primeiro texto ou imagem. [Saiba mais acerca da métrica Primeiro preenchimento com conteúdo](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "A métrica Primeiro preenchimento significativo mede quando é que o conteúdo principal de uma página fica visível. [Saiba mais acerca da métrica Primeiro preenchimento significativo](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "A interação até ao preenchimento seguinte mede a capacidade de resposta da página, bem como o tempo que esta demora a responder visivelmente à introdução do utilizador. [Saiba mais acerca da métrica Interação até ao preenchimento seguinte](https://web.dev/articles/inp)."}, "core/audits/metrics/interactive.js | description": {"message": "O Tempo até à interação é a quantidade de tempo que a página demora a ficar totalmente interativa. [Saiba mais acerca da métrica Tempo até à interação](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "A métrica Maior preenchimento com conteúdo assinala o momento de preenchimento com o maior texto ou imagem. [Saiba mais acerca da métrica Maior preenchimento com conteúdo](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "O máximo potencial do primeiro atraso de entrada que pode afetar os utilizadores é a duração da tarefa mais longa. [Saiba mais acerca da métrica Máximo potencial do primeiro atraso de entrada](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "A métrica Índice de velocidade apresenta a rapidez de preenchimento visível dos conteúdos de uma página. [Saiba mais acerca da métrica Índice de velocidade](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "A soma de todos os períodos entre o FCP e o Tempo até à interação, quando a duração da tarefa é superior a 50 ms, expressa em milissegundos. [Saiba mais acerca da métrica Tempo total de bloqueio](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Os tempos de ida e volta (RTT) da rede têm um grande impacto no desempenho. Se o RTT para uma origem for elevado, é uma indicação de que os servidores mais próximos do utilizador podem melhorar o desempenho. [Saiba mais acerca do tempo de ida e volta](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Tempos de ida e volta da rede"}, "core/audits/network-server-latency.js | description": {"message": "As latências do servidor podem afetar o desempenho Web. Se a latência do servidor de uma origem for elevada, é uma indicação de que o servidor está sobrecarregado ou tem um fraco desempenho de back-end. [Saiba mais acerca do tempo de resposta do servidor](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Latências de back-end do servidor"}, "core/audits/no-unload-listeners.js | description": {"message": "O evento `unload` não é acionado de forma fiável e detetá-lo pode impedir otimizações do navegador, como a cache para a frente/para trás. Opte por usar eventos `pagehide` ou `visibilitychange`. [Saiba mais acerca dos ouvintes de eventos unload](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Regista um ouvinte `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "<PERSON><PERSON><PERSON> ou<PERSON><PERSON> de eventos `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "As animações que não são compostas podem ser de má qualidade e aumentar o CLS. [Saiba como evitar animações não compostas](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# elemento animado encontrado}other{# elementos animados encontrados}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "A propriedade relacionada com o filtro pode mover píxeis"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "O destino tem outra animação incompatível"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "O efeito tem um modo composto diferente de \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "Evite animações não compostas"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "A propriedade relacionada com a transformação depende do tamanho da caixa"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Propriedade CSS não suportada: {properties}}other{Propriedades CSS não suportadas: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "O efeito tem parâmetros de tempo não suportados"}, "core/audits/performance-budget.js | description": {"message": "Mantenha a quantidade e o tamanho dos pedidos de rede abaixo dos alvos estabelecidos pelo orçamento de desempenho disponibilizado. [Saiba mais acerca dos orçamentos de desempenho](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 pedido}other{# pedidos}}"}, "core/audits/performance-budget.js | title": {"message": "Orçamento de desempenho"}, "core/audits/preload-fonts.js | description": {"message": "Faça o pré-carregamento dos tipos de letra `optional` para que os novos visitantes possam usá-los. [Saiba mais acerca do pré-carregamento dos tipos de letra](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Os tipos de letra com `font-display: optional` não foram pré-carregados"}, "core/audits/preload-fonts.js | title": {"message": "Os tipos de letra com `font-display: optional` foram pré-carregados"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Se o elemento de LCP for dinamicamente adicionado à página, deve pré-carregar a imagem para melhorar o elemento de LCP. [Saiba mais acerca do pré-carregamento de elementos de LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Pré-carregue a imagem de Maior preenchimento com conteúdo"}, "core/audits/redirects.js | description": {"message": "Os redirecionamentos introduzem atrasos adicionais antes do carregamento da página. [Saiba como evitar redirecionamentos de páginas](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Evite vários redirecionamentos de página"}, "core/audits/seo/canonical.js | description": {"message": "Os links canónicos sugerem o URL a apresentar nos resultados da pesquisa. [Saiba mais acerca dos links canónicos](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Vários URLs em conflito ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL inválido ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Aponta para outra localização `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Não é um URL absoluto ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Aponta para o URL raiz do domínio (a página inicial), em vez de uma página de conteúdo equivalente."}, "core/audits/seo/canonical.js | failureTitle": {"message": "O documento não tem um `rel=canonical` válido"}, "core/audits/seo/canonical.js | title": {"message": "O documento tem um `rel=canonical` válido"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "O link não pode ser rastreado"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Os motores de pesquisa podem usar atributos `href` em links para rastrear Websites. Confirme que o atributo `href` de elementos de âncora liga a um destino adequado, para que possam ser descobertas mais páginas do site. [Saiba como tornar os links rastreáveis](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Os links não podem ser rastreados"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Os links podem ser rastreados"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Texto ilegível adicional"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% do texto da página"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Os tamanhos de tipo de letra inferiores a 12 px são demasiado pequenos para serem legíveis e requerem que os visitantes de dispositivos móveis \"juntem os dedos para aumentar o zoom\" para conseguirem ler. Tente ter > 60% de texto da página ≥ 12 px. [Saiba mais acerca dos tamanhos de tipos de letra legíveis](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} de texto legível"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "O texto é ilegível porque não existe nenhuma metatag de área visível otimizada para ecrãs de dispositivos móveis."}, "core/audits/seo/font-size.js | failureTitle": {"message": "O documento não utiliza tamanhos de tipo de letra legíveis"}, "core/audits/seo/font-size.js | legibleText": {"message": "Texto legível"}, "core/audits/seo/font-size.js | title": {"message": "O documento utiliza tamanhos de tipo de letra legíveis"}, "core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON> links hreflang indicam aos motores de pesquisa a versão de uma página que devem apresentar nos resultados da pesquisa para um determinado idioma ou região. [Saiba mais sobre `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "O documento não tem um `hreflang` válido"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Valor href relativo"}, "core/audits/seo/hreflang.js | title": {"message": "O documento tem um `hreflang` v<PERSON>lido"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Código de idioma inesperado"}, "core/audits/seo/http-status-code.js | description": {"message": "As páginas com Códigos de estado HTTP sem êxito podem não ser indexadas corretamente. [Saiba mais acerca dos Códigos de estado HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "A página tem código de estado HTTP não executado com êxito"}, "core/audits/seo/http-status-code.js | title": {"message": "A página tem código de estado HTTP executado com êxito"}, "core/audits/seo/is-crawlable.js | description": {"message": "Os motores de pesquisa não podem incluir as suas páginas nos resultados da pesquisa se não tiverem autorização para as rastrear. [Saiba mais acerca das diretivas do motor de rastreio](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "A página está impedida de ser indexada"}, "core/audits/seo/is-crawlable.js | title": {"message": "A página não está impedida de ser indexada"}, "core/audits/seo/link-text.js | description": {"message": "O texto descritivo dos links ajuda os motores de pesquisa a compreender o conteúdo. [Saiba como tornar os links mais acessíveis](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link encontrado}other{# links encontrados}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON> links não têm texto descritivo"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON>s links têm texto descritivo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Execute a [Ferramenta de teste de dados estruturados](https://search.google.com/structured-data/testing-tool/) e o [Linter de dados estruturados](http://linter.structured-data.org/) para validar dados estruturados. [Saiba mais acerca dos dados estruturados](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Os dados estruturados são válidos"}, "core/audits/seo/meta-description.js | description": {"message": "As meta descrições podem ser incluídas nos resultados da pesquisa para resumir concisamente o conteúdo da página. [Saiba mais acerca da meta descrição](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "O texto da descrição está vazio."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "O documento não tem uma meta descrição"}, "core/audits/seo/meta-description.js | title": {"message": "O documento tem uma meta descrição"}, "core/audits/seo/plugins.js | description": {"message": "Não é possível aos motores de pesquisa indexar o conteúdo de plug-ins, e muitos dispositivos restringem plug-ins ou não os suportam. [Saiba como evitar plug-ins](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "O documento utiliza plug-ins"}, "core/audits/seo/plugins.js | title": {"message": "O documento evita plug-ins"}, "core/audits/seo/robots-txt.js | description": {"message": "Se o ficheiro robots.txt estiver mal formado, os motores de rastreio podem não conseguir compreender como quer que o seu Website seja rastreado ou indexado. [Saiba mais acerca do ficheiro robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "O pedido de robots.txt devolveu o seguinte estado de HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 erro encontrado}other{# erros encontrados}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "O Lighthouse não conseguiu transferir um ficheiro robots.txt."}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "O ficheiro robots.txt não é válido"}, "core/audits/seo/robots-txt.js | title": {"message": "O ficheiro robots.txt é válido"}, "core/audits/seo/tap-targets.js | description": {"message": "Os elementos interativos, como botões e links, devem ser suficientemente grandes (48 x 48 px) ou ter espaço suficiente à volta para serem fáceis de tocar sem que se sobreponham a outros elementos. [Saiba mais acerca dos alvos táteis](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "Os alvos táteis foram dimensionados corretamente com um tamanho de {decimalProportion, number, percent}"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Os alvos táteis são demasiado pequenos porque não existe nenhuma metatag de área visível otimizada para ecrãs de dispositivos móveis."}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Os alvos táteis não estão dimensionados corretamente"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Alvo sobreposto"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "Os alvos táteis estão dimensionados corretamente"}, "core/audits/server-response-time.js | description": {"message": "Mantenha o tempo de resposta do servidor para o documento principal curto porque todos os outros pedidos dependem do mesmo. [Saiba mais acerca da métrica Tempo até ao primeiro byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "O documento de raiz demorou {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Reduza o tempo de resposta do servidor inicial"}, "core/audits/server-response-time.js | title": {"message": "O tempo de resposta do servidor inicial foi curto"}, "core/audits/splash-screen.js | description": {"message": "Um ecrã inicial temático garante uma experiência de alta qualidade quando os utilizadores iniciam a app a partir dos respetivos ecrãs principais. [Saiba mais acerca dos ecrãs iniciais](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Não está configurado para um ecrã inicial personalizado"}, "core/audits/splash-screen.js | title": {"message": "Configurado para um ecrã inicial personalizado"}, "core/audits/themed-omnibox.js | description": {"message": "A barra de endereço do navegador pode ter um tema que corresponda ao seu site. [Saiba mais acerca da associação por temas da barra de endereço](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Não define uma cor do tema para a barra de endereço."}, "core/audits/themed-omnibox.js | title": {"message": "Define uma cor do tema para a barra de endereço"}, "core/audits/third-party-cookies.js | description": {"message": "O suporte de cookies de terceiros vai ser removido numa versão futura do Chrome. [Saiba mais acerca da descontinuação dos cookies de terceiros](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{1 cookie encontrado}other{# cookies encontrados}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Usa cookies de terceiros"}, "core/audits/third-party-cookies.js | title": {"message": "Evita cookies de terceiros"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (êxito para os clientes)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (social)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (vídeo)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produ<PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Algumas incorporações de terceiros podem ser carregadas em diferido. Considere a sua substituição por uma fachada até serem necessárias. [Saiba como adiar terceiros com uma fachada](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# fachada alternativa disponível}other{# fachadas alternativas disponíveis}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Alguns recursos de terceiros podem ser carregados em diferido com uma fachada"}, "core/audits/third-party-facades.js | title": {"message": "Recursos de carregamento em diferido de terceiros com fachadas"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Te<PERSON><PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "O código de terceiros pode afetar significativamente o desempenho de carregamento. Limite o número de fornecedores terceiros redundantes e tente carregar o código de terceiros após a conclusão do carregamento da sua página. [Saiba como minimizar o impacto de terceiros](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "O código de terceiros bloqueou o thread principal durante {timeInMs, number, milliseconds} ms."}, "core/audits/third-party-summary.js | failureTitle": {"message": "Reduza o impacto do código de terceiros"}, "core/audits/third-party-summary.js | title": {"message": "Minimize a utilização de terceiros"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Medição"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "M<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Defina um orçamento de tempo para controlar o desempenho do seu site. Os sites com bom desempenho são carregados rapidamente e respondem aos eventos de entrada do utilizador com brevidade. [Saiba mais acerca dos orçamentos de desempenho](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Orçamento de tempo"}, "core/audits/unsized-images.js | description": {"message": "Defina uma largura e uma altura explícitas nos elementos de imagem para reduzir mudanças de esquema e melhorar o CLS. [Saiba como definir dimensões das imagens](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Os elementos de imagem não têm `width` e `height` explícitas."}, "core/audits/unsized-images.js | title": {"message": "Os elementos de imagem têm `width` e `height` explícitas"}, "core/audits/user-timings.js | columnType": {"message": "Tipo"}, "core/audits/user-timings.js | description": {"message": "Pondere a possibilidade de complementar a sua app com a API User Timing para analisar o desempenho real da app durante as principais experiências do utilizador. [Saiba mais acerca das marcas de User Timing](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 tempo do utilizador}other{# tempos do utilizador}}"}, "core/audits/user-timings.js | title": {"message": "Marcas e medições de Tempos do utilizador"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Foi encontrado um atributo `<link rel=preconnect>` para \"{securityOrigin}\", mas este não foi utilizado pelo navegador. Confirme se está a utilizar o atributo `crossorigin` adequadamente."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Considere adicionar instruções para recursos de `preconnect` ou `dns-prefetch` para estabelecer ligações antecipadamente a origens de terceiros importantes. [Saiba como realizar a pré-ligação às origens necessárias](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Efetue a pré-ligação às origens necessárias"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Foram encontradas mais de 2 ligações `<link rel=preconnect>`. Estas ligações devem ser utilizadas com moderação e apenas para as origens mais importantes."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Foi encontrado um atributo `<link rel=preconnect>` para \"{securityOrigin}\", mas este não foi utilizado pelo navegador. Apenas utilize o atributo `preconnect` para origens importantes que a página irá certamente solicitar."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Foi encontrado um atributo `<link>` de pré-carregamento para \"{preloadURL}\", mas este não foi utilizado pelo navegador. Confirme se está a utilizar o atributo `crossorigin` adequadamente."}, "core/audits/uses-rel-preload.js | description": {"message": "Considere usar `<link rel=preload>` para dar prioridade à obtenção de recursos que são atualmente pedidos mais tarde no carregamento de página. [Saiba como pré-carregar pedidos-chave](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Pré-carregue pedidos-chave"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL do mapa"}, "core/audits/valid-source-maps.js | description": {"message": "Os mapas de origem convertem o código reduzido no código-fonte original. Isto ajuda os programadores a depurar durante a produção. Além disso, o Lighthouse pode dar mais informações. Considere implementar mapas de origem para tirar partido destas vantagens. [Saiba mais acerca dos mapas de origem](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Mapas de origem em falta para JavaScript original grande"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Mapa de origem em falta em ficheiro JavaScript grande"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Aviso: 1 item em falta em `.sourcesContent`}other{Aviso: # itens em falta em `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "A página possui mapas de origem válidos"}, "core/audits/viewport.js | description": {"message": "Uma metatag `<meta name=\"viewport\">` não só otimiza a sua app para tamanhos de ecrãs de dispositivos móveis, como também evita [um atraso de 300 milissegundos na introdução do utilizador](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Saiba mais acerca da utilização da metatag de área visível](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Nenhuma etiqueta `<meta name=\"viewport\">` encontrada."}, "core/audits/viewport.js | failureTitle": {"message": "Não tem uma etiqueta `<meta name=\"viewport\">` com `width` ou `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Tem uma etiqueta `<meta name=\"viewport\">` com `width` ou `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Este é o trabalho de bloqueio de threads que ocorre durante a medição da interação até ao preenchimento seguinte. [Saiba mais acerca da métrica Interação até ao preenchimento seguinte](https://web.dev/articles/inp)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms gastos no evento \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Alvo do evento"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimize o trabalho durante a interação principal"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Atraso na entrada"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Atraso na apresentação"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Tempo de processamento"}, "core/audits/work-during-interaction.js | title": {"message": "Minimiza o trabalho durante a interação principal"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Estas são oportunidades para otimizar a utilização do ARIA na sua aplicação, que pode melhorar a experiência dos utilizadores de tecnologias de assistência, como os de leitores de ecrã."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Estas são oportunidades para fornecer conteúdo alternativo para áudio e vídeo. Pode melhorar a experiência dos utilizadores com deficiências auditivas ou visuais."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Áudio e vídeo"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "<PERSON><PERSON><PERSON> itens realçam as práticas recomendadas de acessibilidade comuns."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Práticas recomendadas"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Estas verificações realçam oportunidades de [melhorar a acessibilidade da sua app Web](https://developer.chrome.com/docs/lighthouse/accessibility/). A deteção automática só pode detetar um subconjunto de problemas e não garante a acessibilidade da sua app Web, por isso, também recomendamos [testes manuais](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Estes itens destinam-se a áreas não abrangidas por uma ferramenta de teste automatizada. Saiba mais no nosso guia sobre como [efetuar uma revisão de acessibilidade](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Acessibilidade"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Estas são oportunidades para melhorar a legibilidade do seu conteúdo."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Estas são oportunidades para melhorar a interpretação do seu conteúdo por parte dos utilizadores em locais diferentes."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalização e localização"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Estas são oportunidades para melhorar a semântica dos controlos na sua aplicação. Desta forma, poderá melhorar a experiência dos utilizadores de tecnologia de assistência, como os de leitores de ecrã."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nomes e etiquetas"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Estas são oportunidades para melhorar a navegação do teclado na sua aplicação."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegação"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Estas são oportunidades para melhorar a experiência de leitura de dados em tabelas ou listas com tecnologia de assistência, como os leitores de ecrã."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabelas e listas"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Compatibilidade de navegadores"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Práticas recomendadas"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "G<PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Confiança e segurança"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Experiência do utilizador"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Os orçamentos de desempenho definem padrões para o desempenho do seu site."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Orçamentos"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mais informações sobre o desempenho da sua aplicação. Estes números não [afetam diretamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) a pontuação de desempenho."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "O aspeto mais importante do desempenho é a rapidez de renderização dos píxeis no ecrã. Métricas principais: Primeiro preenchimento com conteúdo, Primeiro preenchimento significativo."}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Melhorias no primeiro preenchimento"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Estas sugestões podem ajudar a sua página a ser carregada mais rapidamente. As mesmas não [afetam diretamente](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) a pontuação de desempenho."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Melhore a experiência de carregamento geral para que a página responda e fique pronta a utilizar logo que possível. Métricas principais: Tempo até à interação, Índice de velocidade."}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> gera<PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Estas verificações validam os aspetos de uma app Web progressiva. [Saiba o que contribui para uma boa app Web progressiva](https://web.dev/articles/pwa-checklist)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "A [Lista de verificação de PWA](https://web.dev/articles/pwa-checklist) de referência requer estas verificações, mas as mesmas não são verificadas automaticamente pelo Lighthouse. Não afetam a sua pontuação, mas é importante que as valide manualmente."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalável"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA otimizada"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Estas verificações asseguram que a página está a seguir os conselhos básicos de otimização do motor de pesquisa. Existem vários fatores adicionais que o Lighthouse não contabiliza aqui e que podem afetar a classificação de pesquisa, incluindo o desempenho nas [Métricas essenciais da Web](https://web.dev/explore/vitals). [Saiba mais acerca do Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Execute estes verificadores adicionais no seu site para consultar mais práticas recomendadas de SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formate o HTML de uma forma que permita aos motores de rastreio compreender melhor o conteúdo da aplicação."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Práticas recomendadas para conteúdo"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para ser apresentada nos resultados da pesquisa, os motores de rastreio necessitam de acesso à sua aplicação."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreio e indexação"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Certifique-se de que as suas páginas são compatíveis com dispositivos móveis, de modo a que os utilizadores não tenham de juntar os dedos ou aumentar o zoom para lerem as páginas de conteúdo. [Saiba como tornar as páginas compatíveis com dispositivos móveis](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Compatível com dispositivos móveis"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "O dispositivo testado parece ter uma CPU mais lenta do que o Lighthouse esperava. Isto pode prejudicar a sua pontuação de desempenho. Saiba como [calibrar um multiplicador de abrandamento da CPU adequado](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "A página pode não estar a ser carregada conforme o esperado porque o seu URL de teste ({requested}) foi redirecionado para {final}. Experimente testar o segundo URL diretamente."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "A página foi carregada demasiado lentamente para terminar dentro do limite de tempo. Os resultados podem estar incompletos."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "A limpeza da cache do navegador excedeu o tempo limite. Experimente auditar novamente esta página e, se o problema persistir, comunique um erro."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Podem existir dados armazenados que estão a afetar o desempenho do carregamento nesta localização: {locations}. Audite esta página numa janela de navegação anónima para impedir esses recursos de afetarem as suas pontuações.}other{Podem existir dados armazenados que estão a afetar o desempenho do carregamento nestas localizações: {locations}. Audite esta página numa janela de navegação anónima para impedir esses recursos de afetarem as suas pontuações.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "A limpeza dos dados da origem excedeu o tempo limite. Experimente auditar novamente esta página e, se o problema persistir, comunique um erro."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Erro ao analisar o cabeçalho `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Foi detetada navegação nas páginas durante a execução. Não se recomenda a utilização do modo do período para auditar navegações nas páginas. Use o modo de navegação para auditar navegações nas páginas de forma a melhorar a atribuição de terceiros e a deteção da thread principal."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON>as as páginas carregadas através de um pedido GET são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Apenas as páginas com um código de estado de 2XX podem ser colocadas em cache."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "O Chrome detetou uma tentativa de execução do JavaScript enquanto a página estava na cache."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Atualmente, as páginas que pediram um AppBanner não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "A cache para a frente/para trás foi desativada devido a um pedido keep-alive."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "A cache para a frente/para trás foi desativada por sinalizações. Visite chrome://flags/#back-forward-cache para a ativar localmente neste dispositivo."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "A cache para a frente/para trás foi desativada pela linha de comandos."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "A cache para a frente/para trás foi desativada devido a memória insuficiente."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "A cache para a frente/para trás não é suportada pelo delegado."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "A cache para a frente/para trás foi desativada pelo pré-renderizador."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Não é possível colocar a página em cache porque tem uma instância BroadcastChannel com ouvintes registados."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Não é possível adicionar páginas com um cabeçalho cache-control:no-store à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "A cache foi limpa intencionalmente."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "A página foi removida da cache para permitir que outra página fosse colocada em cache."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Atualmente, as páginas que contêm plug-ins não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "As páginas que usam a API FileChooser não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "As páginas que usam a API File System Access não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Atualmente, as páginas que usam o Media Device Dispatcher não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Um leitor de multimédia estava em reprodução quando saiu da página."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "As páginas que usam a API MediaSession e definem um estado de reprodução não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "As páginas que usam a API MediaSession e definem controladores de ações não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "A cache para a frente/para trás foi desativada devido ao leitor de ecrã."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "As páginas que usam SecurityHandler não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "As páginas que usam a API Serial não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "As páginas que usam a API WebAuthetication não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "As páginas que usam a API WebBluetooth não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "As páginas que usam a API WebUSB não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "A cache para a frente/para trás foi desativada porque os cookies estão desativados numa página que usa `Cache-Control: no-store`."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Atualmente, as páginas que usam uma trabalhadora ou worklet dedicados não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "O documento não concluiu o carregamento antes de sair dele."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "A faixa da app estava presente quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "O Gestor de palavras-passe do Chrome estava presente quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A destilação DOM estava em curso quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "O Visualizador DOM Distiller estava presente quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "A cache para a frente/para trás foi desativada devido a extensões que usam a API de mensagens."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "As extensões com ligações de longa duração devem fechar a ligação antes de entrar na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "As extensões com ligações de longa duração tentaram enviar mensagens para frames na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "A cache para a frente/para trás foi desativada devido às extensões."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "A caixa de diálogo modal, como a caixa de diálogo da palavra-passe HTTP ou reenvio do formulário, foi apresentada para a página quando saiu dela."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "A página offline foi apresentada quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "A barra Intervenção sem memória estava presente quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Houve pedidos de autorização quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "O bloqueador de pop-ups estava presente quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON>s detalhes da Navegação segura foram apresentados quando saiu da página."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "A Navegação segura considerou esta página abusiva e bloqueou o pop-up."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Um service worker foi ativado enquanto a página estava na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "A cache para a frente/para trás foi desativada devido a um erro do documento."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Não é possível armazenar páginas com FencedFrames na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "A página foi removida da cache para permitir que outra página fosse colocada em cache."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Atualmente, as páginas que concederam acesso à stream de multimédia não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Atualmente, as páginas que usam portais não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Atualmente, as páginas que usam IdleManager não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Atualmente, as páginas com uma ligação IndexedDB aberta não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "A cache para a frente/para trás foi desativada devido a um evento IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Foram usadas APIs não elegíveis."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Atualmente, as páginas em que o `JavaScript` é injetado por extensões não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Atualmente, as páginas em que a `StyleSheet` é injetada por extensões não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON>rro interno."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "A cache para a frente/para trás foi desativada devido a um pedido keep-alive."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Atualmente, as páginas que usam bloqueio do teclado não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | loading": {"message": "A página não concluiu o carregamento antes de sair dela."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-cache à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm recursos principais com cache-control:no-store à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "A navegação foi cancelada antes de a página poder ser restaurada a partir da cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "A página foi removida da cache porque uma ligação de rede ativa recebeu demasiados dados. O Chrome limita a quantidade de dados que uma página pode receber enquanto está em cache."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Atualmente, as páginas com fetch() ou XHR em processamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "A página foi removida da cache para a frente/para trás porque um pedido de rede ativo envolveu um redirecionamento."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "A página foi removida da cache porque uma ligação de rede ficou aberta por demasiado tempo. O Chrome limita o período durante o qual uma página pode receber dados enquanto está em cache."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Não é possível adicionar páginas que não têm um cabeçalho de resposta válido à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "A navegação ocorreu num frame diferente do frame principal."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Atualmente, as páginas com transações DB indexadas em curso não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Atualmente, as páginas com um pedido de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Atualmente, as páginas com um pedido de obtenção de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Atualmente, as páginas com um pedido de rede em processamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Atualmente, as páginas com um pedido de rede XHR em processamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Atualmente, as páginas que usam PaymentManager não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Atualmente, as páginas que usam a funcionalidade ecrã no ecrã não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | portal": {"message": "Atualmente, as páginas que usam portais não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | printing": {"message": "Atualmente, as páginas que usam a IU (interface do utilizador) de impressão não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "A página foi aberta através de \"`window.open()`\" e é referenciada por outro separador ou a página abriu uma janela."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "O processo de renderização da página na cache para a frente/para trás falhou."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "O processo de renderização da página na cache para a frente/para trás foi interrompido."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Atualmente, as páginas que pediram autorizações de captura de áudio não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Atualmente, as páginas que pediram autorizações de sensores não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Atualmente, as páginas que pediram a sincronização em segundo plano ou autorizações de obtenção não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Atualmente, as páginas que pediram autorizações MIDI não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Atualmente, as páginas que pediram autorizações de notificações não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Atualmente, as páginas que pediram acesso ao armazenamento não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Atualmente, as páginas que pediram autorizações de captura de vídeo não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Apenas as páginas com um esquema de URL HTTP/HTTPS podem ser colocadas em cache."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "A página foi reivindicada por um service worker enquanto estava na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Um service worker tentou enviar um `MessageEvent` para a página na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "O registo do ServiceWorker foi anulado enquanto a página estava na cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "A página foi removida da cache para a frente/para trás devido à ativação de um service worker."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "O Chrome foi reiniciado e limpou as entradas da cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Atualmente, as páginas que usam SharedWorker não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Atualmente, as páginas que usam SpeechRecognizer não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "Atualmente, as páginas que usam SpeechSynthesis não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Um iFrame na página iniciou uma navegação que não foi concluída."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Não é possível adicionar as páginas que têm sub-recursos com cache-control:no-cache à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Não é possível adicionar as páginas que têm sub-recursos com cache-control:no-store à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | timeout": {"message": "A página excedeu o tempo máximo na cache para a frente/para trás e expirou."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "A página expirou ao ser adicionada à cache para a frente/para trás (provavelmente devido aos controladores pagehide em execução há muito tempo)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "A página tem um controlador de descarregamento no frame principal."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "A página tem um controlador de descarregamento num subframe."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "O navegador alterou o cabeçalho de substituição do agente do utilizador."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "Atualmente, as páginas que concederam acesso à gravação de vídeo ou áudio não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Atualmente, as páginas que usam WebDatabase não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Atualmente, as páginas que usam WebHID não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Atualmente, as páginas que usam WebLocks não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Atualmente, as páginas que usam WebNfc não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Atualmente, as páginas que usam WebOTPService não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Não é possível adicionar páginas com WebRTC à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Atualmente, as páginas que usam WebShare não são elegíveis para a cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Não é possível adicionar páginas com WebSocket à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Não é possível adicionar páginas com WebTransport à cache para a frente/para trás."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Atualmente, as páginas que usam WebXR não são elegíveis para a cache para a frente/para trás."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Considere adicionar esquemas de URL https: e http: (ignorados por navegadores que suportam `'strict-dynamic'`) para serem retrocompatíveis com navegadores mais antigos."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "A diretiva `disown-opener` foi descontinuada desde a CSP3. Em alternativa, use o cabeçalho Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "A diretiva `referrer` foi descontinuada desde a CSP2. Em alternativa, use o cabeçalho Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "A diretiva `reflected-xss` foi descontinuada desde a CSP2. Em alternativa, use o cabeçalho X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "A diretiva `base-uri` em falta permite que as etiquetas `<base>` injetadas definam o URL de base para todos os URLs relativos (por exemplo, scripts) como um domínio controlado por um atacante. Considere definir `base-uri` como `'none'` ou `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "A diretiva `object-src` em falta permite a injeção de plug-ins que executam scripts inseguros. Considere definir a diretiva `object-src` como `'none'`, se possível."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "A diretiva `script-src` está em falta. Isto pode permitir a execução de scripts inseguros."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Esqueceu-se do ponto e vírgula? {keyword} parece ser uma diretiva e não uma palavra-chave."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Os nonces devem utilizar o charset base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Os nonces devem ter, pelo menos, 8 carateres."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Evite utilizar esquemas de URL simples ({keyword}) nesta diretiva. Os esquemas de URL simples permitem que os scripts sejam obtidos a partir de um domínio inseguro."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Evite utilizar carateres universais simples ({keyword}) nesta diretiva. Os carateres universais simples permitem que os scripts sejam obtidos a partir de um domínio inseguro."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "O destino de relatórios só é configurado através da diretiva report-to. Esta diretiva só é suportada em navegadores baseados no Chromium, pelo que se recomenda também a utilização de uma diretiva `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Nenhuma CSP configura um destino de relatórios. Isto dificulta a manutenção da CSP ao longo do tempo e a monitorização de quaisquer quebras."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "As listas de autorizações de anfitrião podem ser frequentemente ignoradas. Em alternativa, considere usar nonces ou hashes CSP, bem como `'strict-dynamic'`, se precisar."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Diretiva CSP desconhecida."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} parece ser uma palavra-chave inválida."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "A diretiva `'unsafe-inline'` permite a execução de scripts na página e de controladores de eventos inseguros. Considere usar nonces ou hashes CSP para permitir scripts individualmente."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Considere adicionar `'unsafe-inline'` (ignorado por navegadores que suportam nonces/hashes) para ser retrocompatível com navegadores mais antigos."}, "core/lib/deprecation-description.js | feature": {"message": "Verifique a página de estado da funcionalidade para obter mais de<PERSON>hes."}, "core/lib/deprecation-description.js | milestone": {"message": "Esta alteração vai entrar em vigor com o marco {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "Funcionalidade descontinuada usada"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "A autorização não vai estar abrangida pelo símbolo de caráter universal (*) no processamento do atributo `Access-Control-Allow-Headers` de CORS."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "Deve usar o atributo `disableRemotePlayback` em vez do seletor `-internal-media-controls-overlay-cast-button` para desativar a integração do Cast predefinida."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Os pedidos de recursos cujos URLs continham os carateres `(n|r|t)` de espaços em branco removidos e os carateres \"menos de\" (`<`) foram bloqueados. Remova as novas linhas e codifique os carateres \"menos de\" de locais como valores de atributos de elementos para carregar estes recursos."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "O atributo `chrome.loadTimes()` foi descontinuado. Em alternativa, use a API padronizada: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "O atributo `chrome.loadTimes()` foi descontinuado. Em alternativa, use a API padronizada: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "O atributo `chrome.loadTimes()` foi descontinuado. Em alternativa, use a API padronizada: `nextHopProtocol` no Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Os cookies que contêm um caráter `(0|r|n)` vão ser rejeitados em vez de truncados."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "O relaxamento da política da mesma origem ao definir o atributo `document.domain` foi descontinuado e vai ser desativado por predefinição. Este aviso de descontinuação é para um acesso de origem cruzada que foi ativado ao definir o atributo `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "O acionamento da função window.alert a partir de iFrames de origem cruzada foi descontinuado e vai ser removido no futuro."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "O acionamento da função window.confirm a partir de iFrames de origem cruzada foi descontinuado e vai ser removido no futuro."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "Os eventos de mutação DOM, incluindo `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` e `DOMCharacterDataModified` foram descontinuados (https://w3c.github.io/uievents/#legacy-event-types) e vão ser removidos. Em alternativa, use `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "O suporte de URLs data: no elemento <use> dos SVG (Gráficos de vetor dimensionáveis) foram descontinuados e vão ser removidos no futuro."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "O relaxamento da política da mesma origem ao definir o atributo `document.domain` foi descontinuado e vai ser desativado por predefinição. Para continuar a usar esta funcionalidade, recuse os clusters de agentes de chaves de origem ao enviar um cabeçalho `Origin-Agent-Cluster: ?0` em conjunto com a resposta HTTP para o documento e os frames. Consulte https://developer.chrome.com/blog/immutable-document-domain/ para obter mais detalhes."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "O cabeçalho de `Expect-CT` foi descontinuado e vai ser removido. O Chrome requer a Transparência de certificados para todos os certificados publicamente fidedignos emitidos após 30 de abril de 2018."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "Os atributos `getCurrentPosition()` e `watchPosition()` já não funcionam em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Os atributos `getCurrentPosition()` e `watchPosition()` foram descontinuados em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "O atributo `getUserMedia()` já não funciona em origens inseguras. Para usar esta funcionalidade, deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "O atributo `RTCPeerConnectionIceErrorEvent.hostCandidate` foi descontinuado. Em alternativa, use o atributo `RTCPeerConnectionIceErrorEvent.address` ou `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "A origem do comerciante e os dados arbitrários do evento do service worker `canmakepayment` foram descontinuados e vão ser removidos: `topOrigin`, `paymentRequestOrigin`, `methodData` e `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "O Website pediu um sub-recurso de uma rede à qual só podia aceder devido à posição da rede privilegiada dos respetivos utilizadores. Estes pedidos expõem servidores e dispositivos não públicos à Internet, aumentando o risco de um ataque de falsificação de pedido entre sites (CSRF) e/ou uma fuga de informações. Para mitigar estes riscos, o Chrome descontinua pedidos para sub-recursos não públicos quando forem iniciados por contextos não seguros e vai começar a bloqueá-los."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "O nome do campo `dailyUpdateUrl` de `InterestGroups` transmitido a `joinAdInterestGroup()` foi mudado para `updateUrl`, de modo a refletir com maior precisão o respetivo comportamento."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Não é possível carregar o CSS a partir dos URLs `file:`, exceto se terminarem numa extensão de ficheiro `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "A utilização do atributo `SourceBuffer.abort()` para interromper a remoção do intervalo assíncrono do atributo `remove()` foi descontinuado devido a uma alteração na especificação. O suporte vai ser removido no futuro. Em alternativa, deve ouvir o evento `updateend`. O atributo `abort()` destina-se apenas a interromper um estado de analisador de reposição ou anexação de suporte assíncrono."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "A definição do atributo `MediaSource.duration` abaixo da data/hora de apresentação mais elevada de quaisquer frames codificados no buffer foi descontinuada devido a uma alteração na especificação. O suporte para uma remoção implícita do suporte no buffer truncado vai ser removido no futuro. Em alternativa, deve executar um atributo `remove(newDuration, oldDuration)` explícito em todos os `sourceBuffers`, em que `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "A MIDI da Web vai pedir uma autorização de utilização mesmo que o sysex não esteja especificado no atributo `MIDIOptions`."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "O atributo `shadowroot` não padronizado e mais antigo foi descontinuado e *vai deixar de funcionar* no M119. Em alternativa, use o novo atributo `shadowrootmode` padronizado."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "A API Notification já não pode ser usada a partir de origens inseguras. Deve considerar mudar a sua aplicação para uma origem segura, como HTTPS. Consulte https://goo.gle/chrome-insecure-origins para obter mais detalhes."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "A autorização para a API Notification pode já não ser pedida a partir de um iFrame de origem cruzada. Em alternativa, deve considerar pedir autorização a partir de um frame de nível superior ou abrir uma nova janela."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "A opção `imageOrientation: 'none'` em createImageBitmap foi descontinuada. Em alternativa, use createImageBitmap com a opção \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "O seu parceiro está a negociar uma versão do (D)TLS obsoleta. Contacte o seu parceiro para corrigir isto."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Especificar `overflow: visible` em etiquetas img, video e canvas pode fazer com que produzam conteúdo visual fora dos limites do elemento. Consulte https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "A API `paymentManager.instruments` foi descontinuada. Em alternativa, use a instalação just-in-time para os controladores de pagamento."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "A sua chamada de `PaymentRequest` ignorou a diretiva da Política de Segurança de Conteúdos (CSP) `connect-src`. Esta ação de ignorar foi descontinuada. Adicione o identificador do método de pagamento da API `PaymentRequest` (no campo `supportedMethods`) à diretiva da CSP `connect-src`."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "O atributo `StorageType.persistent` foi descontinuado. Em alternativa, use o atributo `navigator.storage` padronizado."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "O atributo `<source src>` com um `<picture>` superior é inválido e, por isso, vai ser ignorado. Em alternativa, use `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "O método webkitCancelAnimationFrame é específico do fornecedor. Em alternativa, use o método cancelAnimationFrame padrão."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "O método webkitRequestAnimationFrame é específico do fornecedor. Em alternativa, use o método requestAnimationFrame padrão."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "A função HTMLVideoElement.webkitDisplayingFullscreen foi descontinuada. Em alternativa, use Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "A API HTMLVideoElement.webkitEnterFullScreen() foi descontinuada. Em alternativa, use a API Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "A API HTMLVideoElement.webkitEnterFullscreen() foi descontinuada. Em alternativa, use a API Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "A API HTMLVideoElement.webkitExitFullScreen() foi descontinuada. Em alternativa, use a API Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "A API HTMLVideoElement.webkitExitFullscreen() foi descontinuada. Em alternativa, use a API Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "A função webkitSupportsFullscreen foi descontinuada. Em alternativa, use a função Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Vamos descontinuar a API `chrome.privacy.websites.privacySandboxEnabled`, mas esta permanece ativa para retrocompatibilidade até ao lançamento M113. Em alternativa, use `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` e `chrome.privacy.websites.adMeasurementEnabled`. Veja https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "A restrição `DtlsSrtpKeyAgreement` foi removida. Especificou um valor `false` para esta restrição, que é interpretado como uma tentativa de usar o método `SDES key negotiation` removido. Esta funcionalidade foi removida. Em alternativa, use um serviço que suporte o atributo `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "A restrição `DtlsSrtpKeyAgreement` foi removida. Especificou um valor `true` para esta restrição, que não teve qualquer efeito, mas pode remover a restrição para fins de organização."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "O método getStats() baseado em chamadas de resposta foi descontinuado e vai ser removido. Em alternativa, use o método getStats() em conformidade com a especificação."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "A API Range.expand() foi descontinuada. Em alternativa, use a API Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Os pedidos de sub-recursos cujos URLs continham credenciais incorporadas (por exemplo, `**********************/`) foram bloqueados."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "A opção `rtcpMuxPolicy` foi descontinuada e vai ser removida."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "O atributo `SharedArrayBuffer` vai requerer isolamento de origem cruzada. Consulte https://developer.chrome.com/blog/enabling-shared-array-buffer/ para obter mais detalhes."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "O atributo `speechSynthesis.speak()` sem a ativação do utilizador foi descontinuado e vai ser removido."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "As extensões devem aceitar o isolamento de origem cruzada para continuar a usar o atributo `SharedArrayBuffer`. Consulte https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "O SQL da Web foi descontinuado. Use SQLite WebAssembly ou Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "O descritor de autorizações `window-placement` foi descontinuado. Em alternativa, use `window-management`. Para obter mais ajuda, consulte https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "A política de autorizações `window-placement` foi descontinuada. Em alternativa, use `window-management`. Para obter mais ajuda, consulte https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "O formato UTF-16 não é suportado por JSON de resposta no atributo `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "A API `XMLHttpRequest` síncrona na thread principal foi descontinuada devido aos respetivos efeitos adversos na experiência do utilizador final. Para obter mais ajuda, consulte https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "O atributo `supportsSession()` foi descontinuado. Em alternativa, use o atributo `isSessionSupported()` e verifique o valor booleano resolvido."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tempo de bloqueio do thread principal"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL da cache"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Descrição"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Duração"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elemento"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Elementos reprovados"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Localização"}, "core/lib/i18n/i18n.js | columnName": {"message": "Nome"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Superior ao orçamento"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Pedidos"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Tamanho do recurso"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Fonte"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Hora de início"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tempo gasto"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Poupança potencial"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Poupança potencial"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Poupança potencial de {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elemento encontrado}other{# elementos encontrados}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Poupança potencial de {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Primeiro preenchimento significativo"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON> de letra"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagem"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interação até ao preenchimento seguinte"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Alto"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Baixo"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Médio"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Máximo potencial de primeiro atraso de entrada"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multimédia"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Outro"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Outros recursos"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Folha de estilos"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Te<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Ocorreu um erro ao registar o rastreio durante o carregamento da sua página. Volte a executar o Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Atingido o tempo limite de espera pela ligação inicial do protocolo do depurador."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "O Chrome não recolheu quaisquer capturas de ecrã durante o carregamento da página. Certifique-se de que existe conteúdo visível na página e, em seguida, experimente executar novamente o Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Os servidores DNS não conseguiram resolver o domínio fornecido."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "O coletor {artifactName} obrigatório encontrou o seguinte erro: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Ocorreu um erro interno do Chrome. Reinicie o Chrome e experimente executar novamente o Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "O coletor {artifactName} obrigatório não foi executado."}, "core/lib/lh-error.js | noFcp": {"message": "A página não preencheu nenhum conteúdo. Certifique-se de que mantém a janela do navegador em primeiro plano durante o carregamento e tente novamente. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "A página não apresentava conteúdo qualificado como Maior preenchimento com conteúdo (LCP). Certifique-se de que a página tem um elemento de LCP válido e tente novamente. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "A página fornecida não é HTML (publicada como tipo MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Esta versão do Chrome é demasiado antiga para suportar \"{featureName}\". Utilize uma versão mais recente para ver os resultados completos."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "O Lighthouse não conseguiu carregar com fiabilidade a página que solicitou. Certifique-se de que está a testar o URL correto e que o servidor está a responder adequadamente a todos os pedidos."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "O Lighthouse não conseguiu carregar com fiabilidade o URL que solicitou porque a página deixou de responder."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "O URL que forneceu não tem um certificado de segurança válido. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "O Chrome impediu o carregamento da página com um anúncio intercalar. Certifique-se de que está a testar o URL correto e que o servidor está a responder adequadamente a todos os pedidos."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "O Lighthouse não conseguiu carregar com fiabilidade a página que solicitou. Certifique-se de que está a testar o URL correto e que o servidor está a responder adequadamente a todos os pedidos. (Detalhes: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "O Lighthouse não conseguiu carregar com fiabilidade a página que pediu. Certifique-se de que está a testar o URL correto e que o servidor está a responder adequadamente a todos os pedidos. (Código de estado: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "A sua página demorou demasiado tempo a ser carregada. Siga as oportunidades no relatório para reduzir o tempo de carregamento da página e, em seguida, experimente executar novamente o Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "A espera pela resposta do protocolo DevTools excedeu o tempo atribuído. (Método: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "A obtenção de conteúdo de recursos excedeu o tempo atribuído."}, "core/lib/lh-error.js | urlInvalid": {"message": "O URL que forneceu parece ser inválido."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "O Lighthouse não conseguiu carregar com fiabilidade a página que pediu. Certifique-se de que está a testar o URL correto e que o servidor está a responder adequadamente a todos os pedidos. (Código de estado: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "O tipo MIME da página é XHTML: o Lighthouse não suporta explicitamente este tipo de documento"}, "core/user-flow.js | defaultFlowName": {"message": "Fluxo do utilizador ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Relatório de navegação ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Relatório de resumo ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Relatório de período ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Todos os relatórios"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Categorias"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Acessibilidade"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Práticas recomendadas"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive web app"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Computador"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Compreender o relatório do fluxo do Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Compreender os fluxos"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Usar relatórios de navegação para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Usar relatórios de resumo para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Usar relatórios de período para…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Obter uma pontuação de desempenho do Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Medir métricas de desempenho de carregamento de página como Maior preenchimento com conteúdo e Índice de velocidade."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Avaliar capacidades de apps Web progressivas."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Detetar problemas de acessibilidade em aplicações de página única ou formulários complexos."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Avaliar as práticas recomendadas de menus e elementos da UI ocultos atrás da interação."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Medir as mudanças de esquema e o tempo de execução de JavaScript numa série de interações."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Descobrir oportunidades de desempenho para melhorar a experiência de páginas de longa duração e aplicações de página única."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} auditoria informativa}other{{numInformative} auditorias informativas}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Dispositivos móveis"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Carregamento de página"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Os relatórios de navegação analisam um carregamento de página único, exatamente como os relatórios originais do Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Relatório de navegação"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} relatório de navegação}other{{numNavigation} relatórios de navegação}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} auditoria que pode ser aprovada}other{{numPassableAudits} auditorias que podem ser aprovadas}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} auditoria aprovada}other{{numPassed} auditorias aprovadas}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Média"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Erro"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Fraca"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Bo<PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Guardar"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Estado da página capturado"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Os relatórios de resumo analisam a página num estado específico, normalmente após interações do utilizador."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Relatório de resumo"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} relatório de resumo}other{{numSnapshot} relatórios de resumo}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Resumo"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interações do utilizador"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Os relatórios de período analisam um período arbitrário que, normalmente, contém interações do utilizador."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Relatório de período"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} relatório de período}other{{numTimespan} relatórios de período}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Relatório do fluxo do utilizador do Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Para conteúdo animado, utilize [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) para minimizar a utilização da CPU quando o conteúdo não está visível."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Considere apresentar todos os seus componentes [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) em formatos WebP, especificando, ao mesmo tempo, uma alternativa apropriada para outros navegadores. [<PERSON><PERSON> ma<PERSON>](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Certifique-se de que está a utilizar [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) para que as imagens efetuem o carregamento em diferido automaticamente. [Sai<PERSON> mais](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Utilize ferramentas como o [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) para [renderizar esquemas AMP do lado do servidor](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Consulte a [documentação de AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) para garantir que todos os estilos são suportados."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "O componente [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) suporta o atributo [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) para especificar os recursos de imagem a utilizar com base no tamanho do ecrã. [Saiba mais](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Considere o deslocamento virtual com o Component Dev Kit (CDK) se estiverem a ser renderizadas listas muito grandes. [<PERSON><PERSON> mais](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Aplique a [divisão de código ao nível do encaminhamento](https://web.dev/route-level-code-splitting-in-angular/) para minimizar o tamanho dos seus pacotes de JavaScript. Considere também a pré-colocação de recursos em cache com o [service worker do Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Se estiver a utilizar a Angular CLI, assegure-se de que as compilações são geradas em modo de produção. [Saiba mais](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Se estiver a utilizar a CLI do Angular, inclua mapas de origem na compilação de produção para inspecionar os seus pacotes. [Saiba mais](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Pré-carregue antecipadamente os encaminhamentos para acelerar a navegação. [Sai<PERSON> mais](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Considere utilizar o utilitário `BreakpointObserver` no Component Dev Kit (CDK) para gerir breakpoints de imagem. [<PERSON><PERSON> mais](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Considere carregar o GIF para um serviço que o disponibilizará para incorporação como vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Especifique `@font-display` quando definir tipos de letra personalizados no seu tema."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Considere configurar os [formatos de imagem WebP com a opção Converter estilo da imagem](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) no seu site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Instale [um módulo do Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) capaz de efetuar o carregamento em diferido de imagens. Esses módulos permitem adiar o carregamento de quaisquer imagens não apresentadas no ecrã para melhorar o desempenho."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Considere usar um módulo para colocar CSS e JavaScript crítico inline e usar o atributo defer para CSS ou JavaScript não crítico."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "As especificações dos temas, módulos e servidor, no seu conjunto, contribuem para o tempo de resposta do servidor. Considere procurar um tema mais otimizado, selecionar cuidadosamente um módulo de otimização e/ou atualizar o servidor. Os seus servidores de alojamento devem utilizar a colocação em cache de código de operação PHP ao colocá-lo na cache da memória para reduzir os tempos de consulta das bases de dados, em sistemas como o Redis ou o Memcached, bem como a lógica de aplicação otimizada para preparar páginas mais rapidamente."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Considere utilizar [estilos de imagens adaptáveis](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) para reduzir o tamanho das imagens carregadas na sua página. Se estiver a utilizar vistas para apresentar vários itens de conteúdo numa página, considere implementar a paginação para limitar o número de itens de conteúdo apresentados numa determinada página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Certifique-se de que ativou a opção \"Agregar ficheiros CSS\" na página \"Administração » Configuração » Programação\".  Certifique-se de que o seu site do Drupal está a executar, pelo menos, o Drupal 10.1 para melhorar o suporte da agregação de recursos."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Certifique-se de que ativou a opção \"Agregar ficheiros JavaScript\" na página \"Administração » Configuração » Programação\".  Certifique-se de que o seu site do Drupal está a executar, pelo menos, o Drupal 10.1 para melhorar o suporte da agregação de recursos."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Considere remover regras de CSS não utilizadas e anexe apenas as bibliotecas do Drupal necessárias à página relevante ou a um componente numa página. Consulte o [link da documentação do Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para obter detalhes. Para identificar bibliotecas anexadas que estejam a adicionar CSS não reconhecido, experimente executar a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas DevTools do Chrome. Pode identificar o tema/módulo responsável a partir do URL da folha de estilos quando a agregação de CSS estiver desativada no seu site do Drupal. Preste atenção a temas/módulos que tenham muitas folhas de estilo na lista com muito vermelho na cobertura do código. Um tema/módulo só deve ter uma folha de estilos na fila de espera se esta for efetivamente utilizada na página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Considere remover recursos de JavaScript não utilizados e anexe apenas as bibliotecas do Drupal necessárias à página relevante ou a um componente numa página. Consulte o [link da documentação do Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) para obter detalhes. Para identificar bibliotecas anexadas que estejam a adicionar JavaScript não reconhecido, experimente executar a [utilização de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas DevTools do Chrome. Pode identificar o tema/módulo responsável a partir do URL do script quando a agregação de JavaScript estiver desativada no seu site do Drupal. Preste atenção a temas/módulos que tenham muitos scripts na lista com muito vermelho na utilização de código. Um tema/módulo só deve ter um script na fila de espera se este for efetivamente utilizado na página."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Defina a opção \"Duração máxima da cache do navegador e do proxy\" na página \"Administração » Configuração » Programação\". Leia sobre a [cache do Drupal e a otimização do desempenho](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Considere utilizar [um módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) que otimize e reduza automaticamente o tamanho das imagens carregadas em todo o site e que mantenha a qualidade das mesmas. Certifique-se também de que está a utilizar os [estilos de imagens adaptáveis](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) nativos fornecidos pelo Drupal (disponíveis no Drupal 8 e superior) para todas as imagens renderizadas no site."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "É possível adicionar instruções para recursos de pré-ligação ou obtenção prévia de DNS ao instalar e configurar [um módulo](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) que forneça estruturas para instruções de recursos de agente do utilizador."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Certifique-se de que está a utilizar os [estilos de imagens adaptáveis](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) nativos fornecidos pelo Drupal (disponíveis no Drupal 8 e superior). Utilize os estilos de imagens adaptáveis quando renderizar campos de imagens através de modos de visualização, vistas ou imagens carregadas através do editor WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Optimize Fonts` para tirar automaticamente partido da funcionalidade CSS `font-display` e garantir que o texto é visível para o utilizador enquanto os tipos de letra para Websites são carregados."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Next-Gen Formats` para converter imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Lazy Load Images` para adiar o carregamento de imagens que não aparecem no ecrã até serem necessárias."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative as definições `Critical CSS` e `Script Delay` para adiar o JS/CSS não crítico."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Utilize o [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) para colocar em cache o seu conteúdo na nossa rede mundial e melhorar o tempo até ao primeiro byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Minify CSS` para reduzir automaticamente o CSS e diminuir os tamanhos dos payloads de rede."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Minify Javascript` para reduzir automaticamente o JS e diminuir os tamanhos dos payloads de rede."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Remove Unused CSS` para ajudar com este problema. Vai identificar as classes CSS efetivamente utilizadas em cada página do seu site e remover todas as outras para manter o tamanho do ficheiro pequeno."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Efficient Static Cache Policy` para definir valores recomendados no cabeçalho da colocação em cache para recursos estáticos."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Next-Gen Formats` para converter imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Pre-Connect Origins` para adicionar automaticamente instruções para recursos `preconnect` e estabelecer ligações antecipadamente a origens de terceiros importantes."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative as definições `Preload Fonts` e `Preload Background Images` para adicionar links `preload` e dar prioridade à obtenção de recursos que são atualmente pedidos mais tarde no carregamento de página."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Utilize o [Ezoic Leap](https://pubdash.ezoic.com/speed) e ative a definição `Resize Images` para redimensionar as imagens para um tamanho adequado aos dispositivos e diminuir os tamanhos dos payloads de rede."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "Use o componente `gatsby-plugin-image`, em vez de `<img>`, para otimizar automaticamente o formato de imagem. [<PERSON><PERSON> ma<PERSON>](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "Use o componente `gatsby-plugin-image`, em vez de `<img>`, para carregar automaticamente as imagens em diferido. [<PERSON><PERSON> mais](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Use o componente `gatsby-plugin-image` e defina a propriedade `loading` como `eager`. [Sai<PERSON> mais](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Use a `Gatsby Script API` para adiar o carregamento de scripts de terceiros não críticos. [Saiba mais](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Use o plug-in `PurgeCSS` `Gatsby` para remover regras não usadas de folhas de estilos. [<PERSON><PERSON> mais](https://purgecss.com/plugins/gatsby.html)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Use o `Webpack Bundle Analyzer` para detetar código JavaScript não usado. [<PERSON><PERSON> mais](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Configure a colocação em cache para recursos imutáveis. [<PERSON><PERSON> <PERSON>](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Use o componente `gatsby-plugin-image`, em vez de `<img>`, para ajustar a qualidade da imagem. [<PERSON><PERSON> ma<PERSON>](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Use o componente `gatsby-plugin-image` para definir os `sizes` apropriados. [Sai<PERSON> mais](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Considere carregar o GIF para um serviço que o disponibilizará para incorporação como vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Considere utilizar um [plug-in](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ou serviço que converta automaticamente as imagens carregadas nos formatos ideais."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Instale um [plug-in do Joomla de carregamento em diferido](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) com capacidade para adiar imagens não visíveis ou mudar para um modelo com essa funcionalidade. A partir do Joomla 4.0, todas as imagens novas receberão [automaticamente](https://github.com/joomla/joomla-cms/pull/30748) o atributo `loading` do núcleo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Há vários plug-ins do Joomla que podem ajudar a [colocar recursos críticos inline](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ou [adiar recursos menos importantes](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Tenha em atenção que as otimizações oferecidas por estes plug-ins podem afetar funcionalidades dos seus modelos ou plug-ins, pelo que poderá ser necessário efetuar testes rigorosos."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "As especificações dos modelos, extensões e servidor, no seu conjunto, contribuem para o tempo de resposta do servidor. Considere procurar um modelo mais otimizado, selecionar cuidadosamente uma extensão de otimização e/ou atualizar o servidor."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Considere mostrar excertos nas suas categorias de artigos (por exemplo, através do link para ler mais), reduzir o número de artigos apresentados numa determinada página, dividir as publicações longas em várias páginas ou utilizar um plug-in para efetuar o carregamento em diferido de comentários."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Várias [extensõ<PERSON> do Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) podem acelerar o seu site ao concatenar, reduzir e comprimir os seus estilos de CSS. Também existem modelos que oferecem esta funcionalidade."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Várias [extensõ<PERSON> do <PERSON>omla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) podem acelerar o seu site ao concatenar, reduzir e comprimir os seus scripts. Também existem modelos que oferecem esta funcionalidade."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Considere reduzir ou mudar o número de [extensões do Joomla](https://extensions.joomla.org/) que carregam CSS não utilizado na sua página. Para identificar extensões que estejam a adicionar CSS não reconhecido, experimente executar a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas DevTools do Chrome. Pode identificar o tema/plug-in responsável a partir do URL da folha de estilos. Preste atenção a plug-ins que tenham muitas folhas de estilo na lista com muito vermelho na cobertura do código. Um plug-in só deve ter uma folha de estilos na fila de espera se esta for realmente utilizada na página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Considere reduzir ou mudar o número de [extensões do Joomla](https://extensions.joomla.org/) que carregam JavaScript não utilizado na sua página. Para identificar plug-ins que estejam a adicionar JS não reconhecido, experimente executar a [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas DevTools do Chrome. Pode identificar a extensão responsável a partir do URL do script. Preste atenção a extensões que tenham muitos scripts na lista com muito vermelho na cobertura do código. Uma extensão só deve ter um script na fila de espera se este for efetivamente utilizado na página."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Leia sobre a [Colocação na cache do navegador no Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Considere utilizar um [plug-in de otimização da imagem](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) que comprima as imagens, ao mesmo tempo que mantém a qualidade."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Considere utilizar um [plug-in de imagens adaptáveis](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) para utilizar imagens adaptáveis no seu conteúdo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Pode ativar a compressão de texto ao ativar Compressão de página Gzip no Joomla (Sistema > Configuração global > Servidor)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Se não estiver a agrupar os seus recursos de JavaScript, considere utilizar o [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Desative o [agrupamento e redução de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrados do Magento e, em alternativa, considere utilizar o [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Especifique `@font-display` quando [definir tipos de letra personalizados](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Considere pesquisar o [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) a fim de obter diversas extensões de terceiros para tirar partido de formatos de imagem mais recentes."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Considere modificar os seus modelos de produto e catálogo para tirar partido da funcionalidade de [carregamento em diferido](https://web.dev/native-lazy-loading) da plataforma Web."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Utilize a [integração com o Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) do Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Ative a opção \"Reduzir ficheiros CSS\" nas Definições do programador da sua loja. [<PERSON><PERSON> mais](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Utilize o [Terser](https://www.npmjs.com/package/terser) para reduzir todos os recursos de JavaScript provenientes da implementação de conteúdo estático e desativar a funcionalidade de redução integrada."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Desative o [agrupamento de JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrado do Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Considere pesquisar o [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) a fim de obter diversas extensões de terceiros para otimizar imagens."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "É possível adicionar instruções para recursos de pré-ligação ou de obtenção prévia de DNS ao [modificar o esquema de um tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "É possível adicionar etiquetas `<link rel=preload>` ao [modificar o esquema de um tema](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Use o componente `next/image`, em vez de `<img>`, para otimizar automaticamente o formato de imagem. [<PERSON><PERSON> mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Use o componente `next/image`, em vez de `<img>`, para carregar automaticamente as imagens em diferido. [Sai<PERSON> mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Utilize o componente `next/image` e defina \"priority\" como true para pré-carregar a imagem LCP. [<PERSON><PERSON> mais](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Utilize o componente `next/script` para adiar o carregamento de scripts de terceiros não críticos. [Sai<PERSON> mais](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Use o componente `next/image` para se certificar de que as imagens são sempre dimensionadas corretamente. [Sai<PERSON> mais](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Considere configurar o `PurgeCSS` na configuração do `Next.js` para remover regras não utilizadas de folhas de estilos. [Sai<PERSON> mais](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Use o `Webpack Bundle Analyzer` para detetar código JavaScript não usado. [<PERSON><PERSON> mais](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Considere utilizar o `Next.js Analytics` para medir o desempenho real da app. [<PERSON><PERSON> ma<PERSON>](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Configure a colocação em cache para recursos imutáveis e páginas `Server-side Rendered` (SSR). [Sai<PERSON> mais](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Use o componente `next/image`, em vez de `<img>`, para ajustar a qualidade da imagem. [<PERSON><PERSON> mais](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Utilize o componente `next/image` para definir o(s) `sizes` apropriado(s). [<PERSON><PERSON> mais](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Ative a compressão no seu servidor Next.js. [<PERSON><PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Contacte o seu gestor de conta para ativar a funcionalidade [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Ao configurá-la, dá prioridade e otimiza o desempenho da renderização da página."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "Use a opção [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) no NitroPack para definir um valor desejado para a regra de apresentação de tipos de letra CSS."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "Use a funcionalidade [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513) para converter automaticamente as suas imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Ative a funcionalidade [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images) para adiar as imagens não visíveis."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Ative a funcionalidade [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack) no NitroPack para tempos de carregamento iniciais mais rápidos."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Melhore o tempo de resposta do servidor e otimize o desempenho observado ao ativar a funcionalidade [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "Ative a funcionalidade [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) nas definições de colocação em cache para reduzir o tamanho dos ficheiros CSS, HTML e JavaScript para tempos de carregamento mais rápidos."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "Ative a funcionalidade [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources) nas definições de colocação em cache para reduzir o tamanho dos ficheiros JS, HTML e CSS para tempos de carregamento mais rápidos."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Ative a funcionalidade [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) para remover regras CSS que não são aplicáveis a esta página."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Configure a funcionalidade [`Delayed Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts) no NitroPack para atrasar o carregamento de scripts até serem necessários."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "Aceda à funcionalidade [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) no menu `Caching` e ajuste o prazo de validade da cache da página para melhorar os tempos de carregamento e a experiência do utilizador."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Ative a definição [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack) para comprimir, otimizar e converter automaticamente as suas imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "Ative a funcionalidade [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site) para otimizar preventivamente as suas imagens e torná-las correspondentes às dimensões dos contentores em que são apresentadas em todos os dispositivos."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Use a funcionalidade [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) no NitroPack para reduzir o tamanho dos ficheiros enviados para o navegador."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Use o componente `nuxt/image` e defina `format=\"webp\"`. [<PERSON><PERSON> <PERSON>](https://image.nuxt.com/usage/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Use o componente `nuxt/image` e defina `loading=\"lazy\"` para as imagens fora do ecrã. [<PERSON><PERSON> mais](https://image.nuxt.com/usage/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Use o componente `nuxt/image` e especifique o `preload` para a imagem de LCP. [Sai<PERSON> mais](https://image.nuxt.com/usage/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Use o componente `nuxt/image` e especifique explicitamente a `width` e a `height`. [<PERSON><PERSON> mais](https://image.nuxt.com/usage/nuxt-img#width-height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Use o componente `nuxt/image` e defina a `quality` apropriada. [<PERSON><PERSON> ma<PERSON>](https://image.nuxt.com/usage/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Use o componente `nuxt/image` e defina os `sizes` apropriados. [<PERSON><PERSON> mais](https://image.nuxt.com/usage/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Substitua GIFs animados por vídeo](https://web.dev/replace-gifs-with-videos/) para obter carregamentos de páginas Web mais rápidos e considere a utilização de formatos de ficheiro modernos, tais como [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ou [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), para melhorar a eficiência de compressão em mais de 30% em relação ao atual codec de vídeo de última geração, VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Considere utilizar um [plug-in](https://octobercms.com/plugins?search=image) ou serviço que converta automaticamente as imagens carregadas nos formatos ideais. As [imagens WebP sem perdas](https://developers.google.com/speed/webp) são 26% mais pequenas em termos de tamanho do que as imagens PNG e 25 a 34% mais pequenas do que as imagens JPEG comparáveis em termos de índice de qualidade SSIM equivalente. Outro formato de imagem de última geração a considerar é o [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Considere a instalação de um [plug-in de carregamento em diferido de imagens](https://octobercms.com/plugins?search=lazy) com capacidade para adiar imagens não visíveis ou mudar para um tema com essa funcionalidade. Considere ainda a utilização do [plug-in de AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Existem vários plug-ins que ajudam a [colocar recursos críticos inline](https://octobercms.com/plugins?search=css). Estes plug-ins podem afetar outros plug-ins, pelo que deve efetuar testes rigorosos."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "As especificações dos temas, dos plug-ins e do servidor, no seu conjunto, contribuem para o tempo de resposta do servidor. Considere localizar um tema mais otimizado, ao selecionar cuidadosamente um plug-in de otimização e/ou atualizar o servidor. O CMS de outubro também permite que os programadores utilizem [`Queues`](https://octobercms.com/docs/services/queues) para adiar o processamento de uma tarefa morosa, como o envio de um email. Isto acelera drasticamente os pedidos Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Considere mostrar excertos nas listas de publicação (por exemplo, através de um botão `show more`), reduzir o número de publicações apresentadas numa determinada página Web, dividir as publicações extensas em várias páginas Web ou utilizar um plug-in para efetuar o carregamento em diferido de comentários."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Existem muitos [plug-ins](https://octobercms.com/plugins?search=css) que podem acelerar um Website ao concatenar, reduzir e comprimir os estilos. A utilização de um processo de compilação para proceder previamente à redução pode acelerar o desenvolvimento."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Existem muitos [plug-ins](https://octobercms.com/plugins?search=javascript) que podem acelerar um Website ao concatenar, reduzir e comprimir os scripts. A utilização de um processo de compilação para proceder previamente à redução pode acelerar o desenvolvimento."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Considere rever os [plug-ins](https://octobercms.com/plugins) que carregam CSS não utilizado no Website. Para identificar plug-ins que adicionam CSS não necessário, execute a [utilização de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas Chrome DevTools. Identifique o tema/plug-in responsável a partir do URL da folha de estilos. Procure plug-ins que tenham muitas folhas de estilos com muito vermelho na utilização de código. Um plug-in só deve adicionar uma folha de estilos se esta for efetivamente utilizada na página Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Considere rever os [plug-ins](https://octobercms.com/plugins?search=javascript) que carregam JavaScript não utilizado na página Web. Para identificar plug-ins que adicionam JavaScript não necessário, execute a [utilização de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) nas Chrome DevTools. Identifique o tema/plug-in responsável no URL do script. Procure plug-ins que tenham muitos scripts com muito vermelho na utilização de código. Um plug-in só deve adicionar um script se este for efetivamente utilizado na página Web."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Leia sobre como [impedir pedidos de rede desnecessários com cache HTTP](https://web.dev/http-cache/#caching-checklist). Existem muitos [plug-ins](https://octobercms.com/plugins?search=Caching) que podem ser utilizados para acelerar a colocação em cache."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Considere utilizar um [plug-in de otimização de imagens](https://octobercms.com/plugins?search=image) para comprimir imagens sem comprometer a qualidade."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Carregue as imagens diretamente no gestor de meios para garantir que os tamanhos de imagem estão disponíveis. Considere utilizar o [filtro de redimensionamento](https://octobercms.com/docs/markup/filter-resize) ou um [plug-in de redimensionamento de imagens](https://octobercms.com/plugins?search=image) para garantir a utilização de tamanhos de imagem ideais."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Ative a compressão de texto na configuração do servidor Web."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Considere utilizar uma biblioteca de \"visualização baseada na janela atual\" como `react-window` para minimizar o número de nós DOM criados se estiver a renderizar muitos elementos repetidos na página. [<PERSON><PERSON> mais](https://web.dev/virtualize-long-lists-react-window/). Minimize também as re-renderizações desnecessárias ao utilizar [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ou [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) e [skip effects](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), somente até determinadas dependências terem sido alteradas caso esteja a utilizar o hook `Effect` para melhorar o desempenho do tempo de execução."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Se estiver a utilizar o React Router, minimize a utilização do componente `<Redirect>` para as [navegações de encaminhamentos](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Se estiver a renderizar componentes React do lado do servidor, considere utilizar `renderToPipeableStream()` ou `renderToStaticNodeStream()` para permitir que o cliente receba e hidrate várias partes da marcação em vez de todas em simultâneo. [<PERSON><PERSON> mais](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Se o sistema de compilação reduzir os ficheiros CSS automaticamente, assegure-se de que está a implementar a compilação de produção da aplicação. Pode verificar esta situação com a extensão React Developer Tools. [Saiba mais](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Se o sistema de compilação reduzir os ficheiros JS automaticamente, assegure-se de que está a implementar a compilação de produção da sua aplicação. Pode verificar esta situação com a extensão React Developer Tools. [Saiba mais](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Se não estiver a renderizar do lado do servidor, [divida os seus pacotes de JavaScript](https://web.dev/code-splitting-suspense/) com `React.lazy()`. <PERSON><PERSON><PERSON> contr<PERSON>, divida o código ao utilizar uma biblioteca de terceiros como [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Utilize o Gerador de perfis do React DevTools, que utiliza a API do Gerador de perfis, para medir o desempenho de renderização dos seus componentes. [<PERSON><PERSON> mais](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Coloque vídeos dentro de `VideoBoxes`, personalize-os com `Video Masks` ou adicione `Transparent Videos`. [<PERSON><PERSON> ma<PERSON>](https://support.wix.com/en/article/wix-video-about-wix-video)."}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Carregue imagens com o `Wix Media Manager` para garantir que são publicadas automaticamente como WebP. Descubra [mais formas de otimizar](https://support.wix.com/en/article/site-performance-optimizing-your-media) o conteúdo multimédia do seu site."}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Quando [adicionar código de terceiros](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) no separador `Custom Code` do painel de controlo do site, certifique-se de que este é diferido ou carregado no final do corpo do código. Sempre que possível, use as [integrações](https://support.wix.com/en/article/about-marketing-integrations) do Wix para incorporar ferramentas de marketing no seu site. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "O Wix usa RFCs (redes de fornecimento de conteúdo) e a colocação em cache para publicar respostas o mais rapidamente possível para a maioria dos visitantes. Considere [ativar manualmente a colocação em cache](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) para o seu site, especialmente se usar o `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Reveja o código de terceiros adicionado ao seu site no separador `Custom Code` do painel de controlo do site e mantenha apenas os serviços necessários para o site. [Saiba mais](https://support.wix.com/en/article/site-performance-removing-unused-javascript)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Considere carregar o GIF para um serviço que o disponibilizará para incorporação como vídeo HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Considere usar o plug-in [Performance Lab](https://wordpress.org/plugins/performance-lab/) para converter automaticamente as suas imagens JPEG carregadas em WebP, sempre que possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Instale um [plug-in do WordPress de carregamento lento](https://wordpress.org/plugins/search/lazy+load/) com capacidade para adiar imagens não visíveis ou para mudar para um tema com essa funcionalidade. Considere ainda a utilização do [plug-in de AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Há vários plug-ins do WordPress que o podem ajudar a [colocar recursos críticos inline](https://wordpress.org/plugins/search/critical+css/) ou [adiar recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). Tenha em atenção que as otimizações oferecidas por estes plug-ins podem quebrar funcionalidades do seu tema ou plug-ins, pelo que poderá ser necessário efetuar alterações ao código."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "As especificações dos temas, plug-ins e servidor, no seu conjunto, contribuem para o tempo de resposta do servidor. Considere procurar um tema mais otimizado, selecionar cuidadosamente um plug-in de otimização e/ou atualizar o servidor."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Considere mostrar excertos nas suas listas de publicações (por exemplo, através da etiqueta de mais), reduzir o número de publicações apresentadas numa determinada página, dividir as publicações longas em várias páginas ou utilizar um plug-in para tornar o carregamento de comentários lento."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Um número de [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+css/) pode acelerar o seu site ao concatenar, reduzir e comprimir os seus estilos. Poderá ainda utilizar um processo de criação para proceder previamente à redução se possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Um número de [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+javascript/) pode acelerar o seu site ao concatenar, reduzir e comprimir os seus scripts. Poderá ainda utilizar um processo de criação para proceder previamente à redução se possível."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Considere reduzir ou mudar o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam CSS não utilizadas na sua página. Para identificar plug-ins que estejam a adicionar CSS não reconhecido, experimente realizar a [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) nas DevTools do Chrome. Pode identificar o tema/plug-in responsável a partir do URL da folha de estilos. Esteja atento a plug-ins que tenham muitas folhas de estilo na lista com muito vermelho na cobertura do código. Um plug-in só deve ter uma folha de estilos na lista de espera se esta for realmente utilizada na página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Considere reduzir ou mudar o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam JavaScript não utilizado na sua página. Para identificar plug-ins que estejam a adicionar JS não reconhecido, experimente realizar a [cobertura de código](https://developer.chrome.com/docs/devtools/coverage/) nas DevTools do Chrome. Pode identificar o tema/plug-in responsável a partir do URL do script. Esteja atento a plug-ins que tenham muitos scripts na lista com muito vermelho na cobertura do código. Um plug-in só deve ter um script na lista de espera se este for realmente utilizado na página."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Leia sobre [Colocação do navegador em cache no WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Considere utilizar um [plug-in do WordPress de otimização da imagem](https://wordpress.org/plugins/search/optimize+images/) que comprima as imagens, ao mesmo tempo que mantém a qualidade."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Carregue imagens diretamente a partir da [biblioteca de multimédia](https://wordpress.org/support/article/media-library-screen/) para garantir que estão disponíveis os tamanhos de imagem necessários e, em seguida, introduza-as a partir da biblioteca de multimédia ou utilize o widget de imagens para garantir que são utilizados os tamanhos ideais das imagens (incluindo as referentes a breakpoints adaptáveis). Evite utilizar imagens de `Full Size`, a menos que as dimensões sejam adequadas à utilização. [Saiba mais](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Pode ativar a compressão de texto na configuração do servidor Web."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Ative o suplemento \"Imagify\" no separador Otimização de imagem no \"WP Rocket\" para converter as suas imagens em WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Ative a funcionalidade [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) no WP Rocket para resolver esta recomendação. Esta funcionalidade atrasa o carregamento das imagens até o visitante deslocar a página para baixo e ter efetivamente de as ver."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Ative as funcionalidades [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) e [Carregar JavaScript diferido](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) no \"WP Rocket\" para resolver esta recomendação. Estas funcionalidades vão otimizar os ficheiros CSS e JavaScript, respetivamente, para que não bloqueiem a renderização da página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Ative a opção [Reduzir ficheiros CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) no \"WP Rocket\" para corrigir este problema. Todos os espaços e comentários nos ficheiros CSS do site vão ser removidos para diminuir o tamanho dos ficheiros e tornar a transferência mais rápida."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Ative a funcionalidade [Reduzir ficheiros JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) no \"WP Rocket\" para corrigir este problema. Os espaços vazios e os comentários vão ser removidos dos ficheiros JavaScript para reduzir o tamanho e tornar a transferência mais rápida."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Ative a funcionalidade [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) no \"WP Rocket\" para corrigir este problema. Reduz o tamanho da página ao remover todos os CSS e folhas de estilos não usados, mantendo apenas os CSS usados para cada página."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Ative a funcionalidade [Atrasar execução de JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) no \"WP Rocket\" para corrigir este problema. Vai melhorar o carregamento da página ao atrasar a execução de scripts até à interação do utilizador. Se o site tiver iFrames, também pode usar as funcionalidades [LazyLoad para iFrames e vídeos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) e [Substituir iFrame do YouTube por imagem de pré-visualização](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) do WP Rocket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Ative o suplemento \"Imagify\" no separador Otimização de imagem no \"WP Rocket\" e execute a Otimização em massa para comprimir as suas imagens."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Use a funcionalidade [Obter previamente pedidos de DNS](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) no \"WP Rocket\" para adicionar \"dns-prefetch\" e acelerar a ligação com domínios externos. Além disso, o \"WP Rocket\" adiciona automaticamente \"preconnect\" ao [domínio do Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) e aos CNAMEs adicionados através da funcionalidade [Ativar RFC](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Para corrigir este problema dos tipos de letra, ative a funcionalidade [Remover CSS não usado](https://docs.wp-rocket.me/article/1529-remove-unused-css) no \"WP Rocket\". Os tipos de letra críticos do site vão ser pré-carregados com prioridade."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Veja a calculadora."}, "report/renderer/report-utils.js | collapseView": {"message": "Reduzir vista"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Navegação inicial"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Latência crítica máxima do caminho:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Copiar JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Ativar/desativar tema escuro"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Imprimir com expansão"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Imprimir resumo"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Guardar como Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Guardar como HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Guardar como JSON"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Ver rastreio não limitado"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Abrir no visualizador"}, "report/renderer/report-utils.js | errorLabel": {"message": "Erro!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Erro de relatório: sem informações de auditoria"}, "report/renderer/report-utils.js | expandView": {"message": "Expandir vista"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "Original"}, "report/renderer/report-utils.js | footerIssue": {"message": "Apresentar um problema"}, "report/renderer/report-utils.js | hide": {"message": "Ocultar"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Dados laboratoriais"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "A análise do [Lighthouse](https://developers.google.com/web/tools/lighthouse/) da página atual numa rede móvel emulada. Os valores são o resultado de uma estimativa e podem variar."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Itens adicionais a verificar manualmente"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Não aplicável"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Abra num novo separador"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Oportunidade"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Poupança estimada"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Auditorias aprovadas"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "De acordo com os [Critérios de capacidade de instalação atualizados do Chrome](https://developer.chrome.com/blog/update-install-criteria), o Lighthouse vai descontinuar a categoria PWA numa versão futura. Consulte a [documentação de PWA atualizada](https://developer.chrome.com/docs/devtools/progressive-web-apps/) para futuros testes de PWA."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Carregamento de página inicial"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Instantâneo num momento no tempo"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Período de interações dos utilizadores"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Limitação personalizada"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Ambiente de trabalho emulado"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Moto G Power emulado"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Sem emulação"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Versão <PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Potência da CPU/memória não limitada"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Limitação da CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Dispositivo"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Limitação de rede"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulação do ecrã"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Agente do utilizador (rede)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Sessão de página única"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Estes dados são retirados de uma sessão de página única, ao contrário dos dados de campo que resumem várias sessões."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Limitação lenta de 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Desconhecido"}, "report/renderer/report-utils.js | show": {"message": "Mostrar"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Mostrar auditorias relevantes para:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Reduzir fragmento"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Expandir fragmento"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos de terceiros"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Fornecido pelo ambiente"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Ocorreram problemas que afetaram esta execução do Lighthouse:"}, "report/renderer/report-utils.js | unattributable": {"message": "Não atribuível"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Os valores são estimados e podem variar. A [pontuação de desempenho é calculada](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) diretamente a partir destas métricas."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON>er rastreio"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Ver mapa em árvore"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "As auditorias foram concluídas com êxito, mas com avisos"}, "report/renderer/report-utils.js | warningHeader": {"message": "Avisos: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Todos os scripts"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Cobertura"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Bytes de recursos"}, "treemap/app/src/util.js | tableColumnName": {"message": "Nome"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Ativar/desativar tabela"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Bytes não utilizados"}}