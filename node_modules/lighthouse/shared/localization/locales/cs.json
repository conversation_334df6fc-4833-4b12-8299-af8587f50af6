{"core/audits/accessibility/accesskeys.js | description": {"message": "Přístupové klávesy uživatelům umožňují rychleji vybrat část stránky. Aby navigace fungovala správně, musí být každá přístupová klávesa jedinečná. [Další informace o přístupových klávesách](https://dequeuniversity.com/rules/axe/4.8/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Hodnoty atributů `[accesskey]` nej<PERSON><PERSON> jed<PERSON>"}, "core/audits/accessibility/accesskeys.js | title": {"message": "Hodnoty `[accesskey]` jsou unik<PERSON>"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> ARIA `role` podporuje konkrétní podmnožinu atributů `aria-*`. Nesprávné přiřazení atributy `aria-*` zneplatní. [Jak atributy ARIA přiřadit k rolím](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributy `[aria-*]` neodpovídají svým rolím"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributy `[aria-*]` odpovídají příslušným rolím"}, "core/audits/accessibility/aria-allowed-role.js | description": {"message": "Atributy `role` odpovídající specifikaci ARIA poskytují asistenčním technologiím informace o roli jednotlivých prvků na webové stránce. Pokud jsou v hodnotách `role` p<PERSON><PERSON><PERSON><PERSON>, neodpoví<PERSON><PERSON><PERSON> hodn<PERSON> `role` podle specifikace ARIA nebo se jedná o abstraktní role, uživatelé asistenčních technologií se nedozvědí účel prvku. [Další informace o rolích ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-allowed-role)"}, "core/audits/accessibility/aria-allowed-role.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> atri<PERSON> `role=\"\"` <PERSON><PERSON><PERSON><PERSON> plat<PERSON> role ARIA."}, "core/audits/accessibility/aria-allowed-role.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>ri<PERSON> `role=\"\"` j<PERSON><PERSON> plat<PERSON> role ARIA."}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Když prvek nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je pak tento prvek v podstatě nepoužitelný. [Jak <PERSON>aj<PERSON>, aby příkazové prvky byly přístupnějš<PERSON>](https://dequeuniversity.com/rules/axe/4.8/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Prvky `button`, `link` a `menuitem` nemají přístupné n<PERSON>zvy."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Prvky `button`, `link` a `menuitem` maj<PERSON>"}, "core/audits/accessibility/aria-dialog-name.js | description": {"message": "Dialogové prvky ARIA bez přístupových názvů mohou uživatelům čteček obrazovky znemožňovat rozeznat účel těchto prvků. [Pře<PERSON>těte si, jak dialogové prvky ARIA zpřístupnit](https://dequeuniversity.com/rules/axe/4.8/aria-dialog-name)."}, "core/audits/accessibility/aria-dialog-name.js | failureTitle": {"message": "Prvky s atributy `role=\"dialog\"` nebo `role=\"alertdialog\"` nemají přístupné n<PERSON>vy."}, "core/audits/accessibility/aria-dialog-name.js | title": {"message": "Prvky s atributy `role=\"dialog\"` nebo `role=\"alertdialog\"` maj<PERSON>."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "<PERSON><PERSON><PERSON> je u prvku `<body>` dokumentu nastaveno `aria-hidden=\"true\"`, asistenční technologie, jako j<PERSON>u <PERSON> obrazovek, nefung<PERSON><PERSON><PERSON> konzistentně. [P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si, jak `aria-hidden` ovlivňuje text dokumentu](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Hodnota `[aria-hidden=\"true\"]` je v prvku `<body>` dokumentu přítomná"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Hodnota `[aria-hidden=\"true\"]` není v prvku `<body>` dokumentu přítomná"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Vybratelné podřízené prvky v prvku `[aria-hidden=\"true\"]` znemožňují využití těchto interaktivních prvků ze strany uživatelů asistenčních technologií, jako j<PERSON>u <PERSON> obrazovky. [Jak atribut `aria-hidden` ovlivňuje vybratelné prvky](https://dequeuniversity.com/rules/axe/4.8/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Prvky `[aria-hidden=\"true\"]` obsahují zaměřitelné podřízené prvky"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Prvky `[aria-hidden=\"true\"]` neobsahují zaměřitelné podřízené prvky"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON><PERSON><PERSON>ad<PERSON> pole nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je toto pole v podstatě nepoužitelné. [Další informace o štítcích vstupních polí](https://dequeuniversity.com/rules/axe/4.8/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Zadávací pole ARIA nemají přístupné n<PERSON>vy"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Zadáva<PERSON>í pole ARIA mají <PERSON><PERSON>"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Když prvek měřiče (meter) nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je pak tento prvek v podstatě nepoužitelný. [Jak pojmenovat prvky `meter`](https://dequeuniversity.com/rules/axe/4.8/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Prvky ARIA `meter` nemají přístupné názvy."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Prvky ARIA `meter` mají p<PERSON> n<PERSON>"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Když prvek `progressbar` nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je pak tento prvek v podstatě nepoužitelný. [Jak označit prvky `progressbar`](https://dequeuniversity.com/rules/axe/4.8/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Prvky ARIA `progressbar` nemají přístupné názvy."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Prvky ARIA `progressbar` maj<PERSON> n<PERSON>"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> role ARIA ma<PERSON><PERSON> povinn<PERSON> atri<PERSON>, <PERSON><PERSON><PERSON> obrazovek popisují stav prvku. [Dalš<PERSON> informace o rolích a povinných atributech](https://dequeuniversity.com/rules/axe/4.8/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Prvky s atributy `[role]` nemají všechny povinné atributy `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Prvky s atributy `[role]` mají všechny povinné atributy `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Někter<PERSON> nadřazené role ARIA musejí kvůli poskytovaní správných funkcí přístupnosti obsahovat určité podřízené role. [Další informace o rolích a požadovaných podřízených prvcích](https://dequeuniversity.com/rules/axe/4.8/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "V prvcích s atributem ARIA `[role]`, j<PERSON><PERSON><PERSON> podří<PERSON> prvky musí obsahovat konkrétní atribut `[role]`, ně<PERSON><PERSON><PERSON> z těchto povinných podřízených prvků chybí."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Prvky s atributem ARIA `[role]`, j<PERSON><PERSON><PERSON> podřízené prvky musí obsahovat konkrétní atribut `[role]`, obsahují všechny povinné podřízené prvky."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Aby pos<PERSON><PERSON>aly správn<PERSON> přís<PERSON>ti, m<PERSON><PERSON><PERSON> být někt<PERSON><PERSON> podřízené role ARIA umístěny v konkrétních nadřazených rolích. [Další informace o rolích ARIA a povinném nadřazeném prvku](https://dequeuniversity.com/rules/axe/4.8/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Prvky s atributem `[role]` nejsou umístěny v požadovaném nadřazeném prvku"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Prvky s atributy `[role]`jsou umístěny v požadovaném nadřazeném prvku"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Aby role ARIA pos<PERSON><PERSON><PERSON> správn<PERSON>, muse<PERSON><PERSON> mít platné hodnot<PERSON>. [Další informace o platných rolích ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> atri<PERSON> `[role]` ne<PERSON><PERSON><PERSON> p<PERSON>"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> atri<PERSON> `[role]` j<PERSON><PERSON> p<PERSON>"}, "core/audits/accessibility/aria-text.js | description": {"message": "Přidáním atributu `role=text` k textovému uzlu rozdělenému značkami umožníte čtečce VoiceOver, aby ho považovala za jednu frázi. Nebudou však oznámeny vybratelné podřízené prvky. [Další informace o atributu `role=text`](https://dequeuniversity.com/rules/axe/4.8/aria-text)"}, "core/audits/accessibility/aria-text.js | failureTitle": {"message": "Prvky s atributem `role=text` ma<PERSON><PERSON> v<PERSON> podří<PERSON> prvky."}, "core/audits/accessibility/aria-text.js | title": {"message": "Prvky s atributem `role=text` ne<PERSON><PERSON><PERSON> vypratelné podřízené prvky."}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON><PERSON> př<PERSON><PERSON><PERSON><PERSON> pole nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je toto pole v podstatě nepoužitelné. [Další informace o přepínacích polích](https://dequeuniversity.com/rules/axe/4.8/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Pole přepínačů ARIA nemají přístupné n<PERSON>zvy"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Pole přepína<PERSON>ů ARIA mají přístupné n<PERSON>zvy"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Když prvek popisku (title) nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je pak tento prvek v podstatě nepoužitelný. [Jak pojmenovat prvky `tooltip`](https://dequeuniversity.com/rules/axe/4.8/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Prvky ARIA `tooltip` nemají přístupné názvy."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Prvky ARIA `tooltip` maj<PERSON>n<PERSON> n<PERSON>vy"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON><PERSON>ž prvek `treeitem` nemá přístupný název, čtečky obrazovek oznamují obecný název a pro jejich uživatele je pak tento prvek v podstatě nepoužitelný. [Další informace o označení prvků `treeitem`](https://dequeuniversity.com/rules/axe/4.8/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Prvky ARIA `treeitem` nemají přístupné názvy."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Prvky ARIA `treeitem` mají p<PERSON>pné n<PERSON>vy"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Asistenční technologie, jak<PERSON> <PERSON><PERSON><PERSON>, atributy ARIA s neplatnými hodnotami nedokážou interpretovat. [Další informace o platných hodnotách atributů ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributy `[aria-*]` nema<PERSON><PERSON> platn<PERSON> hodnoty"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributy `[aria-*]` maj<PERSON> plat<PERSON> hodnoty"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Asistenční technologie, jak<PERSON> <PERSON><PERSON><PERSON>, atributy ARIA s neplatnými názvy nedokážou interpretovat. [Další informace o platných atributech ARIA](https://dequeuniversity.com/rules/axe/4.8/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributy `[aria-*]` nejsou platné nebo v nich jsou překlepy"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributy `[aria-*]` jsou platné a nejsou v nich překlepy"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON><PERSON>ž tlačítko nemá přístupný název, čtečky obrazovek ho oznamují jako „tlačítko“ a pro jejich uživatele je tak v podstatě nepoužitelné. [J<PERSON> zaj<PERSON>it, aby tlačítka byla přístupnější](https://dequeuniversity.com/rules/axe/4.8/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Tlačítka nemají přístupné názvy"}, "core/audits/accessibility/button-name.js | title": {"message": "Tlačítka mají přístupné n<PERSON>zvy"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON><PERSON>ž přidáte možnosti obejití repetitivn<PERSON><PERSON> o<PERSON>, umožníte tím efektivnější procházení stránky pomocí klávesnice. [Další informace o blokování obcházení](https://dequeuniversity.com/rules/axe/4.8/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Stránka neobsahuje nadpis, odkaz k přeskočení ani orientační bod regionu"}, "core/audits/accessibility/bypass.js | title": {"message": "Stránka obsahuje nadpis, odkaz k přeskočení nebo orientační bod regionu"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Text s nízkým kontrastem je pro mnoho uživatelů obtížně čitelný nebo nečitelný. [Jak zajistit dostatečný kontrast barev](https://dequeuniversity.com/rules/axe/4.8/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> poz<PERSON> a popředí nemají dostatečný kontrastní poměr."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON> poz<PERSON> a popředí mají dostate<PERSON>ý kontrastní poměr"}, "core/audits/accessibility/definition-list.js | description": {"message": "<PERSON><PERSON><PERSON> seznamy definic nejsou správně <PERSON>, čtečky obrazovek mohou generovat matoucí nebo nepřesný výstup. [Jak správně strukturovat seznamy definic](https://dequeuniversity.com/rules/axe/4.8/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Prvky `<dl>` neo<PERSON><PERSON><PERSON><PERSON> jen správně seřazené skupiny prvků `<dt>` a `<dd>` nebo prvky `<script>`, `<template>` či `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Prvky `<dl>` obs<PERSON><PERSON><PERSON> jen správně seřazené skupiny prvků `<dt>` a `<dd>` nebo prvky `<script>`, `<template>` či `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "A<PERSON> moh<PERSON>čky obrazovek správně oznamovat položky seznamů definic (`<dt>` a `<dd>`), muse<PERSON><PERSON> být tyto položky umístěny v nadřazeném prvku `<dl>`. [<PERSON><PERSON> správně strukturovat seznamy definic](https://dequeuniversity.com/rules/axe/4.8/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Položky seznamu definic nejsou umístěny v prvcích `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Položky seznamu definic jsou umístěny v prvcích `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> (title) uživatelům čteček obrazovek poskytuje souhrnné informace o stránce a uživatelé vyhledávačů se podle něj rozhodují, zda je stránka pro jejich vyhledávání relevantní. [Další informace o názvech dokumentů](https://dequeuniversity.com/rules/axe/4.8/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Do<PERSON><PERSON> neobsahuje prvek `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument obsahuje prvek `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Všechny vybratelné prvky musí mít unikátní atribut `id`, aby by<PERSON>, že budou viditelné pro asistenční technologie. [Jak opravit duplicitní polo<PERSON> `id`](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Atributy `[id]` u aktivních, zaměřitelných prvků nejsou unikátní"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Atributy `[id]` u aktivních, zaměřitelných prvků jsou unikátní"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Hodnota ID ARIA musí být un<PERSON>, aby asistenční technologie nepřehlížely ostatní instance. [Jak opravit duplicitní ID ARIA](https://dequeuniversity.com/rules/axe/4.8/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ID ARIA nejsou unikátní"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ID ARIA jsou unik<PERSON>ní"}, "core/audits/accessibility/empty-heading.js | description": {"message": "Nadpis bez obsahu nebo nepřístupný text brání uživatelům čteček obrazovky v přístupu k informacím o struktuře stránky. [Další informace o nadpisech](https://dequeuniversity.com/rules/axe/4.8/empty-heading)"}, "core/audits/accessibility/empty-heading.js | failureTitle": {"message": "Prvky nadpisů nemají žádný obsah."}, "core/audits/accessibility/empty-heading.js | title": {"message": "Všechny prvky nadpisů mají obsah."}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Pole formuláře s několika štítky mohou asistenční technologie, jako jsou <PERSON> o<PERSON>, oz<PERSON><PERSON>t matouc<PERSON><PERSON> zp<PERSON>, proto<PERSON><PERSON> pou<PERSON><PERSON><PERSON><PERSON> buď prvn<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo všechny. [Jak ve formulářích používat štítky](https://dequeuniversity.com/rules/axe/4.8/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Pole formul<PERSON><PERSON><PERSON> mají několik štítků"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> pole formuláře nemají několik štítků"}, "core/audits/accessibility/frame-title.js | description": {"message": "Čtečky obrazovek obvykle názvy rámců používají k popisu jejich obsahu. [Další informace o názvech rámců](https://dequeuniversity.com/rules/axe/4.8/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Prvky `<frame>` nebo `<iframe>` nemají atribut title"}, "core/audits/accessibility/frame-title.js | title": {"message": "Prvky `<frame>` a `<iframe>` mají atribut title"}, "core/audits/accessibility/heading-order.js | description": {"message": "Spr<PERSON><PERSON><PERSON><PERSON> seř<PERSON>, k<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ou strukturu stránky a usnadňují tak navigaci a srozumitelnost při použití asistenčních technologií. [Další informace o pořadí nadpisů](https://dequeuniversity.com/rules/axe/4.8/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Prvky nadpisu se nezobrazují v sestupném pořadí"}, "core/audits/accessibility/heading-order.js | title": {"message": "Prvky nadpisu se zobrazují v sestupném pořadí"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Pokud stránka neuvádí atribut `lang`, čtečky obrazovek předpokládají, že je ve výchozím jaz<PERSON>ce, k<PERSON><PERSON> uživatel zvolil při nastavování čtečky obrazovky. Pokud stránka ve skutečnosti ve výchozím jazyce není, čtečka obrazovky text nemusí přečíst správně. [Další informace o atributu `lang`](https://dequeuniversity.com/rules/axe/4.8/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Prvek `<html>` nemá atribut `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Prvek `<html>` má atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Zadáním platného [jazyka BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomůžete čtečkám obrazovek správně oznamovat text. [Jak používat atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Prvek `<html>` nemá platnou hodnotu atributu `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Prvek `<html>` má atribut `[lang]` s platnou hodnotou"}, "core/audits/accessibility/html-xml-lang-mismatch.js | description": {"message": "Pokud <PERSON>ová stránka neuvádí konzistentně j<PERSON>, ne<PERSON><PERSON> č<PERSON>č<PERSON> obrazovky text stránky oznámit správně. [Další informace o atributu `lang`](https://dequeuniversity.com/rules/axe/4.8/html-xml-lang-mismatch)"}, "core/audits/accessibility/html-xml-lang-mismatch.js | failureTitle": {"message": "Prvek `<html>` nemá atribut `[xml:lang]` se stejným základním jazykem, jak<PERSON> je uveden v atributu `[lang]`."}, "core/audits/accessibility/html-xml-lang-mismatch.js | title": {"message": "Prvek `<html>` má atribut `[xml:lang]` se stejným základním jazykem, jak<PERSON> je uveden v atributu `[lang]`."}, "core/audits/accessibility/identical-links-same-purpose.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> se stejným cílem by m<PERSON><PERSON> m<PERSON><PERSON>, a<PERSON> <PERSON><PERSON> vě<PERSON><PERSON><PERSON>, k <PERSON>em<PERSON>, a mohli se rozhodnout, zda na ně přejdou. [Další informace o identických odkazech](https://dequeuniversity.com/rules/axe/4.8/identical-links-same-purpose)."}, "core/audits/accessibility/identical-links-same-purpose.js | failureTitle": {"message": "Identické odkazy nemají s<PERSON>."}, "core/audits/accessibility/identical-links-same-purpose.js | title": {"message": "Stejné od<PERSON> maj<PERSON> s<PERSON>."}, "core/audits/accessibility/image-alt.js | description": {"message": "Informativní prvky by m<PERSON><PERSON> <PERSON><PERSON><PERSON>, popisný alternativní text. Dekorativní prvky lze ignorovat pomocí prázdného atributu alt. [Další informace o atributu `alt`](https://dequeuniversity.com/rules/axe/4.8/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Prvky obrázků nemají atributy `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Prvky obrázků mají atributy `[alt]`"}, "core/audits/accessibility/image-redundant-alt.js | description": {"message": "Informativní prvky by m<PERSON><PERSON> <PERSON><PERSON><PERSON>, popisný alternativní text. Alternativní text přesně odpovídající textu vedle odkazu nebo obrázku může být pro uživatele čteček obrazovek matoucí, proto<PERSON><PERSON> bude přečten dvakrát. [Další informace o atributu `alt`](https://dequeuniversity.com/rules/axe/4.8/image-redundant-alt)"}, "core/audits/accessibility/image-redundant-alt.js | failureTitle": {"message": "Prvky obrázků mají atributy `[alt]` s redundantním textem."}, "core/audits/accessibility/image-redundant-alt.js | title": {"message": "Prvky obrázků nemají atributy `[alt]` s redundantním textem."}, "core/audits/accessibility/input-button-name.js | description": {"message": "Přidání rozeznatelného a přístupného textu k tlačítkům vstupu může uživatelům čteček obrazovek pomoci pochopit účel tlačítka. [Další informace o tlačítkách vstupu](https://dequeuniversity.com/rules/axe/4.8/input-button-name)"}, "core/audits/accessibility/input-button-name.js | failureTitle": {"message": "Tlačítka vstupu nemají rozeznatelný text."}, "core/audits/accessibility/input-button-name.js | title": {"message": "Vstupní tlačítka mají rozeznatelný text."}, "core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> je jako <PERSON> `<input>` pou<PERSON><PERSON> o<PERSON>r<PERSON>, uvedení alternativního textu pomůže uživatelům čteček obrazovek porozumět účelu tlačítka. [Další informace o alternativním textu obrázku použitého pro vstup](https://dequeuniversity.com/rules/axe/4.8/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Prvky `<input type=\"image\">` nemají text `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Prvky `<input type=\"image\">` mají text `[alt]`"}, "core/audits/accessibility/label-content-name-mismatch.js | description": {"message": "Viditelné textové <PERSON>, kter<PERSON> se neshodu<PERSON><PERSON> s přístupovými názvy, mohou být pro uživatele čteček obrazovek matoucí. [Další informace o přístupových názvech](https://dequeuniversity.com/rules/axe/4.8/label-content-name-mismatch)"}, "core/audits/accessibility/label-content-name-mismatch.js | failureTitle": {"message": "Prvky s viditelnými textovými štítky nemají odpovídající přístupové názvy."}, "core/audits/accessibility/label-content-name-mismatch.js | title": {"message": "Prvky s viditelnými textovými štítky mají odpovídající přístupové názvy."}, "core/audits/accessibility/label.js | description": {"message": "Štítky z<PERSON>, aby asistenční technologie (například čtečky obrazovek) správně oznamovaly ovládací prvky formulářů. [Další informace o štítcích prvků formulářů](https://dequeuniversity.com/rules/axe/4.8/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "K prvkům formulářů nejsou přidružené <PERSON>"}, "core/audits/accessibility/label.js | title": {"message": "K prvkům formulářů jsou přidružené <PERSON>ky"}, "core/audits/accessibility/landmark-one-main.js | description": {"message": "Jeden hlavní orientační bod pomáhá uživatelům čteček obrazovky procházet webovou stránkou. [Další informace o orientačních bodech](https://dequeuniversity.com/rules/axe/4.8/landmark-one-main)"}, "core/audits/accessibility/landmark-one-main.js | failureTitle": {"message": "Dokument nemá hlavní orientační bod."}, "core/audits/accessibility/landmark-one-main.js | title": {"message": "Dokument má hlavní orientační bod."}, "core/audits/accessibility/link-in-text-block.js | description": {"message": "Text s nízkým kontrastem je pro mnoho uživatelů obtížně čitelný nebo nečitelný. Rozlišitelný text odkazů usnadňuje používání slabozrakým uživatelům. [P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si, jak odkazy odli<PERSON>](https://dequeuniversity.com/rules/axe/4.8/link-in-text-block)"}, "core/audits/accessibility/link-in-text-block.js | failureTitle": {"message": "Odkazy lze rozlišit pouze podle barvy."}, "core/audits/accessibility/link-in-text-block.js | title": {"message": "Odkazy lze rozlišit i jinak než podle barvy."}, "core/audits/accessibility/link-name.js | description": {"message": "Text odka<PERSON>ů (a náhradní text ob<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jsou použ<PERSON> jako odkazy), kter<PERSON> je rozeznatelný a jedinečný a který lze vybrat, uživatelům čteček obrazovek usnadňuje procházení stránek. [Jak zajistit přístupnost odkazů](https://dequeuniversity.com/rules/axe/4.8/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Od<PERSON>zy nemají rozeznatelné názvy"}, "core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> rozez<PERSON>lné názvy"}, "core/audits/accessibility/list.js | description": {"message": "Čtečky obrazovek oznamují seznamy speciálním způsobem. Použitím správné struktury seznamu pomůžete čtečkám obrazovek s výstupem. [Další informace o správné struktuře seznamů](https://dequeuniversity.com/rules/axe/4.8/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "Seznamy neobsahují výhradně prvky `<li>` a prvky, kter<PERSON> podporují skrip<PERSON> (`<script>` a `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Seznamy obsahují výhradně prvky `<li>` a prvky, kter<PERSON> podporují skripty (`<script>` a `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Aby čtečky obrazovek oznamovaly položky seznamů (`<li>`) správně, musí být umístěny v nadřazeném prvku `<ul>`, `<ol>` nebo `<menu>`. [<PERSON><PERSON><PERSON> informace o správné struktuře seznamů](https://dequeuniversity.com/rules/axe/4.8/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Polož<PERSON> seznamů (`<li>`) nejsou umístěny v nadřazených prvcích `<ul>`, `<ol>` nebo ‎`<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Položky seznamu (`<li>`) jsou umístěny v nadřazených prvcích `<ul>`, `<ol>` nebo `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Uživatelé neočekávají, že se stránka bude automaticky obnovovat. Při automatickém obnovení se prohlížeč vrátí zpět na začátek stránky. M<PERSON>že to vést k nepříjemnému nebo matoucímu chování při procházení. [Další informace o metaznačce „refresh“](https://dequeuniversity.com/rules/axe/4.8/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "V dokumentu je použita metaznačka `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "V dokumentu není použita metaznačka `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Deaktivace změny velikosti zobrazení je problematická pro slaboz<PERSON> u<PERSON>, kte<PERSON><PERSON> jsou při prohlížení obsahu webové stránky závislí na přiblížení obrazovky. [Další informace o metaznačce viewport](https://dequeuniversity.com/rules/axe/4.8/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "V prvku `[user-scalable=\"no\"]` je použit atribut `<meta name=\"viewport\">` nebo je atribut `[maximum-scale]` men<PERSON><PERSON> než 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "V prvku `[user-scalable=\"no\"]` není použit atribut `<meta name=\"viewport\">` a atribut `[maximum-scale]` není men<PERSON> než 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Čtečky obrazovek nedokážou přeložit obsah jiného typu, než je text. K<PERSON>ž k prvkům `<object>` přidáte alternativní text, čtečky obrazovek budou moci předat uživatelům význam. [Další informace o alternativním textu pro prvky `object`](https://dequeuniversity.com/rules/axe/4.8/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Prvky `<object>` nemají alternativní text"}, "core/audits/accessibility/object-alt.js | title": {"message": "Prvky `<object>` mají alternativní text"}, "core/audits/accessibility/select-name.js | description": {"message": "Prvky formul<PERSON><PERSON><PERSON> bez efektivních štítk<PERSON> mohou být pro uživatele čteček obrazovek frustrující. [Další informace o prvku `select`](https://dequeuniversity.com/rules/axe/4.8/select-name)"}, "core/audits/accessibility/select-name.js | failureTitle": {"message": "Prvky select nemají přidružené prvky label."}, "core/audits/accessibility/select-name.js | title": {"message": "Prvky select mají přidružené prvky label."}, "core/audits/accessibility/skip-link.js | description": {"message": "Zahrnutím odkazu pro přeskočení uživatelům umožníte přeskočit na hlavní obsah a ušetřit tak čas. [Další informace o odkazech pro přeskočení](https://dequeuniversity.com/rules/axe/4.8/skip-link)"}, "core/audits/accessibility/skip-link.js | failureTitle": {"message": "Odkazy pro přeskočení nelze vybrat."}, "core/audits/accessibility/skip-link.js | title": {"message": "Odkazy pro přeskočení lze vybrat."}, "core/audits/accessibility/tabindex.js | description": {"message": "Hodnota větší než 0 naznačuje explicitní řazení navigace. Ačkoli je platná, často vede k chování, kter<PERSON> je pro uživatele závislé na asistenčních technologiích nepříjemné. [Další informace o atributu `tabindex`](https://dequeuniversity.com/rules/axe/4.8/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Některé prvky mají hodnot<PERSON> `[tabindex]` větší než 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Žádný prvek nemá hodnotu `[tabindex]` větší než 0"}, "core/audits/accessibility/table-duplicate-name.js | description": {"message": "Atribut summary by mě<PERSON> popisovat struk<PERSON><PERSON> tabul<PERSON>, zatímco prvek `<caption>` by mě<PERSON> obsahovat název zobrazený na obrazovce. Správné označení tabulek pomáhá uživatelům čteček obrazovek. [Další informace o atributu summary a prvku caption](https://dequeuniversity.com/rules/axe/4.8/table-duplicate-name)"}, "core/audits/accessibility/table-duplicate-name.js | failureTitle": {"message": "Tabulky mají stejný obsah v atributu summary a v prvku `<caption>.`"}, "core/audits/accessibility/table-duplicate-name.js | title": {"message": "Tabulky mají různý obsah v atributu summary a v prvku `<caption>`."}, "core/audits/accessibility/table-fake-caption.js | description": {"message": "Čtečky obrazovek maj<PERSON>, kter<PERSON> usnadňují procházení tabulek. Pokud v tabulkách namísto buněk s atributem `[colspan]` použijete skutečné prvky popisků (caption), m<PERSON><PERSON><PERSON> to zlepšit uživatelský dojem uživatelů čteček obrazovky. [Další informace o popiscích](https://dequeuniversity.com/rules/axe/4.8/table-fake-caption)"}, "core/audits/accessibility/table-fake-caption.js | failureTitle": {"message": "V tabulkách nejsou k označení popisků použity prvky `<caption>` (namísto buněk s atributem `[colspan]`)."}, "core/audits/accessibility/table-fake-caption.js | title": {"message": "V tabulkách jsou k označení popisků použity prvky `<caption>` (namísto buněk s atributem `[colspan]`)."}, "core/audits/accessibility/target-size.js | description": {"message": "Dostatečně velká místa dotyku s dostatečnými rozestupy usnadňují aktivaci uživatelům, kte<PERSON><PERSON> mohou mít potíže s výběrem malých ovládacích prvků. [Další informace o místech dotyku](https://dequeuniversity.com/rules/axe/4.8/target-size)"}, "core/audits/accessibility/target-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> dotyku nejsou dostatečně velká nebo nemají dostatečn<PERSON> r<PERSON>est<PERSON>."}, "core/audits/accessibility/target-size.js | title": {"message": "<PERSON>ís<PERSON> dot<PERSON>u jsou dostate<PERSON> velk<PERSON> a mají dostate<PERSON>é rozestup<PERSON>."}, "core/audits/accessibility/td-has-header.js | description": {"message": "Čtečky obrazovek maj<PERSON>, kter<PERSON> usnadňují procházení tabulek. Pokud prvky `<td>` ve velké tabulce (tři nebo více buněk na šířku a výšku) budou mít přid<PERSON><PERSON><PERSON><PERSON> z<PERSON>av<PERSON> tabul<PERSON>, u<PERSON><PERSON><PERSON><PERSON><PERSON> č<PERSON>č<PERSON> obrazovky to usnadní orientaci. [Další informace o záhlavích tabulek](https://dequeuniversity.com/rules/axe/4.8/td-has-header)"}, "core/audits/accessibility/td-has-header.js | failureTitle": {"message": "Prvky `<td>` ve velké tabulce `<table>` nema<PERSON><PERSON>."}, "core/audits/accessibility/td-has-header.js | title": {"message": "Prvky `<td>` ve velké tabulce `<table>` mají ale<PERSON> jedno záhlaví."}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Čtečky obrazovek maj<PERSON>, k<PERSON><PERSON> usnadňují procházení tabulek. <PERSON><PERSON><PERSON>, aby buň<PERSON> `<td>` s atributem `[headers]` odkazovaly pouze na jiné buňky ve stejné tabulce, můžete tím uživatelům čteček obrazovek usnadnit používání. [Další informace o atributu `headers`](https://dequeuniversity.com/rules/axe/4.8/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Buňky v prvku `<table>`, které mají atribut `[headers]`, odkazují na prvek `id`, který se nenachází ve stejné tabulce."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Buňky v prvku `<table>`, které mají atribut `[headers]`, odkazují na buňky ve stejné tabulce."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čtečky obrazovek maj<PERSON>, k<PERSON><PERSON> us<PERSON>dňují procházení tabulek. <PERSON><PERSON><PERSON>, aby záhlaví tabulek vždy odkazovala na nějakou množinu buněk, bude pro uživatele čteček obrazovek procházení stránky snazší. [Další informace o záhlavích tabulek](https://dequeuniversity.com/rules/axe/4.8/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Prvky `<th>` a prvky s atributem `[role=\"columnheader\"/\"rowheader\"]` ne<PERSON><PERSON><PERSON> b<PERSON>, k<PERSON><PERSON>."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Prvky `<th>` a prvky s atributem `[role=\"columnheader\"/\"rowheader\"]` maj<PERSON> b<PERSON>, k<PERSON><PERSON>."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Pokud je u prvků uveden platný [jazyk BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomů<PERSON><PERSON> to zajistit, aby čtečky obrazovek text četly správně. [Jak používat atribut `lang`](https://dequeuniversity.com/rules/axe/4.8/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributy `[lang]` nema<PERSON><PERSON> platnou hodnotu"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributy `[lang]` mají platnou hodnotu"}, "core/audits/accessibility/video-caption.js | description": {"message": "<PERSON><PERSON>ž jsou u videa k dispozici titulky, je pro sluchově postižené uživatele snazší využít informace ve videu. [Další informace o titulcích videí](https://dequeuniversity.com/rules/axe/4.8/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Prvky `<video>` neo<PERSON><PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Prvky `<video>` obs<PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Současná hodnota"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token"}, "core/audits/autocomplete.js | description": {"message": "Atribut `autocomplete` uživatelům pomáhá odesílat formuláře rychleji. Zvažte nastavení atributu `autocomplete` na platnou hodnotu, abyste uživatelům usnadnili zadávání. [Další informace o atributu `autocomplete` ve formulářích](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "Prvky `<input>` nema<PERSON><PERSON> správné atributy `autocomplete`"}, "core/audits/autocomplete.js | manualReview": {"message": "Vyžaduje manuální kontrolu"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Zkontrolujte pořadí tokenů"}, "core/audits/autocomplete.js | title": {"message": "U prvků `<input>` je správně použit atribut `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Tokeny `autocomplete`: Token „{token}“ v atributu {snippet} je neplatný"}, "core/audits/autocomplete.js | warningOrder": {"message": "Zkontrolujte pořadí tokenů {tokens} v {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Lze provést a<PERSON>"}, "core/audits/bf-cache.js | description": {"message": "Mnoho navigace provádí uživatelé návratem na předchozí stránku nebo návratem vpřed. Mezipaměť pro přechod zpět nebo vpřed (bfcache) může tyto navigace zrychlit. [Další informace o mezipaměti bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 př<PERSON><PERSON><PERSON> selhán<PERSON>}few{# příčiny selhán<PERSON>}many{# příčiny selhán<PERSON>}other{# př<PERSON><PERSON>in selh<PERSON>}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Př<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | failureTitle": {"message": "Stránka zabránila obnovení z mezipaměti pro přechod zpět nebo vpřed"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "<PERSON><PERSON><PERSON> prov<PERSON> a<PERSON>"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Funkce v prohlížeči není podporována"}, "core/audits/bf-cache.js | title": {"message": "Stránka nebránila obnovení z mezipaměti pro přechod zpět nebo vpřed"}, "core/audits/bf-cache.js | warningHeadless": {"message": "Mezipaměť pro přechod zpět nebo vpřed nelze testovat ve starém Chromu bez grafické vrstvy (`--chrome-flags=\"--headless=old\"`). Pokud chcete zobrazit výsledky auditu, použijte nový Chrome bez grafické vrstvy (`--chrome-flags=\"--headless=new\"`) nebo standardní Chrome."}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Rychlost načítání této stránky byla negativně ovlivněna rozšířeními pro Chrome. Zkuste stránku zkontrolovat v anonymním režimu nebo profilu Chromu bez rozšíření."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Vyhodnocování sk<PERSON>tů"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>ů"}, "core/audits/bootup-time.js | columnTotal": {"message": "Celková doba využití procesoru"}, "core/audits/bootup-time.js | description": {"message": "Pokuste se zkrátit dobu analyzování, kompilování a spouštění JavaScriptu. Mohlo by pomoci odesílat menší soubory JavaScript. [Jak zkrátit dobu spouštění JavaScriptu](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "Zkraťte dobu provádění JavaScriptu"}, "core/audits/bootup-time.js | title": {"message": "Doba provádění <PERSON>u"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Odstraňte z balíčků velké duplicitní moduly JavaScriptu, abyste omezili zbytečné zatížení sítě. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Odstraňte duplicitní moduly v balíčcích JavaScriptu"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Velké soubory GIF nejsou efektivní při zobrazování animovaného obsahu. Zvažte, zda byste namísto souborů GIF nemohli pro animace použít videa MPEG4/WebM a pro statické obrázky soubory PNG/WebP. Snížíte tak množství přenášených dat. [Další informace o efektivních formátech videí](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Pro animovaný obsah používejte formáty videa"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Doplňující kódy polyfill a transformace umožňují starším prohlížečům používat nové funkce JavaScriptu. Moderní prohlížeče ale řadu z nich nepotřebují. U svých balíčků JavaScriptu použijte moderní strategii nasazení skriptu pomocí detekce module/nomodule, abyste omezili množství kódu přenášeného do moderních prohlížečů, a přitom zachovali podporu pro starší prohlížeče. [Jak používat moderní JavaScript](https://web.dev/articles/publish-modern-javascript)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Nepoužívejte v moderních prohlížečích zastaralý JavaScript"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Formáty obrázků jako WebP a AVIF často nabízejí lepší kompresi než PNG nebo JPEG, což znamená rychlejší stahování a menší využití dat. [Další informace o moderních formátech obrázků](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Zobrazujte obrázky ve formátech nové generace"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Zvažte možnost načítat obrázky mimo obrazovku a skryté obrázky „líně“ až po načtení všech kritických zdrojů, abyste zkrátili dobu k dosažení interaktivnosti. [Jak odložit načtení obrázků mimo obrazovku](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odložte načítání obrázků mimo obrazovku"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "První vykreslení stránky blokují zdroje. Zvažte, zda byste kriticky důležité zdroje JavaScript a CSS nemohli poskytovat přímo v kódu a stahování veškerého nekritického JavaScriptu a stylů odložit. [Jak eliminovat zdroje blokující vykreslování](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> blo<PERSON> v<PERSON>len<PERSON>"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Přenášení velkého množství dat po síti je pro uživatele finančně nákladné a obvykle vede k pomalému načítání. [Jak velikost přenášených dat snížit](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Celková velikost byla {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Předejděte přenášení enormního množství dat"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Nepřenáš<PERSON> enormní množství dat"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifikací souborů CSS lze snížit množství přenášených dat. [<PERSON><PERSON> minifikovat CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifikujte kód CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifikací souborů JavaScript lze snížit množství přenášených dat a zrychlit analýzu skriptů. [Jak minifikovat JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifikujte JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Odstraňte ze šablon stylů nepoužívaná pravidla a odložte styly CSS, které se nepoužívají pro obsah nad okrajem, abyste snížili množství dat využívaných síťovou aktivitou. [Jak omezit nevyužité styly CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Odstraňte nepoužívané styly CSS"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Odstraňte nepoužívaný JavaScript a odložte načítání skriptů, abyste sní<PERSON>ili množství dat využívaných síťovou aktivitou. [Jak omezit nepoužívaný kód JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Odstraňte nepoužívaný JavaScript"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dlouhá platnost mezipaměti může zrychlit opakované návštěvy stránky. [Další informace o efektivních zásadách pro mezipaměť](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 zdroj}few{<PERSON><PERSON> nalezeny # zdroje}many{<PERSON><PERSON> nalezeno # zdroje}other{Bylo nalezeno # zdrojů}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statické podklady zobrazujte s efektivními zásadami pro mezipaměť"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Používá u statických podkladů efektivní zásady pro mezipaměť"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimalizované obrázky se načítají rychle a spotřebovávají méně mobilních dat. [Jak efektivně zakódovat obrázky](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Používejte efektivní kódování obrázků"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Obrázky byly větší než zobrazovaná velikost"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Obrázky byly vhodné pro zobrazovanou velikost"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Zobrazujte obrázky s vhodnou velikostí, abyste ušetřili mobilní data a zrychlili načítání. [Jak zvolit velikost obrázků](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Používejte správnou velikost obrázků"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textové zdroje by se m<PERSON><PERSON> o<PERSON> komprimovan<PERSON> (gzip, deflate nebo brotli), aby se <PERSON><PERSON><PERSON>lo množství přenášen<PERSON>ch dat. [Další informace o kompresi textu](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Zapněte kompresi textu"}, "core/audits/content-width.js | description": {"message": "Pokud se šířka obsahu aplikace neshoduje se šířkou zobrazované oblasti, aplikace nemusí být optimalizována pro obrazovky mobilních telefonů. [Další informace o výběru velikosti obsahu pro zobrazovanou oblast](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "Velikost zobrazované oblasti {innerWidth} px se neshoduje s velikostí okna {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Obsah nemá správnou velikost pro zobrazovanou oblast"}, "core/audits/content-width.js | title": {"message": "Obsah má správnou velikost pro zobrazovanou oblast"}, "core/audits/critical-request-chains.js | description": {"message": "Řetězce kritických požadavků níže ukazují, kter<PERSON> zdroje se načítají s vysokou prioritou. <PERSON><PERSON><PERSON><PERSON>, zda byste načítání stránky nemohli vylep<PERSON>it tím, že řetězce zkr<PERSON>í<PERSON>, zmenšíte zdroje nebo odložíte stahování zdrojů, které nejsou nezbytné. [Jak předejít zřetězení kritických požadavků](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 řetěze<PERSON>}few{Byly nalezeny # řetězce}many{Bylo nalezeno # řetězce}other{Bylo nalezeno # řetězců}}"}, "core/audits/critical-request-chains.js | title": {"message": "Nezřetězuje kritické p<PERSON>žadavky"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Závažnost"}, "core/audits/csp-xss.js | description": {"message": "<PERSON><PERSON><PERSON> z<PERSON> z<PERSON> (CSP) významně snižují riziko útoků skriptováním mezi weby (XSS). [Jak pomocí CSP zabránit skriptování mezi weby](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntaxe"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Stránka obsahuje zásady CSP definované ve značce `<meta>`. Zvažte přesunutí zásad CSP do záhlaví protokolu HTTP nebo definování dalších striktních zásad CSP v záhlaví protokolu HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "V režimu vynucení nejsou žádné CSP"}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON> CSP byly <PERSON> proti útokům XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Ukončení podpory / upozornění"}, "core/audits/deprecations.js | columnLine": {"message": "Řádek"}, "core/audits/deprecations.js | description": {"message": "Zastaralá rozhraní API budou z prohlížeče v budoucnu odstraněna. [Další informace o zastaralých rozhraních API](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Bylo nalezeno 1 upozornění}few{Byla nalezena # upozornění}many{Bylo nalezeno # upozornění}other{Bylo nalezeno # upozornění}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Používá zastaralá rozhraní API"}, "core/audits/deprecations.js | title": {"message": "Nepoužívá zastaralá rozhraní API"}, "core/audits/dobetterweb/charset.js | description": {"message": "Je požadována deklarace kódování znaků. Lze ho deklarovat pomocí značky `<meta>` v prvních 1024 bajtech kódu HTML nebo pomocí záhlaví Content-Type odpovědi HTTP. [Další informace o deklarování kódování znaků](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Deklarace znakové sady chybí nebo k ní v HTML dochází příliš pozdě"}, "core/audits/dobetterweb/charset.js | title": {"message": "Správně definuje z<PERSON> sadu"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Zadáním typu dokumentu (DOCTYPE) předejdete přechodu prohlížeče do adaptivního režimu. [Další informace o deklaraci typu dokumentu (doctype)](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Název typu dokumentu (DOCTYPE) musí být ř<PERSON> `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokument obsahu<PERSON> `doctype`, k<PERSON><PERSON> spouš<PERSON><PERSON> režim `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument musí obsahovat deklaraci typu dokumentu DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "V poli publicId je očekáván prázdný řetězec"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "V poli systemId je očekáván prázdný řetězec"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokument obsahu<PERSON> `doctype`, k<PERSON><PERSON> spoušt<PERSON> režim `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Na stránce není deklarace typu dokumentu (DOCTYPE) HTML, proto se aktivuje adaptivní re<PERSON>im"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Stránka má deklaraci typu dokumentu (DOCTYPE) HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Hodnota"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Velký model DOM povede k vět<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>, prodl<PERSON>žen<PERSON> [vý<PERSON><PERSON><PERSON><PERSON> stylů](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) a náročným [přeform<PERSON>továváním rozvržení](https://developers.google.com/speed/articles/reflow). [Jak předejít nadměrné velikosti modelu DOM](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 prvek}few{# prvky}many{# prvku}other{# prvků}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Nepoužívejte př<PERSON><PERSON>š <PERSON>lk<PERSON> model DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximální hloubka modelu DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Celkový počet prvků DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximální počet podřízených prvků"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Nepoužívá př<PERSON><PERSON><PERSON> velk<PERSON> model DOM"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kter<PERSON> bez kontextu žádají o polohu, mohou být uživatelé nedůvěřiví nebo z nich mohou být zmateni. Zvažte možnost spojit tuto žádost s akcí uživatele. [Další informace o oprávnění ke geolokaci](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Žádá při načtení stránky o oprávnění ke geolokaci"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Nežádá při načtení stránky o oprávnění ke geolokaci"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON><PERSON> pro<PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Problémy uvedené na panelu `Issues` v Chrome DevTools ukazují na nevyřešené problémy. <PERSON><PERSON> být důsledkem selhání síťových požadavků, nedostatečného zabezpečení nebo jiných problémů v prohlížeči. Podrobnosti o jednotlivých problémech najdete na panelu Issues (Problémy) v Chrome DevTools."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problémy byly zaprotokolovány na panelu `Issues` v Chrome DevTools"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Zablokováno kvůli zásadě pro požadavky z různých zdrojů"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Intenzivní využívání zdrojů re<PERSON>mi"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Na panelu `Issues` v Chrome DevTools nejsou žádné problémy"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verze"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Všechny frontendové javascriptové knihovny zjištěné na stránce. [Další informace o tomto diagnostickém auditu zjišťování javascriptových knihoven](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "<PERSON><PERSON> javascriptové knihovny"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "U uživatelů s pomalým připojením mohou externí skripty vkládané metodou `document.write()` načtení stránky zpozdit o desítky sekund. [Jak se vyhnout použití metody document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Nepoužívejte `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Nepoužívá metodu `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, které bez kontextu žádají o oprávnění odesílat oznámení, mohou být uživatelé nedůvěřiví nebo z nich mohou být zmateni. Zvažte možnost spojit tuto žádost s gesty uživatele. [Další informace o odpovědném získání oprávnění zobrazovat oznámení](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Žádá při načtení stránky o oprávnění zobrazovat oznámení"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Nežádá při načtení stránky o oprávnění zobrazovat oznámení"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Zabraňovat vkládání obsahu do vstupních polí je v rozporu s doporučenými postupy v oblasti uživatelského prostředí. Také to znemožňuje používání správců hesel a tím oslabuje bezpečnost.[Další informace o uživatelsky přívětivých zadávacích polích](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)"}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Brání uživatelům ve vkládání obsahu do zadávacích polí"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Povoluje uživatelům vkládání obsahu do zadávacích polí"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "Protokol HTTP/2 oproti protokolu HTTP/1.1 nabízí mnoho výhod, v<PERSON><PERSON><PERSON><PERSON> binárních záhlaví a multiplexingu. [Další informace o protokolu HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 požadavek nebyl realizován pomocí protokolu HTTP/2}few{# požadavky nebyly realizovány pomocí protokolu HTTP/2}many{# požadavku nebylo realizováno pomocí protokolu HTTP/2}other{# požadavků nebylo realizováno pomocí protokolu HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Použijte HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Zvažte označení posluchačů událostí dotyku a kolečka jako pasivn<PERSON>ch (`passive`), aby se stránka posouvala plynuleji. [Další informace o používání pasivních posluchačů událostí](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nepoužívá pasivní p<PERSON>, k<PERSON><PERSON> p<PERSON>"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Používá pasivní p<PERSON>, k<PERSON><PERSON><PERSON><PERSON> poso<PERSON>"}, "core/audits/errors-in-console.js | description": {"message": "Chyby zaprotokolované do konzole ukazují na nevyřešené problémy. Mohou pocházet ze selhání síťových požadavků nebo jiných problémů v prohlížeči. [Další informace o těchto chybách v diagnostickém auditu v konzoli](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Do konzole byly zaprotokolovány chyby prohlížeče"}, "core/audits/errors-in-console.js | title": {"message": "Do konzole nebyly zaprotokolovány žádné chyby prohlížeče"}, "core/audits/font-display.js | description": {"message": "V<PERSON><PERSON><PERSON><PERSON><PERSON> funkci CSS `font-display`, aby byl text b<PERSON><PERSON> na<PERSON>í<PERSON>án<PERSON> webových písem viditelný pro uživatele. [Další informace o funkci `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby text při načítání webfontů zůstal viditelný"}, "core/audits/font-display.js | title": {"message": "Při načítání webfontů zůstává veškerý text viditelný"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON><PERSON>in,plural, =1{Nástroj Lighthouse u zdroje {fontOrigin} nedokázal automaticky zkontrolovat hodnotu `font-display`.}few{Nástroj Lighthouse u zdroje {fontOrigin} nedokázal automaticky zkontrolovat hodnoty `font-display`.}many{Nástroj Lighthouse u zdroje {fontOrigin} nedokázal automaticky zkontrolovat hodnoty `font-display`.}other{Nástroj Lighthouse u zdroje {fontOrigin} nedokázal automaticky zkontrolovat hodnoty `font-display`.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> (skutečný)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON> (zobrazený)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Zobrazované roz<PERSON>ěry obr<PERSON> by m<PERSON><PERSON><PERSON><PERSON> př<PERSON>ému poměru stran. [Další informace o poměru stran obrázků](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Zobrazuje obrázky s nesprávným poměrem stran"}, "core/audits/image-aspect-ratio.js | title": {"message": "Zobrazuje obrázky se správným poměrem stran"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Skutečná velikost"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Zobrazená velikost"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Očekávaná velikost"}, "core/audits/image-size-responsive.js | description": {"message": "Kv<PERSON><PERSON> zaj<PERSON>štění maximální ostrosti obr<PERSON>ů by j<PERSON><PERSON> p<PERSON><PERSON> roz<PERSON> měly být úměrné velikosti displeje a poměru pixelů. [Jak poskytovat responzivní obrázky](https://web.dev/articles/serve-responsive-images)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Zobrazuje obrázky s nízkým rozlišením"}, "core/audits/image-size-responsive.js | title": {"message": "Zobrazuje obrázky se správným rozlišením"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikace je již na<PERSON>"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Z manifestu nelze stáhnout požadovanou ikonu"}, "core/audits/installable-manifest.js | columnValue": {"message": "Př<PERSON><PERSON><PERSON>"}, "core/audits/installable-manifest.js | description": {"message": "Pracovní proces služby je technologie, která aplikaci umožňuje využívat mnoho funkcí progresivních webových aplikací, jako jsou rež<PERSON> offline, přidání na plochu nebo oznámení push. Při správné implementaci skriptu pracovní proces služby a manifestu mohou prohlížeče uživatele aktivně vyzývat, aby si aplikaci přidali na plochu, což může vést k vyšší míře interakce. [Další informace o požadavcích na instalovatelnost manifestu](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 důvod}few{# důvody}many{# důvodu}other{# důvo<PERSON>ů}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifest webové aplikace nebo pracovní proces služby nesplňují instalační požadavky"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Adresa URL aplikace v Obchodu Play a ID Obchodu Play si vzájemně neodpovídají"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Stránka je načtena v anonymním okně"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Vlastnost `display` manifestu musí být jedna z těchto možností `standalone`, `fullscreen` nebo `minimal-ui`"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest obsahuje pole „display_override“ a první podporovaný režim zobrazení musí být „standalone“, „fullscreen“ nebo „minimal-ui“"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifest ne<PERSON><PERSON>, analyzovat nebo je pr<PERSON>z<PERSON>ý"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Adresa URL manifestu se během jeho načítání změnila."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest neo<PERSON><PERSON><PERSON> pole `name` ani `short_name`"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest neobsahuje vhodnou ikonu – je vyžadován formát PNG, SVG nebo WebP o velikosti alespoň {value0} px, musí být nastaven atribut velikostí a atribut účelu, pokud je nastaven, musí zahrnovat hodnoty „any“."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Žádná poskytnutá ikona není čtverec se stranou {value0} px ve formátu PNG, SVG nebo WebP s atributem účelu nenastaveným nebo nastaveným na hodnotu „any“"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Stažená ikona byla prázdná nebo poškozená"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "K dispozici není žádné ID Obchodu Play"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Stránka neobsahuje prvek <link> s adresou URL manifestu"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Skript pracovní proces služby nelze zkontrolovat bez pole „start_url“ v manifestu"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID chyby nestability {errorId} nelze rozpoznat"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Stránka se nenačítá z bezpečného zdroje"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Stránka se nenačítá v hlavním rámci"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Stránka nefunguje offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Progresivní webová aplikace byla o<PERSON> a probíhá resetování kontrol instalovatelnosti."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Zadaná aplikační platforma v systému Android není podporována"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "V manifestu je zadáno prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Parametr prefer_related_applications je podporován pouze v beta verzi Chromu a ve stabilních verzích v systému Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Nástroj Lighthouse nedokázal zjistit, zda je stránka instalovatelná. Zkuste to s novější ve<PERSON>."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Počáteční URL (start_url) v manifestu není platná"}, "core/audits/installable-manifest.js | title": {"message": "Manifest webové aplikace a pracovní proces služby splňují instalační požadavky"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Adresa URL v manifestu obsahuje uživatelské jméno, heslo nebo port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Stránka nefunguje offline. Stránka nebude po vydání stabilní verze Chromu 93 v srpnu 2021 pokládána za instalovatelnou."}, "core/audits/is-on-https.js | allowed": {"message": "Povoleno"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nezabezpečená adresa URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "Řešení požadavku"}, "core/audits/is-on-https.js | description": {"message": "Všechny weby (v<PERSON><PERSON><PERSON><PERSON> těch, k<PERSON><PERSON> s citlivými daty), by m<PERSON><PERSON> b<PERSON><PERSON>r<PERSON>ny protokolem HTTPS. Kromě jiného je třeba se vyhnout [smíšené<PERSON> obsahu](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), tj. načítání některých zdrojů přes HTTP, ačkoliv počáteční požadavek proběhl přes HTTPS. Protokol HTTPS útočníkům znemožňuje upravovat komunikaci mezi aplikací a už<PERSON>li (nebo ji pasivně poslouchat) a je nezbytný pro HTTP/2 a mnoho nových webových rozhraní API. [Další informace o protokolu HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 nezabezpečený požadavek}few{Byly nalezeny # nezabezpečené požadavky}many{Bylo nalezeno # nezabezpečeného požadavku}other{Bylo nalezeno # nezabezpečených požadavků}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Nepoužívá protokol HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Používá protokol HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Automaticky upgradováno na HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Povoleno s upozorněním"}, "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": {"message": "% LCP"}, "core/audits/largest-contentful-paint-element.js | columnPhase": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/largest-contentful-paint-element.js | columnTiming": {"message": "Časování"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Toto je největší obsahový prvek vykreslený v zobrazované oblasti. [Další informace o prvku spojeném s vykreslením největšího obsahu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | itemLoadDelay": {"message": "Prodleva načítání"}, "core/audits/largest-contentful-paint-element.js | itemLoadTime": {"message": "Doba načítání"}, "core/audits/largest-contentful-paint-element.js | itemRenderDelay": {"message": "Prodleva vykreslení"}, "core/audits/largest-contentful-paint-element.js | itemTTFB": {"message": "TTFB"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Prvek Largest Contentful Paint"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Dopad změny rozvržení"}, "core/audits/layout-shift-elements.js | description": {"message": "Tyto prvky DOM byly změnami rozvržení ovlivněny nejvíce. Některé změny rozvržení do hodnoty metriky CLS vzhledem k [normalizaci ](https://web.dev/articles/cls#what_is_cls) nemusí zahrnuty. [Jak zlepšit CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shift-elements.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby ne<PERSON><PERSON><PERSON> k velkým změnám rozvržení"}, "core/audits/layout-shifts.js | columnScore": {"message": "Skóre změny rozvržení"}, "core/audits/layout-shifts.js | description": {"message": "Toto jsou největší změny rozvržení pozorované na stránce. Každá položka v tabulce představuje jednu změnu rozvržení a je u ní uvedeno, který prvek se posunul nejvíce. Pod každou položkou jsou uvedeny možné prvotní příčiny změny rozvržení. Některé z těchto změn rozvržení do hodnoty metriky CLS vzhledem k [použití okenního systému](https://web.dev/articles/cls#what_is_cls) nemusí zahrnuty. [Jak zlepšit CLS](https://web.dev/articles/optimize-cls)"}, "core/audits/layout-shifts.js | displayValueShiftsFound": {"message": "{shiftCount,plural, =1{Byla nalezena 1 změna rozvržení}few{Byly nalezeny # změny rozvržení}many{Bylo nalezeno # změny rozvržení}other{Bylo nalezeno # změn rozvržení}}"}, "core/audits/layout-shifts.js | rootCauseFontChanges": {"message": "<PERSON><PERSON> načteno webové písmo"}, "core/audits/layout-shifts.js | rootCauseInjectedIframe": {"message": "Byl vložen prvek iframe"}, "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": {"message": "Rozvržení str<PERSON>ky bylo upraveno pozdním síťovým požadavkem"}, "core/audits/layout-shifts.js | rootCauseUnsizedMedia": {"message": "U prvku média není explicitně zadána velikost"}, "core/audits/layout-shifts.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby ne<PERSON><PERSON><PERSON> k velkým změnám rozvržení"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Líně načtené obrázky nad okrajem se vykreslí později v životním cyklu stránky, což může zpozdit vykreslení největšího obsahu. [Další informace o optimálním líném načítání](https://web.dev/articles/lcp-lazy-loading)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Obrázek vykreslení největšího obsahu byl líně na<PERSON>"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Obrázek vykreslení největšího obsahu nebyl líně na<PERSON>"}, "core/audits/long-tasks.js | description": {"message": "Vypíše nejdelší úlohy v hlavním podprocesu, což umožňuje zjistit, co nejvíce přispívá k prodlevě vstupu. [Jak se vyhnout dlouhým úlohám v hlavními vláknu](https://web.dev/articles/long-tasks-devtools)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Byla nalezena # dlouh<PERSON>}few{Byly nalezeny # dlouhé <PERSON>}many{Bylo nalezeno # dlouhé <PERSON>}other{Bylo nalezeno # dlouhých ú<PERSON>}}"}, "core/audits/long-tasks.js | title": {"message": "V hlavním podprocesu nepoužívejte dlouhé <PERSON>hy"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Pokuste se zkrátit dobu analyzování, kompilování a spouštění JavaScriptu. Mohlo by pomoci odesílat menší soubory JavaScript. [Jak minimalizovat činnost v hlavním vláknu](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalizujte práci v hlavním podprocesu"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizuje práci v hlavním podprocesu"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Pokud chcete zasáhnout co největší počet uživatelů, měl by web fungovat ve všech nejpoužívanějších prohlížečích. [Další informace o kompatibilitě s různými prohlížeči](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Web funguje v různých prohlížečích"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby na jednotlivé stránky bylo možné přidat přímý odkaz prostřednictvím adresy URL a aby s ohledem na možnost sdílení na sociálních sítích byly adresy URL jedinečné. [Další informace o poskytování přímých odkazů](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Každá stránka má adresu URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Přechody by m<PERSON><PERSON> b<PERSON><PERSON>lynulé i v pomalé síti. Je to velmi důležité pro dobrý uživatelský pocit. [Další informace o přechodech stránek](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Přechody na jiné stránky nepůsobí zabržděně"}, "core/audits/maskable-icon.js | description": {"message": "Maskovatelná ikona zajiš<PERSON>uje, že obrázek při instalaci aplikace do zařízení vyplní celý rámec a nevyskytnou se černé okraje. [Informace o maskovatelných ikonách v manifestu](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest nem<PERSON> maskovatelnou ikonu"}, "core/audits/maskable-icon.js | title": {"message": "Manifest má maskovatelnou ikonu"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulativní změna rozvržení měří přesuny viditelných prvků v zobrazované oblasti. [Další informace o metrice Kumulativní změna rozvržení](https://web.dev/articles/cls)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "První vykreslení obsahu je okamžik vykreslení prvního textu nebo obrázku. [Další informace o metrice První vykreslení obsahu (FCP)](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "První smysluplné vykreslení udává, kdy za<PERSON> být viditelný primární obsah stránky. [Další informace o metrice První smysluplné vykreslení](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interaction-to-next-paint.js | description": {"message": "Doba od interakce k dalšímu vykreslení měří rychlos<PERSON> odezvy s<PERSON>, tj. za jak dlouho stránka viditelně zareaguje na uživatelský vstup. [Další informace o metrice Doba od interakce k dalšímu vykreslení](https://web.dev/articles/inp)"}, "core/audits/metrics/interactive.js | description": {"message": "Doba do interaktivity ud<PERSON><PERSON><PERSON>, jak d<PERSON><PERSON> trv<PERSON>, ne<PERSON> stránka za<PERSON>ne být plně interaktivní. [Další informace o metrice Doba do interaktivity](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Vykreslení největšího obsahu udává čas, kdy byl vykreslen největší text nebo obrázek. [Další informace o metrice Vykreslení největšího obsahu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Maximální potenciální prodleva prvního vstupu, k<PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON> mohli poc<PERSON>, je tr<PERSON><PERSON> ne<PERSON>. [Další informace o metrice Maximální potenciální prodleva prvního vstupu](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "Index rychlosti ukazuje, jak rychle se viditelně vyplní obsah str<PERSON>. [Další informace o metrice Index rychlosti](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Součet všech dob (uvedený v milisekundách) mezi prvním vykreslením obsahu a dobou do interaktivity, u nichž délka úlohy překročila 50 ms. [Další informace o metrice Celková doba blokování](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "Na výkon má velký vliv doba odezvy sítě. Pokud je doba odezvy připojení ke zdroji vysoká, zna<PERSON><PERSON> to, že by se výkon mohl zlepšit při použití serverů méně vzdálených od uživatele. [Další informace o době odezvy](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON> odez<PERSON> s<PERSON>"}, "core/audits/network-server-latency.js | description": {"message": "Na výkon webu může mít dopad latence serverů. Vysoká latence serveru značí, že je server přetížen nebo že backend nen<PERSON> dostatečně výkonný. [Další informace o době odezvy serveru](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "Latence backendu na serveru"}, "core/audits/no-unload-listeners.js | description": {"message": "Ud<PERSON>lost `unload` se nespouští spolehlivě. Naslouchání této události může zabránit optimalizacím v prohlížečích, jako je mezipaměť pro přechod zpět nebo vpřed. Použijte místo ní událost `pagehide` nebo `visibilitychange`. [Další informace o posluchačích událostí unload](https://web.dev/articles/bfcache#never_use_the_unload_event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ud<PERSON><PERSON> `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Nepoužívá posluchače událostí `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Neskládané animace mohou být nekvalitní a mohou zvyšovat míru kumulativní změny rozvržení (CLS). [Jak se vyhnout neskládaným animacím](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen # animovaný prvek}few{Byly nalezeny # animované prvky}many{Bylo nalezeno # animovaného prvku}other{Bylo nalezeno # animovaných prvků}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Vlastnost související s filtrováním může přesouvat pixely"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON><PERSON> m<PERSON> da<PERSON><PERSON> an<PERSON>, k<PERSON><PERSON> není kompatibilní"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Efekt má jiný režim skládání než „replace“"}, "core/audits/non-composited-animations.js | title": {"message": "Nepoužívejte neskládané animace"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Vlastnost související s transformací závisí na velikosti pole"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepodporovaná vlastnost CSS: {properties}}few{Nepodporované vlastnosti CSS: {properties}}many{Nepodporované vlastnosti CSS: {properties}}other{Nepodporované vlastnosti CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Efekt má nepodporované parametry časování"}, "core/audits/performance-budget.js | description": {"message": "Udržujte množství a velikost síťových požadavků pod cílovými hodnotami stanovenými v poskytnutém rozpočtu výkonu. [Další informace o rozpočtech výkonu](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 požadavek}few{# požadavky}many{# požadavku}other{# požadavků}}"}, "core/audits/performance-budget.js | title": {"message": "Rozpočet výkonu"}, "core/audits/preload-fonts.js | description": {"message": "Načítejte písma `optional` <PERSON><PERSON><PERSON><PERSON>, aby je mohli pou<PERSON> i návštěvníci, kte<PERSON><PERSON> přicházejí poprvé. [Další informace o předběžném načítání písem](https://web.dev/articles/preload-optional-fonts)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Písma s atributem `font-display: optional` se nenačítají předem"}, "core/audits/preload-fonts.js | title": {"message": "Písma s atributem `font-display: optional` se načítají předem"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Pokud je na stránce dynamicky přidán prvek LCP, mě<PERSON> byste obrázek načítat předem, abyste dosáhli lepší hodnoty LCP. [Další informace o předběžném načítání prvků LCP](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "Předběžné načtení obrázku s vykreslením největšího obsahu"}, "core/audits/redirects.js | description": {"message": "Přesměrování způsobují dalš<PERSON> prodlevy před načtením strán<PERSON>. [Jak se vyhnout přesmě<PERSON><PERSON><PERSON> stránek](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "Nepoužívejte několik přesměrování stránky"}, "core/audits/seo/canonical.js | description": {"message": "Odkazy na kanonické verze slouží jako n<PERSON>hy, kter<PERSON> adresy URL se mají zobrazovat ve výsledcích vyhledávání. [Další informace o kanonických odkazech](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Několik konfliktních adres URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Neplatná adresa URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Odkazuje na jiné umístění `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Nejedná se o absolutní adresu URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Neodkazuje na ekvivalentní obsahovou stránku, ale na kořenovou adresu URL domény (domovskou stránku)"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nemá platný atribut `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument má platný odkaz `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON><PERSON><PERSON><PERSON>, který nelze procházet"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Vyhledávače mohou atributy `href` odka<PERSON><PERSON> využívat k procházení webů. Zajistěte, aby atribut `href` prvků ukotvení odkazoval na správný cíl a bylo tak možné objevit více stránek webu. [<PERSON><PERSON>aj<PERSON>, aby odkazy bylo možné proch<PERSON>t](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>lze <PERSON>chá<PERSON>"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Od<PERSON><PERSON> lze procházet"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "<PERSON><PERSON><PERSON> text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Velikost písma"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% textu na stránce"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Písma menší než 12 px jsou příliš malá na to, aby byla čitelná. Návštěvníci na mobilních zařízeních je kvůli čtení musí zvětšit roztažením prstů. Snažte se, aby více než 60 % textu na stránce mělo velikosti alespoň 12 px. [Další informace o čitelných velikostech písem](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "Pod<PERSON><PERSON> textu: {decimalProportion, number, extendedPercent}"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Text nen<PERSON>, protože není k dispozici metaznačka viewport optimalizovaná pro obrazovky mobilních zařízení."}, "core/audits/seo/font-size.js | failureTitle": {"message": "V dokumentu nejsou použity čitelné velikosti písma"}, "core/audits/seo/font-size.js | legibleText": {"message": "Čitelný text"}, "core/audits/seo/font-size.js | title": {"message": "V dokumentu jsou použity čitelné velikosti písma"}, "core/audits/seo/hreflang.js | description": {"message": "Odkazy hreflang sděluj<PERSON> v<PERSON>, k<PERSON><PERSON> verzi stránky mají uvádět ve výsledcích vyhledávání pro určitý jazyk či oblast. [Další informace o odkazech `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nemá platný atribut `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativní hodnota atributu href"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument má platný atribut `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Neočekávaný kód jazyka"}, "core/audits/seo/http-status-code.js | description": {"message": "Stránky s neúspěšnými stavovými kódy HTTP nemusejí být správně indexovány. [Další informace o stavových kódech HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Stránka má neúspěšný stavový kód HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "Stránka má úspěšný stavový kód HTTP"}, "core/audits/seo/is-crawlable.js | description": {"message": "Pokud vyhledávače nemají oprávnění procházet va<PERSON><PERSON>, ne<PERSON><PERSON> je zahrnout do výsledků vyhledávání. [Další informace o direktivách pro prohledávače](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indexování str<PERSON> je blokováno"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indexování str<PERSON> nen<PERSON> b<PERSON>"}, "core/audits/seo/link-text.js | description": {"message": "Popisný text odka<PERSON><PERSON> pomá<PERSON> vyhledávačům porozumět vašemu obsahu. [<PERSON><PERSON> zaj<PERSON>, aby odka<PERSON> byly př<PERSON>tupnějš<PERSON>](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 odkaz}few{<PERSON><PERSON> nalezeny # odkazy}many{<PERSON>lo nalezeno # odkazu}other{Bylo nalezeno # odkazů}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON>d<PERSON>zy nemají popisný text"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> pop<PERSON> text"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Pokud chcete ověřit strukturovaná data, spusťte [nástroj na testování strukturovaných dat](https://search.google.com/structured-data/testing-tool/) a [nástroj Structured Data Linter](http://linter.structured-data.org/). [Další informace o strukturovaných datech](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturovaná data jsou platná"}, "core/audits/seo/meta-description.js | description": {"message": "<PERSON><PERSON><PERSON> „description“ mů<PERSON><PERSON> být zahrnut ve výsledcích vyhledávání jako stručný souhrn obsahu stránky. [Další informace o metaznačce „description“](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "Popisný text je pr<PERSON>ý."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nemá metaznačku „description“"}, "core/audits/seo/meta-description.js | title": {"message": "Dokument má metaznačku „description“"}, "core/audits/seo/plugins.js | description": {"message": "Vyhledávače obsah pluginů nedokážou indexovat a na mnoha zařízeních jsou pluginy zakázány nebo nejsou podporovány. [Jak se vyhnout použití pluginů](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument používá pluginy"}, "core/audits/seo/plugins.js | title": {"message": "V dokumentu nejsou použity pluginy"}, "core/audits/seo/robots-txt.js | description": {"message": "Pokud soubor robots.txt nemá správ<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nemusejí být schopné z<PERSON>it, jak váš web mají procházet nebo indexovat. [Další informace o souboru robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Na žádost o soubor robots.txt byl vrácen tento stav HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Byla nalezena 1 chyba}few{Byly nalezeny # chyby}many{Bylo nalezeno # chyby}other{Bylo nalezeno # chyb}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Nástroji Lighthouse se nepodařilo načíst soubor robots.txt."}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Soubor robots.txt nen<PERSON> plat<PERSON>"}, "core/audits/seo/robots-txt.js | title": {"message": "Soubor robots.txt je platný"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktivní prvky, jako jsou tlačí<PERSON> a od<PERSON><PERSON>, by m<PERSON><PERSON> b<PERSON><PERSON> dostate<PERSON><PERSON> velk<PERSON> (48 × 48 px) nebo by ok<PERSON> nich mělo být dost místa na to, aby se na ně dalo dostatečně snadno klepnout bez přesahování do dalších prvků. [Další informace o dotykových prvcích](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> dos<PERSON><PERSON><PERSON> velkých dotykových prvků: {decimalProportion, number, percent}"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Dotykové prvky jsou p<PERSON><PERSON><PERSON>, protože není k dispozici metaznačka viewport optimalizovaná pro obrazovky mobilních zařízení."}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Dotykové prvky nejsou dostatečně velké"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Překrývající se cíl"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Dotykový prvek"}, "core/audits/seo/tap-targets.js | title": {"message": "Dotykové prvky jsou dostateč<PERSON> velké"}, "core/audits/server-response-time.js | description": {"message": "Odpověď serveru s hlavním dokumentem by <PERSON><PERSON><PERSON><PERSON>, protože na ní závisejí všechny ostatní požadavky. [Další informace o metrice doby do přijetí prvního bajtu](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "Hlavní dokument trval {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Zkraťte dobu počáteční odpovědi serveru"}, "core/audits/server-response-time.js | title": {"message": "Počáteční odpověď serveru byla rychlá"}, "core/audits/splash-screen.js | description": {"message": "Stylová úvodní obrazovka zajišťuje kvalitní uživatelský dojem při spuštění aplikace z plochy. [Další informace o úvodních obrazovkách](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON>í <PERSON>gurována vlastní úvodní obrazovka"}, "core/audits/splash-screen.js | title": {"message": "Je nakonfigurována vlastní úvodní obrazovka"}, "core/audits/themed-omnibox.js | description": {"message": "Motiv adresního řádku prohlížeče lze přizpůsobit motivu vašeho webu. [Další informace o přizpůsobení motivu adresního řádku](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Nenastavuje barvu motivu adresního <PERSON>."}, "core/audits/themed-omnibox.js | title": {"message": "Nastavuje barvu motivu adresního <PERSON>."}, "core/audits/third-party-cookies.js | description": {"message": "Podpora souborů cookie třetích stran bude v budoucí verzi Chromu odstraněna. [Další informace o postupném ukončení podpory souborů cookie třetích stran](https://developer.chrome.com/en/docs/privacy-sandbox/third-party-cookie-phase-out/)."}, "core/audits/third-party-cookies.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 soubor cookie}few{<PERSON>ly nalezeny # soubory cookie}many{By<PERSON> nalezeno # souboru cookie}other{Bylo nalezeno # souborů cookie}}"}, "core/audits/third-party-cookies.js | failureTitle": {"message": "Používá soubory cookie třet<PERSON>ch stran"}, "core/audits/third-party-cookies.js | title": {"message": "Nepoužívá soubory cookie třet<PERSON>ch stran"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (zákaznická podpora)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (sociální sítě)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkt"}, "core/audits/third-party-facades.js | description": {"message": "Některé vložené prvky třetích stran lze načíst líně. Zvažte, zda by je nebylo možn<PERSON> nahradit fasádou, dokud nebudou potřeba. [Jak načítání prvků třetích stran odložit pomocí fasády](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{K dispozici je # alternativní fasáda}few{K dispozici jsou # alternativní fasády}many{K dispozici je # alternativní fasády}other{K dispozici je # alternativních fasád}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Některé zdroje třetích stran lze líně načíst pomocí fasády"}, "core/audits/third-party-facades.js | title": {"message": "<PERSON><PERSON><PERSON>ání zdroj<PERSON> třetích stran pomocí fasád"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Třetí strana"}, "core/audits/third-party-summary.js | description": {"message": "<PERSON><PERSON><PERSON> třetích stran může mít významný dopad na rychlost načítání. Omezte počet redundantních externích poskytovatelů a snažte se kód třetích stran načítat až poté, co se dokončí načtení va<PERSON><PERSON> str<PERSON>. [<PERSON><PERSON><PERSON><PERSON>těte si, jak minimalizovat dopad zdrojů od třetích stran](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON> tř<PERSON>í strany na {timeInMs, number, milliseconds} ms zablokoval hlavní podproces"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Snižte vliv kódu třetích stran"}, "core/audits/third-party-summary.js | title": {"message": "Minimalizujte používání kódu od třetích stran"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Met<PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Nastavte časový rozpočet, který vám pomůže sledovat výkon vašeho webu. Výkonné weby se načítají rychle a také rychle reagují na interakci uživatelů. [Další informace o rozpočtech výkonu](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "Časový rozpočet"}, "core/audits/unsized-images.js | description": {"message": "U obrázkových prvků vždy explicitně určete šířku a výšku, aby se omezilo poskakování při vykreslování a zlepšila se hodnota CLS. [Jak nastavit rozměry obrázků](https://web.dev/articles/optimize-cls#images_without_dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Obrázkové prvky nemají explicitní <PERSON> `width` a `height`"}, "core/audits/unsized-images.js | title": {"message": "Obrázkové prvky mají <PERSON> `width` a `height`"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Zkuste v aplikaci pomocí rozhraní User Timing API implementovat měření reálného výkonu při událostech zásadních pro uživatelský dojem. [Další informace o značkách rozhraní User Timing API](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 časování uživatelů}few{# časování uživatelů}many{# časování uživatelů}other{# časování uživatelů}}"}, "core/audits/user-timings.js | title": {"message": "Hodnoty časování uživatelů"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Byl nalezen prvek `<link rel=preconnect>` pro adresu {securityOrigin}, ale prohlížeč ho nepoužil. Zkontrolujte, zda správně používáte atribut `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Zvažte přidání signálů `preconnect` nebo `dns-prefetch`, aby bylo možné včas se připojit k důležitým zdrojům třetích stran. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>te si, jak se předběžně připojit k požadovaným zdrojům](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "K potřebným zdrojům se připojujte předem"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Byla nalezena více než 2 připojení `<link rel=preconnect>`. Tato připojení byste měli používat střídmě a pouze pro nejdůležitější zdroje."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Byl nalezen prvek `<link rel=preconnect>` pro adresu {securityOrigin}, ale prohlížeč ho nepoužil. Atribut `preconnect` používejte jen pro důležité zdroje, o které stránka určitě bude žádat."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Byl na<PERSON>zen prvek `<link>` k předběžnému načtení zdrojů z adresy {preloadURL}, ale prohlížeč ho nepoužil. Zkontrolujte, zda správně používáte atribut `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Zvažte použití <PERSON> `<link rel=preload>` k prioritnímu načtení zdroj<PERSON>, o které se nyní žádá později během načítání stránky. [Jak předběžně načítat důležité požadavky](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "Klíčové požadavky načítejte předběžně"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Adresa URL souboru sourcemap"}, "core/audits/valid-source-maps.js | description": {"message": "Soubory sourcemap překládají minifikovaný kód na původní zdrojový kód. To vývojářům usnadňuje ladění produkčních verzí. Kromě toho to nástroji Lighthouse umožňuje poskytovat více informací. Zvažte implementaci souborů sourcemap, abyste tyto výhody získali. [Další informace o souborech sourcemap](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Chybí soubory sourcemap pro velké soubory JavaScript první strany"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "U velkého souboru JavaScript chybí soubor sourcemap"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Upozornění: V atributu `.sourcesContent` chybí 1 položka}few{Upozornění: V atributu `.sourcesContent` chybí # položky}many{Upozornění: V atributu `.sourcesContent` chybí # položky}other{Upozornění: V atributu `.sourcesContent` chybí # položek}}"}, "core/audits/valid-source-maps.js | title": {"message": "Stránka má platné soubory sourcemap"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` optimalizuje aplikaci pro velikosti obrazovek mobilů a brání [300milisekundové prodlevě uživatelského vstupu](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Další informace o používání metaznačky viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "Nebyla nalezena značka `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "Neobsahuje značku `<meta name=\"viewport\">` s atributem `width` nebo `initial-scale`"}, "core/audits/viewport.js | title": {"message": "O<PERSON><PERSON><PERSON> z<PERSON>č<PERSON> `<meta name=\"viewport\">` s atributem `width` nebo `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Toto je činnost blokující vlákno, k níž dochází během měření doby od interakce k dalšímu vykreslení. [Další informace o metrice Doba od interakce k dalšímu vykreslení](https://web.dev/articles/inp)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "Na událost {interactionType} byla v<PERSON> do<PERSON> {timeInMs, number, milliseconds} ms"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimalizujte práci během klíčové interakce"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Zpoždění vstupu"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "<PERSON>dleva prezentace"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Doba zpracování"}, "core/audits/work-during-interaction.js | title": {"message": "Minimalizuje práci během klíčové interakce"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení používání specifikace ARIA ve vaší aplikaci, kter<PERSON> mohou zlepšit prostředí pro uživatele asistenčních technologií, jako jsou čtečky obrazovek."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Toto jsou příležitosti k poskytnutí alternativního obsahu pro zvuky a videa. <PERSON><PERSON> zlepšit prostředí pro sluchově nebo zrakově postižené uživatele."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk a video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Tyto položky upozorňují na běžné doporučené postupy v oblasti přístupnosti."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Rady a tipy pro odpovídání na recenze"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Tyto kontroly odhalují příležitosti ke [zlepšení přístupnosti webové aplikace](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatická kontrola dokáže rozpoznat pouze část problémů a nezaručuje, že je ebová aplikace přístupná. Doporučujeme proto provést také [ručn<PERSON> testování](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Tyto položky se týkají oblastí, které v současné době automatické testování nedokáže pokrýt. Další informace najdete v průvodci [provedením kontroly přístupnosti](https://web.dev/articles/how-to-review)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Přístupnost"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení čitelnosti obsahu."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení interpretace vašeho obsahu u uživatelů různých jazyků."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizace a lokalizace"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení sémantiky ovládacích prvků v aplikaci. Mohou zlepšit prostředí pro uživatele asistenčních technologií, jako jsou například čtečky obrazovek."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Názvy a štítky"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení navigace pomocí klávesnice v aplikaci."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigace"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení dojmu při čtení tabulek nebo seznamů pomocí asistenčních technologií, jako je čtečka obrazovky."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabulky a seznamy"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilita s prohlížeči"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Doporuč<PERSON><PERSON>upy"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Obecné"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Důvěra a bezpečí"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Zkušenosti uživatelů"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Výkonové rozpočty nastavují standard pro výkon vašeho webu."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Další informace o výkonu vaší aplikace. Tyto hodnoty nemají [přím<PERSON> vliv](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na skóre výkonu."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Nejkritičtějším aspektem výkonu je rychlost vykreslení pixelů na obrazovce. Klíčové metriky: First Contentful Paint, First Meaningful Paint."}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Vylepšení prvního vykreslení"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Tyto návrhy vám mohou pomoci zrychlit načítání str<PERSON>. Na skóre výkonu nemají [přímý vliv](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Příležitosti"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Vylepšete celkové chování při načítání, aby byla stránka co nejdříve responzivní a připravena k používání. Klíčové metriky: Time to Interactive, Speed Index."}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON>ová vylepšení"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON>ý<PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Tyto kontroly prověřu<PERSON>í různé aspekty progresivní webové aplikace. [Jak vytvořit dobrou progresivní webovou aplikaci](https://web.dev/articles/pwa-checklist)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Tyto kontroly vyžaduje základní [kontrolní seznam aplikace PWA](https://web.dev/articles/pwa-checklist), ale nástroj Lighthouse je automaticky neprovádí. Na vaše skóre vliv nemají, ale je dů<PERSON>ž<PERSON>, abyste je ově<PERSON><PERSON> ručně."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalovatelné"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimalizováno pro PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON>to kont<PERSON>, aby va<PERSON>e stránka dodržovala základní radu ohledně optimalizace pro vyhledávače. <PERSON><PERSON><PERSON> da<PERSON><PERSON><PERSON> faktor<PERSON>, kter<PERSON> ovliv<PERSON><PERSON><PERSON><PERSON> umístění ve výsledc<PERSON><PERSON> v<PERSON>hled<PERSON>vání, Lighthouse nehodnotí, např. výkon metrik [Core Web Vitals](https://web.dev/explore/vitals). [Další informace o základních požadavcích Vyhledávání Google](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Chcete-li zkontrolovat dodržování dalších doporučených postupů pro SEO, spusťte pro svůj web tyto další validátory."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Naformátujte soubor HTML způsobem, k<PERSON><PERSON>ž<PERSON>í <PERSON>ávačům lépe porozumět obsahu vaší aplikace."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Doporučené postupy pro obsah"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Aby se vaše aplikace mohla zobrazovat ve výsledcích vyhledávání, prohledávače k ní musí mít přístup."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Procházení a indexování"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby stránky byly optimaliz<PERSON> pro mobily a aby už<PERSON>lé nemuseli obsah stránky zvětšovat. [<PERSON>ak stránky optimalizovat pro mobily](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Optimalizováno pro mobily"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Testovací zařízení má patrně pomale<PERSON><PERSON><PERSON> procesor, než <PERSON> očekává. To může negativně ovlivnit skóre výkonu. Přečtěte si další informace o [kalibrování vhodného multiplikátoru zpomalení procesoru](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Stránka se možná nenačítá očekávaným způsobem, protože testovací adresa URL ({requested}) byla přesměrována na adresu {final}. Zkuste přímo otestovat druhou URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Stránka se načítala pří<PERSON> pomalu, a test se proto do časového limitu nepodařilo do<PERSON>. Výsledky mohou být neúplné."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Při ma<PERSON>ání mezipa<PERSON>ěti prohlížeče vypršel časový limit. Zkuste stránku zkontrolovat znovu, a pokud bude problém přetrvávat, nahlaste chybu."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Rychlost načítání může být ovlivněna daty uloženými v následujícím umístění: {locations}. Spusťte audit této stránky v anonymním okně, aby tyto zdroje na skóre neměly vliv.}few{Rychlost načítání může být ovlivněna daty uloženými v následujících umístěních: {locations}. Spusťte audit této stránky v anonymním okně, aby tyto zdroje na skóre neměly vliv.}many{Rychlost načítání může být ovlivněna daty uloženými v následujících umístěních: {locations}. Spusťte audit této stránky v anonymním okně, aby tyto zdroje na skóre neměly vliv.}other{Rychlost načítání může být ovlivněna daty uloženými v následujících umístěních: {locations}. Spusťte audit této stránky v anonymním okně, aby tyto zdroje na skóre neměly vliv.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Při mazání dat o zdroji vypršel časový limit. Zkuste stránku zkontrolovat znovu, a pokud bude problém přetrvávat, nahlaste chybu."}, "core/gather/gatherers/link-elements.js | headerParseWarning": {"message": "Chyba při analý<PERSON> z<PERSON>aví `link` ({error}): `{header}`"}, "core/gather/timespan-runner.js | warningNavigationDetected": {"message": "Během auditování byla zjištěna navigace stránkami. Použití režimu časového rozpětí k auditu navigací stránkami se nedoporučuje. K auditu navigací stránkami použijte navigační režim. Získáte lepší atribuci třetích stran a detekci hlavního vlákna."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Mezipaměť pro přechod zpět nebo vpřed mohou využívat pouze stránky načtené pomocí požadavků GET."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Do mezipaměti lze uložit pouze stránky se stavovým kódem 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome během uložení v mezipaměti zjistil pokus o spuštění JavaScriptu."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>žádaly o AppBanner, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | authorizationHeader": {"message": "Mezipaměť pro přechod zpět nebo vpřed je kvůli požadavku keepalive zakázána."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Mezipaměť pro přechod zpět nebo vpřed je deaktivována pomocí přízna<PERSON>ů. Pokud ji na tomto zařízení chcete místně povolit, přejděte na adresu chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Mezipaměť pro přechod zpět nebo vpřed je deaktivována pomocí příkazového řádku."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Mezipaměť pro přechod zpět nebo vpřed je z<PERSON>ána, protože není k dispozici dostatek paměti."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Delegát mezipaměť pro přechod zpět nebo vpřed nepodporuje."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Mezipaměť pro přechod zpět nebo vpřed je pro předběžné vykreslování deaktivována."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Stránku nelze uložit do mezipaměti, protože má instanci rozhraní BroadcastChannel se zaregistrovanými posluchači."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Stránky se záhlavím cache-control:no-store nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Mezipaměť byla úmyslně vymazána."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Stránka byla z mezipaměti odstraněna, aby do ní bylo možné uložit jinou stránku."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Stránky s pluginy v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> p<PERSON>žívají rozhraní FileChooser API, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> používají File System Access API, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> p<PERSON>ž<PERSON>vají nástroj Media Device Dispatcher, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "<PERSON><PERSON>i opuště<PERSON><PERSON> str<PERSON>ky hrál př<PERSON><PERSON><PERSON><PERSON> médií."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>žívají rozhraní MediaSession API a nastavují stav přehrávání, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é p<PERSON>žívají rozhraní MediaSession API a nastavují obslužné nástroje akcí, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Mezipaměť pro přechod zpět nebo vpřed je kvůli čtečce obrazovky deaktivována."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON> využ<PERSON>vat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>žívají rozhraní Serial API, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají rozhraní WebAuthetication API, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é p<PERSON>žívají rozhraní WebBluetooth API, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>žívají rozhraní WebUSB API, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | cookieDisabled": {"message": "Mezipaměť pro přechod zpět nebo vpřed je zakázána, proto<PERSON><PERSON> na stránce, k<PERSON><PERSON> použív<PERSON> `Cache-Control: no-store`, jsou zakázány soubory cookie."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> používají vyhrazený pracovní proces nebo worklet, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokument byl opuštěn d<PERSON>, než bylo dokončeno načítání."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Při opuštění stránky byl zobrazen banner aplikace."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON>ři opuš<PERSON>ě<PERSON><PERSON> s<PERSON> byl pou<PERSON><PERSON><PERSON>právce hesel Chrome"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "Při opuště<PERSON><PERSON> str<PERSON>ky probíhal výtah modelu DOM."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "<PERSON><PERSON><PERSON> opuš<PERSON><PERSON><PERSON><PERSON> s<PERSON> byl používán nástroj DOM Distiller Viewer."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Mezipaměť pro přechod zpět nebo vpřed je z<PERSON>, protože rozšíření používají rozhraní API pro odesílání zpráv."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Rozšíření s dlouhodobým připojením musí toto připojení nejdřív zavřít, až pak mohou použít mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Rozšíření s dlouhodobým připojením se pokoušela odesílat zprávy do rámců v mezipaměti pro přechod zpět nebo vpřed"}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Mezipaměť pro přechod zpět nebo vpřed je kvůli rozšířením deaktivována."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Při opuště<PERSON><PERSON> str<PERSON> bylo zobrazeno modální dialogov<PERSON>, např<PERSON><PERSON> okno s opětovným odesláním formuláře nebo s ověřením přes HTTP."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON><PERSON><PERSON> opuš<PERSON><PERSON><PERSON><PERSON> str<PERSON> byla zobrazena offline stránka."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Při opuštěn<PERSON> str<PERSON> byl zobrazen intervenční panel o nedostatku paměti."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Při opuštění stránky probíhaly žádosti o oprávnění."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON><PERSON><PERSON> opuš<PERSON>ě<PERSON><PERSON> str<PERSON>ky bylo aktivní blokování v<PERSON>kovacích oken."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Při opuště<PERSON><PERSON> str<PERSON>ky byly zobrazeny podrobnosti Bezpečného prohlížení."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Bezpečné pro<PERSON>ížení tuto stránku vyhodnotilo jako nebezpečnou a zablokovalo vyskakovací okno."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "V době, kdy stránka byla v mezipaměti pro přechod zpět nebo vpřed, byl aktivován pracovní proces služby."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Mezipaměť pro přechod zpět nebo vpřed je deaktivována kvůli chybě dokumentu."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Strán<PERSON> se snímky FencedFrames nelze ukládat do mezipaměti bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Stránka byla z mezipaměti odstraněna, aby do ní bylo možné uložit jinou stránku."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> byl udělen přístup ke streamu médií, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají portály, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> používají funkci IdleManager, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> mají otevřené připojení k databázi IndexedDB, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | indexedDBEvent": {"message": "Mezipaměť pro přechod zpět nebo vpřed je zakázána kvůli události IndexedDB."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Byla použita nepodporovaná rozhraní API."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, na které rozšíření vkládají položky `JavaScript`, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON><PERSON>, na které rozšíření vkládají položky `StyleSheet`, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Interní ch<PERSON>."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Mezipaměť pro přechod zpět nebo vpřed je kvůli požadavku keepalive zakázána."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é používají zámek klávesnice, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON><PERSON><PERSON><PERSON> byla opuštěna dříve, než bylo dokončeno načítání."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>lavn<PERSON> zdroj má záhlaví cache-control:no-cache, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>lavn<PERSON> zdroj má záhlaví cache-control:no-store, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigace byla zrušena dříve, než bylo možné stránku obnovit z mezipaměti pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Stránka byla z mezipaměti odstraněna, protože aktivní připojení k síti obdrželo příliš mnoho dat. Chrome omezuje množství dat, které stránka během uložení v mezipaměti může obdržet."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Stránky s probíhajícími požadavky fetch nebo XHR v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Stránka byla z mezipaměti odstraněna, protože u aktivního připojení k síti došlo k přesměrování."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Stránka byla z mezipaměti odstraněna, proto<PERSON><PERSON> bylo p<PERSON> dlouho otevřené připojení k síti. Chrome omezuje dobu, po kterou stránka během uložení v mezipaměti může přijímat data."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Stránky bez platného záhlaví odpovědi nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "K navigaci došlo v jiném než hlavním rámci."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Stránky s probíhajícími transakcemi s indexovanou databází, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Stránky s probíhajícím síťovým požadavkem v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Stránky s probíhajícím síťovým požadavkem fetch v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Stránky s probíhajícím síťovým požadavkem v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Stránky s probíhajícím síťovým požadavkem XHR v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é používají funkci PaymentManager, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Pro stránky s funkcí obrazu v obraze v současné době nelze používat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají portály, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é zobrazují uživatelské rozhraní k tisku, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "<PERSON><PERSON><PERSON><PERSON> byla otevř<PERSON> metod<PERSON> `window.open()` a jiná karta na ni má referenci, nebo stránka otev<PERSON><PERSON> ok<PERSON>."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Vykreslovací proces pro stránku v mezipaměti pro přechod zpět nebo vpřed spadl."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Vykreslovací proces pro stránku v mezipaměti pro přechod zpět nebo vpřed byl ukončen."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON> o oprávnění zaznamenávat zvuk, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON> o oprávnění k použití senzorů, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o oprávnění k synchronizaci nebo načítání dat na pozadí, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON> o oprávnění k používání MIDI, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON> o oprávnění k zobrazování oznámení, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON>ž<PERSON>daly o přístup k úložišti, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> p<PERSON> o oprávnění zaznamenávat video, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Do mezipaměti lze ukládat pouze str<PERSON>, j<PERSON><PERSON><PERSON> adresy URL mají schéma HTTP nebo HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Stránka byla během uložení v mezipaměti pro přechod zpět nebo vpřed nárokována pracovním procesem služby."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Pracovní proces služby se stránce v mezipaměti pro přechod zpět nebo vpřed pokusil odeslat zprávu `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "V době, kdy stránka byla v mezipaměti pro přechod zpět nebo vpřed, byla zrušena registrace pracovního procesu slu<PERSON> (ServiceWorker)."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Stránka byla z mezipaměti pro přechod zpět nebo vpřed odstraněna kvůli aktivaci pracovního procesu služby."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome byl restartován a záznamy v mezipaměti pro přechod zpět nebo vpřed vymazal."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> p<PERSON>žívají sdílený pracovní proces (SharedWorker), v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é používají funkci SpeechRecognizer, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é používají rozhraní SpeechSynthesis, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Prvek iframe na stránce zahájil navigaci, která nebyla dokončena."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zdroje maj<PERSON> cache-control:no-cache, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> zahrnují dílčí zdroje se záhlavím cache-control:no-store, ne<PERSON>hou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Stránka překročila maximální dobu v mezipaměti pro přechod zpět nebo vpřed a její platnost vypršela."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Při ukládání stránky do mezipaměti pro přechod zpět nebo vpřed vypršel časový limit (pravděpodobně kvůli dlouho běžícím obslužným nástrojům pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Stránka má v hlavním rámci obslužný nástroj unload."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Stránka má obslužný nástroj unload v dílčím rámci."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Prohlížeč změnil záhlaví k přepsání user-agenta."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> obdržely přístup k záznamu videí nebo zvuku, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají funkci WebDatabase, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají WebHID, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>é používají funkci WebLocks, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají WebNfc, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají službu WebOTPService, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Stránky s WebRTC nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají rozhraní WebShare, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Stránky s funkcí WebSocket nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "<PERSON><PERSON><PERSON><PERSON>, které využívají rozhraní WebTransport, nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON><PERSON><PERSON>, které používají WebXR, v současné době nemohou využívat mezipaměť pro přechod zpět nebo vpřed."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Zvažte přidání schémat URL https: a http: k zajištění zpětné kompatibility se staršími prohlížeči (prohlížeče podporující klíčové slovo `'strict-dynamic'` je ignorují)."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Direktiva `disown-opener` již od verze CSP3 není podpor<PERSON>na. Použijte místo ní záhlaví Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Direktiva `referrer` již od verze CSP2 není podporována. Použijte místo ní záhlaví Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Direktiva `reflected-xss` již od verze CSP2 není podporována. Použijte místo ní záhlaví X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Pokud není zahrnuta direktiva `base-uri`, lze vložením z<PERSON>ek `<base>` nastavit základní URL pro všechny relativní adresy URL (např. skripty) na doménu kontrolovanou útočníkem. Zvažte nastavení `base-uri` na `'none'` nebo `'self'`."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Chybějící direktiva `object-src` umožňuje vložení pluginů, které mohou spouštět nebezpečné skripty. Zvažte nastavení `object-src` na `'none'`."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Chybí direktiva `script-src`. To mů<PERSON>e umožnit spouštěn<PERSON> skriptů, kter<PERSON> nejsou be<PERSON>."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Nezapomněli jste středník? {keyword} vypadá jako direktiva, ne klíčové slovo."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Hodnoty nonce mají používat znakovou sadu base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "<PERSON><PERSON><PERSON><PERSON> nonce by m<PERSON><PERSON> m<PERSON><PERSON> alespoň 8 znaků."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "V této direktivě nepoužívejte prostá schémata adresy URL ({keyword}). Prostá schémata adresy URL povolují skripty pocházející z nebezpečných domén."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "V této direktivě nepoužívejte prosté zástupné zna<PERSON> ({keyword}). Prosté zástupné znaky povolují skripty pocházející z nebezpečných domén."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Cíl pro hlášení je nakonfigurován pouze pomocí direktivy report-to. Tato direktiva je podporovaná pouze v prohlížečích založených na projektu Chromium, proto doporučujeme použít direktivu `report-uri`."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Žádné zásady CSP neurčují cíl pro přehledy. To znesnadňuje dlouhodobou údržbu zásad CSP a monitorování případných prolomení."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Seznamy povolených hostitelských serverů lze často obejít. Použijte místo nich hodnoty CSP nonce nebo hash, případně v kombinaci s klíčovým slovem `'strict-dynamic'`."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Neznámá direktiva CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} je <PERSON><PERSON><PERSON><PERSON> neplatné klíč<PERSON> slovo."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "`'unsafe-inline'` umožň<PERSON>je spouštění potenciálně nebezpečných skriptů a obslužných rutin událostí vložených v kódu stránky. Zvažte použití hodnot CSP nonce nebo hash k povolení jednotlivých skriptů."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Zvažte přidání klíčového slova `'unsafe-inline'` (prohl<PERSON>žeče s podporou hodnot nonce/hash ho ignorují) k zajištění zpětné kompatibility se staršími prohlížeči."}, "core/lib/deprecation-description.js | feature": {"message": "Další podrobnosti najdete na stránce s informacemi o stavu funkcí."}, "core/lib/deprecation-description.js | milestone": {"message": "<PERSON>to změna nabude účinnosti od milníku {milestone}."}, "core/lib/deprecation-description.js | title": {"message": "<PERSON><PERSON> p<PERSON> zastaralá <PERSON>"}, "core/lib/deprecations-strings.js | AuthorizationCoveredByWildcard": {"message": "Zástupný symbol (*) se při zpracování záhlaví `Access-Control-Allow-Headers` pro CORS nevztahuje na záhlaví Authorization."}, "core/lib/deprecations-strings.js | CSSSelectorInternalMediaControlsOverlayCastButton": {"message": "<PERSON><PERSON><PERSON> `-internal-media-controls-overlay-cast-button` byste k vypnutí výchozí integrace funkce Cast měli p<PERSON>t atribut `disableRemotePlayback`."}, "core/lib/deprecations-strings.js | CanRequestURLHTTPContainingNewline": {"message": "Požadavky na zdroje, jej<PERSON>ž adresy URL obsahovaly jak odstraněné prázdné znaky `(n|r|t)`, tak znaky men<PERSON> ne<PERSON> (`<`), jsou blo<PERSON>. Aby se tyto zdroje načetly, je na místech, jako jsou hodnoty atributů, potřeba odstranit zalomení řádků a zakódovat znaky menší než."}, "core/lib/deprecations-strings.js | ChromeLoadTimesConnectionInfo": {"message": "Metoda `chrome.loadTimes()` je zastaral<PERSON>, použijte místo ní standardizované API: Navigation Timing 2."}, "core/lib/deprecations-strings.js | ChromeLoadTimesFirstPaintAfterLoadTime": {"message": "API `chrome.loadTimes()` je zastaralé. Použijte místo něj standardizované API: Paint Timing."}, "core/lib/deprecations-strings.js | ChromeLoadTimesWasAlternateProtocolAvailable": {"message": "Metoda `chrome.loadTimes()` je zastaralá. Použijte místo ní standardizované API: `nextHopProtocol` v Navigation Timing 2."}, "core/lib/deprecations-strings.js | CookieWithTruncatingChar": {"message": "Soubory cookie obsahují<PERSON>í znak `(0|r|n)` budou namís<PERSON> zkrácení odmítnuty."}, "core/lib/deprecations-strings.js | CrossOriginAccessBasedOnDocumentDomain": {"message": "Uvolnění zásad stejného původu nastavením atributu `document.domain` je zastaralé a bude ve výchozím nastavení zakázáno. Toto upozornění na ukončení podpory se týká přístupu mezi různými zdroji, který byl povolen nastavením `document.domain`."}, "core/lib/deprecations-strings.js | CrossOriginWindowAlert": {"message": "Podpora volání funkce window.alert z prvků iframe jiných domén byla ukončena a v budoucnu bude odstraněno."}, "core/lib/deprecations-strings.js | CrossOriginWindowConfirm": {"message": "Podpora volání funkce window.confirm z prvků iframe jiných domén je zastaralá a v budoucnu bude odstraněna."}, "core/lib/deprecations-strings.js | DOMMutationEvents": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> změny DOM, v<PERSON><PERSON><PERSON><PERSON> `DOMSubtreeModified`, `DOMNodeInserted`, `DOMNodeRemoved`, `DOMNodeRemovedFromDocument`, `DOMNodeInsertedIntoDocument` a `DOMCharacterDataModified` jsou <PERSON> (https://w3c.github.io/uievents/ #legacy-event-types) a budou odstraněny. Použijte místo nich rozhraní `MutationObserver`."}, "core/lib/deprecations-strings.js | DataUrlInSvgUse": {"message": "Podpora adres URL se schématem data: v prvku <use> v SVG je zastaralá a v budoucnu bude odstraněna."}, "core/lib/deprecations-strings.js | DocumentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Uvolnění zásad stejného původu nastavením atributu `document.domain` je zastaralé a bude ve výchozím nastavení zakázáno. Pokud tuto funkci chcete nadále p<PERSON>, odesláním záhlaví `Origin-Agent-Cluster: ?0` spolu s odpovědí HTTP pro dokument a rámce se odhlaste z clusterů zástupců rozlišovaných podle zdrojů. Další podrobnosti najdete na stránce https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | ExpectCTHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `Expect-CT` je zastara<PERSON> a bude odstraněno. Chrome u všech veřejně důvěryhodných certifikátů vydaných po 30. dubnu 2018 vyžaduje ochranu Certificate Transparency."}, "core/lib/deprecations-strings.js | GeolocationInsecureOrigin": {"message": "`getCurrentPosition()` a `watchPosition()` již nefungují u nezabezpečených zdrojů. Pokud tuto funkci chcete pou<PERSON>í<PERSON>, měli byste zvážit převedení aplikace na zabezpečený zdroj, jako je HTTPS. Další podrobnosti najdete na stránce https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GeolocationInsecureOriginDeprecatedNotRemoved": {"message": "Metody `getCurrentPosition()` a `watchPosition()` ji<PERSON> v nezabezpečených zdrojích nejsou podporovány. Pokud tuto funkci chcete p<PERSON>í<PERSON>, měli byste zvážit převedení aplikace na zabezpečený zdroj, jako je HTTPS. Další podrobnosti najdete na stránce https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | GetUserMediaInsecureOrigin": {"message": "`getUserMedia()` již nefunguje u nezabezpečených zdrojů. Pokud tuto funkci chcete p<PERSON>, měli byste zvážit převedení aplikace na zabezpečený zdroj, jako je HTTPS. Další podrobnosti najdete na stránce https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | HostCandidateAttributeGetter": {"message": "Rozhraní `RTCPeerConnectionIceErrorEvent.hostCandidate` je zastaralé. Použijte místo něj rozhraní `RTCPeerConnectionIceErrorEvent.address` nebo `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | IdentityInCanMakePaymentEvent": {"message": "Zdroj obchodníka a libovolná data z události `canmakepayment` procesu pracovní proces služby jsou zastaralé a budou odstraněny: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | InsecurePrivateNetworkSubresourceRequest": {"message": "Web si vyžádal dílčí zdroj ze sítě, ke které měl přístup pouze díky privilegované pozici svých uživatelů v síti. Tyto požadavky umožňují získat z internetu přístup k neveřejným zařízením a serverům, č<PERSON><PERSON>ž se zvyšuje riziko útoku CSRF (cross-site request forgery) a/nebo úniku informací. Aby Chrome tato rizika zmírnil, přestává podporovat požadavky na neveřejné dílčí zdroje iniciované z nezabezpečených kontextů a začne je blokovat."}, "core/lib/deprecations-strings.js | InterestGroupDailyUpdateUrl": {"message": "Pole `dailyUpdateUrl` struktury `InterestGroups` předávané do `joinAdInterestGroup()` bylo př<PERSON>no na `updateUrl`, aby jeho název přesněji odpovídal chování."}, "core/lib/deprecations-strings.js | LocalCSSFileExtensionRejected": {"message": "Styly CSS lze z adres URL `file:` načíst pouze v případě, že končí příponou souboru `.css`."}, "core/lib/deprecations-strings.js | MediaSourceAbortRemove": {"message": "Po<PERSON><PERSON><PERSON><PERSON> `SourceBuffer.abort()` ke zrušení asynchronního odstranění rozsahu pomocí metody `remove()` je vzhledem ke změně specifikace zastaralé. Podpora bude v budoucnu odstraněna. Doporučujeme místo toho naslouchat události `updateend`. Metoda `abort()` je určena pouze ke zrušení asynchronního připojení média nebo resetování stavu analyzátoru."}, "core/lib/deprecations-strings.js | MediaSourceDurationTruncatingBuffered": {"message": "Nastavení hodnoty `MediaSource.duration` ni<PERSON><PERSON><PERSON>, než je nejvy<PERSON><PERSON><PERSON> prezentační časové razítko kterýchkoliv kódovaných snímků ve vyrovnávací paměti, je z důvodu změny specifikace zastaralé. Podpora implicitního odstranění zkrácených médií ve vyrovnávací pamětí bude v budoucnu odstraněna. Místo toho byste u všech objektů `sourceBuffers`, u kterých je `newDuration < oldDuration`, měli <PERSON>n<PERSON> provést `remove(newDuration, oldDuration)`."}, "core/lib/deprecations-strings.js | NoSysexWebMIDIWithoutPermission": {"message": "Rozhraní Web MIDI požádá o oprávnění k použití i v případě, že v `MIDIOptions` není zadán sysex."}, "core/lib/deprecations-strings.js | NonStandardDeclarativeShadowDOM": {"message": "<PERSON><PERSON><PERSON>, nestandardizovaný atribut `shadowroot` je zastara<PERSON> a ve vydání M119 už *nebude fungovat*. Použijte místo něj nový, standardizovaný atribut `shadowrootmode`."}, "core/lib/deprecations-strings.js | NotificationInsecureOrigin": {"message": "Notification API již nelze používat z nezabezpečených zdrojů. Zvažte převedení aplikace na zabezpečený zdroj, jako je HTTPS. Další podrobnosti najdete na stránce https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | NotificationPermissionRequestedIframe": {"message": "Oprávnění pro rozhraní Notification API již nelze požadovat z prvku iframe z jiného zdroje. Měli byste zvážit vyžádání oprávnění z rámce nejvyšší úrovně nebo otevření nového okna."}, "core/lib/deprecations-strings.js | ObsoleteCreateImageBitmapImageOrientationNone": {"message": "Možnost `imageOrientation: 'none'` v metodě createImageBitmap je zastaralá. Použijte místo ní metodu createImageBitmap s možností \\{imageOrientation: 'from-image'\\}."}, "core/lib/deprecations-strings.js | ObsoleteWebRtcCipherSuite": {"message": "<PERSON><PERSON><PERSON> partner v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (D)TLS. Požádejte ho o nápravu."}, "core/lib/deprecations-strings.js | OverflowVisibleOnReplacedElement": {"message": "Pokud u značek img, video či canvas zadáte vlastnost `overflow: visible`, může se vizuální obsah zobrazit mimo hranice prvku. Viz https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | PaymentInstruments": {"message": "API `paymentManager.instruments` je zastaralé. Obslužné nástroje pro platby místo toho instalujte bezprostředně před použitím."}, "core/lib/deprecations-strings.js | PaymentRequestCSPViolation": {"message": "Vaše volání rozhraní `PaymentRequest` obchází direktivu `connect-src` zásad zabezpečení obsahu (CSP). Toto obcházení již není podporováno. Přidejte do direktivy CSP `connect-src` identifikátor platební metody z rozhraní API `PaymentRequest` (v poli `supportedMethods`)."}, "core/lib/deprecations-strings.js | PersistentQuotaType": {"message": "Rozhraní `StorageType.persistent` je zastaralé. Použijte místo něj standardizované rozhraní `navigator.storage`."}, "core/lib/deprecations-strings.js | PictureSourceSrc": {"message": "Prvek `<source src>` s nadřazeným prvkem `<picture>` je <PERSON>, a proto je ignorován. Použijte místo něj prvek `<source srcset>`."}, "core/lib/deprecations-strings.js | PrefixedCancelAnimationFrame": {"message": "Metoda webkitCancelAnimationFrame je specifická pro dodavatele. Použijte místo ní standardní metodu cancelAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedRequestAnimationFrame": {"message": "Metoda webkitRequestAnimationFrame je specifická pro dodavatele. Místo ní použijte standardní metodu requestAnimationFrame."}, "core/lib/deprecations-strings.js | PrefixedVideoDisplayingFullscreen": {"message": "Vlastnost HTMLVideoElement.webkitDisplayingFullscreen je zastaralá. Použijte místo ní vlastnost Document.fullscreenElement."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullScreen": {"message": "Metoda HTMLVideoElement.webkitEnterFullScreen() je zastaralá. Použijte místo ní metodu Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoEnterFullscreen": {"message": "Metoda HTMLVideoElement.webkitEnterFullscreen() je zastaralá. Použijte místo ní metodu Element.requestFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullScreen": {"message": "Metoda HTMLVideoElement.webkitExitFullScreen() je zastaralá. Použijte místo ní metodu Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoExitFullscreen": {"message": "Metoda HTMLVideoElement.webkitExitFullscreen() je zastaralá. Použijte místo ní metodu Document.exitFullscreen()."}, "core/lib/deprecations-strings.js | PrefixedVideoSupportsFullscreen": {"message": "Vlastnost HTMLVideoElement.webkitSupportsFullscreen je zastaralá. Použijte místo ní vlastnost Document.fullscreenEnabled."}, "core/lib/deprecations-strings.js | PrivacySandboxExtensionsAPI": {"message": "Ukončujeme podporu rozhraní API `chrome.privacy.websites.privacySandboxEnabled`. Z důvodu zpětné kompatibility však zůstane aktivní až do vydání M113. <PERSON><PERSON><PERSON> toho použijte vlastnosti `chrome.privacy.websites.topicsEnabled`, `chrome.privacy.websites.fledgeEnabled` a `chrome.privacy.websites.adMeasurementEnabled`. Viz https://developer.chrome.com/docs/extensions/reference/privacy/#property-websites-privacySandboxEnabled."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpFalse": {"message": "<PERSON><PERSON><PERSON><PERSON> `DtlsSrtpKeyAgreement` je odstraněno. Pro toto omezení jste zadali hodnotu `false`, kter<PERSON> je interpretována jako pokus o použití odstraněné metody `SDES key negotiation`. Tato funkce je odstraněna. Použijte místo toho službu, která podporuje metodu `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | RTCConstraintEnableDtlsSrtpTrue": {"message": "Omezení `DtlsSrtpKeyAgreement` je odstraněno. Pro toto omezení jste zadali hodnotu `true`, co<PERSON> nem<PERSON><PERSON>, ale pro pořádek můžete toto omezení odstranit."}, "core/lib/deprecations-strings.js | RTCPeerConnectionGetStatsLegacyNonCompliant": {"message": "Metoda getStats() založená na zpětném volání je zastaralá a bude odstraněna. Místo toho použijte metodu getStats() odpovídající specifikacím."}, "core/lib/deprecations-strings.js | RangeExpand": {"message": "Metoda Range.expand() je zastaralá. Použijte místo ní metodu Selection.modify()."}, "core/lib/deprecations-strings.js | RequestedSubresourceWithEmbeddedCredentials": {"message": "Požadavky na dílčí zdroje, jej<PERSON>ž adresy URL obsahují vložené přihlašovací údaje (např. `**********************/`), j<PERSON>u blokovány."}, "core/lib/deprecations-strings.js | RtcpMuxPolicyNegotiate": {"message": "Možnost `rtcpMuxPolicy` je zastaralá a bude odstraněna."}, "core/lib/deprecations-strings.js | SharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` bude vyžadovat izolaci od jiných zdrojů. Další podrobnosti najdete na stránce https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | TextToSpeech_DisallowedByAutoplay": {"message": "<PERSON><PERSON><PERSON> metody `speechSynthesis.speak()` bez aktivace ze strany uživatele je zastaralé a bude odstraněno."}, "core/lib/deprecations-strings.js | V8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Pokud v rozšířeních chcete používat technologii `SharedArrayBuffer`, je u nich potřeba aktivovat izolaci od jiných zdrojů. Viz https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | WebSQL": {"message": "Technologie Web SQL je zastaralá. Použijte SQLite WebAssembly nebo rozhraní Indexed Database"}, "core/lib/deprecations-strings.js | WindowPlacementPermissionDescriptorUsed": {"message": "Deskriptor oprávnění `window-placement` je z<PERSON>. Použijte místo něj deskriptor `window-management`. Další informace najdete na stránce https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | WindowPlacementPermissionPolicyParsed": {"message": "Zásada oprávnění `window-placement` je zastaral<PERSON>. Použijte místo ní zásadu `window-management`. Další informace najdete na stránce https://bit.ly/window-placement-rename."}, "core/lib/deprecations-strings.js | XHRJSONEncodingDetection": {"message": "Kódování UTF-16 není v objektech JSON odpovědí rozhraní `XMLHttpRequest` podporováno"}, "core/lib/deprecations-strings.js | XMLHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synchronní požadavky `XMLHttpRequest` v hlavním vláknu jsou zastaralé, protože mají nepříznivý vliv na dojem koncového uživatele. Další informace najdete na stránce https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | XRSupportsSession": {"message": "Metoda `supportsSession()` je zastaralá. Použijte místo ní metodu `isSessionSupported()` a zkontrolujte vrácenou logickou hodnotu."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Doba blokování hlavního podprocesu"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Hodnota TTL (Time to Live) mezipaměti"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Trvání"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Prvek"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Požadavky"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Velikost zdroje"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Typ zdroje"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Velikost"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Čas zahájení"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Strávený čas"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Velikost přenosu"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Možná úspora"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Možná úspora"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenciální úspora {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Byl nalezen 1 prvek}few{<PERSON><PERSON> nale<PERSON>y # prvky}many{<PERSON><PERSON> nalezeno # prvku}other{Bylo nalezeno # prvků}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Lze uspořit {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "First Meaningful Paint"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Písmo"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Obrázek"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Doba od interakce k dalšímu vykreslení"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Vysoká"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Nízká"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Střední"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Maximální potenciální prodleva prvního vstupu"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Média"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Šablona stylů"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Třetí strana"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Při pořizování záznamu trasování během načítání strán<PERSON> se něco pokazilo. Spusťte nástroj Lighthouse znovu. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Při čekání na počáteční připojení pomocí ladicího protokolu vypršel časový limit."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome při načítání stránky nezískal žádné snímky obrazovky. Zkontrolujte, zda je na stránce vidět nějak<PERSON> obsah, a poté nástroj Lighthouse zkuste spustit znovu. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Servery DNS zadanou doménu nedokázaly přeložit."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON><PERSON>í povinného zdroje {artifactName} do<PERSON><PERSON> k <PERSON>b<PERSON>: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Došlo k interní chybě Chromu. Restartujte Chrome a zkuste nástroj Lighthouse spustit znovu."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Požadovaný shromažďovací nástroj zdroje {artifactName} se nespustil."}, "core/lib/lh-error.js | noFcp": {"message": "Stránka nevykreslila žádný obsah. <PERSON><PERSON><PERSON><PERSON><PERSON>, aby se okno prohlížeče při načítání nacházelo v popředí, a zkuste to znovu. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "<PERSON> stránce nebyl zobrazen obsah, j<PERSON><PERSON><PERSON> by by<PERSON> <PERSON><PERSON><PERSON><PERSON> jako vykreslení největ<PERSON><PERSON><PERSON> o<PERSON> (LCP). Zkon<PERSON>lujte, zda stránka obsahuje platný prvek LCP, a zkuste to znovu. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Poskytnutá stránka není ve formátu HTML (byla dodána s typem MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Tato verze Chromu je př<PERSON> stará a funkci {featureName} nepodporuje. Pokud chcete zobrazit úplné v<PERSON>ky, použijte novější verzi."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Nástroj Lighthouse adresu URL nemohl spolehlivě načíst, protože stránka přestala reagovat."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON> platný bezpečnostní certifikát. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome zabránil načtení stránky a zobrazil vsunutou obrazovku. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky. (Podrobnosti: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky. (Stavový kód: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Načtení stránky trvalo příliš dlouho. Podle návrhů v přehledu zkraťte dobu načítání stránky a poté nástroj Lighthouse zkuste spustit znovu. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Při čekání na odpověď protokolu DevTools byla překročena přidělená doba. (Metoda: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Při načítání obsahu zdroje byla překročena přidělená doba"}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON>, se zd<PERSON> b<PERSON>t <PERSON>."}, "core/lib/navigation-error.js | warningStatusCode": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky. (Stavový kód: {errorCode})"}, "core/lib/navigation-error.js | warningXhtml": {"message": "Typ MIME stránky je XHTML: Lighthouse tento typ dokumentu explicitně nepodporuje"}, "core/user-flow.js | defaultFlowName": {"message": "<PERSON>sta uživatele ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "<PERSON><PERSON><PERSON><PERSON> navigace ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Přehled v konkrétním okamžiku ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "<PERSON><PERSON><PERSON><PERSON> rozpětí ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Všechny přehledy"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Přístupnost"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Doporuč<PERSON><PERSON>upy"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON>ý<PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresivní webová aplikace"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Vysvětlení přehledu procesu Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Vysvětlení procesů"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Pomocí přehledů navigace můžete…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Pomocí přehledů v konkrétním okamžiku můžete…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Pomocí př<PERSON><PERSON>ů časového rozpětí můžete…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Získat skóre výkonu Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON><PERSON><PERSON> metriky <PERSON>, jako j<PERSON>u vykreslení největšího obsahu a index rychlosti."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Vyhodnotit funkce progresivních webových aplikací."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Odhalit problémy s přístupností v jednostránkových aplikacích nebo složitých formulářích."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Vyhodnotit doporučené postupy týkající se nabídek a prvků uživatelského rozhraní skryté za interakcí."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Měřit změny rozvržení a dobu běhu JavaScriptu v sériích interakcí."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Objevit příležitosti ke zlepšení výkonu, kter<PERSON> vám umožní vylepšit výkon dlouho používaných stránek a jednostránkových aplikací."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Nejvyšší dopad"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativní audit}few{{numInformative} informativní audity}many{{numInformative} informativního auditu}other{{numInformative} informativních auditů}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Přehledy navigace analyzují jedno načtení s<PERSON>, s<PERSON><PERSON><PERSON> jako původní přehledy Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON><PERSON> navigace"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} přeh<PERSON> navigace}few{{numNavigation} přehledy navigace}many{{numNavigation} přehledu navigace}other{{numNavigation} přeh<PERSON>ů navigace}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} proveditelný audit}few{{numPassableAudits} proveditelné audity}many{{numPassableAudits} proveditelného auditu}other{{numPassableAudits} proveditelných auditů}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} splněný audit}few{{numPassed} splněné audity}many{{numPassed} splněného auditu}other{{numPassed} splněn<PERSON>ch auditů}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Pr<PERSON><PERSON>ě<PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Chyba"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "S<PERSON>bé"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Uložit"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Zachycený stav stránky"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Přehledy v konkrétním okamžiku analyzují stránku v konkrétním stavu, obvykle po interakcích uživatelů."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Přehled v konkrétním okamžiku"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} p<PERSON><PERSON><PERSON> v konkrétním okamžiku}few{{numSnapshot} přehledy v konkrétním okamžiku}many{{numSnapshot} přehledu v konkrétním okamžiku}other{{numSnapshot} p<PERSON><PERSON><PERSON><PERSON> v konkrétním okamžiku}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Souhrn"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Interakce uživatelů"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Přehledy časového rozpětí analyzují libovolné období, kter<PERSON> obvykle zahrnuje interakce uživatelů."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "<PERSON><PERSON><PERSON><PERSON>ho rozpětí"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} přeh<PERSON> časového rozpětí}few{{numTimespan} přehledy časového rozpětí}many{{numTimespan} přehledu časového rozpětí}other{{numTimespan} přeh<PERSON>ů časového rozpětí}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Přehled toku uživatelů služby Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Pro animovaný obsah použijte prvek [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), abyste minimalizovali využití procesoru, k<PERSON>ž příslušný obsah není na obrazovce."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Zvažte zobrazování všech komponent [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ve formátech WebP a zadání vhodných záložních možností pro ostatní prohlížeče. [Další informace](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Pro obrázky používejte zna<PERSON> [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites), aby se automaticky načítaly líně. [Další informace](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[Vykreslujte rozvržení AMP na straně serveru](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) pomo<PERSON><PERSON> n<PERSON>, jako je [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Prostudujte si [dokumentaci AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) a zkontrolujte, zda jsou všechny vaše styly podporovány."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Prvek [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) podporuje atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/), pomo<PERSON><PERSON> n<PERSON>, kter<PERSON> obr<PERSON>zkov<PERSON> podklady se mají p<PERSON> pro jednotlivé velikosti obrazovek. [Další informace](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Pokud se vykreslují velmi velk<PERSON>, z<PERSON><PERSON>te použití virtuálního posouvání pomocí sady Component Dev Kit (CDK). [Další informace](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Minimalizujte velikost balíčků JavaScriptu pomocí [rozdělení kódu na úrovni trasy](https://web.dev/route-level-code-splitting-in-angular/). Zvažte také předběžné načítání podkladů do mezipaměti pomocí [service workeru platformy Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Pokud používáte Angular CLI, dejte pozor, abyste sestavení generovali v produkčním režimu. [Další informace](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Pokud používáte Angular CLI, do produkčního sestavení zahrňte mapy zdroje, aby balíč<PERSON> bylo možné prozkoumat. [Další informace](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "Zrychlete navigaci tím, že budete trasy načítat předem. [Další informace](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Zvažte použití nástroje `BreakpointObserver` v sadě Component Dev Kit (CDK) ke správě dě<PERSON><PERSON><PERSON> bodů obrázků. [Další informace](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Zvažte nahrání souboru GIF do služby, pomocí které ho bude možné vložit jako video HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "<PERSON><PERSON><PERSON> def<PERSON> vlastních písem pro motiv je třeba určit `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Zvažte možnost nakonfigurovat na webu [form<PERSON><PERSON> obr<PERSON><PERSON><PERSON>ů WebP se stylem obrázků Convert](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Nainstalujte [modul Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), který umožňuje líné načítání obrázků. Tyto moduly umožňují odložit načítání obrázků mimo obrazovku za účelem zvýšení výkonu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Zvažte použití modulu k vložení kritických stylů CSS a JavaScriptu přímo do kódu a pro nekritické styly CSS a JavaScript použijte atribut defer."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Na reakční dobu serveru mají vliv motivy, moduly a specifikace serverů. Zvažte vyhledání optimalizovaného motivu, pečlivý výběr optimalizačního modulu či upgradování serveru. Za účelem zrychlení odezvy databáze doporučujeme na hostujících serverech používat ukládání operačního kódu do mezipaměti pomocí PHP. Slouží k tomu nástroje jako Redis nebo Memcached, případně lze použít optimalizovanou logiku aplikace k rychlejší přípravě stránek."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Zvažte použití [responzivních stylů obr<PERSON>](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) ke snížení velikosti obrázků načítaných stránkou. Pokud používáte Zobrazení k prezentování více obsahových položek na stránce, zvažte implementaci stránkování za účelem omezení počtu obsahových položek na jedné stránce."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Na stránce Administrace » Konfigurace » Vývoj aktivujte funkci Agregovat soubory CSS.  Zajistěte, aby váš web Drupal používal alespoň Drupal 10.1, k<PERSON><PERSON> lépe podporuje agregaci podkladů."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Na stránce Administrace » Konfigurace » Vývoj aktivujte funkci Agregovat soubory JavaScriptu.  Zajistěte, aby v<PERSON><PERSON> web Drupal používal alespoň Drupal 10.1, k<PERSON><PERSON> lépe podporuje agregaci podkladů."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Zvažte odstranění nepoužívaných pravidel CSS a k odpovídající stránce nebo komponentě stránky připojte pouze nezbytné knihovny Drupal. Podrobnosti naleznete v [dokumentaci Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Připojené knihovny, které přidávají nadbytečný kód CSS, můžete vyhledat pomocí funkce [„využit<PERSON> kódu“](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či modul můžete identifikovat podle adresy URL šablony stylů, když je na vašem webu Drupal deaktivována agregace CSS. Hledejte motivy či moduly, kter<PERSON> v seznamu mají mnoho šablon stylů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Motiv či modul by měl šablonu stylů do fronty zařadit jen v případě, že se na stránce opravdu používá."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Zvažte odstranění nepoužívaných javascriptových zdrojů a k odpovídající stránce nebo komponentě stránky připojte pouze nezbytné knihovny Drupal. Podrobnosti naleznete v [dokumentaci Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Přip<PERSON>jen<PERSON> knihovny, které přidávají nadbytečný javascriptový kód, můžete vyhledat pomocí funkce [„využití kódu“](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či modul můžete identifikovat podle adresy URL skriptu, k<PERSON>ž je na vašem webu Drupal deaktivována agregace JavaScriptu. Hledejte motivy či moduly, které v seznamu mají mnoho skriptů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Motiv či modul by měl skript do fronty zařadit jen v případě, že se na stránce opravdu používá."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Na stránce Administrace » Konfigurace » Vývoj nastavte možnost Maximální věk pro mezipaměť a proxy server. Přečtěte si o [mezipaměti systému Drupal a optimalizaci pro vyšší výkon](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Zvažte použití [modulu](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), který automaticky optimalizuje obrázky nahrávané přes web a sníží jejich velikost, aniž by sn<PERSON><PERSON><PERSON> k<PERSON>itu. Zajistěte také použití nativních [responzivních stylů obrázků](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) ze systému Drupal (k dispozici od verze 8) pro všechny obrázky vykreslované na webu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Pokyny k předběžnému připojení nebo načtení <PERSON> (preconnect nebo dns-prefetch) lze přidat instalací a konfigurací [modulu](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), který zprostředkuje hintingové instrukce pro zdroje user-agent."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Zajistěte použití nativních [responzivních stylů obr<PERSON>zk<PERSON>](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) ze systé<PERSON> Drupal (k dispozici od verze 8). Použijte responzivní styly obrázků při vykreslování polí obrázků prostřednictvím režimů zobrazení, zobrazení nebo u obrázků nahraných prostřednictvím editoru WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [E<PERSON> Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Optimize Fonts`, aby se <PERSON>ky využ<PERSON> funkce CSS `font-display` a text tak byl během načítání webových písem viditelný pro uživatele."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Next-Gen Formats`, aby převedl obrázky do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `<PERSON><PERSON>`, abyste odložili načítání snímků mimo obrazovku, dokud nebudou potřeba."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Critical CSS` a `Script Delay`, abyste odložili načítání nekritického JavaScriptu a CSS."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Využijte službu [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) k uložení obsahu do mezipaměti v naší celosvětové síti, což zkrátí dobu do přijetí prvního bajtu."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Minify CSS`, aby automaticky minifikoval CSS a snížila se tak velikost dat přenášených po síti."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Minify Javascript`, aby automaticky minifikoval JavaScript a snížilo se tak množství dat přenášené po síti."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Použ<PERSON><PERSON><PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Remove Unused CSS`, která tento problém pomůže vyřešit. Zjistí, které třídy CSS se na jednotlivých stránkách vašeho webu skutečně používají, a všechny ostatní odstraní, aby byl soubor malý."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Použijte nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Efficient Static Cache Policy`, aby se v záhlaví pro ukládaní do mezipaměti nastavily doporučené hodnoty pro statické podklady."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Next-Gen Formats`, aby převedl obrázky do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Použ<PERSON>j<PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Pre-Connect Origins`, aby automaticky přidal hinty `preconnect` ohledně zdrojů a bylo možné včas zajistit připojení k důležitým zdrojům třetích stran."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Použ<PERSON>jte nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnosti `Preload Fonts` a `Preload Background Images`, aby se přidaly odkazy `preload` k přednostnímu načtení podkladů, o které se aktuálně žádá později během načítání stránky."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nástroj [Ezoic Leap](https://pubdash.ezoic.com/speed) a zapněte možnost `Resize Images`, aby velikost obrázků přizpůsobil zařízení a snížili jste tak množství dat přenášené po síti."}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON>j<PERSON> místo komponenty `<img>` komponentu `gatsby-plugin-image`, aby se automaticky optimalizoval formát obrázků. [Další informace](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON>j<PERSON> místo komponenty `gatsby-plugin-image` komponentu `<img>`, aby se obrázky automaticky načítaly líně. [Další informace](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | prioritize-lcp-image": {"message": "Použ<PERSON>jte komponentu `gatsby-plugin-image` a nastavte vlastnost `loading` na `eager`. [Další informace](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-plugin-image#shared-props)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | render-blocking-resources": {"message": "Odložte načítání nekritických skriptů třetích stran pomocí `Gatsby Script API`. [Další informace](https://www.gatsbyjs.com/docs/reference/built-in-components/gatsby-script/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-css-rules": {"message": "Pomocí pluginu `Gatsby` `PurgeCSS` ze šablon stylů odstraňte nepoužívaná pravidla. [Další informace](https://purgecss.com/plugins/gatsby.html)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | unused-javascript": {"message": "Vyhledejte pomocí nástroje `Webpack Bundle Analyzer` nepoužívaný kód JavaScript. [Další informace](https://www.gatsbyjs.com/plugins/gatsby-plugin-webpack-bundle-analyser-v2/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-long-cache-ttl": {"message": "Nakonfigurujte ukládání neměnných podkladů do mezipaměti. [Další informace](https://www.gatsbyjs.com/docs/how-to/previews-deploys-hosting/caching/)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-optimized-images": {"message": "Pokud chcete upravit kvalitu obr<PERSON>z<PERSON>ů, pou<PERSON>i<PERSON>te místo komponenty `<img>` komponentu `gatsby-plugin-image`. [Další informace](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/gatsby.js | uses-responsive-images": {"message": "Pomocí komponenty `gatsby-plugin-image` nastavte vhodné velikosti (`sizes`). [Další informace](https://www.gatsbyjs.com/docs/how-to/images-and-media/using-gatsby-plugin-image)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Zvažte nahrání souboru GIF do služby, pomocí které ho bude možné vložit jako video HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Zvažte použití [pluginu](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) nebo slu<PERSON>, která nahrané obrázky automaticky převede na optimální formáty."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Nainstalujte [plugin <PERSON><PERSON><PERSON> pro líné načítání](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), který umožňuje odložit načítání obrázků mi<PERSON> o<PERSON>zov<PERSON>, nebo přejdě<PERSON> na šablonu, kter<PERSON> tuto funkci poskytuje. Od verze Joomla 4.0 získají všechny nové obrázky atribut `loading` [automaticky](https://github.com/joomla/joomla-cms/pull/30748) z jádra."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "K dispozici je celá řada pluginů <PERSON>, kter<PERSON> vám pomohou [vložit kritické zdroje přímo do kódu](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) nebo [odložit načítání méně důležitých zdroj<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Upozorňujeme, že optimalizace pomocí těchto pluginů může narušit funkčnost vašich šablon nebo pluginů. Bude potřeba provést důkladné testování."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Na reakční dobu serveru mají vliv <PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON>í a specifikace serverů. Zvažte vyhledání optimalizované <PERSON>, pečlivý výběr optimalizačního rozšíření či upgradování serveru."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Zvažte zobrazování ukázek v kategoriích článků (např. prostřednictvím odkazu k načtení dalšího obsahu), snížení počtu článků zobrazených na jedné stránce, rozdělení dlouhých příspěvků na několik stránek nebo použití pluginu k línému načítání komentářů."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "K dispozici je celá řada [r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), kter<PERSON> váš web mohou zrychlit zřetězením, minifikací a zkomprimováním stylů CSS. Tuto funkci také poskytují některé šablony."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "K dispozici je celá řada [r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), která váš web mohou zrychlit zřetězením, minifikací a zkomprimováním skriptů. Tuto funkci také poskytují některé <PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Zvažte snížení nebo přepnut<PERSON> po<PERSON> [roz<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/), které na stránce načítají nepoužívané styly CSS. Rozšíření, která přidávají nadbytečné styly CSS, můžete vyhledat pomocí funkce [„využit<PERSON> kódu“](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či plugin můžete identifikovat podle adresy URL šablony stylů. Hledejte pluginy, které v seznamu mají mnoho šablon stylů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Plugin by měl šablonu stylů do fronty zařadit jen v případě, že je na stránce opravdu použita."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Zvažte snížení nebo přepnutí počtu [roz<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/), které na stránce načítají nepoužívaný kód JavaScript. Pluginy, které přid<PERSON>vají nadbytečný javascriptový kód, můžete vyhledat pomocí funkce [„využit<PERSON> kódu“](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědné rozšíření poznáte podle adresy URL skriptu. Hledejte rozšíření, která v seznamu mají mnoho skriptů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Rozšíření by mě<PERSON> skript do fronty zařadit jen v případě, že se na stránce opravdu používá."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Přečtěte si o [ukládání do mezipaměti prohlížeče v systému Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Zvažte použití [pluginu na optimalizaci obrázků](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), který obrázky komprimuje, p<PERSON><PERSON><PERSON><PERSON><PERSON> zach<PERSON> jejich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Zvažte použití [pluginu pro responzivní obrázky](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), abyste ve svém obsahu mohli použít responzivní obrázky."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Kompresi textu můžete aktivovat tak, že povolíte funkci Komprese stránek Gzip v nastavení Joomla (Systém > Globální konfigurace > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Pokud javascriptové podklady neslučujete do balíčků, zvažte použití nástroje [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Vypněte [minifikaci a slučování souborů JavaScript do balíčků](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrované v platformě Magento a z<PERSON>žte namísto nich použití nástroje [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "<PERSON><PERSON><PERSON> [defino<PERSON><PERSON> vlastn<PERSON>ch písem](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) zadej<PERSON> `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Zkuste v online tržišti [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) vyhledat rozšíření třetích stran, která umožňují použití novějších formátů obrázků."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Zvažte úpravu šablon produktů a katalogu s cílem využít funkci webové platformy pro [líné načítání](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [integraci platformy Magento s nástrojem Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Ve vývojářských nastaveních obchodu zapněte možnost minifikace souborů CSS. [Další informace](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Všechny javascriptové zdroje při implementaci statického obsahu minifikujte pomocí nástroje [Terser](https://www.npmjs.com/package/terser) a integrovanou funkci minifikace zakažte."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Vypněte [slučován<PERSON> souborů JavaScript do balíčků](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) integrované v platformě Magento."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Zkuste v tržišti [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) vyhledat různá rozšíření od třetích stran určená k optimalizaci obrázků."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Pokyny k předběžnému připojení nebo načtení <PERSON> (preconnect nebo dns-prefetch) lze přidat [upravením rozvržení motivů](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON><PERSON> `<link rel=preload>` lze přidat [úpravou rozvržení motivů](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON>jte místo komponenty `<img>` komponentu `next/image`, aby se <PERSON>ky optimalizoval formát obrázků. [Další informace](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Použ<PERSON>jte místo komponenty `next/image` komponentu `<img>`, aby se obrázky automaticky načítaly líně. [Další informace](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Použijte komponentu `next/image` a nastavte atribut priority na hodnotu true, aby se obrázek LCP načetl předem. [Další informace](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Odložte načítání nekritických skriptů třetích stran pomocí komponenty `next/script`. [Další informace](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Použijte komponentu `next/image`, aby se v<PERSON><PERSON> p<PERSON>y obrázky správné velikosti. [Další informace](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Zvažte nastavení pluginu `PurgeCSS` v konfiguraci platformy `Next.js` k odstranění nepoužívaných pravidel ze šablon stylů. [Další informace](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Vyhledejte pomocí nástroje `Webpack Bundle Analyzer` nepoužívaný kód JavaScript. [Další informace](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Zvažte změření výkonu aplikace v reálném světě pomocí nástroje `Next.js Analytics`. [Další informace](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Nakonfigurujte ukládání neměnných podkladů a stránek vykreslovaných na serveru (`Server-side Rendered` – SSR) do mezipaměti. [Další informace](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Pokud chcete upravit kvalitu obrázků, použi<PERSON>te místo komponenty `<img>` komponentu `next/image`. [Další informace](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Pomocí komponenty `next/image` nastavte vhodné velikost<PERSON> (`sizes`). [Další informace](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Zapněte na serveru Next.js kompresi. [Dalš<PERSON> informace](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | dom-size": {"message": "Požádejte svého správce účtu, aby aktivoval [`HTML Lazy Load`](https://support.nitropack.io/hc/en-us/articles/17144942904337). Nakonfigurováním této funkce optimalizujete rychlost vykreslování stránky."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | font-display": {"message": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> [`Override Font Rendering Behavior`](https://support.nitropack.io/hc/en-us/articles/16547358865041) ve službě NitroPack nastavte požadovanou hodnotu pro pravidlo CSS font-display."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/16547237162513), aby se obrázky automaticky převáděly do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | offscreen-images": {"message": "Zapnutím mož<PERSON> [`Automatic Image Lazy Loading`](https://support.nitropack.io/hc/en-us/articles/12457493524369-NitroPack-Lazy-Loading-Feature-for-Images) odložte načítání obrázků mimo obrazovku."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | render-blocking-resources": {"message": "Pro rychlejší počáteční načítání zapněte ve službě NitroPack funkci [`Remove render-blocking resources`](https://support.nitropack.io/hc/en-us/articles/13820893500049-How-to-Deal-with-Render-Blocking-Resources-in-NitroPack)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | server-response-time": {"message": "Zkraťte dobu odezvy serveru a optimalizujte vnímaný výkon tím, že aktivujete možnost [`Instant Load`](https://support.nitropack.io/hc/en-us/articles/16547340617361)."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-css": {"message": "V nastavení ukládání do mezipaměti zapněte možnost [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources), abyste snížili velikost souborů CSS, HTML a JavaScript a zkrátili dobu načítání."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unminified-javascript": {"message": "V nastavení ukládání do mezipaměti zapněte možnost [`Minify resources`](https://support.nitropack.io/hc/en-us/articles/360061059394-Minify-Resources), abyste snížili velikost souborů JS, HTML a CSS a zkrátili dobu načítání."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-css-rules": {"message": "Zapnutím mož<PERSON>ti [`Reduce Unused CSS`](https://support.nitropack.io/hc/en-us/articles/360020418457-Reduce-Unused-CSS) odstraňte pravidla CSS, která se na tuto stránku nevztahují."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | unused-javascript": {"message": "Nakonfigurujte ve službě NitroPack funkci [`<PERSON>ay<PERSON> Scripts`](https://support.nitropack.io/hc/en-us/articles/1500002600942-Delayed-Scripts), aby se načtení skripty načítaly až ve chvíli, kdy jsou pot<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-long-cache-ttl": {"message": "V nabídce `Caching` přejd<PERSON>te na funkci [`Improve Server Response Time`](https://support.nitropack.io/hc/en-us/articles/1500002321821-Improve-Server-Response-Time) a upravte dobu vypršení me<PERSON>, abyste zkrátili dobu načítání a zlepšili uživatelský dojem."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-optimized-images": {"message": "Zapněte nastavení [`Image Optimization`](https://support.nitropack.io/hc/en-us/articles/14177271695121-How-to-serve-images-in-next-gen-formats-using-NitroPack), aby se obrázky automaticky komprimovaly, optimal<PERSON>ovaly a převáděly do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-responsive-images": {"message": "<PERSON>apn<PERSON><PERSON> funkci [`Adaptive Image Sizing`](https://support.nitropack.io/hc/en-us/articles/10123833029905-How-to-Enable-Adaptive-Image-Sizing-For-Your-Site), aby vaše obrázky preventivně optimalizovala a přizpůsobovala je na všech zařízeních rozměrům kontejnerů, ve kterých se zobrazují."}, "node_modules/lighthouse-stack-packs/packs/nitropack.js | uses-text-compression": {"message": "Použitím mož<PERSON>ti [`Gzip compression`](https://support.nitropack.io/hc/en-us/articles/13229297479313-Enabling-GZIP-compression) ve službě NitroPack zmenšete velikost souborů odesílaných do prohlížeče."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Použ<PERSON>jte komponentu `nuxt/image` a nastavte `format=\"webp\"`. [Další informace](https://image.nuxt.com/usage/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Po<PERSON><PERSON><PERSON>jte komponentu `nuxt/image` a pro obrázky mimo obrazovku nastavte `loading=\"lazy\"`. [Další informace](https://image.nuxt.com/usage/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Pro obrázek LCP použijte komponentu `nuxt/image` a nastavte atribut `preload`. [Další informace](https://image.nuxt.com/usage/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komponentu `nuxt/image` a zadejte explicitně atributy `width` a `height`. [Dalš<PERSON> informace](https://image.nuxt.com/usage/nuxt-img#width-height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Pou<PERSON><PERSON>jte komponentu `nuxt/image` a nastavte vhodnou hodnotu atributu `quality`. [Další informace](https://image.nuxt.com/usage/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Po<PERSON><PERSON><PERSON>jte komponentu `nuxt/image` a nastavte vhodnou hodnotu atributu `sizes`. [Další informace](https://image.nuxt.com/usage/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[<PERSON><PERSON><PERSON> animované obrázky GIF nahradí video](https://web.dev/replace-gifs-with-videos/), budou se webové stránky načítat rychleji. Zvažte použití moderních formátů, jako je [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) nebo [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), které v porovnání se současným videokodekem VP9 zvýší efektivitu komprese o více než 30 %."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Zvažte použití [pluginu](https://octobercms.com/plugins?search=image) nebo služ<PERSON>, která nahrané obrázky automaticky převede na optimální formáty. [Bezztrátové obrázky WebP](https://developers.google.com/speed/webp) jsou o 26 % menší než obrázky PNG a o 25–34 % menší než obrázky JPEG při ekvivalentním indexu kvality SSIM. Dalším formátem obrázku příští generace, který můžete zvážit, je [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Zvažte instalaci [pluginu pro líné načítání](https://octobercms.com/plugins?search=lazy), který umožňuje odložit načítání obrázků mimo obrazovku, nebo přejděte na motiv, který tuto funkci poskytuje. Zvažte také použití [pluginu AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "K dispozici je celá řada pluginů, k<PERSON><PERSON> chod [kritic<PERSON><PERSON><PERSON> podklad<PERSON>](https://octobercms.com/plugins?search=css). Tyto pluginy mohou narušit funkčnost jiných pluginů, proto byste je měli důkladně otestovat."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Na reakční dobu serveru mají vliv motivy, pluginy a specifikace serverů. Zvažte vyhledání optimalizovaného motivu, pečlivý výběr optimalizačního modulu či upgradování serveru. Systém pro správu obsahu October vývojářům také umožňuje odložit zpracování časově náročných úloh, například odeslání e‑mailu, pomocí front ([`Queues`](https://octobercms.com/docs/services/queues)). To webové požadavky výrazně urychlí."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Zvažte zobrazování ukázek v seznamech příspěvků (např. prostřednictvím tlačítka `show more`), snížení počtu příspěvků zobrazených na jedné webové stránce, rozdělení dlouhých příspěvků na několik webových stránek nebo použití pluginu k línému načítání komentářů."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "K dispozici je řada [pluginů](https://octobercms.com/plugins?search=css), které web mohou zrychlit zřetězením, minifikací a zkomprimováním stylů. Vývoj lze urychlit použitím procesu sestavení, který tuto minifikaci provede předem."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "K dispozici je řada [pluginů](https://octobercms.com/plugins?search=javascript), které web mohou zrychlit zřetězením, minifikací a zkomprimováním skriptů. Vývoj lze urychlit použitím procesu sestavení, který tuto minifikaci provede předem."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Zvažte kontrolu [pluginů](https://octobercms.com/plugins), které na webu načítají nepoužívané styly CSS. Pluginy, které přidávají zbytečné styly CSS, můžete vyhledat pomocí přehledu [pokryt<PERSON> kódu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v Chrome DevTools. Odpovědný motiv či plugin poznáte podle adresy URL šablony stylů. Hledejte pluginy s velkým počtem šablon stylů se spoustou červené barvy v grafu využití kódu. Plugin by měl šablonu stylů přidat jen v případě, že je na stránce opravdu použita."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Zvaž<PERSON> kontrolu [pluginů](https://octobercms.com/plugins?search=javascript), které na webové stránce načítají nepoužívaný javascriptový kód. Pluginy, kter<PERSON> přidávají zbytečný javascriptový kód, můžete vyhledat pomocí přehledu [pokrytí kódu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v Chrome DevTools. Odpovědný motiv či plugin poznáte podle adresy URL skriptu. Hledejte pluginy s velkým počtem skriptů se spoustou červené barvy v grafu využití kódu. Plugin by měl přidat jen skripty, které se na webové stránce opravdu používají."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si, jak [pomoc<PERSON> mezipaměti HTTP předcházet zbytečným síťovým požadavkům](https://web.dev/http-cache/#caching-checklist). K dispozici je řada [pluginů](https://octobercms.com/plugins?search=Caching), pomocí nichž lze ukládání do mezipaměti zrychlit."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Zvažte použití [pluginu na optimalizaci obrázků](https://octobercms.com/plugins?search=image), který obrázky komprimuje, p<PERSON><PERSON><PERSON><PERSON><PERSON> zachováv<PERSON> jejich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Nahrajte obrázky přímo prostřednictvím správce médií, abyste zajistili dostupnost potřebných velikostí obrázků. Zvažte použití [filtru pro změnu velikosti](https://octobercms.com/docs/markup/filter-resize) nebo [pluginu pro změnu velikosti](https://octobercms.com/plugins?search=image), abyste zaj<PERSON>ili, že budou použity obrázky optimální velikosti."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "V konfiguraci webového serveru povolte kompresi textu."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Pokud na stránce vykreslujete mnoho opakujících se prvků, z<PERSON><PERSON><PERSON> použití windowingové knihovny, jako je `react-window`, kter<PERSON> minimalizuje počet vytvářených uzlů DOM. [<PERSON>š<PERSON> informace](https://web.dev/virtualize-long-lists-react-window/) Minimalizujte také počet překreslení pomocí funkcí [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) nebo [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo), a pokud používáte hooky `Effect`, [přeskakujte efekty](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), dokud se nezmění určité závislosti."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Pokud použív<PERSON>te React Router, minimalizujte použití komponenty `<Redirect>` k [navigaci po trase](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Pokud některé komponenty React vykreslujete na straně serveru, z<PERSON>žte použití metody `renderToPipeableStream()` nebo `renderToStaticNodeStream()`, k<PERSON><PERSON> klientovi umožní přijímat různé části označení a dosazovat do nich data samostatně namísto všech najednou. [Další informace](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Pokud váš sestavovací systém minifikuje soubory CSS automaticky, dej<PERSON> pozor, abyste implementovali produkční sestavení aplikace. Můžete to zkontrolovat pomocí rozšíření React Developer Tools. [Další informace](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Pokud váš sestavovací systém minifikuje soubory JavaScriptu automaticky, dejte pozor, abyste implementovali produkční sestavení aplikace. Můžete to zkontrolovat pomocí rozšíření React Developer Tools. [Další informace](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Pokud vykreslení neprovádíte na straně serveru, [rozdělte balíčky JavaScriptu](https://web.dev/code-splitting-suspense/) pomocí metody `React.lazy()`. <PERSON>ak kód rozdělte pomocí knihovny třetí strany, jako je napřík<PERSON> [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Změřte rychlost vykreslování komponent pomocí nástroje React DevTools Profiler, který využívá rozhraní Profiler API. [Další informace](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | efficient-animated-content": {"message": "Umístěte videa do `VideoBoxes`, p<PERSON><PERSON><PERSON><PERSON><PERSON>bte je pomoc<PERSON> `Video Masks` nebo přidejte `Transparent Videos`. [Další informace](https://support.wix.com/en/article/wix-video-about-wix-video)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | modern-image-formats": {"message": "Obrázky nahrajte pomocí nástroje `Wix Media Manager`, aby byly automaticky poskytovány ve formátu WebP. [<PERSON><PERSON>í způsoby optimalizace médií na webu](https://support.wix.com/en/article/site-performance-optimizing-your-media)"}, "node_modules/lighthouse-stack-packs/packs/wix.js | render-blocking-resources": {"message": "Při [př<PERSON><PERSON><PERSON><PERSON> kódu třetí strany](https://support.wix.com/en/article/site-performance-using-third-party-code-on-your-site) na kartě `Custom Code` na hlavním panelu webu zajistěte, aby jeho načtení bylo odloženo nebo aby se načítal na konci hlavní části kódu. Pokud je to možné, použijte k vložení marketingových nástrojů na svůj web [integrace](https://support.wix.com/en/article/about-marketing-integrations) Wix. "}, "node_modules/lighthouse-stack-packs/packs/wix.js | server-response-time": {"message": "K co nejrychlejšímu poskytování odpovědí většině návštěvníků využívá Wix sítě CDN a ukládání do mezipaměti. Zvažte [ruční povolení ukládání do mezipaměti](https://support.wix.com/en/article/site-performance-caching-pages-to-optimize-loading-speed) pro svůj web, zejména pokud používáte `Velo`."}, "node_modules/lighthouse-stack-packs/packs/wix.js | unused-javascript": {"message": "Zkontrolujte veškerý kód tř<PERSON>í<PERSON> stran, kter<PERSON> jste na svůj web přidali na kartě `Custom Code` na hlavním panelu webu a ponechte pouze služby, kter<PERSON> jsou pro váš web nezbytné. [Další informace](https://support.wix.com/en/article/site-performance-removing-unused-javascript)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Zvažte nahrání souboru GIF do služby, pomocí které ho bude možné vložit jako video HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Zvažte použití pluginu [Performance Lab](https://wordpress.org/plugins/performance-lab/) k automatickému převedení nahraných obrázků JPEG do formátu WebP všude, kde je podporován."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Nainstalujte [plugin služby WordPress pro líné načítání](https://wordpress.org/plugins/search/lazy+load/), který umožňuje odložit načítání obrázků mimo obrazovku, nebo přejděte na motiv, který tuto funkci poskytuje. Zvažte také použití [pluginu AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "K dispozici je celá řada pluginů služby WordPress, které vám pomohou [vložit kritické podklady přímo do kódu](https://wordpress.org/plugins/search/critical+css/) nebo [odložit načítání méně důležitých zdrojů](https://wordpress.org/plugins/search/defer+css+javascript/). Upozorňujeme, že optimalizace pomocí těchto pluginů může narušit funkčnost vašeho motivu nebo pluginů. V kódu proto pravděpodobně budete muset provést změny."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Na reakční dobu serveru mají vliv motivy, pluginy a specifikace serverů. Zvažte vyhledání optimalizovaného motivu, pečlivý výběr optimalizačního pluginu či upgradování serveru."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Zvažte zobrazování ukázek v seznamech příspěvků (např. prostřednictvím značky k načtení dalšího obsahu), snížení počtu příspěvků zobrazených na jedné str<PERSON>, rozdělení dlouhých příspěvků na několik stránek nebo použití pluginu k línému načítání komentářů."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "K dispozici je celá řada [pluginů služby WordPress](https://wordpress.org/plugins/search/minify+css/), které web mohou zrychlit zřetězením, minifikací a komprimací stylů. Pokud je to možn<PERSON>, můžete minifikaci provést také předem pomocí procesu sestavení."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "K dispozici je celá řada [pluginů služby WordPress](https://wordpress.org/plugins/search/minify+javascript/), kter<PERSON> v<PERSON>š web mohou zrychlit zřetězením, minifikací a zkomprimováním skriptů. Pokud je to možn<PERSON>, můžete minifikaci provést také předem pomocí procesu sestavení."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Zvažte snížení počtu [pluginů služby WordPress](https://wordpress.org/plugins/), které na stránce načítají nevyužité styly CSS. Pluginy, které přidávají nadbytečné styly CSS, můžete vyhledat pomocí funkce [„využití kódu“](https://developer.chrome.com/docs/devtools/coverage/) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či plugin můžete identifikovat podle adresy URL šablony stylů. Hledejte pluginy, které v seznamu mají mnoho šablon stylů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Plugin by měl šablonu stylů do fronty zařadit jen v případě, že je na stránce opravdu použita."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Zvažte snížení počtu [pluginů služby WordPress](https://wordpress.org/plugins/), které na stránce načítají nepoužívaný kód JavaScript. Pluginy, které přidávají nadbytečný javascriptový kód, můžete vyhledat pomocí funkce [„využití kódu“](https://developer.chrome.com/docs/devtools/coverage/) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či plugin poznáte podle adresy URL skriptu. Hledejte pluginy, které v seznamu mají mnoho skriptů, u nichž je při použití funkce „využití kódu“ velké množství kódu označeno červeně. Plugin by měl skript do fronty zařadit jen v případě, že se na stránce opravdu používá."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Přečtěte si o [ukládání do mezipaměti prohlížeče ve službě WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Zvažte použití [pluginu služby WordPress na optimalizaci obrázků](https://wordpress.org/plugins/search/optimize+images/), který obrázky komprimuje, p<PERSON><PERSON><PERSON><PERSON><PERSON> zach<PERSON> jejich kvalitu."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Nahrajte obrázky přímo prostřednictvím [medi<PERSON>ln<PERSON> knihovny](https://wordpress.org/support/article/media-library-screen/), abyste zajistili dostupnost potřebných velikostí obrázků, a poté je vložte z mediální knihovny, případně použití optimálních velikostí obrázků zajistěte pomocí widgetu pro obrázky (včetně obrázků pro responzivní dělicí body). Obrázky v této velikosti: `Full Size` používejte pouze v případě, že jsou jejich rozměry adekvátní k použití. [Další informace](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Můžete v konfiguraci webového serveru zapnout kompresi textu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "V pluginu WP Rocket zapněte na kartě Image Optimization (Optimalizace obrázků) možnost Imagify, aby se obrázky převedly do formátu WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Pokud toto doporučení chcete vyřešit, zapněte v pluginu WP Rocket funkci [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) (<PERSON><PERSON><PERSON>). Tato funkce odloží načítání obrázků do doby, kdy návštěvník stránku posune dolů a skutečně je potřebuje vidět."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Pokud toto doporučení chcete vyřešit, aktivujte v pluginu WP Rocket možnost [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranit nepoužívané styly CSS) a [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (Odložit načítání JavaScriptu). Tyto funkce optimalizují soubory CSS a JavaScriptu, aby neblokovaly v<PERSON><PERSON><PERSON> str<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Tento problém vyřešíte tak, že v pluginu WP Rocket zapnete možnost [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Minifikovat soubory CSS). Případné mezery a komentáře v souborech CSS vašeho webu budou odstraněny, aby tyto soubory byly menš<PERSON> a rychleji se stahovaly."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Tento problém vyřešíte tak, že v pluginu WP Rocket zapnete možnost [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Minifikovat soubory JavaScriptu). Ze souborů JavaScriptu budou odstraněny mezery a komentáře, aby byly men<PERSON> a rychleji se načítaly."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Tento problém vyř<PERSON><PERSON><PERSON><PERSON> tak, že ve službě WP Rocket povolíte možnost [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranit nepoužívané styly CSS). Zmenší velikost stránek tím, že odstraní všechny nepoužívané styly CSS a šablony stylů, a pro každou stránku ponechá pouze používané styly CSS."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Tento problém vyřeš<PERSON><PERSON> tak, že v pluginu WP Rocket zapnete možnost [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Odložit spuštění JavaScriptu). Načítán<PERSON> strán<PERSON> se zlepší, protože se skripty budou spouštět až po interakci uživatele. Pokud váš web obsahuje prvky iframe, můžete v pluginu WP Rocket použít také možnosti [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (<PERSON><PERSON>é načí<PERSON>ání prvků iframe a videí) a [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Nahradit videa YouTube náhledovým obrázkem)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "V pluginu WP Rocket zapněte na kartě Image Optimization (Optimalizace obrázků) možnost Imagify a spusťte hromadnou optimalizaci ke komprimaci obrázků."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Pomocí mož<PERSON> [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Předběžně načítat požadavky DNS) v pluginu WP Rocket přidejte prvek dns-prefetch a urychlete připojení k externím doménám. WP Rocket také automaticky přidává prvek preconnect pro [doménu Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) a veškeré záznamy CNAME přidané pomocí funkce [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Povolit CDN)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Pokud chcete tento problém s písmy vyřešit, zapněte v pluginu WP Rocket možnost [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranit nepoužívané styly CSS). Kritická písma vašeho webu se budou předběžně načítat přednostně."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Zobrazit kalkulačku."}, "report/renderer/report-utils.js | collapseView": {"message": "Sbalit zobrazení"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Počáteční navigace"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Maximální latence kritické trasy:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopírovat JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Přepnout tmavý motiv"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Vytisknout úplný přehled"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Vytisknout souhrn"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Uložit jako gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Uložit jako HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Uložit jako J<PERSON>"}, "report/renderer/report-utils.js | dropdownViewUnthrottledTrace": {"message": "Zobrazit původní trasování"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Otevřít v prohlížeči"}, "report/renderer/report-utils.js | errorLabel": {"message": "Ch<PERSON>ba!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Chyba přehledu: žádné informace o auditu"}, "report/renderer/report-utils.js | expandView": {"message": "Rozbalit zobrazení"}, "report/renderer/report-utils.js | firstPartyChipLabel": {"message": "První strana"}, "report/renderer/report-utils.js | footerIssue": {"message": "Zaznamenat problém"}, "report/renderer/report-utils.js | hide": {"message": "Skr<PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratorní data"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Analýza aktuální stránky pomocí nástroje [Lighthouse](https://developers.google.com/web/tools/lighthouse/) v emulované mobilní síti. Hodnoty jsou odhady a mohou se lišit."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Další položky k ruční kontrole"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Není <PERSON>"}, "report/renderer/report-utils.js | openInANewTabTooltip": {"message": "Otevřít na nové kartě"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Př<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Odhadovaná úspora"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Úspěšné audity"}, "report/renderer/report-utils.js | pwaRemovalMessage": {"message": "V souladu s [aktualizovanými kritérii prohlížeče Chrome pro instalaci](https://developer.chrome.com/blog/update-install-criteria) bude v budoucí verzi nástroje Lighthouse ukončena podpora kategorie aplikací PWA. Pokyny k budoucímu testování aplikací PWA najdete v [aktualizované dokumentaci aplikací PWA](https://developer.chrome.com/docs/devtools/progressive-web-apps/)."}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "První načtení s<PERSON>"}, "report/renderer/report-utils.js | runtimeAnalysisWindowSnapshot": {"message": "Přehled stavu v bodu v čase"}, "report/renderer/report-utils.js | runtimeAnalysisWindowTimespan": {"message": "Časové rozpětí uživatelských interakcí"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "<PERSON>last<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emulovaná plocha"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulované zařízení Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Bez emulace"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Verze knihovny Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Bez omezení výkonu procesoru/paměti"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Omezení procesoru"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Zařízení"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Omezení s<PERSON>ě"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User agent (síť)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Návš<PERSON>ěva jedn<PERSON>"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Tato data pocházejí z návštěvy jedné stránky – v protikladu k datům z terénu, která sumarizují mnoho relací."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Pomalé omezení 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Zobrazit"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Zobrazit audity relevantní pro:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Sbalit úryvek"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Rozbalit úryvek"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Zobrazit zdroje třetích stran"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Poskytnuto prostředím"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON><PERSON> to<PERSON><PERSON> s<PERSON>tění nástroje Lighthouse se vyskytly problémy:"}, "report/renderer/report-utils.js | unattributable": {"message": "Nepřiřaditelné"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Hodnot<PERSON> jsou odhady a mohou se lišit. [Skóre výkonu se počítá](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) přímo z těchto metrik."}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Zobrazit trasovací protokol"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Zobrazit stromovou mapu"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Úspěšné audity s upozorněními"}, "report/renderer/report-utils.js | warningHeader": {"message": "Upozornění: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Všechna písma"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Pokrytí"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Du<PERSON><PERSON><PERSON><PERSON> moduly"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Počet bajtů zdroje"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Zobrazit nebo skrýt tabulku"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Nevyuž<PERSON> baj<PERSON>"}}