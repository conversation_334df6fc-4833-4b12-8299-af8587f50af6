{"name": "lighthouse", "type": "module", "version": "11.7.1", "description": "Automated auditing, performance metrics, and best practices for the web.", "main": "./core/index.js", "bin": {"lighthouse": "./cli/index.js", "chrome-debug": "./core/scripts/manual-chrome-launcher.js", "smokehouse": "./cli/test/smokehouse/frontends/smokehouse-bin.js"}, "engines": {"node": ">=18.16"}, "scripts": {"prepack": "yarn build-report --standalone --flow --esm && yarn build-types", "postpack": "yarn clean-types", "build-all": "npm-run-posix-or-windows build-all:task", "build-all:task": "yarn build-report && yarn build-cdt-lib && yarn build-devtools && concurrently 'yarn build-extension' 'yarn build-lr' 'yarn build-viewer' 'yarn build-treemap' 'yarn build-smokehouse-bundle' && yarn build-pack", "build-all:task:windows": "yarn build-report && yarn build-cdt-lib && yarn build-extension && yarn build-devtools && yarn build-lr && yarn build-viewer && yarn build-treemap && yarn build-smokehouse-bundle", "build-cdt-lib": "node ./build/build-cdt-lib.js", "build-extension": "yarn build-extension-chrome && yarn build-extension-firefox", "build-extension-chrome": "node ./build/build-extension.js chrome", "build-extension-firefox": "node ./build/build-extension.js firefox", "build-devtools": "yarn reset-link && node ./build/build-bundle.js clients/devtools/devtools-entry.js dist/lighthouse-dt-bundle.js && node ./build/build-dt-report-resources.js", "build-smokehouse-bundle": "node ./build/build-smokehouse-bundle.js", "build-lr": "yarn reset-link && node --max-old-space-size=4096 ./build/build-lightrider-bundles.js", "build-pack": "bash build/build-pack.sh", "build-report": "node build/build-report-components.js && node build/build-report.js", "build-sample-reports": "yarn build-report && node build/build-sample-reports.js", "build-treemap": "node ./build/build-treemap.js", "build-viewer": "node ./build/build-viewer.js", "build-types": "yarn type-check && rsync -a .tmp/tsbuildinfo/ ./ --include='*.d.ts' --include='*.d.cts' --exclude='*.map' --exclude='*.tsbuildinfo'", "reset-link": "(yarn unlink || true) && yarn link && yarn link lighthouse", "c8": "bash core/scripts/c8.sh", "clean": "rm -r dist proto/scripts/*.json proto/scripts/*_pb2.* proto/scripts/*_pb.* proto/scripts/__pycache__ proto/scripts/*.pyc *.report.html *.report.dom.html *.report.json *.devtoolslog.json *.trace.json shared/localization/locales/*.ctc.json || true", "clean-types": "git clean -xfq '*.d.ts' '*.d.cts' -e 'node_modules/' -e 'dist/' -e '.tmp/' -e '**/types/'", "lint": "[ \"$CI\" = true ] && eslint --quiet -f codeframe . || eslint .", "smoke": "node -r source-map-support/register cli/test/smokehouse/frontends/smokehouse-bin.js", "debug": "node --inspect-brk ./cli/index.js", "start": "yarn build-report --standalone && node ./cli/index.js", "mocha": "node --loader=testdouble core/test/scripts/run-mocha-tests.js", "test": "yarn diff:sample-json && yarn lint --quiet && yarn unit && yarn type-check", "test-bundle": "yarn smoke --runner bundle", "test-clients": "yarn mocha --testMatch clients/**/*-test.js && yarn mocha --testMatch clients/**/*-test-pptr.js", "test-viewer": "yarn unit-viewer && yarn mocha --testMatch viewer/**/*-test-pptr.js --timeout 35000", "test-treemap": "yarn unit-treemap && yarn mocha --testMatch treemap/**/*-test-pptr.js --timeout 35000", "test-lantern": "bash core/scripts/test-lantern.sh", "test-legacy-javascript": "bash core/scripts/test-legacy-javascript.sh", "test-docs": "yarn --cwd docs/recipes/ test", "test-proto": "yarn compile-proto && yarn build-proto-roundtrip", "unit-core": "yarn mocha core", "unit-cli": "yarn mocha --testMatch cli/**/*-test.js", "unit-report": "yarn mocha --testMatch report/**/*-test.js", "unit-treemap": "yarn mocha --testMatch treemap/**/*-test.js", "unit-viewer": "yarn mocha --testMatch viewer/**/*-test.js", "unit-flow": "bash flow-report/test/run-flow-report-tests.sh", "unit": "yarn unit-flow && yarn mocha", "unit:ci": "NODE_OPTIONS=--max-old-space-size=8192 npm run unit", "core-unit": "yarn unit-core", "cli-unit": "yarn unit-cli", "viewer-unit": "yarn unit-viewer", "watch": "yarn unit-core --watch", "unit:cicoverage": "yarn c8 --all yarn unit:ci", "coverage": "yarn unit:cicoverage && c8 report --reporter html", "coverage:smoke": "yarn c8 yarn smoke -j=1 && c8 report --reporter html", "devtools": "bash core/scripts/roll-to-devtools.sh", "chrome": "node core/scripts/manual-chrome-launcher.js", "fast": "node ./cli/index.js --preset=desktop --throttlingMethod=provided", "deploy-treemap": "yarn build-treemap --deploy", "deploy-viewer": "yarn build-viewer --deploy", "vercel-build": "yarn build-sample-reports && yarn build-viewer && yarn build-treemap", "dogfood-lhci": "./core/scripts/dogfood-lhci.sh", "timing-trace": "node core/scripts/generate-timing-trace.js", "changelog": "conventional-changelog --config ./build/changelog-generator/index.cjs --infile changelog.md --same-file", "type-check": "tsc --build ./tsconfig-all.json", "i18n:checks": "./core/scripts/i18n/assert-strings-collected.sh", "i18n:collect-strings": "node core/scripts/i18n/collect-strings.js", "update:lantern-baseline": "node core/scripts/lantern/update-baseline-lantern-values.js", "update:sample-artifacts": "node core/scripts/update-report-fixtures.js", "update:sample-json": "yarn i18n:collect-strings && node ./cli -A=./core/test/results/artifacts --config-path=./core/test/results/sample-config.js --output=json --output-path=./core/test/results/sample_v2.json && node core/scripts/cleanup-LHR-for-diff.js ./core/test/results/sample_v2.json --only-remove-timing && node ./core/scripts/update-flow-fixtures.js", "update:flow-sample-json": "yarn i18n:collect-strings && node ./core/scripts/update-flow-fixtures.js", "test-devtools": "bash core/test/devtools-tests/test-locally.sh", "open-devtools": "bash core/scripts/open-devtools.sh", "run-devtools": "node core/scripts/pptr-run-devtools.js", "diff:sample-json": "yarn i18n:checks && bash core/scripts/assert-golden-lhr-unchanged.sh", "diff:flow-sample-json": "yarn i18n:collect-strings && bash core/scripts/assert-baseline-flow-result-unchanged.sh", "computeBenchmarkIndex": "./core/scripts/benchmark.js", "save-latest-run": "./core/scripts/save-latest-run.sh", "compile-proto": "protoc --python_out=./ ./proto/lighthouse-result.proto && mv ./proto/*_pb2.py ./proto/scripts || (echo \"❌ Install protobuf = 3.20.x to compile the proto file.\" && false)", "build-proto-roundtrip": "mkdir -p .tmp && python3 proto/scripts/json_roundtrip_via_proto.py", "static-server": "node cli/test/fixtures/static-server.js", "serve-dist": "cd dist && python3 -m http.server 7878", "serve-gh-pages": "cd dist/gh-pages && python3 -m http.server 7333", "serve-treemap": "yarn serve-gh-pages", "serve-viewer": "yarn serve-gh-pages", "flow-report": "yarn build-report --flow && node ./core/scripts/build-test-flow-report.js"}, "devDependencies": {"@build-tracker/cli": "^1.0.0-beta.15", "@esbuild-kit/esm-loader": "^2.1.1", "@esbuild-plugins/node-modules-polyfill": "^0.1.4", "@formatjs/icu-messageformat-parser": "^2.6.2", "@jest/fake-timers": "^28.1.0", "@testing-library/preact": "^3.1.1", "@testing-library/preact-hooks": "^1.1.0", "@types/archiver": "^2.1.2", "@types/chrome": "^0.0.154", "@types/configstore": "^4.0.0", "@types/cpy": "^5.1.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.2.1", "@types/estree": "^0.0.50", "@types/gh-pages": "^2.0.0", "@types/google.analytics": "0.0.39", "@types/jpeg-js": "^0.3.7", "@types/jsdom": "^16.2.13", "@types/lodash": "^4.14.178", "@types/mocha": "^9.0.0", "@types/node": "*", "@types/pako": "^1.0.1", "@types/resize-observer-browser": "^0.1.1", "@types/resolve": "^1.20.2", "@types/semver": "^5.5.0", "@types/tabulator-tables": "^4.9.1", "@types/ws": "^7.0.0", "@types/yargs": "^17.0.8", "@types/yargs-parser": "^20.2.1", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "acorn": "^8.5.0", "angular": "^1.7.4", "archiver": "^3.0.0", "builtin-modules": "^3.3.0", "c8": "^7.11.3", "chalk": "^2.4.1", "chrome-devtools-frontend": "1.0.1153166", "concurrently": "^6.4.0", "conventional-changelog-cli": "^2.1.1", "cpy": "^8.1.2", "cross-env": "^7.0.2", "csv-validator": "^0.0.3", "es-main": "^1.2.0", "esbuild": "0.19.11", "eslint": "^8.4.1", "eslint-config-google": "^0.14.0", "eslint-formatter-codeframe": "^7.32.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-local-rules": "1.1.0", "event-target-shim": "^6.0.2", "expect": "^28.1.0", "firebase": "^9.0.2", "gh-pages": "^2.0.1", "glob": "^7.1.3", "idb-keyval": "2.2.0", "jest-mock": "^27.3.0", "jest-snapshot": "^28.1.0", "jsdom": "^12.2.0", "lighthouse-plugin-soft-navigation": "^1.0.1", "magic-string": "^0.25.7", "mime-types": "^2.1.30", "mocha": "^10.0.0", "node-fetch": "^2.6.1", "npm-run-posix-or-windows": "^2.0.2", "pako": "^2.0.3", "preact": "^10.7.2", "pretty-json-stringify": "^0.0.2", "puppeteer": "^22.5.0", "resolve": "^1.22.1", "rollup": "^2.52.7", "rollup-plugin-polyfill-node": "^0.12.0", "source-map-support": "^0.5.21", "tabulator-tables": "^4.9.3", "terser": "^5.18.2", "testdouble": "^3.18.0", "typed-query-selector": "^2.6.1", "typescript": "^5.0.4", "wait-for-expect": "^3.0.2", "webtreemap-cdt": "^3.2.1"}, "dependencies": {"@paulirish/trace_engine": "^0.0.19", "@sentry/node": "^6.17.4", "axe-core": "^4.9.0", "chrome-launcher": "^1.1.1", "configstore": "^5.0.1", "csp_evaluator": "1.1.1", "devtools-protocol": "0.0.1232444", "enquirer": "^2.3.6", "http-link-header": "^1.1.1", "intl-messageformat": "^10.5.3", "jpeg-js": "^0.4.4", "js-library-detector": "^6.7.0", "lighthouse-logger": "^2.0.1", "lighthouse-stack-packs": "1.12.1", "lodash": "^4.17.21", "lookup-closest-locale": "6.2.0", "metaviewport-parser": "0.3.0", "open": "^8.4.0", "parse-cache-control": "1.0.1", "ps-list": "^8.0.0", "puppeteer-core": "^22.5.0", "robots-parser": "^3.0.1", "semver": "^5.3.0", "speedline-core": "^1.4.3", "third-party-web": "^0.24.1", "tldts-icann": "^6.1.0", "ws": "^7.0.0", "yargs": "^17.3.1", "yargs-parser": "^21.0.0"}, "resolutions": {"puppeteer/**/devtools-protocol": "0.0.1232444", "puppeteer-core/**/devtools-protocol": "0.0.1232444"}, "repository": "GoogleChrome/lighthouse", "keywords": ["google", "chrome", "devtools"], "author": "Google LLC", "license": "Apache-2.0", "bugs": {"url": "https://github.com/GoogleChrome/lighthouse/issues"}, "homepage": "https://github.com/GoogleChrome/lighthouse#readme"}