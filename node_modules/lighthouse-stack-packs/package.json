{"name": "lighthouse-stack-packs", "version": "1.12.1", "description": "Lighthouse Stack Packs", "repository": "GoogleChrome/lighthouse-stack-packs", "keywords": ["google", "chrome", "lighthouse"], "author": "The Chromium Authors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/GoogleChrome/lighthouse-stack-packs/issues"}, "homepage": "https://github.com/GoogleChrome/lighthouse-stack-packs#readme", "main": "index.js", "devDependencies": {"del-cli": "^3.0.0", "expect": "^29.3.1", "jsdom": "^12.2.0", "lighthouse": "GoogleChrome/lighthouse#master", "svgo": "^3.0.2", "typescript": "^5.1.3", "uvu": "^0.5.1"}, "scripts": {"prepublishOnly": "del-cli \"*.d.ts\" \"packs/*.d.ts\" && npm run generate-types", "generate-types": "tsc -p .", "test": "uvu test"}, "files": ["packs/*", "index.d.ts"]}