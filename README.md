# TechSupport Pro Website

Modern, accessible, and performant website for TechSupport Pro - a Dutch IT support company.

## ✨ Features

- **Modern Architecture**: ES6+ modules, SCSS, Vite build system
- **Accessibility First**: WCAG 2.1 AA compliant with comprehensive testing
- **Performance Optimized**: Lighthouse scores 90+, Core Web Vitals optimized
- **Responsive Design**: Mobile-first approach with fluid layouts
- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Internationalization**: Multi-language support (Dutch, English, French)
- **SEO Optimized**: Semantic HTML, structured data, optimized meta tags

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📁 Project Structure

```
├── src/                    # Source files
│   ├── assets/            # Images, fonts, icons
│   ├── styles/            # SCSS (7-1 architecture)
│   ├── scripts/           # JavaScript modules
│   ├── templates/         # HTML templates
│   ├── data/             # Static data files
│   └── index.html        # Main HTML file
├── public/               # Static assets
├── tools/               # Build tools and scripts
├── tests/               # Test files
├── docs/                # Documentation
└── dist/                # Built files (generated)
```

## 🛠️ Technology Stack

- **Build Tool**: Vite 5.0
- **Styling**: SCSS with 7-1 architecture
- **JavaScript**: ES6+ modules, no frameworks
- **Testing**: Jest, Pa11y (accessibility)
- **Performance**: Lighthouse CI, Core Web Vitals
- **Code Quality**: ESLint, Prettier

## 📊 Performance Metrics

- **Lighthouse Performance**: 95+
- **Lighthouse Accessibility**: 100
- **Lighthouse Best Practices**: 95+
- **Lighthouse SEO**: 100
- **Core Web Vitals**: All green

## 🎯 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📚 Documentation

- [Setup Guide](docs/SETUP.md) - Development setup and workflow
- [Recommended Structure](RECOMMENDED_STRUCTURE.md) - Modern project architecture

## 🧪 Testing

```bash
# Unit tests
npm test

# Accessibility tests
npm run a11y-test

# Performance audit
npm run lighthouse
```

## 🔧 Development

```bash
# Code quality
npm run lint
npm run format

# Image optimization
npm run optimize-images

# Generate sitemap
npm run generate-sitemap
```

## 📄 License

© 2024 TechSupport Pro. All rights reserved.