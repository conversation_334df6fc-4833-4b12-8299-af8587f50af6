<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
  <defs>
    <linearGradient id="icon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#667eea" />
      <stop offset="100%" stop-color="#764ba2" />
    </linearGradient>
  </defs>
  <circle cx="30" cy="30" r="29" fill="url(#icon-gradient)" />
  <path d="M20,20 L25,25 M35,35 L40,40 M30,20 C25,20 20,25 20,30 C20,35 25,40 30,40 C35,40 40,35 40,30 C40,25 35,20 30,20 Z" stroke="white" stroke-width="3" fill="none" />
  <circle cx="30" cy="30" r="5" fill="white" />
</svg>