/**
 * TechSupport Pro - Blog functionality
 * With multi-language support
 */

// Blog functionality with language support
const blogFunctions = {
  // Store the current language for blog-specific elements
  currentLang: 'nl',
  
  // Filter categories
  filterCategories: {
    'nl': ['Alle', 'Cybersecurity', 'Tech Tips', 'Handleidingen', 'Bedrijfsnieuws'],
    'en': ['All', 'Cybersecurity', 'Tech Tips', 'Guides', 'Company News'],
    'fr': ['Tous', 'Cybersécurité', 'Conseils Tech', 'Tutoriels', 'Actualités de l\'entreprise'],
    'pt': ['Todos', 'Cibersegurança', 'Dicas de Tecnologia', '<PERSON><PERSON><PERSON>', 'Notícias da Empresa']
  },
  
  // Blog post data with localized content
  blogPosts: {
    'nl': [
      {
        id: 'post-1',
        title: '5 Essentiële Stappen om uw Bedrijf te Beschermen tegen Ransomware',
        excerpt: 'Ransomware blijft een van de grootste bedreigingen voor bedrijven. Leer hoe u uw organisatie effectief kunt beschermen.',
        date: '15 mei 2023',
        author: '<PERSON>',
        category: 'Cybersecurity',
        readingTime: '8 min',
        image: 'images/blog/ransomware-protection.jpg',
        url: 'blog/ransomware-protection.html'
      },
      {
        id: 'post-2',
        title: '10 Windows 11 Tips om Sneller te Werken',
        excerpt: 'Ontdek hoe u uw productiviteit kunt verhogen met deze praktische Windows 11 sneltoetsen en instellingen.',
        date: '28 april 2023',
        author: 'Thomas Vermeulen',
        category: 'Tech Tips',
        readingTime: '5 min',
        image: 'images/blog/windows-11-tips.jpg',
        url: 'blog/sneller-werken-windows-11.html'
      },
      {
        id: 'post-3',
        title: 'TechSupport Pro Opent Nieuw Kantoor in Amsterdam',
        excerpt: 'We zijn verheugd om de opening van ons nieuwe hoofdkantoor in Amsterdam aan te kondigen, waarmee we onze capaciteit vergroten.',
        date: '10 april 2023',
        author: 'Team TechSupport Pro',
        category: 'Bedrijfsnieuws',
        readingTime: '3 min',
        image: 'images/blog/new-office.jpg',
        url: 'blog/nieuw-kantoor-amsterdam.html'
      }
    ],
    'en': [
      {
        id: 'post-1',
        title: '5 Essential Steps to Protect Your Business from Ransomware',
        excerpt: 'Ransomware remains one of the biggest threats to businesses. Learn how to protect your organization effectively.',
        date: 'May 15, 2023',
        author: 'Erik Janssen',
        category: 'Cybersecurity',
        readingTime: '8 min',
        image: 'images/blog/ransomware-protection.jpg',
        url: 'blog/ransomware-protection.html'
      },
      {
        id: 'post-2',
        title: '10 Windows 11 Tips to Work Faster',
        excerpt: 'Discover how to increase your productivity with these practical Windows 11 shortcuts and settings.',
        date: 'April 28, 2023',
        author: 'Thomas Vermeulen',
        category: 'Tech Tips',
        readingTime: '5 min',
        image: 'images/blog/windows-11-tips.jpg',
        url: 'blog/sneller-werken-windows-11.html'
      },
      {
        id: 'post-3',
        title: 'TechSupport Pro Opens New Office in Amsterdam',
        excerpt: 'We are pleased to announce the opening of our new headquarters in Amsterdam, expanding our capacity.',
        date: 'April 10, 2023',
        author: 'Team TechSupport Pro',
        category: 'Company News',
        readingTime: '3 min',
        image: 'images/blog/new-office.jpg',
        url: 'blog/nieuw-kantoor-amsterdam.html'
      }
    ],
    'fr': [
      {
        id: 'post-1',
        title: '5 Étapes essentielles pour protéger votre entreprise contre les ransomwares',
        excerpt: 'Les ransomwares restent l\'une des plus grandes menaces pour les entreprises. Apprenez à protéger efficacement votre organisation.',
        date: '15 mai 2023',
        author: 'Erik Janssen',
        category: 'Cybersécurité',
        readingTime: '8 min',
        image: 'images/blog/ransomware-protection.jpg',
        url: 'blog/ransomware-protection.html'
      },
      {
        id: 'post-2',
        title: '10 Astuces Windows 11 pour travailler plus vite',
        excerpt: 'Découvrez comment augmenter votre productivité avec ces raccourcis et paramètres Windows 11 pratiques.',
        date: '28 avril 2023',
        author: 'Thomas Vermeulen',
        category: 'Conseils Tech',
        readingTime: '5 min',
        image: 'images/blog/windows-11-tips.jpg',
        url: 'blog/sneller-werken-windows-11.html'
      },
      {
        id: 'post-3',
        title: 'TechSupport Pro ouvre un nouveau bureau à Amsterdam',
        excerpt: 'Nous sommes heureux d\'annoncer l\'ouverture de notre nouveau siège à Amsterdam, augmentant notre capacité.',
        date: '10 avril 2023',
        author: 'Équipe TechSupport Pro',
        category: 'Actualités de l\'entreprise',
        readingTime: '3 min',
        image: 'images/blog/new-office.jpg',
        url: 'blog/nieuw-kantoor-amsterdam.html'
      }
    ],
    'pt': [
      {
        id: 'post-1',
        title: '5 Passos Essenciais para Proteger Sua Empresa Contra Ransomware',
        excerpt: 'O ransomware continua sendo uma das maiores ameaças para as empresas. Aprenda como proteger sua organização de forma eficaz.',
        date: '15 de maio de 2023',
        author: 'Erik Janssen',
        category: 'Cibersegurança',
        readingTime: '8 min',
        image: 'images/blog/ransomware-protection.jpg',
        url: 'blog/ransomware-protection.html'
      },
      {
        id: 'post-2',
        title: '10 Dicas do Windows 11 para Trabalhar Mais Rápido',
        excerpt: 'Descubra como aumentar sua produtividade com estes atalhos e configurações práticas do Windows 11.',
        date: '28 de abril de 2023',
        author: 'Thomas Vermeulen',
        category: 'Dicas de Tecnologia',
        readingTime: '5 min',
        image: 'images/blog/windows-11-tips.jpg',
        url: 'blog/sneller-werken-windows-11.html'
      },
      {
        id: 'post-3',
        title: 'TechSupport Pro Abre Novo Escritório em Amsterdã',
        excerpt: 'Temos o prazer de anunciar a abertura da nossa nova sede em Amsterdã, expandindo nossa capacidade.',
        date: '10 de abril de 2023',
        author: 'Equipe TechSupport Pro',
        category: 'Notícias da Empresa',
        readingTime: '3 min',
        image: 'images/blog/new-office.jpg',
        url: 'blog/nieuw-kantoor-amsterdam.html'
      }
    ]
  },
  
  // UI text localization
  uiText: {
    'nl': {
      readMore: 'Lees verder',
      loadMore: 'Meer laden',
      searchPlaceholder: 'Zoek in blog...',
      searchButton: 'Zoeken',
      noResults: 'Geen resultaten gevonden voor uw zoekopdracht.',
      byAuthor: 'door',
      newsletterTitle: 'Inschrijven voor onze Nieuwsbrief',
      newsletterText: 'Ontvang de nieuwste IT-tips direct in uw inbox.',
      emailPlaceholder: 'Uw e-mailadres',
      subscribe: 'Inschrijven',
      privacyText: 'We respecteren uw privacy en sturen nooit spam.',
      readingTime: 'min leestijd',
      relatedPosts: 'Gerelateerde artikelen',
      backToBlog: 'Terug naar blog'
    },
    'en': {
      readMore: 'Read more',
      loadMore: 'Load more',
      searchPlaceholder: 'Search blog...',
      searchButton: 'Search',
      noResults: 'No results found for your search.',
      byAuthor: 'by',
      newsletterTitle: 'Subscribe to our Newsletter',
      newsletterText: 'Get the latest IT tips directly to your inbox.',
      emailPlaceholder: 'Your email address',
      subscribe: 'Subscribe',
      privacyText: 'We respect your privacy and never send spam.',
      readingTime: 'min read',
      relatedPosts: 'Related articles',
      backToBlog: 'Back to blog'
    },
    'fr': {
      readMore: 'Lire la suite',
      loadMore: 'Charger plus',
      searchPlaceholder: 'Rechercher dans le blog...',
      searchButton: 'Rechercher',
      noResults: 'Aucun résultat trouvé pour votre recherche.',
      byAuthor: 'par',
      newsletterTitle: 'Abonnez-vous à notre Newsletter',
      newsletterText: 'Recevez les derniers conseils informatiques directement dans votre boîte de réception.',
      emailPlaceholder: 'Votre adresse e-mail',
      subscribe: 'S\'abonner',
      privacyText: 'Nous respectons votre vie privée et n\'envoyons jamais de spam.',
      readingTime: 'min de lecture',
      relatedPosts: 'Articles connexes',
      backToBlog: 'Retour au blog'
    },
    'pt': {
      readMore: 'Leia mais',
      loadMore: 'Carregar mais',
      searchPlaceholder: 'Pesquisar no blog...',
      searchButton: 'Pesquisar',
      noResults: 'Nenhum resultado encontrado para sua pesquisa.',
      byAuthor: 'por',
      newsletterTitle: 'Inscreva-se em nossa Newsletter',
      newsletterText: 'Receba as últimas dicas de TI diretamente em sua caixa de entrada.',
      emailPlaceholder: 'Seu endereço de email',
      subscribe: 'Inscrever-se',
      privacyText: 'Respeitamos sua privacidade e nunca enviamos spam.',
      readingTime: 'min de leitura',
      relatedPosts: 'Artigos relacionados',
      backToBlog: 'Voltar ao blog'
    }
  },
  
  /**
   * Initialize blog functionality
   */
  init: function() {
    // Use current language from i18n system if available
    if (window.i18n && window.i18n.currentLang) {
      this.currentLang = window.i18n.currentLang;
    }
    
    // Set up language change listener
    window.addEventListener('languageChanged', (event) => {
      this.currentLang = event.detail.language;
      this.updateBlogPage();
    });
    
    // Initialize blog functionality based on current page
    if (document.querySelector('.blog-posts-grid')) {
      this.setupBlogListing();
    }
    
    if (document.querySelector('.blog-post-content')) {
      this.setupBlogPost();
    }
  },
  
  /**
   * Setup the blog listing page
   */
  setupBlogListing: function() {
    // Set up category filters
    this.setupCategoryFilters();
    
    // Set up search functionality
    this.setupSearch();
    
    // Set up load more functionality
    this.setupLoadMore();
    
    // Update UI text for current language
    this.updateUIText();
    
    // Render initial blog posts
    this.renderBlogPosts();
  },
  
  /**
   * Setup blog post page
   */
  setupBlogPost: function() {
    // Update UI text for current language
    this.updateUIText();
    
    // Update related posts based on current language
    this.updateRelatedPosts();
    
    // Update share buttons text
    this.updateShareButtons();
  },
  
  /**
   * Update all blog page content when language changes
   */
  updateBlogPage: function() {
    // Update category filters for current language
    this.updateCategoryFilters();
    
    // Update UI text for current language
    this.updateUIText();
    
    // Re-render blog posts in current language
    if (document.querySelector('.blog-posts-grid')) {
      this.renderBlogPosts();
    }
    
    // Update related posts if on a blog post page
    if (document.querySelector('.blog-post-content')) {
      this.updateRelatedPosts();
      this.updateShareButtons();
    }
  },
  
  /**
   * Set up category filters
   */
  setupCategoryFilters: function() {
    const filterContainer = document.querySelector('.blog-categories');
    if (!filterContainer) return;
    
    // Create filters from the localized categories
    const categories = this.filterCategories[this.currentLang];
    
    // Clear any existing filters
    filterContainer.innerHTML = '';
    
    // Add filter buttons
    categories.forEach((category, index) => {
      const button = document.createElement('button');
      button.textContent = category;
      button.classList.add('blog-category-btn');
      
      // Set first category (All) as active by default
      if (index === 0) button.classList.add('active');
      
      // Add click event
      button.addEventListener('click', () => {
        // Update active button
        document.querySelectorAll('.blog-category-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // Filter posts
        this.filterPostsByCategory(category);
      });
      
      filterContainer.appendChild(button);
    });
  },
  
  /**
   * Update category filters when language changes
   */
  updateCategoryFilters: function() {
    const filterContainer = document.querySelector('.blog-categories');
    if (!filterContainer) return;
    
    // Get active category index to preserve selection
    let activeIndex = 0;
    const activeButton = filterContainer.querySelector('.blog-category-btn.active');
    if (activeButton) {
      const buttons = Array.from(filterContainer.querySelectorAll('.blog-category-btn'));
      activeIndex = buttons.indexOf(activeButton);
      if (activeIndex < 0) activeIndex = 0;
    }
    
    // Create updated filters
    const categories = this.filterCategories[this.currentLang];
    
    // Clear existing filters
    filterContainer.innerHTML = '';
    
    // Add updated filter buttons
    categories.forEach((category, index) => {
      const button = document.createElement('button');
      button.textContent = category;
      button.classList.add('blog-category-btn');
      
      // Set active category
      if (index === activeIndex) button.classList.add('active');
      
      // Add click event
      button.addEventListener('click', () => {
        // Update active button
        document.querySelectorAll('.blog-category-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // Filter posts
        this.filterPostsByCategory(category);
      });
      
      filterContainer.appendChild(button);
    });
  },
  
  /**
   * Filter posts by category
   */
  filterPostsByCategory: function(category) {
    const posts = document.querySelectorAll('.blog-post-card');
    const allCategories = this.filterCategories[this.currentLang][0]; // "All" in current language
    
    posts.forEach(post => {
      const postCategory = post.getAttribute('data-category');
      
      if (category === allCategories || postCategory === category) {
        post.style.display = '';
      } else {
        post.style.display = 'none';
      }
    });
  },
  
  /**
   * Set up search functionality
   */
  setupSearch: function() {
    const searchForm = document.querySelector('.blog-search');
    const searchInput = document.querySelector('.blog-search-input');
    
    if (!searchForm || !searchInput) return;
    
    // Update placeholder text
    searchInput.placeholder = this.uiText[this.currentLang].searchPlaceholder;
    
    // Search form submission
    searchForm.addEventListener('submit', (e) => {
      e.preventDefault();
      const searchTerm = searchInput.value.toLowerCase().trim();
      
      if (searchTerm) {
        this.searchPosts(searchTerm);
      } else {
        // Reset to show all posts when search is cleared
        this.resetFilters();
      }
    });
  },
  
  /**
   * Search posts with the given term
   */
  searchPosts: function(searchTerm) {
    const posts = document.querySelectorAll('.blog-post-card');
    const resultsContainer = document.querySelector('.blog-posts-grid');
    let resultsFound = false;
    
    // Reset category filter visual state
    document.querySelectorAll('.blog-category-btn').forEach((button, index) => {
      button.classList.toggle('active', index === 0);
    });
    
    posts.forEach(post => {
      const postTitle = post.querySelector('.blog-post-title').textContent.toLowerCase();
      const postExcerpt = post.querySelector('.blog-post-excerpt').textContent.toLowerCase();
      
      if (postTitle.includes(searchTerm) || postExcerpt.includes(searchTerm)) {
        post.style.display = '';
        resultsFound = true;
      } else {
        post.style.display = 'none';
      }
    });
    
    // Show "no results" message if needed
    const noResultsMsg = document.querySelector('.blog-no-results');
    if (!noResultsMsg && !resultsFound) {
      const message = document.createElement('p');
      message.textContent = this.uiText[this.currentLang].noResults;
      message.classList.add('blog-no-results');
      resultsContainer.appendChild(message);
    } else if (noResultsMsg && resultsFound) {
      noResultsMsg.remove();
    } else if (noResultsMsg && !resultsFound) {
      noResultsMsg.textContent = this.uiText[this.currentLang].noResults;
    }
  },
  
  /**
   * Reset all filters to show all posts
   */
  resetFilters: function() {
    // Reset category filter
    document.querySelectorAll('.blog-category-btn').forEach((button, index) => {
      button.classList.toggle('active', index === 0);
    });
    
    // Show all posts
    document.querySelectorAll('.blog-post-card').forEach(post => {
      post.style.display = '';
    });
    
    // Remove any "no results" message
    const noResultsMsg = document.querySelector('.blog-no-results');
    if (noResultsMsg) noResultsMsg.remove();
  },
  
  /**
   * Set up "load more" functionality
   */
  setupLoadMore: function() {
    const loadMoreButton = document.querySelector('.blog-load-more');
    if (!loadMoreButton) return;
    
    // Update button text
    loadMoreButton.textContent = this.uiText[this.currentLang].loadMore;
    
    // Initially hide the button if there are fewer posts than the limit
    const posts = document.querySelectorAll('.blog-post-card');
    if (posts.length <= 6) { // Assuming 6 is the initial limit
      loadMoreButton.style.display = 'none';
    }
    
    // Add click event
    loadMoreButton.addEventListener('click', () => {
      // Show 3 more posts
      let shown = 0;
      const hiddenPosts = Array.from(document.querySelectorAll('.blog-post-card[style="display: none;"]'))
        .filter(post => !post.classList.contains('filtered-out'));
      
      for (let i = 0; i < hiddenPosts.length; i++) {
        hiddenPosts[i].style.display = '';
        shown++;
        if (shown >= 3) break;
      }
      
      // Hide the button if no more posts to show
      if (shown < 3) {
        loadMoreButton.style.display = 'none';
      }
    });
  },
  
  /**
   * Update UI text elements based on current language
   */
  updateUIText: function() {
    // Update newsletter section
    const newsletterTitle = document.querySelector('.blog-newsletter-title');
    if (newsletterTitle) {
      newsletterTitle.textContent = this.uiText[this.currentLang].newsletterTitle;
    }
    
    const newsletterText = document.querySelector('.blog-newsletter-text');
    if (newsletterText) {
      newsletterText.textContent = this.uiText[this.currentLang].newsletterText;
    }
    
    const emailInput = document.querySelector('.blog-newsletter-input');
    if (emailInput) {
      emailInput.placeholder = this.uiText[this.currentLang].emailPlaceholder;
    }
    
    const subscribeButton = document.querySelector('.blog-newsletter-submit');
    if (subscribeButton) {
      subscribeButton.textContent = this.uiText[this.currentLang].subscribe;
    }
    
    const privacyText = document.querySelector('.blog-newsletter-privacy');
    if (privacyText) {
      privacyText.textContent = this.uiText[this.currentLang].privacyText;
    }
    
    // Update search elements
    const searchInput = document.querySelector('.blog-search-input');
    if (searchInput) {
      searchInput.placeholder = this.uiText[this.currentLang].searchPlaceholder;
    }
    
    const searchButton = document.querySelector('.blog-search-button');
    if (searchButton) {
      searchButton.textContent = this.uiText[this.currentLang].searchButton;
    }
    
    // Update load more button
    const loadMoreButton = document.querySelector('.blog-load-more');
    if (loadMoreButton) {
      loadMoreButton.textContent = this.uiText[this.currentLang].loadMore;
    }
    
    // Update blog post page elements if on a post page
    if (document.querySelector('.blog-post-content')) {
      const relatedPostsTitle = document.querySelector('.related-posts-title');
      if (relatedPostsTitle) {
        relatedPostsTitle.textContent = this.uiText[this.currentLang].relatedPosts;
      }
      
      const backToBlogs = document.querySelector('.back-to-blog');
      if (backToBlogs) {
        backToBlogs.textContent = this.uiText[this.currentLang].backToBlog;
      }
    }
  },
  
  /**
   * Render blog posts with the current language content
   */
  renderBlogPosts: function() {
    const postsContainer = document.querySelector('.blog-posts-grid');
    if (!postsContainer) return;
    
    // Clear existing posts
    postsContainer.innerHTML = '';
    
    // Get posts for current language
    const posts = this.blogPosts[this.currentLang] || this.blogPosts['en']; // Fallback to English
    
    // Create and add post cards
    posts.forEach(post => {
      const postCard = document.createElement('article');
      postCard.className = 'blog-post-card';
      postCard.setAttribute('data-category', post.category);
      postCard.setAttribute('id', post.id);
      
      postCard.innerHTML = `
        <div class="blog-post-image">
          <a href="${post.url}">
            <img src="${post.image}" alt="${post.title}" width="400" height="225">
          </a>
          <span class="blog-post-category">${post.category}</span>
        </div>
        <div class="blog-post-content">
          <h3 class="blog-post-title"><a href="${post.url}">${post.title}</a></h3>
          <p class="blog-post-meta">
            <span class="blog-post-date">${post.date}</span> | 
            <span class="blog-post-author">${this.uiText[this.currentLang].byAuthor} ${post.author}</span> | 
            <span class="blog-post-reading-time">${post.readingTime} ${this.uiText[this.currentLang].readingTime}</span>
          </p>
          <p class="blog-post-excerpt">${post.excerpt}</p>
          <a href="${post.url}" class="blog-post-link">${this.uiText[this.currentLang].readMore}</a>
        </div>
      `;
      
      postsContainer.appendChild(postCard);
    });
  },
  
  /**
   * Update related posts on blog post page
   */
  updateRelatedPosts: function() {
    const relatedContainer = document.querySelector('.related-posts-list');
    if (!relatedContainer) return;
    
    // Clear existing related posts
    relatedContainer.innerHTML = '';
    
    // Get current post ID (from URL or attribute)
    const currentPath = window.location.pathname;
    const currentPostId = this.getCurrentPostIdFromPath(currentPath);
    
    // Get posts for current language
    const posts = this.blogPosts[this.currentLang] || this.blogPosts['en']; // Fallback to English
    
    // Filter out current post and get 2-3 related posts
    const relatedPosts = posts
      .filter(post => post.id !== currentPostId)
      .slice(0, 3);
    
    // Add related posts
    relatedPosts.forEach(post => {
      const relatedPost = document.createElement('div');
      relatedPost.className = 'related-post-card';
      
      relatedPost.innerHTML = `
        <a href="${post.url}" class="related-post-link">
          <img src="${post.image}" alt="${post.title}" class="related-post-image" width="150" height="100">
          <div class="related-post-info">
            <h4 class="related-post-title">${post.title}</h4>
            <p class="related-post-meta">${post.date} | ${post.readingTime} ${this.uiText[this.currentLang].readingTime}</p>
          </div>
        </a>
      `;
      
      relatedContainer.appendChild(relatedPost);
    });
  },
  
  /**
   * Update share buttons text
   */
  updateShareButtons: function() {
    const shareText = {
      'nl': 'Delen op',
      'en': 'Share on',
      'fr': 'Partager sur',
      'pt': 'Compartilhar no'
    };
    
    // Update social share buttons
    document.querySelectorAll('.share-button').forEach(button => {
      const platform = button.getAttribute('data-platform');
      if (platform) {
        button.setAttribute('aria-label', `${shareText[this.currentLang]} ${platform}`);
      }
    });
  },
  
  /**
   * Helper to get post ID from URL path
   */
  getCurrentPostIdFromPath: function(path) {
    // Extract filename from path
    const filename = path.split('/').pop().replace('.html', '');
    
    // Map filenames to post IDs based on known structure
    const filenameToId = {
      'ransomware-protection': 'post-1',
      'sneller-werken-windows-11': 'post-2',
      'nieuw-kantoor-amsterdam': 'post-3'
    };
    
    return filenameToId[filename] || '';
  }
};

// Initialize blog functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  blogFunctions.init();
});

// Export for reuse in other scripts
window.blogFunctions = blogFunctions;