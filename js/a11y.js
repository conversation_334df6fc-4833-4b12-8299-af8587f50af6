/**
 * Accessibility Enhancements
 * This file contains JavaScript functions to improve site accessibility
 */

class AccessibilityManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupSkipLinks();
        this.setupFocusManagement();
        this.setupFormValidation();
        this.setupScreenReaderAnnouncements();
        this.setupAccessibleTooltips();
        this.setupKeyboardNavigation();
        this.setupAccordions();
        this.setupLiveRegions();
    }

    setupSkipLinks() {
        // Add skip links to main landmarks
        const skipContainer = document.createElement('div');
        skipContainer.className = 'skip-link-list';
        
        const landmarks = [
            { id: 'main-content', label: 'Skip to main content' },
            { id: 'nav', label: 'Skip to navigation' },
            { id: 'footer', label: 'Skip to footer' }
        ];
        
        landmarks.forEach(landmark => {
            if (document.getElementById(landmark.id)) {
                const link = document.createElement('a');
                link.href = `#${landmark.id}`;
                link.className = 'skip-link';
                link.textContent = landmark.label;
                skipContainer.appendChild(link);
            }
        });
        
        if (skipContainer.children.length > 0) {
            document.body.insertBefore(skipContainer, document.body.firstChild);
        }
    }

    setupFocusManagement() {
        // Track focus for modal dialogs
        document.querySelectorAll('.modal').forEach(modal => {
            if (modal.getAttribute('aria-modal') === 'true') {
                const focusableElements = modal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                
                if (focusableElements.length > 0) {
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    
                    modal.addEventListener('keydown', e => {
                        if (e.key === 'Tab') {
                            if (e.shiftKey && document.activeElement === firstElement) {
                                e.preventDefault();
                                lastElement.focus();
                            } else if (!e.shiftKey && document.activeElement === lastElement) {
                                e.preventDefault();
                                firstElement.focus();
                            }
                        }
                    });
                }
            }
        });
    }

    setupFormValidation() {
        // Enhanced form validation with accessible error messages
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', event => {
                const isValid = this.validateForm(form);
                
                if (!isValid) {
                    event.preventDefault();
                    this.announceToScreenReader('Form contains errors. Please review and correct.');
                    
                    // Focus on first invalid field
                    const firstInvalidField = form.querySelector('.form-error');
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                    }
                }
            });
            
            // Live validation on blur
            form.querySelectorAll('input, select, textarea').forEach(field => {
                field.addEventListener('blur', () => {
                    this.validateField(field);
                });
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        
        form.querySelectorAll('input, select, textarea').forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    validateField(field) {
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        field.classList.remove('form-error');
        field.removeAttribute('aria-invalid');
        
        let isValid = true;
        let errorMessage = '';
        
        // Required field validation
        if (field.hasAttribute('required') && !field.value.trim()) {
            isValid = false;
            errorMessage = 'Dit veld is verplicht';
        } 
        // Email validation
        else if (field.type === 'email' && field.value && !this.isValidEmail(field.value)) {
            isValid = false;
            errorMessage = 'Voer een geldig e-mailadres in';
        }
        // Phone validation
        else if (field.type === 'tel' && field.value && !this.isValidPhone(field.value)) {
            isValid = false;
            errorMessage = 'Voer een geldig telefoonnummer in';
        }
        // Min/max length validation
        else if (field.hasAttribute('minlength') && field.value.length < parseInt(field.getAttribute('minlength'))) {
            isValid = false;
            errorMessage = `Minimaal ${field.getAttribute('minlength')} karakters vereist`;
        }
        
        if (!isValid) {
            field.classList.add('form-error');
            field.setAttribute('aria-invalid', 'true');
            
            const errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.id = `${field.id}-error`;
            errorElement.textContent = errorMessage;
            
            field.setAttribute('aria-describedby', `${field.id}-error`);
            field.parentNode.appendChild(errorElement);
        }
        
        return isValid;
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    isValidPhone(phone) {
        return /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(phone);
    }

    setupScreenReaderAnnouncements() {
        // Add a live region for screen reader announcements
        const announcer = document.createElement('div');
        announcer.className = 'sr-announcer';
        announcer.setAttribute('aria-live', 'polite');
        document.body.appendChild(announcer);
        
        this.announcer = announcer;
    }

    announceToScreenReader(message) {
        if (this.announcer) {
            this.announcer.textContent = '';
            // Use setTimeout to ensure screen readers recognize the change
            setTimeout(() => {
                this.announcer.textContent = message;
            }, 50);
        }
    }

    setupAccessibleTooltips() {
        // Make tooltips keyboard accessible
        document.querySelectorAll('.tooltip').forEach(tooltip => {
            tooltip.setAttribute('tabindex', '0');
            
            tooltip.addEventListener('keydown', e => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    tooltip.focus();
                }
            });
        });
    }

    setupKeyboardNavigation() {
        // Enhance keyboard navigation for custom components
        this.setupTabNav();
        this.setupDropdowns();
    }

    setupTabNav() {
        // Setup keyboard navigation for tab interfaces
        document.querySelectorAll('[role="tablist"]').forEach(tablist => {
            const tabs = tablist.querySelectorAll('[role="tab"]');
            
            let tabFocus = 0;
            
            tablist.addEventListener('keydown', e => {
                if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
                    tabs[tabFocus].setAttribute('tabindex', '-1');
                    
                    if (e.key === 'ArrowRight') {
                        tabFocus++;
                        if (tabFocus >= tabs.length) {
                            tabFocus = 0;
                        }
                    } else {
                        tabFocus--;
                        if (tabFocus < 0) {
                            tabFocus = tabs.length - 1;
                        }
                    }
                    
                    tabs[tabFocus].setAttribute('tabindex', '0');
                    tabs[tabFocus].focus();
                }
            });
        });
    }

    setupDropdowns() {
        // Make dropdown menus accessible
        document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
            toggle.addEventListener('keydown', e => {
                const menu = document.getElementById(toggle.getAttribute('aria-controls'));
                
                if (!menu) return;
                
                if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown') {
                    e.preventDefault();
                    
                    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
                    toggle.setAttribute('aria-expanded', !isExpanded);
                    menu.classList.toggle('show');
                    
                    if (!isExpanded) {
                        const firstItem = menu.querySelector('a, button');
                        if (firstItem) {
                            firstItem.focus();
                        }
                    }
                }
            });
        });
    }

    setupAccordions() {
        // Setup accessible accordions
        document.querySelectorAll('.a11y-accordion-trigger').forEach(trigger => {
            const panelId = trigger.getAttribute('aria-controls');
            const panel = document.getElementById(panelId);
            
            if (!panel) return;
            
            trigger.addEventListener('click', () => {
                const isExpanded = trigger.getAttribute('aria-expanded') === 'true';
                trigger.setAttribute('aria-expanded', !isExpanded);
                panel.setAttribute('aria-hidden', isExpanded);
            });
            
            // Setup keyboard interaction
            trigger.addEventListener('keydown', e => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    trigger.click();
                }
            });
        });
    }

    setupLiveRegions() {
        // Initialize live regions for dynamic content
        document.querySelectorAll('[aria-live]').forEach(region => {
            // Ensure regions are properly set up
            if (!region.hasAttribute('aria-atomic')) {
                region.setAttribute('aria-atomic', 'true');
            }
        });
    }
}

// Initialize accessibility manager
const a11y = new AccessibilityManager();

export default a11y;
