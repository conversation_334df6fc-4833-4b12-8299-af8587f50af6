/**
 * TechSupport Pro - Language Routing System
 * 
 * This script enables language-specific URLs (e.g., /en/ or /fr/) and redirects
 * users to their preferred language version based on saved preferences or browser settings.
 */

(function() {
  // Configuration
  const supportedLanguages = ['nl', 'en', 'fr', 'pt'];
  const defaultLanguage = 'nl';
  
  /**
   * Extract language from URL path
   * Format: /[lang]/path/to/page.html
   */
  function getLanguageFromPath() {
    const path = window.location.pathname;
    const segments = path.split('/').filter(Boolean);
    
    // Check if first segment is a supported language code
    if (segments.length > 0 && supportedLanguages.includes(segments[0])) {
      return segments[0];
    }
    
    return null;
  }
  
  /**
   * Get preferred language from local storage or browser settings
   */
  function getPreferredLanguage() {
    // Check for stored preference
    const storedLanguage = localStorage.getItem('techsupport_language');
    if (storedLanguage && supportedLanguages.includes(storedLanguage)) {
      return storedLanguage;
    }
    
    // Try to detect from browser settings
    const browserLang = navigator.language || navigator.userLanguage;
    const shortLang = browserLang.split('-')[0];
    
    if (supportedLanguages.includes(shortLang)) {
      return shortLang;
    }
    
    // Fall back to default
    return defaultLanguage;
  }
  
  /**
   * Generate URL for specified language
   */
  function getLanguageUrl(lang) {
    const currentPath = window.location.pathname;
    const currentLanguage = getLanguageFromPath();
    
    if (currentLanguage) {
      // Replace language code in existing path
      return currentPath.replace(`/${currentLanguage}/`, `/${lang}/`);
    } else {
      // Add language code to path
      if (currentPath === '/' || currentPath === '/index.html') {
        return `/${lang}/`;
      } else {
        return `/${lang}${currentPath}`;
      }
    }
  }
  
  /**
   * Handle language routing
   */
  function routeByLanguage() {
    // Skip if this is a direct file request (not HTML)
    if (location.pathname.match(/\.(css|js|jpg|png|svg|webp|ico)$/i)) {
      return;
    }
    
    const urlLanguage = getLanguageFromPath();
    const preferredLanguage = getPreferredLanguage();
    
    // If URL has no language code and we're on the root page, redirect to preferred language
    if (!urlLanguage && (location.pathname === '/' || location.pathname === '/index.html')) {
      // Only redirect if preferred language is not the default
      if (preferredLanguage !== defaultLanguage) {
        window.location.href = getLanguageUrl(preferredLanguage);
        return;
      }
    }
    
    // If URL has language code, update stored preference
    if (urlLanguage) {
      localStorage.setItem('techsupport_language', urlLanguage);
      
      // If i18n system is available, update it
      if (window.i18n) {
        window.i18n.setLanguage(urlLanguage);
      }
    }
  }
  
  // Wait for DOM to be ready
  document.addEventListener('DOMContentLoaded', function() {
    routeByLanguage();
    
    // Update language toggle button to show current language
    const currentLang = getLanguageFromPath() || getPreferredLanguage();
    const langToggle = document.getElementById('language-toggle');
    if (langToggle) {
      const currentLangSpan = langToggle.querySelector('.current-language');
      if (currentLangSpan) {
        currentLangSpan.textContent = currentLang.toUpperCase();
      }
    }
    
    // Update language dropdown links to go to language-specific URLs
    const langOptions = document.querySelectorAll('.language-option');
    if (langOptions.length) {
      langOptions.forEach(option => {
        const lang = option.getAttribute('data-lang');
        
        // Update the click handler to navigate to URL rather than just change language
        option.addEventListener('click', function(e) {
          e.preventDefault();
          
          // Store the preference
          localStorage.setItem('techsupport_language', lang);
          
          // Navigate to URL for this language
          window.location.href = getLanguageUrl(lang);
        });
      });
    }
  });
})();