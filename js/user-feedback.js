/**
 * User Feedback System
 * This script collects and analyzes user feedback to improve the website experience
 */

class UserFeedbackSystem {
    constructor() {
        this.feedbackKey = 'tsp_user_feedback';
        this.feedbackDelay = 45000; // 45 seconds
        this.minPageViews = 2;
        
        this.init();
    }
    
    init() {
        // Check if we should show feedback form
        this.checkAndShowFeedback();
        
        // Add quick reaction buttons to appropriate pages
        this.addReactionButtons();
    }
    
    checkAndShowFeedback() {
        // Don't show feedback on mobile (smaller screen)
        if (window.innerWidth < 768) {
            return;
        }
        
        // Avoid showing feedback repeatedly
        if (this.hasGivenFeedback()) {
            return;
        }
        
        // Get page view count from localStorage
        const pageViews = this.getPageViews();
        
        if (pageViews >= this.minPageViews) {
            // Show feedback after delay
            setTimeout(() => {
                this.showFeedbackWidget();
            }, this.feedbackDelay);
        }
    }
    
    hasGivenFeedback() {
        const lastFeedback = localStorage.getItem(this.feedbackKey);
        
        if (!lastFeedback) {
            return false;
        }
        
        try {
            const feedbackData = JSON.parse(lastFeedback);
            // Check if feedback was given in the last 7 days
            const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
            return feedbackData.timestamp > sevenDaysAgo;
        } catch (e) {
            return false;
        }
    }
    
    getPageViews() {
        const pageViews = sessionStorage.getItem('page_views') || 0;
        const newPageViews = parseInt(pageViews) + 1;
        sessionStorage.setItem('page_views', newPageViews);
        return newPageViews;
    }
    
    showFeedbackWidget() {
        // Create feedback widget
        const widget = document.createElement('div');
        widget.className = 'feedback-widget';
        widget.setAttribute('aria-labelledby', 'feedback-title');
        
        widget.innerHTML = `
            <button class="feedback-close" aria-label="Sluiten">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
            
            <h3 id="feedback-title">Uw mening telt!</h3>
            <p>Hoe was uw ervaring op onze website?</p>
            
            <div class="feedback-options">
                <button class="feedback-option" data-value="positive">
                    <i class="far fa-smile" aria-hidden="true"></i>
                    <span>Goed</span>
                </button>
                <button class="feedback-option" data-value="neutral">
                    <i class="far fa-meh" aria-hidden="true"></i>
                    <span>Neutraal</span>
                </button>
                <button class="feedback-option" data-value="negative">
                    <i class="far fa-frown" aria-hidden="true"></i>
                    <span>Kan beter</span>
                </button>
            </div>
            
            <div class="feedback-form" style="display: none;">
                <textarea class="feedback-comment" 
                          placeholder="Vertel ons meer over uw ervaring (optioneel)" 
                          aria-label="Vertel ons meer over uw ervaring (optioneel)"></textarea>
                          
                <button class="feedback-submit btn btn-primary">Versturen</button>
            </div>
        `;
        
        // Add to DOM
        document.body.appendChild(widget);
        
        // Add event listeners
        widget.querySelector('.feedback-close').addEventListener('click', () => {
            this.closeFeedbackWidget(widget);
        });
        
        widget.querySelectorAll('.feedback-option').forEach(option => {
            option.addEventListener('click', () => {
                this.handleFeedbackSelection(widget, option);
            });
        });
        
        widget.querySelector('.feedback-submit').addEventListener('click', () => {
            this.submitFeedback(widget);
        });
        
        // Animation to show widget
        setTimeout(() => {
            widget.classList.add('visible');
        }, 100);
    }
    
    handleFeedbackSelection(widget, option) {
        // Remove selected class from all options
        widget.querySelectorAll('.feedback-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        // Add selected class to clicked option
        option.classList.add('selected');
        
        // Show comment form
        widget.querySelector('.feedback-form').style.display = 'block';
    }
    
    submitFeedback(widget) {
        const selectedOption = widget.querySelector('.feedback-option.selected');
        
        if (!selectedOption) {
            return;
        }
        
        const value = selectedOption.getAttribute('data-value');
        const comment = widget.querySelector('.feedback-comment').value;
        
        const feedback = {
            sentiment: value,
            comment: comment,
            page: window.location.pathname,
            timestamp: Date.now()
        };
        
        // Store feedback (normally would be sent to server)
        this.saveFeedback(feedback);
        
        // Show thank you message
        widget.innerHTML = `
            <h3 id="feedback-title">Bedankt voor uw feedback!</h3>
            <p>We gebruiken uw feedback om onze website te verbeteren.</p>
        `;
        
        // Close after delay
        setTimeout(() => {
            this.closeFeedbackWidget(widget);
        }, 3000);
    }
    
    closeFeedbackWidget(widget) {
        widget.classList.remove('visible');
        
        setTimeout(() => {
            if (widget.parentNode) {
                widget.parentNode.removeChild(widget);
            }
        }, 300);
    }
    
    saveFeedback(feedback) {
        // Store in localStorage
        localStorage.setItem(this.feedbackKey, JSON.stringify(feedback));
        
        // In a real implementation, we would send to server
        if (window.fetch) {
            // Disabled for demo
            /*
            fetch('/api/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(feedback)
            }).catch(err => console.error('Error sending feedback:', err));
            */
            
            console.log('Feedback submitted:', feedback);
        }
    }
    
    addReactionButtons() {
        // Add quick reaction buttons to blog posts and service pages
        const contentArticles = document.querySelectorAll('.blog-post, .service-detail');
        
        contentArticles.forEach(article => {
            // Only add if not already present
            if (article.querySelector('.reaction-buttons')) {
                return;
            }
            
            const reactionContainer = document.createElement('div');
            reactionContainer.className = 'reaction-buttons';
            reactionContainer.innerHTML = `
                <h4>Was deze informatie nuttig?</h4>
                <div class="reaction-options">
                    <button class="reaction-btn" data-reaction="helpful">
                        <i class="far fa-thumbs-up" aria-hidden="true"></i>
                        <span>Ja</span>
                    </button>
                    <button class="reaction-btn" data-reaction="not-helpful">
                        <i class="far fa-thumbs-down" aria-hidden="true"></i>
                        <span>Nee</span>
                    </button>
                </div>
                <div class="reaction-feedback" style="display: none;">
                    <textarea class="reaction-comment" 
                              placeholder="Hoe kunnen we dit verbeteren? (optioneel)" 
                              aria-label="Hoe kunnen we dit verbeteren? (optioneel)"></textarea>
                    <button class="reaction-submit btn btn-sm btn-primary">Versturen</button>
                </div>
            `;
            
            // Add after article content
            article.appendChild(reactionContainer);
            
            // Add event listeners
            reactionContainer.querySelectorAll('.reaction-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // Remove active class from all buttons
                    reactionContainer.querySelectorAll('.reaction-btn').forEach(b => {
                        b.classList.remove('active');
                    });
                    
                    // Add active class to clicked button
                    btn.classList.add('active');
                    
                    // Show feedback form for negative reactions
                    const reaction = btn.getAttribute('data-reaction');
                    const feedbackForm = reactionContainer.querySelector('.reaction-feedback');
                    
                    if (reaction === 'not-helpful') {
                        feedbackForm.style.display = 'block';
                    } else {
                        feedbackForm.style.display = 'none';
                        
                        // Submit positive reaction immediately
                        this.saveReaction({
                            reaction: reaction,
                            page: window.location.pathname,
                            articleId: article.id || null,
                            articleTitle: article.querySelector('h1')?.textContent || article.querySelector('h2')?.textContent || null,
                            timestamp: Date.now()
                        });
                        
                        // Show thank you message
                        const thankYou = document.createElement('p');
                        thankYou.className = 'reaction-thanks';
                        thankYou.textContent = 'Bedankt voor uw feedback!';
                        reactionContainer.querySelector('.reaction-options').appendChild(thankYou);
                    }
                });
            });
            
            // Reaction submit button
            const submitBtn = reactionContainer.querySelector('.reaction-submit');
            if (submitBtn) {
                submitBtn.addEventListener('click', () => {
                    const activeBtn = reactionContainer.querySelector('.reaction-btn.active');
                    if (!activeBtn) return;
                    
                    const reaction = activeBtn.getAttribute('data-reaction');
                    const comment = reactionContainer.querySelector('.reaction-comment').value;
                    
                    this.saveReaction({
                        reaction: reaction,
                        comment: comment,
                        page: window.location.pathname,
                        articleId: article.id || null,
                        articleTitle: article.querySelector('h1')?.textContent || article.querySelector('h2')?.textContent || null,
                        timestamp: Date.now()
                    });
                    
                    // Hide form and show thank you
                    reactionContainer.querySelector('.reaction-feedback').style.display = 'none';
                    const thankYou = document.createElement('p');
                    thankYou.className = 'reaction-thanks';
                    thankYou.textContent = 'Bedankt voor uw feedback!';
                    reactionContainer.querySelector('.reaction-options').appendChild(thankYou);
                });
            }
        });
    }
    
    saveReaction(reaction) {
        // In a real implementation, send to server
        console.log('Content reaction:', reaction);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userFeedback = new UserFeedbackSystem();
});
