/**
 * Accessibility Tester
 * This script checks for common accessibility issues in the DOM
 */

class AccessibilityTester {
    constructor() {
        this.issues = [];
        this.warningCount = 0;
        this.errorCount = 0;
    }
    
    runTests() {
        // Run all tests
        this.checkAltText();
        this.checkHeadingHierarchy();
        this.checkFormLabels();
        this.checkColorContrast();
        this.checkARIAAttributes();
        this.checkKeyboardAccessibility();
        
        // Return results
        return {
            issues: this.issues,
            warningCount: this.warningCount,
            errorCount: this.errorCount,
            summary: `Found ${this.errorCount} errors and ${this.warningCount} warnings`
        };
    }
    
    addIssue(type, severity, element, message, suggestion) {
        if (severity === 'error') {
            this.errorCount++;
        } else if (severity === 'warning') {
            this.warningCount++;
        }
        
        this.issues.push({
            type,
            severity,
            element: element.outerHTML,
            message,
            suggestion
        });
    }
    
    checkAltText() {
        // Find images without alt text
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('alt')) {
                this.addIssue(
                    'alt-text', 
                    'error', 
                    img, 
                    'Image missing alt text', 
                    'Add descriptive alt text to the image'
                );
            } else if (img.alt === '' && !img.getAttribute('role') === 'presentation') {
                this.addIssue(
                    'alt-text', 
                    'warning', 
                    img, 
                    'Decorative image should use role="presentation"', 
                    'Add role="presentation" to images with empty alt text'
                );
            }
        });
    }
    
    checkHeadingHierarchy() {
        // Check if headings are in order
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        let lastLevel = 0;
        
        headings.forEach(heading => {
            const level = parseInt(heading.tagName.substring(1));
            
            // Check for skipped heading levels
            if (level - lastLevel > 1 && lastLevel !== 0) {
                this.addIssue(
                    'heading-hierarchy', 
                    'error', 
                    heading, 
                    `Heading level skipped from h${lastLevel} to h${level}`, 
                    `Use h${lastLevel + 1} instead of h${level} to maintain hierarchy`
                );
            }
            
            // Check for multiple h1 tags
            if (level === 1 && lastLevel === 1) {
                this.addIssue(
                    'heading-hierarchy', 
                    'warning', 
                    heading, 
                    'Multiple h1 elements found', 
                    'Use only one h1 element per page'
                );
            }
            
            lastLevel = level;
        });
    }
    
    checkFormLabels() {
        // Check for form inputs without labels
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type !== 'hidden' && input.type !== 'submit' && input.type !== 'button') {
                const id = input.getAttribute('id');
                if (!id) {
                    this.addIssue(
                        'form-label', 
                        'error', 
                        input, 
                        'Input missing ID attribute', 
                        'Add ID to input so it can be associated with a label'
                    );
                } else {
                    const label = document.querySelector(`label[for="${id}"]`);
                    if (!label) {
                        this.addIssue(
                            'form-label', 
                            'error', 
                            input, 
                            `No label found for input with ID "${id}"`, 
                            'Add a label element with a matching for attribute'
                        );
                    }
                }
                
                // Check for aria-label or aria-labelledby if no visible label
                if (!document.querySelector(`label[for="${input.getAttribute('id')}"]`)) {
                    if (!input.hasAttribute('aria-label') && !input.hasAttribute('aria-labelledby')) {
                        this.addIssue(
                            'form-label', 
                            'error', 
                            input, 
                            'Input has no accessible name', 
                            'Add aria-label or aria-labelledby attribute'
                        );
                    }
                }
            }
        });
    }
    
    checkColorContrast() {
        // This is a simplified check - actual contrast checking requires computing styles
        const elements = document.querySelectorAll('*');
        elements.forEach(el => {
            const style = window.getComputedStyle(el);
            const backgroundColor = style.backgroundColor;
            const color = style.color;
            
            // Placeholder for actual contrast calculation
            // Note: This would need a color contrast algorithm to be fully implemented
            if (backgroundColor === 'rgba(0, 0, 0, 0)' && el.children.length === 0) {
                this.addIssue(
                    'color-contrast', 
                    'warning', 
                    el, 
                    'Potential color contrast issue - verify manually', 
                    'Ensure text has a contrast ratio of at least 4.5:1'
                );
            }
        });
    }
    
    checkARIAAttributes() {
        // Check for invalid ARIA attributes
        const elementsWithARIA = document.querySelectorAll('[aria-*]');
        elementsWithARIA.forEach(el => {
            // Check for aria-required on non-form elements
            if (el.hasAttribute('aria-required') && 
                !['input', 'select', 'textarea'].includes(el.tagName.toLowerCase())) {
                this.addIssue(
                    'aria-attributes', 
                    'warning', 
                    el, 
                    'aria-required used on non-form element', 
                    'Only use aria-required on form inputs'
                );
            }
            
            // Check for missing referenced elements
            if (el.hasAttribute('aria-labelledby')) {
                const ids = el.getAttribute('aria-labelledby').split(' ');
                ids.forEach(id => {
                    if (!document.getElementById(id)) {
                        this.addIssue(
                            'aria-attributes', 
                            'error', 
                            el, 
                            `aria-labelledby references non-existent ID: "${id}"`, 
                            'Fix the reference or create an element with this ID'
                        );
                    }
                });
            }
            
            // Check for invalid aria-expanded
            if (el.hasAttribute('aria-expanded') && 
                !el.hasAttribute('aria-controls')) {
                this.addIssue(
                    'aria-attributes', 
                    'warning', 
                    el, 
                    'aria-expanded used without aria-controls', 
                    'Add aria-controls attribute to reference the controlled element'
                );
            }
        });
    }
    
    checkKeyboardAccessibility() {
        // Check for clickable elements without keyboard accessibility
        const clickableElements = document.querySelectorAll('div[onclick], span[onclick]');
        clickableElements.forEach(el => {
            if (!el.hasAttribute('tabindex') || el.getAttribute('tabindex') === '-1') {
                this.addIssue(
                    'keyboard-accessibility', 
                    'error', 
                    el, 
                    'Clickable element not keyboard accessible', 
                    'Add tabindex="0" and appropriate key event handlers'
                );
            }
            
            if (!el.hasAttribute('role')) {
                this.addIssue(
                    'keyboard-accessibility', 
                    'warning', 
                    el, 
                    'Clickable element missing role attribute', 
                    'Add role="button" to indicate the element\'s purpose'
                );
            }
        });
    }
}

// Export for use in other scripts
export default AccessibilityTester;
