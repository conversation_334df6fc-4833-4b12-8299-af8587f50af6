/**
 * Loading States and Animation Manager
 * This script handles loading indicators, transitions, and animation states
 * to improve perceived performance and user experience
 */

class LoadingStatesManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupPageLoadingStates();
        this.setupButtonLoadingStates();
        this.setupFormLoadingStates();
        this.setupLazyElements();
        this.setUpAnimationToggle();
    }
    
    setupPageLoadingStates() {
        // Add loading indicator to body
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner-circle"></div>
                <span class="loading-text">Laden...</span>
            </div>
        `;
        
        document.body.appendChild(loadingOverlay);
        
        // Hide overlay when page is loaded
        window.addEventListener('load', () => {
            loadingOverlay.classList.add('loaded');
            
            // Remove from DOM after animation completes
            setTimeout(() => {
                if (loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                }
            }, 500);
        });
        
        // Handle link clicks to show loading state
        document.querySelectorAll('a:not([href^="#"]):not([target="_blank"]):not([href^="javascript:"]):not([href^="mailto:"]):not([href^="tel:"])').forEach(link => {
            link.addEventListener('click', (e) => {
                // Skip if modifier keys are pressed
                if (e.ctrlKey || e.metaKey || e.shiftKey) return;
                
                // Skip for downloads or media files
                if (link.getAttribute('href').match(/\.(jpg|png|pdf|zip|doc|mp4|mp3)$/i)) return;
                
                // Only apply to internal links
                if (link.hostname === window.location.hostname) {
                    loadingOverlay.classList.add('show');
                }
            });
        });
        
        // Handle form submissions to show loading
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', () => {
                // Only show loading overlay for forms that navigate away from page
                if (form.getAttribute('action') && !form.hasAttribute('data-ajax')) {
                    loadingOverlay.classList.add('show');
                }
            });
        });
    }
    
    setupButtonLoadingStates() {
        const buttons = document.querySelectorAll('.btn[data-loading-text]');
        
        buttons.forEach(button => {
            // Store original content
            button.setAttribute('data-original-content', button.innerHTML);
            
            button.addEventListener('click', () => {
                if (button.classList.contains('loading') || button.disabled) {
                    return; // Already loading or disabled
                }
                
                // Don't apply loading state to certain button types
                if (button.getAttribute('type') === 'reset' || 
                    button.classList.contains('btn-cancel') || 
                    button.hasAttribute('data-dismiss') || 
                    button.hasAttribute('data-toggle')) {
                    return;
                }
                
                // Get loading text
                const loadingText = button.getAttribute('data-loading-text') || 'Laden...';
                
                // Save form reference
                const form = button.closest('form');
                
                // Skip buttons that don't submit forms or have data-loading="false"
                if ((button.getAttribute('type') !== 'submit' && !form) || 
                    button.getAttribute('data-loading') === 'false') {
                    return;
                }
                
                // Apply loading state
                this.setButtonLoading(button, true, loadingText);
                
                // For form submit buttons, reset after form validation failure
                if (form) {
                    // Use mutation observer to check for validation messages
                    const observer = new MutationObserver((mutations) => {
                        const hasErrors = form.querySelectorAll('.error-message:not(:empty), .invalid').length > 0;
                        if (hasErrors) {
                            this.setButtonLoading(button, false);
                            observer.disconnect();
                        }
                    });
                    
                    observer.observe(form, { 
                        childList: true, 
                        subtree: true, 
                        attributes: true, 
                        attributeFilter: ['class'] 
                    });
                    
                    // Also reset if form has a submission error
                    form.addEventListener('invalid-submit', () => {
                        this.setButtonLoading(button, false);
                    }, { once: true });
                }
                
                // For AJAX buttons, add handler to stop loading
                if (button.hasAttribute('data-ajax')) {
                    // The AJAX handler should call this when done
                    button.stopLoading = () => {
                        this.setButtonLoading(button, false);
                    };
                    
                    // Safety - stop loading after timeout
                    setTimeout(() => {
                        this.setButtonLoading(button, false);
                    }, 10000);
                }
            });
        });
    }
    
    setButtonLoading(button, isLoading, loadingText) {
        if (isLoading) {
            button.classList.add('loading');
            button.setAttribute('disabled', 'disabled');
            
            // Store original text and swap with loading text
            const originalContent = button.innerHTML;
            const spinnerHtml = `<span class="btn-spinner"></span> `;
            
            button.innerHTML = spinnerHtml + (loadingText || 'Laden...');
        } else {
            button.classList.remove('loading');
            button.removeAttribute('disabled');
            
            // Restore original content
            button.innerHTML = button.getAttribute('data-original-content');
        }
    }
    
    setupFormLoadingStates() {
        const forms = document.querySelectorAll('form[data-loading-indicator="true"]');
        
        forms.forEach(form => {
            form.addEventListener('submit', () => {
                // Find the submit button
                const submitBtn = form.querySelector('[type="submit"]');
                
                // Don't show loading state for forms that don't navigate
                if (form.hasAttribute('data-ajax')) {
                    return;
                }
                
                // Add loading overlay to form
                this.addFormLoadingOverlay(form);
                
                // Also set button to loading state if it has no loading text
                if (submitBtn && !submitBtn.hasAttribute('data-loading-text')) {
                    this.setButtonLoading(submitBtn, true);
                }
            });
        });
    }
    
    addFormLoadingOverlay(form) {
        // Create and add overlay
        const overlay = document.createElement('div');
        overlay.className = 'form-loading-overlay';
        overlay.innerHTML = `
            <div class="form-spinner">
                <div class="spinner-circle"></div>
                <span>Verwerken...</span>
            </div>
        `;
        
        form.appendChild(overlay);
        
        // Give time for DOM to update, then add active class for animation
        setTimeout(() => {
            overlay.classList.add('active');
        }, 10);
    }
    
    setupLazyElements() {
        if ('IntersectionObserver' in window) {
            const options = {
                rootMargin: '100px',
                threshold: 0.01
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        
                        // Remove skeleton loading class
                        if (element.classList.contains('skeleton-loading')) {
                            this.loadSkeletonContent(element);
                        }
                        
                        // Add visible class for animation
                        element.classList.add('in-view');
                        
                        // Stop observing after it's been revealed
                        observer.unobserve(element);
                    }
                });
            }, options);
            
            // Observe elements with lazy-load class
            document.querySelectorAll('.lazy-load, .skeleton-loading').forEach(element => {
                observer.observe(element);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            document.querySelectorAll('.lazy-load').forEach(element => {
                element.classList.add('in-view');
            });
            
            document.querySelectorAll('.skeleton-loading').forEach(element => {
                this.loadSkeletonContent(element);
            });
        }
    }
    
    loadSkeletonContent(element) {
        // Check if this has a data-load-url
        const url = element.getAttribute('data-load-url');
        
        if (url) {
            // Fetch content via AJAX
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Replace skeleton with real content
                    element.innerHTML = html;
                    element.classList.remove('skeleton-loading');
                    element.classList.add('loaded');
                })
                .catch(error => {
                    console.error('Error loading content:', error);
                    element.classList.remove('skeleton-loading');
                    element.classList.add('load-error');
                    element.innerHTML = `
                        <div class="load-error-message">
                            <p>Er ging iets mis bij het laden van deze inhoud.</p>
                            <button class="btn btn-sm btn-primary reload-content">Probeer opnieuw</button>
                        </div>
                    `;
                    
                    // Add reload functionality
                    const reloadBtn = element.querySelector('.reload-content');
                    if (reloadBtn) {
                        reloadBtn.addEventListener('click', () => {
                            element.classList.add('skeleton-loading');
                            element.classList.remove('load-error');
                            element.innerHTML = this.getSkeletonTemplate(element);
                            this.loadSkeletonContent(element);
                        });
                    }
                });
        } else {
            // Simply remove loading state if no URL
            element.classList.remove('skeleton-loading');
            element.classList.add('loaded');
        }
    }
    
    getSkeletonTemplate(element) {
        // Return appropriate skeleton based on data-skeleton-type
        const type = element.getAttribute('data-skeleton-type') || 'default';
        
        switch (type) {
            case 'card':
                return `
                    <div class="skeleton-line" style="height: 180px; margin-bottom: 15px;"></div>
                    <div class="skeleton-line" style="width: 70%; height: 24px;"></div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 90%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 60%; height: 16px; margin-bottom: 20px;"></div>
                    <div class="skeleton-line" style="width: 30%; height: 36px;"></div>
                `;
            case 'article':
                return `
                    <div class="skeleton-line" style="width: 85%; height: 32px; margin-bottom: 20px;"></div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 95%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 70%; height: 16px; margin-bottom: 30px;"></div>
                    <div class="skeleton-line" style="height: 200px; margin-bottom: 30px;"></div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 90%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 85%; height: 16px;"></div>
                `;
            case 'profile':
                return `
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div class="skeleton-line" style="width: 60px; height: 60px; border-radius: 50%; margin-right: 15px;"></div>
                        <div style="flex: 1;">
                            <div class="skeleton-line" style="width: 70%; height: 20px; margin-bottom: 8px;"></div>
                            <div class="skeleton-line" style="width: 50%; height: 14px;"></div>
                        </div>
                    </div>
                    <div class="skeleton-line" style="width: 100%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 90%; height: 16px;"></div>
                    <div class="skeleton-line" style="width: 80%; height: 16px;"></div>
                `;
            default:
                return `
                    <div class="skeleton-line" style="width: 100%; height: 20px;"></div>
                    <div class="skeleton-line" style="width: 90%; height: 20px;"></div>
                    <div class="skeleton-line" style="width: 70%; height: 20px;"></div>
                `;
        }
    }
    
    setUpAnimationToggle() {
        // Create toggle for users who prefer reduced motion but haven't set OS preference
        const footer = document.querySelector('footer');
        
        if (footer) {
            const animToggle = document.createElement('div');
            animToggle.className = 'animation-toggle-wrapper';
            animToggle.innerHTML = `
                <button class="animation-toggle-btn" aria-pressed="true">
                    <span>Animaties</span> <span class="toggle-switch"></span>
                </button>
            `;
            
            footer.appendChild(animToggle);
            
            // Check for saved preference
            const reduceMotion = localStorage.getItem('reduceMotion') === 'true';
            const toggleBtn = animToggle.querySelector('.animation-toggle-btn');
            
            if (reduceMotion) {
                document.body.classList.add('reduce-motion');
                toggleBtn.setAttribute('aria-pressed', 'false');
            }
            
            // Toggle animations when clicked
            toggleBtn.addEventListener('click', () => {
                const isEnabled = toggleBtn.getAttribute('aria-pressed') === 'true';
                const newState = !isEnabled;
                
                toggleBtn.setAttribute('aria-pressed', newState.toString());
                document.body.classList.toggle('reduce-motion', !newState);
                
                localStorage.setItem('reduceMotion', (!newState).toString());
            });
        }
    }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', () => {
    window.loadingStates = new LoadingStatesManager();
});
