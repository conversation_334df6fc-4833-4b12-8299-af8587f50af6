/**
 * TechSupport Pro - Main JavaScript
 * Version: 1.1
 */

// Toggle mobile menu
function toggleMenu() {
    const navMenu = document.querySelector('.nav-menu'); // Corrected selector
    const hamburger = document.querySelector('.hamburger');
    if (navMenu && hamburger) { // Add null check
        navMenu.classList.toggle('active');
        const isExpanded = navMenu.classList.contains('active');
        hamburger.setAttribute('aria-expanded', isExpanded);
    }
}

// Toggle FAQ accordions
function toggleFaq(element) {
    const answer = element.nextElementSibling;
    const isActive = element.classList.contains('active');
    
    // Close all FAQ items
    document.querySelectorAll('.faq-question').forEach(q => {
        q.classList.remove('active');
        q.nextElementSibling.classList.remove('active');
        q.setAttribute('aria-expanded', 'false');
    });
    
    // Toggle current item
    if (!isActive) {
        element.classList.add('active');
        answer.classList.add('active');
        element.setAttribute('aria-expanded', 'true');
    }
}

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Language switcher (commented out as lang-switch ID is not present and i18n is not functional)
    /*
    const langSwitch = document.getElementById('lang-switch');
    if (langSwitch) {
        langSwitch.addEventListener('click', function(e) {
            e.preventDefault();
            // Redirect to English version (to be implemented)
            alert('English version coming soon!');
        });
    }
    */

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            if(this.id === 'lang-switch') return; // Skip for language switcher
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('header');
        if (window.scrollY > 50) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 2px 30px rgba(0, 0, 0, 0.15)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe service cards
    document.querySelectorAll('.service-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = (index * 0.1) + 's';
        observer.observe(card);
    });
    
    // Toggle mobile navigation menu
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            const isExpanded = hamburger.getAttribute('aria-expanded') === 'true' || false;
            hamburger.setAttribute('aria-expanded', !isExpanded);
            navMenu.classList.toggle('active');
            
            // Update hamburger button label
            hamburger.setAttribute(
                'aria-label', 
                isExpanded ? 'Menu openen' : 'Menu sluiten'
            );
        });
    }
    
    // Close mobile menu when clicking on a nav link
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                hamburger.setAttribute('aria-expanded', 'false');
                hamburger.setAttribute('aria-label', 'Menu openen');
            }
        });
    });
    
    // Add scroll event for navbar behavior
    const navbar = document.querySelector('.navbar');
    
    if (navbar) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }
    
    // Form validation with accessibility
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const subjectInput = document.getElementById('subject');
        const messageInput = document.getElementById('message');
        const termsCheckbox = document.getElementById('terms');
        const nameError = document.getElementById('name-error');
        const emailError = document.getElementById('email-error');
        const subjectError = document.getElementById('subject-error');
        const messageError = document.getElementById('message-error');
        const termsError = document.getElementById('terms-error');
        
        // Reset button functionality
        const resetButton = contactForm.querySelector('button[type="reset"]');
        if (resetButton) {
            resetButton.addEventListener('click', function(e) {
                // Confirm before resetting
                if (!confirm('Weet u zeker dat u het formulier wilt leegmaken?')) {
                    e.preventDefault();
                } else {
                    // Clear all error messages when form is reset
                    const errorMessages = contactForm.querySelectorAll('.error-message');
                    errorMessages.forEach(element => {
                        element.textContent = '';
                    });
                    
                    // Reset aria-invalid attributes
                    const formInputs = contactForm.querySelectorAll('input, select, textarea');
                    formInputs.forEach(input => {
                        input.setAttribute('aria-invalid', 'false');
                    });
                }
            });
        }
        
        // Live validation for email field
        if (emailInput) {
            emailInput.addEventListener('blur', function() {
                validateEmail(emailInput, emailError);
            });
        }

        // Function to validate email format
        function validateEmail(input, errorElement) {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (input.value && !emailPattern.test(input.value)) {
                errorElement.textContent = 'Vul een geldig e-mailadres in';
                input.setAttribute('aria-invalid', 'true');
                return false;
            } else {
                errorElement.textContent = '';
                input.setAttribute('aria-invalid', 'false');
                return true;
            }
        }
        
        // Form submission validation
        contactForm.addEventListener('submit', function(event) {
            let isValid = true;
            let firstInvalid = null;
            
            // Reset all error messages
            const errorMessages = contactForm.querySelectorAll('.error-message');
            errorMessages.forEach(element => {
                element.textContent = '';
            });
            
            // Validate name
            if (!nameInput.value.trim()) {
                nameError.textContent = 'Vul uw naam in';
                nameInput.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = nameInput;
            } else {
                nameInput.setAttribute('aria-invalid', 'false');
            }
            
            // Validate email
            if (!emailInput.value.trim()) {
                emailError.textContent = 'Vul uw e-mailadres in';
                emailInput.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = emailInput;
            } else {
                isValid = validateEmail(emailInput, emailError) && isValid;
                if (emailError.textContent && !firstInvalid) firstInvalid = emailInput;
            }
            
            // Validate subject
            if (!subjectInput.value || subjectInput.value === '') {
                subjectError.textContent = 'Selecteer een onderwerp';
                subjectInput.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = subjectInput;
            } else {
                subjectInput.setAttribute('aria-invalid', 'false');
            }
            
            // Validate message
            if (!messageInput.value.trim()) {
                messageError.textContent = 'Vul uw bericht in';
                messageInput.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = messageInput;
            } else if (messageInput.value.trim().length < 10) {
                messageError.textContent = 'Uw bericht is te kort. Geef meer details.';
                messageInput.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = messageInput;
            } else {
                messageInput.setAttribute('aria-invalid', 'false');
            }
            
            // Validate terms checkbox
            if (!termsCheckbox.checked) {
                termsError.textContent = 'U moet akkoord gaan met de privacyvoorwaarden';
                termsCheckbox.setAttribute('aria-invalid', 'true');
                isValid = false;
                if (!firstInvalid) firstInvalid = termsCheckbox;
            } else {
                termsCheckbox.setAttribute('aria-invalid', 'false');
            }
            
            if (!isValid) {
                event.preventDefault();
                
                // Focus on the first invalid field
                if (firstInvalid) {
                    firstInvalid.focus();
                }
                
                // Create a status message for screen readers
                const statusMessage = document.createElement('div');
                statusMessage.textContent = 'Er zijn fouten gevonden in het formulier. Controleer de gemarkeerde velden.';
                statusMessage.className = 'sr-only';
                statusMessage.setAttribute('role', 'alert');
                statusMessage.setAttribute('aria-live', 'assertive');
                contactForm.insertBefore(statusMessage, contactForm.firstChild);
                
                // Remove the status message after it's been announced
                setTimeout(() => {
                    statusMessage.remove();
                }, 3000);
            } else {
                // For demo purposes, prevent actual form submission and show success
                event.preventDefault();
                
                // Replace form with success message
                const formContainer = contactForm.parentNode;
                const successMessage = document.createElement('div');
                successMessage.className = 'form-success';
                successMessage.innerHTML = `
                    <svg class="success-icon" viewBox="0 0 24 24" width="64" height="64">
                        <circle cx="12" cy="12" r="11" fill="#4CAF50" stroke="white" stroke-width="2"></circle>
                        <path fill="white" d="M9.7 15.3l-3.3-3.3-1.4 1.4 4.7 4.7 10-10-1.4-1.4z"></path>
                    </svg>
                    <h3>Bedankt voor uw bericht!</h3>
                    <p>We hebben uw aanvraag ontvangen en nemen zo spoedig mogelijk contact met u op.</p>
                    <p>Referentienummer: TSP-${Math.floor(Math.random() * 10000)}</p>
                    <button type="button" class="btn" id="newFormBtn">Nieuw bericht</button>
                `;
                
                contactForm.style.display = 'none';
                formContainer.appendChild(successMessage);
                
                // Add button to return to form
                const newFormBtn = document.getElementById('newFormBtn');
                if (newFormBtn) {
                    newFormBtn.addEventListener('click', () => {
                        contactForm.reset();
                        successMessage.remove();
                        contactForm.style.display = 'block';
                    });
                }
            }
        });
    }
    
    // Add keyboard navigation support for card elements
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
        // Make cards focusable for keyboard navigation
        card.setAttribute('tabindex', '0');
        
        // Add keyboard event listener for Enter key
        card.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                // Trigger click on first link in card
                const cardLink = this.querySelector('a');
                if (cardLink) {
                    cardLink.click();
                }
            }
        });
    });
    
    // Add image description toggling
    document.querySelectorAll('.card-img').forEach(img => {
        img.addEventListener('click', function() {
            const description = this.getAttribute('alt');
            const descElement = document.createElement('div');
            
            descElement.className = 'img-description';
            descElement.textContent = description;
            descElement.setAttribute('aria-live', 'polite');
            
            // Remove any existing descriptions
            const existingDesc = this.parentNode.querySelector('.img-description');
            if (existingDesc) {
                existingDesc.remove();
            } else {
                // Add new description
                this.parentNode.insertBefore(descElement, this.nextSibling);
                
                // Auto remove after 3 seconds
                setTimeout(() => {
                    descElement.remove();
                }, 3000);
            }
        });
    });
    
    // Services Tab Functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const serviceContents = document.querySelectorAll('.service-content');
    
    if (tabButtons.length && serviceContents.length) {
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                serviceContents.forEach(content => {
                    content.classList.remove('active');
                });
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                button.setAttribute('aria-selected', 'true');
                
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-content`).classList.add('active');
            });
            
            // Add keyboard navigation
            button.addEventListener('keydown', (event) => {
                if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
                    event.preventDefault();
                    const currentIndex = Array.from(tabButtons).indexOf(event.target);
                    const direction = event.key === 'ArrowLeft' ? -1 : 1;
                    const newIndex = (currentIndex + direction + tabButtons.length) % tabButtons.length;
                    tabButtons[newIndex].focus();
                }
            });
        });
    }
    
    // FAQ Accordion Functionality
    const accordionHeaders = document.querySelectorAll('.accordion-header');
    
    if (accordionHeaders.length) {
        accordionHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const isExpanded = header.getAttribute('aria-expanded') === 'true';
                
                // Close all other accordions
                accordionHeaders.forEach(item => {
                    if (item !== header) {
                        item.setAttribute('aria-expanded', 'false');
                    }
                });
                
                // Toggle current accordion
                header.setAttribute('aria-expanded', !isExpanded);
            });
            
            // Add keyboard accessibility
            header.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault();
                    header.click();
                }
            });
        });
    }
});