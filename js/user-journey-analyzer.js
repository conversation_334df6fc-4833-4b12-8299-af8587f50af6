/**
 * User Journey Analyzer for TechSupport Pro
 * 
 * This script helps identify and optimize common user paths through the website
 * to improve conversion rates and user experience.
 */

class UserJourneyAnalyzer {
    constructor() {
        this.currentPath = window.location.pathname;
        this.referrer = document.referrer;
        this.sessionStartTime = Date.now();
        this.interactions = [];
        this.journeyKey = 'tsp_user_journey';
        
        this.init();
    }
    
    init() {
        // Load previous journey data if available
        this.loadJourney();
        
        // Record page view
        this.recordPageView();
        
        // Track interactions on important elements
        this.trackInteractions();
        
        // Save journey on page unload
        window.addEventListener('beforeunload', () => {
            this.saveJourney();
        });
    }
    
    loadJourney() {
        try {
            const savedJourney = localStorage.getItem(this.journeyKey);
            if (savedJourney) {
                const journeyData = JSON.parse(savedJourney);
                // Only use journey data if it's from the same day
                if (this.isSameDay(journeyData.startTime)) {
                    this.interactions = journeyData.interactions || [];
                    this.sessionStartTime = journeyData.startTime;
                } else {
                    // Start new journey
                    localStorage.removeItem(this.journeyKey);
                }
            }
        } catch (e) {
            console.error('Error loading journey:', e);
            localStorage.removeItem(this.journeyKey);
        }
    }
    
    saveJourney() {
        try {
            const journeyData = {
                startTime: this.sessionStartTime,
                interactions: this.interactions
            };
            localStorage.setItem(this.journeyKey, JSON.stringify(journeyData));
        } catch (e) {
            console.error('Error saving journey:', e);
        }
    }
    
    recordPageView() {
        this.interactions.push({
            type: 'pageview',
            path: this.currentPath,
            referrer: this.referrer,
            timestamp: Date.now()
        });
    }
    
    trackInteractions() {
        // Track clicks on important elements
        const trackElements = {
            'nav-link': '.nav-link',
            'btn': '.btn',
            'service-card': '.service-card',
            'cta': '[data-cta]',
            'footer-link': '.footer-link'
        };
        
        Object.entries(trackElements).forEach(([category, selector]) => {
            document.querySelectorAll(selector).forEach(element => {
                element.addEventListener('click', (e) => {
                    this.recordInteraction(category, element);
                });
            });
        });
        
        // Track form submissions
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                const formId = form.id || form.getAttribute('name') || 'unknown-form';
                this.recordInteraction('form-submit', form, formId);
            });
        });
    }
    
    recordInteraction(category, element, id) {
        const elementId = id || element.id || null;
        const href = element.href || null;
        
        let label = element.innerText?.trim();
        if (!label && element.getAttribute('aria-label')) {
            label = element.getAttribute('aria-label');
        }
        
        this.interactions.push({
            type: 'interaction',
            category: category,
            elementId: elementId,
            label: label,
            href: href,
            path: this.currentPath,
            timestamp: Date.now()
        });
        
        this.saveJourney();
    }
    
    isSameDay(timestamp) {
        const date = new Date(timestamp);
        const today = new Date();
        return date.getDate() === today.getDate() &&
               date.getMonth() === today.getMonth() &&
               date.getFullYear() === today.getFullYear();
    }
    
    analyzeJourney() {
        if (this.interactions.length < 2) {
            return {
                complete: false,
                message: 'Niet genoeg data voor analyse.'
            };
        }
        
        const paths = this.extractPaths();
        const conversions = this.checkConversions();
        const engagementScore = this.calculateEngagement();
        
        // Identify common journeys
        return {
            complete: true,
            paths: paths,
            conversions: conversions,
            engagementScore: engagementScore,
            interactionCount: this.interactions.length,
            timeSpent: Date.now() - this.sessionStartTime
        };
    }
    
    extractPaths() {
        const pageViews = this.interactions.filter(i => i.type === 'pageview');
        return pageViews.map(pv => pv.path);
    }
    
    checkConversions() {
        // Define conversion goals
        const conversionGoals = {
            'contact': {
                path: '/contact.html',
                formSubmit: true
            },
            'diensten-bekeken': {
                path: '/diensten.html'
            },
            'blog-gelezen': {
                path: '/blog/'
            }
        };
        
        const conversions = {};
        
        // Check for pageview conversions
        for (const [goalName, goal] of Object.entries(conversionGoals)) {
            if (goal.path) {
                const pathVisits = this.interactions.filter(i => 
                    i.type === 'pageview' && i.path.includes(goal.path)
                );
                
                if (pathVisits.length > 0) {
                    conversions[goalName] = true;
                }
            }
            
            // Check for form submissions if that's a goal
            if (goal.formSubmit) {
                const formSubmits = this.interactions.filter(i => 
                    i.type === 'interaction' && 
                    i.category === 'form-submit' &&
                    i.path.includes(goal.path)
                );
                
                if (formSubmits.length > 0) {
                    conversions[goalName + '-completed'] = true;
                }
            }
        }
        
        return conversions;
    }
    
    calculateEngagement() {
        if (this.interactions.length < 2) {
            return 0;
        }
        
        const totalTime = Date.now() - this.sessionStartTime;
        const pageViews = this.interactions.filter(i => i.type === 'pageview').length;
        const interactions = this.interactions.filter(i => i.type === 'interaction').length;
        
        // Calculate engagement score based on:
        // - Number of page views
        // - Number of interactions (clicks, submits)
        // - Time spent on site
        
        let score = 0;
        
        // Page views: 5 points each, max 25
        score += Math.min(pageViews * 5, 25);
        
        // Interactions: 2 points each, max 25
        score += Math.min(interactions * 2, 25);
        
        // Time: 10 points for every minute spent, max 50
        const minutesSpent = totalTime / (1000 * 60);
        score += Math.min(Math.floor(minutesSpent * 10), 50);
        
        return Math.round(score);
    }
    
    suggestNextSteps() {
        const analysis = this.analyzeJourney();
        
        if (!analysis.complete) {
            return null;
        }
        
        // Define next step suggestions based on user's journey so far
        const suggestions = [];
        
        // If they've viewed services but not contacted
        if (analysis.conversions['diensten-bekeken'] && !analysis.conversions['contact']) {
            suggestions.push({
                type: 'cta',
                label: 'Vraag een offerte aan',
                url: '/contact.html',
                priority: 'high'
            });
        }
        
        // If they've read blog but not viewed services
        if (analysis.conversions['blog-gelezen'] && !analysis.conversions['diensten-bekeken']) {
            suggestions.push({
                type: 'cta',
                label: 'Bekijk onze diensten',
                url: '/diensten.html',
                priority: 'medium'
            });
        }
        
        // If they've spent time but low engagement
        if (analysis.timeSpent > 60000 && analysis.engagementScore < 30) {
            suggestions.push({
                type: 'engagement',
                label: 'Hulp nodig?',
                action: 'showSupportChat',
                priority: 'medium'
            });
        }
        
        // For high-engagement users without conversion
        if (analysis.engagementScore > 60 && !analysis.conversions['contact']) {
            suggestions.push({
                type: 'newsletter',
                label: 'Schrijf je in voor onze nieuwsbrief',
                action: 'showNewsletterPopup',
                priority: 'low'
            });
        }
        
        return suggestions;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userJourneyAnalyzer = new UserJourneyAnalyzer();
    
    // Check for suggestions after 30 seconds
    setTimeout(() => {
        const suggestions = window.userJourneyAnalyzer.suggestNextSteps();
        
        if (suggestions && suggestions.length > 0) {
            // Sort by priority
            suggestions.sort((a, b) => {
                const priorities = { high: 3, medium: 2, low: 1 };
                return priorities[b.priority] - priorities[a.priority];
            });
            
            // Show top suggestion
            const topSuggestion = suggestions[0];
            
            if (topSuggestion.type === 'cta' && topSuggestion.url) {
                // Show CTA banner
                if (window.UX) {
                    window.UX.showNotification(
                        `<strong>${topSuggestion.label}</strong>`, 
                        'info', 
                        8000
                    );
                }
            } else if (topSuggestion.action) {
                // Execute suggestion action
                if (topSuggestion.action === 'showSupportChat') {
                    // Code to show support chat
                    if (window.supportChat) {
                        window.supportChat.show();
                    }
                } else if (topSuggestion.action === 'showNewsletterPopup') {
                    // Code to show newsletter popup
                    if (window.newsletter) {
                        window.newsletter.showPopup();
                    }
                }
            }
        }
    }, 30000);
});
