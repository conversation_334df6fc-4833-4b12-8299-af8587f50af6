/**
 * TechSupport Pro - Localization/Internationalization (i18n) System
 * 
 * This script handles the language switching functionality and applies translations
 * to the appropriate elements throughout the website.
 */

// Create global translations object if i      'nl': 'Specialisten in IT-ondersteuning voor bedrijven in heel Nederland. 24/7 service voor netwerk en software oplossingen op maat.',
      'en': 'IT support specialists for businesses across the Netherlands. 24/7 service for network and software solutions tailored to your needs.',
      'fr': 'Spécialistes du support informatique pour les entreprises aux Pays-Bas. Service 24/7 pour des solutions réseau et logiciel adaptées à vos besoins.',
      'pt': 'Especialistas em suporte de TI para empresas em toda a Holanda. Serviço 24/7 para soluções de rede e software personalizadas às suas necessidades.'esn't exist
if (typeof window.translations === 'undefined') {
  window.translations = {};
}

// i18n controller
const i18n = {
  defaultLang: 'nl',
  currentLang: 'nl',
  supportedLangs: ['nl', 'en', 'fr', 'pt'],
  selectors: {
    // Navigation
    'nav.home': 'nav .nav-item:nth-child(1) .nav-link',
    'nav.services': 'nav .nav-item:nth-child(2) .nav-link',
    'nav.about': 'nav .nav-item:nth-child(3) .nav-link',
    'nav.testimonials': 'nav .nav-item:nth-child(4) .nav-link',
    'nav.blog': 'nav .nav-item:nth-child(5) .nav-link',
    'nav.contact': 'nav .nav-item:nth-child(6) .nav-link',
    
    // Hero section
    'hero.title': '.hero h1',
    'hero.subtitle': '.hero h1 + p',
    'hero.cta': '.hero-buttons .btn-primary',
    'hero.secondary': '.hero-buttons .btn-secondary',
    
    // Services section
    'services.title': '#diensten h2',
    'services.subtitle': '#diensten .section-subtitle',
    
    // About section
    'about.title': '#over-ons h2',
    'about.subtitle': '#over-ons .section-subtitle',
    
    // Testimonials section
    'testimonials.title': '#referenties h2',
    'testimonials.subtitle': '#referenties .section-subtitle',
    
    // Blog section  
    'blog.title': '.blog-highlights h2',
    'blog.subtitle': '.blog-highlights .section-subtitle',
    'blog.read_more': '.blog-highlights-footer .btn',
    
    // Contact section
    'contact.title': '#contact h2',
    'contact.subtitle': '#contact .section-subtitle',
    
    // Footer
    'footer.links': '#footer-links',
    'footer.legal': '#footer-legal',
    'footer.about_text': '#footer-about + p'
  },
  
  /**
   * Initialize the localization system
   */
  init: function() {
    // Set initial language from storage or browser preference
    this.loadInitialLanguage();
    
    // Make sure translations are loaded
    this.loadTranslationsIfNeeded();
    
    // Set up language selector functionality
    this.setupLanguageSelector();
    
    // Apply initial translations
    this.translatePage();
  },
  
  /**
   * Ensure translations are loaded or use defaults
   */
  loadTranslationsIfNeeded: function() {
    if (!window.translations || !window.translations[this.currentLang]) {
      console.warn(`Loading default translations for ${this.currentLang}`);
      // Load default translations for navigation
      window.translations = window.translations || {};
      window.translations[this.currentLang] = window.translations[this.currentLang] || {};
      window.translations[this.currentLang]['nav'] = {
        'home': 'Start',
        'services': 'Diensten',
        'about': 'Over Ons',
        'testimonials': 'Referenties',
        'blog': 'Blog',
        'contact': 'Contact'
      };
    }
  },
  
  /**
   * Load the initial language from storage or browser settings
   */
  loadInitialLanguage: function() {
    // Check for stored language preference
    const storedLang = localStorage.getItem('techsupport_language');
    
    if (storedLang && this.supportedLangs.includes(storedLang)) {
      this.currentLang = storedLang;
    } else {
      // Try to detect browser language
      const browserLang = navigator.language || navigator.userLanguage;
      const shortLang = browserLang.split('-')[0];
      
      if (this.supportedLangs.includes(shortLang)) {
        this.currentLang = shortLang;
      } else {
        // Fall back to default
        this.currentLang = this.defaultLang;
      }
    }
    
    // Update current language display
    document.documentElement.lang = this.currentLang;
    
    // Update toggle button text
    const langToggle = document.getElementById('language-toggle');
    if (langToggle) {
      const currentLangSpan = langToggle.querySelector('.current-language');
      if (currentLangSpan) {
        currentLangSpan.textContent = this.currentLang.toUpperCase();
      }
    }
    
    // Update active state in dropdown
    this.updateLanguageButtonStates();
  },
  
  /**
   * Set up event listeners for the language selector
   */
  setupLanguageSelector: function() {
    // Toggle dropdown
    const langToggle = document.getElementById('language-toggle');
    if (langToggle) {
      langToggle.addEventListener('click', () => {
        const isExpanded = langToggle.getAttribute('aria-expanded') === 'true';
        langToggle.setAttribute('aria-expanded', !isExpanded);
      });
      
      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!e.target.closest('.language-selector')) {
          langToggle.setAttribute('aria-expanded', 'false');
        }
      });
      
      // Keyboard navigation support
      langToggle.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          langToggle.setAttribute('aria-expanded', 'false');
        } else if (e.key === 'ArrowDown') {
          langToggle.setAttribute('aria-expanded', 'true');
          const firstOption = document.querySelector('.language-option');
          if (firstOption) firstOption.focus();
          e.preventDefault();
        }
      });
    }
    
    // Language selection buttons
    const langOptions = document.querySelectorAll('.language-option');
    if (langOptions.length) {
      langOptions.forEach(option => {
        option.addEventListener('click', (e) => {
          const lang = option.getAttribute('data-lang');
          this.setLanguage(lang);
          
          // Close dropdown
          const langToggle = document.getElementById('language-toggle');
          if (langToggle) {
            langToggle.setAttribute('aria-expanded', 'false');
          }
        });
        
        // Keyboard support
        option.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            const langToggle = document.getElementById('language-toggle');
            if (langToggle) {
              langToggle.setAttribute('aria-expanded', 'false');
              langToggle.focus();
            }
          }
        });
      });
    }
  },
  
  /**
   * Change the current language
   * @param {string} lang - Language code to switch to
   */
  setLanguage: function(lang) {
    if (!this.supportedLangs.includes(lang)) {
      console.error(`Language ${lang} is not supported`);
      return;
    }
    
    // Update current language
    this.currentLang = lang;
    
    // Update HTML lang attribute
    document.documentElement.lang = lang;
    
    // Store preference
    localStorage.setItem('techsupport_language', lang);
    
    // Update toggle button text
    const langToggle = document.getElementById('language-toggle');
    if (langToggle) {
      const currentLangSpan = langToggle.querySelector('.current-language');
      if (currentLangSpan) {
        currentLangSpan.textContent = lang.toUpperCase();
      }
    }
    
    // Update meta tags
    this.updateMetaTags();
    
    // Update active state in dropdown
    this.updateLanguageButtonStates();
    
    // Apply translations
    this.translatePage();
    
    // Dispatch event for other scripts
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
  },
  
  /**
   * Update the active state of language buttons
   */
  updateLanguageButtonStates: function() {
    const langOptions = document.querySelectorAll('.language-option');
    if (langOptions.length) {
      langOptions.forEach(option => {
        const isActive = option.getAttribute('data-lang') === this.currentLang;
        option.classList.toggle('active', isActive);
        option.setAttribute('aria-pressed', isActive);
      });
    }
  },
  
  /**
   * Update meta tags when language changes
   */
  updateMetaTags: function() {
    // Update meta tags based on language
    const metaTitles = {
      'nl': 'TechSupport Pro - Professionele IT Support Services voor Bedrijven',
      'en': 'TechSupport Pro - Professional IT Support Services for Businesses',
      'fr': 'TechSupport Pro - Services de support informatique professionnels pour entreprises',
      'pt': 'TechSupport Pro - Serviços profissionais de suporte de TI para empresas'
    };
    
    const metaDescriptions = {
      'nl': 'Specialisten in IT-ondersteuning voor bedrijven in heel Nederland. 24/7 service voor netwerk en software oplossingen op maat.',
      'en': 'IT support specialists for businesses across the Netherlands. 24/7 service for network and software solutions tailored to your needs.',
      'fr': 'Spécialistes du support informatique pour les entreprises aux Pays-Bas. Service 24/7 pour les solutions réseau et logiciel adaptées à vos besoins.',
      'pt': 'Especialistas em suporte de TI para empresas em toda a Holanda. Serviço 24/7 para soluções de rede e software personalizadas às suas necessidades.'
    };
    
    // Update document title
    document.title = metaTitles[this.currentLang] || metaTitles['nl'];
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', metaDescriptions[this.currentLang] || metaDescriptions['nl']);
    }
    
    // Update OpenGraph title and description
    const ogTitle = document.querySelector('meta[property="og:title"]');
    const ogDesc = document.querySelector('meta[property="og:description"]');
    
    if (ogTitle) {
      ogTitle.setAttribute('content', metaTitles[this.currentLang] || metaTitles['nl']);
    }
    
    if (ogDesc) {
      ogDesc.setAttribute('content', metaDescriptions[this.currentLang] || metaDescriptions['nl']);
    }
    
    // Update Twitter Card title and description
    const twitterTitle = document.querySelector('meta[property="twitter:title"]');
    const twitterDesc = document.querySelector('meta[property="twitter:description"]');
    
    if (twitterTitle) {
      twitterTitle.setAttribute('content', metaTitles[this.currentLang] || metaTitles['nl']);
    }
    
    if (twitterDesc) {
      twitterDesc.setAttribute('content', metaDescriptions[this.currentLang] || metaDescriptions['nl']);
    }
  },
  
  /**
   * Get a translation string by key
   * @param {string} key - The translation key in dot notation (e.g., 'nav.home')
   * @returns {string} - The translated string or the key if not found
   */
  getTranslation: function(key) {
    // Fallback translations for navigation items when translations aren't loaded
    const navFallbacks = {
      'nav.home': 'Start',
      'nav.services': 'Diensten',
      'nav.about': 'Over Ons',
      'nav.testimonials': 'Referenties',
      'nav.blog': 'Blog',
      'nav.contact': 'Contact'
    };
    
    // Check if translations are loaded
    if (!window.translations || !window.translations[this.currentLang]) {
      console.warn(`Translations for ${this.currentLang} not loaded`);
      // Use fallback for navigation items
      if (navFallbacks[key]) {
        return navFallbacks[key];
      }
      return key;
    }
    
    // Split the key into parts (e.g., 'nav.home' -> ['nav', 'home'])
    const parts = key.split('.');
    
    // Navigate through the translations object
    let translation = window.translations[this.currentLang];
    for (const part of parts) {
      translation = translation[part];
      
      // If at any point the path doesn't exist, return fallback or key
      if (translation === undefined) {
        console.warn(`Translation key '${key}' not found for ${this.currentLang}`);
        // Use fallback for navigation items
        if (navFallbacks[key]) {
          return navFallbacks[key];
        }
        return key;
      }
    }
    
    return translation;
  },
  
  /**
   * Apply translations to the page
   */
  translatePage: function() {
    // First translate all elements with explicit data-i18n attributes
    this.translateExplicitElements();
    
    // Then translate elements using the selectors mapping
    this.translateWithSelectors();
    
    // Handle special cases for forms, buttons, etc.
    this.translateSpecialCases();
  },
  
  /**
   * Translate elements with explicit data-i18n attributes
   */
  translateExplicitElements: function() {
    // Find all elements with data-i18n attribute
    const elements = document.querySelectorAll('[data-i18n]');
    
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.getTranslation(key);
      
      // Apply translation only if we have a real translation (not the key itself)
      // This prevents navigation items showing "nav.services" instead of actual text
      if (translation !== key) {
        element.textContent = translation;
      }
    });
    
    // Handle placeholder translations
    const placeholders = document.querySelectorAll('[data-i18n-placeholder]');
    placeholders.forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      const translation = this.getTranslation(key);
      
      // Apply translation to placeholder
      element.setAttribute('placeholder', translation);
    });
    
    // Handle title attribute translations
    const titles = document.querySelectorAll('[data-i18n-title]');
    titles.forEach(element => {
      const key = element.getAttribute('data-i18n-title');
      const translation = this.getTranslation(key);
      
      // Apply translation to title
      element.setAttribute('title', translation);
    });
    
    // Handle alt text translations
    const altTexts = document.querySelectorAll('[data-i18n-alt]');
    altTexts.forEach(element => {
      const key = element.getAttribute('data-i18n-alt');
      const translation = this.getTranslation(key);
      
      // Apply translation to alt text
      element.setAttribute('alt', translation);
    });
    
    // Handle aria-label translations
    const ariaLabels = document.querySelectorAll('[data-i18n-aria-label]');
    ariaLabels.forEach(element => {
      const key = element.getAttribute('data-i18n-aria-label');
      const translation = this.getTranslation(key);
      
      // Apply translation to aria-label
      element.setAttribute('aria-label', translation);
    });
  },
  
  /**
   * Translate elements using the selectors mapping
   */
  translateWithSelectors: function() {
    // Loop through all selector mappings
    for (const [key, selector] of Object.entries(this.selectors)) {
      const elements = document.querySelectorAll(selector);
      if (elements && elements.length) {
        const translation = this.getTranslation(key);
        
        elements.forEach(element => {
          // Skip if element already has a data-i18n attribute
          if (!element.hasAttribute('data-i18n')) {
            // Only set the text if the translation is not just the key itself
            // This prevents showing "nav.home" etc. when translations aren't loaded
            if (translation !== key) {
              element.textContent = translation;
            }
            
            // Add data-i18n attribute for future reference
            element.setAttribute('data-i18n', key);
          }
        });
      }
    }
  },
  
  /**
   * Handle special cases that need custom translation logic
   */
  translateSpecialCases: function() {
    // Contact form placeholders
    const contactForm = document.querySelector('#contact-form');
    if (contactForm) {
      // Name field
      const nameInput = contactForm.querySelector('#name');
      if (nameInput) {
        nameInput.setAttribute('placeholder', this.getTranslation('contact.form.name_placeholder'));
      }
      
      // Email field
      const emailInput = contactForm.querySelector('#email');
      if (emailInput) {
        emailInput.setAttribute('placeholder', this.getTranslation('contact.form.email_placeholder'));
      }
      
      // Phone field
      const phoneInput = contactForm.querySelector('#phone');
      if (phoneInput) {
        phoneInput.setAttribute('placeholder', this.getTranslation('contact.form.phone_placeholder'));
      }
      
      // Company field
      const companyInput = contactForm.querySelector('#company');
      if (companyInput) {
        companyInput.setAttribute('placeholder', this.getTranslation('contact.form.company_placeholder'));
      }
      
      // Message field
      const messageInput = contactForm.querySelector('#message');
      if (messageInput) {
        messageInput.setAttribute('placeholder', this.getTranslation('contact.form.message_placeholder'));
      }
    }
    
    // Footer copyright text
    const footerCopyright = document.querySelector('.footer-copyright p');
    if (footerCopyright) {
      const year = document.getElementById('current-year').textContent;
      footerCopyright.innerHTML = `&copy; ${year} TechSupport Pro. ${this.getTranslation('footer.copyright')}`;
    }
  }
};

// Initialize i18n when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  i18n.init();
});

// Export for reuse in other scripts
window.i18n = i18n;