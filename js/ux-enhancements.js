/**
 * UX Enhancements for TechSupport Pro
 * This file contains JavaScript functions for improving the user experience
 */

class UXManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupNavigationEnhancements();
        this.setupFeedbackSystem();
        this.setupProgressIndicators();
        this.setupFormEnhancements();
        this.setupMicroInteractions();
        this.setupScrollEffects();
        this.setupPageTransitions();
        this.setupUserJourneys();
    }

    /* ====== NAVIGATION ENHANCEMENTS ====== */
    setupNavigationEnhancements() {
        // Add breadcrumbs to all pages
        this.createBreadcrumbs();
        
        // Setup back to top button
        this.setupBackToTop();
        
        // Enhance dropdowns 
        this.enhanceDropdownMenus();
        
        // Add active state indicators
        this.highlightActiveNavItems();
    }
    
    createBreadcrumbs() {
        // Only proceed if we're not on the homepage
        if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
            return;
        }
        
        const breadcrumbContainer = document.createElement('div');
        breadcrumbContainer.className = 'breadcrumbs';
        breadcrumbContainer.setAttribute('aria-label', 'Breadcrumb');
        
        // Create the breadcrumb list
        const breadcrumbList = document.createElement('ol');
        breadcrumbList.setAttribute('itemscope', '');
        breadcrumbList.setAttribute('itemtype', 'https://schema.org/BreadcrumbList');
        
        // Start with homepage
        let breadcrumbs = [
            { title: 'Home', url: '/' }
        ];
        
        // Get current page title and URL
        const currentPath = window.location.pathname;
        const pageTitle = document.title.split('|')[0].trim();
        
        // Handle section pages
        if (currentPath.includes('/blog/')) {
            breadcrumbs.push({ title: 'Blog', url: '/blog/' });
        } else if (currentPath.includes('/diensten.html')) {
            breadcrumbs.push({ title: 'Diensten', url: '/diensten.html' });
        }
        
        // Add current page as last item
        breadcrumbs.push({ title: pageTitle, url: currentPath });
        
        // Generate the breadcrumb HTML
        breadcrumbs.forEach((crumb, index) => {
            const listItem = document.createElement('li');
            listItem.setAttribute('itemprop', 'itemListElement');
            listItem.setAttribute('itemscope', '');
            listItem.setAttribute('itemtype', 'https://schema.org/ListItem');
            
            if (index < breadcrumbs.length - 1) {
                const link = document.createElement('a');
                link.href = crumb.url;
                link.setAttribute('itemprop', 'item');
                link.innerHTML = `<span itemprop="name">${crumb.title}</span>`;
                listItem.appendChild(link);
            } else {
                // Current page (not clickable)
                const span = document.createElement('span');
                span.setAttribute('itemprop', 'name');
                span.textContent = crumb.title;
                span.setAttribute('aria-current', 'page');
                listItem.appendChild(span);
            }
            
            // Add position metadata
            const meta = document.createElement('meta');
            meta.setAttribute('itemprop', 'position');
            meta.setAttribute('content', (index + 1).toString());
            listItem.appendChild(meta);
            
            breadcrumbList.appendChild(listItem);
        });
        
        breadcrumbContainer.appendChild(breadcrumbList);
        
        // Insert after the main navigation
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.insertBefore(breadcrumbContainer, mainContent.firstChild);
        }
    }
    
    setupBackToTop() {
        // Create back to top button
        const backToTopBtn = document.createElement('button');
        backToTopBtn.className = 'back-to-top';
        backToTopBtn.setAttribute('aria-label', 'Terug naar boven');
        backToTopBtn.innerHTML = '<i class="fas fa-arrow-up" aria-hidden="true"></i>';
        
        // Add to body
        document.body.appendChild(backToTopBtn);
        
        // Show/hide based on scroll position
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
        
        // Smooth scroll to top when clicked
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    enhanceDropdownMenus() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const hasDropdown = item.querySelector('.dropdown-menu');
            if (hasDropdown) {
                const link = item.querySelector('.nav-link');
                
                // Add dropdown indicators
                link.innerHTML += '<i class="fas fa-chevron-down dropdown-indicator" aria-hidden="true"></i>';
                
                // Make aria states match visual states
                link.setAttribute('aria-expanded', 'false');
                link.setAttribute('aria-haspopup', 'true');
                
                // Toggle dropdown on click
                link.addEventListener('click', (e) => {
                    // Prevent navigating away
                    if (window.innerWidth > 768) {
                        e.preventDefault();
                    }
                    
                    const expanded = link.getAttribute('aria-expanded') === 'true';
                    link.setAttribute('aria-expanded', (!expanded).toString());
                    
                    item.classList.toggle('dropdown-active');
                });
                
                // Close dropdowns when clicking elsewhere
                document.addEventListener('click', (e) => {
                    if (!item.contains(e.target)) {
                        item.classList.remove('dropdown-active');
                        link.setAttribute('aria-expanded', 'false');
                    }
                });
            }
        });
    }
    
    highlightActiveNavItems() {
        const currentPath = window.location.pathname;
        
        // Find the matching nav item
        document.querySelectorAll('.nav-link').forEach(link => {
            if (link.getAttribute('href') === currentPath || 
                (currentPath.includes(link.getAttribute('href')) && link.getAttribute('href') !== '/')) {
                link.classList.add('active');
                
                // If inside dropdown, also highlight parent
                const parentDropdown = link.closest('.dropdown-menu');
                if (parentDropdown) {
                    const parentNavItem = parentDropdown.closest('.nav-item');
                    if (parentNavItem) {
                        const parentLink = parentNavItem.querySelector('.nav-link');
                        if (parentLink) {
                            parentLink.classList.add('active-parent');
                        }
                    }
                }
            } else {
                link.classList.remove('active');
            }
        });
    }

    /* ====== FEEDBACK SYSTEMS ====== */
    setupFeedbackSystem() {
        // Create notification container if doesn't exist
        if (!document.getElementById('notification-container')) {
            const notificationContainer = document.createElement('div');
            notificationContainer.id = 'notification-container';
            notificationContainer.setAttribute('aria-live', 'polite');
            document.body.appendChild(notificationContainer);
        }
    }
    
    /**
     * Show a toast notification to the user
     * @param {string} message - The message to display
     * @param {string} type - Type of notification: 'success', 'error', 'info', 'warning'
     * @param {number} duration - How long to show the notification (ms)
     */
    showNotification(message, type = 'info', duration = 4000) {
        const container = document.getElementById('notification-container');
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
        
        // Add icon based on type
        let icon = 'info-circle';
        if (type === 'success') icon = 'check-circle';
        if (type === 'error') icon = 'exclamation-circle';
        if (type === 'warning') icon = 'exclamation-triangle';
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}" aria-hidden="true"></i>
            </div>
            <div class="notification-content">${message}</div>
            <button class="notification-close" aria-label="Sluiten">
                <i class="fas fa-times" aria-hidden="true"></i>
            </button>
        `;
        
        // Add to container
        container.appendChild(notification);
        
        // Add visible class after a brief delay (for animation)
        setTimeout(() => {
            notification.classList.add('visible');
        }, 10);
        
        // Close when X is clicked
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.closeNotification(notification);
        });
        
        // Auto-close after duration
        setTimeout(() => {
            this.closeNotification(notification);
        }, duration);
    }
    
    closeNotification(notification) {
        notification.classList.remove('visible');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /* ====== PROGRESS INDICATORS ====== */
    setupProgressIndicators() {
        // Set up page load progress
        this.setupPageLoadProgress();
        
        // Set up form progress for multi-step forms
        this.setupFormProgress();
    }
    
    setupPageLoadProgress() {
        // Create progress indicator
        const progressBar = document.createElement('div');
        progressBar.className = 'page-load-progress';
        document.body.appendChild(progressBar);
        
        // Update on content loaded
        document.addEventListener('DOMContentLoaded', () => {
            progressBar.style.width = '100%';
            
            setTimeout(() => {
                progressBar.style.opacity = '0';
            }, 500);
        });
    }
    
    setupFormProgress() {
        const multistepForms = document.querySelectorAll('.multi-step-form');
        
        multistepForms.forEach(form => {
            const steps = form.querySelectorAll('.form-step');
            if (steps.length <= 1) return; // Not really multi-step
            
            // Create progress indicator
            const progressContainer = document.createElement('div');
            progressContainer.className = 'form-progress';
            
            for (let i = 0; i < steps.length; i++) {
                const step = document.createElement('div');
                step.className = 'form-progress-step';
                step.setAttribute('data-step', i + 1);
                
                // Add label if available
                const stepLabel = steps[i].getAttribute('data-step-label');
                if (stepLabel) {
                    step.setAttribute('data-label', stepLabel);
                }
                
                progressContainer.appendChild(step);
            }
            
            // Add to form
            form.insertBefore(progressContainer, form.firstChild);
            
            // Update progress when step changes
            form.addEventListener('stepChange', (e) => {
                const currentStep = e.detail.currentStep;
                const progressSteps = form.querySelectorAll('.form-progress-step');
                
                progressSteps.forEach((step, index) => {
                    if (index < currentStep) {
                        step.classList.add('completed');
                    } else if (index === currentStep) {
                        step.classList.add('active');
                    } else {
                        step.classList.remove('active', 'completed');
                    }
                });
            });
        });
    }

    /* ====== FORM ENHANCEMENTS ====== */
    setupFormEnhancements() {
        this.setupInlineValidation();
        this.setupFormAutoSave();
    }
    
    setupInlineValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const fields = form.querySelectorAll('input, select, textarea');
            
            fields.forEach(field => {
                // Skip hidden fields
                if (field.type === 'hidden') return;
                
                // Show validation message on blur
                field.addEventListener('blur', () => {
                    this.validateField(field, true);
                });
                
                // Show validation as user types (but with delay)
                let typingTimer;
                field.addEventListener('input', () => {
                    clearTimeout(typingTimer);
                    
                    // Don't show errors while typing, just clear existing errors
                    if (field.classList.contains('invalid')) {
                        const errorElement = document.getElementById(`${field.id}-error`);
                        if (errorElement) {
                            errorElement.textContent = '';
                            errorElement.style.height = '0';
                        }
                    }
                    
                    // Show after short delay
                    if (field.value.length > 2) {
                        typingTimer = setTimeout(() => {
                            this.validateField(field, true);
                        }, 600);
                    }
                });
                
                // Add required indicator
                if (field.hasAttribute('required')) {
                    const label = form.querySelector(`label[for="${field.id}"]`);
                    if (label && !label.classList.contains('required-field')) {
                        label.classList.add('required-field');
                    }
                }
            });
        });
    }
    
    validateField(field, showMessage = true) {
        let isValid = true;
        let errorMessage = '';
        
        // Skip non-required empty fields
        if (!field.hasAttribute('required') && !field.value) {
            field.classList.remove('valid', 'invalid');
            return true;
        }
        
        // Required field validation
        if (field.hasAttribute('required') && !field.value) {
            isValid = false;
            errorMessage = 'Dit veld is verplicht';
        }
        
        // Email validation
        if (field.type === 'email' && field.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(field.value)) {
                isValid = false;
                errorMessage = 'Voer een geldig e-mailadres in';
            }
        }
        
        // Phone validation (optional)
        if (field.type === 'tel' && field.value) {
            const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
            if (!phoneRegex.test(field.value.replace(/\s/g, ''))) {
                isValid = false;
                errorMessage = 'Voer een geldig telefoonnummer in';
            }
        }
        
        // Add appropriate class
        field.classList.remove('valid', 'invalid');
        field.classList.add(isValid ? 'valid' : 'invalid');
        
        // Show message in the related error element
        if (showMessage) {
            const errorElement = document.getElementById(`${field.id}-error`);
            if (errorElement) {
                errorElement.textContent = errorMessage;
                errorElement.style.height = errorMessage ? 'auto' : '0';
            }
        }
        
        return isValid;
    }
    
    setupFormAutoSave() {
        const forms = document.querySelectorAll('form[data-autosave="true"]');
        
        forms.forEach(form => {
            // Create a unique key for this form
            const formId = form.id || 'form_' + Math.random().toString(36).substr(2, 9);
            const storageKey = `autosave_${formId}`;
            
            // Restore saved data if available
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                try {
                    const formData = JSON.parse(savedData);
                    Object.keys(formData).forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field && !['password', 'file'].includes(field.type)) {
                            field.value = formData[fieldName];
                        }
                    });
                    
                    // Show message that data was restored
                    this.showNotification('We hebben uw eerder ingevulde gegevens hersteld', 'info', 3000);
                    
                    // Add option to clear the saved data
                    const clearButton = document.createElement('button');
                    clearButton.type = 'button';
                    clearButton.className = 'btn btn-text';
                    clearButton.textContent = 'Verwijder opgeslagen gegevens';
                    
                    clearButton.addEventListener('click', () => {
                        localStorage.removeItem(storageKey);
                        clearButton.remove();
                        this.showNotification('Opgeslagen gegevens zijn verwijderd', 'info', 2000);
                    });
                    
                    form.appendChild(clearButton);
                } catch (e) {
                    console.error('Error restoring form data:', e);
                    localStorage.removeItem(storageKey);
                }
            }
            
            // Save data as user types
            form.addEventListener('input', () => {
                const formData = {};
                const formElements = form.elements;
                
                for (let i = 0; i < formElements.length; i++) {
                    const field = formElements[i];
                    
                    // Skip submit buttons, hidden fields, and password fields
                    if (field.type === 'submit' || field.type === 'button' || 
                        field.type === 'password' || field.type === 'file' ||
                        !field.name) {
                        continue;
                    }
                    
                    formData[field.name] = field.value;
                }
                
                // Save to localStorage
                localStorage.setItem(storageKey, JSON.stringify(formData));
            });
            
            // Clear saved data on successful submit
            form.addEventListener('submit', () => {
                localStorage.removeItem(storageKey);
            });
        });
    }

    /* ====== MICRO INTERACTIONS ====== */
    setupMicroInteractions() {
        // Add hover/focus animations to interactive elements
        this.setupButtonEffects();
        
        // Add scroll-based animations
        this.setupScrollAnimations();
    }
    
    setupButtonEffects() {
        const buttons = document.querySelectorAll('.btn, .nav-link, .service-card');
        
        buttons.forEach(button => {
            // Add ripple effect on click
            button.addEventListener('click', function(e) {
                // Avoid adding ripples to elements that already have them
                if (e.target.classList.contains('ripple')) {
                    return;
                }
                
                const ripple = document.createElement('span');
                ripple.classList.add('ripple-effect');
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                
                // Calculate position of ripple
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = `${size}px`;
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;
                
                this.appendChild(ripple);
                
                // Remove ripple after animation completes
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }
    
    setupScrollAnimations() {
        if (!('IntersectionObserver' in window)) {
            return; // Not supported
        }
        
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Add animation classes based on data attributes
        document.querySelectorAll('[data-animate]').forEach(element => {
            const animationType = element.getAttribute('data-animate');
            element.classList.add('will-animate', `animate-${animationType}`);
            
            // Add delay if specified
            const delay = element.getAttribute('data-animate-delay');
            if (delay) {
                element.style.animationDelay = `${delay}ms`;
            }
            
            observer.observe(element);
        });
    }

    /* ====== SCROLL EFFECTS ====== */
    setupScrollEffects() {
        // Add reading position indicator
        this.setupReadingProgress();
        
        // Enhance scrolling experience
        this.setupSmoothAnchors();
    }
    
    setupReadingProgress() {
        // Only add to long content pages like blog posts
        const contentArticle = document.querySelector('article.content-article');
        if (!contentArticle) {
            return;
        }
        
        // Create progress bar
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        document.body.appendChild(progressBar);
        
        // Update progress based on scroll position
        window.addEventListener('scroll', () => {
            const contentHeight = contentArticle.offsetHeight;
            const scrollPosition = window.scrollY - contentArticle.offsetTop;
            const windowHeight = window.innerHeight;
            
            // Calculate how far through the article the user has scrolled
            let progress = (scrollPosition / (contentHeight - windowHeight)) * 100;
            progress = Math.min(100, Math.max(0, progress));
            
            progressBar.style.width = `${progress}%`;
        });
    }
    
    setupSmoothAnchors() {
        document.querySelectorAll('a[href^="#"]:not([href="#"])').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    // Scroll to the element
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Update URL without reload
                    history.pushState(null, null, targetId);
                    
                    // Focus the element for accessibility
                    targetElement.setAttribute('tabindex', '-1');
                    targetElement.focus({ preventScroll: true });
                }
            });
        });
    }

    /* ====== PAGE TRANSITIONS ====== */
    setupPageTransitions() {
        const links = document.querySelectorAll('a:not([href^="#"]):not([target="_blank"]):not([href^="mailto:"]):not([href^="tel:"])');
        
        links.forEach(link => {
            // Only apply to internal links
            if (link.hostname === window.location.hostname) {
                link.addEventListener('click', e => {
                    const href = link.getAttribute('href');
                    
                    // Don't apply to files/downloads
                    if (href.match(/\.(pdf|jpg|png|zip|doc|xls|csv|txt)$/i)) {
                        return;
                    }
                    
                    e.preventDefault();
                    
                    // Add exit animation to page
                    document.body.classList.add('page-exit');
                    
                    // Navigate after animation finishes
                    setTimeout(() => {
                        window.location.href = href;
                    }, 300);
                });
            }
        });
        
        // Add entry animation when page loads
        document.addEventListener('DOMContentLoaded', () => {
            document.body.classList.add('page-enter');
        });
    }

    /* ====== USER JOURNEYS ====== */
    setupUserJourneys() {
        // Highlight recommended actions based on context
        this.setupContextualCTA();
        
        // Remember user preferences
        this.setupPreferenceRemembering();
    }
    
    setupContextualCTA() {
        // Get current page path
        const currentPath = window.location.pathname;
        
        // Define path to CTA mapping
        const ctaMap = {
            '/diensten.html': {
                selector: '.cta-contact',
                highlight: true,
                message: 'Interesse? Neem direct contact op voor een vrijblijvende offerte!'
            },
            '/blog/': {
                selector: '.cta-subscribe',
                highlight: true,
                message: 'Blijf op de hoogte van de nieuwste ontwikkelingen!'
            }
        };
        
        // See if we have a mapping for this page
        for (const path in ctaMap) {
            if (currentPath.includes(path)) {
                const config = ctaMap[path];
                const ctaElement = document.querySelector(config.selector);
                
                if (ctaElement) {
                    if (config.highlight) {
                        ctaElement.classList.add('cta-highlight');
                    }
                    
                    if (config.message) {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'cta-message';
                        messageDiv.innerHTML = config.message;
                        
                        ctaElement.appendChild(messageDiv);
                    }
                }
                
                break;
            }
        }
    }
    
    setupPreferenceRemembering() {
        // Remember view mode preferences (list/grid)
        document.querySelectorAll('[data-view-toggle]').forEach(toggle => {
            const viewType = toggle.getAttribute('data-view-toggle');
            const storageKey = `pref_view_${viewType}`;
            
            // Restore saved preference
            const savedView = localStorage.getItem(storageKey);
            if (savedView) {
                const container = document.querySelector(toggle.getAttribute('data-target'));
                if (container) {
                    container.setAttribute('data-view', savedView);
                    
                    // Update toggle buttons
                    document.querySelectorAll(`[data-view-toggle="${viewType}"]`).forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.getAttribute('data-view') === savedView) {
                            btn.classList.add('active');
                        }
                    });
                }
            }
            
            // Save preference when changed
            toggle.addEventListener('click', () => {
                const view = toggle.getAttribute('data-view');
                const target = toggle.getAttribute('data-target');
                
                const container = document.querySelector(target);
                if (container) {
                    container.setAttribute('data-view', view);
                    localStorage.setItem(storageKey, view);
                    
                    // Update toggle buttons
                    document.querySelectorAll(`[data-view-toggle="${viewType}"]`).forEach(btn => {
                        btn.classList.remove('active');
                    });
                    toggle.classList.add('active');
                }
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.uxManager = new UXManager();
});

// Add programmatic access to UX functions for other scripts
window.UX = {
    showNotification: (message, type, duration) => {
        if (window.uxManager) {
            window.uxManager.showNotification(message, type, duration);
        } else {
            console.error('UX Manager not initialized yet');
        }
    }
};
