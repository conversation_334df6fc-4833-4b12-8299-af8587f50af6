// Performance Optimizations
class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.registerServiceWorker();
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.deferNonCriticalCSS();
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('ServiceWorker registration successful');
            } catch (err) {
                console.log('ServiceWorker registration failed: ', err);
            }
        }
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            });

            // Observe all images with data-src
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers that don't support IntersectionObserver
            this.loadAllImages();
        }
    }

    loadImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }
    }

    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => this.loadImage(img));
    }

    setupImageOptimization() {
        // Add loading="lazy" to images below the fold
        document.querySelectorAll('img').forEach(img => {
            if (!this.isInViewport(img)) {
                img.loading = 'lazy';
            }
        });
    }

    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= window.innerHeight &&
            rect.right <= window.innerWidth
        );
    }

    deferNonCriticalCSS() {
        // Load non-critical CSS files
        const nonCriticalCSS = [
            '/css/blog.css',
            '/css/animations.css'
        ];

        nonCriticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.media = 'print';
            link.onload = function() {
                this.media = 'all';
            };
            document.head.appendChild(link);
        });
    }

    // Performance monitoring
    measurePerformance() {
        if ('performance' in window) {
            const timing = performance.timing;
            const performanceMetrics = {
                pageLoad: timing.loadEventEnd - timing.navigationStart,
                domReady: timing.domComplete - timing.domLoading,
                firstPaint: performance.getEntriesByType('paint')[0]?.startTime,
                firstContentfulPaint: performance.getEntriesByType('paint')[1]?.startTime
            };
            console.log('Performance Metrics:', performanceMetrics);
        }
    }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Export for use in other modules
export default performanceOptimizer;
