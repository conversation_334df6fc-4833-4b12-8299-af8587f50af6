class GalleryManager {
    constructor() {
        this.options = {
            rootMargin: '50px 0px', // Start loading images 50px before they enter viewport
            threshold: 0.1
        };
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupImageLoading();
        this.addEventListeners();
    }

    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, this.options);
        }
    }

    setupImageLoading() {
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            // Add loading state
            item.classList.add('loading');
            
            // Setup lazy loading
            const img = item.querySelector('img');
            if (img) {
                if ('loading' in HTMLImageElement.prototype) {
                    // Browser supports native lazy loading
                    img.loading = 'lazy';
                } else if (this.observer) {
                    // Use intersection observer as fallback
                    this.observer.observe(item);
                }
            }
        });
    }

    loadImage(galleryItem) {
        const img = galleryItem.querySelector('img');
        if (!img) return;

        // Start loading the high-res image
        const highResUrl = img.dataset.src;
        if (highResUrl) {
            this.preloadImage(highResUrl)
                .then(() => {
                    img.src = highResUrl;
                    galleryItem.classList.remove('loading');
                    this.addImageTransition(img);
                })
                .catch(error => {
                    console.error('Error loading image:', error);
                    galleryItem.classList.remove('loading');
                    this.handleImageError(galleryItem);
                });
        }
    }

    preloadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = resolve;
            img.onerror = reject;
            img.src = src;
        });
    }

    addImageTransition(img) {
        img.style.opacity = '0';
        requestAnimationFrame(() => {
            img.style.transition = 'opacity 0.3s ease';
            img.style.opacity = '1';
        });
    }

    handleImageError(galleryItem) {
        const fallbackMessage = document.createElement('div');
        fallbackMessage.className = 'gallery-error';
        fallbackMessage.innerHTML = `
            <i class="fas fa-image" aria-hidden="true"></i>
            <p>Image could not be loaded</p>
        `;
        galleryItem.appendChild(fallbackMessage);
    }

    addEventListeners() {
        // Handle keyboard navigation
        document.querySelectorAll('.gallery-item a').forEach(link => {
            link.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    link.click();
                }
            });
        });

        // Handle touch devices
        if ('ontouchstart' in window) {
            document.querySelectorAll('.gallery-item').forEach(item => {
                item.addEventListener('touchstart', () => {
                    item.classList.add('touch-active');
                });

                item.addEventListener('touchend', () => {
                    setTimeout(() => {
                        item.classList.remove('touch-active');
                    }, 100);
                });
            });
        }
    }

    // Method to dynamically add new gallery items
    addGalleryItem(data) {
        const { title, description, imageSrc, link } = data;
        
        const galleryGrid = document.querySelector('.gallery-grid');
        if (!galleryGrid) return;

        const item = document.createElement('div');
        item.className = 'gallery-item loading';
        
        item.innerHTML = `
            <a href="${link}" aria-labelledby="gallery-title-${Date.now()}" class="gallery-link">
                <img 
                    class="gallery-image" 
                    data-src="${imageSrc}"
                    alt="${title}"
                    width="800"
                    height="450"
                />
                <div class="gallery-caption">
                    <h3 id="gallery-title-${Date.now()}">${title}</h3>
                    <p>${description}</p>
                </div>
                <span class="screen-reader-text">View success story: ${title}</span>
            </a>
        `;

        galleryGrid.appendChild(item);
        this.setupImageLoading();
    }
}

// Initialize the gallery manager
const galleryManager = new GalleryManager();

// Export for use in other modules
export default galleryManager;
