class BlogManager {
    constructor() {
        this.posts = [];
        this.storageKey = 'techsupportpro_blog_posts';
        this.version = '1.0.0';
        this.initialize();
    }

    initialize() {
        // Load posts from local storage
        this.loadFromLocalStorage();
        this.setupEventListeners();
        this.setupAutoSave();
    }

    setupEventListeners() {
        // Export button
        document.getElementById('exportBlog')?.addEventListener('click', () => this.exportData());
        
        // Import button and file input
        document.getElementById('importBlog')?.addEventListener('click', () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => this.handleImport(e);
            input.click();
        });
    }

    setupAutoSave() {
        // Auto-save every 5 minutes
        setInterval(() => this.saveToLocalStorage(), 300000);
    }

    loadFromLocalStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                this.posts = JSON.parse(stored);
                this.showFeedback('Blog posts loaded from local storage', 'info');
            }
        } catch (error) {
            this.showFeedback('Error loading posts from local storage', 'error');
            console.error('Error loading from localStorage:', error);
        }
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.posts));
            this.showFeedback('Blog posts saved to local storage', 'success');
        } catch (error) {
            this.showFeedback('Error saving to local storage', 'error');
            console.error('Error saving to localStorage:', error);
        }
    }

    exportData() {
        const exportData = {
            version: this.version,
            timestamp: new Date().toISOString(),
            posts: this.posts
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `blog_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showFeedback('Blog posts exported successfully', 'success');
    }

    async handleImport(event) {
        try {
            const file = event.target.files[0];
            if (!file) return;

            const text = await file.text();
            const importedData = JSON.parse(text);

            // Version check and data migration if needed
            if (this.needsMigration(importedData.version)) {
                importedData.posts = await this.migrateData(importedData.posts, importedData.version);
            }

            // Merge with existing posts or replace them
            const shouldMerge = confirm('Do you want to merge with existing posts? Click OK to merge, Cancel to replace.');
            
            if (shouldMerge) {
                this.posts = this.mergePosts(this.posts, importedData.posts);
            } else {
                this.posts = importedData.posts;
            }

            this.saveToLocalStorage();
            this.showFeedback('Blog posts imported successfully', 'success');
            
            // Trigger refresh of blog display
            this.refreshBlogDisplay();

        } catch (error) {
            this.showFeedback('Error importing blog posts', 'error');
            console.error('Import error:', error);
        }
    }

    mergePosts(existing, imported) {
        // Merge based on unique IDs or timestamps
        const merged = [...existing];
        imported.forEach(importedPost => {
            const existingIndex = merged.findIndex(p => p.id === importedPost.id);
            if (existingIndex === -1) {
                merged.push(importedPost);
            } else {
                // Update existing post if imported is newer
                if (new Date(importedPost.lastModified) > new Date(merged[existingIndex].lastModified)) {
                    merged[existingIndex] = importedPost;
                }
            }
        });
        return merged;
    }

    needsMigration(importVersion) {
        return importVersion !== this.version;
    }

    async migrateData(posts, fromVersion) {
        // Add migration logic here as needed
        // Example:
        switch(fromVersion) {
            case '0.9.0':
                posts = await this.migrateFrom090(posts);
                break;
            // Add more cases as needed
        }
        return posts;
    }

    showFeedback(message, type = 'info') {
        const feedbackContainer = document.getElementById('feedbackContainer') 
            || this.createFeedbackContainer();

        const feedbackElement = document.createElement('div');
        feedbackElement.className = `feedback-message feedback-${type}`;
        
        // Add appropriate icon
        const icon = this.getFeedbackIcon(type);
        feedbackElement.innerHTML = `<i class="${icon}"></i>${message}`;
        
        feedbackContainer.appendChild(feedbackElement);
        
        // Show the message with animation
        setTimeout(() => feedbackElement.classList.add('show'), 10);

        // Remove after 5 seconds
        setTimeout(() => {
            feedbackElement.classList.remove('show');
            setTimeout(() => feedbackElement.remove(), 300);
        }, 5000);
    }

    createFeedbackContainer() {
        const container = document.createElement('div');
        container.id = 'feedbackContainer';
        container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 1000;';
        document.body.appendChild(container);
        return container;
    }

    getFeedbackIcon(type) {
        switch(type) {
            case 'success': return 'fas fa-check-circle';
            case 'error': return 'fas fa-exclamation-circle';
            case 'warning': return 'fas fa-exclamation-triangle';
            default: return 'fas fa-info-circle';
        }
    }

    refreshBlogDisplay() {
        // Implement blog display refresh logic
        const event = new CustomEvent('blogDataUpdated', { detail: this.posts });
        document.dispatchEvent(event);
    }
}

// Initialize blog manager
const blogManager = new BlogManager();

// Export for use in other modules
export default blogManager;
