function e(e,t=document){try{return t.querySelector(e)}catch(n){return null}}function t(e,t=document){try{return t.querySelectorAll(e)}catch(n){return[]}}function n(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):e()}function r(t,n="polite"){const r=e("#screen-reader-announcer")||function(){const e=function(e,t={},n=""){const r=document.createElement(e);return Object.entries(t).forEach(([e,t])=>{"className"===e?r.className=t:"dataset"===e?Object.entries(t).forEach(([e,t])=>{r.dataset[e]=t}):r.setAttribute(e,t)}),"string"==typeof n?r.innerHTML=n:n instanceof Element?r.appendChild(n):Array.isArray(n)&&n.forEach(e=>{"string"==typeof e?r.insertAdjacentHTML("beforeend",e):e instanceof Element&&r.appendChild(e)}),r}("div",{id:"screen-reader-announcer",className:"sr-only","aria-live":"polite","aria-atomic":"true"});return document.body.appendChild(e),e}();r.setAttribute("aria-live",n),r.textContent=t,setTimeout(()=>{r.textContent=""},1e3)}function a(){return window.matchMedia("(prefers-reduced-motion: reduce)").matches}export{e as $,t as a,r as b,a as p,n as r};
