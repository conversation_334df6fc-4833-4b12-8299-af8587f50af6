import{V as e,N as t,C as i}from"./components-pKGiqqTQ.js";import{r as n,$ as s,p as o,a as r,b as a}from"./utils-2gcXUZnL.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const i of e)if("childList"===i.type)for(const e of i.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const c=new class{constructor(){this.components=new Map,this.services=new Map,this.isInitialized=!1,this.init()}init(){return e=this,t=null,i=function*(){try{yield this.waitForDOM(),this.setupServices(),this.initializeComponents(),this.setupGlobalEventListeners(),this.setupAccessibility(),this.setupAnimations(),this.isInitialized=!0,this.announceReady()}catch(e){}},new Promise((n,s)=>{var o=e=>{try{a(i.next(e))}catch(t){s(t)}},r=e=>{try{a(i.throw(e))}catch(t){s(t)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(o,r);a((i=i.apply(e,t)).next())});var e,t,i}waitForDOM(){return new Promise(e=>{n(e)})}setupServices(){const t=new e("nl");this.services.set("validation",t)}initializeComponents(){this.initNavigation(),this.initContactForm(),this.initScrollEffects(),this.initFAQ()}initNavigation(){const e=s(".header nav");if(e){const i=new t(e,{mobileBreakpoint:768,closeOnOutsideClick:!0,closeOnEscape:!0});this.components.set("navigation",i),this.setActiveNavigation()}}initContactForm(){const e=s("#contactForm");if(e){const t=new i(e,{validateOnBlur:!0,showSuccessMessage:!0,resetOnSuccess:!0,trackAnalytics:!1});this.components.set("contactForm",t)}}initScrollEffects(){this.setupSmoothScrolling(),o()||this.setupScrollAnimations(),this.setupHeaderScrollEffect()}setupSmoothScrolling(){r('a[href^="#"]').forEach(e=>{e.addEventListener("click",t=>{const i=e.getAttribute("href");if("#"===i)return;const n=s(i);n&&(t.preventDefault(),n.scrollIntoView({behavior:"smooth",block:"start"}))})})}setupScrollAnimations(){const e=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(t.target.classList.add("animate-in"),e.unobserve(t.target))})},{threshold:.1,rootMargin:"0px 0px -50px 0px"});r(".fade-in-section, .service-card, .card").forEach(t=>{e.observe(t)})}setupHeaderScrollEffect(){const e=s(".header");if(!e)return;let t=!1;const i=()=>{window.scrollY>100?e.classList.add("scrolled"):e.classList.remove("scrolled"),t=!1};window.addEventListener("scroll",()=>{t||(requestAnimationFrame(i),t=!0)})}initFAQ(){const e=r(".faq-item");e.forEach(t=>{const i=t.querySelector(".faq-question"),n=t.querySelector(".faq-answer");i&&n&&(i.addEventListener("click",()=>{const s=i.classList.contains("active");e.forEach(e=>{const i=e.querySelector(".faq-question"),n=e.querySelector(".faq-answer");e!==t&&(i.classList.remove("active"),n.classList.remove("active"),i.setAttribute("aria-expanded","false"))}),s?(i.classList.remove("active"),n.classList.remove("active"),i.setAttribute("aria-expanded","false")):(i.classList.add("active"),n.classList.add("active"),i.setAttribute("aria-expanded","true"))}),i.setAttribute("aria-expanded","false"),i.setAttribute("role","button"),i.setAttribute("tabindex","0"),i.addEventListener("keydown",e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),i.click())}))})}setupGlobalEventListeners(){window.addEventListener("resize",this.handleResize.bind(this)),document.addEventListener("visibilitychange",this.handleVisibilityChange.bind(this)),window.addEventListener("error",this.handleError.bind(this))}setupAccessibility(){this.setupSkipLinks(),this.setupFocusManagement(),this.setupKeyboardNavigation()}setupSkipLinks(){r(".skip-link").forEach(e=>{e.addEventListener("click",t=>{const i=e.getAttribute("href"),n=s(i);n&&(t.preventDefault(),n.setAttribute("tabindex","-1"),n.focus(),n.scrollIntoView({behavior:"smooth"}))})})}setupFocusManagement(){document.addEventListener("keydown",e=>{"Tab"===e.key&&document.body.classList.add("keyboard-navigation")}),document.addEventListener("mousedown",()=>{document.body.classList.remove("keyboard-navigation")})}setupKeyboardNavigation(){document.addEventListener("keydown",e=>{if(e.altKey&&"m"===e.key){e.preventDefault();const t=s(".nav");if(t){const e=t.querySelector(".nav-link");e&&e.focus()}}if(e.altKey&&"c"===e.key){e.preventDefault();const t=s("#main-content");t&&(t.setAttribute("tabindex","-1"),t.focus())}})}setupAnimations(){const e=document.createElement("style");e.textContent="\n      .animate-in {\n        animation: fadeInUp 0.6s ease-out forwards;\n      }\n      \n      @keyframes fadeInUp {\n        from {\n          opacity: 0;\n          transform: translateY(30px);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0);\n        }\n      }\n      \n      .keyboard-navigation *:focus {\n        outline: 2px solid #0056b3 !important;\n        outline-offset: 2px !important;\n      }\n    ",document.head.appendChild(e)}setActiveNavigation(){const e=this.components.get("navigation");if(e){const t=window.location.pathname;e.setActive(t)}}handleResize(){clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.components.forEach(e=>{"function"==typeof e.handleResize&&e.handleResize()})},250)}handleVisibilityChange(){document.hidden}handleError(e){a("Er is een fout opgetreden. Probeer de pagina te vernieuwen.","assertive")}announceReady(){a("Website geladen en klaar voor gebruik","polite")}getComponent(e){return this.components.get(e)}getService(e){return this.services.get(e)}destroy(){this.components.forEach(e=>{"function"==typeof e.destroy&&e.destroy()}),this.components.clear(),this.services.clear(),this.isInitialized=!1}};"undefined"!=typeof window&&(window.TechSupportApp=c);
