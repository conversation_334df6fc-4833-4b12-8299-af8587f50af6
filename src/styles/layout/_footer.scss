// =============================================================================
// FOOTER
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Footer Base
// -----------------------------------------------------------------------------

.footer {
  background-color: $primary-color;
  color: $text-light;
  padding: map-get($spacers, 12) 0 map-get($spacers, 6);
  margin-top: auto;
}

.footer-content {
  @include container;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: map-get($spacers, 8);
  margin-bottom: map-get($spacers, 8);
  
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 6);
  }
}

.footer-section {
  h3 {
    color: $text-light;
    margin-bottom: map-get($spacers, 4);
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }
  
  p {
    margin-bottom: map-get($spacers, 2);
    line-height: $line-height-relaxed;
    opacity: 0.9;
  }
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      margin-bottom: map-get($spacers, 2);
      
      a {
        color: rgba($text-light, 0.8);
        text-decoration: none;
        transition: color $animation-duration-fast ease;
        
        &:hover,
        &:focus {
          color: $text-light;
          text-decoration: underline;
        }
      }
    }
  }
}

.footer-bottom {
  @include container;
  padding-top: map-get($spacers, 6);
  border-top: 1px solid rgba($text-light, 0.2);
  text-align: center;
  
  p {
    margin: 0;
    opacity: 0.7;
    font-size: $font-size-sm;
  }
}
