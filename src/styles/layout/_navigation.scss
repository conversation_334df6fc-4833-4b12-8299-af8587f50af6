// =============================================================================
// NAVIGATION
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Main Navigation
// -----------------------------------------------------------------------------

.nav {
  display: flex;
  align-items: center;
  
  @include media-breakpoint-down(md) {
    position: fixed;
    top: $header-height;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: $primary-color;
    box-shadow: $shadow-lg;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
    z-index: $z-index-dropdown;
    
    &.active {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }
}

.nav-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  
  @include media-breakpoint-down(md) {
    flex-direction: column;
    width: 100%;
    padding: map-get($spacers, 4) 0;
  }
}

.nav-item {
  position: relative;
  
  @include media-breakpoint-down(md) {
    width: 100%;
    border-bottom: 1px solid rgba($text-light, 0.1);
    
    &:last-child {
      border-bottom: none;
    }
  }
}

.nav-link {
  display: flex;
  align-items: center;
  padding: map-get($spacers, 3) map-get($spacers, 4);
  color: $text-light;
  text-decoration: none;
  font-weight: $font-weight-medium;
  font-size: $font-size-base;
  transition: all $animation-duration-fast ease;
  border-radius: $border-radius;
  
  &:hover,
  &:focus {
    color: $accent-color;
    background-color: rgba($text-light, 0.1);
    text-decoration: none;
  }
  
  &:focus-visible {
    @include focus-ring($text-light);
  }
  
  &.active {
    color: $accent-color;
    background-color: rgba($accent-color, 0.1);
  }
  
  @include media-breakpoint-down(md) {
    width: 100%;
    padding: map-get($spacers, 4) map-get($spacers, 6);
    border-radius: 0;
    
    &:hover,
    &:focus {
      background-color: rgba($text-light, 0.05);
    }
  }
}

// -----------------------------------------------------------------------------
// Dropdown Navigation
// -----------------------------------------------------------------------------

.nav-dropdown {
  position: relative;
  
  .nav-link {
    &::after {
      content: '';
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid currentColor;
      margin-left: map-get($spacers, 2);
      transition: transform $animation-duration-fast ease;
    }
    
    &[aria-expanded='true']::after {
      transform: rotate(180deg);
    }
  }
}

.nav-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background-color: $bg-primary;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  box-shadow: $shadow-lg;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  z-index: $z-index-dropdown;
  
  &.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  
  @include media-breakpoint-down(md) {
    position: static;
    min-width: auto;
    background-color: rgba($text-light, 0.05);
    border: none;
    border-radius: 0;
    box-shadow: none;
    opacity: 1;
    visibility: visible;
    transform: none;
    margin-top: map-get($spacers, 2);
  }
}

.nav-dropdown-item {
  .nav-link {
    color: $text-primary;
    padding: map-get($spacers, 3) map-get($spacers, 4);
    
    &:hover,
    &:focus {
      color: $primary-color;
      background-color: $bg-secondary;
    }
    
    @include media-breakpoint-down(md) {
      color: $text-light;
      padding-left: map-get($spacers, 8);
      
      &:hover,
      &:focus {
        color: $accent-color;
        background-color: rgba($text-light, 0.05);
      }
    }
  }
}

// -----------------------------------------------------------------------------
// Language Switcher
// -----------------------------------------------------------------------------

.language-switcher {
  .nav-link {
    display: flex;
    align-items: center;
    gap: map-get($spacers, 2);
  }
  
  .flag-icon {
    width: 20px;
    height: 15px;
    border-radius: 2px;
    object-fit: cover;
  }
  
  .language-text {
    @include media-breakpoint-down(sm) {
      @include visually-hidden;
    }
  }
}

// -----------------------------------------------------------------------------
// Mobile Navigation Overlay
// -----------------------------------------------------------------------------

.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba($text-primary, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all $animation-duration-base ease;
  z-index: $z-index-dropdown - 1;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
  
  @include media-breakpoint-up(md) {
    display: none;
  }
}

// -----------------------------------------------------------------------------
// Breadcrumb Navigation
// -----------------------------------------------------------------------------

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: map-get($spacers, 3) 0;
  margin-bottom: map-get($spacers, 4);
  list-style: none;
  background-color: $bg-secondary;
  border-radius: $border-radius;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  
  &:not(:last-child)::after {
    content: '/';
    margin: 0 map-get($spacers, 2);
    color: $text-muted;
  }
  
  a {
    color: $primary-color;
    text-decoration: none;
    
    &:hover,
    &:focus {
      text-decoration: underline;
    }
  }
  
  &.active {
    color: $text-muted;
  }
}

// -----------------------------------------------------------------------------
// Accessibility Enhancements
// -----------------------------------------------------------------------------

// Screen reader announcements
.nav-announcer {
  @include visually-hidden;
}

// Focus management
.nav-link:focus {
  outline: 2px solid $accent-color;
  outline-offset: 2px;
}

// Keyboard navigation
.nav-item {
  &:focus-within {
    .nav-dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }
}

// -----------------------------------------------------------------------------
// Animation Classes
// -----------------------------------------------------------------------------

.nav-fade-in {
  @include fade-in;
}

.nav-slide-down {
  @include slide-up($animation-duration-base, -20px);
}
