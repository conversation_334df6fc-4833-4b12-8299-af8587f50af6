// =============================================================================
// HEADER
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Header Base
// -----------------------------------------------------------------------------

.header {
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  background-color: $primary-color;
  box-shadow: $shadow-sm;
  transition: all $animation-duration-base ease;
  
  &.scrolled {
    box-shadow: $shadow-md;
  }
}

.header-container {
  @include container;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: $header-height;
  padding-top: map-get($spacers, 3);
  padding-bottom: map-get($spacers, 3);
}

// -----------------------------------------------------------------------------
// Logo
// -----------------------------------------------------------------------------

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: $text-light;
  font-weight: $font-weight-bold;
  font-size: $font-size-lg;
  
  &:hover,
  &:focus {
    color: $text-light;
    text-decoration: none;
  }
  
  img {
    height: 40px;
    width: auto;
    
    @include media-breakpoint-down(sm) {
      height: 32px;
    }
  }
}

// -----------------------------------------------------------------------------
// Skip Links (Accessibility)
// -----------------------------------------------------------------------------

.skip-links {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: $z-index-modal;
  
  .skip-link {
    position: absolute;
    top: 0;
    left: 0;
    padding: map-get($spacers, 2) map-get($spacers, 4);
    background-color: $primary-color;
    color: $text-light;
    text-decoration: none;
    font-weight: $font-weight-medium;
    border-radius: 0 0 $border-radius $border-radius;
    transition: top $animation-duration-fast ease;
    
    &:focus {
      top: 100px;
    }
  }
}

// -----------------------------------------------------------------------------
// Mobile Menu Toggle
// -----------------------------------------------------------------------------

.mobile-menu-toggle {
  @include button-reset;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  color: $text-light;
  border-radius: $border-radius;
  transition: background-color $animation-duration-fast ease;
  
  &:hover,
  &:focus {
    background-color: rgba($text-light, 0.1);
  }
  
  &:focus-visible {
    @include focus-ring($text-light);
  }
  
  @include media-breakpoint-down(md) {
    display: flex;
  }
  
  .hamburger-line {
    width: 20px;
    height: 2px;
    background-color: currentColor;
    transition: all $animation-duration-base ease;
    
    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
  
  &[aria-expanded='true'] {
    .hamburger-line {
      &:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
      }
      
      &:nth-child(2) {
        opacity: 0;
      }
      
      &:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
      }
    }
  }
}

// -----------------------------------------------------------------------------
// Responsive Behavior
// -----------------------------------------------------------------------------

@include media-breakpoint-down(sm) {
  .header-container {
    padding-top: map-get($spacers, 2);
    padding-bottom: map-get($spacers, 2);
  }
  
  .logo {
    font-size: $font-size-base;
  }
}
