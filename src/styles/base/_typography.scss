// =============================================================================
// TYPOGRAPHY
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Base Typography
// -----------------------------------------------------------------------------

html {
  font-size: 16px; // Base font size
  
  @include media-breakpoint-down(sm) {
    font-size: 14px; // Smaller base on mobile
  }
}

body {
  font-family: $font-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  color: $text-primary;
  background-color: $bg-primary;
}

// -----------------------------------------------------------------------------
// Headings
// -----------------------------------------------------------------------------

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $font-heading;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  margin-bottom: map-get($spacers, 4);
  color: $text-primary;
}

h1 {
  @include font-size($font-size-5xl);
  
  @include media-breakpoint-down(md) {
    @include font-size($font-size-4xl);
  }
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-3xl);
  }
}

h2 {
  @include font-size($font-size-4xl);
  
  @include media-breakpoint-down(md) {
    @include font-size($font-size-3xl);
  }
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-2xl);
  }
}

h3 {
  @include font-size($font-size-3xl);
  
  @include media-breakpoint-down(md) {
    @include font-size($font-size-2xl);
  }
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-xl);
  }
}

h4 {
  @include font-size($font-size-2xl);
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-xl);
  }
}

h5 {
  @include font-size($font-size-xl);
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-lg);
  }
}

h6 {
  @include font-size($font-size-lg);
  
  @include media-breakpoint-down(sm) {
    @include font-size($font-size-base);
  }
}

// -----------------------------------------------------------------------------
// Body Text
// -----------------------------------------------------------------------------

p {
  margin-bottom: map-get($spacers, 4);
  max-width: 70ch; // Optimal reading length
}

.lead {
  @include font-size($font-size-lg);
  font-weight: $font-weight-normal;
  line-height: $line-height-relaxed;
  color: $text-secondary;
}

.small {
  @include font-size($font-size-sm);
}

.large {
  @include font-size($font-size-lg);
}

// -----------------------------------------------------------------------------
// Links
// -----------------------------------------------------------------------------

a {
  color: $primary-color;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
  transition: color $animation-duration-fast ease;
  
  &:hover,
  &:focus {
    color: $primary-dark;
    text-decoration-thickness: 2px;
  }
  
  &:focus-visible {
    @include focus-ring;
    text-decoration: none;
  }
}

// -----------------------------------------------------------------------------
// Lists
// -----------------------------------------------------------------------------

ul,
ol {
  margin-bottom: map-get($spacers, 4);
  padding-left: map-get($spacers, 6);
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: map-get($spacers, 2);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// Nested lists
ul ul,
ol ol,
ul ol,
ol ul {
  margin-bottom: 0;
  margin-top: map-get($spacers, 2);
}

// Definition lists
dl {
  margin-bottom: map-get($spacers, 4);
}

dt {
  font-weight: $font-weight-bold;
  margin-bottom: map-get($spacers, 1);
}

dd {
  margin-bottom: map-get($spacers, 3);
  margin-left: map-get($spacers, 4);
}

// -----------------------------------------------------------------------------
// Code & Preformatted Text
// -----------------------------------------------------------------------------

code,
kbd,
samp {
  font-family: $font-mono;
  font-size: 0.875em;
  background-color: $bg-secondary;
  padding: 0.125rem 0.25rem;
  border-radius: $border-radius-sm;
  color: $text-primary;
}

pre {
  font-family: $font-mono;
  font-size: $font-size-sm;
  background-color: $bg-secondary;
  padding: map-get($spacers, 4);
  border-radius: $border-radius;
  overflow-x: auto;
  margin-bottom: map-get($spacers, 4);
  line-height: $line-height-relaxed;
  
  code {
    background: none;
    padding: 0;
    border-radius: 0;
    font-size: inherit;
  }
}

// -----------------------------------------------------------------------------
// Blockquotes
// -----------------------------------------------------------------------------

blockquote {
  margin: map-get($spacers, 6) 0;
  padding: map-get($spacers, 4) map-get($spacers, 6);
  border-left: 4px solid $primary-color;
  background-color: $bg-secondary;
  font-style: italic;
  
  p {
    margin-bottom: map-get($spacers, 3);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  footer {
    margin-top: map-get($spacers, 3);
    font-style: normal;
    font-size: $font-size-sm;
    color: $text-secondary;
    
    &::before {
      content: '— ';
    }
  }
}

// -----------------------------------------------------------------------------
// Text Utilities
// -----------------------------------------------------------------------------

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-truncate {
  @include text-truncate;
}

.text-break {
  word-wrap: break-word;
  word-break: break-word;
}

// -----------------------------------------------------------------------------
// Font Weight Utilities
// -----------------------------------------------------------------------------

.font-light {
  font-weight: $font-weight-light;
}

.font-normal {
  font-weight: $font-weight-normal;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

.font-bold {
  font-weight: $font-weight-bold;
}

// -----------------------------------------------------------------------------
// Color Utilities
// -----------------------------------------------------------------------------

.text-primary {
  color: $text-primary;
}

.text-secondary {
  color: $text-secondary;
}

.text-muted {
  color: $text-muted;
}

.text-light {
  color: $text-light;
}

.text-brand {
  color: $primary-color;
}

.text-accent {
  color: $accent-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-info {
  color: $info-color;
}
