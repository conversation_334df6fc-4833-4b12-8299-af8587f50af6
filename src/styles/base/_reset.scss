// =============================================================================
// RESET
// =============================================================================

@use '../abstracts/variables' as *;

// -----------------------------------------------------------------------------
// Modern CSS Reset
// Based on <PERSON>'s CSS Reset
// -----------------------------------------------------------------------------

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: $line-height-normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

// -----------------------------------------------------------------------------
// Additional Resets
// -----------------------------------------------------------------------------

// Remove default list styles
ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

// Remove default button styles
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

// Remove default link styles
a {
  color: inherit;
  text-decoration: none;
}

// Remove default table styles
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// Remove default fieldset styles
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

// Remove default legend styles
legend {
  padding: 0;
}

// Remove default details/summary styles
details {
  display: block;
}

summary {
  display: list-item;
}

// Remove default hr styles
hr {
  border: none;
  height: 1px;
  background-color: $border-color;
  margin: map-get($spacers, 6) 0;
}

// Remove default blockquote styles
blockquote {
  margin: 0;
  padding: 0;
}

// Remove default address styles
address {
  font-style: normal;
}

// Remove default abbr styles
abbr[title] {
  text-decoration: underline dotted;
}

// Remove default code styles
code,
kbd,
samp,
pre {
  font-family: $font-mono;
  font-size: 1em;
}

// Remove default small styles
small {
  font-size: 80%;
}

// Remove default sub/sup styles
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

// -----------------------------------------------------------------------------
// Focus Management
// -----------------------------------------------------------------------------

// Remove default focus styles for mouse users
:focus:not(:focus-visible) {
  outline: none;
}

// Ensure focus styles for keyboard users
:focus-visible {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

// -----------------------------------------------------------------------------
// Print Styles
// -----------------------------------------------------------------------------

@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: ' (' attr(href) ')';
  }

  abbr[title]::after {
    content: ' (' attr(title) ')';
  }

  a[href^='#']::after,
  a[href^='javascript:']::after {
    content: '';
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
