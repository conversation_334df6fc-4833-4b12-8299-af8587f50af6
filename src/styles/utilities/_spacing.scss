// =============================================================================
// SPACING UTILITIES
// =============================================================================

@use '../abstracts/variables' as *;

// -----------------------------------------------------------------------------
// Margin Utilities
// -----------------------------------------------------------------------------

@each $key, $value in $spacers {
  .m-#{$key} { margin: $value; }
  .mt-#{$key} { margin-top: $value; }
  .mr-#{$key} { margin-right: $value; }
  .mb-#{$key} { margin-bottom: $value; }
  .ml-#{$key} { margin-left: $value; }
  .mx-#{$key} { margin-left: $value; margin-right: $value; }
  .my-#{$key} { margin-top: $value; margin-bottom: $value; }
}

// -----------------------------------------------------------------------------
// Padding Utilities
// -----------------------------------------------------------------------------

@each $key, $value in $spacers {
  .p-#{$key} { padding: $value; }
  .pt-#{$key} { padding-top: $value; }
  .pr-#{$key} { padding-right: $value; }
  .pb-#{$key} { padding-bottom: $value; }
  .pl-#{$key} { padding-left: $value; }
  .px-#{$key} { padding-left: $value; padding-right: $value; }
  .py-#{$key} { padding-top: $value; padding-bottom: $value; }
}

// -----------------------------------------------------------------------------
// Auto Margins
// -----------------------------------------------------------------------------

.m-auto { margin: auto; }
.mt-auto { margin-top: auto; }
.mr-auto { margin-right: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }
