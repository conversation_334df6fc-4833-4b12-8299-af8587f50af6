// =============================================================================
// TEXT UTILITIES
// =============================================================================

@use '../abstracts/variables' as *;

// -----------------------------------------------------------------------------
// Text Alignment
// -----------------------------------------------------------------------------

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// -----------------------------------------------------------------------------
// Text Transform
// -----------------------------------------------------------------------------

.text-lowercase { text-transform: lowercase; }
.text-uppercase { text-transform: uppercase; }
.text-capitalize { text-transform: capitalize; }

// -----------------------------------------------------------------------------
// Text Decoration
// -----------------------------------------------------------------------------

.text-decoration-none { text-decoration: none; }
.text-decoration-underline { text-decoration: underline; }
.text-decoration-line-through { text-decoration: line-through; }

// -----------------------------------------------------------------------------
// Font Weight
// -----------------------------------------------------------------------------

.font-weight-light { font-weight: $font-weight-light; }
.font-weight-normal { font-weight: $font-weight-normal; }
.font-weight-medium { font-weight: $font-weight-medium; }
.font-weight-semibold { font-weight: $font-weight-semibold; }
.font-weight-bold { font-weight: $font-weight-bold; }

// -----------------------------------------------------------------------------
// Font Style
// -----------------------------------------------------------------------------

.font-italic { font-style: italic; }
.font-normal { font-style: normal; }

// -----------------------------------------------------------------------------
// Line Height
// -----------------------------------------------------------------------------

.lh-1 { line-height: 1; }
.lh-sm { line-height: $line-height-tight; }
.lh-base { line-height: $line-height-normal; }
.lh-lg { line-height: $line-height-relaxed; }

// -----------------------------------------------------------------------------
// Text Colors
// -----------------------------------------------------------------------------

.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-muted { color: $text-muted; }
.text-light { color: $text-light; }

.text-brand { color: $primary-color; }
.text-accent { color: $accent-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $error-color; }
.text-info { color: $info-color; }

// -----------------------------------------------------------------------------
// Text Sizes
// -----------------------------------------------------------------------------

.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }
.text-5xl { font-size: $font-size-5xl; }
