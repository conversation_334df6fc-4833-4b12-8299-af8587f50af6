// =============================================================================
// DISPLAY UTILITIES
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Display Properties
// -----------------------------------------------------------------------------

.d-none { display: none; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.d-grid { display: grid; }
.d-table { display: table; }
.d-table-cell { display: table-cell; }

// -----------------------------------------------------------------------------
// Visibility
// -----------------------------------------------------------------------------

.visible { visibility: visible; }
.invisible { visibility: hidden; }

// -----------------------------------------------------------------------------
// Screen Reader Only
// -----------------------------------------------------------------------------

.sr-only,
.visually-hidden {
  @include visually-hidden;
}

// -----------------------------------------------------------------------------
// Responsive Display
// -----------------------------------------------------------------------------

@each $breakpoint, $value in $breakpoints {
  @if $value > 0 {
    @include media-breakpoint-up($breakpoint) {
      .d-#{$breakpoint}-none { display: none; }
      .d-#{$breakpoint}-inline { display: inline; }
      .d-#{$breakpoint}-inline-block { display: inline-block; }
      .d-#{$breakpoint}-block { display: block; }
      .d-#{$breakpoint}-flex { display: flex; }
      .d-#{$breakpoint}-inline-flex { display: inline-flex; }
      .d-#{$breakpoint}-grid { display: grid; }
    }
  }
}

// -----------------------------------------------------------------------------
// Overflow
// -----------------------------------------------------------------------------

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }
