// =============================================================================
// FORMS
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Form Base
// -----------------------------------------------------------------------------

.form {
  width: 100%;
  max-width: 600px;
}

.form-group {
  margin-bottom: map-get($spacers, 5);
}

.form-row {
  display: flex;
  gap: map-get($spacers, 4);
  
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: map-get($spacers, 3);
  }
}

.form-col {
  flex: 1;
}

// -----------------------------------------------------------------------------
// Labels
// -----------------------------------------------------------------------------

.form-label {
  display: block;
  margin-bottom: map-get($spacers, 2);
  font-weight: $font-weight-medium;
  color: $text-primary;
  font-size: $font-size-sm;
  
  &.required::after {
    content: ' *';
    color: $error-color;
  }
}

.form-label-inline {
  display: inline-block;
  margin-right: map-get($spacers, 3);
  margin-bottom: 0;
}

// -----------------------------------------------------------------------------
// Input Fields
// -----------------------------------------------------------------------------

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: $input-padding-y $input-padding-x;
  font-size: $font-size-base;
  font-family: inherit;
  line-height: $line-height-normal;
  color: $text-primary;
  background-color: $bg-primary;
  border: $input-border-width solid $input-border-color;
  border-radius: $input-border-radius;
  transition: border-color $animation-duration-fast ease,
              box-shadow $animation-duration-fast ease;
  
  @include placeholder($text-muted);
  
  &:focus {
    outline: none;
    border-color: $input-focus-border-color;
    box-shadow: $input-focus-box-shadow;
  }
  
  &:disabled {
    background-color: $bg-secondary;
    color: $text-muted;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  &[aria-invalid='true'] {
    border-color: $error-color;
    
    &:focus {
      border-color: $error-color;
      box-shadow: 0 0 0 0.2rem rgba($error-color, 0.25);
    }
  }
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $input-padding-x center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: calc(#{$input-padding-x} + 2em);
  cursor: pointer;
  
  &::-ms-expand {
    display: none;
  }
}

// -----------------------------------------------------------------------------
// Input Sizes
// -----------------------------------------------------------------------------

.form-input-sm,
.form-textarea-sm,
.form-select-sm {
  padding: $btn-padding-y-sm $btn-padding-x-sm;
  font-size: $btn-font-size-sm;
}

.form-input-lg,
.form-textarea-lg,
.form-select-lg {
  padding: $btn-padding-y-lg $btn-padding-x-lg;
  font-size: $btn-font-size-lg;
}

// -----------------------------------------------------------------------------
// Checkboxes & Radio Buttons
// -----------------------------------------------------------------------------

.form-check {
  display: flex;
  align-items: flex-start;
  gap: map-get($spacers, 3);
  margin-bottom: map-get($spacers, 3);
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem; // Align with first line of text
  border: 2px solid $input-border-color;
  border-radius: $border-radius-sm;
  background-color: $bg-primary;
  cursor: pointer;
  transition: all $animation-duration-fast ease;
  
  &:focus {
    outline: none;
    border-color: $input-focus-border-color;
    box-shadow: $input-focus-box-shadow;
  }
  
  &:checked {
    background-color: $primary-color;
    border-color: $primary-color;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
    background-size: 0.75rem;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  &[type='radio'] {
    border-radius: 50%;
    
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    }
  }
  
  &:disabled {
    background-color: $bg-secondary;
    border-color: $border-color;
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.form-check-label {
  font-size: $font-size-sm;
  line-height: $line-height-normal;
  cursor: pointer;
  
  &:has(+ .form-check-input:disabled) {
    color: $text-muted;
    cursor: not-allowed;
  }
}

// -----------------------------------------------------------------------------
// Input Groups
// -----------------------------------------------------------------------------

.input-group {
  display: flex;
  width: 100%;
  
  .form-input {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: $input-border-radius;
      border-bottom-left-radius: $input-border-radius;
    }
    
    &:last-child {
      border-top-right-radius: $input-border-radius;
      border-bottom-right-radius: $input-border-radius;
    }
    
    &:not(:first-child) {
      border-left: 0;
    }
    
    &:focus {
      z-index: 1;
    }
  }
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: $input-padding-y $input-padding-x;
  font-size: $font-size-sm;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  color: $text-secondary;
  text-align: center;
  white-space: nowrap;
  background-color: $bg-secondary;
  border: $input-border-width solid $input-border-color;
  
  &:first-child {
    border-top-left-radius: $input-border-radius;
    border-bottom-left-radius: $input-border-radius;
    border-right: 0;
  }
  
  &:last-child {
    border-top-right-radius: $input-border-radius;
    border-bottom-right-radius: $input-border-radius;
    border-left: 0;
  }
}

// -----------------------------------------------------------------------------
// Form Validation
// -----------------------------------------------------------------------------

.form-error {
  display: block;
  margin-top: map-get($spacers, 1);
  font-size: $font-size-xs;
  color: $error-color;
  
  &:empty {
    display: none;
  }
}

.form-help {
  display: block;
  margin-top: map-get($spacers, 1);
  font-size: $font-size-xs;
  color: $text-muted;
}

.form-success {
  padding: map-get($spacers, 6);
  text-align: center;
  background-color: rgba($success-color, 0.1);
  border: 1px solid rgba($success-color, 0.3);
  border-radius: $border-radius;
  
  .success-icon {
    margin: 0 auto map-get($spacers, 4);
    width: 64px;
    height: 64px;
  }
  
  h3 {
    color: $success-color;
    margin-bottom: map-get($spacers, 3);
  }
  
  p {
    margin-bottom: map-get($spacers, 2);
    
    &:last-of-type {
      margin-bottom: map-get($spacers, 4);
    }
  }
}

// -----------------------------------------------------------------------------
// Form States
// -----------------------------------------------------------------------------

.form-loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($bg-primary, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
  }
}

// -----------------------------------------------------------------------------
// Responsive Forms
// -----------------------------------------------------------------------------

@include media-breakpoint-down(sm) {
  .form-row {
    flex-direction: column;
  }
  
  .input-group {
    flex-direction: column;
    
    .form-input,
    .input-group-text {
      border-radius: $input-border-radius;
      border: $input-border-width solid $input-border-color;
    }
    
    .form-input:not(:first-child),
    .input-group-text:not(:first-child) {
      margin-top: -1px;
    }
  }
}
