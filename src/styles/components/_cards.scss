// =============================================================================
// CARDS
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Base Card
// -----------------------------------------------------------------------------

.card {
  background-color: $bg-primary;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  overflow: hidden;
  transition: all $animation-duration-base ease;
  @include card-shadow(1);
  
  &:hover {
    @include card-shadow(2);
    @include hover-lift;
  }
}

.card-header {
  padding: map-get($spacers, 4) map-get($spacers, 5);
  border-bottom: 1px solid $border-light;
  background-color: $bg-secondary;
  
  h3,
  h4,
  h5 {
    margin-bottom: 0;
  }
}

.card-body {
  padding: map-get($spacers, 5);
}

.card-footer {
  padding: map-get($spacers, 4) map-get($spacers, 5);
  border-top: 1px solid $border-light;
  background-color: $bg-secondary;
}

// -----------------------------------------------------------------------------
// Service Cards
// -----------------------------------------------------------------------------

.service-card {
  @extend .card;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  
  &:hover {
    transform: translateY(-8px);
    @include card-shadow(3);
  }
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: $primary-color;
  color: $text-light;
  border-radius: 50%;
  font-size: $font-size-3xl;
  margin: 0 auto map-get($spacers, 4);
  transition: all $animation-duration-base ease;
  
  .service-card:hover & {
    background-color: $accent-color;
    transform: scale(1.1);
  }
}

.service-content {
  padding: map-get($spacers, 6);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  text-align: center;
  
  h3 {
    color: $primary-color;
    margin-bottom: map-get($spacers, 3);
    font-size: $font-size-xl;
  }
  
  .service-intro {
    color: $text-secondary;
    margin-bottom: map-get($spacers, 4);
    line-height: $line-height-relaxed;
  }
  
  .service-details {
    list-style: none;
    padding: 0;
    margin-bottom: map-get($spacers, 6);
    text-align: left;
    
    li {
      margin-bottom: map-get($spacers, 3);
      padding-left: map-get($spacers, 6);
      position: relative;
      
      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: $success-color;
        font-weight: $font-weight-bold;
      }
      
      strong {
        color: $primary-color;
      }
    }
  }
  
  .btn {
    margin-top: auto;
  }
}

// -----------------------------------------------------------------------------
// Services Grid
// -----------------------------------------------------------------------------

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: map-get($spacers, 8);
  margin-top: map-get($spacers, 8);
  
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 6);
  }
}

// -----------------------------------------------------------------------------
// Card Variants
// -----------------------------------------------------------------------------

.card--elevated {
  @include card-shadow(2);
  
  &:hover {
    @include card-shadow(4);
  }
}

.card--bordered {
  border: 2px solid $border-color;
}

.card--primary {
  border-color: $primary-color;
  
  .card-header {
    background-color: $primary-color;
    color: $text-light;
    border-bottom-color: $primary-color;
  }
}

.card--accent {
  border-color: $accent-color;
  
  .card-header {
    background-color: $accent-color;
    color: $text-primary;
    border-bottom-color: $accent-color;
  }
}

// -----------------------------------------------------------------------------
// Card Image
// -----------------------------------------------------------------------------

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  
  &-top {
    border-radius: $border-radius $border-radius 0 0;
  }
  
  &-bottom {
    border-radius: 0 0 $border-radius $border-radius;
  }
}

// -----------------------------------------------------------------------------
// Card Actions
// -----------------------------------------------------------------------------

.card-actions {
  display: flex;
  gap: map-get($spacers, 3);
  align-items: center;
  
  &--end {
    justify-content: flex-end;
  }
  
  &--center {
    justify-content: center;
  }
  
  &--between {
    justify-content: space-between;
  }
}

// -----------------------------------------------------------------------------
// Responsive Cards
// -----------------------------------------------------------------------------

@include media-breakpoint-down(md) {
  .service-content {
    padding: map-get($spacers, 4);
    
    h3 {
      font-size: $font-size-lg;
    }
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
    font-size: $font-size-2xl;
  }
}

@include media-breakpoint-down(sm) {
  .card-body {
    padding: map-get($spacers, 4);
  }
  
  .card-header,
  .card-footer {
    padding: map-get($spacers, 3) map-get($spacers, 4);
  }
}
