// =============================================================================
// MODALS
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Modal Base
// -----------------------------------------------------------------------------

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: $z-index-modal;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all $animation-duration-base ease;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($text-primary, 0.5);
  backdrop-filter: blur(2px);
}

.modal-content {
  position: relative;
  background-color: $bg-primary;
  border-radius: $border-radius-lg;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  @include card-shadow(4);
  transform: scale(0.9);
  transition: transform $animation-duration-base ease;
  
  .modal.active & {
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: map-get($spacers, 5) map-get($spacers, 6);
  border-bottom: 1px solid $border-light;
  
  h2,
  h3,
  h4 {
    margin: 0;
    color: $primary-color;
  }
}

.modal-body {
  padding: map-get($spacers, 6);
}

.modal-footer {
  display: flex;
  gap: map-get($spacers, 3);
  justify-content: flex-end;
  padding: map-get($spacers, 5) map-get($spacers, 6);
  border-top: 1px solid $border-light;
}

.modal-close {
  @include button-reset;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: $text-muted;
  transition: all $animation-duration-fast ease;
  
  &:hover,
  &:focus {
    background-color: $bg-secondary;
    color: $text-primary;
  }
  
  &:focus-visible {
    @include focus-ring;
  }
}

// -----------------------------------------------------------------------------
// Modal Sizes
// -----------------------------------------------------------------------------

.modal-sm .modal-content {
  max-width: 400px;
}

.modal-md .modal-content {
  max-width: 600px;
}

.modal-lg .modal-content {
  max-width: 800px;
}

.modal-xl .modal-content {
  max-width: 1200px;
}

// -----------------------------------------------------------------------------
// Responsive
// -----------------------------------------------------------------------------

@include media-breakpoint-down(sm) {
  .modal-content {
    max-width: 95vw;
    margin: map-get($spacers, 4);
  }
  
  .modal-header,
  .modal-footer {
    padding: map-get($spacers, 4);
  }
  
  .modal-body {
    padding: map-get($spacers, 4);
  }
}
