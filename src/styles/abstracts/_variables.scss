// =============================================================================
// VARIABLES
// =============================================================================

// -----------------------------------------------------------------------------
// Colors
// -----------------------------------------------------------------------------

// Brand Colors
$primary-color: #0056b3;
$primary-light: lighten($primary-color, 10%);
$primary-dark: darken($primary-color, 10%);

$secondary-color: #f8f9fa;
$secondary-light: lighten($secondary-color, 5%);
$secondary-dark: darken($secondary-color, 5%);

$accent-color: #e69c00;
$accent-light: lighten($accent-color, 10%);
$accent-dark: darken($accent-color, 10%);

// Semantic Colors
$success-color: #28a745;
$warning-color: #ffc107;
$error-color: #dc3545;
$info-color: #17a2b8;

// Text Colors
$text-primary: #1a1a1a;
$text-secondary: #6c757d;
$text-muted: #868e96;
$text-light: #ffffff;

// Background Colors
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-dark: #343a40;

// Border Colors
$border-color: #dee2e6;
$border-light: #e9ecef;
$border-dark: #adb5bd;

// -----------------------------------------------------------------------------
// Typography
// -----------------------------------------------------------------------------

// Font Families
$font-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
$font-heading: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
$font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

// Font Sizes
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Line Heights
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// -----------------------------------------------------------------------------
// Spacing
// -----------------------------------------------------------------------------

$spacer: 1rem;

$spacers: (
  0: 0,
  1: $spacer * 0.25,    // 4px
  2: $spacer * 0.5,     // 8px
  3: $spacer * 0.75,    // 12px
  4: $spacer,           // 16px
  5: $spacer * 1.25,    // 20px
  6: $spacer * 1.5,     // 24px
  8: $spacer * 2,       // 32px
  10: $spacer * 2.5,    // 40px
  12: $spacer * 3,      // 48px
  16: $spacer * 4,      // 64px
  20: $spacer * 5,      // 80px
  24: $spacer * 6,      // 96px
  32: $spacer * 8,      // 128px
);

// -----------------------------------------------------------------------------
// Breakpoints
// -----------------------------------------------------------------------------

$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// -----------------------------------------------------------------------------
// Grid System
// -----------------------------------------------------------------------------

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

$grid-columns: 12;
$grid-gutter-width: 1.5rem;

// -----------------------------------------------------------------------------
// Components
// -----------------------------------------------------------------------------

// Border Radius
$border-radius-sm: 0.25rem;
$border-radius: 0.375rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 0.75rem;
$border-radius-2xl: 1rem;
$border-radius-full: 9999px;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Transitions
$transition-fast: 150ms ease-in-out;
$transition-base: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// -----------------------------------------------------------------------------
// Forms
// -----------------------------------------------------------------------------

$input-padding-y: 0.75rem;
$input-padding-x: 1rem;
$input-border-width: 1px;
$input-border-color: $border-color;
$input-border-radius: $border-radius;
$input-focus-border-color: $primary-color;
$input-focus-box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);

// -----------------------------------------------------------------------------
// Buttons
// -----------------------------------------------------------------------------

$btn-padding-y: 0.75rem;
$btn-padding-x: 1.5rem;
$btn-border-width: 1px;
$btn-border-radius: $border-radius;
$btn-font-weight: $font-weight-medium;

// Button Sizes
$btn-padding-y-sm: 0.5rem;
$btn-padding-x-sm: 1rem;
$btn-font-size-sm: $font-size-sm;

$btn-padding-y-lg: 1rem;
$btn-padding-x-lg: 2rem;
$btn-font-size-lg: $font-size-lg;

// -----------------------------------------------------------------------------
// Animation & Transitions
// -----------------------------------------------------------------------------

$animation-duration-fast: 150ms;
$animation-duration-base: 250ms;
$animation-duration-slow: 350ms;

$animation-curve-fast-out-slow-in: cubic-bezier(0.4, 0, 0.2, 1);
$animation-curve-linear-out-slow-in: cubic-bezier(0, 0, 0.2, 1);
$animation-curve-fast-out-linear-in: cubic-bezier(0.4, 0, 1, 1);

// -----------------------------------------------------------------------------
// Layout
// -----------------------------------------------------------------------------

$header-height: 70px;
$footer-height: auto;
$sidebar-width: 280px;

// Content widths
$content-width-sm: 540px;
$content-width-md: 720px;
$content-width-lg: 960px;
$content-width-xl: 1140px;
$content-width-xxl: 1320px;
