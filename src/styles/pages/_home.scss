// =============================================================================
// HOME PAGE
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Hero Section
// -----------------------------------------------------------------------------

.hero {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: $text-light;
  padding: map-get($spacers, 16) 0 map-get($spacers, 12);
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.1;
    pointer-events: none;
  }
  
  .container {
    position: relative;
    z-index: 1;
  }
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: $font-size-5xl;
  font-weight: $font-weight-bold;
  margin-bottom: map-get($spacers, 6);
  line-height: $line-height-tight;
  
  @include media-breakpoint-down(md) {
    font-size: $font-size-4xl;
  }
  
  @include media-breakpoint-down(sm) {
    font-size: $font-size-3xl;
  }
  
  .highlight {
    color: $accent-color;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background-color: $accent-color;
      border-radius: 2px;
    }
  }
}

.hero-subtitle {
  font-size: $font-size-xl;
  margin-bottom: map-get($spacers, 8);
  opacity: 0.9;
  line-height: $line-height-relaxed;
  
  @include media-breakpoint-down(sm) {
    font-size: $font-size-lg;
  }
}

.hero-buttons {
  display: flex;
  gap: map-get($spacers, 4);
  justify-content: center;
  flex-wrap: wrap;
  
  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: center;
  }
}

// -----------------------------------------------------------------------------
// Services Overview Section
// -----------------------------------------------------------------------------

.services-overview {
  padding: map-get($spacers, 16) 0;
  background-color: $bg-secondary;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.05;
    pointer-events: none;
  }
}

.section-title {
  text-align: center;
  margin-bottom: map-get($spacers, 12);
  
  h2 {
    font-size: $font-size-4xl;
    color: $primary-color;
    margin-bottom: map-get($spacers, 4);
    
    @include media-breakpoint-down(md) {
      font-size: $font-size-3xl;
    }
    
    @include media-breakpoint-down(sm) {
      font-size: $font-size-2xl;
    }
  }
  
  p {
    font-size: $font-size-lg;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
  }
}

// -----------------------------------------------------------------------------
// FAQ Section
// -----------------------------------------------------------------------------

.faq-section {
  padding: map-get($spacers, 16) 0;
  background-color: $bg-primary;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: map-get($spacers, 4);
  border-radius: $border-radius;
  overflow: hidden;
  background-color: $bg-primary;
  border: 1px solid $border-light;
  @include card-shadow(1);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.faq-question {
  @include button-reset;
  width: 100%;
  padding: map-get($spacers, 5) map-get($spacers, 6);
  text-align: left;
  font-weight: $font-weight-medium;
  font-size: $font-size-lg;
  color: $primary-color;
  background-color: $bg-primary;
  border: none;
  cursor: pointer;
  transition: all $animation-duration-base ease;
  position: relative;
  
  &::after {
    content: '+';
    position: absolute;
    right: map-get($spacers, 6);
    top: 50%;
    transform: translateY(-50%);
    font-size: $font-size-2xl;
    font-weight: $font-weight-normal;
    transition: transform $animation-duration-base ease;
  }
  
  &:hover {
    background-color: $bg-secondary;
  }
  
  &:focus-visible {
    @include focus-ring;
  }
  
  &.active {
    background-color: $primary-color;
    color: $text-light;
    
    &::after {
      transform: translateY(-50%) rotate(45deg);
    }
  }
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height $animation-duration-base ease;
  
  &.active {
    max-height: 200px;
  }
  
  p {
    padding: 0 map-get($spacers, 6) map-get($spacers, 5);
    margin: 0;
    color: $text-secondary;
    line-height: $line-height-relaxed;
  }
}

// -----------------------------------------------------------------------------
// Animation Classes
// -----------------------------------------------------------------------------

.fade-in-section {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
  
  &.animate-in {
    opacity: 1;
    transform: translateY(0);
  }
}

// -----------------------------------------------------------------------------
// Responsive Adjustments
// -----------------------------------------------------------------------------

@include media-breakpoint-down(md) {
  .hero {
    padding: map-get($spacers, 12) 0 map-get($spacers, 8);
  }
  
  .services-overview,
  .faq-section {
    padding: map-get($spacers, 12) 0;
  }
  
  .section-title {
    margin-bottom: map-get($spacers, 8);
  }
}

@include media-breakpoint-down(sm) {
  .hero {
    padding: map-get($spacers, 8) 0 map-get($spacers, 6);
  }
  
  .services-overview,
  .faq-section {
    padding: map-get($spacers, 8) 0;
  }
  
  .section-title {
    margin-bottom: map-get($spacers, 6);
  }
  
  .faq-question {
    padding: map-get($spacers, 4) map-get($spacers, 5);
    font-size: $font-size-base;
    
    &::after {
      right: map-get($spacers, 5);
      font-size: $font-size-xl;
    }
  }
  
  .faq-answer p {
    padding: 0 map-get($spacers, 5) map-get($spacers, 4);
  }
}
