/**
 * DOM Utilities
 * Helper functions for DOM manipulation and queries
 */

/**
 * Query selector with error handling
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {Element|null}
 */
export function $(selector, context = document) {
  try {
    return context.querySelector(selector);
  } catch (error) {
    console.error(`Invalid selector: ${selector}`, error);
    return null;
  }
}

/**
 * Query all elements with error handling
 * @param {string} selector - CSS selector
 * @param {Element} context - Context element (default: document)
 * @returns {NodeList}
 */
export function $$(selector, context = document) {
  try {
    return context.querySelectorAll(selector);
  } catch (error) {
    console.error(`Invalid selector: ${selector}`, error);
    return [];
  }
}

/**
 * Create element with attributes and content
 * @param {string} tag - HTML tag name
 * @param {Object} attributes - Element attributes
 * @param {string|Element|Array} content - Element content
 * @returns {Element}
 */
export function createElement(tag, attributes = {}, content = '') {
  const element = document.createElement(tag);
  
  // Set attributes
  Object.entries(attributes).forEach(([key, value]) => {
    if (key === 'className') {
      element.className = value;
    } else if (key === 'dataset') {
      Object.entries(value).forEach(([dataKey, dataValue]) => {
        element.dataset[dataKey] = dataValue;
      });
    } else {
      element.setAttribute(key, value);
    }
  });
  
  // Set content
  if (typeof content === 'string') {
    element.innerHTML = content;
  } else if (content instanceof Element) {
    element.appendChild(content);
  } else if (Array.isArray(content)) {
    content.forEach(item => {
      if (typeof item === 'string') {
        element.insertAdjacentHTML('beforeend', item);
      } else if (item instanceof Element) {
        element.appendChild(item);
      }
    });
  }
  
  return element;
}

/**
 * Check if element is visible
 * @param {Element} element
 * @returns {boolean}
 */
export function isVisible(element) {
  if (!element) return false;
  
  const style = window.getComputedStyle(element);
  return style.display !== 'none' && 
         style.visibility !== 'hidden' && 
         style.opacity !== '0';
}

/**
 * Get element's offset position
 * @param {Element} element
 * @returns {Object}
 */
export function getOffset(element) {
  if (!element) return { top: 0, left: 0 };
  
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  return {
    top: rect.top + scrollTop,
    left: rect.left + scrollLeft,
    width: rect.width,
    height: rect.height
  };
}

/**
 * Smooth scroll to element
 * @param {Element|string} target - Element or selector
 * @param {Object} options - Scroll options
 */
export function scrollTo(target, options = {}) {
  const element = typeof target === 'string' ? $(target) : target;
  if (!element) return;
  
  const defaultOptions = {
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
    offset: 0
  };
  
  const scrollOptions = { ...defaultOptions, ...options };
  const { offset, ...nativeOptions } = scrollOptions;
  
  if (offset) {
    const elementPosition = getOffset(element);
    window.scrollTo({
      top: elementPosition.top - offset,
      behavior: nativeOptions.behavior
    });
  } else {
    element.scrollIntoView(nativeOptions);
  }
}

/**
 * Add event listener with automatic cleanup
 * @param {Element} element
 * @param {string} event
 * @param {Function} handler
 * @param {Object} options
 * @returns {Function} Cleanup function
 */
export function addEventListener(element, event, handler, options = {}) {
  if (!element || typeof handler !== 'function') return () => {};
  
  element.addEventListener(event, handler, options);
  
  return () => {
    element.removeEventListener(event, handler, options);
  };
}

/**
 * Debounce function calls
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @returns {Function}
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * Throttle function calls
 * @param {Function} func
 * @param {number} limit
 * @returns {Function}
 */
export function throttle(func, limit) {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Wait for DOM to be ready
 * @param {Function} callback
 */
export function ready(callback) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', callback);
  } else {
    callback();
  }
}

/**
 * Load script dynamically
 * @param {string} src
 * @param {Object} attributes
 * @returns {Promise}
 */
export function loadScript(src, attributes = {}) {
  return new Promise((resolve, reject) => {
    const script = createElement('script', {
      src,
      ...attributes
    });
    
    script.onload = () => resolve(script);
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    
    document.head.appendChild(script);
  });
}

/**
 * Load CSS dynamically
 * @param {string} href
 * @param {Object} attributes
 * @returns {Promise}
 */
export function loadCSS(href, attributes = {}) {
  return new Promise((resolve, reject) => {
    const link = createElement('link', {
      rel: 'stylesheet',
      href,
      ...attributes
    });
    
    link.onload = () => resolve(link);
    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
    
    document.head.appendChild(link);
  });
}

/**
 * Get all focusable elements within a container
 * @param {Element} container
 * @returns {Array}
 */
export function getFocusableElements(container) {
  const focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');
  
  return Array.from(container.querySelectorAll(focusableSelectors))
    .filter(element => isVisible(element));
}

/**
 * Trap focus within a container
 * @param {Element} container
 * @returns {Function} Release function
 */
export function trapFocus(container) {
  const focusableElements = getFocusableElements(container);
  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];
  
  const handleTabKey = (event) => {
    if (event.key !== 'Tab') return;
    
    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  };
  
  container.addEventListener('keydown', handleTabKey);
  
  // Focus first element
  if (firstElement) {
    firstElement.focus();
  }
  
  // Return release function
  return () => {
    container.removeEventListener('keydown', handleTabKey);
  };
}

/**
 * Announce message to screen readers
 * @param {string} message
 * @param {string} priority - 'polite' or 'assertive'
 */
export function announce(message, priority = 'polite') {
  const announcer = $('#screen-reader-announcer') || createAnnouncer();
  announcer.setAttribute('aria-live', priority);
  announcer.textContent = message;
  
  // Clear after announcement
  setTimeout(() => {
    announcer.textContent = '';
  }, 1000);
}

/**
 * Create screen reader announcer element
 * @returns {Element}
 */
function createAnnouncer() {
  const announcer = createElement('div', {
    id: 'screen-reader-announcer',
    className: 'sr-only',
    'aria-live': 'polite',
    'aria-atomic': 'true'
  });
  
  document.body.appendChild(announcer);
  return announcer;
}

/**
 * Check if user prefers reduced motion
 * @returns {boolean}
 */
export function prefersReducedMotion() {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Get viewport dimensions
 * @returns {Object}
 */
export function getViewport() {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  };
}

/**
 * Check if element is in viewport
 * @param {Element} element
 * @param {number} threshold - Percentage of element that must be visible
 * @returns {boolean}
 */
export function isInViewport(element, threshold = 0) {
  if (!element) return false;
  
  const rect = element.getBoundingClientRect();
  const viewport = getViewport();
  
  const elementHeight = rect.height;
  const elementWidth = rect.width;
  
  const visibleHeight = Math.min(rect.bottom, viewport.height) - Math.max(rect.top, 0);
  const visibleWidth = Math.min(rect.right, viewport.width) - Math.max(rect.left, 0);
  
  const visibleArea = visibleHeight * visibleWidth;
  const totalArea = elementHeight * elementWidth;
  
  return totalArea > 0 && (visibleArea / totalArea) >= (threshold / 100);
}
