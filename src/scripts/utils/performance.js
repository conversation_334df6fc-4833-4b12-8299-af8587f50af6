/**
 * Performance Utilities
 * Tools for monitoring and optimizing performance
 */

/**
 * Lazy load images with Intersection Observer
 * @param {string} selector - Image selector
 * @param {Object} options - Observer options
 */
export function lazyLoadImages(selector = 'img[data-src]', options = {}) {
  const defaultOptions = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  };

  const observerOptions = { ...defaultOptions, ...options };

  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.dataset.src;
          const srcset = img.dataset.srcset;

          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
          }

          if (srcset) {
            img.srcset = srcset;
            img.removeAttribute('data-srcset');
          }

          img.classList.remove('lazy');
          img.classList.add('loaded');
          observer.unobserve(img);
        }
      });
    }, observerOptions);

    document.querySelectorAll(selector).forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers without Intersection Observer
    document.querySelectorAll(selector).forEach(img => {
      const src = img.dataset.src;
      const srcset = img.dataset.srcset;

      if (src) {
        img.src = src;
        img.removeAttribute('data-src');
      }

      if (srcset) {
        img.srcset = srcset;
        img.removeAttribute('data-srcset');
      }

      img.classList.remove('lazy');
      img.classList.add('loaded');
    });
  }
}

/**
 * Preload critical resources
 * @param {Array} resources - Array of resource objects
 */
export function preloadResources(resources) {
  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as || 'fetch';
    
    if (resource.type) {
      link.type = resource.type;
    }
    
    if (resource.crossorigin) {
      link.crossOrigin = resource.crossorigin;
    }
    
    document.head.appendChild(link);
  });
}

/**
 * Measure and report Core Web Vitals
 */
export function measureWebVitals() {
  // Largest Contentful Paint (LCP)
  if ('PerformanceObserver' in window) {
    const lcpObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      console.log('LCP:', lastEntry.startTime);
      
      // Report to analytics if available
      if (window.gtag) {
        window.gtag('event', 'web_vitals', {
          name: 'LCP',
          value: Math.round(lastEntry.startTime),
          event_category: 'Performance'
        });
      }
    });
    
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    const fidObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        console.log('FID:', entry.processingStart - entry.startTime);
        
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            name: 'FID',
            value: Math.round(entry.processingStart - entry.startTime),
            event_category: 'Performance'
          });
        }
      });
    });
    
    fidObserver.observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver(list => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      
      console.log('CLS:', clsValue);
      
      if (window.gtag) {
        window.gtag('event', 'web_vitals', {
          name: 'CLS',
          value: Math.round(clsValue * 1000),
          event_category: 'Performance'
        });
      }
    });
    
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  }
}

/**
 * Optimize font loading
 */
export function optimizeFontLoading() {
  // Add font-display: swap to improve loading performance
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-family: 'Roboto';
      font-display: swap;
    }
    @font-face {
      font-family: 'Montserrat';
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Implement resource hints
 */
export function addResourceHints() {
  const hints = [
    { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
    { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
    { rel: 'dns-prefetch', href: '//cdnjs.cloudflare.com' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
  ];

  hints.forEach(hint => {
    const link = document.createElement('link');
    link.rel = hint.rel;
    link.href = hint.href;
    
    if (hint.crossorigin) {
      link.crossOrigin = hint.crossorigin;
    }
    
    document.head.appendChild(link);
  });
}

/**
 * Defer non-critical JavaScript
 * @param {Array} scripts - Array of script URLs
 */
export function deferScripts(scripts) {
  const loadScript = (src) => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.defer = true;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  // Load scripts after page load
  if (document.readyState === 'complete') {
    scripts.forEach(loadScript);
  } else {
    window.addEventListener('load', () => {
      scripts.forEach(loadScript);
    });
  }
}

/**
 * Implement critical CSS inlining
 * @param {string} criticalCSS - Critical CSS content
 */
export function inlineCriticalCSS(criticalCSS) {
  const style = document.createElement('style');
  style.textContent = criticalCSS;
  document.head.appendChild(style);
}

/**
 * Monitor performance metrics
 */
export function monitorPerformance() {
  // Navigation timing
  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      if (navigation) {
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          dom: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          load: navigation.loadEventEnd - navigation.navigationStart
        };
        
        console.log('Performance Metrics:', metrics);
        
        // Report to analytics
        if (window.gtag) {
          Object.entries(metrics).forEach(([key, value]) => {
            window.gtag('event', 'timing_complete', {
              name: key,
              value: Math.round(value),
              event_category: 'Performance'
            });
          });
        }
      }
    }, 0);
  });
}

/**
 * Optimize images with modern formats
 * @param {Element} img - Image element
 */
export function optimizeImage(img) {
  const src = img.src || img.dataset.src;
  if (!src) return;

  // Create picture element with modern formats
  const picture = document.createElement('picture');
  
  // WebP source
  const webpSource = document.createElement('source');
  webpSource.srcset = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  webpSource.type = 'image/webp';
  
  // AVIF source (most modern)
  const avifSource = document.createElement('source');
  avifSource.srcset = src.replace(/\.(jpg|jpeg|png)$/i, '.avif');
  avifSource.type = 'image/avif';
  
  // Original image as fallback
  const fallbackImg = img.cloneNode(true);
  
  picture.appendChild(avifSource);
  picture.appendChild(webpSource);
  picture.appendChild(fallbackImg);
  
  img.parentNode.replaceChild(picture, img);
}

/**
 * Initialize all performance optimizations
 */
export function initPerformanceOptimizations() {
  // Add resource hints
  addResourceHints();
  
  // Optimize font loading
  optimizeFontLoading();
  
  // Lazy load images
  lazyLoadImages();
  
  // Monitor performance
  monitorPerformance();
  
  // Measure web vitals
  measureWebVitals();
  
  console.log('🚀 Performance optimizations initialized');
}
