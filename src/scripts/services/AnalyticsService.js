/**
 * AnalyticsService
 * Handles analytics tracking and events
 */

export class AnalyticsService {
  constructor(options = {}) {
    this.options = {
      enabled: false, // Disabled by default for privacy
      debug: false,
      ...options
    };
    
    this.events = [];
  }

  /**
   * Track an event
   * @param {string} eventName - Name of the event
   * @param {Object} data - Event data
   */
  track(eventName, data = {}) {
    if (!this.options.enabled) {
      if (this.options.debug) {
        console.log('Analytics (disabled):', eventName, data);
      }
      return;
    }

    const event = {
      name: eventName,
      data,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.events.push(event);

    // Send to analytics service (Google Analytics, etc.)
    this.sendEvent(event);

    if (this.options.debug) {
      console.log('Analytics event:', event);
    }
  }

  /**
   * Send event to analytics service
   * @param {Object} event - Event object
   */
  sendEvent(event) {
    // Google Analytics 4 (gtag)
    if (typeof window.gtag === 'function') {
      window.gtag('event', event.name, {
        ...event.data,
        custom_parameter: event.timestamp
      });
    }

    // Google Analytics Universal (ga)
    if (typeof window.ga === 'function') {
      window.ga('send', 'event', {
        eventCategory: event.data.category || 'General',
        eventAction: event.name,
        eventLabel: event.data.label,
        eventValue: event.data.value
      });
    }

    // Custom analytics endpoint
    if (this.options.endpoint) {
      this.sendToEndpoint(event);
    }
  }

  /**
   * Send event to custom endpoint
   * @param {Object} event - Event object
   */
  async sendToEndpoint(event) {
    try {
      await fetch(this.options.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Failed to send analytics event:', error);
    }
  }

  /**
   * Track page view
   * @param {string} page - Page path
   * @param {string} title - Page title
   */
  pageView(page = window.location.pathname, title = document.title) {
    this.track('page_view', {
      page,
      title,
      referrer: document.referrer
    });
  }

  /**
   * Track user interaction
   * @param {string} element - Element type
   * @param {string} action - Action performed
   * @param {Object} data - Additional data
   */
  interaction(element, action, data = {}) {
    this.track('user_interaction', {
      element,
      action,
      ...data
    });
  }

  /**
   * Track form submission
   * @param {string} formName - Name of the form
   * @param {boolean} success - Whether submission was successful
   * @param {Object} data - Additional data
   */
  formSubmission(formName, success, data = {}) {
    this.track('form_submission', {
      form_name: formName,
      success,
      ...data
    });
  }

  /**
   * Track error
   * @param {string} error - Error message
   * @param {string} context - Error context
   * @param {Object} data - Additional data
   */
  error(error, context, data = {}) {
    this.track('error', {
      error_message: error,
      error_context: context,
      ...data
    });
  }

  /**
   * Track performance metric
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @param {Object} data - Additional data
   */
  performance(metric, value, data = {}) {
    this.track('performance', {
      metric_name: metric,
      metric_value: value,
      ...data
    });
  }

  /**
   * Enable analytics tracking
   */
  enable() {
    this.options.enabled = true;
  }

  /**
   * Disable analytics tracking
   */
  disable() {
    this.options.enabled = false;
  }

  /**
   * Check if analytics is enabled
   * @returns {boolean} Is enabled
   */
  isEnabled() {
    return this.options.enabled;
  }

  /**
   * Get tracked events
   * @returns {Array} Array of events
   */
  getEvents() {
    return [...this.events];
  }

  /**
   * Clear tracked events
   */
  clearEvents() {
    this.events = [];
  }
}
