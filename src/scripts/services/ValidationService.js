/**
 * ValidationService
 * Centralized validation logic with internationalization support
 */

export class ValidationService {
  constructor(locale = 'nl') {
    this.locale = locale;
    this.messages = {
      nl: {
        required: 'Dit veld is verplicht',
        email: 'Vul een geldig e-mailadres in',
        minLength: 'Minimaal {min} karakters vereist',
        maxLength: 'Maximaal {max} karakters toegestaan',
        pattern: 'Ongeldige invoer',
        phone: 'Vul een geldig telefoonnummer in',
        url: 'Vul een geldige URL in',
        number: 'Vul een geldig nummer in',
        min: 'Waarde moet minimaal {min} zijn',
        max: 'Waarde mag maximaal {max} zijn',
        match: 'Velden komen niet overeen',
        custom: 'Ongeldige invoer'
      },
      en: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        minLength: 'Minimum {min} characters required',
        maxLength: 'Maximum {max} characters allowed',
        pattern: 'Invalid input',
        phone: 'Please enter a valid phone number',
        url: 'Please enter a valid URL',
        number: 'Please enter a valid number',
        min: 'Value must be at least {min}',
        max: 'Value must be at most {max}',
        match: 'Fields do not match',
        custom: 'Invalid input'
      }
    };

    this.rules = {
      required: this.validateRequired.bind(this),
      email: this.validateEmail.bind(this),
      phone: this.validatePhone.bind(this),
      url: this.validateUrl.bind(this),
      number: this.validateNumber.bind(this),
      minLength: this.validateMinLength.bind(this),
      maxLength: this.validateMaxLength.bind(this),
      min: this.validateMin.bind(this),
      max: this.validateMax.bind(this),
      pattern: this.validatePattern.bind(this),
      match: this.validateMatch.bind(this),
      custom: this.validateCustom.bind(this)
    };
  }

  /**
   * Validate a value against multiple rules
   * @param {*} value - Value to validate
   * @param {Array} rules - Array of validation rules
   * @param {Object} context - Additional context for validation
   * @returns {Array} Array of error messages
   */
  validate(value, rules = [], context = {}) {
    const errors = [];

    for (const rule of rules) {
      const [ruleName, ...params] = rule.split(':');
      const ruleFunction = this.rules[ruleName];

      if (!ruleFunction) {
        console.warn(`ValidationService: Unknown rule "${ruleName}"`);
        continue;
      }

      const isValid = ruleFunction(value, params, context);
      
      if (!isValid) {
        const errorMessage = this.getErrorMessage(ruleName, params);
        errors.push(errorMessage);
        break; // Stop at first error
      }
    }

    return errors;
  }

  /**
   * Validate required field
   */
  validateRequired(value) {
    if (typeof value === 'boolean') {
      return value === true;
    }
    
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }
    
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    
    return value != null && value !== '';
  }

  /**
   * Validate email format
   */
  validateEmail(value) {
    if (!value) return true; // Allow empty if not required
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  }

  /**
   * Validate phone number
   */
  validatePhone(value) {
    if (!value) return true;
    
    // Dutch phone number patterns
    const phoneRegex = /^(\+31|0031|0)[1-9][0-9]{8}$|^(\+31|0031|0)[1-9][0-9]{7}$/;
    const cleanValue = value.replace(/[\s\-\(\)]/g, '');
    
    return phoneRegex.test(cleanValue);
  }

  /**
   * Validate URL format
   */
  validateUrl(value) {
    if (!value) return true;
    
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate number
   */
  validateNumber(value) {
    if (!value) return true;
    
    return !isNaN(value) && !isNaN(parseFloat(value));
  }

  /**
   * Validate minimum length
   */
  validateMinLength(value, params) {
    if (!value) return true;
    
    const minLength = parseInt(params[0], 10);
    return value.toString().length >= minLength;
  }

  /**
   * Validate maximum length
   */
  validateMaxLength(value, params) {
    if (!value) return true;
    
    const maxLength = parseInt(params[0], 10);
    return value.toString().length <= maxLength;
  }

  /**
   * Validate minimum value
   */
  validateMin(value, params) {
    if (!value) return true;
    
    const minValue = parseFloat(params[0]);
    const numValue = parseFloat(value);
    
    return !isNaN(numValue) && numValue >= minValue;
  }

  /**
   * Validate maximum value
   */
  validateMax(value, params) {
    if (!value) return true;
    
    const maxValue = parseFloat(params[0]);
    const numValue = parseFloat(value);
    
    return !isNaN(numValue) && numValue <= maxValue;
  }

  /**
   * Validate against regex pattern
   */
  validatePattern(value, params) {
    if (!value) return true;
    
    const pattern = new RegExp(params[0]);
    return pattern.test(value);
  }

  /**
   * Validate field match (e.g., password confirmation)
   */
  validateMatch(value, params, context) {
    if (!value) return true;
    
    const fieldToMatch = params[0];
    const matchValue = context[fieldToMatch];
    
    return value === matchValue;
  }

  /**
   * Custom validation function
   */
  validateCustom(value, params, context) {
    const customFunction = context.customValidators?.[params[0]];
    
    if (typeof customFunction === 'function') {
      return customFunction(value, context);
    }
    
    return true;
  }

  /**
   * Get localized error message
   */
  getErrorMessage(ruleName, params = []) {
    const messages = this.messages[this.locale] || this.messages.nl;
    let message = messages[ruleName] || messages.custom;

    // Replace placeholders
    params.forEach((param, index) => {
      const placeholder = index === 0 ? '{min}' : index === 1 ? '{max}' : `{${index}}`;
      message = message.replace(placeholder, param);
    });

    return message;
  }

  /**
   * Add custom validation rule
   */
  addRule(name, validator, message) {
    this.rules[name] = validator;
    
    if (message) {
      Object.keys(this.messages).forEach(locale => {
        this.messages[locale][name] = message[locale] || message;
      });
    }
  }

  /**
   * Set locale for error messages
   */
  setLocale(locale) {
    this.locale = locale;
  }

  /**
   * Validate form data object
   */
  validateForm(formData, validationRules) {
    const errors = {};
    let isValid = true;

    Object.entries(validationRules).forEach(([fieldName, rules]) => {
      const fieldValue = formData[fieldName];
      const fieldErrors = this.validate(fieldValue, rules, formData);

      if (fieldErrors.length > 0) {
        errors[fieldName] = fieldErrors;
        isValid = false;
      }
    });

    return {
      isValid,
      errors
    };
  }

  /**
   * Real-time validation with debouncing
   */
  createRealtimeValidator(element, rules, options = {}) {
    const {
      debounceTime = 300,
      validateOnBlur = true,
      validateOnInput = true,
      errorElement = null,
      onValid = null,
      onInvalid = null
    } = options;

    let debounceTimer;

    const validate = () => {
      const value = element.type === 'checkbox' ? element.checked : element.value;
      const errors = this.validate(value, rules);
      const isValid = errors.length === 0;

      // Update UI
      element.setAttribute('aria-invalid', !isValid);
      
      if (errorElement) {
        errorElement.textContent = errors[0] || '';
        errorElement.style.display = errors.length > 0 ? 'block' : 'none';
      }

      // Callbacks
      if (isValid && onValid) {
        onValid(element, value);
      } else if (!isValid && onInvalid) {
        onInvalid(element, errors);
      }

      return isValid;
    };

    const debouncedValidate = () => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(validate, debounceTime);
    };

    // Event listeners
    if (validateOnInput) {
      element.addEventListener('input', debouncedValidate);
    }

    if (validateOnBlur) {
      element.addEventListener('blur', validate);
    }

    // Return cleanup function
    return () => {
      clearTimeout(debounceTimer);
      element.removeEventListener('input', debouncedValidate);
      element.removeEventListener('blur', validate);
    };
  }
}
