/**
 * ContactService
 * Handles contact form submissions and API communication
 */

export class ContactService {
  constructor(options = {}) {
    this.options = {
      apiEndpoint: '/api/contact',
      timeout: 10000,
      ...options
    };
  }

  /**
   * Submit contact form data
   * @param {Object} formData - Form data to submit
   * @returns {Promise<Object>} Response object
   */
  async submitForm(formData) {
    try {
      // Simulate API call for demo purposes
      // In a real implementation, this would make an actual HTTP request
      await this.delay(1000); // Simulate network delay

      // Validate required fields
      if (!formData.name || !formData.email || !formData.message) {
        throw new Error('Alle verplichte velden moeten worden ingevuld');
      }

      // Simulate successful response
      return {
        success: true,
        message: 'Uw bericht is succesvol verzonden!',
        referenceNumber: this.generateReferenceNumber(),
        data: formData
      };
    } catch (error) {
      console.error('ContactService: Form submission failed', error);
      
      return {
        success: false,
        message: error.message || 'Er is een fout opgetreden bij het verzenden van uw bericht',
        error: error
      };
    }
  }

  /**
   * Generate a reference number for the submission
   * @returns {string} Reference number
   */
  generateReferenceNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `TSP-${timestamp}-${random}`;
  }

  /**
   * Delay utility for simulating network requests
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} Is valid email
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Sanitize form data
   * @param {Object} formData - Raw form data
   * @returns {Object} Sanitized form data
   */
  sanitizeFormData(formData) {
    const sanitized = {};
    
    Object.entries(formData).forEach(([key, value]) => {
      if (typeof value === 'string') {
        // Basic HTML sanitization
        sanitized[key] = value
          .trim()
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<[^>]*>/g, '');
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }
}
