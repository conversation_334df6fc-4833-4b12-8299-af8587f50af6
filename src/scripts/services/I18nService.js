/**
 * I18nService
 * Handles internationalization and language switching
 */

export class I18nService {
  constructor(options = {}) {
    this.options = {
      defaultLanguage: 'nl',
      fallbackLanguage: 'nl',
      storageKey: 'techsupport_language',
      ...options
    };

    this.currentLanguage = this.options.defaultLanguage;
    this.translations = new Map();
    this.isLoading = false;
    
    this.init();
  }

  /**
   * Initialize the i18n service
   */
  async init() {
    // Get language from URL, localStorage, or browser
    this.currentLanguage = this.detectLanguage();
    
    // Load translations for current language
    await this.loadLanguage(this.currentLanguage);
    
    // Apply translations to the page
    this.applyTranslations();
    
    // Update HTML lang attribute
    document.documentElement.lang = this.currentLanguage;
    
    // Save language preference
    this.saveLanguagePreference();
  }

  /**
   * Detect the user's preferred language
   */
  detectLanguage() {
    // 1. Check URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const urlLang = urlParams.get('lang');
    if (urlLang && this.isValidLanguage(urlLang)) {
      return urlLang;
    }

    // 2. Check localStorage
    const storedLang = localStorage.getItem(this.options.storageKey);
    if (storedLang && this.isValidLanguage(storedLang)) {
      return storedLang;
    }

    // 3. Check browser language
    const browserLang = navigator.language.split('-')[0];
    if (this.isValidLanguage(browserLang)) {
      return browserLang;
    }

    // 4. Fall back to default
    return this.options.defaultLanguage;
  }

  /**
   * Check if language is supported
   */
  isValidLanguage(lang) {
    const supportedLanguages = ['nl', 'en', 'fr'];
    return supportedLanguages.includes(lang);
  }

  /**
   * Load translations for a specific language
   */
  async loadLanguage(language) {
    if (this.translations.has(language)) {
      return this.translations.get(language);
    }

    this.isLoading = true;

    try {
      const response = await fetch(`/src/data/i18n/${language}.json`);
      
      if (!response.ok) {
        throw new Error(`Failed to load language file: ${language}`);
      }

      const translations = await response.json();
      this.translations.set(language, translations);
      
      return translations;
    } catch (error) {
      console.error(`Error loading language ${language}:`, error);
      
      // Try to load fallback language
      if (language !== this.options.fallbackLanguage) {
        return this.loadLanguage(this.options.fallbackLanguage);
      }
      
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Switch to a different language
   */
  async switchLanguage(language) {
    if (!this.isValidLanguage(language)) {
      console.error(`Unsupported language: ${language}`);
      return;
    }

    if (language === this.currentLanguage) {
      return;
    }

    // Load new language
    await this.loadLanguage(language);
    
    // Update current language
    this.currentLanguage = language;
    
    // Apply translations
    this.applyTranslations();
    
    // Update HTML lang attribute
    document.documentElement.lang = language;
    
    // Save preference
    this.saveLanguagePreference();
    
    // Update URL without reload
    this.updateURL();
    
    // Update language switcher UI
    this.updateLanguageSwitcher();
    
    // Trigger language change event
    this.triggerLanguageChangeEvent();
  }

  /**
   * Get translation for a key
   */
  t(key, params = {}) {
    const translations = this.translations.get(this.currentLanguage);
    
    if (!translations) {
      console.warn(`No translations loaded for language: ${this.currentLanguage}`);
      return key;
    }

    // Navigate nested object using dot notation
    const value = this.getNestedValue(translations, key);
    
    if (value === undefined) {
      console.warn(`Translation missing for key: ${key} in language: ${this.currentLanguage}`);
      
      // Try fallback language
      const fallbackTranslations = this.translations.get(this.options.fallbackLanguage);
      if (fallbackTranslations && this.currentLanguage !== this.options.fallbackLanguage) {
        const fallbackValue = this.getNestedValue(fallbackTranslations, key);
        if (fallbackValue !== undefined) {
          return this.interpolate(fallbackValue, params);
        }
      }
      
      return key;
    }

    return this.interpolate(value, params);
  }

  /**
   * Get nested value from object using dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Interpolate parameters in translation string
   */
  interpolate(text, params) {
    if (typeof text !== 'string') {
      return text;
    }

    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  /**
   * Apply translations to the page
   */
  applyTranslations() {
    // Find all elements with data-i18n attribute
    const elements = document.querySelectorAll('[data-i18n]');
    
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.t(key);
      
      // Check if element has data-i18n-attr for attribute translation
      const attrName = element.getAttribute('data-i18n-attr');
      
      if (attrName) {
        element.setAttribute(attrName, translation);
      } else {
        element.textContent = translation;
      }
    });

    // Update page title if it has translation
    const titleKey = document.querySelector('title')?.getAttribute('data-i18n');
    if (titleKey) {
      document.title = this.t(titleKey);
    }
  }

  /**
   * Save language preference to localStorage
   */
  saveLanguagePreference() {
    localStorage.setItem(this.options.storageKey, this.currentLanguage);
  }

  /**
   * Update URL with language parameter
   */
  updateURL() {
    const url = new URL(window.location);
    url.searchParams.set('lang', this.currentLanguage);
    
    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());
  }

  /**
   * Update language switcher UI
   */
  updateLanguageSwitcher() {
    // Update dropdown toggle text
    const currentLangElement = document.querySelector('.current-language');
    if (currentLangElement) {
      const languageNames = {
        nl: 'Nederlands',
        en: 'English',
        fr: 'Français'
      };
      currentLangElement.textContent = languageNames[this.currentLanguage];
    }

    // Update active state in dropdown
    const langLinks = document.querySelectorAll('[data-lang]');
    langLinks.forEach(link => {
      const lang = link.getAttribute('data-lang');
      if (lang === this.currentLanguage) {
        link.classList.add('active');
        link.setAttribute('aria-current', 'true');
      } else {
        link.classList.remove('active');
        link.removeAttribute('aria-current');
      }
    });
  }

  /**
   * Trigger language change event
   */
  triggerLanguageChangeEvent() {
    const event = new CustomEvent('languagechange', {
      detail: {
        language: this.currentLanguage,
        previousLanguage: this.previousLanguage
      }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * Get current language
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * Get available languages
   */
  getAvailableLanguages() {
    return ['nl', 'en', 'fr'];
  }

  /**
   * Check if service is loading
   */
  isServiceLoading() {
    return this.isLoading;
  }

  /**
   * Preload all languages
   */
  async preloadLanguages() {
    const languages = this.getAvailableLanguages();
    const promises = languages.map(lang => this.loadLanguage(lang));
    
    try {
      await Promise.all(promises);
      console.log('All languages preloaded successfully');
    } catch (error) {
      console.error('Error preloading languages:', error);
    }
  }
}
