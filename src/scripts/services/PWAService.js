/**
 * PWAService
 * Handles Progressive Web App functionality
 */

export class PWAService {
  constructor() {
    this.deferredPrompt = null;
    this.isInstalled = false;
    this.isOnline = navigator.onLine;
    
    this.init();
  }

  /**
   * Initialize PWA service
   */
  init() {
    this.registerServiceWorker();
    this.setupInstallPrompt();
    this.setupOfflineHandling();
    this.setupUpdateNotifications();
    this.trackInstallation();
  }

  /**
   * Register service worker
   */
  async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        
        console.log('Service Worker registered successfully:', registration);
        
        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateNotification();
            }
          });
        });
        
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Setup install prompt handling
   */
  setupInstallPrompt() {
    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.deferredPrompt = event;
      this.showInstallButton();
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      this.isInstalled = true;
      this.hideInstallButton();
      this.trackEvent('pwa_installed');
    });

    // Check if already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      this.isInstalled = true;
    }
  }

  /**
   * Show install button
   */
  showInstallButton() {
    const installButton = this.createInstallButton();
    
    // Add to header or create floating button
    const header = document.querySelector('header');
    if (header && !document.querySelector('.pwa-install-btn')) {
      header.appendChild(installButton);
    }
  }

  /**
   * Create install button
   */
  createInstallButton() {
    const button = document.createElement('button');
    button.className = 'pwa-install-btn btn btn--secondary btn--sm';
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
      </svg>
      App Installeren
    `;
    
    button.addEventListener('click', () => this.promptInstall());
    
    return button;
  }

  /**
   * Prompt user to install app
   */
  async promptInstall() {
    if (!this.deferredPrompt) return;

    try {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      
      this.trackEvent('pwa_install_prompt', { outcome });
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      this.deferredPrompt = null;
    } catch (error) {
      console.error('Error prompting install:', error);
    }
  }

  /**
   * Hide install button
   */
  hideInstallButton() {
    const installButton = document.querySelector('.pwa-install-btn');
    if (installButton) {
      installButton.remove();
    }
  }

  /**
   * Setup offline handling
   */
  setupOfflineHandling() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.hideOfflineNotification();
      this.syncOfflineData();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineNotification();
    });

    // Check initial state
    if (!this.isOnline) {
      this.showOfflineNotification();
    }
  }

  /**
   * Show offline notification
   */
  showOfflineNotification() {
    if (document.querySelector('.offline-notification')) return;

    const notification = document.createElement('div');
    notification.className = 'offline-notification';
    notification.innerHTML = `
      <div class="offline-content">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.64 7c-.45-.34-4.93-4-11.64-4-1.5 0-2.89.19-4.15.48L18.18 13.8 23.64 7zm-6.6 8.22L3.27 1.44 2 2.72l2.05 2.06C1.91 5.76.59 6.82.36 7l11.63 14.49.01.01.01-.01 3.9-4.86 3.32 3.32 1.27-1.27-3.46-3.46z"/>
        </svg>
        <span>U bent offline. Sommige functies zijn mogelijk beperkt.</span>
        <button class="offline-dismiss" aria-label="Melding sluiten">×</button>
      </div>
    `;

    document.body.appendChild(notification);

    // Add dismiss functionality
    notification.querySelector('.offline-dismiss').addEventListener('click', () => {
      notification.remove();
    });

    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  /**
   * Hide offline notification
   */
  hideOfflineNotification() {
    const notification = document.querySelector('.offline-notification');
    if (notification) {
      notification.remove();
    }

    // Show online notification briefly
    this.showOnlineNotification();
  }

  /**
   * Show online notification
   */
  showOnlineNotification() {
    const notification = document.createElement('div');
    notification.className = 'online-notification';
    notification.innerHTML = `
      <div class="online-content">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
        </svg>
        <span>Verbinding hersteld!</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }

  /**
   * Setup update notifications
   */
  setupUpdateNotifications() {
    // Listen for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload();
      });
    }
  }

  /**
   * Show update notification
   */
  showUpdateNotification() {
    const notification = document.createElement('div');
    notification.className = 'update-notification';
    notification.innerHTML = `
      <div class="update-content">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
        </svg>
        <span>Er is een update beschikbaar!</span>
        <button class="update-btn btn btn--sm btn--accent">Vernieuwen</button>
        <button class="update-dismiss" aria-label="Later">Later</button>
      </div>
    `;

    document.body.appendChild(notification);

    // Add update functionality
    notification.querySelector('.update-btn').addEventListener('click', () => {
      window.location.reload();
    });

    notification.querySelector('.update-dismiss').addEventListener('click', () => {
      notification.remove();
    });
  }

  /**
   * Sync offline data when back online
   */
  async syncOfflineData() {
    try {
      // Get offline data from localStorage or IndexedDB
      const offlineData = this.getOfflineData();
      
      if (offlineData.length > 0) {
        console.log('Syncing offline data...');
        
        for (const data of offlineData) {
          await this.syncDataItem(data);
        }
        
        this.clearOfflineData();
        console.log('Offline data synced successfully');
      }
    } catch (error) {
      console.error('Error syncing offline data:', error);
    }
  }

  /**
   * Get offline data from storage
   */
  getOfflineData() {
    try {
      const data = localStorage.getItem('offlineData');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error getting offline data:', error);
      return [];
    }
  }

  /**
   * Sync individual data item
   */
  async syncDataItem(dataItem) {
    // Implementation depends on your API structure
    // This is a placeholder for actual sync logic
    console.log('Syncing data item:', dataItem);
  }

  /**
   * Clear offline data after sync
   */
  clearOfflineData() {
    localStorage.removeItem('offlineData');
  }

  /**
   * Track installation and usage
   */
  trackInstallation() {
    // Track PWA usage
    if (this.isInstalled) {
      this.trackEvent('pwa_usage', {
        standalone: window.matchMedia('(display-mode: standalone)').matches,
        userAgent: navigator.userAgent
      });
    }
  }

  /**
   * Track events (integrate with your analytics)
   */
  trackEvent(eventName, data = {}) {
    // Integration with analytics service
    if (window.gtag) {
      window.gtag('event', eventName, data);
    }
    
    console.log('PWA Event:', eventName, data);
  }

  /**
   * Check if app can be installed
   */
  canInstall() {
    return this.deferredPrompt !== null;
  }

  /**
   * Check if app is installed
   */
  isAppInstalled() {
    return this.isInstalled;
  }

  /**
   * Get installation status
   */
  getInstallationStatus() {
    return {
      canInstall: this.canInstall(),
      isInstalled: this.isAppInstalled(),
      isOnline: this.isOnline,
      hasServiceWorker: 'serviceWorker' in navigator
    };
  }
}
