/**
 * NotificationService
 * Handles user notifications and announcements
 */

export class NotificationService {
  constructor() {
    this.notifications = [];
    this.container = null;
    this.announcer = null;
    
    this.init();
  }

  /**
   * Initialize the notification system
   */
  init() {
    this.createContainer();
    this.createAnnouncer();
  }

  /**
   * Create notification container
   */
  createContainer() {
    this.container = document.createElement('div');
    this.container.className = 'notification-container';
    this.container.setAttribute('aria-live', 'polite');
    this.container.setAttribute('aria-atomic', 'false');
    
    // Add styles
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      max-width: 400px;
      pointer-events: none;
    `;
    
    document.body.appendChild(this.container);
  }

  /**
   * Create screen reader announcer
   */
  createAnnouncer() {
    this.announcer = document.createElement('div');
    this.announcer.className = 'sr-only';
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    
    document.body.appendChild(this.announcer);
  }

  /**
   * Show a notification
   * @param {Object} options - Notification options
   */
  show(options = {}) {
    const notification = {
      id: this.generateId(),
      type: options.type || 'info',
      title: options.title || '',
      message: options.message || '',
      duration: options.duration || 5000,
      persistent: options.persistent || false,
      actions: options.actions || []
    };

    this.notifications.push(notification);
    this.render(notification);

    // Auto-remove after duration
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        this.remove(notification.id);
      }, notification.duration);
    }

    return notification.id;
  }

  /**
   * Render a notification
   * @param {Object} notification - Notification object
   */
  render(notification) {
    const element = document.createElement('div');
    element.className = `notification notification--${notification.type}`;
    element.setAttribute('role', 'alert');
    element.setAttribute('data-notification-id', notification.id);
    
    // Add styles
    element.style.cssText = `
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      pointer-events: auto;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      max-width: 100%;
      word-wrap: break-word;
    `;

    // Type-specific styling
    const typeColors = {
      success: '#28a745',
      error: '#dc3545',
      warning: '#ffc107',
      info: '#17a2b8'
    };

    if (typeColors[notification.type]) {
      element.style.borderLeftColor = typeColors[notification.type];
      element.style.borderLeftWidth = '4px';
    }

    // Build content
    let content = '';
    
    if (notification.title) {
      content += `<div style="font-weight: 600; margin-bottom: 8px; color: #333;">${notification.title}</div>`;
    }
    
    if (notification.message) {
      content += `<div style="color: #666; line-height: 1.4;">${notification.message}</div>`;
    }

    // Add actions
    if (notification.actions.length > 0) {
      content += '<div style="margin-top: 12px; display: flex; gap: 8px;">';
      notification.actions.forEach(action => {
        content += `<button 
          onclick="window.notificationAction('${notification.id}', '${action.id}')"
          style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; font-size: 14px;"
        >${action.label}</button>`;
      });
      content += '</div>';
    }

    // Add close button
    content += `
      <button 
        onclick="window.removeNotification('${notification.id}')"
        style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer; color: #999; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;"
        aria-label="Melding sluiten"
      >×</button>
    `;

    element.innerHTML = content;
    element.style.position = 'relative';

    this.container.appendChild(element);

    // Animate in
    setTimeout(() => {
      element.style.transform = 'translateX(0)';
    }, 10);

    // Setup global handlers
    this.setupGlobalHandlers();
  }

  /**
   * Setup global event handlers
   */
  setupGlobalHandlers() {
    if (!window.removeNotification) {
      window.removeNotification = (id) => this.remove(id);
    }
    
    if (!window.notificationAction) {
      window.notificationAction = (notificationId, actionId) => {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
          const action = notification.actions.find(a => a.id === actionId);
          if (action && action.handler) {
            action.handler();
          }
        }
      };
    }
  }

  /**
   * Remove a notification
   * @param {string} id - Notification ID
   */
  remove(id) {
    const element = this.container.querySelector(`[data-notification-id="${id}"]`);
    if (element) {
      element.style.transform = 'translateX(100%)';
      element.style.opacity = '0';
      
      setTimeout(() => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }, 300);
    }

    this.notifications = this.notifications.filter(n => n.id !== id);
  }

  /**
   * Announce message to screen readers
   * @param {string} message - Message to announce
   * @param {string} priority - 'polite' or 'assertive'
   */
  announce(message, priority = 'polite') {
    if (this.announcer) {
      this.announcer.setAttribute('aria-live', priority);
      this.announcer.textContent = message;
      
      // Clear after announcement
      setTimeout(() => {
        this.announcer.textContent = '';
      }, 1000);
    }
  }

  /**
   * Clear all notifications
   */
  clear() {
    this.notifications.forEach(notification => {
      this.remove(notification.id);
    });
  }

  /**
   * Generate unique ID
   * @returns {string} Unique ID
   */
  generateId() {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Show success notification
   * @param {string} message - Success message
   * @param {Object} options - Additional options
   */
  success(message, options = {}) {
    return this.show({
      type: 'success',
      message,
      ...options
    });
  }

  /**
   * Show error notification
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   */
  error(message, options = {}) {
    return this.show({
      type: 'error',
      message,
      duration: 0, // Persistent by default
      ...options
    });
  }

  /**
   * Show warning notification
   * @param {string} message - Warning message
   * @param {Object} options - Additional options
   */
  warning(message, options = {}) {
    return this.show({
      type: 'warning',
      message,
      ...options
    });
  }

  /**
   * Show info notification
   * @param {string} message - Info message
   * @param {Object} options - Additional options
   */
  info(message, options = {}) {
    return this.show({
      type: 'info',
      message,
      ...options
    });
  }
}
