/**
 * Navigation Component
 * Handles mobile navigation, dropdowns, and accessibility
 */

export class Navigation {
  constructor(element, options = {}) {
    this.nav = element;
    this.options = {
      mobileBreakpoint: 768,
      closeOnOutsideClick: true,
      closeOnEscape: true,
      ...options
    };

    this.isOpen = false;
    this.activeDropdown = null;
    
    this.init();
  }

  /**
   * Initialize navigation
   */
  init() {
    if (!this.nav) {
      console.error('Navigation: Element not found');
      return;
    }

    this.setupElements();
    this.bindEvents();
    this.setupAccessibility();
  }

  /**
   * Setup DOM elements
   */
  setupElements() {
    this.toggle = this.nav.querySelector('.mobile-menu-toggle');
    this.menu = this.nav.querySelector('.nav');
    this.overlay = document.querySelector('.nav-overlay') || this.createOverlay();
    this.dropdowns = this.nav.querySelectorAll('.nav-dropdown');
    this.links = this.nav.querySelectorAll('.nav-link');
  }

  /**
   * Create mobile overlay
   */
  createOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'nav-overlay';
    overlay.setAttribute('aria-hidden', 'true');
    document.body.appendChild(overlay);
    return overlay;
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Mobile toggle
    if (this.toggle) {
      this.toggle.addEventListener('click', this.toggleMobile.bind(this));
    }

    // Overlay click
    this.overlay.addEventListener('click', this.closeMobile.bind(this));

    // Dropdown toggles
    this.dropdowns.forEach(dropdown => {
      const toggle = dropdown.querySelector('.nav-link');
      const menu = dropdown.querySelector('.nav-dropdown-menu');
      
      if (toggle && menu) {
        toggle.addEventListener('click', (e) => {
          e.preventDefault();
          this.toggleDropdown(dropdown);
        });
      }
    });

    // Link clicks (close mobile menu)
    this.links.forEach(link => {
      if (!link.closest('.nav-dropdown')) {
        link.addEventListener('click', () => {
          if (this.isOpen) {
            this.closeMobile();
          }
        });
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', this.handleKeydown.bind(this));

    // Outside clicks
    if (this.options.closeOnOutsideClick) {
      document.addEventListener('click', this.handleOutsideClick.bind(this));
    }

    // Window resize
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * Setup accessibility attributes
   */
  setupAccessibility() {
    // Mobile toggle
    if (this.toggle) {
      this.toggle.setAttribute('aria-expanded', 'false');
      this.toggle.setAttribute('aria-controls', this.menu.id || 'nav-menu');
      
      if (!this.menu.id) {
        this.menu.id = 'nav-menu';
      }
    }

    // Dropdown toggles
    this.dropdowns.forEach((dropdown, index) => {
      const toggle = dropdown.querySelector('.nav-link');
      const menu = dropdown.querySelector('.nav-dropdown-menu');
      
      if (toggle && menu) {
        const menuId = menu.id || `dropdown-menu-${index}`;
        menu.id = menuId;
        
        toggle.setAttribute('aria-expanded', 'false');
        toggle.setAttribute('aria-haspopup', 'true');
        toggle.setAttribute('aria-controls', menuId);
        
        menu.setAttribute('role', 'menu');
        
        // Menu items
        const items = menu.querySelectorAll('.nav-link');
        items.forEach(item => {
          item.setAttribute('role', 'menuitem');
        });
      }
    });
  }

  /**
   * Toggle mobile navigation
   */
  toggleMobile() {
    if (this.isOpen) {
      this.closeMobile();
    } else {
      this.openMobile();
    }
  }

  /**
   * Open mobile navigation
   */
  openMobile() {
    this.isOpen = true;
    
    this.menu.classList.add('active');
    this.overlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    if (this.toggle) {
      this.toggle.setAttribute('aria-expanded', 'true');
      this.toggle.setAttribute('aria-label', 'Menu sluiten');
    }

    // Focus first link
    const firstLink = this.menu.querySelector('.nav-link');
    if (firstLink) {
      firstLink.focus();
    }

    this.announceToScreenReader('Menu geopend');
  }

  /**
   * Close mobile navigation
   */
  closeMobile() {
    this.isOpen = false;
    
    this.menu.classList.remove('active');
    this.overlay.classList.remove('active');
    document.body.style.overflow = '';
    
    if (this.toggle) {
      this.toggle.setAttribute('aria-expanded', 'false');
      this.toggle.setAttribute('aria-label', 'Menu openen');
    }

    // Close all dropdowns
    this.closeAllDropdowns();

    this.announceToScreenReader('Menu gesloten');
  }

  /**
   * Toggle dropdown menu
   */
  toggleDropdown(dropdown) {
    const toggle = dropdown.querySelector('.nav-link');
    const menu = dropdown.querySelector('.nav-dropdown-menu');
    const isOpen = menu.classList.contains('active');

    // Close other dropdowns
    if (!isOpen) {
      this.closeAllDropdowns();
    }

    // Toggle current dropdown
    if (isOpen) {
      this.closeDropdown(dropdown);
    } else {
      this.openDropdown(dropdown);
    }
  }

  /**
   * Open dropdown menu
   */
  openDropdown(dropdown) {
    const toggle = dropdown.querySelector('.nav-link');
    const menu = dropdown.querySelector('.nav-dropdown-menu');
    
    menu.classList.add('active');
    toggle.setAttribute('aria-expanded', 'true');
    
    this.activeDropdown = dropdown;

    // Focus first item
    const firstItem = menu.querySelector('.nav-link');
    if (firstItem) {
      firstItem.focus();
    }
  }

  /**
   * Close dropdown menu
   */
  closeDropdown(dropdown) {
    const toggle = dropdown.querySelector('.nav-link');
    const menu = dropdown.querySelector('.nav-dropdown-menu');
    
    menu.classList.remove('active');
    toggle.setAttribute('aria-expanded', 'false');
    
    if (this.activeDropdown === dropdown) {
      this.activeDropdown = null;
    }
  }

  /**
   * Close all dropdown menus
   */
  closeAllDropdowns() {
    this.dropdowns.forEach(dropdown => {
      this.closeDropdown(dropdown);
    });
  }

  /**
   * Handle keyboard navigation
   */
  handleKeydown(event) {
    const { key } = event;

    // Escape key
    if (key === 'Escape') {
      if (this.options.closeOnEscape) {
        if (this.activeDropdown) {
          this.closeDropdown(this.activeDropdown);
          const toggle = this.activeDropdown.querySelector('.nav-link');
          if (toggle) toggle.focus();
        } else if (this.isOpen) {
          this.closeMobile();
          if (this.toggle) this.toggle.focus();
        }
      }
    }

    // Arrow navigation in dropdowns
    if (this.activeDropdown && (key === 'ArrowDown' || key === 'ArrowUp')) {
      event.preventDefault();
      this.navigateDropdown(key === 'ArrowDown' ? 1 : -1);
    }

    // Tab navigation
    if (key === 'Tab' && this.isOpen) {
      this.handleTabNavigation(event);
    }
  }

  /**
   * Navigate dropdown with arrow keys
   */
  navigateDropdown(direction) {
    const menu = this.activeDropdown.querySelector('.nav-dropdown-menu');
    const items = Array.from(menu.querySelectorAll('.nav-link'));
    const currentIndex = items.indexOf(document.activeElement);
    
    let nextIndex = currentIndex + direction;
    
    if (nextIndex < 0) {
      nextIndex = items.length - 1;
    } else if (nextIndex >= items.length) {
      nextIndex = 0;
    }
    
    items[nextIndex].focus();
  }

  /**
   * Handle tab navigation in mobile menu
   */
  handleTabNavigation(event) {
    const focusableElements = this.menu.querySelectorAll(
      'a, button, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * Handle outside clicks
   */
  handleOutsideClick(event) {
    if (!this.nav.contains(event.target)) {
      if (this.activeDropdown) {
        this.closeAllDropdowns();
      }
      if (this.isOpen && !this.overlay.contains(event.target)) {
        this.closeMobile();
      }
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    if (window.innerWidth > this.options.mobileBreakpoint && this.isOpen) {
      this.closeMobile();
    }
  }

  /**
   * Announce to screen readers
   */
  announceToScreenReader(message) {
    const announcer = document.querySelector('.nav-announcer') || this.createAnnouncer();
    announcer.textContent = message;
    
    // Clear after announcement
    setTimeout(() => {
      announcer.textContent = '';
    }, 1000);
  }

  /**
   * Create screen reader announcer
   */
  createAnnouncer() {
    const announcer = document.createElement('div');
    announcer.className = 'nav-announcer';
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    document.body.appendChild(announcer);
    return announcer;
  }

  /**
   * Set active navigation item
   */
  setActive(href) {
    this.links.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === href) {
        link.classList.add('active');
        link.setAttribute('aria-current', 'page');
      } else {
        link.removeAttribute('aria-current');
      }
    });
  }

  /**
   * Destroy navigation
   */
  destroy() {
    // Remove event listeners
    if (this.toggle) {
      this.toggle.removeEventListener('click', this.toggleMobile);
    }
    
    this.overlay.removeEventListener('click', this.closeMobile);
    document.removeEventListener('keydown', this.handleKeydown);
    document.removeEventListener('click', this.handleOutsideClick);
    window.removeEventListener('resize', this.handleResize);

    // Clean up
    this.closeMobile();
    this.closeAllDropdowns();
    
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
  }
}
