/**
 * LanguageSwitcher Component
 * Handles language switching UI and interactions
 */

export class LanguageSwitcher {
  constructor(element, i18nService, options = {}) {
    this.element = element;
    this.i18nService = i18nService;
    this.options = {
      showFlags: true,
      showText: true,
      ...options
    };

    this.isOpen = false;
    
    this.init();
  }

  /**
   * Initialize the language switcher
   */
  init() {
    if (!this.element || !this.i18nService) {
      console.error('LanguageSwitcher: Missing required parameters');
      return;
    }

    this.setupElements();
    this.bindEvents();
    this.updateUI();
  }

  /**
   * Setup DOM elements
   */
  setupElements() {
    this.toggle = this.element.querySelector('.nav-link');
    this.menu = this.element.querySelector('.nav-dropdown-menu');
    this.currentLangElement = this.element.querySelector('.current-language');
    this.langLinks = this.element.querySelectorAll('[data-lang]');
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Toggle dropdown
    if (this.toggle) {
      this.toggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleDropdown();
      });
    }

    // Language selection
    this.langLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const lang = link.getAttribute('data-lang');
        this.switchLanguage(lang);
      });
    });

    // Close dropdown on outside click
    document.addEventListener('click', (e) => {
      if (!this.element.contains(e.target)) {
        this.closeDropdown();
      }
    });

    // Keyboard navigation
    this.element.addEventListener('keydown', this.handleKeydown.bind(this));

    // Listen for language change events
    document.addEventListener('languagechange', this.handleLanguageChange.bind(this));
  }

  /**
   * Toggle dropdown menu
   */
  toggleDropdown() {
    if (this.isOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  /**
   * Open dropdown menu
   */
  openDropdown() {
    this.isOpen = true;
    this.menu.classList.add('active');
    this.toggle.setAttribute('aria-expanded', 'true');
    
    // Focus first menu item
    const firstLink = this.menu.querySelector('.nav-link');
    if (firstLink) {
      firstLink.focus();
    }
  }

  /**
   * Close dropdown menu
   */
  closeDropdown() {
    this.isOpen = false;
    this.menu.classList.remove('active');
    this.toggle.setAttribute('aria-expanded', 'false');
  }

  /**
   * Switch language
   */
  async switchLanguage(language) {
    if (this.i18nService.isServiceLoading()) {
      return;
    }

    try {
      // Show loading state
      this.setLoadingState(true);
      
      // Switch language
      await this.i18nService.switchLanguage(language);
      
      // Close dropdown
      this.closeDropdown();
      
      // Update UI
      this.updateUI();
      
    } catch (error) {
      console.error('Error switching language:', error);
      
      // Show error notification
      this.showError('Failed to switch language. Please try again.');
      
    } finally {
      this.setLoadingState(false);
    }
  }

  /**
   * Handle keyboard navigation
   */
  handleKeydown(event) {
    const { key } = event;

    if (key === 'Escape') {
      this.closeDropdown();
      this.toggle.focus();
      return;
    }

    if (!this.isOpen) {
      return;
    }

    const links = Array.from(this.langLinks);
    const currentIndex = links.indexOf(document.activeElement);

    if (key === 'ArrowDown') {
      event.preventDefault();
      const nextIndex = currentIndex < links.length - 1 ? currentIndex + 1 : 0;
      links[nextIndex].focus();
    } else if (key === 'ArrowUp') {
      event.preventDefault();
      const prevIndex = currentIndex > 0 ? currentIndex - 1 : links.length - 1;
      links[prevIndex].focus();
    } else if (key === 'Enter' || key === ' ') {
      event.preventDefault();
      if (document.activeElement.hasAttribute('data-lang')) {
        const lang = document.activeElement.getAttribute('data-lang');
        this.switchLanguage(lang);
      }
    }
  }

  /**
   * Handle language change event
   */
  handleLanguageChange(event) {
    this.updateUI();
  }

  /**
   * Update UI to reflect current language
   */
  updateUI() {
    const currentLang = this.i18nService.getCurrentLanguage();
    
    // Update current language display
    if (this.currentLangElement) {
      const languageNames = {
        nl: 'Nederlands',
        en: 'English',
        fr: 'Français'
      };
      this.currentLangElement.textContent = languageNames[currentLang];
    }

    // Update active states
    this.langLinks.forEach(link => {
      const lang = link.getAttribute('data-lang');
      if (lang === currentLang) {
        link.classList.add('active');
        link.setAttribute('aria-current', 'true');
      } else {
        link.classList.remove('active');
        link.removeAttribute('aria-current');
      }
    });

    // Update flag icons if enabled
    if (this.options.showFlags) {
      this.updateFlags();
    }
  }

  /**
   * Update flag icons
   */
  updateFlags() {
    const currentLang = this.i18nService.getCurrentLanguage();
    
    // Update current flag in toggle
    const currentFlag = this.toggle.querySelector('.flag-icon');
    if (currentFlag) {
      currentFlag.src = `/src/assets/images/flags/${currentLang}.svg`;
      currentFlag.alt = `${currentLang.toUpperCase()} flag`;
    }
  }

  /**
   * Set loading state
   */
  setLoadingState(isLoading) {
    if (isLoading) {
      this.element.classList.add('loading');
      this.toggle.setAttribute('aria-busy', 'true');
    } else {
      this.element.classList.remove('loading');
      this.toggle.setAttribute('aria-busy', 'false');
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    // Create temporary error message
    const errorElement = document.createElement('div');
    errorElement.className = 'language-error';
    errorElement.textContent = message;
    errorElement.setAttribute('role', 'alert');
    
    // Style the error
    errorElement.style.cssText = `
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #dc3545;
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      z-index: 1000;
      margin-top: 4px;
    `;
    
    this.element.style.position = 'relative';
    this.element.appendChild(errorElement);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (errorElement.parentNode) {
        errorElement.parentNode.removeChild(errorElement);
      }
    }, 3000);
  }

  /**
   * Get current language
   */
  getCurrentLanguage() {
    return this.i18nService.getCurrentLanguage();
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Remove event listeners
    document.removeEventListener('click', this.handleOutsideClick);
    document.removeEventListener('languagechange', this.handleLanguageChange);
    
    this.langLinks.forEach(link => {
      link.removeEventListener('click', this.handleLanguageClick);
    });
    
    if (this.toggle) {
      this.toggle.removeEventListener('click', this.handleToggleClick);
    }
  }
}
