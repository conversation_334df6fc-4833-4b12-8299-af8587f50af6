/**
 * ContactForm Component
 * Handles contact form validation, submission, and accessibility
 */

import { ValidationService } from '../services/ValidationService.js';
import { ContactService } from '../services/ContactService.js';
import { NotificationService } from '../services/NotificationService.js';
import { AnalyticsService } from '../services/AnalyticsService.js';

export class ContactForm {
  constructor(formElement, options = {}) {
    this.form = formElement;
    this.options = {
      validateOnBlur: true,
      showSuccessMessage: true,
      resetOnSuccess: true,
      trackAnalytics: true,
      ...options
    };

    this.fields = {};
    this.validators = new ValidationService();
    this.contactService = new ContactService();
    this.notifications = new NotificationService();
    this.analytics = new AnalyticsService();

    this.init();
  }

  /**
   * Initialize the contact form
   */
  init() {
    if (!this.form) {
      console.error('ContactForm: Form element not found');
      return;
    }

    this.setupFields();
    this.bindEvents();
    this.setupAccessibility();
    
    if (this.options.trackAnalytics) {
      this.analytics.track('contact_form_loaded');
    }
  }

  /**
   * Setup form fields and their validation rules
   */
  setupFields() {
    this.fields = {
      name: {
        element: this.form.querySelector('#name'),
        errorElement: this.form.querySelector('#name-error'),
        rules: ['required', 'minLength:2', 'maxLength:50']
      },
      email: {
        element: this.form.querySelector('#email'),
        errorElement: this.form.querySelector('#email-error'),
        rules: ['required', 'email']
      },
      subject: {
        element: this.form.querySelector('#subject'),
        errorElement: this.form.querySelector('#subject-error'),
        rules: ['required']
      },
      message: {
        element: this.form.querySelector('#message'),
        errorElement: this.form.querySelector('#message-error'),
        rules: ['required', 'minLength:10', 'maxLength:1000']
      },
      terms: {
        element: this.form.querySelector('#terms'),
        errorElement: this.form.querySelector('#terms-error'),
        rules: ['required']
      }
    };
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Form submission
    this.form.addEventListener('submit', this.handleSubmit.bind(this));

    // Reset button
    const resetButton = this.form.querySelector('button[type="reset"]');
    if (resetButton) {
      resetButton.addEventListener('click', this.handleReset.bind(this));
    }

    // Field validation on blur
    if (this.options.validateOnBlur) {
      Object.values(this.fields).forEach(field => {
        if (field.element) {
          field.element.addEventListener('blur', () => {
            this.validateField(field);
          });
        }
      });
    }

    // Real-time email validation
    if (this.fields.email.element) {
      this.fields.email.element.addEventListener('input', 
        this.debounce(() => this.validateField(this.fields.email), 500)
      );
    }
  }

  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    // Add ARIA labels and descriptions
    Object.entries(this.fields).forEach(([fieldName, field]) => {
      if (field.element && field.errorElement) {
        field.element.setAttribute('aria-describedby', field.errorElement.id);
        field.errorElement.setAttribute('aria-live', 'polite');
      }
    });

    // Add form role and label
    this.form.setAttribute('role', 'form');
    this.form.setAttribute('aria-label', 'Contact formulier');
  }

  /**
   * Handle form submission
   */
  async handleSubmit(event) {
    event.preventDefault();

    if (this.options.trackAnalytics) {
      this.analytics.track('contact_form_submit_attempted');
    }

    // Validate all fields
    const isValid = this.validateForm();

    if (!isValid) {
      this.focusFirstError();
      this.announceErrors();
      return;
    }

    // Show loading state
    this.setLoadingState(true);

    try {
      // Collect form data
      const formData = this.collectFormData();

      // Submit form
      const response = await this.contactService.submitForm(formData);

      if (response.success) {
        this.handleSuccess(response);
        
        if (this.options.trackAnalytics) {
          this.analytics.track('contact_form_submit_success');
        }
      } else {
        throw new Error(response.message || 'Submission failed');
      }
    } catch (error) {
      this.handleError(error);
      
      if (this.options.trackAnalytics) {
        this.analytics.track('contact_form_submit_error', { error: error.message });
      }
    } finally {
      this.setLoadingState(false);
    }
  }

  /**
   * Validate entire form
   */
  validateForm() {
    let isValid = true;

    Object.values(this.fields).forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });

    return isValid;
  }

  /**
   * Validate individual field
   */
  validateField(field) {
    if (!field.element) return true;

    const value = field.element.type === 'checkbox' 
      ? field.element.checked 
      : field.element.value.trim();

    const errors = this.validators.validate(value, field.rules);

    if (errors.length > 0) {
      this.showFieldError(field, errors[0]);
      return false;
    } else {
      this.clearFieldError(field);
      return true;
    }
  }

  /**
   * Show field error
   */
  showFieldError(field, message) {
    if (field.errorElement) {
      field.errorElement.textContent = message;
      field.errorElement.classList.add('visible');
    }

    if (field.element) {
      field.element.setAttribute('aria-invalid', 'true');
      field.element.classList.add('error');
    }
  }

  /**
   * Clear field error
   */
  clearFieldError(field) {
    if (field.errorElement) {
      field.errorElement.textContent = '';
      field.errorElement.classList.remove('visible');
    }

    if (field.element) {
      field.element.setAttribute('aria-invalid', 'false');
      field.element.classList.remove('error');
    }
  }

  /**
   * Focus first error field
   */
  focusFirstError() {
    const firstErrorField = Object.values(this.fields).find(field => 
      field.element && field.element.getAttribute('aria-invalid') === 'true'
    );

    if (firstErrorField) {
      firstErrorField.element.focus();
    }
  }

  /**
   * Announce errors to screen readers
   */
  announceErrors() {
    const errorCount = Object.values(this.fields).filter(field => 
      field.element && field.element.getAttribute('aria-invalid') === 'true'
    ).length;

    this.notifications.announce(
      `Er ${errorCount === 1 ? 'is' : 'zijn'} ${errorCount} fout${errorCount === 1 ? '' : 'en'} gevonden in het formulier.`,
      'assertive'
    );
  }

  /**
   * Collect form data
   */
  collectFormData() {
    const data = {};

    Object.entries(this.fields).forEach(([fieldName, field]) => {
      if (field.element) {
        data[fieldName] = field.element.type === 'checkbox' 
          ? field.element.checked 
          : field.element.value.trim();
      }
    });

    return data;
  }

  /**
   * Handle successful submission
   */
  handleSuccess(response) {
    if (this.options.showSuccessMessage) {
      this.showSuccessMessage(response);
    }

    if (this.options.resetOnSuccess) {
      this.form.reset();
      this.clearAllErrors();
    }

    this.notifications.announce('Uw bericht is succesvol verzonden!', 'polite');
  }

  /**
   * Handle submission error
   */
  handleError(error) {
    console.error('ContactForm submission error:', error);
    
    this.notifications.show({
      type: 'error',
      title: 'Fout bij verzenden',
      message: 'Er is een fout opgetreden bij het verzenden van uw bericht. Probeer het later opnieuw.',
      duration: 5000
    });
  }

  /**
   * Show success message
   */
  showSuccessMessage(response) {
    const successHtml = `
      <div class="form-success" role="alert" aria-live="polite">
        <div class="success-icon">
          <svg viewBox="0 0 24 24" width="48" height="48">
            <circle cx="12" cy="12" r="11" fill="#28a745" stroke="white" stroke-width="2"></circle>
            <path fill="white" d="M9.7 15.3l-3.3-3.3-1.4 1.4 4.7 4.7 10-10-1.4-1.4z"></path>
          </svg>
        </div>
        <h3>Bedankt voor uw bericht!</h3>
        <p>We hebben uw aanvraag ontvangen en nemen zo spoedig mogelijk contact met u op.</p>
        <p><strong>Referentienummer:</strong> ${response.referenceNumber || this.generateReferenceNumber()}</p>
        <button type="button" class="btn btn--secondary" id="newFormBtn">Nieuw bericht</button>
      </div>
    `;

    this.form.style.display = 'none';
    this.form.insertAdjacentHTML('afterend', successHtml);

    // Add event listener for new form button
    const newFormBtn = document.getElementById('newFormBtn');
    if (newFormBtn) {
      newFormBtn.addEventListener('click', () => {
        this.resetToForm();
      });
    }
  }

  /**
   * Reset to form view
   */
  resetToForm() {
    const successMessage = this.form.parentNode.querySelector('.form-success');
    if (successMessage) {
      successMessage.remove();
    }

    this.form.style.display = 'block';
    this.form.reset();
    this.clearAllErrors();
    
    // Focus first field
    const firstField = Object.values(this.fields).find(field => field.element);
    if (firstField) {
      firstField.element.focus();
    }
  }

  /**
   * Set loading state
   */
  setLoadingState(isLoading) {
    const submitButton = this.form.querySelector('button[type="submit"]');
    
    if (submitButton) {
      if (isLoading) {
        submitButton.classList.add('btn--loading');
        submitButton.disabled = true;
        submitButton.setAttribute('aria-label', 'Bezig met verzenden...');
      } else {
        submitButton.classList.remove('btn--loading');
        submitButton.disabled = false;
        submitButton.setAttribute('aria-label', 'Bericht verzenden');
      }
    }
  }

  /**
   * Clear all field errors
   */
  clearAllErrors() {
    Object.values(this.fields).forEach(field => {
      this.clearFieldError(field);
    });
  }

  /**
   * Handle reset button
   */
  handleReset(event) {
    if (!confirm('Weet u zeker dat u het formulier wilt leegmaken?')) {
      event.preventDefault();
      return;
    }

    this.clearAllErrors();
    
    if (this.options.trackAnalytics) {
      this.analytics.track('contact_form_reset');
    }
  }

  /**
   * Generate reference number
   */
  generateReferenceNumber() {
    return `TSP-${Date.now().toString().slice(-6)}`;
  }

  /**
   * Debounce utility
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Destroy the component
   */
  destroy() {
    // Remove event listeners and clean up
    this.form.removeEventListener('submit', this.handleSubmit);
    
    Object.values(this.fields).forEach(field => {
      if (field.element) {
        field.element.removeEventListener('blur', this.validateField);
        field.element.removeEventListener('input', this.validateField);
      }
    });
  }
}
