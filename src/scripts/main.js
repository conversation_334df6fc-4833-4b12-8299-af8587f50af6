/**
 * TechSupport Pro - Main Application
 * Entry point for the website functionality
 */

// Import components
import { Navigation } from './components/Navigation.js';
import { ContactForm } from './components/ContactForm.js';

// Import services
import { ValidationService } from './services/ValidationService.js';

// Import utilities
import { ready, $, $$, announce, prefersReducedMotion } from './utils/dom.js';

/**
 * Main Application Class
 */
class App {
  constructor() {
    this.components = new Map();
    this.services = new Map();
    this.isInitialized = false;
    
    this.init();
  }

  /**
   * Initialize the application
   */
  async init() {
    try {
      await this.waitForDOM();
      
      this.setupServices();
      this.initializeComponents();
      this.setupGlobalEventListeners();
      this.setupAccessibility();
      this.setupAnimations();
      
      this.isInitialized = true;
      this.announceReady();
      
      console.log('TechSupport Pro website initialized successfully');
    } catch (error) {
      console.error('Failed to initialize application:', error);
    }
  }

  /**
   * Wait for DOM to be ready
   */
  waitForDOM() {
    return new Promise(resolve => {
      ready(resolve);
    });
  }

  /**
   * Setup global services
   */
  setupServices() {
    // Validation service
    const validationService = new ValidationService('nl');
    this.services.set('validation', validationService);
  }

  /**
   * Initialize all components
   */
  initializeComponents() {
    this.initNavigation();
    this.initContactForm();
    this.initScrollEffects();
    this.initFAQ();
  }

  /**
   * Initialize navigation component
   */
  initNavigation() {
    const navElement = $('.header nav');
    if (navElement) {
      const navigation = new Navigation(navElement, {
        mobileBreakpoint: 768,
        closeOnOutsideClick: true,
        closeOnEscape: true
      });
      
      this.components.set('navigation', navigation);
      
      // Set active page
      this.setActiveNavigation();
    }
  }

  /**
   * Initialize contact form
   */
  initContactForm() {
    const formElement = $('#contactForm');
    if (formElement) {
      const contactForm = new ContactForm(formElement, {
        validateOnBlur: true,
        showSuccessMessage: true,
        resetOnSuccess: true,
        trackAnalytics: false // Disable for simple implementation
      });
      
      this.components.set('contactForm', contactForm);
    }
  }

  /**
   * Initialize scroll effects
   */
  initScrollEffects() {
    // Smooth scrolling for anchor links
    this.setupSmoothScrolling();
    
    // Scroll animations
    if (!prefersReducedMotion()) {
      this.setupScrollAnimations();
    }
    
    // Header scroll effect
    this.setupHeaderScrollEffect();
  }

  /**
   * Setup smooth scrolling
   */
  setupSmoothScrolling() {
    $$('a[href^="#"]').forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        if (href === '#') return;
        
        const target = $(href);
        if (target) {
          e.preventDefault();
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  /**
   * Setup scroll animations
   */
  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements with animation classes
    $$('.fade-in-section, .service-card, .card').forEach(element => {
      observer.observe(element);
    });
  }

  /**
   * Setup header scroll effect
   */
  setupHeaderScrollEffect() {
    const header = $('.header');
    if (!header) return;

    let lastScrollY = window.scrollY;
    let ticking = false;

    const updateHeader = () => {
      const scrollY = window.scrollY;
      
      if (scrollY > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
      
      lastScrollY = scrollY;
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateHeader);
        ticking = true;
      }
    });
  }

  /**
   * Initialize FAQ accordion
   */
  initFAQ() {
    const faqItems = $$('.faq-item');
    
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      const answer = item.querySelector('.faq-answer');
      
      if (question && answer) {
        question.addEventListener('click', () => {
          const isOpen = question.classList.contains('active');
          
          // Close all other FAQ items
          faqItems.forEach(otherItem => {
            const otherQuestion = otherItem.querySelector('.faq-question');
            const otherAnswer = otherItem.querySelector('.faq-answer');
            
            if (otherItem !== item) {
              otherQuestion.classList.remove('active');
              otherAnswer.classList.remove('active');
              otherQuestion.setAttribute('aria-expanded', 'false');
            }
          });
          
          // Toggle current item
          if (!isOpen) {
            question.classList.add('active');
            answer.classList.add('active');
            question.setAttribute('aria-expanded', 'true');
          } else {
            question.classList.remove('active');
            answer.classList.remove('active');
            question.setAttribute('aria-expanded', 'false');
          }
        });
        
        // Setup accessibility
        question.setAttribute('aria-expanded', 'false');
        question.setAttribute('role', 'button');
        question.setAttribute('tabindex', '0');
        
        // Keyboard support
        question.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            question.click();
          }
        });
      }
    });
  }

  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // Handle visibility change
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // Handle errors
    window.addEventListener('error', this.handleError.bind(this));
  }

  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    // Skip links
    this.setupSkipLinks();
    
    // Focus management
    this.setupFocusManagement();
    
    // Keyboard navigation
    this.setupKeyboardNavigation();
  }

  /**
   * Setup skip links
   */
  setupSkipLinks() {
    const skipLinks = $$('.skip-link');
    
    skipLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        const target = $(href);
        
        if (target) {
          e.preventDefault();
          target.setAttribute('tabindex', '-1');
          target.focus();
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });
  }

  /**
   * Setup focus management
   */
  setupFocusManagement() {
    // Ensure focus is visible for keyboard users
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });
    
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  /**
   * Setup keyboard navigation
   */
  setupKeyboardNavigation() {
    // Global keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Alt + M: Focus main navigation
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        const nav = $('.nav');
        if (nav) {
          const firstLink = nav.querySelector('.nav-link');
          if (firstLink) firstLink.focus();
        }
      }
      
      // Alt + C: Focus main content
      if (e.altKey && e.key === 'c') {
        e.preventDefault();
        const main = $('#main-content');
        if (main) {
          main.setAttribute('tabindex', '-1');
          main.focus();
        }
      }
    });
  }

  /**
   * Setup animations
   */
  setupAnimations() {
    // Add CSS classes for animations
    const style = document.createElement('style');
    style.textContent = `
      .animate-in {
        animation: fadeInUp 0.6s ease-out forwards;
      }
      
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .keyboard-navigation *:focus {
        outline: 2px solid #0056b3 !important;
        outline-offset: 2px !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Set active navigation item
   */
  setActiveNavigation() {
    const navigation = this.components.get('navigation');
    if (navigation) {
      const currentPath = window.location.pathname;
      navigation.setActive(currentPath);
    }
  }

  /**
   * Handle window resize
   */
  handleResize() {
    // Debounce resize events
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      // Trigger resize event for components
      this.components.forEach(component => {
        if (typeof component.handleResize === 'function') {
          component.handleResize();
        }
      });
    }, 250);
  }

  /**
   * Handle visibility change
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // Page is hidden
      console.log('Page hidden');
    } else {
      // Page is visible
      console.log('Page visible');
    }
  }

  /**
   * Handle global errors
   */
  handleError(event) {
    console.error('Global error:', event.error);
    
    // Show user-friendly error message
    announce('Er is een fout opgetreden. Probeer de pagina te vernieuwen.', 'assertive');
  }

  /**
   * Announce that the app is ready
   */
  announceReady() {
    announce('Website geladen en klaar voor gebruik', 'polite');
  }

  /**
   * Get component instance
   */
  getComponent(name) {
    return this.components.get(name);
  }

  /**
   * Get service instance
   */
  getService(name) {
    return this.services.get(name);
  }

  /**
   * Destroy the application
   */
  destroy() {
    // Destroy all components
    this.components.forEach(component => {
      if (typeof component.destroy === 'function') {
        component.destroy();
      }
    });
    
    this.components.clear();
    this.services.clear();
    this.isInitialized = false;
  }
}

// Initialize the application
const app = new App();

// Make app globally available for debugging
if (typeof window !== 'undefined') {
  window.TechSupportApp = app;
}

export default app;
