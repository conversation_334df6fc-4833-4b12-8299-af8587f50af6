name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  CACHE_KEY: node-modules

jobs:
  # Code Quality & Testing
  quality:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Lint JavaScript
      run: npm run lint
      
    - name: Format check
      run: npm run format:check
      
    - name: Run tests
      run: npm test
      
    - name: Upload test coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        
  # Accessibility Testing
  accessibility:
    name: Accessibility Testing
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Start preview server
      run: npm run preview &
      
    - name: Wait for server
      run: npx wait-on http://localhost:4173
      
    - name: Run accessibility tests
      run: npm run a11y-test
      
    - name: Upload accessibility report
      uses: actions/upload-artifact@v3
      with:
        name: accessibility-report
        path: ./reports/
        
  # Performance Testing
  performance:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Start preview server
      run: npm run preview &
      
    - name: Wait for server
      run: npx wait-on http://localhost:4173
      
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        
    - name: Upload Lighthouse report
      uses: actions/upload-artifact@v3
      with:
        name: lighthouse-report
        path: ./.lighthouseci/
        
  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run npm audit
      run: npm audit --audit-level moderate
      
    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high
        
  # Build & Deploy
  build-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [quality, accessibility, performance]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      env:
        NODE_ENV: production
        
    - name: Optimize images
      run: npm run optimize-images
      
    - name: Generate sitemap
      run: npm run generate-sitemap
      
    - name: Deploy to staging
      if: github.ref == 'refs/heads/develop'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
        destination_dir: staging
        
    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
        cname: techsupportpro.nl
        
    - name: Notify deployment
      if: success()
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: '🚀 TechSupport Pro website deployed successfully!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
  # Post-deployment testing
  post-deploy:
    name: Post-deployment Testing
    runs-on: ubuntu-latest
    needs: build-deploy
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Test production deployment
      run: |
        curl -f https://techsupportpro.nl || exit 1
        curl -f https://techsupportpro.nl/diensten.html || exit 1
        curl -f https://techsupportpro.nl/contact.html || exit 1
        
    - name: Run production Lighthouse audit
      run: |
        npm install -g lighthouse
        lighthouse https://techsupportpro.nl --output=html --output-path=./production-lighthouse.html
        
    - name: Upload production report
      uses: actions/upload-artifact@v3
      with:
        name: production-lighthouse-report
        path: ./production-lighthouse.html
