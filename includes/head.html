<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechSupport Pro - IT Ondersteuning op Maat</title>
    
    <!-- Resource Hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Critical CSS -->
    <style>
        /* Inline critical CSS here */
        :root {
            --primary-color: #0056b3;
            --text-color: #1a1a1a;
        }
        body {
            margin: 0;
            font-family: 'Roboto', sans-serif;
        }
        .skip-nav {
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: max-content;
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 0 0 4px 4px;
            transition: top 0.3s ease;
            z-index: 1000;
        }
        .skip-nav:focus {
            top: 0;
        }
    </style>
    
    <!-- Deferred CSS Loading -->
    <link rel="preload" href="/css/style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/css/style.css"></noscript>
    
    <!-- UX Enhancement CSS -->
    <link rel="preload" href="/css/ux-enhancements.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/css/loading-states.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/css/feedback.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="/css/ux-enhancements.css">
        <link rel="stylesheet" href="/css/loading-states.css">
        <link rel="stylesheet" href="/css/feedback.css">
    </noscript>
    
    <!-- Deferred Font Loading -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&family=Roboto:wght@300;400;500;700&display=swap"></noscript>
    
    <!-- Deferred Icon Font Loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"></noscript>

    <!-- Meta Tags -->
    <meta name="description" content="Professionele IT-ondersteuning op maat voor software en online problemen. Snel en persoonlijk, zonder technisch jargon.">
    <meta name="keywords" content="IT support, computer hulp, software problemen, online ondersteuning">
    <meta name="author" content="TechSupport Pro">
    <link rel="canonical" href="https://techsupportpro.nl/">
    
    <!-- Favicons with sizes -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/logo/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/logo/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/logo/apple-touch-icon.png">
    
    <!-- PWA Support -->
    <link rel="manifest" href="/site.webmanifest">
    <meta name="theme-color" content="#0056b3">
    
    <!-- Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://techsupportpro.nl/">
    <meta property="og:title" content="TechSupport Pro - IT Ondersteuning op Maat">
    <meta property="og:description" content="Professionele IT-ondersteuning op maat voor software en online problemen.">
    <meta property="og:image" content="https://techsupportpro.nl/images/logo/logo.png">
    
    <!-- Performance Optimization Script -->
    <script>
        // Intersection Observer for lazy loading
        document.addEventListener('DOMContentLoaded', function() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img.lazy').forEach(img => imageObserver.observe(img));
            }
        });
    </script>
    
    <!-- UX Enhancement Scripts - Load at end of body -->
    <script defer src="/js/ux-enhancements.js"></script>
    <script defer src="/js/loading-states.js"></script>
    <script defer src="/js/user-feedback.js"></script>
    <script defer src="/js/user-journey-analyzer.js"></script>
</head>
